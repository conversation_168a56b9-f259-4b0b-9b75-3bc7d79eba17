import { Component, Input, OnInit, SimpleChanges, OnChanges, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';

import { PanelModule } from 'primeng/panel';
import { ButtonModule } from 'primeng/button';
import { ProductFileService } from 'src/app/services/pms/product-file/product-file.service';
import { ActivatedRoute, Router } from '@angular/router';
import { ProductDetail, ProductDocumentInput, SectionConfig, ProductRecordVersion } from 'src/app/models/interface/pms';
import { ConfirmationService } from 'primeng/api';
import { AlertService } from 'src/app/shared/services/alert.service';
import { EventBusService } from 'src/app/services/eventBus.service';
import { catchError, finalize, map, Observable, of, Subscription, take } from 'rxjs';
import { environment } from 'src/environments/environment';
import { EditTableSectionComponent } from 'src/app/modules/pms/components/edit-table-section/edit-table-section.component';

@Component({
    selector: 'app-design-profile',
    standalone: true,
    imports: [CommonModule, EditTableSectionComponent, PanelModule, ButtonModule],
    templateUrl: './design-profile.component.html',
    styleUrls: ['./design-profile.component.scss'],
    providers: [ProductFileService],
})
export class DesignProfileComponent implements OnInit, OnChanges, OnDestroy {
    // Danh sách các section, sẽ render lần lượt và đánh số 1,2,3...
    @Input() isCheckNote: boolean = false;
    @Input() data!: any;
    @Input() version!: ProductRecordVersion;
    @Input() currentProduct!: ProductDetail;
    @Input() mode!: string;
    @Input() updatedAt: string | number = '';
    lastUpdated: string = '';
    isEditMode: boolean = false;
    isChecking = false;
    isSaving = false;
    canCheckRdBom = false;
    compareResultUrl: string | null = null;
    reloadTrigger = 0;
    rdBomSelected: any;
    cadFileSelected: any;
    checkLoadingCadFile: boolean = true;
    private requestSub!: Subscription;
    timeCheck: number;
    public STORAGE_BASE_URL = environment.STORAGE_BASE_URL;
    sections: SectionConfig[] = [
        {
            category: '0',
            title: 'Thông tin sản phẩm',
            columns: [
                { header: 'Mã tài liệu', field: 'fileCode', type: 'readonly' },
                { header: 'Tài liệu', field: 'document', type: 'text' },
                { header: 'File', field: 'file', type: 'file' },
                { header: 'Tên tài liệu', field: 'name', type: 'text' },
                { header: 'MD5', field: 'md5', type: 'readonly' },
                { header: 'Build time', field: 'buildtime', type: 'text' },
                { header: 'Version', field: 'version', type: 'text' },
            ],
            rows: [],
        },
        {
            category: '1',
            title: 'RD BOM',
            columns: [
                { header: 'Mã tài liệu', field: 'fileCode', type: 'readonly' },
                { header: 'Tài liệu', field: 'document', type: 'text', minWidth: '250px' },
                {
                    header: 'Loại RD BOM',
                    field: 'rdBomType',
                    type: 'select-one',
                    minLength: 1, // chỉ gọi API khi nhập >= 2 ký tự
                    options: [], // để parent fill
                    totalRecords: 0, // để parent fill
                    optionLabel: 'name',
                    optionValue: 'value',
                    placeholder: 'Gõ để tìm ...',
                    // quan trọng: bind đúng this để load query
                    // nhận 2 tham số: row (toàn bộ object của dòng) và term (chuỗi tìm)
                    lazyLoadFn: (row: any, term: string): Observable<any[]> => {
                        if (row.document === 'RD BOM') {
                            return this.loadRdBomOptions(term);
                        }

                        return of([]); // <- đảm bảo luôn return
                    },
                    minWidth: '250px',
                },
                { header: 'File', field: 'fileName', type: 'readonly', minWidth: '150px' },
                { header: 'Version', field: 'version', type: 'readonly', minWidth: '150px' },
                { header: 'Trạng thái', field: 'status', type: 'readonly', minWidth: '150px' },
            ],
            rows: [],
        },
        {
            category: '2',
            title: 'Thiết kế HW',
            columns: [
                { header: 'Mã tài liệu', field: 'fileCode', type: 'readonly' },
                { header: 'Tài liệu', field: 'document', type: 'text' },
                { header: 'File', field: 'file', type: 'file' },
                { header: 'Tên tài liệu', field: 'name', type: 'text' },
                { header: 'MD5', field: 'md5', type: 'readonly' },
                { header: 'Build time', field: 'buildtime', type: 'text' },
                { header: 'Version', field: 'version', type: 'text' },
            ],
            rows: [],
        },
        {
            category: '3',
            title: 'Thiết kế ID/MD',
            columns: [
                { header: 'Mã tài liệu', field: 'fileCode', type: 'readonly' },
                { header: 'Tài liệu', field: 'document', type: 'text' },
                { header: 'File', field: 'file', type: 'file' },
                { header: 'Tên tài liệu', field: 'name', type: 'text' },
                { header: 'MD5', field: 'md5', type: 'readonly' },
                { header: 'Build time', field: 'buildtime', type: 'text' },
                { header: 'Version', field: 'version', type: 'text' },
            ],
            rows: [],
        },
        {
            category: '4',
            title: 'Thiết kế Accessories và Packaging',
            columns: [
                { header: 'Mã tài liệu', field: 'fileCode', type: 'readonly' },
                { header: 'Tài liệu', field: 'document', type: 'text' },
                { header: 'File', field: 'file', type: 'file' },
                { header: 'Tên tài liệu', field: 'name', type: 'text' },
                { header: 'MD5', field: 'md5', type: 'readonly' },
                { header: 'Build time', field: 'buildtime', type: 'text' },
                { header: 'Version', field: 'version', type: 'text' },
            ],
            rows: [],
        },
        {
            category: '5',
            title: 'Bootloader/ Firmware Sản phẩm',
            columns: [
                { header: 'Mã tài liệu', field: 'fileCode', type: 'readonly' },
                { header: 'Tài liệu', field: 'document', type: 'text' },
                { header: 'File', field: 'file', type: 'file' },
                { header: 'Tên tài liệu', field: 'name', type: 'text' },
                { header: 'MD5', field: 'md5', type: 'readonly' },
                { header: 'Build time', field: 'buildtime', type: 'text' },
                { header: 'Version', field: 'version', type: 'text' },
            ],
            rows: [],
        },
    ];

    constructor(
        private productFileService: ProductFileService,
        private confirmationService: ConfirmationService,
        private alertService: AlertService,
        private router: Router,
        private route: ActivatedRoute,
        private bus: EventBusService,
    ) {}

    ngOnInit(): void {
        this.buildSections();
        // Xác định xem segment đầu tiên của path có phải 'edit' không
        const firstSegment = this.route.snapshot.url[0]?.path;
        this.isEditMode = firstSegment === 'edit';
        this.requestSub = this.bus
            .on<void>('REQUEST_PAYLOAD_ALL')
            .pipe(take(1)) // chỉ xử lý 1 lần rồi tự huỷ
            .subscribe(() => {
                const payload = this.handleSave();
                const timeCheck = this.timeCheck;
                const url = this.compareResultUrl;
                const eventData = {
                    payload,
                };
                this.bus.emit('RESPONSE_PAYLOAD_DESIGN', eventData);
            });
    }

    computeCanCheckRdBom(): void {
        const rdbom = this.sections[1].rows;
        const hw = this.sections[2].rows;
        this.canCheckRdBom = !!(rdbom?.[0]?.filePath && hw?.[3]?.md5);
    }
    ngOnChanges(changes: SimpleChanges) {
        if (changes['currentProduct'] && changes['currentProduct'].currentValue) {
            // mỗi khi parent truyền vào giá trị mới

            if (this.currentProduct.productVersions && this.currentProduct.productVersions.length > 0) {
                this.isCheckNote = true;
            } else {
                this.isCheckNote = false;
            }
        }
        if (changes['data'] && this.data) {
            this.updateRows();
        }
        if (changes['version'] && changes['version'].currentValue) {
            this.timeCheck = this.version.compared;
            this.compareResultUrl = this.version.compareUrl;
        }
    }

    private updateRows() {
        // xử lý button check RDbom
        this.rdBomSelected = this.data[1][0];
        this.rdBomSelected.rdBomType = { value: this.rdBomSelected.filePath };
        this.cadFileSelected = this.data[2][3];
        this.checkLoadingCadFile = false;
        // ---
        this.sections.forEach((section) => {
            const docs = this.data[section.category] || [];
            // if (docs.length > 0 && docs[0].updated) {
            //     this.lastUpdated = this.formatTimestamp(docs[0].updated);
            // }
            // const prefix = 'TK';

            // this.sections.forEach((section, sectionIdx) => {
            //     const sectionCode = `${prefix}${sectionIdx + 1}`; // VD: TK1, SX2...

            //     // reset STT trong mỗi section
            //     section.rows.forEach((row, rowIdx) => {
            //         row.fileCode = `${sectionCode}${(rowIdx + 1).toString().padStart(2, '0')}`; // TK101, TK102...
            //     });
            // });
            if (section.rows.length === 0) {
                section.rows = docs.map((doc) => ({
                    id: doc.id,
                    document: doc.description || '',
                    file: doc.filePath || null,
                    name: doc.documentName || '',
                    md5: doc.md5 || '',
                    buildtime: doc.buildTime || '',
                    version: doc.versionName || '',
                    status: this.getStatusText(doc.status),
                    updated: doc.updated || '',
                    isDefault: doc.isDefault || 0,
                    fileName: doc.fileName || '',
                    note: doc.note || '',
                    filePath: doc?.filePath || '',
                    category: doc.category,
                    fileCode: doc.fileCode || '', // Giữ nguyên mã tài liệu nếu có
                }));
            } else {
                const newRows = docs.map((doc, index) => {
                    const oldRow = section.rows[index];

                    return {
                        // Giữ id cũ nếu có, nếu không thì null
                        id: oldRow ? oldRow.id : null,
                        document: doc.description || '',
                        file: doc.filePath || null,
                        name: doc.documentName || '',
                        md5: doc.md5 || '',
                        buildtime: doc.buildTime || '',
                        version: doc.versionName || '',
                        status: this.getStatusText(doc.status),
                        updated: doc.updated || '',
                        isDefault: doc.isDefault || 0,
                        fileName: doc.fileName || '',
                        note: doc.note || '',
                        filePath: doc?.filePath || '',
                        category: doc.category,
                        fileCode: oldRow?.fileCode || doc.fileCode || '', // Giữ nguyên mã tài liệu nếu có
                    };
                });

                // Gán lại section.rows
                section.rows = newRows;
            }
        });
        this.reloadTrigger++;

        this.computeCanCheckRdBom();
    }
    getLastUpdatedTime(): string {
        const rdBomSection = this.sections.find((section) => section.category === '1');
        if (!rdBomSection || !rdBomSection.rows || rdBomSection.rows.length === 0) return '';

        const updatedTimestamp = rdBomSection.rows[0].updated;
        return this.formatDateTime(updatedTimestamp);
    }
    private formatDateTime(timestamp: number): string {
        if (!timestamp) return '';

        const date = new Date(timestamp);
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();

        return `${hours}:${minutes} ngày ${day}/${month}/${year}`;
    }

    private buildSections(): void {
        if (!this.isCheckNote) {
            // Không cần cột note → giữ nguyên
            // this.sections = [...this.sections];
            return;
        }

        // Khi cần thêm cột Note
        this.sections = this.sections.map((section) => ({
            ...section,
            columns: [...section.columns, { header: 'Ghi chú', field: 'note', type: 'text', minWidth: '250px' }],
        }));
    }
    loadRdBomOptions(query: string): Observable<any[]> {
        // tìm đúng column để bật/tắt loading
        const sec = this.sections.find((s) => s.category === '1');
        const col = sec?.columns.find((c) => c.field === 'rdBomType') as any;
        if (!col) {
            return of([]); // luôn trả về Observable
        }

        return this.productFileService
            .fetchListRdBOM({
                page: 0,
                size: 100,
                unpaged: false,
                name: query,
            })
            .pipe(
                // map API response thành mảng options
                map((res) =>
                    res.map((item: any) => ({
                        name: item.name,
                        value: item.id,
                        version: item.version,
                        status: this.getStatusText(item.status),
                    })),
                ),
                // nếu lỗi thì trả mảng rỗng
                catchError(() => of([])),
                // tắt loading dù thành công hay lỗi
            );
    }
    private getStatusText(status: number): string {
        switch (status) {
            case 0:
                return 'Draft';
            case 1:
                return 'Send to Approve';
            case 2:
                return 'Approved';
            case 4:
                return 'Reject';
            default:
                return '';
        }
    }

    // onSaveSectionRow(section: SectionConfig, e: { index: number; row: any; done: (updated: any) => void }) {
    //     const { index, row, done } = e;
    //     console.log('check save', row);
    //     console.log('check save', section);
    //     console.log('check this.version', this.version);
    //     // 1) Chuẩn bị payload cho tạo mới
    //     const createDto: CreateProductDocumentDto = {
    //         versionId: this.version.id,
    //         description: row.document,
    //         type: 1, // tab hồ sơ thiết kế
    //         category: +section.category,
    //         documentType: 1, // mỗi bảng trong 1 tab
    //         fileName: row.file || '',
    //         filePath: row.filePath || '',
    //         md5: row.md5 || '',
    //         buildTime: Number(row.buildtime) || '',
    //         versionName: row.version || '',
    //         note: row.note || '',
    //     };

    //     // 2) Nếu đã có row.id → build Update DTO
    //     let obs$;
    //     if (row.id) {
    //         const updateDto: UpdateProductDocumentDto = {
    //             description: createDto.description,
    //             fileName: createDto.fileName,
    //             filePath: createDto.filePath,
    //             md5: createDto.md5,
    //             buildTime: createDto.buildTime,
    //             versionName: createDto.versionName,
    //             note: createDto.note,
    //         };
    //         obs$ = this.productFileService.updateDocumentRow(row.id, updateDto);
    //     } else {
    //         // 3) Ngược lại call create
    //         obs$ = this.productFileService.createDocumentRow(createDto);
    //     }

    //     // 4) Subscribe chung
    //     obs$.subscribe({
    //         next: (updated) => {
    //             this.alertService.success('Thành công', 'Lưu bản ghi thành công');

    //             // cập nhật mảng JS
    //             section.rows[index] = { ...section.rows[index], ...updated };

    //             // callback để con patch FormGroup
    //             done(updated);
    //         },
    //         error: (err) => {
    //             this.alertService.error('Lỗi', 'Lưu bản ghi thất bại');
    //             console.error('save error:', err);
    //         },
    //     });
    // }

    onDeleteRow(e: { index: number; row: any; done: () => void }) {
        const { index, row, done } = e;
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.productFileService.delete(row.id).subscribe({
                    next: () => {
                        this.alertService.success('Thành công', 'Xóa bản ghi thành công');
                        done();
                        // reload dữ liệu
                    },
                    error: (err) => {
                        this.alertService.error('Lỗi', 'Xóa bản ghi thất bại');
                        console.error('Delete error:', err);
                    },
                });
            },
        });
    }
    buildPayload(): ProductDocumentInput[] {
        return this.sections.flatMap((section) =>
            section.rows.map((row) => {
                // Base payload chung
                const base: Partial<ProductDocumentInput> = {
                    id: row.id || 0,
                    versionId: this.version?.id || 0,
                    description: row.document || '',
                    type: 1,
                    category: +section.category,
                    md5: row.md5 || '',
                    buildTime: row.buildtime || '',
                    documentName: row.name || '',
                    note: row.note || '',
                    isDefault: row.isDefault || 0,
                    documentType: 1,
                    fileCode: row.fileCode || '',
                };

                // Nếu là section category=1 và có rdBomType thì ưu tiên lấy từ nó
                if (section.category === '1' && row.rdBomType) {
                    return {
                        ...base,
                        fileName: row.rdBomType.name || '',
                        filePath: row.rdBomType.value || '',
                        versionName: row.rdBomType.version || null,
                        // status: row.status || '',
                    } as ProductDocumentInput;
                }

                // Mặc định lấy từ row.fileName, row.filePath, row.version
                return {
                    ...base,
                    fileName: row.fileName || '',
                    filePath: row.filePath || '',
                    versionName: row.version || null,
                    // status: row.status || '',
                } as ProductDocumentInput;
            }),
        );
    }

    handleSave() {
        this.isSaving = true;
        const payload = this.buildPayload();
        return payload;
        // this.productFileService
        //     .createUpdateProductDocument(this.currentProduct.id, payload)
        //     .pipe(finalize(() => (this.isSaving = false)))
        //     .subscribe({
        //         next: (versionId) => {
        //             this.alertService.success('Thành công', 'Lưu hồ sơ thiết kế thành công');
        //             // Kiểm tra xem URL đã chứa segment 'edit' chưa
        //             const urlSegments = this.route.snapshot.url.map((s) => s.path);
        //             const alreadyEdit = urlSegments[0] === 'edit';

        //             if (!alreadyEdit) {
        //                 this.router.navigate(['/pms/product-file/edit', this.currentProduct.id, versionId], {
        //                     state: {
        //                         currentProduct: this.currentProduct,
        //                         version: this.version,
        //                         mode: 'edit',
        //                     },
        //                     replaceUrl: true, // tránh tạo thêm history entry
        //                 });
        //             } else {
        //                 // Đã ở edit → chỉ refresh các tab
        //                 this.bus.emit('refreshTabs');
        //             }
        //         },
        //     });
    }

    handleDeleteVersion(v: ProductRecordVersion): void {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.productFileService.deleteVersion(this.version.id).subscribe({
                    next: () => {
                        this.alertService.success('Thành công', 'Xóa thành công');
                        this.router.navigate(['/pms/product-file'], {
                            replaceUrl: true, // tránh tạo thêm history entry
                        });
                    },
                    error: (err) => {
                        this.alertService.error('Lỗi', 'Xóa thất bại');
                        console.error('Delete error:', err);
                    },
                });
            },
        });
    }
    handleLoadingRowsChange(value) {
        this.checkLoadingCadFile = value.get(3) ?? true;
        if (this.rdBomSelected && this.cadFileSelected && !this.checkLoadingCadFile) {
            this.canCheckRdBom = true;
        } else {
            this.canCheckRdBom = false;
        }
    }
    onSectionRowsChange(section: SectionConfig, newRows: any[]) {
        if (newRows && newRows.length > 0) {
            if (newRows[0] && newRows[0].category === 1) {
                if (newRows[0].rdBomType) {
                    this.rdBomSelected = newRows[0];
                } else {
                    this.rdBomSelected = null;
                }
            }
            if (newRows[3] && newRows[3].category === 2) {
                if (newRows[3].filePath) {
                    this.cadFileSelected = newRows[3];
                } else {
                    this.cadFileSelected = null;
                }
            }
        }

        if (
            this.rdBomSelected &&
            this.rdBomSelected.rdBomType &&
            this.rdBomSelected.rdBomType.value &&
            this.cadFileSelected &&
            this.cadFileSelected.filePath &&
            !this.checkLoadingCadFile
        ) {
            this.canCheckRdBom = true;
        } else {
            this.canCheckRdBom = false;
        }

        section.rows = newRows;
        this.updateDocumentCodes(section);
    }
    private updateDocumentCodes(section: SectionConfig): void {
        const prefix = 'TK'; // hoặc động theo tab nếu cần
        const sectionCode = `${prefix}${+section.category + 1}`;

        // Dùng counter để sinh mã tiếp theo
        if (!section['documentCodeCounter']) {
            const existingCodes = section.rows.map((row) => row.fileCode).filter((code) => code?.startsWith(sectionCode));
            const max = existingCodes.map((code) => parseInt(code.slice(sectionCode.length), 10)).filter((num) => !isNaN(num));
            section['documentCodeCounter'] = max.length > 0 ? Math.max(...max) : 100;
        }

        // Gán mã cho các row chưa có
        section.rows.forEach((row) => {
            if (!row.fileCode) {
                section['documentCodeCounter']++;
                row.fileCode = `${sectionCode}${section['documentCodeCounter'].toString().padStart(2, '0')}`; // ✅ FIX
            }
        });
    }
    onDownloadResult() {
        if (!this.compareResultUrl) return;

        // nếu muốn mở tab mới

        window.open(`${this.STORAGE_BASE_URL}${this.compareResultUrl}`, '_blank');
        // hoặc nếu bạn muốn gán href trực tiếp thì template bên dưới sẽ handle
    }
    checkRdBom() {
        this.isChecking = true;
        let versionId;
        let bomId;
        let cadPath;
        let cadName;
        if (this.version && this.version.id) {
            versionId = this.version.id;
        } else {
            versionId = 0;
        }
        if (this.rdBomSelected) {
            bomId = this.rdBomSelected.rdBomType.value;
        }
        if (this.cadFileSelected) {
            cadPath = this.cadFileSelected.filePath;
            cadName = this.cadFileSelected.fileName;
        }

        this.productFileService
            .getCheckRdBom(versionId, bomId, cadPath, cadName)
            .pipe(finalize(() => (this.isChecking = false)))
            .subscribe({
                next: (res) => {
                    this.compareResultUrl = res.url;
                    this.timeCheck = res.checkTime;
                },
                error: (err) => console.error('❌ error:', err),
            });
    }
    ngOnDestroy(): void {
        this.requestSub.unsubscribe();
    }
}
