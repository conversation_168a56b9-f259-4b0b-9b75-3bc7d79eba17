<div style="padding: 1rem 1rem 0 1rem">
    <div class="tw-flex tw-flex-col tw-justify-end">
        <h2 class="text-blue-400 tw-text-end">Quản lý cung ứng</h2>
        <p class="text-blue-400 tw-text-end">Hệ thống quản lý nhà cung cấp & giao dịch</p>
    </div>

    <div class="tw-rounded-md tw-bg-white tw-p-5 tw-mt-5 tw-mx-auto" style="max-width: 1000px">
        <p><i class="pi pi-shop"></i> Nhà cung cấp</p>
        <div class="tw-grid tw-gap-2 tw-grid-cols-2 sm:tw-grid-cols-3 md:tw-grid-cols-4 tw-m-2">
            <div
                (click)="navigateSupplier(0)"
                class="tw-flex tw-flex-row tw-gap-4 tw-items-center tw-bg-slate-100 hover:tw-bg-gray-100 tw-rounded-md tw-p-3 tw-cursor-pointer"
            >
                <i class="tw-text-stone-500 pi pi-question-circle" style="font-size: 40px"></i>
                <div class="tw-flex tw-flex-col">
                    <span style="font-size: 2rem">{{
                        summaryReport ? summaryReport.supplier.quantityEvaluating : 0
                    }}</span>
                    <span>Đang đánh giá</span>
                </div>
            </div>

            <div
                (click)="navigateSupplier(1)"
                class="tw-flex tw-flex-row tw-gap-4 tw-items-center tw-bg-slate-100 hover:tw-bg-gray-100 tw-rounded-md tw-p-3 tw-cursor-pointer"
            >
                <i class="tw-text-stone-500 pi pi-stop-circle" style="font-size: 40px"></i>
                <div class="tw-flex tw-flex-col">
                    <span style="font-size: 2rem" class="tw-text-pink-400">
                        {{ summaryReport ? summaryReport.supplier.quantityLimited : 0 }}
                    </span>
                    <span>Hạn chế hợp tác</span>
                </div>
            </div>

            <div
                (click)="navigateSupplier(2)"
                class="tw-flex tw-flex-row tw-gap-4 tw-items-center tw-bg-slate-100 hover:tw-bg-gray-100 tw-rounded-md tw-p-3 tw-cursor-pointer"
            >
                <i class="tw-text-stone-500 pi pi-receipt" style="font-size: 40px"></i>
                <div class="tw-flex tw-flex-col">
                    <span style="font-size: 2rem" class="tw-text-orange-400">{{
                        summaryReport ? summaryReport.supplier.quantityTemporary : 0
                    }}</span>
                    <span>Phê duyệt tạm thời</span>
                </div>
            </div>
            <div
                (click)="navigateSupplier(3)"
                class="tw-flex tw-flex-row tw-gap-4 tw-items-center tw-bg-slate-100 hover:tw-bg-gray-100 tw-rounded-md tw-p-3 tw-cursor-pointer"
            >
                <i class="tw-text-stone-500 pi pi-times-circle" style="font-size: 40px"></i>
                <div class="tw-flex tw-flex-col">
                    <span style="font-size: 2rem" class="tw-text-red-400">{{
                        summaryReport ? summaryReport.supplier.quantityStop : 0
                    }}</span>
                    <span>Dừng hợp tác</span>
                </div>
            </div>
            <div
                (click)="navigateSupplier(4)"
                class="tw-flex tw-flex-row tw-gap-4 tw-items-center tw-bg-slate-100 hover:tw-bg-gray-100 tw-rounded-md tw-p-3 tw-cursor-pointer"
            >
                <i class="tw-text-stone-500 pi pi-stop" style="font-size: 40px"></i>
                <div class="tw-flex tw-flex-col">
                    <span style="font-size: 2rem" class="tw-text-violet-400">{{
                        summaryReport ? summaryReport.supplier.quantityNotTraded : 0
                    }}</span>
                    <span>Chưa giao dịch</span>
                </div>
            </div>
            <div
                (click)="navigateSupplier(5)"
                class="tw-flex tw-flex-row tw-gap-4 tw-items-center tw-bg-slate-100 hover:tw-bg-gray-100 tw-rounded-md tw-p-3 tw-cursor-pointer"
            >
                <i class="tw-text-stone-500 pi pi-sync" style="font-size: 40px"></i>
                <div class="tw-flex tw-flex-col">
                    <span style="font-size: 2rem" class="tw-text-cyan-400">{{
                        summaryReport ? summaryReport.supplier.quantityTrading : 0
                    }}</span>
                    <span>Đang giao dịch</span>
                </div>
            </div>
            <div
                (click)="navigateSupplier(6)"
                class="tw-flex tw-flex-row tw-gap-4 tw-items-center tw-bg-slate-100 hover:tw-bg-gray-100 tw-rounded-md tw-p-3 tw-cursor-pointer"
            >
                <span class="material-icons material-symbols-outlined tw-text-stone-500" style="font-size: 40px"
                    >move_up</span
                >
                <div class="tw-flex tw-flex-col">
                    <span style="font-size: 2rem" class="tw-text-lime-400">{{
                        summaryReport ? summaryReport.supplier.quantityTraded : 0
                    }}</span>
                    <span>Đã giao dịch</span>
                </div>
            </div>
        </div>
    </div>
    <div class="tw-rounded-md tw-bg-white tw-p-5 tw-mt-5 tw-mx-auto" style="max-width: 1000px">
        <p><i class="pi pi-box"></i> Đơn mua</p>
        <div class="tw-grid tw-gap-2 tw-grid-cols-2 sm:tw-grid-cols-3 md:tw-grid-cols-4 tw-m-2">
            <div
                (click)="navigatePo(0)"
                class="tw-flex tw-flex-row tw-gap-4 tw-items-center tw-bg-slate-100 hover:tw-bg-gray-100 tw-rounded-md tw-p-3 tw-cursor-pointer"
            >
                <i class="tw-text-stone-500 pi pi-sparkles" style="font-size: 40px"></i>
                <div class="tw-flex tw-flex-col">
                    <span style="font-size: 2rem" class="tw-text-amber-400">{{
                        summaryReport ? summaryReport.po.quantityNew : 0
                    }}</span>
                    <span>Mới</span>
                </div>
            </div>

            <div
                (click)="navigatePo(1)"
                class="tw-flex tw-flex-row tw-gap-4 tw-items-center tw-bg-slate-100 hover:tw-bg-gray-100 tw-rounded-md tw-p-3 tw-cursor-pointer"
            >
                <i class="tw-text-stone-500 pi pi-spinner-dotted" style="font-size: 40px"></i>
                <div class="tw-flex tw-flex-col">
                    <span style="font-size: 2rem" class="tw-text-purple-400">{{
                        summaryReport ? summaryReport.po.quantityProcessing : 0
                    }}</span>
                    <span>Đang xử lý</span>
                </div>
            </div>

            <div
                (click)="navigatePo(2)"
                class="tw-flex tw-flex-row tw-gap-4 tw-items-center tw-bg-slate-100 hover:tw-bg-gray-100 tw-rounded-md tw-p-3 tw-cursor-pointer"
            >
                <i class="tw-text-stone-500 pi pi-truck" style="font-size: 40px"></i>
                <div class="tw-flex tw-flex-col">
                    <span style="font-size: 2rem" class="tw-text-cyan-400">{{
                        summaryReport ? summaryReport.po.quantityDelivering : 0
                    }}</span>
                    <span>Đang giao hàng</span>
                </div>
            </div>
            <div
                (click)="navigatePo(3)"
                class="tw-flex tw-flex-row tw-gap-4 tw-items-center tw-bg-slate-100 hover:tw-bg-gray-100 tw-rounded-md tw-p-3 tw-cursor-pointer"
            >
                <i class="tw-text-stone-500 pi pi-check-circle" style="font-size: 40px"></i>
                <div class="tw-flex tw-flex-col">
                    <span style="font-size: 2rem" class="tw-text-lime-400">{{
                        summaryReport ? summaryReport.po.quantityCompleted : 0
                    }}</span>
                    <span>Hoàn thành</span>
                </div>
            </div>
        </div>
    </div>
</div>
