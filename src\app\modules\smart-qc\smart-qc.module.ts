import { NgModule } from '@angular/core';
import { FileService } from 'src/app/shared/services/file.service';
import { SmartQcRoutingModule } from './smart-qc.routing';
import { BaseUserService } from 'src/app/services/administration/admin/user.service';
import { TableCommonService } from 'src/app/shared/table-module/table.common.service';

@NgModule({
    imports: [SmartQcRoutingModule],
    providers: [FileService, BaseUserService, TableCommonService],
})
export class SmartQcModule {}
