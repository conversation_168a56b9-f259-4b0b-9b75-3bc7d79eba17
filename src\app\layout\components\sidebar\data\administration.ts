import { SideBarItem } from '../sidebar.menu.component';

export const SIDE_BAR_ADMINISTRATION: SideBarItem[] = [
    {
        label: 'Trang chủ',
        icon: 'pi pi-home',
        routerLink: '/administration/dashboard',
        authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_SUB_ADMIN'],
    },
    {
        label: 'Quản trị',
        icon: 'pi pi-ethereum',
        authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_SUB_ADMIN'],
        items: [
            {
                label: 'Người dùng',
                authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_SUB_ADMIN'],
                routerLink: '/administration/user',
            },
            {
                label: 'Vai trò',
                authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_SUB_ADMIN'],
                routerLink: '/administration/role',
            },
            {
                label: 'Quyền',
                authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_SUB_ADMIN'],
                routerLink: '/administration/privilege',
            },
        ],
    },
    {
        label: 'Quản lý tiến trình',
        icon: 'pi pi-slack',
        authorize: ['ROLE_SYSTEM_ADMIN'],
        items: [
            {
                label: 'Trạng thái',
                routerLink: '/administration/state',
                authorize: ['ROLE_SYSTEM_ADMIN'],
            },
            {
                label: 'Tiến trình',
                routerLink: '/administration/process',
                authorize: ['ROLE_SYSTEM_ADMIN'],
            },
        ],
    },
];
