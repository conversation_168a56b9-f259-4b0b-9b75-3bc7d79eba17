import { Component, OnInit, inject } from '@angular/core';
import { BaseUserService } from 'src/app/services/administration/admin/user.service';
import { ButtonModule } from 'primeng/button';
import { AuthService } from 'src/app/core/auth/auth.service';
import { TagModule } from 'primeng/tag';
import { CommonModule, NgStyle } from '@angular/common';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { AvatarModule } from 'primeng/avatar';
import { AlertService } from 'src/app/shared/services/alert.service';
import { InputTextModule } from 'primeng/inputtext';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MultiSelectModule } from 'primeng/multiselect';
import { DropdownModule } from 'primeng/dropdown';
import { ConfirmationService } from 'primeng/api';
import { InputSwitchModule } from 'primeng/inputswitch';
import { ContractService } from 'src/app/services/smart-qc/masterdata/contract.service';
import { AccordionModule } from 'primeng/accordion';
import { PasswordModule } from 'primeng/password';
import { Contract } from 'src/app/models/interface/smart-qc';
import { Role, User } from '../../../models/interface';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { RoleService } from 'src/app/services/administration/admin/role.service';

@Component({
    selector: 'app-user-detail',
    templateUrl: './user-detail.component.html',
    styleUrls: ['./user-detail.component.scss'],
    standalone: true,
    imports: [
        ButtonModule,
        TagModule,
        CommonModule,
        AvatarModule,
        RouterLink,
        InputTextModule,
        FormsModule,
        MultiSelectModule,
        DropdownModule,
        InputSwitchModule,
        ReactiveFormsModule,
        AccordionModule,
        PasswordModule,
        SubHeaderComponent,
        NgStyle,
    ],
    providers: [ContractService, BaseUserService, RoleService],
})
export class UserDetailComponent implements OnInit {
    userService = inject(BaseUserService);
    roleService = inject(RoleService);
    loadingService = inject(LoadingService);
    alertService = inject(AlertService);
    contractService = inject(ContractService);
    route = inject(ActivatedRoute);
    router = inject(Router);
    confirmationService = inject(ConfirmationService);
    authService = inject(AuthService);
    // areaService = inject(AreaService);
    user: User = {
        email: '',
        note: '',
        phone: '',
        fullName: '',
        roles: [],
        departments: [],
        areas: [],
        qcContractIds: [],
    };
    userId: number;
    editMode: boolean;
    systemRoles: Role[] = [];
    normalRoles: Role[] = [];
    roleOption: Role[];
    // areaOption: Area[];
    // departmentOption: any[];
    isRequiredContract = false;
    unEditedRoles: Role[] = [];
    unEditedSystemRoles: Role[] = [];
    normalRolesOrigin: Role[] = [];
    contractOptions: Contract[];
    isEditable = false;

    userForm;
    constructor(private formBuilder: FormBuilder) {
        this.userForm = this.formBuilder.group({
            email: [null, [Validators.maxLength(255), Validators.required]],
            fullName: [null, [Validators.required, Validators.maxLength(255)]],
            phone: [null, [Validators.pattern('^[0-9]*$'), Validators.maxLength(255)]],
            systemRoles: [{ value: null, disabled: true }],
            normalRoles: [null, Validators.required],
            password: [null, Validators.minLength(8)],
            note: [null],
            // department: [null],
            // area: [null],
            qcContractIds: [null],
            active: [null],
        });
    }

    ngOnInit() {
        this.route.data.subscribe((data) => {
            this.editMode = data['mode'] === 'edit';
        });

        this.contractService.getPage(`query=&size=1000&page=0`).subscribe((res) => {
            this.contractOptions = res;
        });

        this.roleService.getRoleByUser().subscribe((data) => {
            this.roleOption = data;
            if (this.editMode || !this.userId) {
                this.systemRoles = this.roleOption.filter((a) => a.type);
                this.normalRoles = this.roleOption.filter((a) => !a.type && a.qcLevel !== null && a.qcLevel !== 0);
            }
            this.normalRolesOrigin = [...this.normalRoles];
            this.userForm.patchValue({
                systemRoles: this.roleOption.find((a) => a.type && a.name === 'ROLE_SMARTQC'),
            });
            this.route.paramMap.subscribe((params) => {
                this.userId = params.get('id') ? Number(params.get('id')) : null;
                if (this.userId) {
                    this.loadingService.show();

                    this.userService.getOne(this.userId).subscribe({
                        next: (res) => {
                            this.user = { active: null, ...res };
                            this.user.systemRoles = this.systemRoles = this.user.roles.filter((a) => a.type);
                            this.user.normalRoles = this.user.roles.filter((a) => !a.type);
                            // this.user.area = this.user.areas.length > 0 ? this.user.areas[0] : null;
                            // this.user.department = this.user.departments.length > 0 ? this.user.departments[0] : null;
                            this.unEditedSystemRoles = this.user.systemRoles.filter((a) => a.name !== 'ROLE_SMARTQC');
                            this.unEditedRoles = this.unEditedSystemRoles.concat(
                                this.user.normalRoles.filter((a) => a.qcLevel === 0 || !a.qcLevel),
                            );

                            this.userForm.patchValue({
                                fullName: this.user.fullName,
                                email: this.user.email,
                                phone: this.user.phone,
                                note: this.user.note,
                                normalRoles: res.roles.find((a) => a.qcLevel && a.qcLevel !== 0),
                                // area: this.user.areas?.length > 0 ? this.user.areas[0normalRoles] : null,
                                // department: this.user.departments.length > 0 ? this.user.departments[0] : null,\
                                password: null,
                                qcContractIds: this.user.qcContractIds,
                                active: this.user.active,
                            });

                            if (this.authService.isPM() && !this.user.roles.some((r) => r.name === 'ROLE_SMARTQC')) {
                                this.alertService.warning('Cảnh báo', 'Bạn không có quyền truy cập dữ liệu');
                                this.router.navigate(['/sqc/user']);
                            }

                            if (
                                this.authService.isSubPM() &&
                                !this.user.roles.some((r) => r.name === 'ROLE_QC_EMPLOYEE')
                            ) {
                                this.alertService.warning('Cảnh báo', 'Bạn không có quyền truy cập dữ liệu');
                                this.router.navigate(['/sqc/user']);
                            }
                            this.onSelectRole();
                            this.userForm.get('email').disable();
                            this.setStatus();
                        },
                        error: () => {
                            this.loadingService.hide();
                        },
                        complete: () => {
                            this.loadingService.hide();
                        },
                    });
                }
            });
        });

        // this.userService.getAllDeparment('query=&page=0&size=200').subscribe((res) => {
        //     this.departmentOption = res;
        // });
        // this.areaService.getAll().subscribe((res) => {
        //     this.areaOption = res;
        // });
    }

    getContractName(id) {
        return this.contractOptions.find((e) => e.id === id)?.name;
    }

    getSeverity(state: boolean): string {
        switch (state) {
            case true:
                return 'success';
            case false:
                return 'warning';
            default:
                return 'info';
        }
    }

    getStateText(state: boolean): string {
        switch (state) {
            case true:
                return 'Hoạt động';
            case false:
                return 'Chưa hoạt động';
            default:
                return 'Đăng kí';
        }
    }

    onSelectRole() {
        if (
            // this.userForm.getRawValue().normalRoles.some((e) => e.qcLevel == 4)
            this.userForm.getRawValue().normalRoles?.qcLevel === 4
        ) {
            this.isRequiredContract = true;
            this.userForm.controls['qcContractIds'].setValidators([Validators.required]);
        } else {
            this.isRequiredContract = false;
            this.userForm.controls['qcContractIds'].clearValidators();
            this.userForm.patchValue({ qcContractIds: [] });
        }
        this.userForm.controls['qcContractIds'].updateValueAndValidity();

        // let roles = this.userForm.getRawValue().normalRoles;
        // if (
        //     (roles?.length == 1 && this.userForm.getRawValue().normalRoles.some((e) => e.qcLevel != 3)) ||
        //     (roles?.length == 2 && this.userForm.getRawValue().normalRoles.some((e) => e.qcLevel == 3))
        // ) {
        //     this.normalRoles = Common.mergeArray(
        //         value,
        //         this.normalRolesOrigin.filter((e) => e.qcLevel == 3),
        //         (a, b) => a.id === b.id,
        //     );
        // } else {
        //     this.normalRoles = [...this.normalRolesOrigin];
        // }
    }

    selectContract(e) {
        this.user.qcContractIds = e.values;
    }

    onSetAccount(e) {
        if (!e.target.value.match(/^\S+@\S+\.\S+$/g)) {
            this.userForm.controls['password'].setValidators([Validators.required, Validators.minLength(8)]);
        } else {
            this.userForm.controls['password'].clearValidators();
        }

        this.userForm.controls['password'].updateValueAndValidity();
    }

    save() {
        const userFormValue = this.userForm.getRawValue();

        this.user = {
            ...this.user,
            fullName: userFormValue.fullName,
            email: userFormValue.email,
            phone: userFormValue.phone,
            note: userFormValue.note,
            roles: this.unEditedRoles.concat(...[userFormValue.normalRoles], ...[userFormValue.systemRoles]),
            qcContractIds: userFormValue.qcContractIds,
        };
        if (!this.userForm.valid) return;
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: this.userId ? 'Cập nhật thông tin người dùng' : 'Tạo tài khoản người dùng',
            icon: 'pi pi-exclamation-triangle',
            acceptIcon: 'none',
            rejectIcon: 'none',
            rejectButtonStyleClass: 'p-button-text',
            acceptButtonStyleClass: 'p-button-outlined',
            acceptLabel: 'Đồng ý',
            rejectLabel: 'Hủy',
            accept: () => {
                this.loadingService.show();
                if (this.userId) {
                    this.user.adminChangePassword = userFormValue.password;
                    this.userService.update(this.user).subscribe({
                        next: () => {
                            this.alertService.success('Thành công', 'Cập nhật tài khoản thành công');
                            this.router.navigate(['..'], { relativeTo: this.route });
                            this.loadingService.hide();
                        },
                        error: () => {
                            this.loadingService.hide();
                        },
                    });
                } else {
                    this.user.password =
                        !userFormValue.password || userFormValue.password === '' ? null : userFormValue.password;
                    this.userService.create(this.user).subscribe({
                        next: () => {
                            this.alertService.success(
                                'Thành công',
                                this.user.password
                                    ? 'Tạo tài khoản thành công'
                                    : `Vui lòng truy cập email ${this.user.email} để kích hoạt tài khoản`,
                            );
                            this.router.navigate(['..'], { relativeTo: this.route });
                            this.loadingService.hide();
                        },
                        error: () => {
                            this.loadingService.hide();
                        },
                    });
                }
            },
        });
    }

    status;
    activeClass;
    title;
    iconClass;
    setStatus = () => {
        const statusTitle = {
            true: 'Hoạt động', // ngừng hoạt động
            false: 'Ngừng hoạt động', //kích hoạt
            null: 'Đăng ký', // đăng ký
        };
        const iconStyle = {
            true: 'success',
            false: 'danger',
            null: 'warning',
        };

        const statusStyle = {
            true: { color: '#00b87b' },
            false: { color: '#f4516c' },
            null: { color: '#fba65d' },
        };
        if (this.user.active === true) {
            this.status = 'global.common.active';
        } else if (this.user.active === null || this.user.active === undefined) {
            this.status = 'global.common.register';
        } else {
            this.status = 'global.common.archive';
        }
        this.activeClass = statusStyle['' + this.user.active];
        this.title = statusTitle['' + this.user.active];
        this.iconClass = iconStyle['' + this.user.active];
    };

    setActiveStatus = () => {
        if (this.user.active === null) return;

        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: this.user.active ? 'Khóa tài khoản người dùng' : 'Kích hoạt tài khoản người dùng',
            icon: 'pi pi-exclamation-triangle',
            acceptIcon: 'none',
            rejectIcon: 'none',
            rejectButtonStyleClass: 'p-button-text',
            acceptButtonStyleClass: 'p-button-outlined',
            acceptLabel: 'Đồng ý',
            rejectLabel: 'Hủy',
            accept: () => {
                this.loadingService.show();

                if (this.user.active) {
                    this.userService.deactivate(this.user.id).subscribe(() => {
                        this.userService.getOne(this.user.id).subscribe({
                            next: (res) => {
                                this.user = { active: null, ...res };
                                this.userForm.patchValue({ active: this.user.active });
                                this.loadingService.hide();
                                this.setStatus();
                            },
                            error: () => {
                                this.loadingService.hide();
                            },
                        });
                    });
                } else {
                    this.userService.activate(this.user.id).subscribe(() => {
                        this.userService.getOne(this.user.id).subscribe({
                            next: (res) => {
                                this.user = { active: null, ...res };
                                this.userForm.patchValue({ active: this.user.active });
                                this.loadingService.hide();
                                this.setStatus();
                            },
                            error: () => {
                                this.loadingService.hide();
                            },
                        });
                    });
                }
            },
        });
    };
}
