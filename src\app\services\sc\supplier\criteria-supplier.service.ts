import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from 'src/app/services/base.service';
import {SupplierCriteria} from "../../../models/interface/sc";

@Injectable({
    providedIn: 'root',
})
export class CriteriaSupplierService extends BaseService<SupplierCriteria> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/criteria');
    }
}
