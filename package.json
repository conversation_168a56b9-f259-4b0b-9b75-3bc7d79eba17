{"name": "smartqc-web", "version": "2.5.1", "scripts": {"ng": "ng", "dev": "ng serve  --configuration=vivas", "start": "ng serve --hmr --configuration=hmr --open", "hmr": "ng serve --hmr --configuration=hmr", "prebuild": "node version.js", "build": "ng build", "build:prod": "ng build --c=prod", "build:151": "ng build --c=151", "build:staging": "ng build --c=staging", "build:vivas": "ng build --c=vivas", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "eslint src/", "lint:fix": "eslint --fix src/"}, "private": true, "dependencies": {"@angular/animations": "^16.2.0", "@angular/cdk": "^16.2.0", "@angular/common": "^16.2.0", "@angular/compiler": "^16.2.0", "@angular/core": "^16.2.0", "@angular/fire": "^16.0.0", "@angular/forms": "^16.2.0", "@angular/platform-browser": "^16.2.0", "@angular/platform-browser-dynamic": "^16.2.0", "@angular/router": "^16.2.0", "@firebase/vertexai-preview": "^0.0.3", "@fullcalendar/angular": "^6.1.14", "@fullcalendar/core": "^6.1.14", "@fullcalendar/daygrid": "^6.1.14", "@fullcalendar/interaction": "^6.1.14", "@fullcalendar/timegrid": "^6.1.14", "@ng-select/ng-option-highlight": "^11.0.6", "@ng-select/ng-select": "^11.0.6", "@ngneat/query": "^2.0.1", "@ngx-loading-bar/core": "^6.0.2", "@ngx-loading-bar/router": "^6.0.2", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "@tanstack/query-core": "^5.44.0", "chart.js": "^4.4.3", "d3": "^7.9.0", "date-fns": "^4.1.0", "git-describe": "^4.1.1", "jwt-decode": "^3.0.0", "lodash": "^4.17.4", "material-icons": "^1.13.12", "ng2-datepicker": "^12.0.0", "ngx-image-zoom": "^3.0.0", "ngx-loading": "^16.0.0", "ngx-skeleton-loader": "^7.0.0", "primeflex": "^3.3.1", "primeicons": "^7.0.0", "primeng": "16.9.1", "prismjs": "^1.29.0", "quill": "^2.0.2", "rxjs": "~7.8.0", "smartqc-web": "file:", "spark-md5": "3.0.2", "tslib": "^2.3.0", "xlsx": "^0.18.5", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.14", "@angular-eslint/eslint-plugin": "^18.3.1", "@angular-eslint/eslint-plugin-template": "^18.3.1", "@angular-eslint/template-parser": "^18.3.1", "@angular/cli": "^16.2.14", "@angular/compiler-cli": "^16.2.0", "@angularclass/hmr": "^3.0.0", "@types/jasmine": "~4.3.0", "@types/lodash": "^4.17.5", "@types/node": "^22.5.5", "@types/spark-md5": "^3.0.5", "@typescript-eslint/eslint-plugin": "^8.5.0", "eslint-plugin-unused-imports": "^4.1.4", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "prettier": "^3.3.2", "prettier-eslint": "^16.3.0", "tailwindcss": "^3.4.3", "typescript": "~5.1.3"}}