.add-template-button .p-button {
    background-color: #0b5ed7 !important;
}
.cdk-drag-preview {
    padding: 0.75rem;
    border-radius: 0.75rem;
    background-color: rgb(96 165 250);
    box-sizing: border-box;
    box-shadow:
        0 5px 5px -3px rgba(0, 0, 0, 0.2),
        0 8px 10px 1px rgba(0, 0, 0, 0.14),
        0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
    opacity: 0;
}

.cdk-drag-animating {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.drag-custom-box:last-child {
    border: none;
}

.drag-custom-list.cdk-drop-list-dragging .drag-custom-box:not(.cdk-drag-placeholder) {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}


:host ::ng-deep  .p-chip-text {
    margin: 0 !important;
}
