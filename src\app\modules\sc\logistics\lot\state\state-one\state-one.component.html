<p-panel header="Giai đoạn 1: Chi tiết lô" [toggleable]="true">
    <app-form *ngIf="formGroup" #formStateOne [formGroup]="formGroup" layout="vertical">
        <div class="tw-grid lg:tw-grid-cols-4 tw-grid-cols-2 tw-gap-6">
            <app-form-item label="Đại lý" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_logistics_edit', 'sc_logistics_view']">
                <app-filter-table
                    type="select-one"
                    [configSelect]="{
                        fieldValue: 'id',
                        fieldLabel: 'name',
                        options: optionAgent,
                        filterLocal: true,
                    }"
                    formControlName="salesAgentId"
                ></app-filter-table>
            </app-form-item>
            <app-form-item label="&nbsp;" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_logistics_edit', 'sc_logistics_view']">
                <p-button label="Thêm đại lý" icon="pi pi-plus" size="small" (click)="visible = true; initFormAgentFunc()"></p-button>
            </app-form-item>
        </div>
        <br />
        <p-panel header="Cước phí vận chuyển dự kiến" [toggleable]="true">
            <div class="tw-flex tw-justify-end tw-mb-4 tw-mx-4 tw-gap-4">
                <p-button icon="pi pi-filter" [outlined]="true" [size]="'small'" (click)="op.toggle($event)"></p-button>
            </div>
            <p-table styleClass="p-datatable-gridlines" [value]="lot ? [lot.estimatedTransportCost] : []" [scrollable]="true">
                <ng-template pTemplate="header">
                    <tr>
                        <th class="tw-whitespace-nowrap p-th-sticky" style="left: 0rem; min-width: 10rem">NCC dịch vụ</th>
                        <th class="tw-whitespace-nowrap" class="p-th-sticky" style="left: 10rem; min-width: 15rem">Phương thức vận chuyển</th>
                        <th *ngIf="!columns[2].hide" style="min-width: 10rem">Tỷ giá VND/USD</th>
                        <th *ngIf="!columns[3].hide" style="min-width: 10rem">Phí chứng từ đầu xuất</th>
                        <th *ngIf="!columns[4].hide" style="min-width: 10rem">Phí hải quan xuất khẩu</th>
                        <th *ngIf="!columns[5].hide" style="min-width: 10rem">Phí bến bãi hàng truck qua cửa khẩu</th>
                        <th *ngIf="!columns[6].hide" style="min-width: 10rem">Phí lưu ca xe đầu xuất</th>
                        <th *ngIf="!columns[7].hide" style="min-width: 10rem">Phí EXW đầu xuất</th>
                        <th *ngIf="!columns[8].hide" style="min-width: 10rem">Phí FCA đầu xuất</th>
                        <th *ngIf="!columns[9].hide" style="min-width: 10rem">Phí local change đầu xuất</th>
                        <th *ngIf="!columns[10].hide" style="min-width: 10rem">Phí giấy phép xuất khẩu</th>
                        <th *ngIf="!columns[11].hide" style="min-width: 10rem">Phí kiểm tra từ tính</th>
                        <th *ngIf="!columns[12].hide" style="min-width: 10rem">Phí đầu xuất phát sinh khác(Nếu có)</th>
                        <th *ngIf="!columns[13].hide" style="min-width: 10rem">Phí trucking từ kho nhà máy đến cửa khẩu/cảng biển/cảng hàng không</th>
                        <th *ngIf="!columns[14].hide" style="min-width: 10rem">Phí nâng hạ bốc xếp tại cảng nhập(Chi hộ)</th>
                        <th *ngIf="!columns[15].hide" style="min-width: 10rem">Cước hàng không/biển/bộ chuyển phát nhanh</th>
                        <th *ngIf="!columns[16].hide" style="min-width: 10rem">Phí local carge tại cảng nhập</th>
                        <th *ngIf="!columns[17].hide" style="min-width: 10rem">Phí bến bãi biên phòng kiểm dịch</th>
                        <th *ngIf="!columns[18].hide" style="min-width: 10rem">Phí cầu cảng</th>
                        <th *ngIf="!columns[19].hide" style="min-width: 10rem">Phí hải quan giám sát tại cửa khẩu</th>
                        <th *ngIf="!columns[20].hide" style="min-width: 10rem">Phí dịch vụ hải quan tại cảng nhập</th>
                        <th *ngIf="!columns[21].hide" style="min-width: 10rem">Phí vận chuyển nội địa từ cảng nhập đến địa điểm nhận hàng cuối cùng</th>
                        <th *ngIf="!columns[22].hide" style="min-width: 10rem">Phí phát sinh khác tại cảng nhập(Nếu có)</th>
                        <th *ngIf="!columns[23].hide" style="min-width: 10rem">Phí bốc xếp tại kho(Nếu có)</th>
                        <th *ngIf="!columns[24].hide" style="min-width: 15rem">Tổng chi phí vận chuyển dự kiến dự kiến (VNĐ)</th>
                        <th *ngIf="!columns[25].hide" style="min-width: 10rem">Tổng chi phí vận chuyển dự kiến (VNĐ) SAU KHI TRỪ PHẦN PHÍ NCC CHỊU (Nếu có)</th>
                        <th *ngIf="!columns[26].hide" style="min-width: 10rem">Đơn giá tính cước quốc tế (USD)</th>
                        <th *ngIf="!columns[27].hide" style="min-width: 10rem">Đơn vị tính cước quốc tế(CBM/CONT/KG)</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                    <tr>
                        <td class="p-td-sticky" style="left: 0rem; min-width: 10rem">
                            {{ item.logisticShortName }}
                        </td>
                        <td class="p-td-sticky" style="left: 10rem; min-width: 15rem">
                            {{ mapTypeShippingMethod[item.shippingMethodId] + (item.roadNote ? '(' + item.roadNote + ')' : '') }}
                        </td>
                        <td *ngIf="!columns[2].hide">
                            {{ item.exchangeRate | number }}
                        </td>
                        <td *ngIf="!columns[3].hide">
                            {{ item.documentFee | number }}
                        </td>
                        <td *ngIf="!columns[4].hide">
                            {{ item.exportCustomsFee | number }}
                        </td>
                        <td *ngIf="!columns[5].hide">
                            {{ item.borderTruckYardFee | number }}
                        </td>
                        <td *ngIf="!columns[6].hide">
                            {{ item.exportTruckStorageFee | number }}
                        </td>
                        <td *ngIf="!columns[7].hide">
                            {{ item.exwFee | number }}
                        </td>
                        <td *ngIf="!columns[8].hide">
                            {{ item.fcaFee | number }}
                        </td>
                        <td *ngIf="!columns[9].hide">
                            {{ item.exportLocalCharge | number }}
                        </td>
                        <td *ngIf="!columns[10].hide">
                            {{ item.exportLicenseFee | number }}
                        </td>
                        <td *ngIf="!columns[11].hide">
                            {{ item.magneticInspectionFee | number }}
                        </td>
                        <td *ngIf="!columns[12].hide">
                            {{ item.otherExportFees | number }}
                        </td>
                        <td *ngIf="!columns[13].hide">
                            {{ item.factoryToPortFee | number }}
                        </td>
                        <td *ngIf="!columns[14].hide">
                            {{ item.importPortHandlingFee | number }}
                        </td>
                        <td *ngIf="!columns[15].hide">
                            {{ item.mainTransportCost | number }}
                        </td>
                        <td *ngIf="!columns[16].hide">
                            {{ item.importLocalCharge | number }}
                        </td>
                        <td *ngIf="!columns[17].hide">
                            {{ item.borderInspectionFee | number }}
                        </td>
                        <td *ngIf="!columns[18].hide">
                            {{ item.wharfFee | number }}
                        </td>
                        <td *ngIf="!columns[19].hide">
                            {{ item.supervisionFee | number }}
                        </td>
                        <td *ngIf="!columns[20].hide">
                            {{ item.serviceFee | number }}
                        </td>
                        <td *ngIf="!columns[21].hide">
                            {{ item.domesticTransportFee | number }}
                        </td>
                        <td *ngIf="!columns[22].hide">
                            {{ item.otherImportFees | number }}
                        </td>
                        <td *ngIf="!columns[23].hide">
                            {{ item.warehouseHandlingFee | number }}
                        </td>
                        <td *ngIf="!columns[24].hide">
                            {{ item.totalEstimatedTransportCost | number }}
                        </td>
                        <td *ngIf="!columns[25].hide">
                            {{ item.totalCostAfterSupplierDeduction | number }}
                        </td>
                        <td *ngIf="!columns[26].hide">
                            {{ item.internationalPrice | number }}
                        </td>
                        <td *ngIf="!columns[27].hide">
                            <app-filter-table
                                type="select-one"
                                [configSelect]="{
                                    fieldValue: 'id',
                                    fieldLabel: 'name',
                                    rsql: true,
                                    url: '/sc/api/international-rate-unit/search',
                                    paramForm: 'id',
                                }"
                                [disabled]="true"
                                [initValue]="item.internationalRateUnitId"
                            ></app-filter-table>
                        </td>
                    </tr>
                </ng-template>
            </p-table>
        </p-panel>
        <br />
        <p-panel header="Lịch trình dự kiến" [toggleable]="true">
            <p-table styleClass="p-datatable-gridlines" [value]="lot ? [lot?.estimatedSchedule] : []" [scrollable]="true">
                <ng-template pTemplate="header">
                    <tr>
                        <th class="tw-whitespace-nowrap">Nhà cung cấp dịch vụ</th>
                        <th class="tw-whitespace-nowrap">Phương thức vận chuyển</th>
                        <th style="min-width: 15rem">Ngày hàng sẵn sàng tại địa điểm giao hàng</th>
                        <th style="min-width: 15rem">Ngày yêu cầu hàng về địa điểm nhận hàng cuối cùng</th>
                        <th style="min-width: 15rem">Ngày giao hàng tại cảng xuất</th>
                        <th style="min-width: 15rem">Ngày tàu chạy từ cảng xuất</th>
                        <th style="min-width: 15rem">Ngày tàu về cảng nhập</th>
                        <th style="min-width: 15rem">Thời gian khai nộp thuế, làm thủ tục hải quan và tại cảng nhập</th>
                        <th style="min-width: 15rem">Ngày giao hàng tại địa điểm cuối cùng</th>
                        <th style="min-width: 20rem">Ghi chú</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                    <tr>
                        <td>
                            {{ item.logisticShortName }}
                        </td>
                        <td>
                            {{ mapTypeShippingMethod[item.shippingMethodId] + (item.roadNote ? '(' + item.roadNote + ')' : '') }}
                        </td>
                        <td>
                            {{ item.readyDate | date: 'dd/MM/yyyy' }}
                        </td>
                        <td>
                            {{ item.finalDeliveryRequestDate | date: 'dd/MM/yyyy' }}
                        </td>
                        <td>
                            {{ item.exportPortDeliveryDate | date: 'dd/MM/yyyy' }}
                        </td>
                        <td>
                            {{ item.departureDate | date: 'dd/MM/yyyy' }}
                        </td>
                        <td>
                            {{ item.arrivalDate | date: 'dd/MM/yyyy' }}
                        </td>
                        <td>
                            {{ item.clearanceTime }}
                        </td>
                        <td>
                            {{ item.finalDeliveryDate | date: 'dd/MM/yyyy' }}
                        </td>
                        <td>
                            {{ item.note }}
                        </td>
                    </tr>
                </ng-template>
            </p-table>
        </p-panel>
        <br />
        <p-panel header="Phí bảo hiểm dự kiến" [toggleable]="true" *ngIf="lot?.estimatedInsurance">
            <p-table styleClass="p-datatable-gridlines" [value]="lot ? [lot.estimatedInsurance] : []" [scrollable]="true">
                <ng-template pTemplate="header">
                    <tr>
                        <th class="tw-whitespace-nowrap">Nhà cung cấp dịch vụ bảo hiểm</th>
                        <th class="tw-whitespace-nowrap">Phương thức vận chuyển</th>
                        <th style="min-width: 15rem">Tỷ lệ phí bảo hiểm</th>
                        <th style="min-width: 15rem">Tỷ giá VNĐ/USD</th>
                        <th style="min-width: 15rem">Tổng số tiền bảo hiểm (USD)</th>
                        <th style="min-width: 15rem">Phí bảo hiểm (VNĐ)</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                    <tr>
                        <td>
                            {{ item.logisticShortName }}
                        </td>
                        <td>
                            {{ mapTypeShippingMethod[item.shippingMethodId] + (item.roadNote ? '(' + item.roadNote + ')' : '') }}
                        </td>
                        <td>{{ item.insuranceRate | number }} {{ item.insuranceRate ? '%' : '' }}</td>
                        <td>
                            {{ item.exchangeRate | number }}
                        </td>
                        <td>
                            {{ item.totalInsuranceValue | number }}
                        </td>
                        <td>
                            {{ item.insuranceFee | number }}
                        </td>
                    </tr>
                </ng-template>
            </p-table>
        </p-panel>
        <br />
        <app-form-item label="Ghi chú">
            <textarea rows="5" formControlName="noteLot" pInputTextarea class="tw-w-full"></textarea>
        </app-form-item>
        <br />
        <app-form-item label="Thông tin đính kèm">
            <app-button-group-file
                simpleUpload=""
                (onFileSelected)="handleUploadFile($event)"
                [attachments]="formGroup.getRawValue().lotAttachments"
                formControlName="lotAttachmentIds"
                [multiple]="true"
            ></app-button-group-file>

            <app-button-group-file
                *ngIf="formGroup.getRawValue().lotAttachments && formGroup.getRawValue().lotAttachments.length > 0"
                class="tw-col-span-2"
                (onFileSelected)="handleUploadFile($event)"
                [multiple]="true"
                simpleUpload=""
                formControlName="lotAttachmentIds"
            ></app-button-group-file>
        </app-form-item>
        <br />
    </app-form>
    <div class="tw-flex tw-justify-end tw-gap-4">
        <p-button label="Thông báo cho người tiếp nhận" type="button" severity="primary" size="small" (click)="showPopupSubmit()"></p-button>
    </div>
    <br />
</p-panel>
<p-overlayPanel #op>
    <p-table [value]="columns" [selection]="columnChoose" (selectionChange)="setColumnSelection($event)" [scrollable]="true" scrollHeight="500px">
        <ng-template pTemplate="header">
            <tr>
                <th>
                    <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                </th>
                <th>Tất cả</th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rowData>
            <tr>
                <td style="font-size: 14px; padding: 8px 12px">
                    <p-tableCheckbox [value]="rowData" [hidden]="rowData.default" />
                </td>
                <td style="font-size: 14px; padding: 8px 12px">
                    {{ rowData['header'] }}
                </td>
            </tr>
        </ng-template>
    </p-table>
</p-overlayPanel>

<p-dialog
    header="Tạo đại lý nhà cung cấp Logistics"
    [(visible)]="visible"
    [style]="{ width: '80vw' }"
    [breakpoints]="{ '1199px': '80vw', '575px': '100vw' }"
    (onHide)="closePopupAgents()"
>
    <hr style="margin: 0" />
    <br />
    <app-form [formGroup]="formGroupAgents" layout="vertical">
        <ng-container formArrayName="logisticsAgents">
            <p-panel header="Danh sách đại lý" [toggleable]="true">
                <p-table styleClass="p-datatable-gridlines" [value]="agents.controls">
                    <ng-template pTemplate="header">
                        <tr>
                            <th style="max-width: 5rem">Thao tác</th>
                            <th style="max-width: 30rem">Tên <span class="tw-text-red-500">*</span></th>
                            <th style="min-width: 10rem">Địa chỉ <span class="tw-text-red-500">*</span></th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                        <tr *ngIf="item.value.isEdit" [formGroupName]="rowIndex">
                            <td>
                                <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                    <button
                                        class="p-link tw-p-2 bg-green-300 hover:tw-bg-green-400 tw-text-white"
                                        (click)="saveItem(rowIndex)"
                                        pTooltip="Lưu"
                                        tooltipPosition="top"
                                        type="button"
                                    >
                                        <span class="pi pi-save"></span>
                                    </button>
                                    <button
                                        class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                        (click)="cancelCreate(rowIndex)"
                                        pTooltip="Hủy"
                                        tooltipPosition="top"
                                        type="button"
                                    >
                                        <span class="pi pi-times"></span>
                                    </button>
                                </div>
                            </td>
                            <td>
                                <app-form-item label="">
                                    <input pInputText class="tw-w-full" formControlName="name" />
                                </app-form-item>
                            </td>
                            <td>
                                <app-form-item label="">
                                    <textarea rows="2" pInputTextarea class="tw-w-full" formControlName="address"></textarea>
                                </app-form-item>
                            </td>
                        </tr>
                        <tr *ngIf="!item.value.isEdit">
                            <td>
                                <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                    <button
                                        class="p-link tw-p-2 bg-green-300 hover:tw-bg-green-400 tw-text-white"
                                        (click)="editItem(rowIndex)"
                                        pTooltip="Sửa"
                                        tooltipPosition="top"
                                        type="button"
                                        *ngIf="!isAddingAgent && !isEditingAgent"
                                    >
                                        <span class="pi pi-pencil"></span>
                                    </button>
                                    <button
                                        class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                        (click)="deleteItem(rowIndex)"
                                        pTooltip="Xóa"
                                        tooltipPosition="top"
                                        type="button"
                                        *ngIf="!isAddingAgent && !isEditingAgent"
                                    >
                                        <span class="pi pi-trash"></span>
                                    </button>
                                </div>
                            </td>
                            <td>{{ item.getRawValue().name }}</td>
                            <td>{{ item.getRawValue().address }}</td>
                        </tr>
                    </ng-template>
                </p-table>
                <div class="tw-mt-3">
                    <p-button
                        label="Thêm"
                        icon="pi pi-plus"
                        severity="info"
                        size="small"
                        [disabled]="isEditingAgent || isAddingAgent"
                        (click)="addItem()"
                    ></p-button>
                </div>
            </p-panel>
        </ng-container>
    </app-form>
    <ng-template pTemplate="footer">
        <div>
            <p-button label="Đóng" [text]="true" [raised]="true" size="small" severity="secondary" (click)="closePopupAgents()"></p-button>
        </div>
    </ng-template>
</p-dialog>

<p-dialog
    header="Thông báo cho người tiếp nhận"
    [(visible)]="visibleSubmit"
    [style]="{ width: '80vw' }"
    [breakpoints]="{ '1199px': '80vw', '575px': '100vw' }"
    (onHide)="formGroupSubmit.reset()"
>
    <hr style="margin: 0" />
    <br />
    <app-form #formSubmit *ngIf="visibleSubmit" [formGroup]="formGroupSubmit" layout="vertical" (onSubmit)="sendNotification($event)">
        <app-form-item label="Nội dung">
            <textarea rows="5" formControlName="content" pInputTextarea class="tw-w-full"></textarea>
        </app-form-item>
        <app-form-item label="Người tiếp nhận" [isRequired]="true">
            <app-filter-table
                type="select"
                [configSelect]="{
                    fieldValue: 'id',
                    fieldLabel: 'email',
                    options: receivers,
                    filterLocal: true,
                }"
                (onChange)="handleChangeReceivers($event)"
            ></app-filter-table>
        </app-form-item>
    </app-form>
    <ng-template pTemplate="footer">
        <div>
            <p-button
                severity="primary"
                size="small"
                (click)="formSubmit.handleSubmit()"
                label="Xác nhận gửi"
                [disabled]="formGroupSubmit.invalid"
                *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_lot_edit']"
            ></p-button>
            <p-button
                label="Hủy"
                [text]="true"
                [raised]="true"
                size="small"
                severity="secondary"
                (click)="visibleSubmit = false; formGroupSubmit.reset()"
            ></p-button>
        </div>
    </ng-template>
</p-dialog>
