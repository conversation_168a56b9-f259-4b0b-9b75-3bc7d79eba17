import { canAuthorize } from '../../../core/auth/auth.guard';

export const TTFRouting = {
    path: 'technology-transfer-forms',
    title: 'Phiếu CG CNSX',
    canActivate: [canAuthorize],
    data: { authorize: ['ROLE_SYSTEM_ADMIN'] },
    children: [
        {
            path: '',
            title: '<PERSON><PERSON> sách yêu cầu CG CNSX',
            data: { authorize: ['ROLE_SYSTEM_ADMIN'] },
            loadComponent: () => import('./list/list.component').then((c) => c.TechnologyTransferFormsListComponent),
        },
        // {
        //     path: 'create',
        //     title: 'T<PERSON><PERSON> mới hồ sơ sản phẩm',
        //     data: { authorize: ['ROLE_SYSTEM_ADMIN'] },
        //     loadComponent: () => import('./edit/product-file.edit.component').then((c) => c.ProductFileEditComponent),
        // },
        // {
        //     path: 'edit/:productId/:versionId',
        //     title: 'Chỉnh s<PERSON><PERSON> hồ sơ sản phẩm',
        //     data: { authorize: ['ROLE_SYSTEM_ADMIN'] },
        //     loadComponent: () => import('./edit/product-file.edit.component').then((c) => c.ProductFileEditComponent),
        // },
        // {
        //     path: 'view/:productId/:versionId',
        //     title: 'Xem chi tiết hồ sơ sản phẩm',
        //     data: { authorize: ['ROLE_SYSTEM_ADMIN'] },
        //     loadComponent: () => import('./edit/product-file.edit.component').then((c) => c.ProductFileEditComponent),
        // },
    ],
};
