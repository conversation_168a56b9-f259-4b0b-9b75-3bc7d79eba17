@import './_component.scss';
@import './mixins';
@import './preloading';
@import './typography';

.transaction-bg {
    transition: background-color 0.2s ease; /* Transition for background color */
}

.tooltip {
    max-width: max-content !important;
}

.rotate-180 {
    transform: rotate(180deg);
    transition: transform 0.1s ease;
}

.rotate-revert {
    transform: rotate(0);
    transition: transform 0.1s ease;
}
.db-container {
    margin: auto;
    width: 95%;
}

.in-module-container {
    margin-top: 8px;
    display: flex;
    flex-wrap: wrap;      /* Cho phép các item xuống dòng */
    gap: 16px;            /* <PERSON><PERSON><PERSON>ng cách giữa các div (tùy chỉnh) */
}

.in-module {
    flex: 1 1 240px;
    background: #ffffff;

    /*box-shadow: 0px 0px 0px 0px rgba(165, 163, 174, 0);*/
    border-radius: 12px;
    padding: 16px;
    cursor: pointer;
    /*opacity: 0.6;*/
    transition: 0.3s;
    width: 220px;

}
.in-module-card {
    width: 220px;
}
.in-module-icon {
    text-align: center;
    //padding-top: 12px;
    //padding-bottom: 12px;
    background: #fafafa;
    border-radius: 8px;
}
.in-module-image{
    width: 100%;
    height: 120px;
    object-fit: cover;
}
.in-module-text {
    text-align: center;
    min-height: 40px;
    color: #444;
    font-size: 16px;
}
.muted-text{
    color: #737373;
    padding-top: 24px;
    padding-bottom: 4px;
}
.in-module:hover {
    background: white;
    transform: scale(1.05) !important;
    box-shadow: 0px 4px 16px 0px rgba(180, 180, 180, 0.3);
    .in-exam-name {
        /*transition: 0.3s;*/
        -webkit-line-clamp: 4;         /* Số dòng muốn hiển thị */
    }
    .in-module-text {
        color: #366BA1;
    }
    .in-module-image {
        filter: drop-shadow(2px 2px 2px #888);
    }
}

.in-login-container{
    margin-top: 8px;
}

.in-login-card {
    display: inline-block;
    background: #ffffff;
    border-radius: 12px;
    min-width: 26rem !important;
    max-width: 30rem !important;
}

@media only screen and (max-width: 1400px){
    .in-module {
        width: 100%;
    }
    .in-module-image{
        width: 100%;
        height: 120px;
    }
    .in-module-card{
        width: 220px;
    }
}

@media only screen and (max-width: 1080px){
    .in-module {
        width: 100%;
    }
    .in-module-image{
        width: 100%;
        height: 140px;
    }
    .in-module-card{
        width: 30%;
    }
}

@media only screen and (max-width: 700px){
    .in-module {
        width: 100%;
    }
    .in-module-image{
        width: 100%;
        height: 200px;
    }
    .in-module-card{
        width: 100%;
    }
}

