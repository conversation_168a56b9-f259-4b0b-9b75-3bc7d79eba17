import { CommonModule } from '@angular/common';
import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { TableModule } from 'primeng/table';
import { Po, PoDetail, PoDetailDay } from 'src/app/models/interface/sc';
import { PoDetailService } from 'src/app/services/sc/po/po-detail.service';
import { ButtonGroupFileComponent } from 'src/app/shared/components/button-group-file/button-group-file.component';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { AlertService } from 'src/app/shared/services/alert.service';
import { FileService } from 'src/app/shared/services/file.service';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { TooltipModule } from 'primeng/tooltip';
import { Column } from 'src/app/models/interface';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { DecimalPipe } from '@angular/common';

@Component({
    selector: 'app-list-po-detail',
    templateUrl: './list-po-detail.component.html',
    styleUrls: ['./list-po-detail.component.scss'],
    standalone: true,
    imports: [TableModule, ButtonGroupFileComponent, CommonModule, ButtonModule, HasAnyAuthorityDirective, TooltipModule, OverlayPanelModule],
    providers: [PoDetailService, DecimalPipe],
})
export class ListPoDetailComponent implements OnInit, OnChanges {
    @Input() po: Po;
    allPoDetailDays: PoDetailDay[];
    detailDaysFilter: PoDetailDay[];
    detailDaysMap: Map<string, PoDetailDay>;
    poDetailFirst: PoDetail = {
        poId: null,
        accountingCode: '',
        internalReference: '',
        display: '',
        manufacturer: '',
        manPn: '',
        description: '',
        unit: '',
        unitPrice: '',
        quantity: 0,
        price: 0,
        amount: 0,
        deliveredQuantity: 0,
        deliveredAmount: 0,
        remainingQuantity: 0,
        remainingAmount: 0,
        poDetailDays: [],
    };

    detailDaysFake = [
        {
            amount: 1,
            quantity: 1,
            quantityHistory: '',
        },
    ];

    totalAmountDateMap: Map<string, number>;
    totalAmount: number;
    totalDeliveredAmount: number;
    totalRemainingAmount: number;

    columns = [
        {
            header: 'STT',
            field: 'stt',
            default: true,
        },
        {
            header: 'Mã kế toán',
            field: 'accountingCode',
            default: true,
        },
        {
            header: 'Mã VNPT',
            field: 'internalReference',
            default: true,
        },
        {
            header: 'MAN PN',
            field: 'manPn',
            hide: false,
        },
        {
            header: 'Nhà sản xuất',
            field: 'manufacturer',
            hide: false,
        },
        {
            header: 'Description',
            field: 'description',
            hide: false,
        },
        {
            header: 'Đơn vị tính',
            field: 'unit',
            hide: false,
        },
        {
            header: 'Thông tin đặt hàng',
            field: 'order',
            hide: false,
        },
        {
            header: 'Thông tin hàng về',
            field: 'orderDelivery',
            hide: false,
        },
        {
            header: 'Thông tin hàng đã về',
            field: 'orderDelivered',
            hide: false,
        },
        {
            header: 'Thông tin hàng chưa về',
            field: 'orderDelivering',
            hide: false,
        },
    ];
    columnChoose = [];
    constructor(
        private loadingService: LoadingService,
        private alertService: AlertService,
        private fileService: FileService,
        private poDetailService: PoDetailService,
        private decimalPipe: DecimalPipe,
    ) {}

    ngOnInit(): void {
        this.columnChoose = this.columns;
        if (!this.po.poDetails) {
            this.po.poDetails = [];
        } else if (this.po.poDetails.length > 0) {
            this.poDetailFirst = this.po.poDetails[0];
        }

        this.updateTotal();
        this.filterDetailDays(this.po.poDetails);
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['po']) {
            this.updatePoDetailFirst();
            this.filterDetailDays(this.po.poDetails);
        }
    }

    private filterDetailDays(poDetailDays: PoDetailDay[]) {
        this.allPoDetailDays = this.po.poDetailDays;
        this.detailDaysMap = this.po.detailDaysMap;

        // Lọc ra các cặp date + invNumber duy nhất
        this.detailDaysFilter = Object.values(
            (this.allPoDetailDays ?? []).reduce((acc, item) => {
                const key = item.invTransferNumber + '-' + item.date;
                if (!acc[key]) {
                    acc[key] = { invTransferNumber: item.invTransferNumber, date: item.date };
                }
                return acc;
            }, {}),
        );
    }

    private updatePoDetailFirst(): void {
        if (!this.po.poDetails || this.po.poDetails.length === 0) {
            this.poDetailFirst = {
                poId: null,
                accountingCode: '',
                internalReference: '',
                display: '',
                manufacturer: '',
                manPn: '',
                description: '',
                unit: '',
                unitPrice: '',
                quantity: 0,
                price: 0,
                amount: 0,
                deliveredQuantity: 0,
                deliveredAmount: 0,
                remainingQuantity: 0,
                remainingAmount: 0,
                poDetailDays: [],
            };
        } else {
            this.poDetailFirst = this.po.poDetails[0];
        }

        this.updateTotal();
    }

    export() {
        this.loadingService.show();
        this.poDetailService.exportDetailOfPo(this.po.id).subscribe({
            next: (res) => {
                this.loadingService.hide();
                this.fileService.downLoadFileByService(res.url, '/sc/api');
            },
            error: (e) => {
                this.alertService.handleError(e);
                this.loadingService.hide();
            },
        });
    }

    updateTotal() {
        this.totalAmount = this.getTotalAmount();
        console.log(this.totalAmount);
        this.totalDeliveredAmount = this.getTotalDeliveredAmount();
        this.totalRemainingAmount = this.getTotalRemainingAmount();
        this.totalAmountDateMap = this.getTotalAmountByDate();
    }

    getTotalAmount(): number {
        return this.po.poDetails.reduce((total, item) => {
            const roundedAmount = this.roundAmount(item.amount || 0);
            return total + roundedAmount;
        }, 0);
    }

    getTotalDeliveredAmount(): number {
        return this.po.poDetails.reduce((total, item) => {
            const roundedDelivered = this.roundAmount(item.deliveredAmount || 0);
            return total + roundedDelivered;
        }, 0);
    }

    getTotalRemainingAmount(): number {
        return this.po.poDetails.reduce((total, item) => {
            const roundedRemaining = this.roundAmount(item.remainingAmount || 0);
            return total + roundedRemaining;
        }, 0);
    }

    getTotalAmountByDate(): Map<string, number> {
        const dateAmountMap = new Map<string, number>();

        this.po.poDetails.forEach((poDetail) => {
            poDetail.poDetailDays.forEach((day) => {
                const key = `${day.date}-${day.invTransferNumber}`;
                const currentAmount = dateAmountMap.get(key) || 0;
                const roundedDayAmount = this.roundAmount(day.amount || 0);
                dateAmountMap.set(key, currentAmount + roundedDayAmount);
            });
        });

        return dateAmountMap;
    }

    private roundAmount(amount: number): number {
        const isUSD = this.poDetailFirst?.unitPrice === 'USD';
        if (isUSD) {
            const formatter = new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
                useGrouping: false, // Tắt dấu phẩy hàng nghìn
            });
            return Number(formatter.format(amount)); // "4924.50" -> 4924.5
        }
        return Math.round(amount);
    }

    formatTooltipContent(jsonString: string | null): string {
        if (!jsonString) {
            return `Import: 0\nIQC: 0\nExport: 0`;
        }

        try {
            const data = JSON.parse(jsonString);
            return `Import: ${data.import ?? 0}\nIQC: ${data.iqc ?? 0}\nExport: ${data.export ?? 0}`;
        } catch {
            return `Import: 0\nIQC: 0\nExport: 0`;
        }
    }

    setColumnSelection(selectedColumns: Column[]) {
        if (selectedColumns.length === 0) {
            this.columns.forEach((c) => {
                if (!c.default) {
                    c.hide = true;
                }
            });
            return;
        } else {
            this.columns.forEach((c) => {
                if (!c.default) {
                    if (selectedColumns.some((s) => c.field === s.field)) {
                        c.hide = false; // Show the column
                    } else {
                        c.hide = true; // Hide the column
                    }
                }
            });
        }
    }

    getRowSpan(index: number): number {
        if (index === 0 || this.po.poDetails[index].indexLevel !== this.po.poDetails[index - 1].indexLevel) {
            return this.po.poDetails.filter((p) => p.indexLevel === this.po.poDetails[index].indexLevel).length;
        }
        return 0;
    }

    getDetailDayFromDetailDaysMap(internalReference: string, accountingCode: string, invTransferNumber: string, date: number) {
        const key = `${internalReference}-${accountingCode}-${invTransferNumber}-${date}`;
        const detailDay = this.detailDaysMap[key] || { quantity: 0, amount: 0 };
        return detailDay;
    }
}
