<div class="tw-grid lg:tw-grid-cols-2 tw-grid-cols-1 tw-gap-4">
    <div class="tw-mb-4 tw-flex tw-items-center">
        <div>
            <span class="tw-font-bold">Số PO /Hợp đồng: </span>
            <span>{{ po.orderNo }}</span>
        </div>
    </div>
    <div class="tw-flex tw-justify-end tw-mb-4 tw-mx-4 tw-gap-4">
        <p-button icon="pi pi-filter" [outlined]="true" [size]="'small'" (click)="op.toggle($event)"></p-button>

        <p-button
            label="Xuất bảng kê"
            size="small"
            (click)="export()"
            *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_po_export']"
        >
        </p-button>
    </div>
</div>

<p-table
    [value]="po.poDetails"
    styleClass="p-datatable-gridlines "
    scrollHeight="700px"
    [resizableColumns]="true"
    [scrollable]="true"
>
    <ng-template pTemplate="header">
        <tr>
            <th *ngIf="!columns[0].hide" rowspan="3" class="p-th-sticky" style="left: 0px; min-width: 4rem">STT</th>
            <th *ngIf="!columns[1].hide" rowspan="3" class="p-th-sticky" style="left: 4rem; min-width: 18rem">
                Mã kế toán
            </th>
            <th *ngIf="!columns[2].hide" rowspan="3" class="p-th-sticky" style="left: 22rem; min-width: 15rem">
                Mã VNPT
            </th>
            <th *ngIf="!columns[3].hide" rowspan="3">MAN PN</th>
            <th *ngIf="!columns[4].hide" rowspan="3">Nhà sản xuất</th>
            <th *ngIf="!columns[5].hide" rowspan="3">Description</th>
            <th *ngIf="!columns[6].hide" rowspan="3">Đơn vị tính</th>
            <th *ngIf="!columns[7].hide" colspan="3" rowspan="2">Thông tin đặt hàng</th>
            <th
                *ngIf="!columns[8].hide"
                [attr.colspan]="detailDaysFilter.length === 0 ? 2 : detailDaysFilter.length * 2"
            >
                Thông tin hàng về
            </th>
            <th *ngIf="!columns[9].hide" colspan="2" rowspan="2">Thông tin hàng đã về</th>
            <th *ngIf="!columns[10].hide" colspan="2" rowspan="2">Thông tin hàng chưa về</th>
        </tr>
        <tr>
            <ng-container *ngIf="detailDaysFilter.length > 0 && !columns[8].hide">
                <th colspan="2" style="width: 10rem" *ngFor="let item of detailDaysFilter ?? [{ date: '' }]">
                    {{ item.date | date: 'dd/MM/yyyy' }}
                </th>
            </ng-container>
            <ng-container
                *ngIf="(!detailDaysFilter || detailDaysFilter.length === 0) && !columns[8].hide"
            >
                <th colspan="2"></th>
            </ng-container>
        </tr>
        <tr>
            <th *ngIf="!columns[7].hide">Đơn giá</th>
            <th *ngIf="!columns[7].hide">Số lượng</th>
            <th *ngIf="!columns[7].hide">Thành tiền</th>
            <ng-container *ngIf="detailDaysFilter.length > 0 && !columns[8].hide">
                <ng-container *ngFor="let item of detailDaysFilter">
                    <th>Số lượng</th>
                    <th>Thành tiền</th>
                </ng-container>
            </ng-container>

            <ng-container *ngIf="detailDaysFilter.length === 0 && !columns[8].hide">
                <th>Số lượng</th>
                <th>Thành tiền</th>
            </ng-container>

            <th *ngIf="!columns[9].hide">Số lượng</th>
            <th *ngIf="!columns[9].hide">Thành tiền</th>
            <th *ngIf="!columns[10].hide">Số lượng</th>
            <th *ngIf="!columns[10].hide">Thành tiền</th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-poDetail let-rowIndex="rowIndex">
        <tr>
            <td
                *ngIf="getRowSpan(rowIndex) > 0 && !columns[0].hide"
                class="p-td-sticky"
                style="left: 0px; min-width: 4rem"
                [attr.rowspan]="getRowSpan(rowIndex) * poDetail.boqs.length"
            >
                {{ poDetail.indexLevel }}
            </td>
            <td [rowSpan]="poDetail.boqs.length" *ngIf="!columns[1].hide" class="p-td-sticky" style="left: 4rem; min-width: 18rem">
                {{ poDetail.accountingCode }}
            </td>
            <td *ngIf="!columns[2].hide" class="p-td-sticky" style="left: 22rem; min-width: 15rem">
                {{ poDetail.boqs[0]?.productName }}
            </td>
            <td *ngIf="!columns[3].hide">
                {{ poDetail.boqs[0]?.manufacturerCode }}
            </td>
            <td *ngIf="!columns[4].hide">
                {{ poDetail.boqs[0]?.manufacturer }}
            </td>
            <td *ngIf="!columns[5].hide">
                {{ poDetail.boqs[0]?.description }}
            </td>
            <td *ngIf="!columns[6].hide">
                {{ poDetail.unit }}
            </td>
            <td [rowSpan]="poDetail.boqs.length" *ngIf="!columns[7].hide">
                {{
                    poDetail.price
                        | currency
                            : (poDetail?.unitPrice === 'USD' ? 'USD' : 'VND')
                            : 'symbol'
                            : (poDetail?.unitPrice === 'USD' ? '1.6-6' : '1.0-0')
                }}
            </td>
            <td [rowSpan]="poDetail.boqs.length" *ngIf="!columns[7].hide">{{ poDetail.quantity | number }}</td>
            <td [rowSpan]="poDetail.boqs.length" *ngIf="!columns[7].hide">
                {{
                    poDetail.amount
                        | currency
                            : (poDetail?.unitPrice === 'USD' ? 'USD' : 'VND')
                            : 'symbol'
                            : (poDetail?.unitPrice === 'USD' ? '1.2-2' : '1.0-0')
                }}
            </td>

            <ng-container *ngIf="detailDaysFilter && detailDaysFilter.length > 0 && !columns[8].hide">
                <ng-container *ngFor="let item of detailDaysFilter">
                    <td
                        *ngIf="getDetailDayFromDetailDaysMap(poDetail.boqs[0]?.productName, poDetail.accountingCode, item.invTransferNumber, item.date).quantityHistory !== null; else noTooltip"
                        pTooltip="{{ formatTooltipContent(getDetailDayFromDetailDaysMap(poDetail.boqs[0]?.productName, poDetail.accountingCode, item.invTransferNumber, item.date).quantityHistory) }}"
                        tooltipPosition="top"
                    >
                        {{ getDetailDayFromDetailDaysMap(poDetail.boqs[0]?.productName, poDetail.accountingCode, item.invTransferNumber, item.date).quantity }}
                    </td>

                    <ng-template #noTooltip>
                        <td pTooltip="{{ formatTooltipContent(null) }}" tooltipPosition="top">
                            {{ getDetailDayFromDetailDaysMap(poDetail.boqs[0]?.productName, poDetail.accountingCode, item.invTransferNumber, item.date).quantity }}
                        </td>
                    </ng-template>
                    <td>
                        {{
                            getDetailDayFromDetailDaysMap(poDetail.boqs[0]?.productName, poDetail.accountingCode, item.invTransferNumber, item.date).amount
                                | currency
                                    : (poDetail?.unitPrice === 'USD' ? 'USD' : 'VND')
                                    : 'symbol'
                                    : (poDetail?.unitPrice === 'USD' ? '1.2-2' : '1.0-0')
                        }}
                    </td>
                </ng-container>
            </ng-container>
            <ng-container *ngIf="(!detailDaysFilter || detailDaysFilter.length === 0) && !columns[8].hide">
                <td></td>
                <td></td>
            </ng-container>
            <td [rowSpan]="poDetail.boqs.length" *ngIf="!columns[9].hide">{{ poDetail.deliveredQuantity | number }}</td>
            <td [rowSpan]="poDetail.boqs.length" *ngIf="!columns[9].hide">
                {{
                    poDetail.deliveredAmount
                        | currency
                            : (poDetail?.unitPrice === 'USD' ? 'USD' : 'VND')
                            : 'symbol'
                            : (poDetail?.unitPrice === 'USD' ? '1.2-2' : '1.0-0')
                }}
            </td>
            <td [rowSpan]="poDetail.boqs.length" *ngIf="!columns[10].hide">{{ poDetail.remainingQuantity | number }}</td>
            <td [rowSpan]="poDetail.boqs.length" *ngIf="!columns[10].hide">
                {{
                    poDetail.remainingAmount
                        | currency
                            : (poDetail?.unitPrice === 'USD' ? 'USD' : 'VND')
                            : 'symbol'
                            : (poDetail?.unitPrice === 'USD' ? '1.2-2' : '1.0-0')
                }}
            </td>
        </tr>
        <tr *ngFor="let boq of poDetail.boqs.slice(1)">
            <td *ngIf="!columns[2].hide" class="p-td-sticky" style="left: 22rem; min-width: 15rem">
                {{ boq.productName }}
            </td>
            <td *ngIf="!columns[3].hide">{{ boq.manufacturerCode }}</td>
            <td *ngIf="!columns[4].hide">{{ boq.manufacturer }}</td>
            <td *ngIf="!columns[5].hide">{{ boq.description }}</td>
            <td *ngIf="!columns[5].hide">{{ poDetail.unit }}</td>
            <ng-container *ngIf="detailDaysFilter && detailDaysFilter.length > 0 && !columns[8].hide">
                <ng-container *ngFor="let item of detailDaysFilter">
                    <td
                        *ngIf="getDetailDayFromDetailDaysMap(boq.productName, poDetail.accountingCode, item.invTransferNumber, item.date).quantityHistory !== null; else noTooltip"
                        pTooltip="{{ formatTooltipContent(getDetailDayFromDetailDaysMap(boq.productName, poDetail.accountingCode, item.invTransferNumber, item.date).quantityHistory) }}"
                        tooltipPosition="top"
                    >
                        {{ getDetailDayFromDetailDaysMap(boq.productName, poDetail.accountingCode, item.invTransferNumber, item.date).quantity }}
                    </td>

                    <ng-template #noTooltip>
                        <td pTooltip="{{ formatTooltipContent(null) }}" tooltipPosition="top">
                            {{ getDetailDayFromDetailDaysMap(boq.productName, poDetail.accountingCode, item.invTransferNumber, item.date).quantity }}
                        </td>
                    </ng-template>
                    <td>
                        {{
                            getDetailDayFromDetailDaysMap(boq.productName, poDetail.accountingCode, item.invTransferNumber, item.date).amount
                                | currency
                                : (poDetail?.unitPrice === 'USD' ? 'USD' : 'VND')
                                    : 'symbol'
                                    : (poDetail?.unitPrice === 'USD' ? '1.2-2' : '1.0-0')
                        }}
                    </td>
                </ng-container>
            </ng-container>
            <ng-container *ngIf="(!detailDaysFilter || detailDaysFilter.length === 0) && !columns[8].hide">
                <td></td>
                <td></td>
            </ng-container>
        </tr>
    </ng-template>
    <ng-template pTemplate="footer">
        <tr>
            <td colspan="3" class="p-th-sticky" style="left: 0; bottom: 0; min-width: 37rem">
                <span>SUM</span>
                <span>({{ poDetailFirst?.unitPrice }})</span>
            </td>
            <td *ngIf="!columns[3].hide" style="border-left: none"></td>
            <td *ngIf="!columns[4].hide" style="border-left: none"></td>
            <td *ngIf="!columns[5].hide" style="border-left: none"></td>
            <td *ngIf="!columns[6].hide" style="border-left: none"></td>
            <td *ngIf="!columns[7].hide" style="border-left: none"></td>
            <td *ngIf="!columns[7].hide" style="border-left: none"></td>
            <td *ngIf="!columns[7].hide">
                {{
                    totalAmount | currency: (poDetailFirst?.unitPrice === 'USD' ? 'USD' : 'VND') : 'symbol' : (poDetailFirst?.unitPrice === 'USD' ? '1.2-2' : '1.0-0')
                }}
            </td>
            <ng-container *ngIf="detailDaysFilter.length > 0 && !columns[8].hide">
                <ng-container *ngFor="let item of detailDaysFilter">
                    <td></td>
                    <td>
                        {{
                            totalAmountDateMap.get(item.date + "-" + item.invTransferNumber)
                                | currency
                                    : (poDetailFirst?.unitPrice === 'USD' ? 'USD' : 'VND')
                                    : 'symbol'
                                    : (poDetailFirst?.unitPrice === 'USD' ? '1.2-2' : '1.0-0')
                        }}
                    </td>
                </ng-container>
            </ng-container>
            <ng-container *ngIf="detailDaysFilter.length === 0 && !columns[8].hide">
                <td colspan="2"></td>
            </ng-container>
            <td *ngIf="!columns[9].hide"></td>
            <td *ngIf="!columns[9].hide">
                {{
                    totalDeliveredAmount
                        | currency: (poDetailFirst?.unitPrice === 'USD' ? 'USD' : 'VND') : 'symbol' : (poDetailFirst?.unitPrice === 'USD' ? '1.2-2' : '1.0-0')
                }}
            </td>
            <td *ngIf="!columns[10].hide"></td>
            <td *ngIf="!columns[10].hide">
                {{
                    totalRemainingAmount
                        | currency: (poDetailFirst?.unitPrice === 'USD' ? 'USD' : 'VND') : 'symbol' : (poDetailFirst?.unitPrice === 'USD' ? '1.2-2' : '1.0-0')
                }}
            </td>
        </tr>
        <tr>
            <td
                colspan="3"
                class="p-th-sticky"
                style="
                    left: 0;
                    bottom: 0;
                    min-width: 37rem;
                    max-width: 200px;
                    white-space: normal;
                    word-wrap: break-word;
                "
            >
                <span style="font-weight: bold !important">SỐ PHIẾU CHUYỂN GIAO: </span>
                <!--<span style="font-weight: normal !important;">{{po.invNumberTransferImport}}</span>-->
            </td>
            <td *ngIf="!columns[3].hide" style="border-left: none"></td>
            <td *ngIf="!columns[4].hide" style="border-left: none"></td>
            <td *ngIf="!columns[5].hide" style="border-left: none"></td>
            <td *ngIf="!columns[6].hide" style="border-left: none"></td>
            <td *ngIf="!columns[7].hide" style="border-left: none"></td>
            <td *ngIf="!columns[7].hide" style="border-left: none"></td>
            <td *ngIf="!columns[7].hide" style="border-left: none"></td>
            <ng-container *ngIf="detailDaysFilter.length > 0 && !columns[8].hide">
                <ng-container *ngFor="let item of detailDaysFilter">
                    <td colspan="2" style="max-width: 200px; white-space: normal; word-wrap: break-word">
                        {{ item.invTransferNumber ?? '' }}
                    </td>
                </ng-container>
            </ng-container>
            <td *ngIf="!columns[9].hide"></td>
            <td *ngIf="!columns[9].hide"></td>
            <td *ngIf="!columns[10].hide"></td>
            <td *ngIf="!columns[10].hide"></td>
        </tr>
    </ng-template>
</p-table>

<p-overlayPanel #op>
    <p-table
        [value]="columns"
        [selection]="columnChoose"
        (selectionChange)="setColumnSelection($event)"
        [scrollable]="true"
        scrollHeight="500px"
    >
        <ng-template pTemplate="header">
            <tr>
                <th>
                    <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                </th>

                <th>Tất cả</th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rowData>
            <tr>
                <td style="font-size: 14px; padding: 8px 12px">
                    <p-tableCheckbox [value]="rowData" [hidden]="rowData.default" />
                </td>
                <td style="font-size: 14px; padding: 8px 12px">
                    {{ rowData['header'] }}
                </td>
            </tr>
        </ng-template>
    </p-table>
</p-overlayPanel>
