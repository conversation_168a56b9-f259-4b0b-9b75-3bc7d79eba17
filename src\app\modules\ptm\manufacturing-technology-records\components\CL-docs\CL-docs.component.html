<ng-container>
    <div class="tw-my-2 label-tab-cnsx">
        <label>Chi tiết tài liệu chất l<PERSON></label>
    </div>

    <div #container *ngIf="formGroup">
        <app-form #form [formGroup]="formGroup" (onSubmit)="onSave()">
            <div formArrayName="items">
                <p-panel [toggleable]="true">
                    <ng-template pTemplate="icons">
                        <button
                            pButton
                            type="button"
                            class="p-button-outlined p-button-sm toggle-btn pi pi-arrows-alt"
                            appFullscreenToggle
                            [target]="container"
                        ></button>
                    </ng-template>
                    <p-table styleClass="p-datatable-gridlines" [value]="items.controls">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="min-width: 2rem">STT</th>
                                <th style="min-width: 9rem">Tên tài liệu</th>
                                <th style="min-width: 9rem">Version</th>
                                <th style="min-width: 9rem">Biểu mẫu</th>
                                <th style="min-width: 9rem">Trạng thái</th>
                                <th style="min-width: 9rem">Last Update</th>
                                <th style="min-width: 9rem">Người cập nhật</th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                            <tr [formGroupName]="rowIndex">
                                <!-- STT -->
                                <td>
                                    <app-form-item label=""> {{ rowIndex + 1 }}</app-form-item>
                                </td>
                                <!-- Tên tài liệu -->
                                <td>
                                    <app-form-item label="">
                                        <span>{{ item.get('docType')!.value }}</span>
                                    </app-form-item>
                                </td>
                                <!-- Version -->
                                <td>
                                    <app-form-item label="">
                                        <app-combobox-nonRSQL
                                            #versionSelect
                                            type="select-one"
                                            formControlName="qualityControlDocumentId"
                                            [fieldValue]="versionConfigs[rowIndex].fieldValue"
                                            [fieldLabel]="versionConfigs[rowIndex].fieldLabel"
                                            [url]="versionConfigs[rowIndex].url"
                                            [param]="versionConfigs[rowIndex].param"
                                            [initSearch]="item.get('versionName')?.value"
                                            [additionalParams]="{ size: 100 }"
                                            (onChange)="onVersionSelect($event.objects[0], rowIndex)"
                                        >
                                        </app-combobox-nonRSQL>
                                    </app-form-item>
                                </td>
                                <!-- Biểu mẫu -->
                                <td>
                                    <app-form-item label="">
                                        <span>{{ item.get('formName')!.value }}</span>
                                    </app-form-item>
                                </td>
                                <!-- Trạng thái -->
                                <td>
                                    <app-form-item label="">
                                        <span>{{ item.get('statusText')!.value }}</span>
                                    </app-form-item>
                                </td>
                                <!-- Last Update -->
                                <td>
                                    <app-form-item label="">
                                        <span>{{ item.get('lastUpdateText')!.value }}</span>
                                    </app-form-item>
                                </td>
                                <!-- Người cập nhật -->
                                <td>
                                    <app-form-item label="">
                                        <span>{{ item.get('updatedByEmail')!.value }}</span>
                                    </app-form-item>
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                </p-panel>
            </div>

            <app-tab-action-buttons [form]="formGroup" [mode]="mode" [status]="version.status" [isSaving]="isBusy$"></app-tab-action-buttons>
        </app-form>
    </div>
</ng-container>
