import { Injectable } from '@angular/core';
import { BaseService } from '../../base.service';
import { HttpClient } from '@angular/common/http';
import { ParamsTable } from '../../../shared/table-module/table.common.service';
import { AcceptanceDocument, Contract } from '../../../models/interface/smart-qc';

@Injectable({
    providedIn: 'root',
})
export class AcceptanceDocumentService extends BaseService<AcceptanceDocument> {
    constructor(protected override http: HttpClient) {
        super(http, '/smart-qc/api/acceptance-docs');
    }

    getPageTableCustomPost({ pageable }: ParamsTable, body) {
        return this.http.post<Contract[]>(`/smart-qc/api/acceptance-docs/search-custom?${pageable}`, body, {
            observe: 'response',
        });
    }

    getImage({ pageable }: ParamsTable, body) {
        return this.http.post<unknown[]>(
            `/smart-qc/api/acceptance-docs/get-image?sort=header,sub,detail,asc${pageable}`,
            body,
            {
                observe: 'response',
            },
        );
    }

    getAllImage(body) {
        return this.http.post<unknown[]>(`/smart-qc/api/acceptance-docs/get-all-image`, body, {
            observe: 'response',
        });
    }

    getStation({ pageable }: ParamsTable, body) {
        return this.http.post<unknown[]>(`/smart-qc/api/acceptance-docs/get-station?${pageable}`, body, {
            observe: 'response',
        });
    }

    getAllStation(body) {
        return this.http.post<unknown[]>(`/smart-qc/api/acceptance-docs/get-all-station`, body, {
            observe: 'response',
        });
    }

    exportAcceptanceDocument(filter) {
        return this.http.post<unknown[]>(`/smart-qc/api/acceptance-docs/export-acceptance`, filter, {
            observe: 'response',
        });
    }

    getImageById(body) {
        return this.http.post<unknown[]>(`/smart-qc/api/acceptance-docs/get-image-by-id?`, body, {
            observe: 'response',
        });
    }

    getStationById(body) {
        return this.http.post<unknown[]>(`/smart-qc/api/acceptance-docs/get-station-by-id?`, body, {
            observe: 'response',
        });
    }

    overrideAcceptanceDocument(acceptanceDocument: AcceptanceDocument) {
        return this.http.post<unknown[]>(`/smart-qc/api/acceptance-docs/overwrite?`, acceptanceDocument, {
            observe: 'response',
        });
    }

    exportApproveData(filter) {
        return this.http.post<unknown[]>(`/smart-qc/api/report/export-approve-data`, filter, {
            observe: 'response',
        });
    }
}
