import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import {PoTransfer, Rfq} from 'src/app/models/interface/sc';
import {ApiResponse} from "../../../models/interface";

@Injectable()
export class RfqService extends BaseService<Rfq> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/rfq');
    }

    importItemFile(file: File, rfqId: number) {
        const formData: FormData = new FormData();
        formData.append('file', file);
        formData.append('rfqId', rfqId.toString());

        return this.http.post<ApiResponse>('/sc/api/rfq/import-item', formData);
    }

    importInventoryFile(file: File, rfqId: number) {
        const formData: FormData = new FormData();
        formData.append('file', file);
        formData.append('rfqId', rfqId.toString());

        return this.http.post<ApiResponse>('/sc/api/rfq/import-inventory', formData);
    }

    requestForQuotation(id: number) {
        return this.http.get<Rfq>(`/sc/api/rfq/request-for-quotation?id=${id}`);
    }
}
