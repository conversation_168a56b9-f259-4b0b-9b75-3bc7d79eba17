import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { Template } from 'src/app/models/interface/smart-qc';

@Injectable({
    providedIn: 'root',
})
export class CardService extends BaseService<Template> {
    constructor(protected override http: HttpClient) {
        super(http, '/smart-qc/api/card');
    }
}
