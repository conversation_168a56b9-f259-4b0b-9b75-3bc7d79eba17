<div  class="border-1 border-gray-300 tw-inline-flex tw-rounded ">
    <ng-container class="tw-flex tw-items-center" *ngIf="fileSelected || attachment; else templateInputUpload">
        <div class="tw-px-4 tw-py-4 tw-inline-block tw-text-nowrap tw-overflow-hidden tw-text-ellipsis align-middle" [ngClass]="getFileColor()">
            {{ checkFileType() }}
        </div>
        <div *ngIf="fileSelected && !attachment"
              class="tw-py-4 tw-max-w-56 tw-min-w-56 tw-inline-block tw-text-nowrap tw-overflow-hidden tw-text-ellipsis align-middle"
        >{{ fileSelected.name }}</div>
        <div *ngIf="attachment" class="tw-py-4 tw-max-w-56 tw-inline-block tw-text-ellipsis tw-overflow-hidden tw-cursor-pointer tw-text-nowrap align-middle"
           (click)="downloadAttachment()">{{ attachment.name }}</div>
        <div style="margin-top: 8px; margin-right: 5px" class="tw-p-2 pi pi-times tw-text-red-500 tw-cursor-pointer hover:tw-bg-red-200 tw-rounded-full tw-flex tw-items-center tw-justify-center tw-h-8 tw-w-8"
             (click)="clearFiles()">
        </div>
    </ng-container>
    <ng-template #templateInputUpload>
        <label for="fileInput" class="tw-inline-flex tw-px-4 tw-py-2 tw-w-fit tw-cursor-pointer tw-flex tw-items-center">
            <span class="pi pi-cloud-upload" style="color: #3490dc; margin-right: 5px; font-size: x-large"></span>
            <span class="" style="color: #6494C2">Chọn file</span>
            <input
                id="fileInput"
                type="file"
                #fileInput
                [accept]="typesAccept"
                class="tw-hidden"
                (change)="onSelectFile($event)"
            />
        </label>
    </ng-template>
</div>
