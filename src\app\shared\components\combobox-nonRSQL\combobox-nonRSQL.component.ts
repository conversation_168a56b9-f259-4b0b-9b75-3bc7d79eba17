/* eslint-disable @typescript-eslint/no-explicit-any */
import { Component, OnInit, OnChanges, Input, Output, EventEmitter, ViewChild, ElementRef, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, finalize } from 'rxjs/operators';
import { debounce, isArray, isEqual, unionBy, filter, includes, get, isObject } from 'lodash';
import { FormsModule, NG_VALUE_ACCESSOR } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { OverlayPanel, OverlayPanelModule } from 'primeng/overlaypanel';
import { forwardRef } from '@angular/core';

@Component({
    selector: 'app-combobox-nonRSQL',
    standalone: true,
    imports: [CommonModule, InputTextModule, FormsModule, OverlayPanelModule],
    templateUrl: 'combobox-nonRSQL.component.html',
    styleUrls: ['./combobox-nonRSQL.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => ComboboxNonRSQLComponent),
            multi: true,
        },
    ],
})
export class ComboboxNonRSQLComponent implements OnInit, OnChanges {
    @ViewChild('combobox') comboboxRef!: ElementRef;
    @ViewChild('dropdownSearch') dropdownSearchRef!: ElementRef;
    @ViewChild('overlayPanel') overlayPanel!: OverlayPanel;

    @Input() type: 'select' | 'select-one' = 'select';
    @Input() url!: string;
    @Input() fieldValue: string = 'id';
    @Input() fieldLabel: any = 'name';
    @Input() options: any[] = [];
    @Input() param!: string;
    @Input() body!: Record<string, unknown>;
    @Input() placeholder: string = '';
    @Input() disabled: boolean = false;
    @Input() requiredFilter: string[] = [];
    @Input() removeField!: string;
    @Input() removeFieldValues!: unknown[];
    @Input() additionalCondition!: string;
    @Input() value!: unknown;
    @Output() panelShow = new EventEmitter<void>();
    @Output() onChange = new EventEmitter<{ value: unknown; objects: any[] }>();
    @Output() onRemove = new EventEmitter<{ value: unknown; object: any }>();
    public isFetching = false;
    private valueSubject = new Subject<unknown>();
    private onChangeCallback: (value: any) => void = () => {};
    private onTouchedCallback: () => void = () => {};
    debouncedGetOptions!: (searchValue: unknown) => void;
    searchValue: string = '';
    isSelected: boolean = false;
    filteredOptions: any[] = [];
    showDropdown = false;
    activeIndex = -1;
    dropdownPosition = { top: '0px', left: '0px', width: '0px' };
    objectValue: any[] = [];
    fetchFirstDone = false;

    // paging/sort
    @Input() page: number;
    @Input() size: number = 100;
    @Input() sort: string;

    // query params
    @Input() searchParam: string = 'search';
    @Input() additionalParams: Record<string, any> = {};
    @Input() returnObject = false;
    @Input() ignoreAdditionalParamsOnSearch: boolean = false;
    @Input() fetchOnInit: boolean = true;
    @Input() initSearch: string = '';
    private hasInteracted = false;

    constructor(private http: HttpClient) {
        this.debouncedGetOptions = debounce((searchValue: unknown) => this.fetchOptions(searchValue), this.getDebounceTime());
        this.initializeDebounce();
    }

    ngOnInit(): void {
        this.filteredOptions = [...this.options];
        // Nếu có initSearch (tức là versionName từ cha), fetch luôn option này
        if (this.initSearch) {
            this.fetchOptions(this.initSearch);
            this.searchValue = this.initSearch;
        }
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['options']) {
            this.filteredOptions = [...changes['options'].currentValue];
        }
        // Chỉ gọi fetch khi additionalParams thay đổi và lineId (hoặc key bạn cần) khác
        if (changes['additionalParams'] && !changes['additionalParams'].firstChange) {
            const prev = changes['additionalParams'].previousValue;
            const curr = changes['additionalParams'].currentValue;

            // so sánh trường bạn quan tâm, ví dụ lineId, ...
            const importantKeys = ['lineId', 'modelId', 'productLineId'];

            const hasChanged = importantKeys.some((key) => prev?.[key] !== curr?.[key]);

            if (hasChanged) {
                // reset ô search để fetch full list theo params mới
                this.searchValue = '';
                this.fetchOptions(null);
            }
        }
        if (changes['body'] || changes['value']) {
            this.debouncedGetOptions(this.value);
        }
    }

    private initializeDebounce(): void {
        this.valueSubject
            .pipe(
                debounceTime(this.getDebounceTime()),
                // distinctUntilChanged((p, c) => isEqual(p, c)),
            )
            .subscribe((value) => {
                this.updateValueLogic(value);
                this.onChangeCallback(value);
                this.onChange.emit({ value, objects: this.objectValue });
            });
    }

    private getDebounceTime(): number {
        return this.type === 'select' ? 300 : 500;
    }

    private updateValueLogic(value: unknown): void {
        this.value = this.type === 'select' ? this.handleSelectValue(value) : value;
        this.updateObjectValue();
    }

    private handleSelectValue(value: unknown): unknown[] {
        if (!value) return [];
        return isArray(value) ? value : [value];
    }

    writeValue(value: any): void {
        // Chỉ cập nhật internal value, không gọi fetchOptions
        if (isEqual(value, this.value)) {
            return;
        }
        this.updateValueLogic(value);
    }

    registerOnChange(fn: any): void {
        this.onChangeCallback = fn;
    }

    registerOnTouched(fn: any): void {
        this.onTouchedCallback = fn;
    }

    setDisabledState(isDisabled: boolean): void {
        this.disabled = isDisabled;
    }

    filterOptions(term: string): void {
        this.hasInteracted = true;
        this.searchValue = term;
        if (this.url) {
            this.debouncedGetOptions(term);
        } else {
            const lower = term.toLowerCase();
            this.filteredOptions = filter(this.options, (opt) => includes(this.getDisplayValue(opt).toLowerCase(), lower));
        }
        this.activeIndex = -1;
    }

    private updateObjectValue(): void {
        const combined = unionBy(this.filteredOptions, this.objectValue, this.fieldValue);
        if (this.value) {
            const vals = isArray(this.value) ? this.value : [this.value];
            this.objectValue = vals.map((v) => combined.find((o) => get(o, this.fieldValue) === v)).filter((x) => !!x);
        } else {
            this.objectValue = [];
        }
    }

    buildQueryParams(search: unknown): HttpParams {
        const term = search != null ? String(search).trim() : '';
        // 1) Luôn add name/search term + paging/sort
        let params = new HttpParams().set(this.param || this.searchParam, term).set('size', String(this.size));

        if (this.page) params = params.set('page', String(this.page));
        if (this.sort) params = params.set('sort', this.sort);

        // 2) Chỉ merge additionalParams khi:
        //    - hoặc term === '' (mở dropdown lần đầu)
        //    - hoặc flag=false (các combobox khác)
        const isSearching = term.length > 0;
        if (!(isSearching && this.ignoreAdditionalParamsOnSearch)) {
            Object.entries(this.additionalParams).forEach(([k, v]) => {
                if (v != null) {
                    params = params.set(k, String(v));
                }
            });
        }

        return params;
    }

    getDropdownPosition() {
        if (!this.comboboxRef) return { top: '0px', left: '0px', width: '200px' };
        const inputRect = this.comboboxRef.nativeElement.getBoundingClientRect();
        const windowHeight = window.innerHeight;
        const isInputInBottomHalf = inputRect.y > windowHeight / 2;
        let top: string;
        let dropdownHeight = 250;

        if (this.filteredOptions.length < 6 && this.filteredOptions.length > 0) {
            dropdownHeight = 33 * this.filteredOptions.length + 33;
        }

        if (isInputInBottomHalf) {
            top = `${0 - dropdownHeight}px`;
        } else {
            top = `${inputRect.height}px`;
        }

        return {
            top,
            left: `${inputRect.x}px`,
            width: `${inputRect.width}px`,
        };
    }

    fetchOptions(searchValue: unknown): void {
        if (!this.url || this.requiredFilter.some((f) => !this.body?.[f])) return;
        this.showDropdown = true;
        this.isFetching = true;

        this.http
            .get<any[]>(this.url, { params: this.buildQueryParams(searchValue) })
            .pipe(finalize(() => (this.isFetching = false)))
            .subscribe({
                next: (data) => {
                    this.fetchFirstDone = true;
                    this.filteredOptions = data;
                    this.updateObjectValue();
                },
                error: () => {
                    this.filteredOptions = [];
                },
            });
    }

    onKeyDown(event: KeyboardEvent): void {
        switch (event.key) {
            case 'ArrowDown':
                this.activeIndex = Math.min(this.activeIndex + 1, this.filteredOptions.length - 1);
                event.preventDefault();
                break;
            case 'ArrowUp':
                this.activeIndex = Math.max(this.activeIndex - 1, -1);
                event.preventDefault();
                break;
            case 'Enter':
                if (this.activeIndex >= 0 && this.activeIndex < this.filteredOptions.length) {
                    this.selectOption(this.filteredOptions[this.activeIndex]);
                    event.preventDefault();
                }
                break;
            case 'Escape':
                this.overlayPanel.hide();
                this.showDropdown = false;
                event.preventDefault();
                break;
        }
    }

    isSelectedOption(option: unknown): boolean {
        const value = this.getStoredValue(option);
        return isArray(this.value) ? this.value.includes(value) : this.value === value;
    }

    selectOption(option: unknown): void {
        const value = this.returnObject ? option : this.getStoredValue(option);

        if (this.type === 'select-one') {
            // 1. Cập nhật value ngay lập tức
            this.value = value;
            this.updateObjectValue();
            // 2. Emit không debounce để parent nhận luôn
            this.onChangeCallback(this.value);
            this.onChange.emit({ value: this.value, objects: this.objectValue });

            // 3. Ẩn dropdown
            this.overlayPanel.hide();
            this.showDropdown = false;
        } else {
            const currentValues = isArray(this.value) ? [...this.value] : [];
            if (!currentValues.includes(value)) {
                currentValues.push(value);
            } else {
                currentValues.splice(currentValues.indexOf(value), 1);
            }
            this.value = currentValues;
        }
        // 3) Cập nhật objectValue cho cả 2 trường hợp
        this.updateObjectValue();

        // 4) Đẩy vào Subject (nếu bạn vẫn muốn debounce cho 1 số logic khác)
        this.valueSubject.next(this.value);

        // 5) Cập nhật lại vị trí dropdown
        this.updateDropdownPosition();
    }

    removeTag(option: unknown, event: MouseEvent): void {
        event.stopPropagation();
        const value = this.getStoredValue(option);
        const object = this.objectValue.find((item) => get(item, this.fieldValue) === value);
        const currentValues = isArray(this.value) ? [...this.value] : [];
        currentValues.splice(currentValues.indexOf(value), 1);
        this.value = currentValues;
        this.updateObjectValue();
        this.valueSubject.next(this.value);
        this.updateDropdownPosition();
        this.onRemove.emit({ value, object });
    }

    clearSelection(event?: MouseEvent): void {
        event.stopPropagation();
        // 1) Reset nội bộ
        this.searchValue = '';
        this.value = null;
        this.objectValue = [];
        this.isSelected = false;
        // 2) Emit ngay cho parent (không debounce)
        this.onChangeCallback(this.value);
        this.onChange.emit({ value: this.value, objects: [] });
        // 3) Vẫn tiếp tục đẩy vào Subject nếu bạn cần debounce cho search
        this.valueSubject.next(null);
        this.filterOptions('');
        this.updateDropdownPosition();
    }

    focusInput(event: Event): void {
        if (this.disabled) return;
        this.hasInteracted = true;
        if (this.url && !this.fetchFirstDone) {
            this.debouncedGetOptions(null);
        }
        this.showDropdown = true;
        this.activeIndex = -1;
        this.overlayPanel.show(event, this.comboboxRef.nativeElement);
        this.updateDropdownPosition();
        setTimeout(() => this.dropdownSearchRef?.nativeElement.focus(), 0);
    }

    getDisplayValue(item: unknown): string {
        if (Array.isArray(this.fieldLabel)) {
            return this.fieldLabel.map((field) => item[field]).join(' - ');
        }
        return isObject(item) && this.fieldLabel ? get(item, this.fieldLabel, '') : String(item);
    }

    getStoredValue(item: unknown): unknown {
        return isObject(item) && this.fieldValue ? get(item, this.fieldValue) : item;
    }

    updateDropdownPosition(): void {
        this.dropdownPosition = this.getDropdownPosition();
    }
}
