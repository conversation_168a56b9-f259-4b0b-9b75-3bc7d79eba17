:root {
    /* Light Theme Variables */
    --text-color: #39465f; /* <PERSON><PERSON><PERSON> chữ mặc định */
    --text-secondary-color: #6b7280; /* <PERSON><PERSON><PERSON> chữ phụ mờ hơn */
    --primary-color: #08549E; /* <PERSON><PERSON><PERSON> chủ đạo */
    --primary-color-text: #ffffff; /* <PERSON><PERSON><PERSON> chữ khi nền là màu chủ đạo */
    // --font-family: 'Arial', sans-serif; /* Font chính của theme */
    --inline-spacing: 1rem; /* <PERSON><PERSON><PERSON>ng cách giữa các item */
    --border-radius: 0.5rem; /* <PERSON><PERSON> kính bo góc */
    --focus-ring: 0 0 0 3px rgba(8, 84, 158, 0.5); /* Box shadow khi focus */
    --mask-bg: rgba(0, 0, 0, 0.3); /* <PERSON><PERSON>u nền của overlay */
    --highlight-bg: #f0f4f8; /* <PERSON><PERSON><PERSON> nền khi highlight */
    --highlight-text-color: #08549E; /* <PERSON>àu chữ khi highlight */
    --secondary-color: #fff;

    --background-color: #dbe0e5;
    --background-disabled-color: #ebeff5;
}


[data-theme="dark"] {
    /* Dark Theme Variables */
    --text-color: #e5e7eb; /* Màu chữ mặc định */
    --text-secondary-color: #9ca3af; /* Màu chữ phụ mờ hơn */
    --primary-color: #08549E; /* Màu chủ đạo */
    --primary-color-text: #ffffff; /* Màu chữ khi nền là màu chủ đạo */
    // --font-family: 'Arial', sans-serif; /* Font chính của theme */
    --inline-spacing: 1rem; /* Khoảng cách giữa các item */
    --border-radius: 0.5rem; /* Bán kính bo góc */
    --focus-ring: 0 0 0 3px rgba(8, 84, 158, 0.5); /* Box shadow khi focus */
    --mask-bg: rgba(0, 0, 0, 0.7); /* Màu nền của overlay */
    --highlight-bg: #333f4f; /* Màu nền khi highlight */
    --highlight-text-color: #ffffff; /* Màu chữ khi highlight */
    --secondary-color: #fff;

    --background-color: #dbe0e5;
    --background-disabled-color: #ebeff5;

    }
    $scale: 14px; /* main font size */
    $borderRadius: 12px; /* border radius of layout element e.g. card, sidebar */
    $transitionDuration: 0.2s;
