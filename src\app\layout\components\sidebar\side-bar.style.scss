
ul  {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

 li {
    list-style-type: none;
}


.menu_item-level {
    margin-left: 12px !important;
}


.expandable {
    max-height: 0;
    overflow: hidden;
}
.expandable.expand {
    max-height: 1000px; /* <PERSON><PERSON><PERSON> trị max-height đủ lớn để chứa nội dung mở rộng */
    transition: max-height 0.3s cubic-bezier(0.86, 0, 0.07, 1);
}

.transition_max-height {
    transition: max-height 0.3s cubic-bezier(0.86, 0, 0.07, 1);
}




