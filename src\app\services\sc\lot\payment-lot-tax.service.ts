import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { PaymentLotTax } from '../../../models/interface/sc';

@Injectable()
export class PaymentLotTaxService extends BaseService<PaymentLotTax> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/payment-lot-tax');
    }
}
