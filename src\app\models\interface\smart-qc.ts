import { BaseEntity } from 'src/app/models/BaseEntity';

export { Contract, Action, Task, Station };

interface Contract extends BaseEntity {
    name?: string;
    code?: string;
    description?: string;
    status?: number;
    startTime?: number;
    endTime?: number;
    actions: Action[];
    createdBy: string;
    errors?: Error[];
    cards?: Card[];
}

interface Action extends BaseEntity {
    contractId?: number;
    name?: string;
    description?: string;
    templateId?: number;
    tasks?: Task[];
    template?: Template;
}

interface Task extends BaseEntity {
    date?: number;
    contractId?: number;
    actionId?: number;
    stationId?: number;
    templateId?: number;

    assigneeId?: number;
    managerId?: number;
    pmResponse?: string;
    description?: string;
    station?: Station;
}

interface Station extends BaseEntity {
    name: string;
    code: string;
    areaId: number;
}

export interface ApproveDetailDTO {
    contractId: number;
    taskId: number;
    actionName: string;
    contractName: string;
    submitDate: number;
    submitDateEp: number;
    areaName: string;
    districtName: string;
    stationName: string;
    stationCode: string;
    subPM: string;
    employee: string;
    note: string;
    address: string;
    state: number;
    treeCheckList: TreeCheckListDTO[];
}

export interface TaskDetail extends BaseEntity {
    stationId: number; // Id trạm
    actionId: number; // Id công việc
    templateId: number; // Id mẫu kiểm tra
    checkListHeaderId: number; // Id header
    checkListSubId: number; // Id sub header
    checkListDetailId: number; // Id check list detail
    type: number;
    valueCheckList: string; // Giá trị để lựa chọn theo mẫu kiểm tra
    value: string; // Giá trị trên mobile gửi lên
    taskId: number;
    pmRate: number; // Đánh giá của PM
    subPmRate: number; // Đánh giá của subPM
    pmResponse: string; // Phản hồi của PM
    subPmResponse: string; // Phản hồi của subPM
    employeeResponse: string; // Phản hồi của đội thi công
    comment: string; // Phản hồi cho đội thi công
    task: Task;
    checklistHeader: Checklist;
    checklistSub: Checklist;
    checklistDetail: Checklist;
    imageTaskList: ImageTask[];
    response: ResponseApprove;
    ignored: boolean;
}

export interface ResponseApprove extends BaseEntity {
    checkListHeaderId: number;
    checkListSubId: number;
    taskId: number;
    rate?: number; // Đánh giá của PM
    pmToSubPm?: string;
    subPmToEmployee: string;
    employeeToSubPm: string;
    subPmToPm: string;
    responseErrors: ResponseErrorSQC[];
}
export interface ResponseErrorSQC extends BaseEntity {
    responseApproveId?: number;
    taskId: number;
    keyMap?: string;
    errorId: number;
}

export interface ApproveLog extends BaseEntity {
    taskId: number;
    action: number; // 0 tu choi, 1 châp nhan
    logs: Record<string, string[]>;
}

export interface ImageTask {
    taskDetailId: number;
    checkListId: number;
    taskId: number;
    url: string;
    uploadBy: number;
}

export interface TreeCheckListDTO {
    key: string;
    label: string;
    data: TaskDetail[];
    checkListHeaderId: number;
    checkListSubId: number;
    icon: string;
    level: number;
    levelChild: number;
    children: TreeCheckListDTO[];
    response: ResponseApprove;
}

export interface ImageUpload {
    url: string;
}

export interface Checklist {
    id?: number;

    idTemp?: number;

    position?: number;

    name?: string;

    content?: string;

    type?: number;

    level?: number;

    checklists?: Checklist[];

    nonSubHeader?: number;

    templateId?: number;
}

export interface ContractDTO {
    id: number;
    name: string;
    code: string;
    status: number;
    startTime: number;
    endTime: number;
    totalAction: number;
    totalStation: number;
    totalEmployee: number;
    totalSubPM: number;
}
export interface ContractFilter {
    ids?: number[];
    names?: string[];
    codes?: string[];
    statuses?: number[];
    totalAction?: number;
    totalStation?: number;
    totalSubPM?: number;
    totalEmployee?: number;
    startTimeStart?: number;
    startTimeEnd?: number;
    endTimeStart?: number;
    endTimeEnd?: number;
}
export interface ApproveFilter {
    contractIds?: number[];
    taskIds?: number[];
    areaIds?: number[];
    stationIds?: number[];
    subPMIds?: number[];
    employeeIds?: number[];
    submitDate?: Date;
    startSubmitDate?: number;
    endSubmitDate?: number;
    statuses?: number[];
}
export interface ApproveDTO {
    id: number;
    submitDate: number;
    state: number;
    contractName: string;
    actionName: string;
    areaName: string;
    stationName: string;
    subPM: string;
    employee: string;
}

export interface Template extends BaseEntity {
    workType?: number;

    name?: string;

    code?: string;

    description?: string;

    key?: number;

    checklists?: Checklist[];

    checklistsRemove?: Checklist[];

    used?: number;
}

export interface AcceptanceDocument extends BaseEntity {
    contractId?: number;

    actionId?: number;

    title?: string;

    checklistDetailIds?: number[];

    taskIds?: number[];
}

export interface ErrorSmartQC extends BaseEntity {
    contractId: number;

    level: number;

    name: string;

    displayName?: string;
}

export interface Error extends BaseEntity {
    name?: string;

    level?: number;

    contractId?: number;

    isUsed?: boolean;
}

export interface Card extends BaseEntity {
    name?: string;

    contractId?: number;

    isUsed?: boolean;
}
export interface Maintenance extends BaseEntity {
    taskId: number;

    subPmId: number;

    createDate: number;

    doneDate: number;

    backTimes: number;

    processDetail: string;

    note: string;

    hw: string;

    maintenanceCards: MaintenanceCard[];

    stationName: string;

    stationCode: string;

    stationId: number;

    contractId: number;

    contractName: string;

    areaId: number;

    areaName: string;
    createDateCustom: Date;
    doneDateCustom: Date;
}

export interface MaintenanceCard extends BaseEntity {
    maintenanceId: number;

    cardId: number;

    cardName: string;

    quantity: number;
}

export interface MaintenanceDTO {
    id: number;

    contractName: string;

    areaName: string;

    stationId: number;

    stationName: string;

    stationCode: string;

    subPm: string;

    hw: string;

    createDate: number;

    doneDate: number;

    backTimes: string;

    processDetail: string;
    note: string;
}

export interface MaintenanceFilter {
    contractId: number;

    areaId: number;

    stationName: string[];

    stationCode: string[];

    subPmId: number[];

    hw: string;

    createdDateStart: number;

    createdDateEnd: number;

    doneDateStart: number;

    doneDateEnd: number;

    processDetail: string;
    note: string;
}

export interface SubPm {
    id: number;
    displayName: string;
    completedTasks: number;
}

export interface ContractMaintenance {
    id: number;
    name: string;
    code: string;
    stationId: number;
    areaId: number;
    areaName: string;
    subPms: SubPm[];
}
