import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { environment } from '../../../../../../../environments/environment';

interface RowData {
    section: string;
    name: string;
    note1: string;
    note2: string;
    note3?: string;
    note4?: string;
    path1?: string;
    path3?: string;
}

@Component({
    selector: 'app-design-profile',
    standalone: true,
    imports: [CommonModule, TableModule, ButtonModule],
    templateUrl: './design-profile.component.html',
    styles: [
        `
            .section-table ::ng-deep table {
                table-layout: fixed;
            }
        `,
        `
            .section-table ::ng-deep .p-table-thead > tr > th {
                background: #e0e0e0;
                font-weight: bold;
                text-align: left;
            }
        `,
        `
            .mb-4 {
                margin-bottom: 1rem;
            }
        `,
        `
            .footer {
                text-align: right;
                margin-top: 1rem;
            }
        `,
        `
            .ml-auto {
                margin-left: auto;
            }
        `,
    ],
})
export class ProductionProfileComponent implements OnChanges {
    @Input() docRes1: any;
    @Input() docRes2: any;

    groupedRows: { name: string; items: RowData[] }[] = [];
    public STORAGE_BASE_URL = environment.STORAGE_BASE_URL;
    ngOnChanges(changes: SimpleChanges) {
        if (changes['docRes1'] || changes['docRes2']) {
            this.updateGroupedRows();
        }
    }

    updateGroupedRows() {

            // Kiểm tra dữ liệu đầu vào
            if (!this.docRes1 && !this.docRes2) {
                this.groupedRows = [];
                return;
            }
            const sectionMap: { [key: number]: string } = {
                0: 'Quy trình sản xuất',
                1: 'Công cụ dụng cụ sản xuất phần mềm',
                2: 'Phần mềm hỗ trợ sản xuất',
            };

            const data1 = this.docRes1 ?? {};
            const data2 = this.docRes2 ?? {};
            const allSectionKeys = new Set([...Object.keys(data1), ...Object.keys(data2)]);

            const map = new Map<string, RowData[]>();

            for (const sectionKey of allSectionKeys) {
                const sectionNum = Number(sectionKey);
                const sectionName = sectionMap[sectionNum] || 'Khác';

                const list1 = data1[sectionKey] || [];
                const list2 = data2[sectionKey] || [];

                const maxLength = Math.max(list1.length, list2.length);
                const rows: RowData[] = [];

                for (let i = 0; i < maxLength; i++) {
                    const item1 = list1[i] || {};
                    const item2 = list2[i] || {};

                    rows.push({
                        section: sectionName,
                        name: item1.description || item2.description || '',
                        note1: item1.fileName || '',
                note2: item1.note || '',
                note3: item2.fileName || '',
                note4: item2.note || '',
                path1: item2.filePath || '',
                    path3: item2.filePath || '',
                    });
                }

                if (rows.length > 0) {
                    map.set(sectionName, rows);
                }
            }

            this.groupedRows = Array.from(map, ([name, items]) => ({ name, items }));

    }
    downloadFile(url: string, namePath: string) {
        const a = document.createElement('a');
        a.href = this.STORAGE_BASE_URL + '/' + url;
        a.download = `${namePath}`;
        a.click();
        a.remove();
    }
}
