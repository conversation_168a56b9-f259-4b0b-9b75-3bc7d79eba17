import { Component, Input, OnChanges, OnInit, SimpleChanges, TemplateRef, inject } from '@angular/core';
import { Column } from 'src/app/models/interface';
import { Pagination, TableCommonService } from '../table.common.service';
import { Observable } from 'rxjs';
import { isArray } from 'lodash';
import Common from 'src/app/utils/common';
import { ConfirmationService } from 'primeng/api';
import { AlertService } from '../../services/alert.service';

@Component({
    selector: 'app-table-common',
    templateUrl: './table-common.component.html',
    styleUrls: ['./table-common.component.scss'],
})
export class TableCommonComponent implements OnInit, OnChanges {
    @Input() tableId: string;
    @Input() showCaption: boolean = true;
    @Input() name: string = 'Table';
    @Input() columns: Column[] = [];
    @Input() data: unknown[] | undefined;
    @Input() loading: boolean = false;
    @Input() styleClass: string = '';
    @Input() stt: boolean = false;
    @Input() hiddenSelect: (rowData: unknown) => boolean;
    @Input() fieldSelect: string = 'id';
    @Input() rowSelectable: (rowData: unknown) => boolean;
    @Input() selectionMode: 'multiple' | 'single' = 'multiple';
    @Input() rowExpandTemplate: TemplateRef<Element>;
    @Input() filterTemplate: TemplateRef<Element>;
    @Input() actionTemplate: TemplateRef<Element>;
    @Input() summaryTemplate: TemplateRef<Element>;
    @Input() headerTemplate: TemplateRef<Element>;
    @Input() footerTemplate: TemplateRef<Element>;
    @Input() funcDelete: (ids: unknown[], rowData?: unknown[]) => Observable<unknown>;
    @Input() funcDeleteAll: () => Observable<unknown>;
    @Input() authoritiesDelete: string[];
    @Input() funcDownload: Observable<unknown>;
    @Input() tableStyle: {
        [klass: string]: unknown;
    } = {};
    @Input() hideButtonHeader: boolean = false;
    @Input() rowHide: unknown[];
    @Input() deleteAllButton: boolean = false;
    @Input() deleteButton: boolean = true;
    @Input() inactiveBtn: boolean = false;
    // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
    /**
     * Các biến mặc định cho table
     */
    defaultValue = {
        style: { 'min-width': '20rem' },
        class: [], //'p-datatable-striped' 'p-datatable-sm'
        classError: 'bg-red-200',
    };
    /**
     *
     */

    warningIds: unknown[] = [];
    combinedTableStyle: unknown = {};
    combinedTableClass: string;
    rowSelects: unknown[] = [];
    pagination: Pagination;
    idSelects: unknown[] = [];
    columnAll: Column[] = [];
    columnVisible: Column[] = [];
    columnChoose: Column[] = [];

    private tableCommonService = inject(TableCommonService);

    private confirmationService = inject(ConfirmationService);
    private alertService = inject(AlertService);

    ngOnInit() {
        if (!this.rowSelectable) {
            this.rowSelectable = () => true;
        }
        this.tableCommonService.getRowSelect(this.tableId).subscribe((state: unknown) => {
            if (isArray(state)) {
                this.rowSelects = state;
                this.idSelects = state.map((e: unknown) => {
                    return e[this.fieldSelect];
                });
            }
        });
        this.tableCommonService.getPagination(this.tableId).subscribe((state) => (this.pagination = state));

        this.initColums();
        this.initStyle();
        this.initColums();
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['tableStyle']) {
            this.initStyle();
        }
        if (changes['styleClass']) {
            this.initClass();
        }
        if (changes['columns']) {
            this.initColums();
        }
        if (changes['rowHide']) {
            this.initData();
        }
    }

    private initData() {
        if (isArray(this.rowHide) && isArray(this.data)) {
            this.data = this.data.filter((row) => !this.rowHide.includes(row[this.fieldSelect]));
        }
    }

    private initStyle(): void {
        this.combinedTableStyle = {
            ...this.defaultValue.style,
            ...this.tableStyle,
        };
    }

    private initClass(): void {
        this.combinedTableClass = this.defaultValue.class.join(' ') + this.styleClass;
    }

    private initColums(): void {
        this.columnAll = this.columnVisible = this.columns.filter((col: Column, index: number) => {
            col.index = index;
            if (col.typeSort === undefined || col.typeSort === null) {
                col.typeSort = 'asc';
            }
            return !col.hide && !col.visible;
        });
        this.columnChoose = this.columns.filter((col: Column) => {
            return !col.hide || (col.header && col.header.trim().length > 0);
        });
        this.tableCommonService.updateColumnVisible(
            this.tableId,
            this.columnVisible.map((c) => c.field),
        );
    }

    rowSelectableWrapper = (row: unknown) => {
        return this.rowSelectable(row['data']);
    };
    onSort(col: Column): void {
        if (this.pagination.refetch && !this.pagination.local && col.sort) {
            col.typeSort = col?.typeSort === 'asc' ? 'desc' : 'asc';
            this.tableCommonService.updateFilterPageable(this.tableId, {
                sort: `${col.sort || col.field},${col.typeSort}`,
                page: 0,
            });
        }
    }

    isWarning(rowData: unknown): boolean {
        return this.warningIds.includes(rowData[`${this.fieldSelect}`]);
    }

    setRowSelection(selectedRows: unknown | unknown[]) {
        this.tableCommonService.updateRowSelect(this.tableId, isArray(selectedRows) ? selectedRows : [selectedRows]);
    }

    setColumnSelection(selectedColumns: Column[]) {
        const columsShow = selectedColumns.length === 0 ? this.columnAll.filter((col) => col.default) : selectedColumns.filter((col) => col.hide !== true);
        this.columnVisible = Common.sortByOriginalOrder(this.columns, columsShow);

        this.tableCommonService.updateColumnVisible(
            this.tableId,
            this.columnVisible.map((c) => c.field),
        );
    }

    // hideCheckbox(rowData: unknown): boolean {
    //     if (this.hiddenSelect === undefined) return false;
    //     return this.hiddenSelect(rowData);
    // }

    deleteRecord() {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                if (this.funcDelete) {
                    this.loading = true;
                    this.funcDelete(this.idSelects, this.rowSelects).subscribe((res: number[]) => {
                        if (isArray(res) && res.length > 0) {
                            this.warningIds = res;
                            this.loading = false;
                            this.rowSelects = [];
                            this.idSelects = [];
                            this.alertService.error('Lỗi!', 'Không thể xóa các bản ghi không phù hợp');
                        } else {
                            this.warningIds = [];
                            this.rowSelects = [];
                            this.idSelects = [];
                            this.pagination.refetch();
                            this.alertService.success('Thành công', 'Xóa bản ghi thành công');
                        }
                    });
                }
            },
        });
    }

    deleteAllRecord() {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn xóa tất cả',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                if (this.funcDeleteAll) {
                    this.loading = true;
                    this.funcDeleteAll().subscribe({
                        next: () => {
                            this.warningIds = [];
                            this.rowSelects = [];
                            this.idSelects = [];
                            this.pagination.refetch();
                            this.alertService.success('Thành công', 'Xóa tất cả bản ghi thành công');
                        },
                        error: () => {
                            this.alertService.error('Lỗi!', 'Xóa tất cả bản ghi thất bại');
                        },
                        complete: () => {},
                    });
                }
            },
        });
    }

    getLink(colUrl: string, rowData: unknown): string {
        return colUrl.replace(/{(\w+)}/g, (_, key) => rowData[key]);
    }
}
