import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { Po, PoDraft } from 'src/app/models/interface/sc';
import { ApiResponse, GeneralEntity } from '../../../models/interface';

@Injectable()
export class PoDraftService extends BaseService<PoDraft> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/po-draft');
    }

    confirm(id: number, scBomIds: number[]) {
        return this.http.post<ApiResponse>('/sc/api/po-draft/confirm/' + id, scBomIds);
    }

    approvePo(id: number, poIds: number[]) {
        return this.http.post<Po[]>('/sc/api/po-draft/approve/' + id, poIds);
    }

    getBoq(id: number) {
        return this.http.get<GeneralEntity>('/sc/api/po-draft/boq/' + id);
    }
}
