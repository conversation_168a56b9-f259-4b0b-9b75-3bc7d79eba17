import { Component, inject, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { TableCommonService } from '../table.common.service';

@Component({
    selector: 'app-paginator',
    templateUrl: './paginator.component.html',
    styleUrls: ['./paginator.component.scss'],
})
export class PaginatorComponent implements OnInit, OnChanges {
    @Input('tableId') tableId: string;
    @Input() totalCount: number;
    @Input() size: number = 10;
    @Input() currentPage: number = 1;
    @Input() totalPages: number = 0;
    @Input() refetch: (() => void) | undefined;
    newPage: number;

    sizeOption: number[] = [5, 10, 20, 50];

    private tableCommonService = inject(TableCommonService);

    ngOnInit(): void {
        this.newPage = this.currentPage;
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['currentPage']) {
            this.newPage = this.currentPage;
        }
    }

    previousPage() {
        if (this.currentPage > 1) {
            // Thay đổi điều kiện kiểm tra trang trước
            this.currentPage--;
            this.updateFilter();
        }
    }

    nextPage() {
        if (this.currentPage < this.totalPages) {
            // Thay đổi điều kiện kiểm tra trang kế tiếp
            this.currentPage++;
            this.updateFilter();
        }
    }

    goToPage(pageNumber: number) {
        if (this.currentPage === pageNumber) return;
        this.currentPage = pageNumber;
        this.updateFilter();
    }

    changePageSize(size: number) {
        this.size = size;
        this.currentPage = 1; // Reset về trang đầu tiên khi thay đổi kích thước trang
        this.updateFilter();
    }

    private updateFilter() {
        this.tableCommonService.updateFilterPageable(this.tableId, {
            page: this.currentPage - 1,
            size: this.size,
        });
    }
}
