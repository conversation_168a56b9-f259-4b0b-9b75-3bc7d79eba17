<app-sub-header [items]="itemsHeader" [action]="actionHeader">
    <ng-template #actionHeader>
        <p-button
            size="small"
            label="Lưu"
            *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_supplier_edit']"
            severity="success"
            (click)="submitForm('formSupplier')"
        />
        <app-popup
            header="Xuất file chi tiết nhà cung cấp"
            label="Xuất excel"
            *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_supplier_export']"
            (onSubmit)="exportDetail($event)"
            typePopup="download"
        ></app-popup>
        <p-button label="Hủy" severity="secondary" routerLink="/sc/supplier-infor"></p-button>
    </ng-template>
</app-sub-header>

<div style="padding: 1rem 1rem 0 1rem">
    <app-form formId="formSupplier" [formGroup]="formGroup" layout="vertical" (onSubmit)="onSubmit($event)">
        <p-panel header="Thông tin cơ bản" [toggleable]="true">
            <div class="tw-grid lg:tw-grid-cols-2 tw-grid-cols-1 tw-gap-4">
                <app-form-item label="Tên đầy đủ">
                    <input type="text" class="tw-w-full" pInputText formControlName="name" />
                </app-form-item>

                <app-form-item label="Viết tắt">
                    <input type="text" class="tw-w-full" pInputText formControlName="shortName" />
                </app-form-item>

                <app-form-item label="ID">
                    <input type="text" class="tw-w-full" [disabled]="true" placeholder="Hệ thống tự sinh" pInputText formControlName="code" />
                </app-form-item>

                <app-form-item label="Loại hình NCC">
                    <app-filter-table
                        formControlName="supplierTypeId"
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'displayName',
                            param: 'name',
                            rsql: true,
                            url: '/sc/api/supplier-type/search',
                            paramForm: 'id',
                        }"
                        (onChange)="onChangeSuppilerType($event)"
                    ></app-filter-table>
                </app-form-item>
                <app-form-item label="Hàng hóa cung cấp">
                    <textarea rows="5" cols="30" pInputTextarea class="tw-w-full" formControlName="productSupplied"> </textarea>
                </app-form-item>

                <app-form-item label="Địa chỉ">
                    <textarea rows="5" cols="30" pInputTextarea class="tw-w-full" formControlName="address"> </textarea>
                </app-form-item>

                <app-form-item label="Quốc gia">
                    <input type="text" class="tw-w-full" pInputText formControlName="country" />
                </app-form-item>

                <app-form-item label="Website">
                    <input type="text" class="tw-w-full" pInputText formControlName="website" />
                </app-form-item>
                <app-form-item label="Người liên hệ">
                    <input type="text" class="tw-w-full" pInputText formControlName="contact" />
                </app-form-item>

                <app-form-item label="Chức vụ">
                    <input type="text" class="tw-w-full" pInputText formControlName="position" />
                </app-form-item>

                <app-form-item label="Email">
                    <input type="text" class="tw-w-full" pInputText formControlName="email" />
                </app-form-item>

                <app-form-item label="Số điện thoại">
                    <input type="text" class="tw-w-full" pInputText formControlName="phone" />
                </app-form-item>
                <app-form-item label="Năm bắt đầu giao dịch">
                    <p-dropdown [showClear]="true" [options]="yearRange" optionLabel="label" optionValue="value" [formControl]="formGroup.get('yearBegin')" />
                </app-form-item>
                <app-form-item label="Trạng thái nhà cung cấp">
                    <p-dropdown [options]="stateSupplier" optionLabel="label" optionValue="value" [formControl]="formGroup.get('state')" />
                </app-form-item>
                <app-form-item label="Ghi chú" class="tw-col-span-2">
                    <textarea rows="5" cols="30" pInputTextarea class="tw-w-full" formControlName="note"> </textarea>
                </app-form-item>
            </div>
        </p-panel>
        <br />
        <p-panel header="Hồ sơ năng lực" [toggleable]="true">
            <div class="tw-grid lg:tw-grid-cols-2 tw-grid-cols-1 tw-gap-4">
                <div>
                    <app-form-item label="Hồ sơ năng lực">
                        <input type="text" class="tw-w-full" pInputText formControlName="profileLink" />
                    </app-form-item>
                    <div class="tw-mt-4 tw-leading-8" *ngIf="formGroup.get('profileLink').value">
                        <i class="pi pi-verified"></i> <b class="tw-mx-3">Thông tin</b
                        ><span *ngIf="messageSharePoint" class="tw-inline-block text-red-400">( {{ messageSharePoint }} )</span>
                    </div>
                    <div *ngIf="supplierDocumentRes?.documentInfos" class="border-1 border-gray-400 tw-rounded-md tw-p-4">
                        <div *ngFor="let item of supplierDocumentRes.documentInfos; let idx = index; trackBy: trackByName">
                            <p [ngSwitch]="item.status">
                                <span *ngSwitchCase="0" class="text-green-400"> {{ item.name }} : Tài liệu hợp lệ </span>
                                <span *ngSwitchCase="1" class="text-red-400"> {{ item.name }} : Thiếu (vui lòng kiểm tra và bổ sung) </span>
                                <span *ngSwitchCase="2" class="text-red-400"> {{ item.name }} : Đã hết hạn (vui lòng cập nhật) </span>
                                <span *ngSwitchDefault class="text-gray-400"> {{ item.name }} : Trạng thái không xác định </span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </p-panel>

        <br />
        <p-panel header="Lịch sử đánh giá" [toggleable]="true">
            <p-splitter [style]="{ height: 'auto' }" styleClass="border-none">
                <ng-template pTemplate>
                    <div>
                        <p>Đánh giá nhà cung cấp mới</p>
                        <div class="tw-flex tw-flex-row tw-flex-nowrap tw-gap-2 tw-justify-between">
                            <app-form-item label="Phiếu đánh giá">
                                <app-button-group-file
                                    [disabled]="disabledFileCriteria"
                                    (onClickDowload)="handleDowloadFileCriteria()"
                                    (onFileSelected)="handleUploadFileCriteria($event)"
                                    (onClearFile)="handleClearFile('criteria')"
                                    [attachment]="oldSupplier?.criteriaAttachment"
                                    [types]="['excel']"
                                ></app-button-group-file>
                            </app-form-item>
                            <div class="tw-mt-7 tw-align-bottom tw-flex tw-items-center tw-h-full">
                                <span
                                    *ngIf="oldSupplier?.criteriaList && oldSupplier.criteriaList.length > 0"
                                    class="bg-red-400 text-white tw-px-5 tw-py-3"
                                    [ngClass]="{
                                        'bg-red-400': !getPassCriteria(oldSupplier.criteriaList),
                                        'bg-green-400': getPassCriteria(oldSupplier.criteriaList),
                                    }"
                                >
                                    {{ getPassCriteria(oldSupplier.criteriaList) ? 'PASS' : 'FAIL' }}</span
                                >
                            </div>
                        </div>
                        <br />
                        <p-table
                            *ngIf="oldSupplier?.criteriaListView && oldSupplier?.criteriaListView.length > 0"
                            [value]="oldSupplier?.criteriaListView || []"
                        >
                            <ng-template pTemplate="header">
                                <tr>
                                    <th>Tiêu chí</th>
                                    <th>Giá trị</th>
                                    <th>Tiêu chuẩn</th>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="body" let-product>
                                <tr>
                                    <td>{{ product.name }}</td>
                                    <td class="text-red-400">{{ product.value }}</td>
                                    <td>{{ product.valueCriteria }}</td>
                                </tr>
                            </ng-template>
                        </p-table>
                    </div>
                </ng-template>
                <ng-template pTemplate>
                    <div class="ml-4">
                        <p>Đánh giá trước mua</p>
                        <br />
                        <div class="tw-overflow-y-scroll">
                            <p-table
                                [value]="oldSupplier?.criteriaBuySuppliers || []"
                                styleClass="p-datatable-gridlines "
                                [scrollable]="true"
                                scrollHeight="350px"
                            >
                                <ng-template pTemplate="header">
                                    <tr>
                                        <th>Ngày đánh giá</th>
                                        <th>Phiếu đánh giá</th>
                                        <th>Ghi chú</th>
                                        <th>Kết quả</th>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="body" let-criteria>
                                    <tr>
                                        <td>{{ criteria.evaluateDate | date: 'dd/MM/yyyy' }}</td>
                                        <td>
                                            <app-attachment [attachment]="criteria.evaluationDoc"></app-attachment>
                                        </td>
                                        <td>{{ criteria.noteCriteriaBuy }}</td>
                                        <td class="tw-flex tw-flex-row tw-gap-4 tw-items-center">
                                            <span
                                                class="bg-red-400 text-white tw-px-5 tw-py-3"
                                                [ngClass]="{
                                                    'bg-red-400': !getPassCriteria(criteria.criteriaBuySupplierDetails),
                                                    'bg-green-400': getPassCriteria(criteria.criteriaBuySupplierDetails),
                                                }"
                                            >
                                                {{ getPassCriteria(criteria.criteriaBuySupplierDetails) ? 'PASS' : 'FAIL' }}</span
                                            >

                                            <button
                                                class="p-link tw-p-0.5 tw-w-6 tw-h-6 tw-rounded-full tw-bg-gray-800 tw-text-white"
                                                (click)="showDetailResult(criteria.criteriaBuySupplierDetails)"
                                                pTooltip="Xóa"
                                                tooltipPosition="top"
                                                type="button"
                                                *ngIf="!getPassCriteria(criteria.criteriaBuySupplierDetails)"
                                            >
                                                <span class="pi pi-info tw-mx-auto"></span>
                                            </button>
                                        </td>
                                    </tr>
                                </ng-template>
                            </p-table>
                        </div>
                    </div>
                </ng-template>
            </p-splitter>
        </p-panel>
        <br />
        <p-panel header="Thỏa thuận chất lượng" [toggleable]="true">
            <div class="tw-grid lg:tw-grid-cols-2 tw-grid-cols-1 tw-gap-4">
                <app-button-group-file
                    class="tw-col-span-2"
                    simpleUpload="Tải lên thỏa thuận chất lượng"
                    (onClearFile)="handleClearFile('quality')"
                    (onFileSelected)="handleUploadFileQualityAgree($event)"
                    [attachment]="oldSupplier?.agreeQualityAttachment"
                ></app-button-group-file>
                <app-form-item label="Mã số TTCL">
                    <input type="text" class="tw-w-full" pInputText formControlName="qualityAgree" />
                </app-form-item>
                <app-form-item label="Ngày ký TTCL">
                    <p-calendar
                        [showButtonBar]="true"
                        placeholder="dd/MM/yyyy"
                        [showIcon]="true"
                        dateFormat="dd/mm/yy"
                        formControlName="dateAgreeCustom"
                    ></p-calendar>
                </app-form-item>
            </div>
            <br />
            <p-table styleClass="p-datatable-gridlines " [scrollable]="true" [value]="oldSupplier.agreeItems" scrollHeight="500px">
                <ng-template pTemplate="header">
                    <tr>
                        <th>VNPT/PN</th>
                        <th>Mô tả</th>
                        <th>Nhà sản xuất</th>
                    </tr>
                </ng-template>

                <ng-template pTemplate="body" let-item>
                    <tr>
                        <td>{{ item.internalReference }}</td>
                        <td>{{ item.productDescription }}</td>
                        <td>{{ item.manufacturerName }}</td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="30" class="text-center">Không có dữ liệu</td>
                    </tr>
                </ng-template>
            </p-table>
        </p-panel>
        <br />
    </app-form>
    <p-panel *ngIf="supplierId" header="Thông tin giao dịch" [toggleable]="true">
        <app-form formId="supplierReport" [formGroup]="formReport" styleClass="tw-grid tw-gap-5 tw-grid-cols-4 tw-relative" (onSubmit)="handleFilter()">
            <app-form-item>
                <p-dropdown
                    [options]="[
                        {
                            label: 'Năm',
                            value: 'year',
                        },
                        {
                            label: 'Tháng',
                            value: 'month',
                        },
                    ]"
                    optionLabel="label"
                    optionValue="value"
                    formControlName="type"
                    placeholder="Chọn"
            /></app-form-item>
            <app-form-item *ngIf="formReport.get('type').value === 'year'">
                <p-dropdown [options]="yearRange" optionLabel="label" formControlName="year" optionValue="value"
            /></app-form-item>
            <app-form-item *ngIf="formReport.get('type').value === 'month'">
                <p-calendar view="month" dateFormat="mm/yy" formControlName="startTime" placeholder="Từ"></p-calendar
            ></app-form-item>
            <app-form-item *ngIf="formReport.get('type').value === 'month'">
                <p-calendar view="month" dateFormat="mm/yy" formControlName="endTime" placeholder="Đến"></p-calendar
            ></app-form-item>
            <p-button class="tw-col-start-4 tw-justify-self-end" type="submit" label="Báo cáo"></p-button>
            <p *ngIf="formReport.errors?.maxDateRange" class="text-red-400 tw-col-span-full">Khoảng thời gian tối đa là 36 tháng</p>

            <div class="tw-absolute tw-right-0 tw-z-10 tw-flex tw-gap-4 tw-flex-row" style="top: calc(100% + 10px)">
                <span class="tw-p-3 border-1 border-gray-300 tw-rounded">Điểm trung bình: {{ averagePoint }}</span>
                <span class="tw-p-3 border-1 border-gray-300 tw-rounded">Xếp hạng: {{ averageRate }}</span>
            </div>
        </app-form>
        <p-tabView [(activeIndex)]="activeIndex">
            <p-tabPanel header="Báo cáo chất lượng">
                <app-supplier-chart-quality #childQuality [supplierId]="supplierId" [formReport]="formReport"></app-supplier-chart-quality>
            </p-tabPanel>
            <p-tabPanel header="Biến động giá">
                <app-supplier-chart-price #childPrice [supplierId]="supplierId" [formReport]="formReport"></app-supplier-chart-price>
            </p-tabPanel>
            <p-tabPanel header="Tiến độ giao hàng">
                <app-supplier-chart-delivery #childDelivery [supplierId]="supplierId" [formReport]="formReport"></app-supplier-chart-delivery>
            </p-tabPanel>
        </p-tabView>
    </p-panel>
    <br />

    <app-suppplier-table-po [hasViewPriceRole]="hasViewPriceRole" *ngIf="supplierId" [supplierId]="supplierId" [provideItems]="oldSupplier?.provideItems"></app-suppplier-table-po>

    <br />
</div>

<p-dialog
    header="Chi tiết kết quả đánh giá"
    [modal]="true"
    [(visible)]="isOpenModalViewResult"
    (onHide)="isOpenModalViewResult = false"
    [breakpoints]="{ '1199px': '40vw', '575px': '30vw' }"
    [style]="{ width: '40vw' }"
>
    <div>
        <div class="tw-flex tw-justify-end tw-items-center tw-mb-5">
            <span
                class="bg-red-400 text-white tw-px-5 tw-py-3"
                [ngClass]="{
                    'bg-red-400': !getPassCriteria(resultsDetail),
                    'bg-green-400': getPassCriteria(resultsDetail),
                }"
            >
                {{ getPassCriteria(resultsDetail) ? 'PASS' : 'FAIL' }}</span
            >
        </div>
        <p-table [value]="resultsDetail" styleClass="p-datatable-gridlines">
            <ng-template pTemplate="header">
                <tr>
                    <th>Tiêu chí</th>
                    <th>Giá trị</th>
                    <th>Tiêu chuẩn</th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                <tr>
                    <td>{{ item.name }}</td>
                    <td class="text-red-400">{{ item.value }}</td>
                    <td>{{ item.valueCriteria }}</td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</p-dialog>
