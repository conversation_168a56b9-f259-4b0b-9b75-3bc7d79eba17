import { canAuthorize } from '../../../core/auth/auth.guard';

export const PFRouting = {
    path: 'product-file',
    title: '<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> hồ sơ sản phẩm',

    children: [
        {
            path: '',
            title: '<PERSON><PERSON> s<PERSON><PERSON> hồ sơ sản phẩm',
            canActivate: [canAuthorize],
            data: {
                authorize: ['ROLE_SYSTEM_ADMIN', 'version_view'],
            },
            loadComponent: () => import('./list/list.component').then((c) => c.ProductFileListComponent),
        },
        {
            path: 'create',
            title: 'T<PERSON><PERSON> mớ<PERSON> hồ sơ sản phẩm',
            canActivate: [canAuthorize],
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'version_create'] },
            loadComponent: () => import('./edit/product-file.edit.component').then((c) => c.ProductFileEditComponent),
        },
        {
            path: 'edit/:productId/:versionId',
            title: 'Chỉnh s<PERSON><PERSON> hồ sơ sản phẩm',
            canActivate: [canAuthorize],
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'version_update'] },
            loadComponent: () => import('./edit/product-file.edit.component').then((c) => c.ProductFileEditComponent),
        },
        {
            path: 'view/:productId/:versionId',
            title: 'Xem chi tiết hồ sơ sản phẩm',
            canActivate: [canAuthorize],
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'version_detail_view'] },
            loadComponent: () => import('./edit/product-file.edit.component').then((c) => c.ProductFileEditComponent),
        },
    ],
};
