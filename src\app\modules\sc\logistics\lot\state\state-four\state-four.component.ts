import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PanelModule } from 'primeng/panel';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { TableModule } from 'primeng/table';
import { FormCustomModule } from '../../../../../../shared/form-module/form.custom.module';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { CalendarModule } from 'primeng/calendar';
import { DropdownModule } from 'primeng/dropdown';
import { ButtonModule } from 'primeng/button';
import { Lot, LotCustom } from '../../../../../../models/interface/sc';
import { DialogModule } from 'primeng/dialog';
import { ButtonGroupFileComponent } from '../../../../../../shared/components/button-group-file/button-group-file.component';
import { LotService } from 'src/app/services/sc/lot/lot.service';
import { AttachmentComponent } from 'src/app/shared/components/attachment/attachment.component';
import { FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { ApiResponse, User } from 'src/app/models/interface';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { LotCustomService } from 'src/app/services/sc/lot/lot-custom.service';
import { InputNumberModule } from 'primeng/inputnumber';
import { InputNumberComponent } from 'src/app/shared/components/inputnumber/inputnumber.component';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { debounce } from 'lodash';

@Component({
    selector: 'app-lot-state-four',
    standalone: true,
    templateUrl: './state-four.component.html',
    styleUrls: ['./state-four.component.scss'],
    imports: [
        CommonModule,
        PanelModule,
        InputTextModule,
        InputNumberModule,
        InputTextareaModule,
        TableModule,
        TableCommonModule,
        FormCustomModule,
        ReactiveFormsModule,
        OverlayPanelModule,
        CalendarModule,
        FormCustomModule,
        DropdownModule,
        ButtonModule,
        DialogModule,
        ButtonGroupFileComponent,
        AttachmentComponent,
        InputNumberComponent,
        HasAnyAuthorityDirective,
    ],
    providers: [LotService, LotCustomService],
})
export class StateFourComponent implements OnInit {
    @Input() lot: Lot;
    @Input() receivers: User[];

    // Form group
    formGroup: FormGroup;

    @ViewChild('form', { static: false }) form: FormComponent;
    @ViewChild('formSubmit', { static: false }) formSubmit: FormComponent;

    lotCustom: LotCustom;

    //
    visibleSubmit: boolean = false;
    formGroupSubmit: FormGroup;
    @Output('onComplete') onComplete: EventEmitter<Lot> = new EventEmitter();

    constructor(
        private fb: FormBuilder,
        private loadingService: LoadingService,
        private alertService: AlertService,
        private lotService: LotService,
        private lotCustomService: LotCustomService,
    ) {
        this.initForm(null);
    }

    ngOnInit() {
        this.formGroupSubmit = new FormGroupCustom(this.fb, {
            receiverIds: [null, Validators.required],
            content: [null],
        });
        this.loadingService.show();
        this.lotCustomService.getByLot(this.lot.id).subscribe({
            next: (res) => {
                this.lotCustom = res;
                this.loadingService.hide();
                this.initForm(res);
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    initForm(data: LotCustom) {
        this.formGroup = null;
        this.formGroup = new FormGroupCustom(this.fb, {
            lotId: [data?.lotId ?? this.lot?.id],
            boId: [data?.boId ?? this.lot?.boId],
            customsDocumentNumber: [data?.customsDocumentNumber],
            customsDeclareDateCustom: [data?.customsDeclareDate ? new Date(data?.customsDeclareDate) : null],
            vatTax: [data?.vatTax],
            taxImport: [data?.taxImport],
            totalTax: [data?.totalTax],
            orderCode: [data?.orderCode],
            submitDateCustom: [data?.submitDate ? new Date(data?.submitDate) : null],
            otherFee: [data?.otherFee],
            note: [data?.note],
            attachmentCustomId: [data?.attachmentCustomId],
            attachmentCustom: [data?.attachmentCustom],
            attachmentIds: [data?.attachmentIds],
            attachments: [data?.attachments],
        });

        this.formGroup.valueChanges.subscribe(() => {
            this.debouncedUpdateState();
        });
    }

    handleUploadFile(files: File[]) {
        this.loadingService.show();
        this.lotCustomService.importFile(files).subscribe({
            next: (res: ApiResponse) => {
                const itemValue = this.formGroup.getRawValue() as LotCustom;

                const attachmentIds = itemValue?.attachmentIds ?? [];
                const attachments = itemValue?.attachments ?? [];
                if (res.code === 1) {
                    // Nối mảng cũ và mảng mới
                    const newAttachmentIds = [...attachmentIds, ...(res.data['attachmentIds'] || [])];
                    const newAttachments = [...attachments, ...(res.data['attachments'] || [])];
                    this.formGroup.patchValue({
                        attachmentIds: newAttachmentIds,
                        attachments: newAttachments,
                    });
                } else {
                    this.alertService.error('Có lỗi xảy ra khi lưu file');
                    this.formGroup.patchValue({ attachmentIds: attachmentIds, attachments: attachments });
                }

                this.loadingService.hide();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    handleUploadFileCustoms(file: File) {
        this.loadingService.show();
        this.lotCustomService.importFileCustoms(file).subscribe({
            next: (res: ApiResponse) => {
                if (res.code === 1 && res?.data) {
                    const data = res.data as LotCustom;
                    this.formGroup.patchValue({
                        attachmentCustomId: res?.attachment.id,
                        attachmentCustom: res?.attachment,
                        customsDocumentNumber: data.customsDocumentNumber,
                        customsDeclareDateCustom: data.customsDeclareDate ? new Date(data.customsDeclareDate) : null,
                        vatTax: data.vatTax,
                        taxImport: data.taxImport,
                        totalTax: data.totalTax,
                    });
                } else if (res.code === 0) {
                    this.alertService.error('Có lỗi xảy ra khi lưu file');
                    this.formGroup.patchValue({ attachmentCustomId: null, attachmentCustom: null });
                }

                this.loadingService.hide();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }
    debouncedUpdateState = debounce(() => {
        this.onSubmitState();
    }, 500);

    onSubmitState() {
        if (this.formGroup.status !== 'VALID') return;
        const lotCustomForm = this.formGroup.getRawValue() as LotCustom;
        lotCustomForm.customsDeclareDate = lotCustomForm.customsDeclareDateCustom ? lotCustomForm.customsDeclareDateCustom.getTime() : null;
        lotCustomForm.submitDate = lotCustomForm.submitDateCustom ? lotCustomForm.submitDateCustom.getTime() : null;

        this.lotService.stateFourUpdate({ ...this.lotCustom, ...lotCustomForm }).subscribe({
            next: (res) => {
                this.onComplete.emit(res);
            },
            error: () => {},
        });
    }

    sendNotification(value: { receiverIds: number[]; content: string }) {
        this.loadingService.show();
        this.lotService.sendNotification(this.lot.id, value).subscribe({
            next: () => {
                this.visibleSubmit = false;
                this.loadingService.hide();
                this.alertService.success('Thành công');
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    handleChangeReceivers(event) {
        if (event.value !== null && event.objects !== null && event.objects.length > 0) {
            const ids = event.objects.map((obj) => obj.id);
            this.formGroupSubmit.patchValue({
                receiverIds: ids,
            });
        } else {
            this.formGroupSubmit.patchValue({
                receiverIds: null,
            });
        }
    }
}
