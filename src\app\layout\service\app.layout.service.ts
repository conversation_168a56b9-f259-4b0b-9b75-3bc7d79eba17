import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { SideBarItem } from '../components/sidebar/sidebar.menu.component';
import { Module } from 'src/app/models/constant';
import { isArray, isEqual } from 'lodash';

export interface SideBarState {
    items: SideBarItem[];
    state: 'collapse' | 'expand' | 'close';
    device: 'mobile' | 'desktop';
}

@Injectable({
    providedIn: 'root',
})
export class LayoutService {
    private readonly MOBILE_WIDTH = 768;

    module = new BehaviorSubject<Module>(null);
    sideBarState = new BehaviorSubject<SideBarState>({
        items: [],
        state: this.getInitialState(),
        device: this.getDeviceType(),
    });

    constructor() {
        this.checkScreenSize();
        window.addEventListener('resize', () => this.checkScreenSize());
    }

    changeSideBarState() {
        const { state, items } = this.sideBarState.getValue();
        const isMobile = this.isMobile();

        let newState: 'collapse' | 'expand' | 'close';

        if (isMobile) {
            newState = state === 'close' ? 'expand' : 'close';
            if (!this.hasItems(items)) newState = 'close';
        } else {
            newState = state === 'expand' ? 'collapse' : 'expand';
            if (!this.hasItems(items)) newState = 'close';
        }

        this.updateSideBarState(newState);
    }

    private checkScreenSize() {
        const isMobile = this.isMobile();
        const { device, items } = this.sideBarState.getValue();

        if (isMobile && device !== 'mobile') {
            this.updateSideBarState('close', 'mobile');
        } else if (!isMobile && device !== 'desktop') {
            const newState = this.hasItems(items) ? 'expand' : 'close';
            this.updateSideBarState(newState, 'desktop');
        }
    }

    setSideBarItems(items: SideBarItem[]) {
        const { state } = this.sideBarState.getValue();
        const isMobile = this.isMobile();
        let newState = state;

        if (!this.hasItems(items)) {
            newState = 'close';
        } else if (this.hasItems(items) && !isMobile) {
            newState = state === 'close' ? 'expand' : state;
        }

        this.updateSideBarState(newState, undefined, items);
    }

    setModule(module: Module) {
        if (isEqual(module, this.module.getValue())) return;
        this.module.next(module);
    }

    private updateSideBarState(
        newState: 'collapse' | 'expand' | 'close',
        device: 'mobile' | 'desktop' = this.sideBarState.getValue().device,
        items: SideBarItem[] = this.sideBarState.getValue().items,
    ) {
        if (
            isEqual(newState, this.sideBarState.getValue().state) &&
            isEqual(items, this.sideBarState.getValue().items)
        ) {
            return;
        }

        this.sideBarState.next({ state: newState, device, items });
    }

    private getInitialState(): 'collapse' | 'expand' | 'close' {
        return window.innerWidth <= this.MOBILE_WIDTH ? 'close' : 'expand';
    }

    private getDeviceType(): 'mobile' | 'desktop' {
        return window.innerWidth <= this.MOBILE_WIDTH ? 'mobile' : 'desktop';
    }

    private isMobile(): boolean {
        return window.innerWidth <= this.MOBILE_WIDTH;
    }

    private hasItems(items: SideBarItem[]): boolean {
        return isArray(items) && items.length > 0;
    }
}
