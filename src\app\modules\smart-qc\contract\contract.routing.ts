import { canAuthorize } from "../../../core/auth/auth.guard";

export const ContractRouting = {
    path: 'contract',
    children: [
        {
            path: '',
            title: 'Dự án',
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'ROLE_QC_SUBPM', 'ROLE_QC_CUSTOMER'] },
            canActivate: [canAuthorize],
            loadComponent: () => import('./contract.component').then((c) => c.ContractComponent),
        },
        {
            path: 'create',
            title: 'Tạo dự án',
            canActivate: [canAuthorize],
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM'] },
            loadComponent: () =>
                import('./contract.create.component').then((c) => c.ContractCreateComponent),
        },
        {
            path: ':id/edit',
            title: '<PERSON> tiết dự án',
            canActivate: [canAuthorize],
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'ROLE_QC_SUBPM', 'ROLE_QC_CUSTOMER'] },
            loadComponent: () =>
                import('./contract.edit.component').then((c) => c.ContractEditComponent),
        },
    ],
}
