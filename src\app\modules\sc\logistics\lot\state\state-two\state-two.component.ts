import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PanelModule } from 'primeng/panel';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { TableModule } from 'primeng/table';
import { FormCustomModule } from '../../../../../../shared/form-module/form.custom.module';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { CalendarModule } from 'primeng/calendar';
import { DropdownModule } from 'primeng/dropdown';
import { ButtonModule } from 'primeng/button';
import { BoDto, Lot, LotCustom, LotHistory, LotHistoryExport } from '../../../../../../models/interface/sc';
import { DialogModule } from 'primeng/dialog';
import { ButtonGroupFileComponent } from '../../../../../../shared/components/button-group-file/button-group-file.component';
import { LotService } from 'src/app/services/sc/lot/lot.service';
import { LotHistoryService } from 'src/app/services/sc/lot/lot-history.service';
import { AttachmentComponent } from 'src/app/shared/components/attachment/attachment.component';
import { FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { ApiResponse, User } from 'src/app/models/interface';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { LOT_HISTORY_TWO, TYPE_SHIPPING_EXPORT } from 'src/app/models/constant/sc';
import { TabViewChangeEvent, TabViewModule } from 'primeng/tabview';
import { LotCustomService } from 'src/app/services/sc/lot/lot-custom.service';
import { forkJoin } from 'rxjs';
import { InputNumberModule } from 'primeng/inputnumber';
import { LotHistoryExportService } from 'src/app/services/sc/lot/lot-history-export.service';
import { FileService } from 'src/app/shared/services/file.service';
import { InputNumberComponent } from 'src/app/shared/components/inputnumber/inputnumber.component';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { debounce } from 'lodash';
@Component({
    selector: 'app-lot-state-two',
    standalone: true,
    templateUrl: './state-two.component.html',
    styleUrls: ['./state-two.component.scss'],
    imports: [
        CommonModule,
        PanelModule,
        InputTextModule,
        InputTextareaModule,
        InputNumberModule,
        TableModule,
        TableCommonModule,
        FormCustomModule,
        ReactiveFormsModule,
        OverlayPanelModule,
        CalendarModule,
        FormCustomModule,
        DropdownModule,
        ButtonModule,
        DialogModule,
        ButtonGroupFileComponent,
        AttachmentComponent,
        TabViewModule,
        InputNumberComponent,
        HasAnyAuthorityDirective,
    ],
    providers: [LotService, LotHistoryService, LotCustomService, LotHistoryExportService],
})
export class StateTwoComponent implements OnInit, OnChanges {
    @Input() lot: Lot;
    @Input() receivers: User[];

    lastLotHistory: LotHistory;
    // Form group
    formGroup: FormGroup;
    visibleNew: boolean = false;
    visibleHistory: boolean = false;

    @ViewChild('form', { static: false }) form: FormComponent;
    @ViewChild('formSubmit', { static: false }) formSubmit: FormComponent;
    @ViewChild('formGroupExport', { static: false }) formGroupExport: FormComponent;

    // table history
    lotHistoryList: LotHistory[] = [];
    isLoadingList: boolean = false;

    //
    visibleSubmit: boolean = false;
    formGroupSubmit: FormGroup;
    @Output('onComplete') onComplete: EventEmitter<Lot> = new EventEmitter();

    //
    visibleExport: boolean = false;
    boDto: BoDto;
    formGroupBoDto: FormGroup;
    activeIndexPopupExport: number = 0;
    listExportHistory: LotHistoryExport[] = [];
    // four
    formGroupFour: FormGroup;
    lotCustom: LotCustom;
    constructor(
        private fb: FormBuilder,
        private lotHistoryService: LotHistoryService,
        private loadingService: LoadingService,
        private alertService: AlertService,
        private lotService: LotService,
        private lotCustomService: LotCustomService,
        private lotHistoryExportService: LotHistoryExportService,
        private fileService: FileService,
    ) {
        this.initFormGroupBoDto(null);
        this.initFormFour(null);
    }
    ngOnChanges(changes: SimpleChanges): void {
        if (changes['lot'] && !changes['lot']?.previousValue && changes['lot']?.currentValue) {
            this.loadingService.show();
            forkJoin([this.lotHistoryService.getLast(this.lot.id, LOT_HISTORY_TWO), this.lotCustomService.getByLot(this.lot.id)]).subscribe({
                next: ([lotHistory, lotCustom]) => {
                    this.lastLotHistory = lotHistory;
                    this.lotCustom = lotCustom;
                    this.loadingService.hide();
                    if (this.lot.type === TYPE_SHIPPING_EXPORT) {
                        this.initFormFour(lotCustom);
                    }
                },
                error: () => {
                    this.loadingService.hide();
                },
            });
        }
    }

    ngOnInit() {
        this.formGroupSubmit = new FormGroupCustom(this.fb, {
            receiverIds: [null, Validators.required],
            content: [null],
        });
    }

    showPopupCreateHis() {
        this.visibleNew = true;
        this.formGroup = null;

        this.formGroup = new FormGroupCustom(this.fb, {
            lotId: [this.lot.id],
            boId: [this.lot.boId],
            logisticId: [this.lot.salesAgentId],
            expectedDeliveryDateCustom: [],
            expectedLeaveDateCustom: [],
            expectedPortDateCustom: [],
            expectedWarehouseDateCustom: [],
            note: [],
            type: [LOT_HISTORY_TWO],
            attachmentIds: [],
            attachments: [],
        });
    }

    initFormFour(data: LotCustom) {
        this.formGroupFour = null;

        this.formGroupFour = new FormGroupCustom(this.fb, {
            lotId: [data?.lotId ?? this.lot?.id],
            boId: [data?.boId ?? this.lot?.boId],
            customsDocumentNumber: [data?.customsDocumentNumber],
            customsDeclareDateCustom: [data?.customsDeclareDate ? new Date(data?.customsDeclareDate) : null],
            vatTax: [data?.vatTax],
            taxImport: [data?.taxImport],
            totalTax: [data?.totalTax],
            orderCode: [data?.orderCode],
            submitDateCustom: [data?.submitDate ? new Date(data?.submitDate) : null],
            otherFee: [data?.otherFee],
            note: [data?.note],
            attachmentCustomId: [data?.attachmentCustomId],
            attachmentCustom: [data?.attachmentCustom],
            attachmentIds: [data?.attachmentIds],
            attachments: [data?.attachments],
        });

        this.formGroupFour.valueChanges.subscribe(() => {
            this.debouncedUpdateStateFour();
        });
    }

    handleUploadFileLotCustoms(files: File[]) {
        this.loadingService.show();
        this.lotCustomService.importFile(files).subscribe({
            next: (res: ApiResponse) => {
                const itemValue = this.formGroupFour.getRawValue() as LotCustom;

                const attachmentIds = itemValue?.attachmentIds ?? [];
                const attachments = itemValue?.attachments ?? [];
                if (res.code === 1) {
                    // Nối mảng cũ và mảng mới
                    const newAttachmentIds = [...attachmentIds, ...(res.data['attachmentIds'] || [])];
                    const newAttachments = [...attachments, ...(res.data['attachments'] || [])];
                    this.formGroupFour.patchValue({
                        attachmentIds: newAttachmentIds,
                        attachments: newAttachments,
                    });
                } else {
                    this.alertService.error('Có lỗi xảy ra khi lưu file');
                    this.formGroupFour.patchValue({ attachmentIds: attachmentIds, attachments: attachments });
                }

                this.loadingService.hide();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    handleUploadFile(files: File[]) {
        this.loadingService.show();
        this.lotHistoryService.importFile(files).subscribe({
            next: (res: ApiResponse) => {
                const itemValue = this.formGroup.getRawValue() as LotHistory;
                const attachmentIds = itemValue?.attachmentIds ?? [];
                const attachments = itemValue?.attachments ?? [];
                if (res.code === 1) {
                    // Nối mảng cũ và mảng mới
                    const newAttachmentIds = [...attachmentIds, ...(res.data['attachmentIds'] || [])];
                    const newAttachments = [...attachments, ...(res.data['attachments'] || [])];
                    this.formGroup.patchValue({
                        attachmentIds: newAttachmentIds,
                        attachments: newAttachments,
                    });
                } else {
                    this.alertService.error('Có lỗi xảy ra khi lưu file');
                    this.formGroup.patchValue({ attachmentIds: attachmentIds, attachments: attachments });
                }
                this.loadingService.hide();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    onSubmitHis(value: LotHistory) {
        value.expectedDeliveryDate = value.expectedDeliveryDateCustom ? value.expectedDeliveryDateCustom.getTime() : this.lastLotHistory?.expectedDeliveryDate;
        value.expectedLeaveDate = value.expectedLeaveDateCustom ? value.expectedLeaveDateCustom.getTime() : this.lastLotHistory?.expectedLeaveDate;
        value.expectedPortDate = value.expectedPortDateCustom ? value.expectedPortDateCustom.getTime() : this.lastLotHistory?.expectedPortDate;
        value.expectedWarehouseDate = value.expectedWarehouseDateCustom
            ? value.expectedWarehouseDateCustom.getTime()
            : this.lastLotHistory?.expectedWarehouseDate;
        this.loadingService.show();
        this.lotHistoryService.create(value).subscribe({
            next: (res) => {
                this.loadingService.hide();
                this.alertService.success('Thành công');
                this.visibleNew = false;
                this.lastLotHistory = res.body;
                this.onSubmitStateTwo();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    getTotalHistory() {
        this.visibleHistory = true;
        this.isLoadingList = true;
        this.lotHistoryService.getPage(`query=lotId==${this.lot.id};type==${LOT_HISTORY_TWO}&page=0&size=1000&sort=created,desc`).subscribe({
            next: (res) => {
                this.lotHistoryList = res.body;
                this.isLoadingList = false;
            },
            error: () => {
                this.isLoadingList = false;
            },
        });
    }

    onSubmitStateTwo() {
        if (this.formGroup.status !== 'VALID') return;
        this.lotService.stateDeliveryUpdate(this.lot).subscribe({
            next: (res) => {
                this.onComplete.emit(res);
            },
            error: () => {},
        });
    }

    onSubmitStateFour() {
        if (this.formGroupFour.status !== 'VALID') return;

        const lotCustomForm = this.formGroupFour.getRawValue() as LotCustom;
        lotCustomForm.customsDeclareDate = lotCustomForm.customsDeclareDateCustom ? lotCustomForm.customsDeclareDateCustom.getTime() : null;
        lotCustomForm.submitDate = lotCustomForm.submitDateCustom ? lotCustomForm.submitDateCustom.getTime() : null;

        this.lotService.stateFourUpdate({ ...this.lotCustom, ...lotCustomForm }).subscribe({
            next: (res) => {
                this.onComplete.emit(res);
            },
            error: () => {},
        });
    }

    debouncedUpdateStateFour = debounce(() => {
        this.onSubmitStateFour();
    }, 500);

    sendNotification(value: { receiverIds: number[]; content: string }) {
        this.loadingService.show();
        this.lotService.sendNotification(this.lot.id, value).subscribe({
            next: () => {
                this.visibleSubmit = false;
                this.loadingService.hide();
                this.alertService.success('Thành công');
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    showPopupExport() {
        this.visibleExport = true;
        this.lotHistoryService.getInfo(this.lot.id).subscribe({
            next: (res) => {
                this.boDto = res;
                this.initFormGroupBoDto(res);
            },
        });
    }

    handleUploadFileCustoms(file: File) {
        this.loadingService.show();
        this.lotCustomService.importFileCustoms(file).subscribe({
            next: (res: ApiResponse) => {
                if (res.code === 1 && res.data) {
                    const data = res.data as LotCustom;
                    this.formGroupFour.patchValue({
                        attachmentCustomId: res?.attachment.id,
                        attachmentCustom: res?.attachment,
                        customsDocumentNumber: data.customsDocumentNumber,
                        customsDeclareDateCustom: data.customsDeclareDate ? new Date(data.customsDeclareDate) : null,
                        vatTax: data.vatTax,
                        taxImport: data.taxImport,
                        totalTax: data.totalTax,
                    });
                } else if (res.code === 0) {
                    this.alertService.error('Có lỗi xảy ra khi lưu file');
                    this.formGroupFour.patchValue({ attachmentCustomId: null, attachmentCustom: null });
                }

                this.loadingService.hide();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    initFormGroupBoDto(data: BoDto) {
        this.formGroupBoDto = new FormGroupCustom(this.fb, {
            boId: [data?.boId],
            goodsName: [data?.goodsName],
            supplierName: [data?.supplierName],
            supplierAddress: [data?.supplierAddress],
            totalWeight: [data?.totalWeight],
            packageNumber: [data?.packageNumber],
            unitLabel: [data?.unitLabel],
            deliveryCondition: [data?.deliveryCondition],
            shipmentValue: [data?.shipmentValue],
            expectedLeaveDateCustom: [data?.expectedLeaveDate ? new Date(data.expectedLeaveDate) : null],
            paymentCondition: [data?.paymentCondition],
            boCode: [data?.boCode],
            accountingCode: [data?.accountingCode],
            rateMoney: [data?.rateMoney],
            exportDateCustom: [new Date()],
            tabulator: [''],
            boInsuranceDtos: this.fb.array(
                (data?.boInsuranceDtos || []).map((item) =>
                    this.fb.group({
                        name: [item?.name],
                        isChoice: [item?.isChoice || false],
                        insuranceRate: [item?.insuranceRate],
                        insuranceFee: [item?.insuranceFee],
                        note: [item?.note],
                    }),
                ),
            ),
        });
    }

    onExport(value: BoDto, type: number) {
        //type: 0: YCBH, 1: YCBH_UPDATE

        const boDto = this.formGroupBoDto.getRawValue() as BoDto;
        const exportDate = boDto.exportDateCustom ? boDto.exportDateCustom.getTime() : null;
        const expectedLeaveDate = boDto.expectedLeaveDateCustom ? boDto.expectedLeaveDateCustom.getTime() : null;
        const tabulator = type === 1 ? boDto.tabulator : '';
        this.loadingService.show();
        this.lotHistoryExportService.export({ ...value, expectedLeaveDate, exportDate, tabulator }, this.lot.id, this.lastLotHistory?.id, type).subscribe({
            next: (res) => {
                this.loadingService.hide();
                this.fileService.downLoadFileByService(res.url, '/sc/api');
                this.loadingService.hide();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    getLotHistoryExport(event: TabViewChangeEvent) {
        if (event.index === 1) {
            this.lotHistoryExportService.getPage(`query=lotId==${this.lot.id}&page=0&size=1000&sort=created,desc`).subscribe({
                next: (res) => {
                    this.listExportHistory = res.body;
                },
                error: () => {},
            });
        }
    }

    handleChangeReceivers(event) {
        if (event.value !== null && event.objects !== null && event.objects.length > 0) {
            const ids = event.objects.map((obj) => obj.id);
            this.formGroupSubmit.patchValue({
                receiverIds: ids,
            });
        } else {
            this.formGroupSubmit.patchValue({
                receiverIds: null,
            });
        }
    }
}
