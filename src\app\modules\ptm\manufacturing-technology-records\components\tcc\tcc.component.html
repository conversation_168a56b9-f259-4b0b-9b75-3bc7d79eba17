<ng-container>
    <form [formGroup]="tccForm">
        <div class="p-fluid tw-space-y-2">
            <app-info-share [title]="title"></app-info-share>
        </div>
        <div class="detail-tab tw-mt-4">
            <div class="tw-flex tw-justify-between tw-border-b-2">
                <div> Chi tiết về TCC</div>
                <div class="tw-flex tw-space-x-2">
                    <p-button label="Track Changes" size="small"></p-button>
                    <p-button label="Xuất excel" size="small"></p-button>
                </div>
            </div>
        </div>
        <div formArrayName="tccSections">
            <div *ngFor="let section of tccSections.controls; let i = index" [formGroupName]="i">
                <h3>{{ (section.get('line')?.value) }}</h3>
                <div class="tw-grid tw-grid-cols-2 tw-mt-4 tw-gap-4">
                    <div formGroupName="tccProductionTime" class="table-wrapper">

                    </div>
                </div>
            </div>
        </div>
    </form>
</ng-container>
