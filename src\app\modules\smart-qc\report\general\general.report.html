<style>
    .custom-table th,
    .custom-table td {
        border: 1px solid #ddd;
    }

    .custom-table {
        border-collapse: collapse;
    }
</style>

<app-sub-header [items]="itemsHeader" [action]="actionHeader"></app-sub-header>
<ng-template #actionHeader>
    <p-button (click)="getReport()" size="small" label="Xem báo cáo" severity="success" />
    <p-button (click)="export()" size="small" label="Xuất Excel" severity="success" />
</ng-template>
<div class="tw-p-5">
    <form [formGroup]="reportForm" class="tw-bg-white tw-p-5 tw-rounded-xl tw-mb-5">
        <div class="tw-grid lg:tw-grid-cols-5 md:tw-grid-cols-1 sm:tw-grid-cols-1 tw-gap-4 tw-mb-3">
            <div class="tw-flex tw-flex-col tw-space-y-3">
                <div class="tw-font-bold">Dự án <span class="tw-text-red-600">(*)</span>:</div>
                <div *ngIf="authService.isAdminOrPM()">
                    <app-filter-table
                        field="name"
                        rsql="true"
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            rsql: true,
                            param: 'name',
                            url: '/smart-qc/api/contract/search',
                        }"
                        (onChange)="selectContract($event)"
                        placeholder="Tên dự án"
                        #filerArea
                    ></app-filter-table>
                </div>
                <div *ngIf="authService.isSubPM()">
                    <app-filter-table
                        field="name"
                        rsql="true"
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            param: 'name',
                            rsql: false,
                            body: {},
                            sort: 'name',
                            url: '/smart-qc/api/combobox/contract-sub-pm',
                        }"
                        (onChange)="selectContract($event)"
                        placeholder="Tên dự án"
                        #filerArea
                    ></app-filter-table>
                </div>
                <div *ngIf="authService.isCustomer()">
                    <app-filter-table
                        field="name"
                        rsql="true"
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            param: 'name',
                            rsql: false,
                            body: {},
                            url: '/smart-qc/api/combobox/contract-customer',
                        }"
                        (onChange)="selectContract($event)"
                        placeholder="Tên dự án"
                        #filerArea
                    ></app-filter-table>
                </div>
                <div
                    *ngIf="reportForm.get('contractId').errors && reportForm.get('contractId').touched"
                    class="text-red-600"
                >
                    <div *ngIf="reportForm.get('contractId').errors['required']">Tên dự án là trường bắt buộc</div>
                </div>
            </div>
            <div class="tw-flex tw-flex-col tw-space-y-3">
                <div class="tw-font-bold">Tỉnh/ thành phố:</div>
                <div>
                    <app-filter-table
                        [disabled]="!reportForm.get('contractId').value"
                        field="name"
                        rsql="true"
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            param: 'name',
                            rsql: false,
                            body: {
                                contractId: reportForm.get('contractId').value,
                            },
                            url: '/smart-qc/api/combobox/area',
                        }"
                        [initValue]="reportForm.get('areaId').value"
                        (onChange)="selectArea($event)"
                        placeholder="Tỉnh/ thành phố"
                        #filerArea
                    ></app-filter-table>
                </div>
            </div>
            <div class="tw-flex tw-flex-col tw-space-y-3">
                <div class="tw-font-bold">Quận/ huyện:</div>
                <div>
                    <app-filter-table
                        [disabled]="!reportForm.get('areaId').value"
                        field="name"
                        rsql="true"
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            param: 'name',
                            rsql: false,
                            body: {
                                contractId: reportForm.get('contractId').value,
                                areaId: reportForm.get('areaId').value,
                            },
                            url: '/smart-qc/api/combobox/district',
                        }"
                        [initValue]="reportForm.get('districtId').value"
                        (onChange)="selectDistrict($event)"
                        placeholder="Quận/ huyện"
                        #filerArea
                    ></app-filter-table>
                </div>
            </div>
            <div class="tw-flex tw-flex-col tw-space-y-3">
                <div class="tw-font-bold">Ngày/ tháng:</div>
                <div>
                    <p-calendar
                        formControlName="date"
                        [showIcon]="true"
                        [showOnFocus]="false"
                        dateFormat="dd/mm/yy"
                        inputId="buttondisplay"
                    />
                </div>
            </div>
        </div>
    </form>
    <div *ngIf="showReport" class="tw-bg-white tw-p-5 tw-rounded-xl tw-mb-5">
        <div class="tw-text-center tw-text-xl tw-font-bold tw-mb-5">Tiến độ hoàn thành dự án ngày {{ reportDate }}</div>
        <div>
            <p-table
                class="custom-table"
                [columns]="data?.header"
                [value]="data?.infoResList"
                [tableStyle]="{ 'min-width': '50rem' }"
            >
                <ng-template pTemplate="header" let-columns>
                    <tr>
                        <th class="tw-text-center" rowspan="2">STT</th>
                        <th class="tw-text-center" rowspan="2">
                            {{ isDisplayDistrict ? 'Quận/ huyện' : 'Tỉnh/ thành phố' }}
                        </th>
                        <th class="tw-text-center" rowspan="2">Tổng số trạm</th>
                        <th class="tw-text-center" colspan="2" *ngFor="let col of columns">
                            {{ col }}
                        </th>
                    </tr>
                    <tr>
                        <ng-container *ngFor="let col of columns">
                            <th class="tw-text-center" colspan="1">Số trạm hoàn thành</th>
                            <th class="tw-text-center" colspan="1">Tỉ lệ hoàn thành</th>
                        </ng-container>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-rowData let-i="rowIndex">
                    <tr>
                        <td class="tw-text-center">{{ i + 1 }}</td>
                        <!-- Cột STT -->
                        <td class="tw-text-center">{{ rowData.areaName }}</td>
                        <td class="tw-text-center">{{ rowData.totalStation }}</td>
                        <ng-container *ngFor="let col of data?.header">
                            <td class="tw-text-center">{{ getActionInfo(rowData, col, 'completeQuantity') }}</td>
                            <td class="tw-text-center">{{ getActionInfo(rowData, col, 'rateComplete') }}</td>
                        </ng-container>
                    </tr>
                </ng-template>
            </p-table>
        </div>
    </div>
</div>

<p-dialog header="Xuất dữ liệu" [modal]="true" [(visible)]="isOpenModalExport" [style]="{ width: '45rem' }">
    <a [href]="downloadExportFileLink">Bấm vào đây để tải xuống</a>
    <div class="flex justify-content-end gap-2">
        <p-button label="Đóng" severity="secondary" (click)="isOpenModalExport = false" />
    </div>
</p-dialog>
