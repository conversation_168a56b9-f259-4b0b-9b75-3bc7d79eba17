import { ElementRef, Input, OnInit } from '@angular/core';
import { Component } from '@angular/core';
import { LayoutService, SideBarState } from '../../service/app.layout.service';
import { Module } from '../../../models/constant';
import { AppMenuitemComponent } from './app.menuitem.component';
import { CommonModule } from '@angular/common';

export interface SideBarItem {
    label: string;
    icon?: string;
    routerLink?: string;
    authorize?: string[];
    items?: SideBarItem[];
    disabled?: boolean;
}

@Component({
    selector: 'app-sidebar',
    templateUrl: './sidebar.menu.component.html',
    styleUrls: ['./side-bar.style.scss'],
    standalone: true,
    imports: [AppMenuitemComponent, CommonModule],
})
export class AppSidebarMenuComponent implements OnInit {
    @Input() toggleSideBar: () => void;
    sidebar: SideBarState;
    module: Module;
    constructor(
        public layoutService: LayoutService,
        public el: ElementRef,
    ) {}

    ngOnInit() {
        this.layoutService.sideBarState.subscribe((data) => {
            this.sidebar = data;
        });
        this.layoutService.module.subscribe((data) => {
            this.module = data;
        });
    }

    onButtonClick(event: Event) {
        event.stopPropagation();
        this.layoutService.changeSideBarState();
    }
}
