import { Component, Input, OnInit, SimpleChanges, OnChanges, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';

import { PanelModule } from 'primeng/panel';
import { ButtonModule } from 'primeng/button';
import { ProductFileService } from 'src/app/services/pms/product-file/product-file.service';
import { ActivatedRoute, Router } from '@angular/router';
import { ProductDetail, ProductDocumentInput, ColumnTableSection, ProductRecordVersion } from 'src/app/models/interface/pms';

import { ConfirmationService } from 'primeng/api';
import { AlertService } from 'src/app/shared/services/alert.service';
import { EventBusService } from 'src/app/services/eventBus.service';
import { finalize, Subscription, take } from 'rxjs';
import { environment } from 'src/environments/environment';
import { EditTableSectionComponent } from 'src/app/modules/pms/components/edit-table-section/edit-table-section.component';

interface SectionConfig {
    category: string;
    title: string;
    columns: Array<ColumnTableSection>;
    rows: any[];
}
@Component({
    selector: 'app-design-profile',
    standalone: true,
    imports: [CommonModule, EditTableSectionComponent, PanelModule, ButtonModule],
    templateUrl: './design-profile.component.html',
    styleUrls: ['./design-profile.component.scss'],
    providers: [ProductFileService],
})
export class DesignProfileComponent implements OnInit, OnChanges, OnDestroy {
    // Danh sách các section, sẽ render lần lượt và đánh số 1,2,3...
    @Input() isCheckNote: boolean = false;
    @Input() data!: any;
    @Input() version!: ProductRecordVersion;
    @Input() currentProduct!: ProductDetail;
    @Input() mode!: string;
    lastUpdated: string = '';
    isEditMode: boolean = false;
    isChecking = false;
    isSaving = false;
    canCheckRdBom = false;
    compareResultUrl: string | null = null;
    reloadTrigger = 0;
    private requestSub!: Subscription;
    public STORAGE_BASE_URL = environment.STORAGE_BASE_URL;
    sections: SectionConfig[] = [
        {
            category: '0',
            title: 'Thông tin sản phẩm',
            columns: [
                { header: 'Tài liệu', field: 'document', type: 'text' },
                { header: 'File', field: 'file', type: 'file' },
                { header: 'Tên tài liệu', field: 'name', type: 'readonly' },
                { header: 'MD5', field: 'md5', type: 'readonly' },
                { header: 'Build time', field: 'buildtime', type: 'readonly' },
                { header: 'Version', field: 'version', type: 'readonly' },
            ],
            rows: [
                // {
                //     document: '',
                //     file: null,
                //     name: '',
                //     md5: '',
                //     buildtime: '',
                //     version: '',
                //     isDefault: 1,
                //     fileName: '',
                //     documentType: 1,
                // },
                // {
                //     document: '',
                //     file: null,
                //     name: '',
                //     md5: '',
                //     buildtime: '',
                //     version: '',
                //     isDefault: 1,
                //     fileName: '',
                //     documentType: 1,
                // },
                // {
                //     document: '',
                //     file: null,
                //     name: '',
                //     md5: '',
                //     buildtime: '',
                //     version: '',
                //     isDefault: 1,
                //     fileName: '',
                //     documentType: 1,
                // },
            ],
        },
        {
            category: '1',
            title: 'RD BOM',
            columns: [
                { header: 'Tài liệu', field: 'document', type: 'text', minWidth: '250px' },
                {
                    header: 'Loại RD BOM',
                    field: 'rdBomType',
                    type: 'select-one',
                    minLength: 2, // chỉ gọi API khi nhập >= 2 ký tự
                    options: [], // để parent fill
                    totalRecords: 0, // để parent fill
                    optionLabel: 'name',
                    optionValue: 'value',
                    placeholder: 'Gõ để tìm RD BOM...',
                    // quan trọng: bind đúng this để load query
                    lazyLoadFn: (q: string) => this.loadRdBomOptions(q),
                    minWidth: '250px',
                },
                { header: 'File', field: 'fileName', type: 'readonly', minWidth: '100px' },
                { header: 'Version', field: 'version', type: 'readonly', minWidth: '100px' },
                // { header: 'Trạng thái', field: 'status', type: 'readonly', minWidth: '100px' },
            ],
            rows: [
                // { document: '', file: null, version: '', documentType: 2 },
                // { document: '', file: null, version: '', documentType: 2 },
            ],
        },
        {
            category: '2',
            title: 'Thiết kế HW',
            columns: [
                { header: 'Tài liệu', field: 'document', type: 'text' },
                { header: 'File', field: 'file', type: 'file' },
                { header: 'Tên tài liệu', field: 'name', type: 'readonly' },
                { header: 'MD5', field: 'md5', type: 'readonly' },
                { header: 'Build time', field: 'buildtime', type: 'readonly' },
                { header: 'Version', field: 'version', type: 'readonly' },
            ],
            rows: [],
        },
        {
            category: '3',
            title: 'Thiết kế ID/MD',
            columns: [
                { header: 'Tài liệu', field: 'document', type: 'text' },
                { header: 'File', field: 'file', type: 'file' },
                { header: 'Tên tài liệu', field: 'name', type: 'readonly' },
                { header: 'MD5', field: 'md5', type: 'readonly' },
                { header: 'Build time', field: 'buildtime', type: 'readonly' },
                { header: 'Version', field: 'version', type: 'readonly' },
            ],
            rows: [],
        },
        {
            category: '4',
            title: 'Thiết kế Accessories và Packaging',
            columns: [
                { header: 'Tài liệu', field: 'document', type: 'text' },
                { header: 'File', field: 'file', type: 'file' },
                { header: 'Tên tài liệu', field: 'name', type: 'readonly' },
                { header: 'MD5', field: 'md5', type: 'readonly' },
                { header: 'Build time', field: 'buildtime', type: 'readonly' },
                { header: 'Version', field: 'version', type: 'readonly' },
            ],
            rows: [],
        },
        {
            category: '5',
            title: 'Bootloader/ Firmware Sản phẩm',
            columns: [
                { header: 'Tài liệu', field: 'document', type: 'text' },
                { header: 'File', field: 'file', type: 'file' },
                { header: 'Tên tài liệu', field: 'name', type: 'readonly' },
                { header: 'MD5', field: 'md5', type: 'readonly' },
                { header: 'Build time', field: 'buildtime', type: 'readonly' },
                { header: 'Version', field: 'version', type: 'readonly' },
            ],
            rows: [],
        },
    ];

    constructor(
        private productFileService: ProductFileService,
        private confirmationService: ConfirmationService,
        private alertService: AlertService,
        private router: Router,
        private route: ActivatedRoute,
        private bus: EventBusService,
    ) {}

    ngOnInit(): void {
        this.buildSections();
        // Xác định xem segment đầu tiên của path có phải 'edit' không
        const firstSegment = this.route.snapshot.url[0]?.path;
        this.isEditMode = firstSegment === 'edit';
        this.requestSub = this.bus
            .on<void>('REQUEST_PAYLOAD_ALL')
            .pipe(take(1)) // chỉ xử lý 1 lần rồi tự huỷ
            .subscribe(() => {
                const payload = this.handleSave(); // trả về mảng payload
                this.bus.emit('RESPONSE_PAYLOAD_DESIGN', payload);
            });
    }

    computeCanCheckRdBom(): void {
        const rdbom = this.sections[1].rows;
        const hw = this.sections[2].rows;
        this.canCheckRdBom = !!(rdbom?.[0]?.filePath && hw?.[3]?.md5);
    }
    ngOnChanges(changes: SimpleChanges) {
        if (changes['currentProduct'] && changes['currentProduct'].currentValue) {
            // mỗi khi parent truyền vào giá trị mới

            if (this.currentProduct.productVersions && this.currentProduct.productVersions.length > 0) {
                this.isCheckNote = true;
            } else {
                this.isCheckNote = false;
            }
        }
        if (changes['data'] && this.data) {
            this.updateRows();
        }
    }

    private updateRows() {
        this.sections.forEach((section) => {
            const docs = this.data[section.category] || [];
            // if (docs.length > 0 && docs[0].updated) {
            //     this.lastUpdated = this.formatTimestamp(docs[0].updated);
            // }
            if (section.rows.length === 0) {
                section.rows = docs.map((doc) => ({
                    id: doc.id,
                    document: doc.description || '',
                    file: doc.filePath || null,
                    name: doc.fileName ? doc.fileName.split('_')[1] : '',
                    md5: doc.md5 || '',
                    buildtime: doc.buildTime || '',
                    version: doc.versionName || '',
                    // status: this.getStatusText(doc.status),
                    isDefault: doc.isDefault || 0,
                    fileName: doc.fileName || '',
                    note: doc.note || '',
                    filePath: doc?.filePath || '',
                }));
            } else {
                const newRows = docs.map((doc, index) => {
                    const oldRow = section.rows[index];

                    return {
                        // Giữ id cũ nếu có, nếu không thì null
                        id: oldRow ? oldRow.id : null,
                        document: doc.description || '',
                        file: doc.filePath || null,
                        name: doc.fileName ? doc.fileName.split('_')[1] : doc.name || '',
                        md5: doc.md5 || '',
                        buildtime: doc.buildTime || '',
                        version: doc.versionName || '',
                        // status: doc.status || '',
                        isDefault: doc.isDefault || 0,
                        fileName: doc.fileName || '',
                        note: doc.note || '',
                        filePath: doc?.filePath || '',
                    };
                });

                // Gán lại section.rows
                section.rows = newRows;
            }
        });
        this.reloadTrigger++;

        this.computeCanCheckRdBom();
    }
    private buildSections(): void {
        if (!this.isCheckNote) {
            // Không cần cột note → giữ nguyên
            // this.sections = [...this.sections];
            return;
        }

        // Khi cần thêm cột Note
        this.sections = this.sections.map((section) => ({
            ...section,
            columns: [...section.columns, { header: 'Ghi chú', field: 'note', type: 'text', minWidth: '250px' }],
            // Giữ nguyên rows (không cần clone!)
        }));
    }
    loadRdBomOptions(query: string) {
        // tìm đúng column trong sections
        const sec = this.sections.find((s) => s.category === '1');
        if (!sec) return;
        const col = sec.columns.find((c) => c.field === 'rdBomType') as any;
        if (!col) return;

        col.loading = true;
        this.productFileService
            .fetchListRdBOM({
                page: 0,
                size: 100,
                unpaged: false,
                name: query, // dùng query làm filter
            })
            .subscribe({
                next: (res) => {
                    // map API về [{label, value}]
                    col.options = res.map((item: any) => ({
                        name: item.name,
                        value: item.id,
                        version: item.version,
                        // status: this.getStatusText(item.status),
                    }));
                    // col.totalRecords = res.totalElements;
                },
                error: () => {
                    col.options = [];
                },
                complete: () => {
                    col.loading = false;
                },
            });
    }
    private getStatusText(status: number): string {
        switch (status) {
            case 0:
                return 'Nháp';
            case 1:
                return 'Send to Approve';
            case 2:
                return 'Approved';
            default:
                return '';
        }
    }

    // onSaveSectionRow(section: SectionConfig, e: { index: number; row: any; done: (updated: any) => void }) {
    //     const { index, row, done } = e;
    //     console.log('check save', row);
    //     console.log('check save', section);
    //     console.log('check this.version', this.version);
    //     // 1) Chuẩn bị payload cho tạo mới
    //     const createDto: CreateProductDocumentDto = {
    //         versionId: this.version.id,
    //         description: row.document,
    //         type: 1, // tab hồ sơ thiết kế
    //         category: +section.category,
    //         documentType: 1, // mỗi bảng trong 1 tab
    //         fileName: row.file || '',
    //         filePath: row.filePath || '',
    //         md5: row.md5 || '',
    //         buildTime: Number(row.buildtime) || '',
    //         versionName: row.version || '',
    //         note: row.note || '',
    //     };

    //     // 2) Nếu đã có row.id → build Update DTO
    //     let obs$;
    //     if (row.id) {
    //         const updateDto: UpdateProductDocumentDto = {
    //             description: createDto.description,
    //             fileName: createDto.fileName,
    //             filePath: createDto.filePath,
    //             md5: createDto.md5,
    //             buildTime: createDto.buildTime,
    //             versionName: createDto.versionName,
    //             note: createDto.note,
    //         };
    //         obs$ = this.productFileService.updateDocumentRow(row.id, updateDto);
    //     } else {
    //         // 3) Ngược lại call create
    //         obs$ = this.productFileService.createDocumentRow(createDto);
    //     }

    //     // 4) Subscribe chung
    //     obs$.subscribe({
    //         next: (updated) => {
    //             this.alertService.success('Thành công', 'Lưu bản ghi thành công');

    //             // cập nhật mảng JS
    //             section.rows[index] = { ...section.rows[index], ...updated };

    //             // callback để con patch FormGroup
    //             done(updated);
    //         },
    //         error: (err) => {
    //             this.alertService.error('Lỗi', 'Lưu bản ghi thất bại');
    //             console.error('save error:', err);
    //         },
    //     });
    // }

    onDeleteRow(e: { index: number; row: any; done: () => void }) {
        const { index, row, done } = e;
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.productFileService.delete(row.id).subscribe({
                    next: () => {
                        this.alertService.success('Thành công', 'Xóa bản ghi thành công');
                        done();
                        // reload dữ liệu
                    },
                    error: (err) => {
                        this.alertService.error('Lỗi', 'Xóa bản ghi thất bại');
                        console.error('Delete error:', err);
                    },
                });
            },
        });
    }
    buildPayload(): ProductDocumentInput[] {
        return this.sections.flatMap((section) =>
            section.rows.map((row) => {
                // Base payload chung
                const base: Partial<ProductDocumentInput> = {
                    id: row.id || 0,
                    versionId: this.version?.id || 0,
                    description: row.document || '',
                    type: 1,
                    category: +section.category,
                    md5: row.md5 || '',
                    buildTime: row.buildtime || 0,
                    note: row.note || '',
                    isDefault: row.isDefault || 0,
                    documentType: 1,
                };

                // Nếu là section category=1 và có rdBomType thì ưu tiên lấy từ nó
                if (section.category === '1' && row.rdBomType) {
                    return {
                        ...base,
                        fileName: row.rdBomType.name || '',
                        filePath: row.rdBomType.value || '',
                        versionName: row.rdBomType.version || null,
                        // status: row.status || '',
                    } as ProductDocumentInput;
                }

                // Mặc định lấy từ row.fileName, row.filePath, row.version
                return {
                    ...base,
                    fileName: row.fileName || '',
                    filePath: row.filePath || '',
                    versionName: row.version || null,
                    // status: row.status || '',
                } as ProductDocumentInput;
            }),
        );
    }

    handleSave() {
        this.isSaving = true;
        const payload = this.buildPayload();
        return payload;
        // this.productFileService
        //     .createUpdateProductDocument(this.currentProduct.id, payload)
        //     .pipe(finalize(() => (this.isSaving = false)))
        //     .subscribe({
        //         next: (versionId) => {
        //             this.alertService.success('Thành công', 'Lưu hồ sơ thiết kế thành công');
        //             // Kiểm tra xem URL đã chứa segment 'edit' chưa
        //             const urlSegments = this.route.snapshot.url.map((s) => s.path);
        //             const alreadyEdit = urlSegments[0] === 'edit';

        //             if (!alreadyEdit) {
        //                 this.router.navigate(['/pms/product-file/edit', this.currentProduct.id, versionId], {
        //                     state: {
        //                         currentProduct: this.currentProduct,
        //                         version: this.version,
        //                         mode: 'edit',
        //                     },
        //                     replaceUrl: true, // tránh tạo thêm history entry
        //                 });
        //             } else {
        //                 // Đã ở edit → chỉ refresh các tab
        //                 this.bus.emit('refreshTabs');
        //             }
        //         },
        //     });
    }

    handleDeleteVersion(v: ProductRecordVersion): void {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.productFileService.deleteVersion(this.version.id).subscribe({
                    next: () => {
                        this.alertService.success('Thành công', 'Xóa thành công');
                        this.router.navigate(['/pms/product-file'], {
                            replaceUrl: true, // tránh tạo thêm history entry
                        });
                    },
                    error: (err) => {
                        this.alertService.error('Lỗi', 'Xóa thất bại');
                        console.error('Delete error:', err);
                    },
                });
            },
        });
    }
    onSectionRowsChange(section: SectionConfig, newRows: any[]) {
        section.rows = newRows;
    }
    onDownloadResult() {
        if (!this.compareResultUrl) return;

        // nếu muốn mở tab mới

        window.open(`${this.STORAGE_BASE_URL}${this.compareResultUrl}`, '_blank');
        // hoặc nếu bạn muốn gán href trực tiếp thì template bên dưới sẽ handle
    }
    checkRdBom() {
        this.isChecking = true;
        this.productFileService
            .getCheckRdBom(this.version.id)
            .pipe(finalize(() => (this.isChecking = false)))
            .subscribe({
                next: (res) => (this.compareResultUrl = res.url),
                error: (err) => console.error('❌ error:', err),
            });
    }
    ngOnDestroy(): void {
        this.requestSub.unsubscribe();
    }
}
