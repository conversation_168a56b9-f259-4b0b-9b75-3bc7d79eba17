import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import {Ng<PERSON><PERSON>, <PERSON><PERSON>orO<PERSON>, Ng<PERSON>ty<PERSON>} from "@angular/common";
import {StateWizard} from "../../../models/interface";

@Component({
    selector: 'app-wizard-double-row',
    templateUrl: './wizard-double-row.component.html',
    styleUrls: ['./wizard-double-row.component.scss'],
    imports: [
        NgForOf,
        NgClass,
        NgStyle
    ],
    standalone: true
})
export class WizardDoubleRowComponent implements OnInit {
    // Declare
    @Input() minWidth: string = '150px'; // Giá trị mặc định nếu không truyền vào
    @Input() versionStates: StateWizard[] = []; // Danh sách các trạng thái
    @Input() stateIdSelected: number; // ID trạng thái được chọn
    @Output() onSelectState = new EventEmitter<number>(); // Sự kiện khi click

    @Input() isActivePreviousState?: boolean = false;
    @Input() valueField?: string = "id";
    // End declare

    selectState(stateId: number) {
        if (this.onSelectState) {
            this.onSelectState.emit(stateId);
        }
    }

    ngOnInit() {
        // console.log(this.versionStates);
    }
}
