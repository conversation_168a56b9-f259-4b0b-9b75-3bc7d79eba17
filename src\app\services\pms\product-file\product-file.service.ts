// src/app/modules/pms/shared/services/product-profile.service.ts
import { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { catchError, throwError, Observable } from 'rxjs';
import {
    ApprovalRequest,
    CreateProductDocumentDto,
    FilterProductDocumentRequest,
    ProductDocumentInput,
    SendApprovalRequest,
} from 'src/app/models/interface/pms';
import { environment } from 'src/environments/environment';

@Injectable()
export class ProductFileService {
    #http = inject(HttpClient);
    readonly STORAGE_BASE_URL = environment.STORAGE_BASE_URL;
    /** Phân trang + lọc nâng cao */

    /** Lấy toàn bộ profile (không phân trang) */
    getAllProduct(params: { page?: number; size?: number; unpaged?: boolean; lineId?: number; modelId?: number; customerId?: number }): any {
        const cleanParams: Record<string, string> = {};
        for (const [key, val] of Object.entries(params)) {
            if (val != null) {
                cleanParams[key] = String(val);
            }
        }

        const httpParams = new HttpParams({ fromObject: cleanParams });
        return this.#http.get<unknown[]>('/pr/api/product', { params: httpParams });
    }
    getDetailProduct(id: number): Observable<any> {
        return this.#http.get<any>(`/pr/api/product/${id}`);
    }
    deleteVersion(versionId: number): Observable<any> {
        return this.#http.delete<any>(`/pr/api/product-version/${versionId}`);
    }
    getProductVersionById(id: number): Observable<any> {
        return this.#http.get<any>(`/pr/api/product-version/${id}`);
    }
    getVersionHistory(productId: number): Observable<any[]> {
        return this.#http.get<any[]>(`/pr/api/history/product-version/${productId}`);
    }
    exportProductVersionHistory(query: { productId: number; historyId: number }): Observable<string> {
        return this.#http.get(`/pr/api/history/product-version/export`, {
            params: new HttpParams({ fromObject: query }),
            responseType: 'text',
        });
    }

    getProductDoc(filters: { versionId?: number; types?: number }): Observable<any> {
        let params = new HttpParams();

        if (filters.versionId != null) {
            params = params.set('versionId', filters.versionId.toString());
        }
        if (filters.types != null) {
            params = params.set('types', filters.types.toString());
        }

        return this.#http.get<any>(`/pr/api/product-document`, { params });
    }
    createProfile(productId: number): Observable<any> {
        const formData = new FormData();
        formData.append('productId', productId.toString());
        // Nếu API không cần body, bạn vẫn phải truyền null hoặc {} tuỳ backend
        return this.#http.post<any>('/pr/api/product-version', formData).pipe(
            // Bắt lỗi ở service, log và re-throw
            catchError((err) => {
                console.error('[ProductFileService] createProfile failed', err);
                throw err;
            }),
        );
    }
    getChangeHistory(documentId: number): Observable<any> {
        return this.#http.get<any>(`/pr/api/history/product-document/${documentId}`);
    }
    getInfoApprovalVersion(id: number): Observable<any> {
        return this.#http.get<any>(`/pr/api/approval/product-version/${id}`);
    }
    createDocumentRow(payload: CreateProductDocumentDto): Observable<CreateProductDocumentDto> {
        return this.#http.post<CreateProductDocumentDto>('/pr/api/product-document', payload);
    }
    // updateDocumentRow(documentId: number, payload: UpdateProductDocumentDto): Observable<UpdateProductDocumentDto> {
    //     return this.#http.put<UpdateProductDocumentDto>(`/pr/api/product-document/${documentId}`, payload);
    // }
    delete(id: number): Observable<void> {
        return this.#http.delete<void>(`/pr/api/product-document/${id}`);
    }
    sendApprovalRequest(payload: SendApprovalRequest): Observable<any> {
        return this.#http.post<any>(`/pr/api/approval/product-version`, payload);
    }
    confirmApprovalRequest(payload: ApprovalRequest): Observable<any> {
        return this.#http.post<any>(`/pr/api/approval/product-version/confirm`, payload);
    }

    filterByVersion(req: FilterProductDocumentRequest): Observable<any> {
        // Chuyển req thành query params
        const params = new HttpParams().set('versionId', req.versionId.toString()).set('types', req.types.toString());

        return this.#http.get<any>(`/pr/api/product-document`, { params });
    }
    cloneVersionId(versionId: number, options: number): Observable<any> {
        return this.#http.post<any>(`/pr/api/product-version/${versionId}/duplicate`, { options });
    }
    createUpdateProductDocument(
        productId: number,
        payload: ProductDocumentInput[],
        lifecycleStage: number,
        versionRef?: number,
        timeCheck?: number,
        url?: string,
    ) {
        return this.#http.post<any>(`/pr/api/product-document/batch`, {
            productId,
            documentsJson: payload,
            lifecycleStage: lifecycleStage,
            // chỉ thêm versionRef nếu khác null/undefined
            ...(versionRef != null && { versionRef }),
            // chỉ thêm checkTime nếu khác null/undefined
            ...(timeCheck != null && { timeCheck }),
            // chỉ thêm url nếu có giá trị non-empty
            ...(url ? { url } : {}),
        });
    }
    transferVersion(productVersionId: number, body: any) {
        return this.#http.post<any>(`/pr/api/product-version/${productVersionId}/transfer`, body);
    }
    nextPhase(productVersionId: number, lifecycleStage: number) {
        return this.#http.post<any>(`/pr/api/product-version/${productVersionId}/next-phase`, {
            lifecycleStage,
        });
    }
    getCheckRdBom(id: number, bomId: number, cadPath: string, cadName: string): Observable<any> {
        const params = new HttpParams().set('bomId', bomId.toString()).set('cadPath', cadPath).set('cadName', cadName);

        return this.#http.get<any>(`/pr/api/product-document/compare/${id}`, {
            params,
            responseType: 'json', // nếu trả về JSON; nếu là file Excel thì đổi thành 'blob'
        });
    }

    fetchListRdBOM(query: { page: number; size: number; unpaged: boolean; name?: string }): Observable<any> {
        let params = new HttpParams().set('page', query.page.toString()).set('size', query.size.toString()).set('unpaged', query.unpaged.toString());
        if (query.name) {
            params = params.set('name', query.name);
        }
        return this.#http.get<any>('/pr/api/rdbom/list', { params });
    }
    fetchRdBomById(id: number): Observable<any> {
        return this.#http.get<any>('/pr/api/rdbom/list', {
            params: { id: id },
        });
    }
    /** Xử lý lỗi chung */
    private handleError(error: HttpErrorResponse) {
        console.error('ProductProfile API error', error);
        return throwError(() => error);
    }
    exportFileProdVer(versionId: number): Observable<string> {
        return this.#http.get(`/pr/api/product-version/${versionId}/export`, { responseType: 'text' });
    }
    getFileBlob(relPath: string) {
        // 2) Lấy actual file từ storage dưới dạng Blob
        const url = `${this.STORAGE_BASE_URL}${relPath}`;
        return this.#http.get(url, { responseType: 'blob' as 'json' });
    }
}
