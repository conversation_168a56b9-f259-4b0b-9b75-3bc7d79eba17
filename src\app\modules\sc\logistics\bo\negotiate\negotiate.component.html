<app-form *ngIf="formGroup" #form [formGroup]="formGroup" layout="vertical" (onSubmit)="confirmApprove($event, true, 'form')">
    <p-panel header="Thông tin lô hàng" [toggleable]="true">
        <p-table styleClass="p-datatable-gridlines" [value]="[bo]" [scrollable]="true">
            <ng-template pTemplate="header">
                <tr>
                    <th class="tw-whitespace-nowrap">Ng<PERSON><PERSON> y<PERSON><PERSON> c<PERSON><PERSON> - s<PERSON> thứ tự</th>
                    <th>Người gửi hàng</th>
                    <th>Người nhận hàng</th>
                    <th style="min-width: 200px">Tên hàng</th>
                    <th style="min-width: 200px">Điều kiện giao hàng</th>
                    <th style="min-width: 250px">Khối lượng dự kiến(KG) - CW</th>
                    <th style="min-width: 10rem">Th<PERSON> tích dự kiến(CBM)</th>
                    <th style="min-width: 10rem"><PERSON><PERSON><PERSON> điểm giao hàng</th>
                    <th style="min-width: 10rem">Cảng xuất</th>
                    <th style="min-width: 10rem">Cảng nhập</th>
                    <th style="min-width: 10rem">Địa điểm nhận hàng cuối cùng</th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                <tr formGroupName="shipmentInfo">
                    <td>
                        <input type="text" pInputText formControlName="requestDateOrder" />
                    </td>
                    <td>
                        <input type="text" pInputText formControlName="sender" />
                    </td>
                    <td>
                        <input type="text" pInputText formControlName="receiver" />
                    </td>
                    <td>
                        {{ formGroup.get('shipmentInfo')?.get('itemName')?.value }}
                    </td>
                    <td>
                        {{ formGroup.get('shipmentInfo')?.get('deliveryCondition')?.value }}
                    </td>
                    <td>
                        {{ formGroup.get('shipmentInfo')?.get('estimatedWeightCw')?.value | number }}
                    </td>
                    <td>
                        <input type="text" pInputText formControlName="estimatedVolumeCbm" />
                    </td>
                    <td>
                        <input type="text" pInputText formControlName="deliveryLocation" />
                    </td>
                    <td>
                        <input type="text" pInputText formControlName="exportPort" />
                    </td>
                    <td>
                        <input type="text" pInputText formControlName="importPort" />
                    </td>
                    <td>
                        <input type="text" pInputText formControlName="finalDestination" />
                    </td>
                </tr>
            </ng-template>
        </p-table>
        <br />
        <p-table styleClass="p-datatable-gridlines" [value]="[bo]" [scrollable]="true">
            <ng-template pTemplate="header">
                <tr>
                    <th style="min-width: 10rem">Số PO/HĐ/DA</th>
                    <th style="min-width: 10rem">Mã kế toán</th>
                    <th style="min-width: 10rem">Số thứ tự shipment theo PO/HĐ/DA</th>
                    <th style="min-width: 10rem">Giá trị các lô hàng đã vận chuyển (USD)</th>
                    <th style="min-width: 10rem">Giá trị lô hàng đang đề xuất phương án vận chuyển (USD)</th>
                    <th style="min-width: 10rem">Giá trị các lô hàng chưa gửi yêu cầu (USD)</th>
                    <th style="min-width: 10rem">Tổng giá trị PO/HĐ/DA (USD)</th>
                    <th style="min-width: 10rem">Tổng chi phí vận chuyển dự kiến của PO/HĐ/DA (VNĐ)</th>
                    <th style="min-width: 10rem">Tỷ lệ chi phí vận chuyển và giá trị của PO/HĐ/DA</th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                <tr formGroupName="shipmentInfo">
                    <td>
                        {{ formGroup.get('shipmentInfo')?.get('boCode')?.value }}
                    </td>
                    <td>
                        {{ formGroup.get('shipmentInfo')?.get('accountingCode')?.value }}
                    </td>
                    <td>
                        {{ formGroup.get('shipmentInfo')?.get('indexShipment')?.value | number }}
                    </td>
                    <td>
                        <input type="text" pInputText formControlName="shippedValue" />
                    </td>
                    <td>
                        {{ formGroup.get('shipmentInfo')?.get('currentShipmentValue')?.value | number }}
                    </td>
                    <td>
                        <app-inputNumber mode="decimal" maxLength="20" formControlName="pendingShipmentValue"> </app-inputNumber>
                    </td>
                    <td>
                        <app-inputNumber mode="decimal" maxLength="20" formControlName="totalPoValue"> </app-inputNumber>
                    </td>
                    <td>
                        <app-inputNumber mode="decimal" maxLength="20" formControlName="totalEstimatedTransportCost"> </app-inputNumber>
                    </td>
                    <td>
                        <app-inputNumber mode="decimal" maxLength="20" formControlName="transportCostRatio"> </app-inputNumber>
                    </td>
                </tr>
            </ng-template>
        </p-table>
    </p-panel>
    <br />

    <p-panel header="Cước phí vận chuyển dự kiến" [toggleable]="true">
        <div class="tw-flex tw-justify-end tw-mb-4 tw-mx-4 tw-gap-4">
            <p-button label="Thêm chi phí" icon="pi pi-plus" [outlined]="true" [size]="'small'" (click)="openAddCostDialog()"></p-button>
            <p-button icon="pi pi-filter" [outlined]="true" [size]="'small'" (click)="op.toggle($event)"></p-button>
        </div>
        <ng-container formArrayName="estimatedTransportCosts">
            <p-table styleClass="p-datatable-gridlines" [value]="estimatedTransportCosts.controls" [scrollable]="true">
                <ng-template pTemplate="header">
                    <tr>
                        <th class="tw-whitespace-nowrap p-th-sticky" style="left: 0px; min-width: 4rem">Lựa chọn</th>
                        <th class="tw-whitespace-nowrap p-th-sticky" style="left: 4rem; min-width: 10rem">NCC dịch vụ</th>
                        <th class="tw-whitespace-nowrap" class="p-th-sticky" style="left: 14rem; min-width: 15rem">
                            {{ bo?.type === 0 ? 'Phương thức vận chuyển' : 'Phương thức vận chuyển/ dịch vụ' }}
                        </th>
                        <th *ngFor="let col of columns.slice(3)" style="min-width: 15rem" [ngStyle]="{ display: col.hide ? 'none' : 'table-cell' }">
                            <p class="tw-flex tw-justify-between tw-items-center">
                                {{ col.header }}

                                <p-button
                                    *ngIf="col?.group === 'custom'"
                                    icon="pi pi-trash tw-float-right "
                                    [rounded]="true"
                                    [text]="true"
                                    severity="danger"
                                    size="small"
                                    (click)="deleteAdditionalFee(col.field)"
                                />
                            </p>
                        </th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                    <tr [formGroupName]="rowIndex">
                        <td class="p-td-sticky" style="left: 0px; min-width: 4rem">
                            <p-checkbox
                                formControlName="isChoice"
                                [binary]="true"
                                [inputId]="'id-checkobox-estimatedTransportCosts' + rowIndex"
                                (onChange)="changeCheckBox($event, rowIndex, 'estimatedTransportCosts')"
                            ></p-checkbox>
                        </td>
                        <td class="p-td-sticky" style="left: 4rem; min-width: 10rem">
                            {{ estimatedTransportCosts.controls[rowIndex].get('logisticShortName').value }}
                        </td>
                        <td class="p-td-sticky" style="left: 14rem; min-width: 15rem">
                            {{
                                mapTypeShippingMethod[estimatedTransportCosts.controls[rowIndex].get('shippingMethodId').value] +
                                    (estimatedTransportCosts.controls[rowIndex].get('roadNote').value
                                        ? '(' + estimatedTransportCosts.controls[rowIndex].get('roadNote').value + ')'
                                        : '')
                            }}
                        </td>
                        <td *ngFor="let col of columns.slice(3)" [ngStyle]="{ display: col.hide ? 'none' : 'table-cell' }">
                            <ng-container [ngSwitch]="col.field">
                                <ng-container *ngSwitchCase="'internationalRateUnitId'">
                                    <app-filter-table
                                        type="select-one"
                                        [configSelect]="{
                                            fieldValue: 'id',
                                            fieldLabel: 'name',
                                            rsql: true,
                                            url: '/sc/api/international-rate-unit/search',
                                            paramForm: 'id',
                                            body: {
                                                shippingMethodId: estimatedTransportCosts.controls[rowIndex].get('shippingMethodId').value,
                                            },
                                        }"
                                        formControlName="internationalRateUnitId"
                                    ></app-filter-table>
                                </ng-container>
                                <ng-container *ngSwitchCase="'mainTransportCost'">
                                    <app-inputNumber mode="decimal" maxLength="20" formControlName="{{ col.field }}" [allowNegative]="true"></app-inputNumber>
                                </ng-container>
                                <ng-container *ngSwitchCase="'totalEstimatedTransportCost'">
                                    <app-inputNumber
                                        mode="decimal"
                                        maxLength="20"
                                        formControlName="{{ col.field }}"
                                        (onInput)="findIndexLowestCost()"
                                    ></app-inputNumber>
                                </ng-container>
                                <ng-container *ngSwitchCase="'totalCostAfterSupplierDeduction'">
                                    <app-inputNumber
                                        mode="decimal"
                                        maxLength="20"
                                        formControlName="{{ col.field }}"
                                        (onInput)="findIndexLowestCost()"
                                    ></app-inputNumber>
                                </ng-container>
                                <ng-container *ngSwitchDefault>
                                    <app-inputNumber mode="decimal" maxLength="20" formControlName="{{ col.field }}"></app-inputNumber>
                                </ng-container>
                            </ng-container>
                        </td>
                    </tr>
                </ng-template>
            </p-table>
        </ng-container>
    </p-panel>
    <br />
    <p-panel header="Lịch trình dự kiến" [toggleable]="true">
        <ng-container formArrayName="estimatedSchedules">
            <p-table styleClass="p-datatable-gridlines" [value]="estimatedSchedules.controls" [scrollable]="true">
                <ng-template pTemplate="header">
                    <tr>
                        <th class="tw-whitespace-nowrap">Nhà cung cấp dịch vụ</th>
                        <th class="tw-whitespace-nowrap">Phương thức vận chuyển</th>
                        <th style="min-width: 15rem">Ngày hàng sẵn sàng tại địa điểm giao hàng</th>
                        <th style="min-width: 15rem">Ngày yêu cầu hàng về địa điểm nhận hàng cuối cùng</th>
                        <th style="min-width: 15rem">Ngày giao hàng tại cảng xuất</th>
                        <th style="min-width: 15rem">Ngày tàu chạy từ cảng xuất</th>
                        <th style="min-width: 15rem">Ngày tàu về cảng nhập</th>
                        <th style="min-width: 15rem">Thời gian khai nộp thuế, làm thủ tục hải quan và tại cảng nhập</th>
                        <th style="min-width: 15rem">Ngày giao hàng tại địa điểm cuối cùng</th>
                        <th style="min-width: 30rem">Ghi chú</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                    <tr [formGroupName]="rowIndex">
                        <td>
                            {{ estimatedSchedules.controls[rowIndex].get('logisticShortName').value }}
                        </td>
                        <td>
                            {{
                                mapTypeShippingMethod[estimatedSchedules.controls[rowIndex].get('shippingMethodId').value] +
                                    (estimatedSchedules.controls[rowIndex].get('roadNote').value
                                        ? '(' + estimatedSchedules.controls[rowIndex].get('roadNote').value + ')'
                                        : '')
                            }}
                        </td>
                        <td>
                            <p-calendar
                                [showButtonBar]="true"
                                placeholder="dd/MM/yyyy"
                                dateFormat="dd/mm/yy"
                                appendTo="body"
                                formControlName="readyDateCustom"
                            ></p-calendar>
                        </td>
                        <td>
                            <p-calendar
                                [showButtonBar]="true"
                                placeholder="dd/MM/yyyy"
                                dateFormat="dd/mm/yy"
                                appendTo="body"
                                formControlName="finalDeliveryRequestDateCustom"
                            ></p-calendar>
                        </td>
                        <td>
                            <p-calendar
                                [showButtonBar]="true"
                                placeholder="dd/MM/yyyy"
                                dateFormat="dd/mm/yy"
                                appendTo="body"
                                formControlName="exportPortDeliveryDateCustom"
                            ></p-calendar>
                        </td>
                        <td>
                            <p-calendar
                                [showButtonBar]="true"
                                placeholder="dd/MM/yyyy"
                                dateFormat="dd/mm/yy"
                                appendTo="body"
                                formControlName="departureDateCustom"
                            ></p-calendar>
                        </td>
                        <td>
                            <p-calendar
                                [showButtonBar]="true"
                                placeholder="dd/MM/yyyy"
                                dateFormat="dd/mm/yy"
                                appendTo="body"
                                formControlName="arrivalDateCustom"
                            ></p-calendar>
                        </td>
                        <td>
                            <input formControlName="clearanceTime" pInputText class="tw-w-full" />
                        </td>
                        <td>
                            <p-calendar
                                [showButtonBar]="true"
                                placeholder="dd/MM/yyyy"
                                dateFormat="dd/mm/yy"
                                appendTo="body"
                                formControlName="finalDeliveryDateCustom"
                            ></p-calendar>
                        </td>
                        <td>
                            <textarea cols="2" formControlName="note" pInputTextarea class="tw-w-full"></textarea>
                        </td>
                    </tr>
                </ng-template>
            </p-table>
        </ng-container>
    </p-panel>
    <br />
    <p-panel header="Phí bảo hiểm dự kiến" [toggleable]="true" *ngIf="isShowEstimatedInsurances">
        <ng-container formArrayName="estimatedInsurances">
            <p-table styleClass="p-datatable-gridlines" [value]="estimatedInsurances.controls" [scrollable]="true">
                <ng-template pTemplate="header">
                    <tr>
                        <th class="tw-whitespace-nowrap">Lựa chọn</th>
                        <th class="tw-whitespace-nowrap">Nhà cung cấp dịch vụ bảo hiểm</th>
                        <th class="tw-whitespace-nowrap">Phương thức vận chuyển</th>
                        <th style="min-width: 15rem">Tỷ lệ phí bảo hiểm</th>
                        <th style="min-width: 15rem">Tỷ giá VNĐ/USD</th>
                        <th style="min-width: 15rem">Tổng số tiền bảo hiểm (USD)</th>
                        <th style="min-width: 15rem">Phí bảo hiểm (VNĐ)</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                    <tr [formGroupName]="rowIndex">
                        <td>
                            <p-checkbox
                                formControlName="isChoice"
                                [binary]="true"
                                [inputId]="'id-checkobox-estimatedInsurances' + rowIndex"
                                (onChange)="changeCheckBox($event, rowIndex, 'estimatedInsurances')"
                            ></p-checkbox>
                        </td>
                        <td>
                            {{ estimatedInsurances.controls[rowIndex].get('logisticShortName').value }}
                        </td>
                        <td>
                            {{ mapTypeShippingMethod[estimatedInsurances.controls[rowIndex].get('shippingMethodId').value] }}
                        </td>
                        <td>
                            {{
                                estimatedInsurances.controls[rowIndex].get('insuranceRate').value
                                    ? estimatedInsurances.controls[rowIndex].get('insuranceRate').value + ' %'
                                    : ''
                            }}
                            <!--<div class="p-inputgroup">
                                <app-inputNumber mode="decimal" maxLength="20"   formControlName="insuranceRate"> </app-inputNumber>
                                <span class="p-inputgroup-addon">%</span>
                            </div>-->
                        </td>
                        <td>
                            <app-inputNumber mode="decimal" maxLength="20" formControlName="exchangeRate"> </app-inputNumber>
                        </td>
                        <td>
                            <app-inputNumber mode="decimal" maxLength="20" formControlName="totalInsuranceValue"> </app-inputNumber>
                        </td>
                        <td>
                            {{ estimatedInsurances.controls[rowIndex].get('insuranceFee').value | number }}
                        </td>
                    </tr>
                </ng-template>
            </p-table>
        </ng-container>
    </p-panel>
    <br />
    <app-form-item label="Ghi chú">
        <textarea rows="5" formControlName="noteNegotiate" pInputTextarea class="tw-w-full"></textarea>
    </app-form-item>
    <br />
    <div class="tw-flex tw-justify-end tw-gap-4">
        <app-popup
            header="Xuất chọn phương án vận chuyển và nhà cung cấp dịch vụ vận chuyển bảo hiểm, vận chuyển"
            label="Xuất excel"
            (onSubmit)="export($event)"
            typePopup="download"
        ></app-popup>
        <p-button label="Phê duyệt" severity="success" (click)="form.handleSubmit()" size="small"></p-button>
    </div>
    <br />
    <br />
</app-form>

<p-overlayPanel #op>
    <p-table [value]="columns" [selection]="columnChoose" (selectionChange)="setColumnSelection($event)" [scrollable]="true" scrollHeight="500px">
        <ng-template pTemplate="header">
            <tr>
                <th>
                    <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                </th>

                <th>Tất cả</th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rowData>
            <tr>
                <td style="font-size: 14px; padding: 8px 12px">
                    <p-tableCheckbox [value]="rowData" [hidden]="rowData.default" />
                </td>
                <td style="font-size: 14px; padding: 8px 12px">
                    {{ rowData['header'] }}
                </td>
            </tr>
        </ng-template>
    </p-table>
</p-overlayPanel>

<p-dialog header="Thêm chi phí mới" [(visible)]="showAddCostDialog" [style]="{ width: '400px' }">
    <form [formGroup]="addCostForm">
        <app-form-item label="Tên chi phí">
            <input pInputText formControlName="name" class="tw-w-full" />
        </app-form-item>
    </form>
    <ng-template pTemplate="footer">
        <p-button label="Hủy" severity="secondary" (click)="showAddCostDialog = false" [outlined]="true" size="small"></p-button>
        <p-button label="Thêm" severity="primary" (click)="addNewCost()" [disabled]="addCostForm.invalid" size="small"></p-button>
    </ng-template>
</p-dialog>
