import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { ApiResponse, GeneralEntity } from 'src/app/models/interface';
import { SupplierType } from '../../../models/interface/sc';

@Injectable({
    providedIn: 'root',
})
export class SupplierTypeService extends BaseService<SupplierType> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/supplier-type');
    }

    getFileCriteria(id: number) {
        return this.http.get<GeneralEntity>(`/sc/api/supplier-type/export-template-criteria?supplierTypeId=${id}`);
    }

    importCriteria(file: File, supplierTypeId: number) {
        const formData: FormData = new FormData();
        formData.append('file', file);
        formData.append('supplierTypeId', supplierTypeId.toString());

        return this.http.post<ApiResponse>('/sc/api/supplier-type/import-criteria', formData);
    }

    exportCriteria = (supplierTypeId: number, criteriaType: number) => {
        return this.http.get<GeneralEntity>(this.baseUrl + `/export?supplierTypeId=${supplierTypeId}&type=${criteriaType}`);
    };
}
