<div
    #combobox
    class="tw-relative tw-w-full tw-min-h-[40px] border-1 tw-border-gray-300 tw-rounded-md tw-bg-white tw-p-1 tw-flex tw-items-center tw-gap-2"
    [ngClass]="{
        'tw-bg-gray-100/30 tw-cursor-not-allowed': disabled,
        'tw-border-blue-500': showDropdown,
        'tw-cursor-pointer hover:tw-border-blue-500': !disabled,
    }"
    (click)="focusInput($event)"
>
    <!-- Hiển thị placeholder hoặc giá trị đã chọn -->
    <div class="tw-flex-1 tw-flex tw-flex-nowrap tw-gap-1 tw-h-full tw-overflow-x-auto" style="width: calc(100% - 5rem)">
        <ng-container *ngIf="objectValue.length === 0">
            <span class="tw-text-gray-400">{{ placeholder }}</span>
        </ng-container>
        <ng-container *ngIf="type === 'select-one' && objectValue.length">
            <span class="tw-text-gray-800">{{ getDisplayValue(objectValue[0]) }}</span>
        </ng-container>
        <ng-container *ngIf="type === 'select' && objectValue.length">
            <div
                *ngFor="let option of objectValue"
                class="tw-bg-blue-100 tw-text-blue-800 tw-rounded tw-p-2 tw-flex tw-items-center tw-gap-1 tw-whitespace-nowrap tw-cursor-default"
            >
                {{ getDisplayValue(option) }}
                <i
                    *ngIf="!disabled"
                    class="pi pi-times-circle"
                    style="width: 1rem; height: 1rem; margin-left: 0.5rem; cursor: pointer"
                    (click)="removeTag(option, $event)"
                ></i>
            </div>
        </ng-container>
    </div>

    <!-- Nút xóa tất cả -->
    <i *ngIf="objectValue.length && !disabled" class="tw-mr-3 pi pi-times" (click)="clearSelection($event)"></i>
    <i *ngIf="!objectValue.length" class="tw-mr-3 pi pi-chevron-down tw-transition-transform tw-duration-500" [ngClass]="{ 'rotate-180': showDropdown }"></i>
</div>

<!-- Dropdown sử dụng OverlayPanel -->
<p-overlayPanel
    #overlayPanel
    styleClass="tw-overflow-auto tw-max-h-[250px] combobox"
    appendTo="body"
    (onShow)="panelShow.emit()"
    [style]="{ left: dropdownPosition.left, width: dropdownPosition.width }"
>
    <span class="tw-w-full tw-px-3 tw-py-2 tw-bg-stone-100 tw-sticky tw-top-0 p-input-icon-right">
        <input
            #dropdownSearch
            [(ngModel)]="searchValue"
            (input)="filterOptions($event.target.value)"
            (keydown)="onKeyDown($event)"
            pInputText
            class="tw-w-full"
        />
        <i class="pi pi-search tw-mr-2"></i>
    </span>

    <div class="tw-w-full tw-bg-white tw-border tw-border-gray-300 tw-rounded-md tw-shadow-lg tw-overflow-auto">
        <!-- Khi đang fetch -->
        <ng-container *ngIf="isFetching; else loaded">
            <div class="tw-px-4 tw-py-3 tw-text-gray-500 tw-text-center">Đang tải dữ liệu...</div>
        </ng-container>

        <!-- Khi đã fetch xong và có kết quả -->
        <ng-template #loaded>
            <!-- Có dữ liệu -->
            <ul class="tw-list-none tw-p-0 tw-m-0" *ngIf="filteredOptions?.length > 0; else noData">
                <li
                    *ngFor="let option of filteredOptions; let i = index"
                    class="tw-px-4 tw-py-3 tw-cursor-pointer"
                    [ngClass]="{
                        'tw-bg-blue-100 tw-text-blue-500 hover:tw-bg-blue-200': isSelectedOption(option),
                        'tw-bg-gray-200': i === activeIndex,
                        'hover:tw-bg-gray-200': !isSelectedOption(option),
                    }"
                    (click)="selectOption(option)"
                >
                    {{ getDisplayValue(option) }}
                </li>
            </ul>

            <!-- Không có dữ liệu sau khi load xong -->
            <ng-template #noData>
                <div class="tw-px-4 tw-py-3 tw-text-gray-500 tw-text-center">Không có dữ liệu</div>
            </ng-template>
        </ng-template>
    </div>
</p-overlayPanel>
