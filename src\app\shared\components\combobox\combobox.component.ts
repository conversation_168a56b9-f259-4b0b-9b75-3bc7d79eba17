/* eslint-disable @typescript-eslint/no-explicit-any */
import { Component, OnInit, OnChanges, Input, Output, EventEmitter, ViewChild, ElementRef, inject, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { injectQuery, QueryObserverBaseResult } from '@ngneat/query';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { debounce, isArray, isEqual, unionBy, filter, includes, get, isObject } from 'lodash';
import { FormsModule, NG_VALUE_ACCESSOR } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { OverlayPanel, OverlayPanelModule } from 'primeng/overlaypanel';
import { forwardRef } from '@angular/core';

@Component({
    selector: 'app-combobox',
    standalone: true,
    imports: [CommonModule, InputTextModule, FormsModule, OverlayPanelModule],
    templateUrl: './combobox.component.html',
    styleUrls: ['./combobox.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => ComboboxComponent),
            multi: true,
        },
    ],
})
export class ComboboxComponent implements OnInit, OnChanges {
    @ViewChild('combobox') comboboxRef: ElementRef;
    @ViewChild('dropdownSearch') dropdownSearchRef: ElementRef;
    @ViewChild('overlayPanel') overlayPanel: OverlayPanel;

    @Input() type: 'select' | 'select-one' = 'select';
    @Input() url: string;
    @Input() fieldValue: string = 'id';
    @Input() fieldLabel: string = 'name';
    @Input() options: any[] = [];
    @Input() param: string;
    @Input() rsql: boolean = true;
    @Input() dataKey: string = 'id';
    @Input() page: number = 0;
    @Input() size: number = 100;
    @Input() sort: string = 'id,desc';
    @Input() body: Record<string, unknown>;
    @Input() placeholder: string = '';
    @Input() disabled: boolean = false;
    @Input() requiredFilter: string[] = [];
    @Input() removeField: string;
    @Input() removeFieldValues: unknown[];
    @Input() additionalCondition: string;
    @Input() value: unknown;

    @Output() onChange: EventEmitter<{ value: unknown; objects: Array<any> }> = new EventEmitter();
    @Output() onRemove: EventEmitter<{ value: unknown; object: any }> = new EventEmitter();

    private valueSubject = new Subject<unknown>();
    #http = inject(HttpClient);
    #query = injectQuery();

    filteredOptions: unknown[] = [];
    showDropdown = false;
    activeIndex = -1;
    dropdownPosition = { top: '0px', left: '0px', width: '0px' };
    resultSelect: QueryObserverBaseResult<unknown, Error>;
    objectValue: unknown[] = [];
    fetchFirstDone: boolean = false;
    debouncedGetOptions: (...args: unknown[]) => void;
    isSelected: boolean = false;
    searchValue: string = '';
    prioritizeForm: boolean = false;
    placeholderData: Array<any> = [];
    countCallApi: number = 0;
    needMoreOption: boolean = false;

    private onChangeCallback: (value: any) => void = () => {};
    private onTouchedCallback: () => void = () => {};

    constructor() {
        this.debouncedGetOptions = debounce((searchValue: unknown) => this.fetchOptions(searchValue, this.prioritizeForm), this.getDebounceTime());
        this.initializeDebounce();
    }

    ngOnInit(): void {
        this.initializeResultSelect();
        this.filteredOptions = [...(this.resultSelect.data as unknown[])];
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['options']) {
            this.updateResultSelectOptions(changes['options'].currentValue);
        }
        if (changes['body']) {
            this.debouncedGetOptions(this.value);
        }
        if (changes['value']) {
            this.writeValue(changes['value'].currentValue);
        }
    }

    private initializeDebounce(): void {
        this.valueSubject
            .pipe(
                debounceTime(this.getDebounceTime()),
                distinctUntilChanged((prev, curr) => isEqual(prev, curr)),
            )
            .subscribe((value) => {
                this.updateValueLogic(value);
                this.onChangeCallback(value);
                this.onChange.emit({ value, objects: this.objectValue });
            });
    }

    private getDebounceTime(): number {
        return this.type === 'select' ? 300 : 500;
    }

    private updateValueLogic(value: unknown): void {
        this.value = this.type === 'select' ? this.handleSelectValue(value) : value;
        this.isSelected = this.type === 'select-one' && !!value;
    }

    private handleSelectValue(value: unknown): Array<unknown> {
        if (!value) return [];
        return isArray(value) ? value : [value];
    }

    private initializeResultSelect(): void {
        this.resultSelect = {
            data: this.options || [],
            error: null,
            dataUpdatedAt: Date.now(),
            errorUpdateCount: 0,
            errorUpdatedAt: 0,
            failureCount: 0,
            failureReason: null,
            fetchStatus: 'idle',
            isError: false,
            isPending: true,
            isLoadingError: false,
            isRefetchError: false,
            isSuccess: false,
            status: 'pending',
            isFetched: false,
            isFetchedAfterMount: false,
            isFetching: false,
            isInitialLoading: false,
            isLoading: false,
            isPaused: false,
            isPlaceholderData: true,
            isRefetching: false,
            isStale: false,
            refetch: () => Promise.resolve(null),
        };
    }

    private updateResultSelectOptions(newOptions: any[]): void {
        if (newOptions) {
            const newData = unionBy(newOptions, this.objectValue, this.fieldValue);
            this.resultSelect = { ...this.resultSelect, data: newData };
            this.filteredOptions = [...newData];
        }
    }

    writeValue(value: any): void {
        if (isEqual(value, this.value)) return;
        this.updateValueLogic(value);
        if (this.url) {
            this.prioritizeForm = true;
            this.debouncedGetOptions(value);
        } else {
            this.updateObjectValue();
        }
    }

    registerOnChange(fn: any): void {
        this.onChangeCallback = fn;
    }

    registerOnTouched(fn: any): void {
        this.onTouchedCallback = fn;
    }

    setDisabledState(isDisabled: boolean): void {
        if (this.disabled) return;
        this.disabled = isDisabled;
    }

    private updateObjectValue(): void {
        const data = unionBy(this.resultSelect.data as any, this.objectValue, this.fieldValue);

        if (this.value) {
            const values = isArray(this.value) ? this.value : [this.value];
            const selectedOptions = values.map((val) => data.find((option) => get(option, this.fieldValue) === val)).filter((option) => option);
            this.objectValue = selectedOptions;
        } else {
            this.objectValue = [];
            this.isSelected = false;
        }
    }

    filterOptions(value: string): void {
        this.searchValue = value;
        if (this.url) {
            this.debouncedGetOptions(value);
        } else {
            const searchText = value.toLowerCase();
            this.filteredOptions = filter(this.resultSelect.data as any, (option) => {
                const label = this.getDisplayValue(option).toLowerCase();
                return includes(label, searchText);
            });
        }
        this.activeIndex = -1;
    }

    getDisplayValue(item: unknown): string {
        return isObject(item) && this.fieldLabel ? get(item, this.fieldLabel, '') : String(item);
    }

    getStoredValue(item: unknown): unknown {
        return isObject(item) && this.fieldValue ? get(item, this.fieldValue) : item;
    }

    focusInput(event: Event): void {
        if (this.disabled) return;
        if (this.url && !this.fetchFirstDone) {
            this.debouncedGetOptions(null);
        }
        this.showDropdown = true;
        this.activeIndex = -1;
        this.overlayPanel.show(event, this.comboboxRef.nativeElement);
        this.updateDropdownPosition();
        setTimeout(() => this.dropdownSearchRef?.nativeElement.focus(), 0);
    }

    blurInput(): void {
        setTimeout(() => {
            this.onTouchedCallback();
        }, 150);
    }

    onKeyDown(event: KeyboardEvent): void {
        switch (event.key) {
            case 'ArrowDown':
                this.activeIndex = Math.min(this.activeIndex + 1, this.filteredOptions.length - 1);
                event.preventDefault();
                break;
            case 'ArrowUp':
                this.activeIndex = Math.max(this.activeIndex - 1, -1);
                event.preventDefault();
                break;
            case 'Enter':
                if (this.activeIndex >= 0 && this.activeIndex < this.filteredOptions.length) {
                    this.selectOption(this.filteredOptions[this.activeIndex]);
                    event.preventDefault();
                }
                break;
            case 'Escape':
                this.overlayPanel.hide();
                this.showDropdown = false;
                event.preventDefault();
                break;
        }
    }

    isSelectedOption(option: unknown): boolean {
        const value = this.getStoredValue(option);
        return isArray(this.value) ? this.value.includes(value) : this.value === value;
    }

    selectOption(option: unknown): void {
        const value = this.getStoredValue(option);

        if (this.type === 'select-one') {
            this.value = value;
            this.overlayPanel.hide();
            this.showDropdown = false;
        } else {
            const currentValues = isArray(this.value) ? [...this.value] : [];
            if (!currentValues.includes(value)) {
                currentValues.push(value);
            } else {
                currentValues.splice(currentValues.indexOf(value), 1);
            }
            this.value = currentValues;
        }
        this.updateObjectValue();
        this.valueSubject.next(this.value);
        this.updateDropdownPosition();
    }

    removeTag(option: unknown, event: MouseEvent): void {
        event.stopPropagation();
        const value = this.getStoredValue(option);
        const object = this.objectValue.find((item) => get(item, this.fieldValue) === value);
        const currentValues = isArray(this.value) ? [...this.value] : [];
        currentValues.splice(currentValues.indexOf(value), 1);
        this.value = currentValues;
        this.updateObjectValue();
        this.valueSubject.next(this.value);
        this.updateDropdownPosition();
        this.onRemove.emit({ value, object });
    }

    clearSelection(event: MouseEvent): void {
        event.stopPropagation();
        this.value = null;
        this.objectValue = [];
        this.isSelected = false;
        this.valueSubject.next(null);
        this.updateDropdownPosition();
    }

    // @HostListener('document:click', ['$event'])
    // clickOutside(event: MouseEvent): void {
    //     const dropdownElement = this.comboboxRef?.nativeElement.querySelector('.dropdown');
    //     const comboboxElement = this.comboboxRef?.nativeElement;
    //     if (!comboboxElement?.contains(event.target) && !dropdownElement?.contains(event.target)) {
    //         this.showDropdown = false;
    //         this.updateObjectValue();
    //     }
    // }

    // @HostListener('window:scroll', ['$event'])
    // onScroll(): void {
    //     this.updateDropdownPosition();
    // }

    getDropdownPosition() {
        if (!this.comboboxRef) return { top: '0px', left: '0px', width: '200px' };
        const inputRect = this.comboboxRef.nativeElement.getBoundingClientRect();
        const windowHeight = window.innerHeight;
        const isInputInBottomHalf = inputRect.y > windowHeight / 2;
        let top: string;
        let dropdownHeight = 250;

        if (this.filteredOptions.length < 6 && this.filteredOptions.length > 0) {
            dropdownHeight = 33 * this.filteredOptions.length + 33;
        }

        if (isInputInBottomHalf) {
            top = `${0 - dropdownHeight}px`;
        } else {
            top = `${inputRect.height}px`;
        }

        return {
            top,
            left: `${inputRect.x}px`,
            width: `${inputRect.width}px`,
        };
    }

    updateDropdownPosition(): void {
        this.dropdownPosition = this.getDropdownPosition();
    }

    fetchOptions(searchValue: unknown, prioritizeForm: boolean = false): void {
        if (!this.url || this.requiredFilter.some((filter) => !this.body?.[filter])) return;
        this.#query({
            queryKey: [this.url, this.fieldValue, this.fieldLabel, this.param, this.body, searchValue],
            queryFn: () => this.getOption(searchValue),
            placeholderData: this.options,
            refetchOnWindowFocus: false,
            staleTime: 0,
        }).result$.subscribe((res) => this.handleFetchResult(res, prioritizeForm));
    }

    private getOption(searchValue: unknown) {
        const pageable = `&page=${this.page || 0}&size=${this.size || 100}&sort=${this.sort || 'id,desc'}`;
        return this.rsql
            ? this.#http.get<any[]>(this.createRsqlUrlParams(searchValue) + pageable)
            : this.#http.post<any[]>(`${this.url}?${pageable}`, this.createPostBody(searchValue));
    }

    private createRsqlUrlParams(value: unknown): string {
        const keyFilter = this.prioritizeForm ? this.fieldValue ?? this.fieldLabel : this.param ?? this.fieldLabel;
        const filters = this.buildRsqlFilters(keyFilter, value);
        if (this.removeField && this.removeFieldValues?.length) {
            filters.push(`${this.removeField}=out=(${this.removeFieldValues.join(',')})`);
        }
        const additional = this.additionalCondition && !this.prioritizeForm ? (filters.length ? ';' : '') + this.additionalCondition : '';
        return `${this.url}?query=${filters.join(';')}${additional}`;
    }

    private buildRsqlFilters(keyFilter: string, value: unknown): string[] {
        const filters: string[] = [];
        if (isArray(value) && value.length) {
            const searchValue = value.every((item) => typeof item === 'number') ? value.join(',') : value.map(encodeURIComponent).join('","');
            filters.push(`${keyFilter}=in=(${searchValue})`);
        } else if (typeof value === 'string' && value.trim()) {
            filters.push(`${keyFilter}=='*${value.trim()}*'`);
        } else if (typeof value === 'number') {
            filters.push(`${keyFilter}==${value}`);
        }
        if (this.body) {
            for (const [key, val] of Object.entries(this.body)) {
                if (isArray(val) && val.length) {
                    const searchValue = val.every((item) => typeof item === 'number') ? val.join(',') : val.map(encodeURIComponent).join('","');
                    filters.push(`${key}=in=(${searchValue})`);
                } else if (val === 'null') {
                    filters.push(`${key}==${val}`);
                } else if (val === '!=null') {
                    filters.push(`${key}!=null`);
                } else if (typeof val === 'string' && val.trim()) {
                    filters.push(`${key}==*${val.trim()}*`);
                } else if (typeof val === 'number') {
                    filters.push(`${key}==${val}`);
                }
            }
        }
        return filters;
    }

    private createPostBody(value: unknown): unknown {
        const body = { ...this.body };
        const param = this.prioritizeForm ? this.fieldValue : this.param ?? this.fieldLabel;
        if (param) {
            body[param] = (isArray(value) || typeof value === 'string') && value ? value : undefined;
            if (typeof value === 'number' && value) {
                body[param] = value;
            }
        }
        return Object.fromEntries(Object.entries(body).map(([k, v]) => [k, isArray(v) && !v.length ? null : v]));
    }

    private handleFetchResult(res: QueryObserverBaseResult<unknown, Error>, prioritizeForm: boolean = false): void {
        this.resultSelect = {
            ...res,
            data: unionBy(this.objectValue, res.data as any, this.fieldValue),
        };
        if (res.isFetching) return;
        this.filteredOptions = this.searchValue ? (res.data as any) : (this.resultSelect.data as any);
        this.updateObjectValue();

        if (prioritizeForm) {
            this.onChange.emit({ value: this.value, objects: this.objectValue });
            this.prioritizeForm = false;
        }

        if (this.value && this.countCallApi === 0) {
            this.needMoreOption = true;
        }

        if (this.needMoreOption) {
            this.fetchOptions(null, false);
            this.needMoreOption = false;
        }

        this.fetchFirstDone = true;
        this.countCallApi++;
    }
}
