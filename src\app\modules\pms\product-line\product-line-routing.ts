import { canAuthorize } from '../../../core/auth/auth.guard';

export const PLRouting = {
    path: 'product-line',
    title: '<PERSON>uản lý dòng sản phẩm',
    children: [
        {
            path: '',
            title: 'Dòng sản phẩm',
            canActivate: [canAuthorize],
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'product_view', 'product_line_view'] },
            loadComponent: () => import('./list/list.component').then((c) => c.ProductLineListComponent),
        },
    ],
};
