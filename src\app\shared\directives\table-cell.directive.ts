// track-change-cell.directive.ts
import { Directive, Input, TemplateRef } from '@angular/core';

@Directive({
    standalone: true,
    selector: '[TableCell]', // chọn mọi <ng-template> có attribute TableCell
})
export class TableCellDirective {
    @Input('TableCell') field!: string;
    //  khi dùng <ng-template TableCell="details"> thì
    //    `field === 'details'`

    constructor(public template: TemplateRef<any>) {}
    //  Angular sẽ truyền vào TemplateRef của <ng-template> đó để render lại
}
