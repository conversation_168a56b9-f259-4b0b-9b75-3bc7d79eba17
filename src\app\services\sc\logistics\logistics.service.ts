import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { Logistics } from '../../../models/interface/sc';
import { GeneralEntity } from '../../../models/interface';

@Injectable()
export class LogisticsService extends BaseService<Logistics> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/logistics');
    }

    exportSupplierLogistics = () => {
        return this.http.get<GeneralEntity>(`/sc/api/logistics/export`);
    };
    updateContractDate(body: Logistics) {
        return this.http.post(`/sc/api/logistics/update-contract-date`, body);
    }
}
