import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { map } from 'rxjs/operators';
import { Observable } from 'rxjs';
import {
    CreateProject,
    UpdateProject,
    ProjectPlan,
    SendApprovalRequest,
    ApprovalRequest,
    ProjectWorkingSettingRequest,
} from 'src/app/models/interface/ptm/project-plan';
import { ParamsTable } from 'src/app/shared/table-module/table.common.service';
// import { CreateProject } from 'src/app/models/interface/ptm';

@Injectable({ providedIn: 'root' })
export class ProjectPlanService {
    path = '/pr/api/project';
    #http = inject(HttpClient);

    createProjectPlan(payload: CreateProject): Observable<CreateProject> {
        return this.#http.post<CreateProject>(this.path, payload);
    }

    updateProjectPlan(id: number, payload: UpdateProject): Observable<UpdateProject> {
        return this.#http.put<UpdateProject>(`${this.path}/${id}`, payload);
    }

    getProject(id: number) {
        return this.#http.get(`${this.path}/${id}`);
    }

    cloneProjectPlan(projectId: number, payload: UpdateProject) {
        return this.#http.post<UpdateProject>(`/pr/api/project/${projectId}/duplicate`, payload);
    }

    updateProjectWorkingSettings(projectId: number, data: ProjectWorkingSettingRequest) {
        return this.#http.put(`/pr/api/project/${projectId}/working-setting`, data);
    }

    exportProject(projectId: number): Observable<void> {
        return this.exportFile(`/pr/api/project/${projectId}/export`);
    }

    private exportFile(url: string, params?: any): Observable<void> {
        // Nếu có params thì append vào url, nếu không thì truyền url trực tiếp
        return this.#http
            .get(url, {
                params,
                responseType: 'blob',
                observe: 'response',
            })
            .pipe(
                map((response: HttpResponse<Blob>) => {
                    // Lấy file name từ Content-Disposition
                    const contentDisposition = response.headers.get('Content-Disposition') || '';
                    let fileName = 'download.xlsx';
                    // Ưu tiên filename* (utf-8)
                    const filenameStarMatch = contentDisposition.match(/filename\*=(?:UTF-8''|)([^;]+)/i);
                    if (filenameStarMatch && filenameStarMatch[1]) {
                        fileName = decodeURIComponent(filenameStarMatch[1].trim());
                    } else {
                        // Fallback filename thường
                        const filenameMatch = contentDisposition.match(/filename\s*=\s*["']?([^"';]+)["']?/i);
                        if (filenameMatch && filenameMatch[1]) {
                            fileName = filenameMatch[1];
                        }
                    }
                    // Tạo blob và tải về
                    const blob = new Blob([response.body as BlobPart]);
                    const link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = fileName;
                    link.style.display = 'none';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    // Dọn dẹp
                    setTimeout(() => window.URL.revokeObjectURL(link.href), 1000);

                    return; // Observable<void>
                }),
            );
    }

    getProjectWorkingSettings(projectId: number) {
        return this.#http.get<any[]>(`/pr/api/project/${projectId}/working-setting`);
    }

    sendApprovalRequest(payload: SendApprovalRequest): Observable<any> {
        return this.#http.post<any>(`/pr/api/approval/project`, payload);
    }

    confirmApprovalRequest(payload: ApprovalRequest): Observable<any> {
        return this.#http.post<any>(`/pr/api/approval/project/confirm`, payload);
    }

    getInfoApprovalProject(id: number): Observable<any> {
        return this.#http.get<any>(`/pr/api/approval/project/${id}`);
    }

    deleteProject(id: number): Observable<any> {
        return this.#http.delete<any>(`/pr/api/project/${id}`);
    }

    getProjectHistory(id: number) {
        return this.#http.get(`/pr/api/history/project/${id}`);
    }
    searchProjects(params: ParamsTable, body) {
        const customQs = this.buildCustomQueryString(params.native, params.pageable);
        const subQuery = [];
        if (body.projectName) {
            subQuery.push(`projectName=${encodeURIComponent(body.projectName)}`);
        }
        if (body.productName) {
            subQuery.push(`productName=${encodeURIComponent(body.productName)}`);
        }
        if (body.vnptManPn) {
            subQuery.push(`vnptManPn=${encodeURIComponent(body.vnptManPn)}`);
        }
        // if (body.startDate) {
        //     subQuery.push(`timelineFrom=${encodeURIComponent(body.startDate)}`);
        // }
        if (body.startDate && Array.isArray(body.startDate)) {
            const [from, to] = body.startDate;
            if (from) {
                subQuery.push(`timelineFrom=${new Date(from).getTime()}`);
            }
            if (to) {
                subQuery.push(`timelineTo=${new Date(to).getTime()}`);
            }
        }
        if (body.rate) {
            subQuery.push(`completeRatio=${encodeURIComponent(body.rate)}`);
        }
        if (body.milestoneMultilevelNumbering) {
            subQuery.push(`milestone=${encodeURIComponent(body.milestoneMultilevelNumbering)}`);
        }
        if (body.approvalStatus) {
            subQuery.push(`approvalStatus=${encodeURIComponent(body.approvalStatus)}`);
        }

        // Nếu customQs rỗng, thì URL là "/.../filter?".strip("&")
        const queryString = customQs.replace(/^&/, '');

        let query = '';
        if (subQuery.length) {
            query = `&${subQuery.join('&')}`;
        }

        return this.#http.get<ProjectPlan[]>(this.path + `/simple-search?${queryString}${query}`, {
            observe: 'response',
        });
    }
    private buildCustomQueryString(nativePart: string, pageablePart: string): string {
        // Bỏ dấu '&' đầu (nếu có)
        const rawNative = nativePart ? nativePart.replace(/^&/, '') : '';
        const rawPageable = pageablePart ? pageablePart.replace(/^&/, '') : '';

        // Tách rawNative thành từng cặp "key=value"
        const nativePairs = rawNative ? rawNative.split('&') : [];
        const multiParts: string[] = [];
        const pageablePairs = rawPageable ? rawPageable.split('&') : [];

        const filteredPageable = pageablePairs.filter((p) => !p.startsWith('size=') && !p.startsWith('sort='));

        nativePairs.forEach((pair) => {
            const [key, value] = pair.split('=');
            if (key === 'productIds' && value) {
                // Nếu value = "2,3" → phân tách thành ["2","3"]
                value.split(',').forEach((id) => {
                    if (id) {
                        multiParts.push(`productIds=${encodeURIComponent(id)}`);
                    }
                });
            } else if (pair) {
                // Giữ nguyên nếu key khác
                multiParts.push(pair);
            }
        });

        // Ghép pageable trước, rồi native đã tách
        const allParts = [...filteredPageable, ...multiParts];
        return allParts.join('&');
    }
}
