import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ParamsTable } from '../../../shared/table-module/table.common.service';
import { ApproveDetailDTO, ApproveDTO, ApproveFilter } from 'src/app/models/interface/smart-qc';
import { ApiResponse } from 'src/app/models/interface';

@Injectable()
export class ApproveService {
    constructor(private http: HttpClient) {}

    getOne(id: number) {
        return this.http.get<ApproveDetailDTO>('/smart-qc/api/approve/' + id);
    }

    getPageTableCustom({ pageable = '&page=0&size=10' }: ParamsTable, body: ApproveFilter) {
        return this.http.post<ApproveDTO[]>(`/smart-qc/api/approve/search-native?${pageable}`, body, {
            observe: 'response',
        });
    }

    approveBySubPm(id: number, body) {
        return this.http.post<void>('/smart-qc/api/approve/subpm/' + id, body);
    }

    approveByPm(id: number, body) {
        return this.http.post<void>('/smart-qc/api/approve/pm/' + id, body);
    }

    acceptMultiByPm(ids: number[]) {
        return this.http.post<void>('/smart-qc/api/approve/pm/accept', ids);
    }

    export(body: ApproveFilter) {
        return this.http.post<ApiResponse>('/smart-qc/api/approve/export', body);
    }
}
