import { HttpClient } from '@angular/common/http';
import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { Subject } from 'rxjs';
import Common from 'src/app/utils/common';

@Component({
    selector: 'app-input-cbb',
    templateUrl: './input.cbb.component.html',
    standalone: true,
    imports: [ReactiveFormsModule, NgSelectModule],
})
export class InputCBBComponent implements OnInit {
    @Input() selectizeId;
    @Input() url: string;
    @Input() itemLable;
    @Input() itemValue;
    @Input() placeHolder;
    @Input() isMultiple = false;
    @Input() model;
    @Output('handleChangeSelected') handleChangeSelected: EventEmitter<unknown> = new EventEmitter();
    // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
    @Input('callback') callback: Function = () => {};
    @Input('options') options: unknown[] = [];

    constructor(public http: HttpClient) {}

    loading: boolean = true;
    page: number = 0;
    originPage: number = 0;
    input$ = new Subject<string>();
    selected = [];

    ngOnInit(): void {
        // const api: string = this.genQuery('');
        this.fetchMore('');
    }

    fetchMore(term) {
        const api: string = this.genQuery(term);
        this.http.get<unknown[]>(api, { observe: 'response' }).subscribe((res) => {
            this.options = Common.mergeArray(this.options, res.body, (a, b) => a.id === b.id);
            this.loading = false;

            if (term === '' || term === undefined) {
                this.originPage = ++this.page;
            }
        });
    }

    onSearch(e) {
        this.page = e.term === '' || e.term === undefined ? this.originPage : 0;
        this.fetchMore(e.term);
    }

    onRemove(e) {
        this.page = this.originPage;
        this.fetchMore('');
        this.selected = this.selected.filter((obj) => obj.id !== e.id);
        this.handleChangeSelected.emit([this.selected]);
    }

    onClear() {
        this.page = this.originPage;
        this.fetchMore('');
    }

    onAdd(e) {
        const selected = this.options.filter((obj) => obj['id'] === e.id);
        this.selected.push(...selected);
        this.handleChangeSelected.emit([this.selected]);
    }

    genQuery(param) {
        let query = this.url;
        if (param !== '' && param !== undefined) {
            query += `${this.itemLable}=='*${param}*'`;
        }
        return `${query}&page=${this.page}&size=10&sort=id,desc`;
    }

    onChange(e) {
        this.callback(e);
    }
}
