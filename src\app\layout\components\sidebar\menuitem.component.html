<a
    *ngIf="!item.routerLink"
    [attr.tabindex]="item?.disabled ? '-1' : '0'"
    class="tw-w-full tw-flex tw-items-center tw-gap-3 tw-p-3 tw-cursor-pointer transaction-bg tw-relative"
    (click)="itemClick($event)"
    [ngClass]="{
        'tw-justify-center': sideBarState.state !== 'expand',
    }"
    [appAuthorities]="item.authorize"
>
    <span
        *ngIf="item.icon; else noIcon"
        class="tw-flex tw-justify-center tw-items-center"
        [ngStyle]="{
            'background-color': active || haveChildActive ? 'var(--primary-color)' : 'var(--background-color)',
            'text-align': sideBarState.state === 'expand' ? 'normal' : 'center',
        }"
        style="border-radius: 4px; width: 24px; height: 24px; flex: 0 0 auto"
    >
        <i
            [ngClass]="item.icon"
            [ngStyle]="{
                color: active || haveChildActive ? 'var(--primary-color-text)' : 'var(--text-color)',
            }"
        >
        </i>
    </span>
    <span
        class="tw-h-fit tw-text-nowrap tw-text-ellipsis tw-overflow-hidden"
        [ngStyle]="{
            color: active || haveChildActive ? 'var(--primary-color)' : 'var(--text-color)',
            'font-weight': active || haveChildActive ? '600' : '400',
            display: sideBarState.state === 'expand' ? 'inline-block' : 'none',
        }"
        >{{ item?.label }}</span
    >

    <i
        class="pi pi-fw pi-angle-down tw-text-sm tw-ml-auto"
        [ngClass]="{ 'rotate-180': expand, 'rotate-revert': !expand }"
        *ngIf="item.items && sideBarState.state === 'expand'"
        style="flex: 0 0 auto"
    ></i>
</a>
<a
    *ngIf="item.routerLink"
    [routerLink]="item.routerLink"
    [attr.tabindex]="item?.disabled ? '-1' : '0'"
    routerLinkActive="active-route"
    [routerLinkActiveOptions]="
        item.routerLinkActiveOptions || {
            paths: 'subset',
            queryParams: 'ignored',
            matrixParams: 'ignored',
            fragment: 'ignored',
        }
    "
    class="tw-w-full tw-flex tw-items-center tw-gap-3 tw-p-3 tw-cursor-pointer tw-relative"
    (click)="itemClick($event)"
    (isActiveChange)="onRouterLinkActive($event)"
    [ngClass]="{
        'tw-justify-center': sideBarState.state !== 'expand',
    }"
    [appAuthorities]="item.authorize"
>
    <span
        *ngIf="item.icon; else noIcon"
        class="tw-flex tw-justify-center tw-items-center"
        [ngStyle]="{
            'background-color': active || haveChildActive ? 'var(--primary-color)' : 'var(--background-color)',
        }"
        style="border-radius: 4px; width: 24px; height: 24px; flex: 0 0 auto"
    >
        <i
            [ngClass]="item.icon"
            [ngStyle]="{
                color: active || haveChildActive ? 'var(--primary-color-text)' : 'var(--text-color)',
            }"
        >
        </i>
    </span>
    <span
        class="tw-h-fit tw-text-nowrap tw-text-ellipsis"
        [ngStyle]="{
            color: active || haveChildActive ? 'var(--primary-color)' : 'var(--text-color)',
            'font-weight': active || haveChildActive ? '600' : '400',
            display: sideBarState.state === 'expand' ? 'inline-block' : 'none',
        }"
        >{{ item?.label }}</span
    >
</a>
<ul
    *ngIf="item?.items"
    class="expandable menu_item-level"
    [ngClass]="{ expand: sideBarState.state === 'expand' && expand, 'transition_max-height': !expand }"
    [appAuthorities]="item.authorize"
>
    <ng-container *ngFor="let item of item.items">
        <li class="">
            <app-menuitem
                app-menuitem
                [item]="item"
                [level]="level + 1"
                (isActiveChange)="onChildRouterLinkActive(item.routerLink, $event)"
            ></app-menuitem>
        </li>
    </ng-container>
</ul>

<ng-template #noIcon>
    <span
        class="tw-absolute tw-top-0 tw-left-0 tw-h-full"
        [ngStyle]="{
            'background-color': active || haveChildActive ? 'var(--primary-color)' : 'var(--background-color)',
            display: sideBarState.state === 'expand' ? 'inline-block' : 'none',
            width: '2.5px',
        }"
    ></span>
</ng-template>
