import { Compo<PERSON>, OnInit, QueryList, <PERSON>Child, ViewChildren } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { MultiSelectModule } from 'primeng/multiselect';
import { PanelModule } from 'primeng/panel';
import { STATE_SUPPLIER, TIME_TYPE } from 'src/app/models/constant/sc';

import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { AlertService } from 'src/app/shared/services/alert.service';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { DateUtils } from 'src/app/utils/date-utils';
import { submitSpecificForm } from 'src/app/utils/form';
import { dateRangeValidator, sharePointUrlValidator } from 'src/app/utils/validator';
import { CommonModule } from '@angular/common';
import { FilterChangeEvent } from 'src/app/shared/table-module/filter-table/filter-table.component';
import { isNumber, isArray, isEmpty } from 'lodash';
import {ApiResponse, Attachment, EventPopupSubmit, GeneralEntity, User} from 'src/app/models/interface';
import { FileService } from 'src/app/shared/services/file.service';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { SupplierInforService } from 'src/app/services/sc/supplier/suppiler-infor.service';
import { SupplierTypeService } from 'src/app/services/sc/supplier/suppiler-type.service';
import { BehaviorSubject, forkJoin } from 'rxjs';
import { SupplierKpiService } from 'src/app/services/sc/supplier/supplier.kpi.service';
import { SupplierQualityService } from 'src/app/services/sc/supplier-report/suppiler-quality.service';

import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { Supplier, SupplierCriteria, SupplierDocumentRes, SupplierItem, SupplierKpi } from 'src/app/models/interface/sc';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { ButtonGroupFileComponent } from 'src/app/shared/components/button-group-file/button-group-file.component';
import { ChartQualityComponent } from '../report/chart-quality/chart-quality.component';
import { TablePoComponent } from '../report/table-po/table-po.component';
import { ChartPriceComponent } from '../report/chart-price/chart-price.component';
import { PopupComponent } from 'src/app/shared/components/popup/popup.component';
import { ChartDeliveryComponent } from '../report/chart-delivery/chart-delivery.component';
import Common from 'src/app/utils/common';
import { SplitterModule } from 'primeng/splitter';
import { AttachmentComponent } from 'src/app/shared/components/attachment/attachment.component';
import { DialogModule } from 'primeng/dialog';
import {PoService} from "../../../../services/sc/po/po.service";
import {AuthService} from "../../../../core/auth/auth.service";
@Component({
    selector: 'app-supplier-infor-detail',
    templateUrl: './detail.component.html',
    styleUrls: ['./infor.component.scss'],
    standalone: true,
    imports: [
        ButtonModule,
        RouterLink,
        PanelModule,
        ReactiveFormsModule,
        InputTextModule,
        DropdownModule,
        MultiSelectModule,
        InputTextareaModule,
        CalendarModule,
        TableModule,
        TabViewModule,
        FormsModule,
        CalendarModule,
        HasAnyAuthorityDirective,
        FormCustomModule,
        TableCommonModule,
        SubHeaderComponent,
        ButtonGroupFileComponent,
        CommonModule,
        ChartQualityComponent,
        TablePoComponent,
        ChartPriceComponent,
        PopupComponent,
        ChartDeliveryComponent,
        SplitterModule,
        AttachmentComponent,
        DialogModule,
    ],
    providers: [SupplierInforService, SupplierTypeService, FileService, SupplierKpiService, SupplierQualityService],
})
export class SupplierDetailComponent implements OnInit {
    itemsHeader = [];

    // Thông tin params
    supplierId: number;

    // inject tham số

    // Form
    oldSupplier: Supplier = {
        name: '',
        shortName: '',
        supplierTypeId: '',
        email: '',
        profileStatus: 2,
        state: 0,
        criteriaListView: [],
    };
    formGroup: FormGroup;
    formReport: FormGroup;
    @ViewChildren(FormComponent) forms: QueryList<FormComponent>;

    // Các option select
    yearRange: { label: string; value: number }[];
    stateSupplier = STATE_SUPPLIER;

    // state
    disabledFileCriteria: boolean = false;
    supplierKpiOption: SupplierKpi[] = [];
    supplierDocumentRes: SupplierDocumentRes;
    messageSharePoint: string;

    // state cấu hình báo cáo
    selectedTimeType: 'year' | 'month' = 'year';
    @ViewChild('childQuality') childQuality: ChartQualityComponent;
    @ViewChild('childPrice') childPrice: ChartPriceComponent;
    @ViewChild('childDelivery') childDelivery: ChartDeliveryComponent;

    private objectResultReportSubject = new BehaviorSubject({
        quality: 0,
        price: 0,
        delivery: 0,
        config: null,
    });
    averagePoint: number | string = '_';
    averageRate: string = 'E';
    objectResultReport$ = this.objectResultReportSubject.asObservable();
    activeIndex = 0;

    // chi tiết đánh giá trước mua

    isOpenModalViewResult: boolean = false;
    resultsDetail: SupplierCriteria[] = [];

    // Role
    user: User = { email: '' };
    hasViewPriceRole: boolean = false;
    // End role

    constructor(
        private fb: FormBuilder,
        private route: ActivatedRoute,
        private supplierInforService: SupplierInforService,
        private loadingService: LoadingService,
        private alertService: AlertService,
        private supplierTypeService: SupplierTypeService,
        private fileService: FileService,
        private supplierKpiService: SupplierKpiService,
        private router: Router,
        public auth: AuthService
    ) {
        this.initForm();
        this.initFormReport();

        this.auth.userObserver.subscribe((data) => {
            this.user = data;
            if (this.user.authorities && (this.user.authorities.includes('ROLE_SYSTEM_ADMIN') || this.user.authorities.includes('sc_view_price'))) {
                this.hasViewPriceRole = true;
                console.log("Can view price");
            }
        });
    }

    ngOnInit(): void {
        this.objectResultReport$.subscribe((report) => {
            this.averagePoint = Common.calculateAverageByConfigWeight(report.quality, report.price, report.delivery, report.config);
            this.averageRate = Common.getGrade(this.averagePoint, false);
        });
        this.route.paramMap.subscribe((params) => {
            this.supplierId = params.get('id') ? Number(params.get('id')) : null;

            if (!this.supplierId) {
                this.itemsHeader = [
                    { label: 'Quản lý nhà cung cấp' },
                    { label: 'Quản lý thông tin nhà cung cấp', url: '/sc/supplier-infor' },
                    { label: 'Tạo mới' },
                ];
            } else {
                this.loadingService.show();
                forkJoin([this.supplierInforService.getOne(this.supplierId), this.supplierKpiService.advancedGroup({})]).subscribe({
                    next: ([response1, response2]) => {
                        this.oldSupplier = response1.body;
                        this.oldSupplier.criteriaListView = this.oldSupplier.criteriaList.filter((criteria) => !criteria.result);
                        this.initForm(this.oldSupplier);
                        this.loadingService.hide();
                        this.itemsHeader = [
                            { label: 'Quản lý nhà cung cấp' },
                            { label: 'Quản lý thông tin nhà cung cấp', url: '/sc/supplier-infor' },
                            { label: this.oldSupplier.name },
                        ];

                        this.supplierKpiOption = response2.body;
                        this.applyDataKpiOnLoad();
                        if (this.oldSupplier.profileLink) {
                            this.getInforSharePoint(this.oldSupplier.profileLink);
                        }
                    },
                    error: () => {
                        this.loadingService.hide();
                    },
                });
            }
        });

        this.yearRange = DateUtils.generateYearRange(2000);
    }

    initForm(supplierData: Supplier = null): void {
        this.formGroup = this.fb.group({
            name: [supplierData ? supplierData.name : null, Validators.required],
            shortName: [supplierData ? supplierData.shortName : null, [Validators.required, Validators.maxLength(100)]],
            code: [{ value: supplierData ? supplierData.code : null, disabled: true }],
            supplierTypeId: [supplierData ? supplierData.supplierTypeId : null, Validators.required],
            profileLink: [supplierData ? supplierData.profileLink : null, [sharePointUrlValidator()]],
            profileStatus: [supplierData ? supplierData?.profileStatus ?? 2 : 2, [Validators.required]],
            productSupplied: [supplierData ? supplierData.productSupplied : null],
            address: [supplierData ? supplierData.address : null],
            country: [supplierData ? supplierData.country : null],
            website: [supplierData ? supplierData.website : null, []],
            contact: [supplierData ? supplierData.contact : null],
            position: [supplierData ? supplierData.position : null],
            email: [supplierData ? supplierData.email : null, [Validators.required]],
            phone: [supplierData ? supplierData.phone : null, []],
            yearBegin: [supplierData ? supplierData.yearBegin : null],
            state: [supplierData ? supplierData.state : this.stateSupplier[0].value, Validators.required],
            note: [supplierData ? supplierData.note : null],
            qualityAgree: [supplierData ? supplierData.qualityAgree : null],
            dateAgreeCustom: [supplierData && supplierData.dateAgree ? new Date(supplierData.dateAgree) : null],
        });

        this.formGroup.get('profileLink').valueChanges.subscribe((newValue) => {
            if (newValue && newValue.trim() !== '' && newValue.length > 0) {
                this.getInforSharePoint(newValue);
            } else {
                this.formGroup.patchValue({ profileStatus: 2 });
                this.messageSharePoint = null;
                this.supplierDocumentRes = null;
            }
        });
    }

    initFormReport(): void {
        this.formReport = this.fb.group(
            {
                year: [null, Validators.required],
                type: ['year', Validators.required],
                startTime: [null], // Không cần Validators.required ở đây, sẽ xử lý trong valueChanges
                endTime: [null], // Không cần Validators.required ở đây, sẽ xử lý trong valueChanges
            },
            {
                validators: dateRangeValidator(36, 'month', 'startTime', 'endTime'),
            },
        );

        // Theo dõi thay đổi của type
        this.formReport.get('type')?.valueChanges.subscribe((type) => {
            const yearControl = this.formReport.get('year');
            const startControl = this.formReport.get('startTime');
            const endControl = this.formReport.get('endTime');
            if (type === 'year') {
                yearControl?.setValidators(Validators.required); // Thiết lập year là required
                startControl?.clearValidators(); // Bỏ validators cho startTime
                endControl?.clearValidators(); // Bỏ validators cho endTime

                const configYear = this.supplierKpiOption.find((item) => item.timeType === TIME_TYPE.YEAR);

                this.formReport.patchValue({
                    year: configYear?.year || new Date().getFullYear(),
                });
            } else if (type === 'month') {
                startControl?.setValidators(Validators.required); // Thiết lập startTime là required
                endControl?.setValidators(Validators.required); // Thiết lập endTime là required
                yearControl?.clearValidators(); // Bỏ validators cho year

                const configMonth = this.supplierKpiOption.find((item) => item.timeType === TIME_TYPE.MONTH);

                this.formReport.patchValue({
                    startTime: new Date(configMonth?.startTime),
                    endTime: new Date(configMonth?.endTime),
                });
            }

            // Cập nhật lại giá trị của các control
            yearControl?.updateValueAndValidity();
            startControl?.updateValueAndValidity();
            endControl?.updateValueAndValidity();
        });
    }

    getInforSharePoint(link: string) {
        this.supplierInforService.checkQualityAgree(link).subscribe({
            next: (res) => {
                this.supplierDocumentRes = res;
                switch (res.errorCode) {
                    case 0:
                        this.messageSharePoint = 'Lấy thông tin thất bại';
                        break;
                    case 1:
                        this.messageSharePoint = null;
                        break;
                    case 2:
                        this.messageSharePoint = 'Token hết hạn';
                        break;
                    case 3:
                        this.messageSharePoint = 'Url không hợp lệ';
                        break;
                }
            },
            error: (e) => {
                this.alertService.handleError(e);
            },
        });
    }

    onSubmit(value: Supplier): void {
        this.loadingService.show();
        if (value.dateAgreeCustom) {
            value.dateAgree = value.dateAgreeCustom.getTime();
        }

        if (this.supplierId) {
            this.supplierInforService.update({ ...this.oldSupplier, ...value }).subscribe({
                next: () => {
                    this.loadingService.hide();
                    this.alertService.success('Thành công');
                },
                error: () => {
                    this.loadingService.hide();
                },
            });
        } else {
            this.supplierInforService.create(value).subscribe({
                next: (res) => {
                    this.alertService.success('Thành công');
                    this.loadingService.hide();
                    this.router.navigate([`/sc/supplier-infor/${res.body.id}`]);
                },
                error: () => {
                    this.loadingService.hide();
                },
            });
        }
    }

    submitForm(formId: string): void {
        submitSpecificForm(this.forms, formId); // Gọi hàm util
    }

    onChangeSuppilerType(data: FilterChangeEvent) {
        this.disabledFileCriteria = !isNumber(data.value);
    }

    handleDowloadFileCriteria() {
        this.supplierTypeService.getFileCriteria(this.formGroup.get('supplierTypeId').value).subscribe({
            next: (res: GeneralEntity) => {
                this.fileService.downLoadFileByService(res.url, '/sc/api');
            },
        });
    }
    handleUploadFileCriteria(file: File) {
        this.loadingService.show();
        this.supplierTypeService.importCriteria(file, this.formGroup.get('supplierTypeId').value).subscribe({
            next: (res: ApiResponse) => {
                if (typeof res.data === 'string' && res.data === 'No data available') {
                    this.alertService.error('Nhà cung cấp chưa có bộ tiêu chí đánh giá phù hợp');
                    this.oldSupplier.criteriaList = [];
                    this.oldSupplier.criteriaAttachment = null;
                    this.oldSupplier.saveCriteria = false;
                    this.formGroup.patchValue({
                        profileStatus: 2,
                    });
                } else {
                    const newCriteriaList = res.data as unknown as SupplierCriteria[];
                    this.oldSupplier.criteriaList = newCriteriaList;

                    if (isArray(newCriteriaList)) {
                        this.oldSupplier.criteriaListView = newCriteriaList.filter((criteria) => !criteria.result);
                    } else {
                        this.oldSupplier.criteriaListView = [];
                    }
                    this.formGroup.patchValue({
                        profileStatus: this.getPassCriteria(this.oldSupplier.criteriaList) ? 1 : 0,
                    });
                    this.oldSupplier.criteriaAttachment = res.attachment as unknown as Attachment;
                    this.oldSupplier.saveCriteria = true;
                }
                this.loadingService.hide();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    handleUploadFileQualityAgree(file: File) {
        this.loadingService.show();
        this.supplierInforService.importFileQuality(file, this.supplierId).subscribe({
            next: (res: ApiResponse) => {
                this.oldSupplier.agreeQualityAttachment = res.attachment;
                this.oldSupplier.saveQuality = true;
                if (isEmpty(res?.data)) {
                    this.formGroup.patchValue({
                        qualityAgree: null,
                        dateAgreeCustom: null,
                    });
                    this.oldSupplier.agreeItems = [];
                } else {
                    this.formGroup.patchValue({
                        qualityAgree: res.data['qualityAgree'],
                        dateAgreeCustom: new Date(res.data['dateAgree'] as unknown as number),
                    });
                    this.oldSupplier.agreeItems = res.data['agreeItems'] as SupplierItem[];
                    if (this.supplierId) {
                        this.alertService.success('Thành công');
                    }
                }

                this.loadingService.hide();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    getPassCriteria(criteriaList: SupplierCriteria[]) {
        if (!criteriaList || criteriaList.length === 0) return true;
        return !criteriaList.some((criteria) => !criteria.result);
    }

    handleClearFile(type: 'quality' | 'criteria') {
        if (type === 'quality') {
            if (isArray(this.oldSupplier?.removeAttachments)) {
                // Nếu là mảng, thêm id mới vào mảng và gán lại cho removeAttachments
                this.oldSupplier?.removeAttachments.push(this.oldSupplier.agreeQualityAttachment.id);
            } else {
                // Nếu không, tạo một mảng mới với id mới
                this.oldSupplier.removeAttachments = [this.oldSupplier.agreeQualityAttachment.id];
            }
            this.oldSupplier.agreeQualityAttachment = null;
            this.formGroup.patchValue({
                qualityAgree: null,
                dateAgreeCustom: null,
            });
        } else {
            if (isArray(this.oldSupplier?.removeAttachments)) {
                // Nếu là mảng, thêm id mới vào mảng và gán lại cho removeAttachments
                this.oldSupplier.removeAttachments.push(this.oldSupplier.criteriaAttachment.id);
            } else {
                // Nếu không, tạo một mảng mới với id mới
                this.oldSupplier.removeAttachments = [this.oldSupplier.criteriaAttachment.id];
            }
            this.oldSupplier.criteriaAttachment = null;
            this.formGroup.patchValue({ profileStatus: 2 });
        }
    }

    handleFilter() {
        switch (this.activeIndex) {
            case 0:
                this.childQuality.callApiReportQuality().subscribe({
                    next: (res) => {
                        this.objectResultReportSubject.next({
                            ...this.objectResultReportSubject.getValue(),
                            quality: res.averagePoint,
                        });
                    },
                });
                this.childPrice.callApiReportPrice().subscribe({
                    next: (res) => {
                        this.objectResultReportSubject.next({
                            ...this.objectResultReportSubject.getValue(),
                            price: res.averagePoint,
                        });
                    },
                });
                this.childDelivery.callApiReportDelivery().subscribe({
                    next: (res) => {
                        this.objectResultReportSubject.next({
                            ...this.objectResultReportSubject.getValue(),
                            delivery: res.averagePoint,
                        });
                    },
                });
                break;
            case 1:
                this.childPrice.callApiReportPrice().subscribe({
                    next: (res) => {
                        this.objectResultReportSubject.next({
                            ...this.objectResultReportSubject.getValue(),
                            price: res.averagePoint,
                        });
                    },
                });
                this.childQuality.callApiReportQuality().subscribe({
                    next: (res) => {
                        this.objectResultReportSubject.next({
                            ...this.objectResultReportSubject.getValue(),
                            quality: res.averagePoint,
                        });
                    },
                });
                this.childDelivery.callApiReportDelivery().subscribe({
                    next: (res) => {
                        this.objectResultReportSubject.next({
                            ...this.objectResultReportSubject.getValue(),
                            delivery: res.averagePoint,
                        });
                    },
                });
                break;

            default:
                this.childDelivery.callApiReportDelivery().subscribe({
                    next: (res) => {
                        this.objectResultReportSubject.next({
                            ...this.objectResultReportSubject.getValue(),
                            delivery: res.averagePoint,
                        });
                    },
                });
                this.childQuality.callApiReportQuality().subscribe({
                    next: (res) => {
                        this.objectResultReportSubject.next({
                            ...this.objectResultReportSubject.getValue(),
                            quality: res.averagePoint,
                        });
                    },
                });
                this.childPrice.callApiReportPrice().subscribe({
                    next: (res) => {
                        this.objectResultReportSubject.next({
                            ...this.objectResultReportSubject.getValue(),
                            price: res.averagePoint,
                        });
                    },
                });
        }
    }

    applyDataKpiOnLoad() {
        const configTime = this.supplierKpiOption.find((item) => item.code === 'TIME');

        this.formReport.patchValue({
            year: configTime.timeType === TIME_TYPE.YEAR ? configTime.year || new Date().getFullYear() : null,
            type: configTime.timeType === TIME_TYPE.YEAR ? 'year' : 'month',
            startTime: configTime.timeType === TIME_TYPE.MONTH ? new Date(configTime.startTime) : null,
            endTime: configTime.timeType === TIME_TYPE.MONTH ? new Date(configTime.endTime) : null,
        });
        const configWeight = this.supplierKpiOption.find((item) => item.code === 'WEIGHT');
        this.objectResultReportSubject.next({
            ...this.objectResultReportSubject.getValue(),
            config: configWeight,
        });
        this.childQuality.callApiReportQuality().subscribe({
            next: (res) => {
                this.objectResultReportSubject.next({
                    ...this.objectResultReportSubject.getValue(),
                    quality: res.averagePoint,
                });
            },
        });
        this.childPrice.callApiReportPrice().subscribe({
            next: (res) => {
                this.objectResultReportSubject.next({
                    ...this.objectResultReportSubject.getValue(),
                    price: res.averagePoint,
                });
            },
        });
        this.childDelivery.callApiReportDelivery().subscribe({
            next: (res) => {
                this.objectResultReportSubject.next({
                    ...this.objectResultReportSubject.getValue(),
                    delivery: res.averagePoint,
                });
            },
        });
    }

    exportDetail(event: EventPopupSubmit<unknown>) {
        this.loadingService.show();
        this.supplierInforService.exportExcelDetail(this.supplierId).subscribe({
            next: (res: Blob) => {
                // Gọi hàm downloadBlob để tải file
                this.fileService.downloadBlob(res, 'Supplier_Detail.xlsx');
                this.loadingService.hide();
                event.close();
            },
            error: () => {
                this.alertService.error('Có lỗi xảy ra');
                this.loadingService.hide();
            },
        });
    }

    showDetailResult(items: SupplierCriteria[]) {
        this.isOpenModalViewResult = true;
        this.resultsDetail = items;
    }
}
