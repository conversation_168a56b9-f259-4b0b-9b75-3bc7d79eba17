import { Component, EventEmitter, Input, Output } from '@angular/core';
import { AlertService } from '../../services/alert.service';
import { RangeDateContentComponent } from '../../table-module/range-date/range-date-content.component';

@Component({
    selector: 'app-file-select',
    templateUrl: './file-select.component.html',
    styleUrls: ['./file-select.component.scss'],
    standalone: true,
    imports: [RangeDateContentComponent],
})
export class FileSelectComponent {
    @Input('file') fileUpload;
    @Output() fileChange = new EventEmitter<Element>();

    @Input('errorFileUrl') errorFileUrl;
    @Input('templateUrl') templateUrl;

    alertService;
    constructor(alertService: AlertService) {
        this.alertService = alertService;
    }

    forbiddenChars = /[<>:"/\\|?*\x00-\x1F]/g;

    onSelectFile = (e) => {
        this.fileUpload = this.validateFile(e.target.files[0]);
        e.target.value = null;
        this.fileChange.emit(this.fileUpload);
    };

    validateFile = (file) => {
        if (!file.type?.includes('vnd.ms-excel') && !file.type?.includes('spreadsheetml')) {
            this.alertService.error('Lỗi chọn file', 'File import sai định dạng, vui lòng thử lại với file excel');
            return null;
        }

        if (this.forbiddenChars.test(file.name)) {
            this.alertService.error('Lỗi chọn file', 'Tên file không được chứa các ký tự đặc biết');
            return null;
        }

        return file;
    };
}
