<app-sub-header [items]="itemsHeader" [action]="actionHeader">
    <ng-template #actionHeader>
        <p-button *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'software_create']" size="small" (click)="openEditDialog($event, 'create')" label="Thêm mới" />
    </ng-template>
</app-sub-header>
<div style="padding: 1rem 1rem 0 1rem">
    <app-form [formGroup]="filterForm" (onSubmit)="onSearch()" layout="vertical">
        <div class="tw-grid tw-grid-cols-4 tw-gap-4 tw-my-4">
            <!-- Dòng sản phẩm -->

            <app-custom-form-item [noGrid]="true" label="Dòng sản phẩm">
                <app-combobox-nonRSQL
                    #lineSelect
                    [fetchOnInit]="false"
                    type="select"
                    formControlName="productLine"
                    fieldValue="id"
                    fieldLabel="name"
                    url="/pr/api/product-line/filter"
                    param="name"
                    placeholder="Chọn dòng sản phẩm"
                    [additionalParams]="{ size: 100 }"
                    (onChange)="onProductLineSelect($event)"
                >
                </app-combobox-nonRSQL>
            </app-custom-form-item>

            <app-custom-form-item [noGrid]="true" label="Model">
                <app-combobox-nonRSQL
                    #modelSelectFilter
                    [fetchOnInit]="false"
                    type="select"
                    formControlName="model"
                    fieldValue="id"
                    fieldLabel="name"
                    url="/pr/api/product-model"
                    param="name"
                    placeholder="Chọn Model"
                    (onChange)="onChangeModel($event)"
                    [additionalParams]="{ size: 100, page: 0, unpaged: false }"
                    [ignoreAdditionalParamsOnSearch]="true"
                >
                </app-combobox-nonRSQL>
            </app-custom-form-item>

            <!-- Tên sản phẩm -->

            <app-custom-form-item [noGrid]="true" label="Sản phẩm" [control]="filterForm.get('productId')">
                <app-combobox-nonRSQL
                    #productSelect
                    [fetchOnInit]="false"
                    type="select"
                    formControlName="productId"
                    fieldValue="id"
                    [fieldLabel]="['name', 'vnptManPn']"
                    url="/pr/api/product"
                    [additionalParams]="productParams"
                    (panelShow)="handlePanelShow(productSelect)"
                    param="searchNameOrManPn"
                    placeholder="Chọn sản phẩm"
                    [ignoreAdditionalParamsOnSearch]="true"
                ></app-combobox-nonRSQL>
            </app-custom-form-item>

            <div>
                <div style="height: 28px"></div>
                <button pButton class="p-button-rounded p-button-secondary" type="submit" icon="pi pi-search"></button>
            </div>
        </div>
    </app-form>

    <app-table-common
        [tableId]="tableId"
        [columns]="columns"
        [data]="state.data"
        [loading]="state.isFetching"
        [authoritiesDelete]="['ROLE_SYSTEM_ADMIN']"
        [deleteButton]="true"
        [inactiveBtn]="true"
        [actionTemplate]="actionsTpl"
        [hideButtonHeader]="true"
        [filterTemplate]="filterTemplate"
        [selectionMode]="null"
        name=""
    >
        <ng-template #filterTemplate>
            <tr>
                <th [appFilter]="[tableId, 'softwareName']">
                    <app-filter-table [tableId]="tableId" field="softwareName"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'versionName']">
                    <app-filter-table [tableId]="tableId" field="versionName"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'path']">
                    <app-filter-table [tableId]="tableId" field="path"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'buildTime']">
                    <app-filter-table [tableId]="tableId" field="buildTime"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'md5']">
                    <app-filter-table [tableId]="tableId" field="md5"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'userGuide']">
                    <app-filter-table [tableId]="tableId" field="userGuide"></app-filter-table>
                </th>
                <th></th>
            </tr>
        </ng-template>
        <ng-template #templateFile let-rowData>
            <a *ngIf="rowData.path" [href]="STORAGE_BASE_URL + '/' + rowData.path" rel="noopener" target="_blank">
                {{ rowData.fileName }}
            </a>
        </ng-template>

        <ng-template #templateInstruction let-rowData>
            <a *ngIf="rowData.userGuide" [href]="STORAGE_BASE_URL + '/' + rowData.userGuide" rel="noopener" target="_blank">
                {{ rowData.instructionName }}
            </a>
        </ng-template>
        <ng-template #actionsTpl let-row>
            <td style="white-space: nowrap; text-align: center">
                <p-menu #ActionMenu [popup]="true" [model]="itemsAction" appendTo="body">
                    <ng-template let-item pTemplate="item">
                        <ng-container *appHasAnyAuthority="item.authorities">
                            <div class="flex items-center gap-2 px-3 py-2 hover:bg-gray-100 rounded cursor-pointer">
                                <i [ngClass]="item.icon" class="text-gray-600"></i>
                                <span>{{ item.label }}</span>
                            </div>
                        </ng-container>
                    </ng-template>
                </p-menu>
                <!-- Nút bật menu -->
                <button
                    pButton
                    type="button"
                    icon="pi pi-ellipsis-h"
                    class="p-button-text p-button-sm"
                    (click)="onMenuClick($event, row)"
                    title="Thao tác"
                ></button>
            </td>
        </ng-template>
    </app-table-common>

    <!-- popup-Edit -->
    <app-popup
        #EditPopup
        [dialogWidth]="'60vw'"
        [header]="title"
        [isButtonVisible]="isButtonVisible"
        [formGroup]="formEditPopup"
        (onSubmit)="submitFormEdit($event)"
        (onClose)="handlePopupClose()"
    >
        <app-form [formGroup]="formEditPopup" styleClass="tw-grid tw-grid-cols-1 tw-gap-4" *ngIf="formEditPopup">
            <div class="tw-grid tw-grid-cols-2 tw-gap-4">
                <app-custom-form-item label="File" [control]="formEditPopup.get('file')">
                    <app-upload-custom
                        [loading]="loadingSoftware"
                        [filePath]="formEditPopup.get('path')?.value"
                        #SoftwareUploader
                        accept=".zip,.rar,.bin,.exe"
                        [limit]="1"
                        (onChange)="handleChangeFile($event, 'SOFTWARE_RESOURCE')"
                        (onClear)="handleClearFile('SOFTWARE_RESOURCE')"
                        [fileNamePattern]="fileNamePattern"
                        [strictFileNamePattern]="false"
                        fileNamePatternMessage="Tên file không đúng format, vui lòng kiểm tra lại theo format Tên sản phẩm_Tên phần mềm_Version_Buildtime"
                    ></app-upload-custom>
                </app-custom-form-item>

                <app-custom-form-item class="tw-break-normal" label="Version">
                    <!-- <span class="tw-leading-8"> {{ formEditPopup.get('versionName')?.value || '' }}</span> -->
                    <input
                        *ngIf="formEditPopup.get('file')?.value"
                        type="text"
                        pInputText
                        class="tw-w-full"
                        [formControl]="formEditPopup.get('versionName')"
                        placeholder="Nhập version"
                    />
                </app-custom-form-item>
                <app-custom-form-item class="tw-break-normal" label="Tên Phần mềm">
                    <!-- <span class="tw-leading-8"> {{ formEditPopup.get('name')?.value || '' }} </span> -->
                    <input
                        *ngIf="formEditPopup.get('file')?.value"
                        type="text"
                        pInputText
                        class="tw-w-full"
                        [formControl]="formEditPopup.get('name')"
                        placeholder="Nhập tên phần mềm"
                    />
                </app-custom-form-item>
                <app-custom-form-item class="tw-break-normal" label="Build time">
                    <!-- <span class="tw-leading-8"> {{ formEditPopup.get('buildTime')?.value || '' }}</span> -->
                    <input
                        *ngIf="formEditPopup.get('file')?.value"
                        type="text"
                        pInputText
                        class="tw-w-full"
                        [formControl]="formEditPopup.get('buildTime')"
                        placeholder="Nhập build time"
                    />
                </app-custom-form-item>
                <app-custom-form-item label="Hướng dẫn vận hành" [control]="formEditPopup.get('instructionName')">
                    <app-upload-custom
                        [loading]="loadingInstruction"
                        [filePath]="formEditPopup.get('userGuide')?.value"
                        #DocUploader
                        [limit]="1"
                        [fileNamePattern]="fileNamePattern"
                        fileNamePatternMessage="Tên file không đúng format, vui lòng kiểm tra lại theo format Tên sản phẩm_Tên tài liệu_Version_Buildtime"
                        (onChange)="handleChangeFile($event, 'SOFTWARE_INSTRUCTION')"
                        (onClear)="handleClearFile('SOFTWARE_INSTRUCTION')"
                    ></app-upload-custom>
                </app-custom-form-item>
                <app-custom-form-item class="tw-break-normal" label="MD5">
                    <span class="tw-leading-8"> {{ formEditPopup.get('md5')?.value || '' }}</span>
                </app-custom-form-item>
            </div>
            <app-custom-form-item label="Sản phẩm áp dụng: "></app-custom-form-item>
            <div class="tw-grid tw-grid-cols-2 tw-gap-4">
                <app-custom-form-item label="Dòng sản phẩm" [control]="formEditPopup.get('productLines')">
                    <app-combobox-nonRSQL
                        #productLinesPopup
                        [fetchOnInit]="true"
                        type="select"
                        formControlName="productLines"
                        fieldValue="id"
                        (panelShow)="handlePanelShow(productLinesPopup)"
                        fieldLabel="name"
                        url="/pr/api/product-line/filter"
                        param="name"
                        placeholder="Chọn dòng sản phẩm"
                        [additionalParams]="{ unpaged: false }"
                    >
                    </app-combobox-nonRSQL>
                </app-custom-form-item>
                <app-custom-form-item label="Sản phẩm" [control]="formEditPopup.get('productIds')">
                    <app-combobox-nonRSQL
                        #productIdsPopup
                        [fetchOnInit]="false"
                        type="select"
                        formControlName="productIds"
                        fieldValue="id"
                        (panelShow)="handlePanelShow(productIdsPopup)"
                        [fieldLabel]="['name', 'vnptManPn']"
                        url="/pr/api/product"
                        param="searchNameOrManPn"
                        placeholder="Chọn sản phẩm"
                        [additionalParams]="{ unpaged: false, modelId: 0, customerId: 0, lineId: formEditPopup.get('productLines')?.value }"
                    >
                    </app-combobox-nonRSQL>
                </app-custom-form-item>
            </div>
        </app-form>
    </app-popup>
    <!-- popup theo dõi lịch sử thay đổi -->
    <app-popup #HistoryPopup header="Theo dõi lịch sử cập nhật phần mềm" [isButtonVisible]="false" dialogWidth="80vw" [showConfirmButton]="false">
        <app-table-custom [columns]="trackCols" [data]="trackData">
            <ng-template TableCell="details" let-row>
                <a
                    *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'software_applied_profiles']"
                    href="#"
                    (click)="openProfileList(row); $event.preventDefault()"
                    style="text-decoration: underline; cursor: pointer"
                >
                    Xem danh sách
                </a>
            </ng-template>
            <ng-template TableCell="userGuide" let-row>
                <a [href]="STORAGE_BASE_URL + '/' + row.userGuide" rel="noopener" target="_blank">
                    {{ row.instructionName }}
                </a>
            </ng-template>

            Theo dõi lịch sử cập nhật phần mềm
        </app-table-custom>
    </app-popup>

    <!-- popup danh sách hồ sơ áp dụng -->
    <app-popup #ListProfilePopup header="Danh sách hồ sơ áp dụng" [isButtonVisible]="false" dialogWidth="60vw" [showConfirmButton]="false">
        <app-table-custom [columns]="profileCols" [data]="profileData">
            <ng-template TableCell="action" let-row>
                <a
                    [routerLink]="['/pms/product-file/view', row.productId, row.productVersion]"
                    target="_blank"
                    rel="noopener"
                    style="text-decoration: underline; cursor: pointer"
                >
                    Mở hồ sơ
                </a>
            </ng-template>
        </app-table-custom>
    </app-popup>

    <app-popup
        #SubmitChangeVersion
        [dialogWidth]="'50vw'"
        header="Chọn hồ sơ cập nhật version phần mềm"
        [isButtonVisible]="isButtonVisible"
        (onSubmit)="submitChangeVersion()"
        (onClose)="closeChangeVersionPopup()"
    >
        <p-table [value]="dataTableVersionCols" [(selection)]="selectedRowsToSubmit" [tableStyle]="{ 'min-width': '50rem' }">
            <ng-template pTemplate="header">
                <tr>
                    <th style="width: 4rem">
                        <p-tableHeaderCheckbox />
                    </th>
                    <th *ngFor="let row of tableVersionCols">{{ row.header }}</th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-rowData>
                <tr>
                    <td>
                        <p-tableCheckbox [value]="rowData" />
                    </td>
                    <td *ngFor="let col of tableVersionCols">
                        <ng-container [ngSwitch]="col.field">
                            <ng-container *ngSwitchCase="'lifeCycleStageSelect'">
                                <p-dropdown
                                    [options]="stageKeys"
                                    optionLabel="name"
                                    optionValue="id"
                                    [(ngModel)]="rowData.lifeCycleStageSelect"
                                    [filter]="true"
                                    filterBy="name"
                                    [showClear]="true"
                                    appendTo="body"
                                    placeholder="Chọn LifecycleStage"
                                ></p-dropdown>
                            </ng-container>
                            <ng-container *ngSwitchDefault>
                                {{ col.field === 'lifecycleStage' ? mapLifecycle(rowData[col.field]) : rowData[col.field] }}
                            </ng-container>
                        </ng-container>
                    </td>
                </tr>
            </ng-template>
        </p-table>
    </app-popup>
</div>
