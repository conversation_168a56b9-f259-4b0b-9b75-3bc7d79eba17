import { Component, ElementRef, ViewChild } from '@angular/core';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { Form<PERSON>uilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { GeneralReportService } from '../../../../../services/smart-qc/report/general.report.service';
import { LoadingService } from '../../../../../shared/services/loading.service';
import { TableModule } from 'primeng/table';
import { CalendarModule } from 'primeng/calendar';
import { finalize } from 'rxjs';
import { AuthService } from '../../../../../core/auth/auth.service';
import Common from '../../../../../utils/common';
import { DialogModule } from 'primeng/dialog';
import { environment } from '../../../../../../environments/environment';
import { EventChangeFilter } from '../../../../../models/interface';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { TabViewModule } from 'primeng/tabview';
import Chart from 'chart.js/auto';
import { NgIf } from '@angular/common';
import { LogisticsReportService } from 'src/app/services/sc/logistics/logistics-report.service';
import { RouterLink } from '@angular/router';
import {ShippingMethodService} from "../../../../../services/sc/bo/shipping-method.service";
import { isArray } from 'lodash';
import {InternationalRateUnit, ShippingMethod} from "../../../../../models/interface/sc";
import {AlertService} from "../../../../../shared/services/alert.service";
import {InternationalRateUnitService} from "../../../../../services/sc/logistics/international-rate-unit.service";

@Component({
    selector: 'logistict-report',
    templateUrl: './price-report.component.html',
    standalone: true,
    imports: [
        NgIf,
        RouterLink,
        DropdownModule,
        TabViewModule,
        InputTextModule,
        InputTextareaModule,
        ReactiveFormsModule,
        ButtonModule,
        TableModule,
        CalendarModule,
        DialogModule,
        TableCommonModule,
        SubHeaderComponent,
    ],
    providers: [LogisticsReportService, ShippingMethodService, InternationalRateUnitService],
})
export class ReportComponent {
    reportForm: FormGroup;
    data: any;
    itemsHeader = [{ label: 'Báo cáo thống kê' }, { label: 'Thống kê biến động giá hàng nhập', url: '' }];

    constructor(
        private formBuilder: FormBuilder,
        private logisticsReportService: LogisticsReportService,
        private loadingService: LoadingService,
        protected authService: AuthService,
        private shippingMethodService: ShippingMethodService,
        private rateUnitService: InternationalRateUnitService,
        private alertService: AlertService,
    ) {
        this.initForm();
    }

    initForm() {
        this.reportForm = this.formBuilder.group({
            logisticId: [null, []],
            shippingMethodId: [null, [Validators.required]],
            rateUnitId: [null, [Validators.required]],
            startDate: [null, []],
            endDate: [null, []],
            deliveryCondition: [null, []],
        });

        this.reportForm.get('rateUnitId')?.valueChanges.subscribe((newValue) => {
            if (newValue == null) {
                this.data = null;
                if (this.chart) {
                    this.chart.destroy();
                }
            }
        });


        // Get Air option
        this.shippingMethodService.getPage(`query=name==AIR`).subscribe({
            next: (res) => {
                if (isArray(res.body) && res.body.length > 0) {
                    const airMethod: ShippingMethod = res.body[0];
                    this.reportForm.patchValue({
                        shippingMethodId: airMethod.id,
                    });
                }
                setTimeout(() => {
                    this.reportForm.get('shippingMethodId')?.valueChanges.subscribe((newValue) => {
                        this.reportForm.patchValue({
                            rateUnitId: null,
                        });
                    });
                }, 2000)
            },
        });

        this.rateUnitService.getPage(`query=name=='%3C%2045%20KG'`).subscribe({
            next: (res) => {
                console.log(res)
                if (isArray(res.body) && res.body.length > 0) {
                    const rateUnit: InternationalRateUnit = res.body[0];
                    this.reportForm.patchValue({
                        rateUnitId: rateUnit.id,
                    });
                }
            },
        });
    }

    onSelectFirstUnitDone(data) {
        this.reportForm.patchValue({
            rateUnitId: data[0].id,
        });
    }

    pageable = {
        size: 5,
        page: 0,
        first: 0,
    };

    logEvent(e) {
        this.pageable.page = e.first / e.rows;
    }

    getReport() {
        this.pageable.first = 0;
        if (this.reportForm.valid) {
            let startDate = this.reportForm.get('startDate').value != null ? Common.getTimeFromHCMTimeZone(this.reportForm.get('startDate').value) : null;
            let endDate = this.reportForm.get('endDate').value != null ? Common.getTimeFromHCMTimeZone(this.reportForm.get('endDate').value) : null;

            if (startDate != null && endDate != null && endDate < startDate) {
                this.alertService.error('Ngày Đến phải lớn hơn ngày Từ');
                return;
            }

            this.loadingService.show();
            this.logisticsReportService
                .getPriceChangeReport({ ...this.reportForm.getRawValue(), startTime: startDate, endTime: endDate })
                .pipe(
                    finalize(() => {
                        this.loadingService.hide();
                    }),
                )
                .subscribe({
                    next: (res: any) => {
                        this.data = res;
                        this.genNewChart([...this.data]);
                    },
                    complete: () => {},
                });
        } else {
            Common.markAllAsTouchedForm(this.reportForm);
        }
    }

    @ViewChild('chartCanvas') chartCanvas: ElementRef<HTMLCanvasElement>;
    chart: Chart;
    chartData: unknown = [];

    genNewChart(data) {
        if (this.chart) this.chart.destroy();
        if (data == null || data.length == 0) return;

        const labels = data.map((e) => e.boCode);
        const dataset = data.map((e) => (e.internationalPrice != null ? e.internationalPrice : 0));

        this.chart = new Chart(this.chartCanvas.nativeElement, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: '',
                        data: dataset,
                        fill: false,
                        borderColor: 'rgb(75, 192, 192)',
                        tension: 0.1,
                        backgroundColor: 'rgba(75, 192, 192, 0.2)', // Fill under line
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(75, 192, 192, 1)', // filled color
                        pointBorderColor: 'rgba(75, 192, 192, 1)',
                        pointRadius: 4,
                    },
                ],
            },
            options: {
                plugins: {
                    legend: {
                        display: false, // Displays legend entries
                    },
                    title: {
                        display: true, // Enable the title
                        text: 'Biến động giá dịch vụ', // The title text
                        font: {
                            size: 18, // Font size of the title
                            weight: 'bold', // Font weight of the title
                            family: 'Arial', // Font family
                        },
                        padding: {
                            top: 10, // Padding from the top
                            bottom: 30, // Padding from the bottom
                        },
                    },
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'BO',
                            padding: { top: 30 },
                        },
                        ticks: {
                            maxRotation: 45, // Rotate labels at 45 degrees
                            minRotation: 45,
                            padding: 10,
                            callback: function (value) {
                                const splitLabel = labels[value].split(/(\b\d{2}\/\d{2}\/\d{4}\b)/);

                                if (splitLabel.length > 1) {
                                    return [splitLabel[0], splitLabel.slice(1).join('')].filter((part) => part.trim() !== ''); // Return first and date as separate lines
                                }

                                // Remove empty strings and return as an array for multi-line labels
                                return labels[value];
                            },
                        },
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Cước phí (USD)',
                        },
                        beginAtZero: true,
                    },
                },
            },
        });
    }

    getActionInfo(row, actionName, key) {
        const actionInfo = row.actionRateInfos.find((info) => info.actionName === actionName);
        if (!actionInfo) {
            if (key === 'completeQuantity') {
                return '0';
            }
            if (key === 'rateComplete') {
                return '0%';
            }
        }
        if (key === 'rateComplete') {
            return actionInfo[key] + '%';
        }
        return actionInfo[key];
    }
}
