<app-sub-header [items]="[{ label: 'Quản lý thông tin mua hàng' }, { label: 'Quản lý SC BOM' }]" [action]="actionHeader"></app-sub-header>

<ng-template #actionHeader>
    <div *ngIf="selectedTab === 0">
        <p-button (click)="goToCreateBomPage()" label="Tạo mới" severity="primary" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_bom_edit']" />
    </div>
    <div *ngIf="selectedTab === 1">
        <p-button (click)="isOpenAddModal = true" label="Tạo mới" severity="primary" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_bom_edit']" />
    </div>

    <!--<app-popup
        header="Xuất danh sách PO"
        label="Xuất danh sách"
        (onSubmit)="exportExcel($event)"
        typePopup="download"
        severity="success"
    ></app-popup>-->
</ng-template>
<div style="padding: 1rem 1rem 0 1rem">
    <p-tabView [(activeIndex)]="selectedTab" (activeIndexChange)="handleTabChange($event)">
        <p-tabPanel header="SC BOM">
            <app-table-common
                [tableId]="bomTableId"
                [columns]="bomColumns"
                [data]="bomState.data"
                [loading]="bomState.isFetching"
                [filterTemplate]="filterBomTemplate"
                name="Danh sách SC BOM"
                [funcDelete]="deleteBom"
                [authoritiesDelete]="['ROLE_SYSTEM_ADMIN', 'sc_po_delete']"
            >
                <ng-template #filterBomTemplate>
                    <tr>
                        <th></th>
                        <th [appFilter]="[bomTableId, 'code']">
                            <app-filter-table [tableId]="bomTableId" field="code"></app-filter-table>
                        </th>
                        <th [appFilter]="[bomTableId, 'accountingCode']">
                            <app-filter-table [tableId]="bomTableId" field="accountingCode"></app-filter-table>
                        </th>
                        <th [appFilter]="[bomTableId, 'rdBom']">
                            <app-filter-table [tableId]="bomTableId" field="rdBom"></app-filter-table>
                        </th>
                        <th [appFilter]="[bomTableId, 'note']">
                            <app-filter-table [tableId]="bomTableId" field="note"></app-filter-table>
                        </th>
                        <th [appFilter]="[bomTableId, 'bomCost']">
                            <app-filter-table [tableId]="bomTableId" field="bomCost" type="number"></app-filter-table>
                        </th>
                        <th [appFilter]="[bomTableId, 'created']" style="max-width: 8rem">
                            <app-filter-table
                                [tableId]="bomTableId"
                                [rsql]="true"
                                field="created"
                                type="date-range"
                                placeholder="Thời gian tạo"
                            ></app-filter-table>
                        </th>
                        <th [appFilter]="[bomTableId, 'createdBy']">
                            <app-filter-table [tableId]="bomTableId" field="createdBy"></app-filter-table>
                        </th>
                        <th [appFilter]="[bomTableId, 'status']">
                            <app-filter-table
                                [tableId]="bomTableId"
                                field="status"
                                type="select-one"
                                [rsql]="true"
                                [configSelect]="{
                                    fieldValue: 'value',
                                    fieldLabel: 'label',
                                    options: bomStatus,
                                }"
                            ></app-filter-table>
                        </th>
                        <th>Nháp</th>
                        <th>Mới</th>
                        <th>Đang xử lý</th>
                        <th>Hoàn thành</th>
                    </tr>
                </ng-template>
                <ng-template #templateStatus let-rowData>
                    <p-tag [style]="{ width: '100%' }" [severity]="getSeverityBomStatus(rowData.status)" [value]="getBomStatusLabel(rowData.status)"></p-tag>
                </ng-template>

                <ng-template #templatePoLinks let-rowData>
                    <td class="tw-whitespace-nowrap" style="min-width: 8rem">
                        <ng-container *ngIf="rowData.poGroups && rowData.poGroups[-1] && rowData.poGroups[-1].length > 0; else noPo">
                            <span *ngFor="let item of rowData.poGroups[-1]">{{ item.orderNo }}, </span>
                        </ng-container>
                    </td>
                    <td class="tw-whitespace-nowrap" style="min-width: 8rem">
                        <ng-container *ngIf="rowData.poGroups && rowData.poGroups[0] && rowData.poGroups[0].length > 0; else noPo">
                            <a target="_blank" rel="noopener noreferrer" *ngFor="let item of rowData.poGroups[0]" [routerLink]="['/sc/po', item.id]"
                                >{{ item.orderNo }},
                            </a>
                        </ng-container>
                    </td>
                    <td class="tw-whitespace-nowrap" style="min-width: 8rem">
                        <ng-container *ngIf="rowData.poGroups && rowData.poGroups[1] && rowData.poGroups[1].length > 0; else noPo">
                            <a target="_blank" rel="noopener noreferrer" *ngFor="let item of rowData.poGroups[1]" [routerLink]="['/sc/po', item.id]"
                                >{{ item.orderNo }},
                            </a>
                        </ng-container>
                    </td>
                    <td class="tw-whitespace-nowrap" style="min-width: 8rem">
                        <ng-container *ngIf="rowData.poGroups && rowData.poGroups[3] && rowData.poGroups[3].length > 0; else noPo">
                            <a target="_blank" rel="noopener noreferrer" *ngFor="let item of rowData.poGroups[3]" [routerLink]="['/sc/po', item.id]"
                                >{{ item.orderNo }},
                            </a>
                        </ng-container>
                    </td>
                </ng-template>
                <ng-template #noPo>-</ng-template>
            </app-table-common>
        </p-tabPanel>
        <p-tabPanel header="Đơn nháp">
            <app-table-common
                [tableId]="poDraftTableId"
                [columns]="poDraftColumns"
                [data]="poDraftState.data"
                [loading]="poDraftState.isFetching"
                [filterTemplate]="filterDraftTemplate"
                name="Danh sách đơn nháp"
                [funcDelete]="deletePoDraft"
                [authoritiesDelete]="['ROLE_SYSTEM_ADMIN', 'sc_po_delete']"
            >
                <ng-template #filterDraftTemplate>
                    <tr>
                        <th></th>
                        <th [appFilter]="[poDraftTableId, 'code']">
                            <app-filter-table [tableId]="poDraftTableId" field="code"></app-filter-table>
                        </th>
                        <th [appFilter]="[poDraftTableId, 'bomCodes']">
                            <app-filter-table [tableId]="poDraftTableId" field="bomCodes"></app-filter-table>
                        </th>
                        <th [appFilter]="[poDraftTableId, 'note']">
                            <app-filter-table [tableId]="poDraftTableId" field="note"></app-filter-table>
                        </th>
                        <th [appFilter]="[poDraftTableId, 'created']" style="max-width: 8rem">
                            <app-filter-table
                                [tableId]="poDraftTableId"
                                [rsql]="true"
                                field="created"
                                type="date-range"
                                placeholder="Thời gian tạo"
                            ></app-filter-table>
                        </th>
                        <th [appFilter]="[poDraftTableId, 'createdBy']">
                            <app-filter-table [tableId]="poDraftTableId" field="createdBy"></app-filter-table>
                        </th>
                        <th [appFilter]="[poDraftTableId, 'status']">
                            <app-filter-table
                                [tableId]="poDraftTableId"
                                field="status"
                                type="select-one"
                                [rsql]="true"
                                [configSelect]="{
                                    fieldValue: 'value',
                                    fieldLabel: 'label',
                                    options: poDraftStatus,
                                }"
                            ></app-filter-table>
                        </th>
                    </tr>
                </ng-template>
                <ng-template #templatePoDraftStatus let-rowData>
                    <p-tag
                        [style]="{ width: '100%' }"
                        [severity]="getSeverityPoDraftStatus(rowData.status)"
                        [value]="getPoDraftStatusLabel(rowData.status)"
                    ></p-tag>
                </ng-template>
            </app-table-common>
        </p-tabPanel>
    </p-tabView>
</div>

<p-dialog [style]="{ minWidth: '500px' }" [(visible)]="isOpenAddModal" [modal]="true" [closable]="true" [header]="'Tạo mới đơn nháp'">
    <div class="tw-mt-1">
        <app-form #form [formGroup]="poDraftFormGroup" layout="horizontal" (onSubmit)="onSubmitCreate()">
            <app-form-item label="Mã đơn nháp" isRequired="true">
                <input type="text" class="tw-w-full" pInputText formControlName="code" placeholder="Hệ thống tự sinh" />
            </app-form-item>
            <br />
            <app-form-item label="Ghi chú">
                <textarea placeholder="Ghi chú" rows="5" formControlName="note" pInputTextarea class="tw-w-full"></textarea>
            </app-form-item>
        </app-form>
        <div class="tw-flex tw-justify-end tw-mt-4 tw-space-x-3">
            <p-button label="Đóng" (click)="isOpenAddModal = false" severity="secondary" size="small"></p-button>
            <p-button label="Lưu" (click)="form.handleSubmit()" severity="primary" size="small"></p-button>
        </div>
    </div>
</p-dialog>
