<div>
    <p-panel header="Thông tin chung" [toggleable]="true">
        <div [formGroup]="rfqForm">
            <div class="tw-grid tw-grid-cols-2 tw-gap-4">
                <div>
                    <div class="tw-font-bold tw-mb-3">Mã</div>
                    <input
                        type="text"
                        placeholder="Hệ thống tự sinh"
                        class="tw-w-full"
                        pInputText
                        formControlName="code"
                    />
                </div>
                <div>
                    <div class="tw-font-bold tw-mb-3">Tên đợt hỏi giá</div>
                    <app-editable-input
                        [control]="rfqForm.get('name')"
                        type="input"
                        placeholder="Tên đợt hỏi giá"
                        [trim]="true"
                        (save)="saveGeneralInfo()"
                        fieldName="tên đợt hỏi giá"
                        [inTable]="false"
                    >
                    </app-editable-input>
                </div>
            </div>
            <br />
            <div class="tw-grid tw-grid-cols-2 tw-gap-4">
                <div>
                    <div class="tw-font-bold tw-mb-3">Ghi chú</div>
                    <app-editable-input
                        [control]="rfqForm.get('note')"
                        type="textarea"
                        placeholder="Ghi chú"
                        [trim]="true"
                        (save)="saveGeneralInfo()"
                        fieldName="ghi chú"
                        [inTable]="false"
                    >
                    </app-editable-input>
                </div>
                <div>

                </div>
            </div>
        </div>
    </p-panel>
    <br />
    <p-panel header="Thông tin dự án" [toggleable]="true">
        <div>
            <p-table
                [value]="rfqItemsDisplay"
                styleClass="p-datatable-gridlines"
                [scrollable]="true"
                scrollHeight="700px"
                [resizableColumns]="true"
            >
                <ng-template pTemplate="header">
                    <tr>
                        <th rowspan="2" style="">STT</th>
                        <th rowspan="2" style="">Dự án</th>
                        <th rowspan="2" style="">Mã kế toán</th>
                        <th rowspan="2" style="">Sản phẩm</th>
                        <th rowspan="2" style="">Tên sản phẩm viết tắt</th>
                        <th rowspan="2" style="">Số lượng</th>
                        <th [attr.colspan]="sortedUniqueTimes.length" style="text-align: center;">
                            Thời gian yêu cầu
                        </th>
                        <th rowspan="2" style="">Version BOM</th>
                    </tr>
                    <tr>
                        <th *ngFor="let time of sortedUniqueTimes">
                            {{ time | date:'dd/MM/yyyy' }}
                        </th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                    <tr>
                        <td style="">{{ rowIndex + 1 }}</td>
                        <td style="">{{ item.project }}</td>
                        <td style="">{{ item.accountingCode }}</td>
                        <td style="">{{ item.productName }}</td>
                        <td style="">{{ item.productShortName }}</td>
                        <td style="">{{ item.quantity }}</td>
                        <td *ngFor="let time of sortedUniqueTimes">
                            {{ item?.type === RFQ_ITEM_TYPE.SC_BOM? getQuantity(item, time) : '' }}
                        </td>
                        <td style="">{{ item.bomVersion }}</td>
                    </tr>
                </ng-template>
            </p-table>
        </div>
    </p-panel>
    <br />
    <p-panel header="Thông tin linh kiện" [toggleable]="true">
        <p-tabView>
            <p-tabPanel header="Danh sách linh kiện">
                <div class="tw-flex tw-space-x-3 tw-items-center">
                    <p-checkbox
                        id="selectNotWarning"
                        name="selectNotWarning"
                        [(ngModel)]="showOnlyCheckNccTrue"
                        [binary]="true"
                        inputId="selectNotWarning"
                        (onChange)="onToggleShowOnlyCheckNccTrue($event.checked)">
                    ></p-checkbox>
                    <div>Chọn những level có số lượng NCC <= 3</div>
                </div>
                <br/>
                <div>
                    <p-table
                        [value]="materials?.controls"
                        styleClass="p-datatable-gridlines"
                        [scrollable]="true"
                        scrollHeight="700px"
                        [resizableColumns]="true"
                        >
                        <ng-template pTemplate="header">
                            <tr>
                                <th rowspan="2">STT</th>
                                <th rowspan="2">Level</th>
                                <th rowspan="2" style="min-width: 200px">Hạng mục</th>
                                <th rowspan="2">Mã nhà sản xuất</th>
                                <th rowspan="2">Mô tả</th>
                                <th rowspan="2">Nhà sản xuất</th>
                                <th rowspan="2">Tổng số lượng yêu cầu</th>
                                <th rowspan="2">Tồn khả dụng</th>
                                <th rowspan="2">Tổng số lượng đặt hàng</th>
                                <th [attr.colspan]="sortedUniqueTimesMaterials.length" style="text-align: center;">
                                    Lịch giao hàng yêu cầu
                                </th>
                                <th rowspan="2" style="min-width: 200px">Yêu cầu về datecode</th>
                                <th rowspan="2">Danh sách NCC dự kiến hỏi giá</th>
                                <th rowspan="2" style="min-width: 200px">Ghi chú</th>
                            </tr>
                            <tr>
                                <th *ngFor="let time of sortedUniqueTimesMaterials">
                                    {{ time | date:'dd/MM/yyyy' }}
                                </th>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="body" let-material let-rowIndex="rowIndex">
                            <tr
                                [style.display]="showOnlyCheckNccTrue && material.get('checkNcc')?.value === true ? 'none' : ''"
                                [ngClass]="{ 'tw-bg-yellow-100': material.get('checkNcc')?.value === false }">
                                <td>{{ getVisibleIndex(rowIndex) }}</td>
                                <td>
                                    <span *ngFor="let level of material.get('levels')?.value">
                                      <span [title]="material.get('checkNcc')?.value === false ? 'Tổng NCC dự kiến của level nhỏ hơn 3' : null">
                                          {{ level }}
                                      </span><br>
                                    </span>
                                </td>
                                <td>
                                    <app-editable-input
                                        [control]="material.get('section')"
                                        type="input"
                                        placeholder="Hạng mục"
                                        [trim]="true"
                                        (save)="saveMaterialData(material)"
                                        fieldName="hạng mục"
                                        [inTable]="true"
                                    >
                                    </app-editable-input>
                                </td>
                                <td>{{ material.get('manPn')?.value }}</td>
                                <td>{{ material.get('description')?.value }}</td>
                                <td>{{ material.get('manufacturerName')?.value }}</td>
                                <td>{{ roundQuantity(material.get('requestQuantity')?.value) }}</td>
                                <td>{{ roundQuantity(material.get('availableQuantity')?.value) }}</td>
                                <td>{{ roundQuantity(material.get('orderQuantity')?.value) }}</td>
                                <td *ngFor="let time of sortedUniqueTimesMaterials">
                                    {{ getMaterialQuantity(material, time) }}
                                </td>
                                <td>
                                    <app-editable-input
                                        [control]="material.get('dateCode')"
                                        type="input-number"
                                        placeholder="Yêu cầu về datecode"
                                        [trim]="true"
                                        (save)="saveMaterialData(material)"
                                        fieldName="yêu cầu về datecode"
                                        [inTable]="true"
                                    >
                                    </app-editable-input>
                                </td>
                                <td>{{ material.get('providers')?.value }}</td>
                                <td>
                                    <app-editable-input
                                        [control]="material.get('note')"
                                        type="input"
                                        placeholder="Ghi chú"
                                        [trim]="true"
                                        (save)="saveMaterialData(material)"
                                        fieldName="ghi chú"
                                        [inTable]="true"
                                    >
                                    </app-editable-input>
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                </div>
            </p-tabPanel>
            <p-tabPanel header="Danh sách Nhà cung cấp">
                <div>
                    <div>
                        <app-table-common
                            [tableId]="tableId"
                            [columns]="columns"
                            [data]="state.data"
                            [loading]="state.isFetching"
                            [funcDelete]="null"
                            name="Danh sách Nhà cung cấp"
                            [filterTemplate]="filterTemplate"
                            [authoritiesDelete]="['ROLE_SYSTEM_ADMIN', 'sc_po_delete']"
                        >
                            <ng-template #filterTemplate>
                                <tr>
                                    <th></th>
                                    <th [appFilter]="[tableId, 'name']">
                                        <app-filter-table [tableId]="tableId" field="name"></app-filter-table>
                                    </th>
                                    <th [appFilter]="[tableId, 'shortName']">
                                        <app-filter-table [tableId]="tableId" field="shortName"></app-filter-table>
                                    </th>
                                    <th [appFilter]="[tableId, 'rate']">
                                        <app-filter-table [tableId]="tableId" field="rate"></app-filter-table>
                                    </th>
                                    <th></th>
                                    <th [appFilter]="[tableId, 'note']">
                                        <app-filter-table [tableId]="tableId" field="note"></app-filter-table>
                                    </th>
                                </tr>
                            </ng-template>
                            <ng-template #templateNote let-rowData>
                                <div>
                                    <!--<input type="text" pInputText (keydown)="onKeyPress($event)" />-->
                                    <app-editable-input
                                        [data]="rowData.note"
                                        type="input"
                                        placeholder="Ghi chú"
                                        [trim]="true"
                                        [inTable]="true"
                                        (save)="saveMaterialSupplierNote(rowData, $event)"
                                        fieldName="ghi chú"
                                    >
                                    </app-editable-input>
                                </div>
                            </ng-template>
                            <ng-template #templateTotalRevenue let-rowData>
                                <div>
                                    {{ getTotalRevenue(rowData.unitPrice, rowData.totalRevenue) }}
                                </div>
                            </ng-template>
                        </app-table-common>
                    </div>
                    <br />
                    <div class="text-blue-500 tw-italic">Tích chọn Nhà cung cấp dự kiến hỏi giá</div>
                </div>
            </p-tabPanel>
        </p-tabView>
        <div class="tw-flex tw-justify-end">
            <app-popup
                header="Xuất danh mục hỏi giá"
                label="Xuất danh mục hỏi giá"
                *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_supplier_export']"
                (onSubmit)="exportMaterial($event)"
                typePopup="download"
                severity="success"
            ></app-popup>
        </div>
    </p-panel>
    <br />
    <!--<div class="tw-flex tw-justify-end">
        <p-button label="Xuất danh mục hỏi giá" icon="pi pi-download" severity="success"></p-button>
    </div>-->
</div>
