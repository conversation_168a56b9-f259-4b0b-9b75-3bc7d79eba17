<app-sub-header [items]="itemsHeader" [action]="actionHeader">
    <ng-template #actionHeader>
        <ng-container *ngIf="!editMode && userId">
            <p-button
                size="small"
                label="Sửa"
                routerLink="edit"
                severity="success"
            />
        </ng-container>
        <ng-container *ngIf="editMode || !userId">
            <p-button
                size="small"
                label="Lưu"
                (click)="form.handleSubmit()"
                severity="success"
                [disabled]="formGroup.invalid"
            />
        </ng-container>
        <p-button *ngIf="!editMode" size="small" routerLink="/administration/user" severity="secondary" label="Hủy"/>
        <p-button
            *ngIf="editMode"
            size="small"
            [routerLink]="'/administration/user/' + userId"
            severity="secondary"
            label="Hủy"
        />
    </ng-template>
</app-sub-header>

<div class="tw-p-4">
    <app-form #form  [formGroup]="formGroup" layout="vertical" (onSubmit)="onSubmit($event)">

        <div class="tw-p-4 tw-gap-4 tw-mx-auto tw-bg-white tw-rounded-md" style="max-width: 850px">
            <div class="tw-flex tw-justify-between tw-gap-4 tw-w-full tw-mb-5">
                <p-avatar [label]="formGroup.get('fullName')?.value?.[0] || ''" styleClass="mr-2" size="xlarge"
                          shape="circle"/>

                <div *ngIf="!editMode && userId">
                    <div
                        style="
                            display: flex;
                            background-color: #f5f5f5 !important;
                            border-right: 1px solid #e0e0e0;
                            padding: 2px 8px !important;
                            border-radius: 8px;
                        "
                    >
                        <p-inputSwitch
                            formControlName="active"
                            [ngClass]="iconClass"
                            (click)="setActiveStatus()"
                            [readonly]="true"
                        />
                        <span [ngStyle]="activeClass" style="margin-left: 4px; margin-top: 4px">{{ title }}</span>
                    </div>
                </div>
            </div>

            <div *ngIf="!editMode && userId" class="tw-grid md:tw-grid-cols-6 tw-grid-cols-2 tw-gap-4 tw-w-full">
                <b>Họ và tên:</b>
                <span class="md:tw-col-span-2 tw-col-span-1">{{ formGroup.get('fullName')?.value }}</span>
                <b>Email:</b>
                <span style="text-wrap: wrap"
                      class="md:tw-col-span-2 tw-col-span-1">{{ formGroup.get('email')?.value }}</span>
                <b>Số điện thoại:</b>
                <span class="md:tw-col-span-2 tw-col-span-1">{{ formGroup.get('phone')?.value }}</span>
                <b>Ghi chú:</b>
                <span style="text-wrap: wrap"
                      class="md:tw-col-span-2 tw-col-span-1">{{ formGroup.get('note')?.value }}</span>
                <b>Quyền hệ thống:</b>
                <div class="md:tw-col-span-2 tw-col-span-1 tw-gap-y-1 tw-flex tw-flex-col">
                    <p-tag
                        *ngFor="let item of formGroup.get('systemRoles')?.value"
                        ngClass="tw-block p-tag-cutom"
                        [value]="item.displayName"
                    ></p-tag>
                </div>
                <b>Khu vực:</b>
                <div class="md:tw-col-span-2 tw-col-span-1 tw-gap-y-1 tw-flex tw-flex-col">
                    <p-tag
                        *ngIf="formGroup.get('area')?.value"
                        ngClass="tw-block p-tag-cutom"
                        [value]="formGroup.get('area')?.value.name"
                    ></p-tag>
                </div>
                <b>Vai trò:</b>
                <div class="md:tw-col-span-2 tw-col-span-1 tw-gap-y-1 tw-flex tw-flex-col">
                    <p-tag
                        *ngFor="let item of formGroup.get('normalRoles')?.value"
                        ngClass="tw-block p-tag-cutom"
                        [value]="item.displayName"
                    ></p-tag>
                </div>
                <b>Phòng ban:</b>
                <div class="md:tw-col-span-2 tw-col-span-1 tw-gap-y-1 tw-flex tw-flex-col">
                    <p-tag
                        *ngIf="formGroup.get('department')?.value"
                        ngClass="tw-block p-tag-cutom"
                        [value]="formGroup.get('department')?.value.name"
                    ></p-tag>
                </div>
            </div>

            <p-panel *ngIf="editMode || !userId" header="Thông tin người dùng" [toggleable]="true">
                <div class="tw-grid md:tw-grid-cols-2 tw-grid-cols-1 tw-gap-4">
                    <app-form-item label="Họ và tên">
                        <input class="tw-w-full" type="text" pInputText formControlName="fullName"/>
                    </app-form-item>
                    <app-form-item label="Email">
                        <input class="tw-w-full" type="text" pInputText formControlName="email"/>
                    </app-form-item>
                    <app-form-item label="Số điện thoại">
                        <input class="tw-w-full" type="number" pInputText formControlName="phone"/>
                    </app-form-item>
                    <app-form-item label="Ghi chú">
                        <input class="tw-w-full" type="text" pInputText formControlName="note"/>
                    </app-form-item>
                    <app-form-item label="Quyền hệ thống" class="tw-col-span-2">
                        <p-multiSelect
                            class="tw-w-full tw-max-w-80  "
                            [options]="systemRoles"
                            formControlName="systemRoles"
                            optionLabel="displayName"
                            dataKey="id"
                            display="chip"
                        ></p-multiSelect>
                    </app-form-item>
                    <app-form-item label="Khu vực" class="tw-col-span-2">
                        <p-dropdown
                            class="tw-w-full"
                            [options]="areaOption"
                            [filter]="true"
                            dataKey="id"
                            filterBy="name"
                            formControlName="area"
                            optionLabel="name"
                        ></p-dropdown>
                    </app-form-item>
                    <app-form-item label="Vai trò" class="tw-col-span-2">
                        <p-multiSelect
                            class="tw-w-full"
                            [options]="normalRoles"
                            formControlName="normalRoles"
                            optionLabel="displayName"
                            dataKey="id"
                            display="chip"
                        ></p-multiSelect>
                    </app-form-item>
                    <app-form-item label="Phòng ban" class="tw-col-span-2">
                        <p-dropdown
                            class="tw-w-full"
                            [options]="departmentOption"
                            dataKey="id"
                            formControlName="department"
                            optionLabel="name"
                            placeholder="Phòng ban..."
                        ></p-dropdown>
                    </app-form-item>
                </div>
            </p-panel>
        </div>
    </app-form>
</div>



