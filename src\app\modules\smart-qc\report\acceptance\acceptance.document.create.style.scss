/* CSS cho phần preview khi kéo thả */
.drag-preview {
    display: table;
    width: 100%;
    background: white;
    border-collapse: collapse;
}

.cdk-drag-placeholder {
    opacity: 0;
}

.cdk-drag-animating {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.drag-custom-box:last-child {
    border: none;
}

.drag-custom-list.cdk-drop-list-dragging .drag-custom-box:not(.cdk-drag-placeholder) {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}
