import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, OnInit, TemplateRef, ViewChild, inject } from '@angular/core';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { isArray } from 'lodash';
import { ConfirmationService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';
import { ChipModule } from 'primeng/chip';
import { AuthService } from 'src/app/core/auth/auth.service';
import { TABLE_KEY } from 'src/app/models/constant';
import { Column } from 'src/app/models/interface';
import { ApproveService } from 'src/app/services/smart-qc/masterdata/approve.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { TableCommonService } from 'src/app/shared/table-module/table.common.service';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { Bom, Po, PoDraft } from 'src/app/models/interface/sc';
import { PoService } from 'src/app/services/sc/po/po.service';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { BomStatus, MAP_PO_STATE, PO_STATE, PoDraftStatus } from 'src/app/models/constant/sc';
import { FileService } from 'src/app/shared/services/file.service';
import { DialogModule } from 'primeng/dialog';
import { FormCustomModule } from '../../../../shared/form-module/form.custom.module';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { PaginatorModule } from 'primeng/paginator';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { TabViewModule } from 'primeng/tabview';
import { BomService } from '../../../../services/sc/sc-bom/bom.service';
import { PoDraftService } from '../../../../services/sc/sc-bom/po-draft.service';

@Component({
    selector: 'app-sc-po',
    standalone: true,
    imports: [
        TableCommonModule,
        CommonModule,
        RouterModule,
        ButtonModule,
        TagModule,
        TooltipModule,
        ChipModule,
        SubHeaderComponent,
        HasAnyAuthorityDirective,
        DialogModule,
        FormCustomModule,
        InputTextModule,
        InputTextareaModule,
        PaginatorModule,
        ReactiveFormsModule,
        TabViewModule,
    ],
    templateUrl: './list.component.html',
    styleUrls: ['list.component.scss'],
    providers: [ApproveService, TableCommonService, PoService, BomService, PoDraftService],
})
export class ListComponent implements OnInit, AfterViewInit {
    // Inject service
    orderService = inject(PoService);
    fileService = inject(FileService);
    tableCommonService = inject(TableCommonService);
    loadingService = inject(LoadingService);
    confirmationService = inject(ConfirmationService);
    alertService = inject(AlertService);
    authService = inject(AuthService);
    bomService = inject(BomService);
    poDraftService = inject(PoDraftService);

    // End inject service
    @ViewChild('templateStatus') templateStatus: TemplateRef<Element>;
    @ViewChild('templatePoDraftStatus') templatePoDraftStatus: TemplateRef<Element>;
    @ViewChild('templatePoLinks') templatePoLinks: TemplateRef<Element>;

    selectedTab: number = 0;

    // Bom table
    bomState: QueryObserverBaseResult<Bom[]>;
    bomColumns: Column[] = [];
    bomTableId: string = TABLE_KEY.BOM_TABLE;

    // End Bom table

    // Po draft table
    poDraftState: QueryObserverBaseResult<PoDraft[]>;
    poDraftColumns: Column[] = [];
    poDraftTableId: string = TABLE_KEY.PO_DRAFT_TABLE;
    // End Po draft table

    actionHeader: TemplateRef<Element>;
    bodyFilter: Record<string, unknown> = {
        contractIds: null,
        actionIds: null,
    };
    rowSelects: Po[] = [];
    mapState = MAP_PO_STATE;
    optionPoState = PO_STATE;

    // Popup create
    isOpenAddModal: boolean = false;
    poDraftFormGroup: FormGroup;
    // End Popup create

    constructor(
        private fb: FormBuilder,
        private router: Router,
        private route: ActivatedRoute,
    ) {}

    ngOnInit() {
        const tabIndex = this.route.snapshot.queryParamMap.get('tabIndex');
        if (tabIndex === null) {
            this.selectedTab = 0;
        } else {
            this.selectedTab = Number(tabIndex);
        }
        this.tableCommonService
            .init<Bom>({
                tableId: this.bomTableId,
                queryFn: (filter) => this.bomService.getPageTableNeedPo(filter),
                configFilterRSQL: {
                    code: 'Text',
                    rdBom: 'Text',
                    accountingCode: 'Text',
                    note: 'Text',
                    status: 'Number',
                    created: 'DateRange',
                    createdBy: 'Text',
                    bomCost: 'Number',
                },
                filterUrl: true,
            })
            .subscribe((state) => {
                this.bomState = state;
            });

        this.tableCommonService.getRowSelect(this.bomTableId).subscribe((state) => {
            if (isArray(state)) {
                this.rowSelects = state;
            }
        });

        this.tableCommonService
            .init<PoDraft>({
                tableId: this.poDraftTableId,
                queryFn: (filter) => this.poDraftService.getPageTableCustom(filter),
                configFilterRSQL: {
                    code: 'Text',
                    bomCodes: 'Text',
                    note: 'Text',
                    status: 'Number',
                    created: 'DateRange',
                    createdBy: 'Text',
                    bomCost: 'Number',
                },
                filterUrl: true,
            })
            .subscribe((state) => {
                this.poDraftState = state;
            });

        this.initAddForm();
    }

    initAddForm() {
        this.poDraftFormGroup = this.fb.group({
            code: [{ value: null, disabled: true }],
            note: [null, []],
        });
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.bomColumns = [
                {
                    header: 'SC BOM',
                    field: 'code',
                    type: 'link',
                    url: './{id}',
                    default: true,
                },
                {
                    field: 'accountingCode',
                    header: 'Mã kế toán',
                    style: { 'max-width': '8rem' },
                },
                {
                    field: 'rdBom',
                    header: 'R&D BOM',
                    style: { 'min-width': '8rem' },
                },
                {
                    field: 'note',
                    header: 'Ghi chú',
                    style: { 'max-width': '8rem' },
                },
                {
                    field: 'bomCost',
                    header: 'BOM Cost',
                },
                {
                    field: 'created',
                    header: 'Thời gian tạo',
                    type: 'date',
                    format: 'dd/MM/yyyy',
                },
                {
                    field: 'createdBy',
                    header: 'Người tạo',
                    style: { 'max-width': '8rem' },
                },
                {
                    field: 'status',
                    header: 'Trạng thái',
                    body: this.templateStatus,
                    style: { 'max-width': '10rem' },
                },
                {
                    field: 'poGroups',
                    header: 'Đơn đã tạo',
                    bodyWrapper: this.templatePoLinks,
                    style: { 'text-align': 'center' },
                    colspan: 4,
                },
            ];

            this.poDraftColumns = [
                {
                    header: 'Mã đợt tạo',
                    field: 'code',
                    type: 'link',
                    url: '/sc/po-draft/{id}',
                    default: true,
                },
                {
                    field: 'bomCodes',
                    header: 'SC BOM',
                    style: { 'max-width': '8rem' },
                },
                {
                    field: 'note',
                    header: 'Ghi chú',
                    style: { 'max-width': '8rem' },
                },
                {
                    field: 'created',
                    header: 'Thời gian tạo',
                    type: 'date',
                    format: 'dd/MM/yyyy',
                },
                {
                    field: 'createdBy',
                    header: 'Người tạo',
                    style: { 'max-width': '8rem' },
                },
                {
                    field: 'status',
                    header: 'Trạng thái',
                    body: this.templatePoDraftStatus,
                    style: { 'max-width': '8rem', 'text-align': 'center' },
                },
            ];
        }, 0);
    }

    deleteBom = (ids: number[]) => {
        return this.bomService.batchDelete(ids);
    };

    deletePoDraft = (ids: number[]) => {
        return this.poDraftService.batchDelete(ids);
    };

    onSubmitCreate() {
        this.loadingService.show();
        const data = this.poDraftFormGroup.getRawValue();
        this.poDraftService.create(data).subscribe({
            next: (res) => {
                this.alertService.success('Thành công', 'Tạo đơn nháp thành công');
                this.loadingService.hide();
                const poDraft: PoDraft = res.body as unknown as PoDraft;
                this.router.navigate(['/sc/po-draft/' + poDraft.id]);
            },
            error: (error) => {
                this.loadingService.hide();
                this.alertService.handleError(error);
            },
        });
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    handleTabChange(index: any) {
        this.selectedTab = index;
        if (index === 0) {
            this.bomState?.refetch();
        } else {
            this.poDraftState?.refetch();
        }
        this.router.navigate([], {
            relativeTo: this.route,
            queryParams: { tabIndex: index },
            queryParamsHandling: 'merge',
        });
    }

    goToCreateBomPage() {
        this.router.navigate(['/sc/bom/create']);
    }

    getPoDraftStatusLabel(statusValue: number) {
        return PoDraftStatus.find((status) => status.value === statusValue)?.label;
    }

    getSeverityPoDraftStatus(status: number) {
        switch (status) {
            case 0:
                return 'secondary';
            case 1:
                return 'info';
            case 2:
                return 'success';
            default:
                return 'secondary';
        }
    }

    getBomStatusLabel(statusValue: number) {
        return BomStatus.find((status) => status.value === statusValue)?.label;
    }

    getSeverityBomStatus(status: number) {
        switch (status) {
            case 0:
                return 'secondary';
            case 1:
                return 'info';
            default:
                return 'secondary';
        }
    }

    protected readonly bomStatus = BomStatus;
    protected readonly poDraftStatus = PoDraftStatus;
}
