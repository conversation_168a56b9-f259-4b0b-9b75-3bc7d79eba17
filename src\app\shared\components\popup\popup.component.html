<p-button [label]="label" (onClick)="openDialog()" [severity]="severity" [hidden]="!isButtonVisible" size="small"></p-button>

<p-dialog
    [header]="header"
    [(visible)]="isVisible"
    [draggable]="false"
    [modal]="true"
    [breakpoints]="{ '1199px': '75vw', '575px': '90vw' }"
    [style]="{ width: dialogWidth || '50vw' }"
    (onHide)="closeDialog()"
>
    <hr style="margin: 0" />
    <br />
    <ng-container
        [ngTemplateOutlet]="contentTemplate"
        [ngTemplateOutletContext]="{
            $implicit: isVisible,
        }"
    ></ng-container>
    <ng-container *ngIf="typePopup === 'default'">
        <ng-content></ng-content>
    </ng-container>
    <a *ngIf="typePopup === 'download'" (click)="handleOnSubmit()">
        <i style="text-decoration-line: underline; cursor: pointer"><PERSON>ấm vào đây để tải xuống</i>
    </a>
    <br />
    <ng-template pTemplate="footer">
        <div>
            <p-button
                *ngIf="typePopup === 'default' && showConfirmButton"
                severity="primary"
                type="submit"
                [disabled]="formGroup ? formGroup.invalid : false"
                size="small"
                (click)="handleOnSubmit()"
                >Xác nhận</p-button
            >
            <p-button label="Hủy" [text]="true" [raised]="true" size="small" severity="secondary" (click)="closeDialog()"></p-button>
        </div>
    </ng-template>
</p-dialog>
