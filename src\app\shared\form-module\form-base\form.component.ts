import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { TrimmedFormControl } from 'src/app/utils/form';
import { FormGroupCustom } from '../from-group.custom';
import { FormContextService } from '../form-context.service';

@Component({
    selector: 'app-form',
    template: `
        <form *ngIf="formGroup" [formGroup]="formGroup" [id]="formId" [class]="styleClass" (ngSubmit)="handleSubmit()">
            <ng-content></ng-content>
        </form>
    `,
    providers: [FormContextService], // Cung cấp service cục bộ
})
export class FormComponent implements OnInit {
    @Input() formGroup: FormGroupCustom | FormGroup;
    @Input() formId: string;
    @Input() layout: 'horizontal' | 'vertical' = 'horizontal';
    @Input() labelCol: string = 'tw-col-span-4'; // <PERSON>i<PERSON> trị mặc định cho labelCol
    @Input() wrapperCol: string = 'tw-col-span-8'; // Giá trị mặc định cho wrapperCol
    @Input() validateTrigger: 'change' | 'touched' | 'submit' = 'submit'; // Mặc định validate trên submit
    @Input() styleClass: string = '';

    @Output() onSubmit: EventEmitter<unknown> = new EventEmitter();

    constructor(private formContext: FormContextService) {}

    ngOnInit(): void {
        // Thiết lập context cho form
        this.formContext.setConfig({
            formGroup: this.formGroup,
            labelCol: this.labelCol,
            wrapperCol: this.wrapperCol,
            validateTrigger: this.validateTrigger,
            layout: this.layout,
        });
    }

    handleSubmit(): void {
        this.formContext.setSubmited(true);
        this.formGroup['isSubmited'] = true;
        // console.log(this.formGroup);

        if (this.formGroup.status === 'VALID') {
            this.trimFormValues(this.formGroup);
            this.onSubmit.emit(this.formGroup.getRawValue());
        }
    }

    trimFormValues(formGroup: FormGroup) {
        Object.keys(formGroup.controls).forEach((key) => {
            const control = formGroup.get(key);
            if (control instanceof TrimmedFormControl) {
                const value = control.value;
                control.setValue(value ? value.trim() : value, { emitEvent: false });
            }
        });
    }

    public resetSubmitState(): void {
        this.formGroup['isSubmited'] = false;
        this.formContext.setSubmited(false);
    }
}
