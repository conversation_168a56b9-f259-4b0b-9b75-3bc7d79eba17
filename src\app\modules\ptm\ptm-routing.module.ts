import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { PLRouting } from './project-plans/project-plans-routing';
import { MTRRouting } from './manufacturing-technology-records/manufacturing-technology-records-routing';
import { CRRouting } from './consolidated-reports/consolidated-reports-routing';
import { TTFRouting } from './technology-transfer-forms/technology-transfer-forms-routing';

@NgModule({
    imports: [
        RouterModule.forChild([
            {
                path: '',
                redirectTo: 'project-plans',
                pathMatch: 'full',
            },
            PLRouting,
            MTRRouting,
            TTFRouting,
            CRRouting,
        ]),
    ],
    exports: [RouterModule],
})
export class PTMRoutingModule {}
