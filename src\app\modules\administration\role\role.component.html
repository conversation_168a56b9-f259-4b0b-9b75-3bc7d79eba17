<app-sub-header [items]="itemsHeader" [action]="actionHeader"></app-sub-header>

<ng-template #actionHeader>
    <p-button routerLink="create" label="Tạo mới" severity="success" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN']" />
</ng-template>
<div style="padding: 1rem 1rem 0 1rem">
    <app-table-common
        [tableId]="tableId"
        [columns]="columns"
        [data]="state.data"
        [loading]="state.isFetching"
        [filterTemplate]="filterTemplate"
        [funcDelete]="deleteSelectedRole"
        name="Danh sách vai trò"
        [authoritiesDelete]="['ROLE_SYSTEM_ADMIN']"
        [rowSelectable]="rowSelectable"
    >
        <ng-template #filterTemplate>
            <tr>
                <th></th>
                <th [appFilter]="[tableId, 'displayName']">
                    <app-filter-table [tableId]="tableId" field="displayName"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'description']" style="max-width: 8rem">
                    <app-filter-table [tableId]="tableId" field="description"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'type']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="type"
                        type="select-one"
                        [rsql]="true"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'label',
                            rsql: true,
                            options: [
                                { label: 'Mặc định', value: 1 },
                                { label: 'Tùy chọn', value: 0 },
                            ],
                        }"
                        placeholder="loại"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'supplierNo']">
                    <app-filter-table [tableId]="tableId" field="supplierNo"></app-filter-table>
                </th>
            </tr>
        </ng-template>
        <ng-template #templateType let-rowData>
            <p-tag [value]="rowData.type ? 'Mặc định' : 'Tùy chọn'"></p-tag>
        </ng-template>
        <ng-template #templateName let-rowData>
            <a *ngIf="!rowData.type" [routerLink]="'/administration/role/' + rowData.id">{{ rowData.displayName }}</a>
            <span *ngIf="rowData.type">{{ rowData.displayName }}</span>
        </ng-template>
    </app-table-common>
</div>
