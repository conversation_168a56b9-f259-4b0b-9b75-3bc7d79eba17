import { Component, TemplateRef, ViewChild, OnInit, AfterViewInit } from '@angular/core';
import { DistrictService } from '../../../services/administration/district/district.service';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MessageService } from 'primeng/api';
import { CommonModule } from '@angular/common';
import { TableModule } from 'primeng/table';
import { PaginatorModule } from 'primeng/paginator';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { Contract } from '../../../models/interface/smart-qc';
import { Column, District, EventChangeFilter } from '../../../models/interface';
import { TableCommonService } from '../../../shared/table-module/table.common.service';
import { InputTextModule } from 'primeng/inputtext';
import { FilterTableComponent } from '../../../shared/table-module/filter-table/filter-table.component';
import { AuthService } from '../../../core/auth/auth.service';
import { InputValidationComponent } from '../../../shared/components/input-validation/input.validation.component';
import { TABLE_KEY } from 'src/app/models/constant';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';

@Component({
    selector: 'app-district',
    templateUrl: './district.component.html',
    standalone: true,
    imports: [
        CommonModule,
        TableModule,
        PaginatorModule,
        ButtonModule,
        DialogModule,
        ReactiveFormsModule,
        TableCommonModule,
        InputTextModule,
        InputValidationComponent,
        SubHeaderComponent,
    ],
    providers: [DistrictService],
})
export class DistrictComponent implements OnInit, AfterViewInit {
    @ViewChild('actionDistrict') actionDistrict: TemplateRef<Element>;
    @ViewChild('filerArea') filterTableComponent: FilterTableComponent;

    isEdit: boolean = false;

    state: QueryObserverBaseResult<Contract[]>;
    columns: Column[] = [];
    tableId: string = TABLE_KEY.DISTRICT;
    itemsHeader = [{ label: 'Quản lý danh mục' }, { label: 'Danh sách Quận/ Huyện', url: '' }];

    addDistrictForm: FormGroup;
    isOpenAddDistrictModal: boolean = false;

    constructor(
        private districtService: DistrictService,
        private tableCommonService: TableCommonService,
        private formBuilder: FormBuilder,
        private messageService: MessageService,
        protected authService: AuthService,
    ) {}

    ngOnInit() {
        this.addDistrictForm = this.formBuilder.group({
            id: [''],
            name: ['', [Validators.maxLength(255), Validators.required]],
            code: ['', [Validators.maxLength(50), Validators.required]],
            areaId: ['', Validators.required],
            areaName: [''],
        });

        this.tableCommonService
            .init<Contract>({
                tableId: this.tableId,
                queryFn: (filter) => this.districtService.getPageTableCustom(filter),
                configFilterRSQL: {
                    name: 'MultiText',
                    code: 'Text',
                    areaName: 'MultiText',
                },
                initialData: [],
            })
            .subscribe((state) => {
                this.state = state;
            });
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            if (this.authService.isAdminOrPM()) {
                this.columns = [
                    { field: 'name', header: 'Tên', default: true },
                    { field: 'code', header: 'Mã' },
                    { field: 'areaName', header: 'Tỉnh/ Thành phố' },
                    { header: 'Thao tác', body: this.actionDistrict, default: true },
                ];
            } else {
                this.columns = [
                    { field: 'name', header: 'Tên', default: true },
                    { field: 'code', header: 'Mã' },
                    { field: 'areaName', header: 'Tỉnh/ Thành phố' },
                ];
            }
        });
    }

    addDistrict() {
        if (this.addDistrictForm.valid) {
            const district = this.addDistrictForm.getRawValue();
            this.districtService.addDistrict(district).subscribe(() => {
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'success',
                    summary: 'Thành công',
                    detail: 'Thêm Quận/ Huyện thành công',
                });
                this.state.refetch();
            });
            this.isOpenAddDistrictModal = false;
        } else {
            this.markAllAsTouched(this.addDistrictForm);
        }
    }

    openModalEditDistrict(rowData: District) {
        this.addDistrictForm.patchValue({
            id: rowData.id,
            name: rowData.name,
            code: rowData.code,
            areaName: rowData.areaName,
            areaId: rowData.areaId,
        });
        this.isEdit = true;
        // this.filterTableComponent.setValue(rowData.areaId);
        this.isOpenAddDistrictModal = true;
    }

    saveDistrict() {
        if (this.addDistrictForm.valid) {
            const district = this.addDistrictForm.getRawValue();
            this.districtService.update(district).subscribe(() => {
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'success',
                    summary: 'Thành công',
                    detail: 'Sửa Quận/ Huyện thành công',
                });
                this.state.refetch();
            });
            this.isOpenAddDistrictModal = false;
        } else {
            this.markAllAsTouched(this.addDistrictForm);
        }
    }

    onChangeAreaForm(data: EventChangeFilter) {
        this.addDistrictForm.patchValue({
            areaId: data.value,
        });
    }

    markAllAsTouched(form: FormGroup) {
        Object.keys(form.controls).forEach((field) => {
            const control = form.get(field);
            control?.markAsTouched({ onlySelf: true });
        });
    }

    deleteSelectedDistrict(ids: number[]) {
        return this.districtService.batchDelete(ids);
    }

    isRowSelectable(data: District) {
        return data.used === 0;
    }
}
