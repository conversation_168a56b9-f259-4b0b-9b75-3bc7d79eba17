// src/app/components/process-flow.component.ts
import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ViewChild, SimpleChanges, QueryList, ViewChildren } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { FormBuilder, FormGroup, FormsModule, Validators } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { AlertService } from 'src/app/shared/services/alert.service';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { PanelModule } from 'primeng/panel';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { OtherDocViewModel, OtherDocRequest, OtherDocsSaveData, UpdateOtherDocsPayload, OtherDocDto } from 'src/app/models/interface/ptm/other-docs';
import { AbstractControlCustom, FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { FormArrayCustom } from 'src/app/shared/form-module/from-array.custom';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { UploadCustomComponent } from 'src/app/shared/components/upload-custom/upload-custom.component';
import { OtherDocsService } from 'src/app/services/ptm/manufacturing-technology-records/other-docs/other-docs.service';
import { TabActionButtonsComponent } from '../tab-action-buttons/tab-action-buttons.component';
import { v4 as uuid } from 'uuid';
import { FormActionService } from 'src/app/services/ptm/manufacturing-technology-records/form-action.service';
import { AuthService } from 'src/app/core/auth/auth.service';
import { FileUploadManagerService } from 'src/app/services/upload/file-upload-manager.service';
import { catchError, combineLatest, finalize, forkJoin, map, of, Subject, Subscription, switchMap, takeUntil, tap } from 'rxjs';
import { UploadService } from 'src/app/services/upload/upload.service';
import { FullscreenToggleDirective } from 'src/app/shared/directives/fullscreen-toggle.directive';
import { TabSharedStateService } from 'src/app/services/ptm/manufacturing-technology-records/tab-shared-state/tab-shared-state.service';
import { environment } from 'src/environments/environment';

@Component({
    selector: 'app-other-docs',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        InputTextModule,
        FormCustomModule,
        PanelModule,
        TableModule,
        ButtonModule,
        UploadCustomComponent,
        TabActionButtonsComponent,
        FullscreenToggleDirective,
    ],
    templateUrl: './other-docs.component.html',
    styleUrls: ['./other-docs.component.scss'],
    providers: [FormActionService, DatePipe],
})
export class OtherDocsComponent implements OnInit, OnDestroy {
    // 🧠 INPUTS từ cha truyền xuống
    @Input() version: { status: number } = { status: 1 };
    mode: 'view' | 'create' | 'edit' = 'create';
    productVersionId: number;
    phase: number;
    productInstructionId: number;
    // 📤 OUTPUTS emit về cha

    @Output() changed = new EventEmitter<string>();
    @Output() submitted = new EventEmitter<OtherDocViewModel[]>();

    @ViewChild('form', { static: false }) formComponent!: FormComponent;
    @ViewChildren('OtherDocsUploader') otherDocsUploader!: QueryList<UploadCustomComponent>;
    formGroup!: FormGroupCustom<{ items: OtherDocViewModel[] }>;
    private uploadIds = new Map<number, string>();
    private subs = new Map<number, Subscription>();
    public loadingRows = new Map<number, boolean>();
    public isUploading = false;
    private uploadSub!: Subscription;
    private destroy$ = new Subject<void>();
    downloadBaseUrl: string = environment.STORAGE_BASE_URL;
    // Kết hợp hai stream thành một Observable<boolean>
    public isBusy$ = combineLatest([this.formSvc.isSaving$, this.fileUploadManagerService.isUploading$]).pipe(
        map(([saving, uploading]) => saving || uploading),
    );

    constructor(
        private fb: FormBuilder,
        private datePipe: DatePipe,
        private otherDocsService: OtherDocsService,
        public formSvc: FormActionService<any>,
        private authService: AuthService,
        private alertService: AlertService,
        private fileUploadManagerService: FileUploadManagerService,
        private uploadService: UploadService,
        private tabSharedState: TabSharedStateService,
    ) {}
    ngOnInit() {
        this.tabSharedState
            .getProductInstructionId$()
            .pipe(takeUntil(this.destroy$))
            .subscribe((id) => {
                this.productInstructionId = id;
                if (id) {
                    this.handleGetOtherDocuments();
                }
            });

        this.tabSharedState
            .getMode$()
            .pipe(takeUntil(this.destroy$))
            .subscribe((mode) => {
                this.mode = mode;
            });

        this.tabSharedState
            .getProductVersionId$()
            .pipe(takeUntil(this.destroy$))
            .subscribe((verId) => {
                this.productVersionId = verId;
            });

        this.tabSharedState
            .getPhase$()
            .pipe(takeUntil(this.destroy$))
            .subscribe((phase) => {
                this.phase = phase;
            });

        this.uploadSub = this.fileUploadManagerService.isUploading$.pipe(takeUntil(this.destroy$)).subscribe((flag) => (this.isUploading = flag));

        console.log('🧹 [other-docs] mounted', this.productInstructionId);
        if ((this.mode === 'edit' || this.mode === 'view') && this.productInstructionId) {
            // Nếu là edit, chỉ build form khi có dữ liệu trả về
            // this.handleGetOtherDocuments();
        } else {
            // Nếu là tạo mới hoặc view thì vẫn tạo form mặc định như cũ
            this.formGroup = new FormGroupCustom(this.fb, {
                items: new FormArrayCustom([
                    this.createRow(this.createOtherDocDto('Layout Line SX'), false),
                    this.createRow(this.createOtherDocDto('HDSD setup'), false),
                    this.createRow(this.createOtherDocDto('HDSD sửa chữa'), false),
                ]),
            });
        }
        // Disable controls nếu là view mode

        // Khởi tạo service cho cả hai mode, KHÔNG phụ thuộc formGroup
        this.formSvc.initialize({
            saveApi: (data: OtherDocsSaveData) => this.otherDocsService.saveOtherDocs(data.productInstructionId, data.payload),
        });
    }
    async handleGetOtherDocuments() {
        this.otherDocsService.getOtherDocuments(this.productInstructionId).subscribe({
            next: (docs) => {
                if (docs && docs.otherDocumentDtos.length > 0) {
                    // Lấy mảng các observable lấy header cho từng file
                    const docsWithPath = docs.otherDocumentDtos.filter((doc) => !!doc.filePath);

                    if (docsWithPath.length === 0) {
                        // Không có filePath nào → dùng logic cũ luôn
                        this.formGroup = new FormGroupCustom(this.fb, {
                            items: new FormArrayCustom(docs.otherDocumentDtos.map((doc) => this.createRow({ ...doc, action: 0 }, false))),
                        });
                        this.registerActionWatcher();
                    } else {
                        // Có ít nhất 1 doc có filePath → gọi forkJoin lấy header
                        const headerObservables = docsWithPath.map((doc) => {
                            const url = this.downloadBaseUrl + '/' + doc.filePath;
                            return this.uploadService.getFileHeaders(url).pipe(catchError(() => of({})));
                        });
                        forkJoin(headerObservables).subscribe((headersList) => {
                            const mergedDocs = docs.otherDocumentDtos.map((doc, i) => {
                                if (doc.filePath) {
                                    const headerIdx = docsWithPath.findIndex((d) => d === doc);
                                    const headerObj = headersList[headerIdx] || {};
                                    return {
                                        ...doc,
                                        fileName: this.extractFileName(headerObj['content-disposition']),
                                    };
                                }
                                return { ...doc };
                            });

                            this.formGroup = new FormGroupCustom(this.fb, {
                                items: new FormArrayCustom(mergedDocs.map((doc) => this.createRow({ ...doc, action: 0 }, false))),
                            });
                            this.registerActionWatcher();
                        });
                    }
                } else {
                    this.formGroup = new FormGroupCustom(this.fb, {
                        items: new FormArrayCustom([
                            this.createRow(this.createOtherDocDto('Layout Line SX'), false),
                            this.createRow(this.createOtherDocDto('HDSD setup'), false),
                            this.createRow(this.createOtherDocDto('HDSD sửa chữa'), false),
                        ]),
                    });
                }
                if (this.mode === 'view') {
                    this.disableFormControls();
                }
            },
            error: (err) => {
                console.log('Lỗi khi lấy tài liệu khác', err.message);
                // (Có thể build form rỗng hoặc báo lỗi)
                this.formGroup = new FormGroupCustom(this.fb, { items: new FormArrayCustom([]) });
            },
        });
    }
    extractFileName(disposition: string): string {
        if (!disposition) return '';
        let fileName = '';

        // Ưu tiên filename*= (RFC 5987, đã encode)
        const filenameStarMatch = disposition.match(/filename\*\s*=\s*([^;]+)/i);
        if (filenameStarMatch && filenameStarMatch[1]) {
            const value = filenameStarMatch[1].trim();
            const parts = value.split("''");
            fileName = parts.length > 1 ? parts[1] : parts[0];
            return decodeURIComponent(fileName); // <- giải mã ra tên chuẩn
        }
        return fileName;
    }
    private registerActionWatcher() {
        const items = this.formGroup.get('items') as FormArrayCustom;
        items.controls.forEach((ctrl: FormGroup) => {
            if (!(ctrl as any)._actionWatcherRegistered) {
                ctrl.valueChanges.subscribe(() => {
                    const actionCtrl = ctrl.get('action');
                    if (actionCtrl && actionCtrl.value !== 3) {
                        actionCtrl.setValue(2, { emitEvent: false });
                    }
                });
                (ctrl as any)._actionWatcherRegistered = true;
            }
        });
    }
    createOtherDocDto(name: string): OtherDocDto {
        return {
            id: Date.now() + Math.floor(Math.random() * 100000),
            name,
            filePath: '',
            fileName: '',
            lastUpdate: null,
            userUpdate: {
                id: 0,
                email: '',
                fullName: '',
            },
            action: 1,
        };
    }
    disableFormControls() {
        if (this.formGroup) {
            this.formGroup.disable({ onlySelf: true, emitEvent: false });
        }
    }
    get items(): FormArrayCustom<FormGroup> {
        return this.formGroup.get('items') as FormArrayCustom<FormGroup>;
    }

    private createRow(data?: OtherDocDto, disable = false) {
        return this.fb.group({
            id: [data?.id ?? Date.now() + Math.floor(Math.random() * 100000)],
            name: [{ value: data?.name || '', disabled: disable }, Validators.required],
            file: null,
            fileName: [data?.fileName || null],
            lastUpdate: [data?.lastUpdate || null],
            filePath: [data?.filePath ?? ''],
            userUpdate: [data?.userUpdate?.email ?? ''],
            lastUpdateText: [data?.lastUpdate ? this.datePipe.transform(data.lastUpdate, 'HH:mm dd/MM/yyyy') : ''],
            // action: 1=create, 2=update, 3=delete
            action: [data?.action ?? 1],
        });
    }
    ngOnChanges(changes: SimpleChanges) {}

    onChange(newVal: string) {
        this.changed.emit(newVal);
    }

    addItem() {
        this.items.push(this.createRow());
    }

    removeItem(rowId: number) {
        const idx = this.items.controls.findIndex((ctrl) => ctrl.get('id')!.value === rowId);
        if (idx === -1) return;
        if (idx < 3) return;
        const ctrl = this.items.at(idx);

        // Nếu dòng mới (action=1) thì xoá hẳn,
        // ngược lại chỉ đánh dấu xóa để vẫn gửi action=3
        const currentAction = ctrl.get('action')!.value;
        if (currentAction === 1) {
            // mới thêm, chưa sync với BE → xoá hẳn
            this.items.removeAt(idx);
        } else {
            // đã tồn tại trên BE → mark delete
            ctrl.get('action')!.setValue(3);
        }

        // map loadingRows, uploadIds xoá sạch
        this.uploadIds.delete(rowId);
        this.loadingRows.delete(rowId);
    }
    onClearFile(rowId: number) {
        const uploadId = this.uploadIds.get(rowId);
        if (uploadId) {
            this.fileUploadManagerService.cancel(uploadId);
            this.uploadIds.delete(rowId);
        }

        // Reset controls for this row
        const idx = this.items.controls.findIndex((ctrl) => ctrl.get('id')?.value === rowId);
        if (idx !== -1) {
            const ctrl = this.items.at(idx);
            ctrl.patchValue({ file: null, lastUpdateText: '', lastUpdate: null, userUpdate: '', filePath: '' });
        }
    }
    onFileSelected(files: any[], rowId: number) {
        const file = files[0];
        const fileName: string = files[0].name;
        if (!file) return;
        this.loadingRows.set(rowId, true);
        const uploadId = uuid();
        this.uploadIds.set(rowId, uploadId);

        const currentUser = this.authService.getPrinciple();
        // Find the row form group by id
        const idx = this.items.controls.findIndex((ctrl) => ctrl.get('id')?.value === rowId);
        if (idx === -1) return;
        const ctrl = this.items.at(idx);

        ctrl.patchValue({
            file,
            lastUpdate: Date.now(),
            lastUpdateText: this.datePipe.transform(Date.now(), 'HH:mm dd/MM/yyyy'),
            userUpdate: currentUser?.email || '',
        });
        const sub = this.uploadService
            .analyzeFile(fileName, 'OTHERS')

            .pipe(
                tap((meta) => {
                    if (ctrl.get('filePath')) ctrl.get('filePath')?.setValue(meta.objectPath);
                    if (ctrl.get('fileName')) ctrl.get('fileName')?.setValue(meta.fileName);
                }),
                switchMap((meta) => this.uploadService.uploadToPresignedUrl(files[0], meta.presignedUrl)),
                // dù complete hay error, luôn finish uploadId
                finalize(() => this.fileUploadManagerService.finish(uploadId)),
            )
            .subscribe({
                next: (p) => ({}),
                error: (e) => {
                    const list = this.otherDocsUploader.toArray();
                    if (list[idx]) list[idx].clearAll();
                },
                complete: () => {
                    this.loadingRows.set(rowId, false);
                },
            });
        this.subs.set(rowId, sub);
        // Hook cancel callback
        this.fileUploadManagerService.start(uploadId, () => {
            const s = this.subs.get(rowId);
            if (s) {
                s.unsubscribe();
                this.subs.delete(rowId);
            }
        });
    }

    onSave() {
        if (this.formGroup.invalid) {
            return;
        } else {
            const docs: OtherDocViewModel[] = (this.formGroup.get('items') as FormArrayCustom).getRawValue();

            const otherDocuments: OtherDocRequest[] = docs.map((d) => {
                if (d.action === 0 || d.action === 3) {
                    return {
                        id: d.id,
                        action: d.action,
                    };
                }
                return {
                    id: d.id,
                    name: d.name,
                    filePath: d.filePath,
                    action: d.action,
                    lastUpdate: typeof d.lastUpdate === 'number' ? d.lastUpdate : d.lastUpdate ? Date.parse(d.lastUpdate) : null,
                    userUpdate: d.userUpdate ? d.userUpdate : null,
                };
            });

            const payload: UpdateOtherDocsPayload = {
                productVersionId: this.productVersionId,
                phase: this.phase,
                otherDocuments,
            };
            console.log('payload', payload);

            const data: OtherDocsSaveData = {
                payload: payload,
                productInstructionId: this.productInstructionId || 0,
            };
            this.formSvc.save(data).subscribe({
                next: (resp: OtherDocViewModel[]) => {
                    if (this.productInstructionId) {
                        this.alertService.success('Thành công', 'Lưu hồ sơ tài liệu khác thành công');
                    }

                    this.exitFullscreenIfNeeded();
                    // Ví dụ side-effect: cập nhật lại UI, emit sự kiện, show toast…
                    this.submitted.emit(resp);
                },
                error: (err) => {
                    console.error('Save failed', err);
                },
            });
        }
    }

    exitFullscreenIfNeeded() {
        document.body.classList.remove('fullscreen');
    }
    /** Kéo xử lý thành công thì emit và reset */
    // protected onSubmit(value) {
    //     console.log('value form submit', value);
    //     console.log('this.items.value', this.items.value);
    //     this.formGroup.resetForm();
    // }
    ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
        if (this.uploadSub) {
            this.uploadSub.unsubscribe();
        }
        console.log('🧹 [other-docs] Unmounted');
    }
}
