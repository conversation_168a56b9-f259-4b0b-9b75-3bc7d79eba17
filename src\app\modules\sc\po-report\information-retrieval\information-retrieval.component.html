<app-sub-header
    [items]="[{ label: 'Quản lý thông tin mua hàng' }, { label: '<PERSON><PERSON><PERSON> cáo thống kê' }, { label: '<PERSON><PERSON><PERSON> xuất thông tin vật tư' }]"
    [action]="actionHeader"
></app-sub-header>

<ng-template #actionHeader>
    <app-popup
        header="Xuất báo cáo Truy xuất thông tin vật tư"
        severity="success"
        label="Xuất báo cáo"
        typePopup="download"
        (onSubmit)="handleExportReport($event)"
    ></app-popup>
</ng-template>

<div style="padding: 1rem 1rem 0 1rem">
    <app-form [formGroup]="formGroup" layout="vertical">
        <div class="tw-grid lg:tw-grid-cols-4 md:tw-grid-cols-2 sm:tw-grid-cols-1 tw-bg-white tw-p-4 tw-gap-6 tw-rounded-md">
            <app-form-item label="VNPT PN">
                <input type="text" class="tw-w-full" formControlName="vnptPn" pInputText />
            </app-form-item>
            <app-form-item label="VNPT MAN PN">
                <input type="text" class="tw-w-full" formControlName="vnptManPn" pInputText />
            </app-form-item>
            <app-form-item label="Mã nhà sản xuất">
                <input type="text" class="tw-w-full" formControlName="manPn" pInputText />
            </app-form-item>
            <app-form-item label="Mô tả">
                <input type="text" class="tw-w-full" formControlName="description" pInputText />
            </app-form-item>
            <app-form-item label="Nhà cung cấp">
                <app-filter-table
                    type="select-one"
                    [configSelect]="{
                        fieldValue: 'id',
                        fieldLabel: 'shortName',
                        rsql: true,
                        url: '/sc/api/supplier/search',
                        additionalCondition: 'deletedAt==null',
                    }"
                    formControlName="supplierId"
                ></app-filter-table>
            </app-form-item>
            <app-form-item label="Lọc theo danh sách" class="tw-col-span-2">
                <app-button-group-file
                    urlTemplate="template_search_report_po.xlsx"
                    service="/sc/api"
                    (onFileSelected)="handleUploadFile($event)"
                    (onClearFile)="handleClearFile()"
                    [types]="['excel']"
                ></app-button-group-file>
            </app-form-item>
            <div class="flex tw-items-end">
                <p-button icon="pi pi-search" (click)="search()"></p-button>
            </div>
        </div>
    </app-form>
    <br />

    <app-table-common
        [tableId]="tableId"
        [columns]="columns"
        [data]="state.data"
        [loading]="state.isFetching"
        [showCaption]="false"
        [selectionMode]="null"
        [stt]="true"
    >
        <ng-template #templateAction let-rowData>
            <p-button icon="pi pi-pencil" size="small" (click)="showDialog(rowData)"></p-button>
        </ng-template>
    </app-table-common>
</div>

<p-dialog header="Cập nhật thông tin NCC cho mã vật tư" [(visible)]="visible" [style]="{ width: '80vw' }">
    <div class="tw-grid tw-grid-cols-2">
        <b>VNPT Man PN</b>
        <p>{{ itemPopup?.vnptManPn }}</p>
    </div>
    <br />
    <b>Danh sách Nhà cung cấp hiện hữu</b>
    <p-table [value]="materialHaveTransfer" [tableStyle]="{ 'min-width': '50rem' }">
        <ng-template pTemplate="header">
            <tr>
                <th>STT</th>
                <th>Tên viết tắt</th>
                <th>Tên đầy đủ</th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-material let-rowIndex="rowIndex">
            <tr>
                <td>{{ rowIndex + 1 }}</td>
                <td>{{ material.supplierShortName }}</td>
                <td>{{ material.supplierName }}</td>
            </tr>
        </ng-template>
    </p-table>
    <br />
    <b>Danh sách Nhà cung cấp có thể cung cấp</b>
    <app-form [formGroup]="formGroupMaterial">
        <ng-container formArrayName="materials">
            <p-table [value]="materials.controls" [tableStyle]="{ 'min-width': '50rem' }">
                <ng-template pTemplate="header">
                    <tr>
                        <th>Thao tác</th>
                        <th>Tên viết tắt</th>
                        <th>Tên đầy đủ</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                    <tr *ngIf="item.value.isEdit" [formGroupName]="rowIndex">
                        <td>
                            <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                <button
                                    class="p-link tw-p-2 bg-green-300 hover:tw-bg-green-400 tw-text-white"
                                    (click)="saveItem(rowIndex)"
                                    pTooltip="Lưu"
                                    tooltipPosition="top"
                                    type="button"
                                >
                                    <span class="pi pi-save"></span>
                                </button>
                                <button
                                    class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                    (click)="cancelCreate(rowIndex)"
                                    pTooltip="Hủy"
                                    tooltipPosition="top"
                                    type="button"
                                >
                                    <span class="pi pi-times"></span>
                                </button>
                            </div>
                        </td>
                        <td>
                            <app-form-item label="">
                                <app-filter-table
                                    type="select-one"
                                    [configSelect]="{
                                        fieldValue: 'id',
                                        fieldLabel: 'shortName',
                                        rsql: true,
                                        url: '/sc/api/supplier/search',
                                        additionalCondition: 'deletedAt==null',
                                        formParam: 'id',
                                    }"
                                    formControlName="supplierId"
                                    (onChange)="selectSupplier($event, rowIndex)"
                                ></app-filter-table>
                            </app-form-item>
                        </td>
                        <td>
                            <app-form-item label="">
                                <app-filter-table
                                    type="select-one"
                                    [configSelect]="{
                                        fieldValue: 'shortName',
                                        fieldLabel: 'name',
                                        rsql: true,
                                        url: '/sc/api/supplier/search',
                                        additionalCondition: 'deletedAt==null',
                                    }"
                                    formControlName="supplierShortName"
                                ></app-filter-table>
                            </app-form-item>
                        </td>
                    </tr>
                    <tr *ngIf="!item.value.isEdit">
                        <td>
                            <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                <button
                                    class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                    (click)="deleteItem(rowIndex)"
                                    pTooltip="Xóa"
                                    tooltipPosition="top"
                                    type="button"
                                    *ngIf="!isAddingMaterial"
                                >
                                    <span class="pi pi-trash"></span>
                                </button>
                            </div>
                        </td>
                        <td>{{ item.value?.supplierShortName }}</td>
                        <td>{{ item.value?.supplierName }}</td>
                    </tr>
                </ng-template>
            </p-table>
            <div class="tw-mt-3">
                <p-button label="Thêm" icon="pi pi-plus" severity="info" size="small" [disabled]="isAddingMaterial" (click)="addItem()"></p-button>
            </div>
        </ng-container>
    </app-form>

    <ng-template pTemplate="footer">
        <div>
            <p-button label="Đóng" [text]="true" [raised]="true" size="small" severity="secondary" (click)="closeDialog()"></p-button>
        </div>
    </ng-template>
</p-dialog>
