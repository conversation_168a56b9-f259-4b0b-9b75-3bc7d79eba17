import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ConfigDate } from '../filter-table/filter-table.component';
import { isArray, isDate } from 'lodash';

@Component({
    template: `
        <div class="tw-grid md:tw-grid-cols-2 tw-grid-cols-1 md:tw-gap-8 tw-gap-4">
            <div>
                <p-calendar
                    appendTo="body"
                    [showButtonBar]="true"
                    placeholder="từ ngày"
                    [maxDate]="endDate"
                    [showIcon]="true"
                    dateFormat="dd/mm/yy"
                    [(ngModel)]="startDate"
                ></p-calendar>
            </div>
            <div>
                <p-calendar
                    appendTo="body"
                    [showButtonBar]="true"
                    [minDate]="startDate"
                    placeholder="đến ngày"
                    [showIcon]="true"
                    dateFormat="dd/mm/yy"
                    [(ngModel)]="endDate"
                ></p-calendar>
            </div>
        </div>
        <div class="tw-mt-10 tw-flex tw-justify-end tw-gap-4">
            <p-button label="Xóa" [text]="true" severity="secondary" (click)="clear()"></p-button>
            <p-button label="Tìm" [raised]="true" (click)="search()"></p-button>
        </div>
    `,
    standalone: true,
    imports: [CalendarModule, ButtonModule, FormsModule],
})
export class RangeDateContentComponent {
    startDate: Date | undefined;
    endDate: Date | undefined;

    configDate: ConfigDate;
    constructor(
        public ref: DynamicDialogRef,
        public config: DynamicDialogConfig,
    ) {
        const configDate = config.data.configDate as ConfigDate;
        if (configDate) {
            this.configDate = configDate;

            if (isArray(this.configDate.filter) && this.configDate.filter.length === 2) {
                if (isDate(this.configDate.filter[0])) this.startDate = this.configDate.filter[0];
                if (isDate(this.configDate.filter[1])) this.endDate = this.configDate.filter[1];
            }
        } else {
            this.configDate = {
                dateFormat: 'dd/mm/yy',
            };
        }
    }

    clear() {
        this.startDate = undefined;
        this.endDate = undefined;
        this.ref.close([]);
    }

    search() {
        if (isDate(this.startDate) || isDate(this.endDate)) {
            this.ref.close([this.startDate, this.endDate]);
        } else {
            this.ref.close([]);
        }
    }
}
