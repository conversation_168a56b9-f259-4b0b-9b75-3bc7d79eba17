import { TemplateRef } from '@angular/core';
import { BaseEntity } from 'src/app/models/BaseEntity';

export interface Column {
    field?: string;
    sort?: string;
    typeSort?: 'desc' | 'asc';
    header: string;
    body?: TemplateRef<unknown> | string;
    bodyWrapper?: TemplateRef<unknown> | string;
    hide?: boolean;
    visible?: boolean;
    style?: Record<string, unknown>;
    rowspan?: number;
    colspan?: number;
    default?: boolean;
    index?: number;
    format?: string;
    type?: 'date' | 'number' | 'link' | 'newTab' | 'currency';
    fixed?: 'right' | 'left';
    url?: string;
    fallBackValue?: unknown;
    group?: string;
}

export interface ApiResponse {
    code: 0 | 1;
    message: string;
    data?: unknown;
    attachment?: Attachment;
}

export interface Attachment extends BaseEntity {
    name: string;
    url: string;
    parentId?: number;
    model?: string;
    type: number; // 1-PDF ; 2-WORD ; 3-EXCEL
    description?: string;
    timeGroupId?: number;
    timeGroup?: number;
}
export interface GeneralEntity {
    fileName?: string;
    url: string;
}

export interface TokenDTO {
    token?: string;
}

export interface TokenLogin {
    id_token?: string;
}

export interface EventChangeFilter {
    value: unknown;
    objects: unknown[];
}

export interface ValidateMessage {
    type?: string;

    message?: string;
}

export interface Area {
    id?: number;

    name?: string;

    code?: string;
}

export interface District {
    id?: number;

    name?: string;

    code?: string;

    areaId?: number;

    areaName?: string;

    used?: number;
}

export interface User extends BaseEntity {
    fullName?: string;
    email?: string;
    areas?: Area[];
    authorities?: string[];
    departments?: unknown[];
    modules?: [];
    roles?: Role[];
    phone?: string;
    note?: string;
    area?: Area;
    systemRoles?: Role[];
    normalRoles?: Role[];
    department?: unknown;
    qcContractIds?: unknown[];
    password?: unknown;
    newPassword?: unknown;
    confirmPassword?: unknown;
    adminChangePassword?: unknown;
}

export interface Role extends BaseEntity {
    name: string;
    displayName: string;
    description: string;
    type: boolean;
    qcLevel?: unknown;
    privileges?: Privilege[];
    user: User;
}

export interface Privilege extends BaseEntity {
    name: string;
    description: string;
    displayName: string;
    categoryName: string;
}

export interface Process extends BaseEntity {
    name: string;
    description: string;
    keyEntity: string;
    treeState: State;
    states: State[];
    isUpdateDetail: boolean;
    arrange: boolean;
}

export interface ProcessDetail extends BaseEntity {
    processId: number;
    currentStateId: number;
    nextStateId: number;
    description: string;
    isUpdateDetail: boolean;
}

export interface State extends BaseEntity {
    name: string;
    description: string;
    keyEntity: string;
    nextStates: State[];
    descriptionProcessDetail: string;
    key: string;
    isRoot: boolean;
    value: number;
    color: string;
}

export interface StateHistory extends BaseEntity {
    stateId: number;
    entity: string;
    entityId: number;
    action: string;
    content: string;
}

export type TypeValueRsql =
    | 'Text'
    | 'TextURL'
    | 'TextExact'
    | 'TextOr'
    | 'Number'
    | 'NotEqual'
    | 'Boolean'
    | 'MultiText'
    | 'MultiTextOr'
    | 'SetLong'
    | 'SetLongNotIn'
    | 'NumberRange'
    | 'MultiNumber'
    | 'MultiBoolean'
    | 'DateRange'
    | 'DateRangeInfinity'
    | 'Month'
    | 'IsNull';

export interface EventPopupSubmit<T> {
    value: T;
    close: () => void;
}

export interface StateWizard {
    id?: number;

    value?: number;

    name?: string;

    description?: string;
}
