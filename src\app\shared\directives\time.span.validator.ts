import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function timeSpamValidator(startTimeControlName, endTimeControlName): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        const startTime = control.get(startTimeControlName);
        const endTime = control.get(endTimeControlName);
        return startTime?.value && endTime?.value && startTime.value > endTime.value ? { timespan: true } : null;
    };
}

export function isDateTypeValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        return !control.value || control.value instanceof Date ? null : { dateType: true };
    };
}

export const trimValidator = (): ValidationErrors | null => {
    return (control: AbstractControl): ValidationErrors | null => {
        const value = control.value;

        if (!value || value === '') return null;

        if (typeof value === 'string' && value.startsWith(' ')) {
            control.setValue(control.value?.trim(), { emitEvent: false, onlySelf: true });
        }
        return null;
    };
};
