import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { RouterLink } from '@angular/router';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { ButtonModule } from 'primeng/button';
import { TagModule } from 'primeng/tag';
import { TABLE_KEY } from 'src/app/models/constant';
import { Column, Role } from 'src/app/models/interface';
import { RoleService } from 'src/app/services/administration/admin/role.service';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { TableCommonService } from 'src/app/shared/table-module/table.common.service';

@Component({
    selector: 'app-role',
    standalone: true,
    templateUrl: './role.component.html',
    imports: [
        TableCommonModule,
        CommonModule,
        RouterLink,
        SubHeaderComponent,
        HasAnyAuthorityDirective,
        TagModule,
        ButtonModule,
    ],
    providers: [RoleService],
})
export class RoleComponent implements OnInit, AfterViewInit {
    tableId: string = TABLE_KEY.ROLE;
    @ViewChild('templateType') templateType: TemplateRef<Element>;
    @ViewChild('templateName') templateName: TemplateRef<Element>;

    state: QueryObserverBaseResult<Role[]>;
    columns: Column[] = [];
    itemsHeader = [{ label: 'Quản trị hệ thống' }, { label: 'Vai trò', url: 'role' }];
    constructor(
        private roleService: RoleService,
        private tableCommonService: TableCommonService,
    ) {}
    ngAfterViewInit(): void {
        setTimeout(() => {
            this.columns = [
                { field: 'displayName', header: 'Tên', default: true, body: this.templateName },
                { field: 'description', header: 'Mô tả' },
                { field: 'type', header: 'Loại', body: this.templateType },
            ];
        });
    }

    ngOnInit(): void {
        this.tableCommonService
            .init<Role>({
                tableId: this.tableId,
                queryFn: (filter) => this.roleService.getPageTableCustom(filter),
                configFilterRSQL: {
                    name: 'Text',
                    description: 'Text',
                    type: 'Number',
                },
            })
            .subscribe((state) => {
                this.state = state;
            });
    }

    deleteSelectedRole = (ids: number[]) => {
        return this.roleService.batchDelete(ids);
    };

    hiddenSelect = (e: Role) => {
        return e.type;
    };

    rowSelectable = (rowData: Role) => {
        return !rowData.type;
    };
}
