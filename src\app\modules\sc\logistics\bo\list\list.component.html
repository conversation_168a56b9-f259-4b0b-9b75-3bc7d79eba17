<app-sub-header [items]="[{ label: '<PERSON>uản lý yêu cầu vận chuyển' }, { label: '<PERSON>h sách' }]" [action]="actionHeader"></app-sub-header>

<ng-template #actionHeader>
    <p-button routerLink="create" label="Tạo mới" severity="success" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_bo_edit']" />
</ng-template>
<div style="padding: 1rem 1rem 0 1rem">
    <app-table-common
        [tableId]="tableId"
        [columns]="columns"
        [data]="state.data"
        [loading]="state.isFetching"
        [filterTemplate]="filterTemplate"
        name="Danh sách yêu cầu vận chuyển"
        [funcDelete]="deleteSelected"
        [authoritiesDelete]="['ROLE_SYSTEM_ADMIN', 'sc_bo_delete']"
    >
        <ng-template #filterTemplate>
            <tr>
                <th></th>
                <th [appFilter]="[tableId, 'code']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="id"
                        type="select-one"
                        [rsql]="true"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'code',
                            rsql: true,
                            param: 'code',
                            paramForm: 'id',
                            url: '/sc/api/bo/search',
                            body: {
                                status: [0, 1, 2, 3, 4],
                            },
                        }"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'supplierName']">
                    <app-filter-table [rsql]="true" [tableId]="tableId" field="supplierName"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'poNumber']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="poNumber"
                        type="select-one"
                        [rsql]="true"
                        [configSelect]="{
                            fieldValue: 'poNumber',
                            fieldLabel: 'poNumber',
                            rsql: true,
                            param: 'poNumber',
                            paramForm: 'poNumber',
                            url: '/sc/api/bo/search',
                        }"
                        [removeNullLabel]="true"
                    ></app-filter-table>
                </th>

                <th [appFilter]="[tableId, 'type']">
                    <app-filter-table
                        [rsql]="true"
                        [tableId]="tableId"
                        field="type"
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'label',
                            filterLocal: true,
                            options: TYPE_SHIPPING,
                        }"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'indexShipment']">
                    <app-filter-table [rsql]="true" type="number" [tableId]="tableId" field="indexShipment"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'accountingCode']">
                    <app-filter-table [rsql]="true" [tableId]="tableId" field="accountingCode"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'readyDate']">
                    <app-filter-table [rsql]="true" [tableId]="tableId" field="readyDate" type="date-range"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'requiredArrivedDate']">
                    <app-filter-table [rsql]="true" [tableId]="tableId" field="requiredArrivedDate" type="date-range"></app-filter-table>
                </th>

                <th [appFilter]="[tableId, 'totalWeight']"></th>
                <th [appFilter]="[tableId, 'packageNumber']"></th>
                <th [appFilter]="[tableId, 'departmentId']">
                    <app-filter-table
                        [rsql]="true"
                        [tableId]="tableId"
                        field="departmentId"
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            url: '/sc/api/department/search',
                            rsql: true,
                            param: 'name',
                            paramForm: 'id',
                        }"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'note']">
                    <app-filter-table [rsql]="true" [tableId]="tableId" field="note"></app-filter-table>
                </th>
            </tr>
        </ng-template>

        <ng-template #templateType let-rowData>
            {{ MAP_TYPE_SHIPPING[rowData.type] }}
        </ng-template>

        <ng-template #templateDepartment let-rowData>
            {{ mapDepartment[rowData.departmentId] }}
        </ng-template>
    </app-table-common>
</div>
