import { Component, Input, OnInit, inject, Output, EventEmitter, OnChanges, SimpleChanges } from '@angular/core';

import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { HttpClient, HttpParams } from '@angular/common/http';
import { QueryObserverBaseResult, injectQuery } from '@ngneat/query';
import { debounce, isArray, isDate, unionBy, isEqual, isEmpty, isString, isNumber } from 'lodash';
import { MultiSelectChangeEvent } from 'primeng/multiselect';
import { FormControl } from '@angular/forms';
import { DropdownChangeEvent, DropdownFilterEvent } from 'primeng/dropdown';
import { FilterTable, TableCommonService } from 'src/app/shared/table-module/table.common.service';

// Interface for configuring select input
export interface ConfigSelectProps {
    option?: Array<unknown>; // Options for select input
    fieldValue: string; // Field used as value for each option
    fieldLabel: string; // Field used as label for each option
    param?: string; // Additional parameters for filtering
    url?: string; // API URL to fetch options
    rsql?: boolean; // Flag to determine if RSQL is used
    dataKey?: string; // Key to uniquely identify data
    page?: number; // Pagination: current page
    size?: number; // Pagination: page size
    sort?: string; // Sort order for data
    body?: Record<string, unknown>; // Request body for non-RSQL filtering
    filterLocal?: boolean; // Flag to filter locally without calling API

    urlForm?: string; // Alternative URL for form
    fieldValueForm?: string; // Alternative value field for form
    fieldLabelForm?: string; // Alternative label field for form
    paramForm?: string; // Alternative param for form

    /** Nếu true → giữ POST cũ; nếu false (mặc định) → dùng GET truyền params */
    usePost?: boolean;
}

// Interface for date configurations
export interface ConfigDate {
    minDate?: Date; // Minimum allowed date
    maxDate?: Date; // Maximum allowed date
    dateFormat?: string; // Date format
    filter?: Date[] | Date; // Date range or single date filter
}

// Interface to emit filter change event
export interface FilterChangeEvent {
    value: unknown; // New value for the filter
    objects: Array<unknown>; // Filtered objects
}

@Component({
    selector: 'app-custom-filter-table',
    templateUrl: './custom-filter-table.component.html',
    styleUrls: ['./custom-filter-table.component.scss'],
})
export class CustomFilterTableComponent implements OnInit, OnChanges {
    private valueSubject = new Subject<unknown>(); // Subject to handle debounced value changes
    private objectFilter: FilterTable; // Filter object for the table
    value: unknown; // Current value for the filter
    objectValue: unknown[] = []; // Selected objects
    #http = inject(HttpClient); // Inject HttpClient for API calls
    #query = injectQuery(); // Inject Query for data fetching
    debouncedGetOptions;
    fetchFirstDone: boolean = false; // Flag to track if the first fetch is completed
    dropDownFirstDone: boolean = false; // Flag to track if tag select or select-one is dropdown
    isHasValueUrl: boolean = false;
    countCallApi: number = 0;
    needMoreOption: boolean = false; // cờ call thêm api nếu lần đầu form có giá trị để option có thêm các lựa chọn ngoài giá trị đã select
    /**
     * Input properties
     */
    @Input('disabled') disabled: boolean = false; // Disable the filter
    @Input('viewOnly') viewOnly: boolean = false; // View-only mode
    // @Input('fetchOnInit') fetchOnInit: boolean = false; // Fetch options on initialization
    @Input('selectFirstValue') selectFirstValue: boolean = false; // Auto-select the first value in the options
    @Input('tableId') tableId: string; // ID of the table associated with this filter
    @Input('field') field: string; // Field name for the filter
    @Input('placeholder') placeholder: string = ''; // Placeholder for the input
    @Input('type') type: 'text' | 'number' | 'select' | 'select-one' | 'date' | 'date-range' = 'text'; // Type of filter
    @Input('rsql') rsql: boolean = true; // Use RSQL for filtering
    @Input('filterCombobox') filterCombobox: boolean = true; // Flag for enabling combobox filtering
    @Input('requiredFilter') requiredFilter: string[] = []; // Required filters to be checked before fetching options
    @Input('showToggleAll') showToggleAll: boolean = true;
    @Input('configSelect') configSelect: ConfigSelectProps = {
        // Configuration for select inputs
        option: [],
        fieldValue: 'id',
        fieldLabel: 'id',
        rsql: true,
        dataKey: 'id',
    };
    @Input('configDate') configDate: ConfigDate = {
        // Date configuration
        dateFormat: 'dd/mm/yy',
    };
    @Input('initValue') initValue: unknown; // Initial value for the filter
    @Input('control') control?: FormControl; // Form control to manage filter state
    @Input() selectionLimit: number = 2;
    // output
    @Output('onChange') onChange: EventEmitter<FilterChangeEvent> = new EventEmitter(); // Emit filter change events
    @Output() onSelectFirstValueDone: EventEmitter<unknown> = new EventEmitter(); // Emit when the first value is selected
    // @Output() onFetchFirstDone: EventEmitter<unknown> = new EventEmitter(); // Emit when first fetch is done

    resultSelect: QueryObserverBaseResult<unknown, Error>;
    placeholderData: Array<unknown> = [];
    constructor(private tableCommonService: TableCommonService) {
        this.initializeDebounce();
    }

    // Initialize debounce and distinctUntilChanged for filter changes
    private initializeDebounce() {
        this.valueSubject
            .pipe(
                debounceTime(this.getDebounceTime()),
                distinctUntilChanged((prev, curr) => JSON.stringify(prev) === JSON.stringify(curr)),
            )
            .subscribe((value) => {
                this.applyFilter(value);
                this.value = value;
            });

        this.debouncedGetOptions = debounce(this.fetchOptions.bind(this), this.getDebounceTime());
    }

    // Determines debounce time based on input type
    private getDebounceTime(): number {
        return ['select', 'select-one'].includes(this.type) ? 300 : 500;
    }
    ngOnInit(): void {
        this.initializeFilter();
        this.initializeResultSelect();

        if (this.control && this.control.value) {
            this.value = this.control.value;
        }

        if ((this.selectFirstValue || this.value) && this.configSelect.url && !this.fetchFirstDone) {
            this.debouncedGetOptions(this.value);
        }
    }

    ngOnChanges(simpleChanges: SimpleChanges): void {
        if (this.hasChanged(simpleChanges, 'configSelect', 'option')) {
            this.updateResultSelectOptions(simpleChanges['configSelect'].currentValue?.option);
        }

        if (this.hasChanged(simpleChanges, 'configSelect', 'body')) {
            this.debouncedGetOptions(null);
        }

        if (this.hasChanged(simpleChanges, 'initValue')) {
            this.updateValue(simpleChanges['initValue']?.currentValue);
        }

        if (this.hasChanged(simpleChanges, 'control')) {
            if (simpleChanges['control'].previousValue?.value === simpleChanges['control'].currentValue?.value) return;
            this.value = simpleChanges['control'].currentValue.value;

            if (!this.fetchFirstDone && !this.value) return;
            this.debouncedGetOptions(simpleChanges['control'].currentValue.value);
        }
    }

    // Utility to check if an input property has changed
    private hasChanged(changes: SimpleChanges, key: string, subKey?: string): boolean {
        return (
            changes[key] &&
            (!subKey || !isEqual(changes[key].currentValue[subKey], changes[key].previousValue ? changes[key].previousValue[subKey] : undefined))
        );
    }

    private updateResultSelectOptions(newOptions: unknown[]): void {
        if (newOptions) {
            const newData = unionBy(newOptions, this.objectValue, this.configSelect.fieldValue);
            this.resultSelect = { ...this.resultSelect, data: newData };
        }
    }

    // Update value and emit through the subject
    private updateValue(newValue: unknown): void {
        this.valueSubject.next(newValue);
    }

    // Initialize object filter and set initial value
    private initializeFilter() {
        if (this.configSelect && this.configSelect.rsql === undefined) {
            this.configSelect.rsql = true;
        }
        if (this.configSelect && this.configSelect.url === undefined) {
            this.configSelect.filterLocal = true;
        }
        if (this.configSelect && this.configSelect.dataKey === undefined) {
            this.configSelect.dataKey = this.configSelect?.fieldValue ?? 'id';
        }
        if (this.configSelect && this.configSelect.option) {
            this.placeholderData = this.configSelect.option;
        }
        this.objectFilter = this.tableCommonService.getFilter(this.tableId);
        this.setValueFromTableService();
    }

    // Initialize resultSelect object
    private initializeResultSelect() {
        this.resultSelect = {
            data: this.configSelect.option || [],
            error: null,
            dataUpdatedAt: Date.now(),
            errorUpdateCount: 0,
            errorUpdatedAt: 0,
            failureCount: 0,
            failureReason: null,
            fetchStatus: 'idle',
            isError: false,
            isFetched: true,
            isFetchedAfterMount: true,
            isFetching: true,
            isInitialLoading: false,
            isLoading: true,
            isLoadingError: false,
            isPaused: false,
            isPending: false,
            isPlaceholderData: false,
            isRefetchError: false,
            isRefetching: false,
            isStale: false,
            status: this.configSelect.url ? 'pending' : 'success',
            isSuccess: this.configSelect.url ? false : true,
            refetch: () => Promise.resolve(null),
            // promise: Promise.resolve(null),
        };
    }

    onValueChange(newValue: Date[] | Date): void {
        if (this.type === 'date-range') {
            this.configDate.filter = newValue;
        }

        this.updateValue(newValue);
        if (this.configSelect.url && !['select', 'select-one'].includes(this.type)) {
            this.fetchOptions(null);
        }
    }

    onChangeDropdown(event: DropdownChangeEvent) {
        if (this.type === 'select-one' && isArray(this.resultSelect.data)) {
            this.objectValue = this.resultSelect.data?.filter((item) => item[this.configSelect.fieldValue] === event.value);
        }

        this.updateValue(event.value);
    }

    onSelectChange(event: MultiSelectChangeEvent) {
        if (!event.value || (isArray(event.value) && event.value.length === 0)) {
            this.objectValue = [];
        }
        if (isArray(event.value) && event.value.length > 0) {
            if (event.originalEvent['selected']) {
                for (let index = 0; index < this.objectValue.length; index++) {
                    if (this.objectValue[index][this.configSelect.fieldValue] === event.itemValue[this.configSelect.fieldValue]) {
                        this.objectValue.splice(index, 1);
                        break;
                    }
                }
            } else if (isArray(this.resultSelect.data) && this.objectValue.length < this.resultSelect.data.length - 1) {
                this.objectValue.push(event.itemValue);
            }
        }

        this.updateValue(event.value);
    }

    onDropdowShow(): void {
        /**
         * Khi dropdown show. Nếu tìm kiếm trong option( ko call api )
         */
        if (this.configSelect.filterLocal) {
            this.dropDownFirstDone = true;

            return;
        }
        if (!this.configSelect.url) {
            this.dropDownFirstDone = true;
            return;
        }
        if (this.configSelect.url && (!this.fetchFirstDone || !this.dropDownFirstDone || (this.isHasValueUrl && this.countCallApi === 0))) {
            this.fetchOptions(null);
            this.dropDownFirstDone = true;
        }
    }

    onDropdownSearch(event: DropdownFilterEvent): void {
        /**
         * Khi dropdown search. Nếu tìm kiếm trong option( ko call api )
         */

        if (this.configSelect.filterLocal && isArray(this.resultSelect.data) && this.resultSelect.data.length > 0) return;

        if (this.configSelect.url) {
            this.debouncedGetOptions(event.filter);
        }
    }

    remove(event: MouseEvent, object: unknown): void {
        event.stopPropagation();

        // Lọc ra những đối tượng không phải là đối tượng cần xóa
        if (isArray(this.objectValue)) {
            this.objectValue = this.objectValue.filter((item) => item[this.configSelect.fieldValue] !== object[this.configSelect.fieldValue]);
        }
        if (isArray(this.value)) {
            const updatedValues = this.value.filter((item: unknown) => item !== object[this.configSelect.fieldValue]);

            this.updateValue(updatedValues);
        }
    }

    onSelectClear(value: unknown) {
        this.updateValue(value);
    }

    onDateHide() {
        this.updateValue(this.configDate.filter);
    }

    private applyFilter(value: unknown) {
        if (value === null) {
            value = undefined;
        }

        this.onChange.emit({ value: value, objects: this.objectValue });

        // if (!['select', 'select-one'].includes(this.type)) {
        //     this.onChange.emit({ value: value, objects: this.objectValue });
        // } else if ((!value && this.objectValue.length === 0) || (value && this.objectValue.length > 0)) {
        //     this.onChange.emit({ value: value, objects: this.objectValue });
        // }

        if (!this.tableId) return;
        const newFilter = { [this.field]: value };

        if (this.rsql) {
            this.tableCommonService.updateFilterRSQL(this.tableId, newFilter);
        } else {
            this.tableCommonService.updateFilter(this.tableId, newFilter);
        }
    }

    private setValueFromTableService(): void {
        if (!this.tableId) {
            if (this.configSelect && this.initValue && this.configSelect.url) {
                this.updateValue(this.initValue);
            }
            return;
        }
        if (this.objectFilter) {
            // ko sử dụng updateValue vì tránh call alij api getPage trong table
            if (this.rsql && this.objectFilter.rsql && this.field in this.objectFilter.rsql) {
                this.value = this.objectFilter.rsql[this.field];
            } else if (this.objectFilter.native && this.field in this.objectFilter.native) {
                this.value = this.objectFilter.native[this.field];
                if (this.type === 'date-range') {
                    this.configDate.filter = this.value as Date[];
                }
            }
        }

        if (this.value) {
            switch (typeof this.value) {
                case 'string':
                    this.isHasValueUrl = this.value.trim().length > 0;
                    break;
                case 'number':
                    this.isHasValueUrl = true;
                    break;
                case 'bigint':
                    this.isHasValueUrl = true;
                    break;
                case 'boolean':
                    this.isHasValueUrl = true;
                    break;
                case 'symbol':
                    this.isHasValueUrl = true;
                    break;
                case 'undefined':
                    this.isHasValueUrl = false;
                    break;
                case 'object':
                    if (isArray(this.value) && this.value.length > 0) {
                        this.isHasValueUrl = true;
                    }
                    break;
            }
        }
    }
    getOption(configSelect: ConfigSelectProps, value: unknown) {
        const pageable = `&page=${configSelect.page || '0'}&size=${configSelect.size || '100'}&sort=${configSelect.sort || 'id,desc'}`;

        // Handle RSQL filter

        if (configSelect.rsql) {
            const urlParams = this.createRsqlUrlParams(configSelect, value) + pageable;
            return this.#http.get<unknown[]>(urlParams);
        }
        // 2) Tính toán usePost:
        //    - nếu non-RSQL, mặc định true
        //    - chỉ false khi configSelect.usePost === false
        const usePost = configSelect.usePost === false ? false : true;
        if (usePost) {
            // Handle non-RSQL filter using POST request
            const body: unknown = configSelect.body || {};

            Object.keys(body).forEach((key) => {
                if (isArray(body[key]) && isEmpty(body[key])) {
                    body[key] = null;
                }
            });

            const param =
                (this.isHasValueUrl && this.countCallApi === 0) || (this.control && this.countCallApi === 0)
                    ? configSelect.paramForm ?? configSelect.param ?? configSelect.fieldLabel
                    : configSelect.param ?? configSelect.fieldLabel;

            if (param) {
                body[param] = (isString(value) || isArray(value)) && value.length > 0 ? value : undefined;

                const splitKey = param.includes('&') ? param.split('&') : [];

                // Handle Date and Date Range
                if (body[param] instanceof Date) {
                    body[param] = body[param].getTime() + 86400000 - 1; // Add 1 day in milliseconds
                } else if (this.isDateRange(body[param])) {
                    if (splitKey[0]) {
                        body[splitKey[0]] = body[param][0].getTime();
                    }
                    if (splitKey[1]) {
                        body[splitKey[1]] = body[param][1].getTime() + 86400000 - 1; // Add 1 day in milliseconds
                    }
                }

                if (isNumber(value) && !!value) body[param] = value;
            }

            const url = `${configSelect.url}?${pageable}`;
            return this.#http.post<unknown[]>(url, body);
        } else {
            // === GET với params ===
            let params = new HttpParams();
            if (configSelect.page) params = params.set('page', `${configSelect.page}`);
            if (configSelect.size) params = params.set('size', `${configSelect.size}`);
            if (configSelect.sort) params = params.set('sort', configSelect.sort);
            // filter chính
            const key = configSelect.param ?? configSelect.fieldLabel;
            if (value != null && (isString(value) ? (value as string).trim() !== '' : true)) {
                if (Array.isArray(value)) {
                    value.forEach((v) => (params = params.append(key, `${v}`)));
                } else {
                    params = params.set(key, `${value}`);
                }
            }
            // thêm các trường trong body
            if (configSelect.body) {
                Object.entries(configSelect.body).forEach(([k, v]) => {
                    if (v != null && (!Array.isArray(v) || (v as any[]).length > 0)) {
                        if (Array.isArray(v)) v.forEach((item) => (params = params.append(k, `${item}`)));
                        else params = params.set(k, `${v}`);
                    }
                });
            }

            return this.#http.get<unknown[]>(configSelect.url!, { params });
        }
    }

    createRsqlUrlParams = (configSelect: ConfigSelectProps, value: unknown) => {
        let urlParams = `${!this.fetchFirstDone ? configSelect?.urlForm ?? configSelect.url : configSelect.url}?query=`;
        const arrFilter = [];
        if (Array.isArray(value) && value.length > 0) {
            let searchValue;
            if (value.every((item) => typeof item === 'number')) {
                searchValue = value.join(',');
            } else {
                searchValue = value.map(encodeURIComponent).join('","');
            }

            arrFilter.push(
                `${!this.fetchFirstDone ? configSelect.paramForm ?? configSelect.param ?? configSelect.fieldLabel : configSelect.param ?? configSelect.fieldLabel}=in=("${searchValue}")`,
            );
        } else if (typeof value === 'string' && value.trim()) {
            arrFilter.push(
                `${!this.fetchFirstDone ? configSelect.paramForm ?? configSelect.param ?? configSelect.fieldLabel : configSelect.param ?? configSelect.fieldLabel}=='*${value.trim()}*'`,
            );
        } else if (typeof value === 'number') {
            arrFilter.push(
                `${!this.fetchFirstDone ? configSelect.paramForm ?? configSelect.param ?? configSelect.fieldLabel : configSelect.param ?? configSelect.fieldLabel}==${value}`,
            );
        }

        for (const key in configSelect.body) {
            const valueKey = configSelect.body[`${key}`];

            if (Array.isArray(valueKey) && valueKey.length > 0) {
                let searchValue;

                if (valueKey.every((item) => typeof item === 'number')) {
                    searchValue = valueKey.join(',');
                } else {
                    searchValue = valueKey.map(encodeURIComponent).join('","');
                }

                arrFilter.push(`${key}=in=(${searchValue})`);
            } else if (typeof valueKey === 'string' && valueKey.trim()) {
                arrFilter.push(`${key}==*${valueKey.trim()}*`);
            } else if (typeof valueKey === 'number') {
                arrFilter.push(`${key}==${valueKey}`);
            }
        }

        urlParams += arrFilter.join(';');
        return urlParams;
    };

    fetchOptions(searchValue): void {
        if (!this.configSelect || (!this.configSelect.url && !this.configSelect.urlForm)) return;
        if (this.requiredFilter.some((filter) => !this.configSelect.body?.[filter])) return;

        this.#query({
            queryKey: [
                this.configSelect.url,
                this.configSelect.fieldValue,
                this.configSelect.fieldLabel,
                this.configSelect.param,
                this.configSelect.paramForm,
                searchValue,
            ],
            queryFn: () => this.getOption(this.configSelect, searchValue),
            placeholderData: this.placeholderData,
            refetchOnWindowFocus: false,
        }).result$.subscribe({
            next: (res) => {
                const copyRes = Object.assign({}, res);
                copyRes.data = unionBy(res.data, this.objectValue, this.configSelect.fieldValue || 'id');
                this.resultSelect = copyRes;
                this.placeholderData = this.resultSelect.data as unknown[];
                // Fetch on init
                if (!this.fetchFirstDone && this.resultSelect.isFetching === false && this.value) {
                    this.objectValue = this.placeholderData;

                    // this.onChange.emit({ value: this.value, objects: this.objectValue });
                    this.needMoreOption = true;
                }
                if (this.resultSelect.isFetching === false) {
                    this.countCallApi++;

                    this.fetchFirstDone = true;
                    this.dropDownFirstDone = true; // chỉ cần call api lần đầu thì tương đương click drop 1 lần tránh việc khi click sẽ call api lại
                }

                if (this.selectFirstValue && copyRes.data.length > 0) {
                    this.onSelectFirstValueDone.emit(copyRes.data);
                }

                if (this.needMoreOption) {
                    this.debouncedGetOptions(null);
                    this.needMoreOption = false;
                }
            },
        });
    }

    isDateRange(value: unknown): boolean {
        if (Array.isArray(value) && value.length === 2) {
            return isDate(value[0]) || isDate(value[1]);
        }
        return false;
    }

    getOptionLabel(option: any) {
        // console.log(option);
        // console.log(Array.isArray(this.configSelect.fieldLabel));
        if (Array.isArray(this.configSelect.fieldLabel)) {
            return this.configSelect.fieldLabel.map((field) => option[field]).join(' - ');
        }
        return option[this.configSelect.fieldLabel];
    }
}
