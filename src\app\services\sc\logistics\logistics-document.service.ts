import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { LogisticsDocument } from '../../../models/interface/sc';
import { ApiResponse } from 'src/app/models/interface';

@Injectable()
export class LogisticsDocumentService extends BaseService<LogisticsDocument> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/logistics-document');
    }

    importFile(files: File[]) {
        const formData: FormData = new FormData();
        files.forEach((file) => formData.append('files', file)); // Append each file separately
        return this.http.post<ApiResponse>('/sc/api/logistics-document/import-file', formData);
    }
}
