/* version-table.component.scss */
:host ::ng-deep .version-table {
    .p-datatable-table {
        table-layout: fixed;
    }
    .p-datatable-thead > tr > th {
        text-align: center;
    }

    .version-card {
        .action-group {
            display: flex; /* biến thẻ thành flex container */
            align-items: center; /* căn block action-group lên top */
        }
        .p-card-content {
            display: flex; /* biến thẻ thành flex container */
            justify-content: space-between; /* cách đều trái – phải */
            align-items: center; /* căn block action-group lên top */
            line-height: 22px; /* Khoảng cách các dòng */
        }
        .menu-btn {
        }
    }
}

::ng-deep .p-menu .p-menu-list {
    z-index: 9999;
    visibility: visible !important;
}
:host ::ng-deep .center-menu .p-menuitem-link {
    display: flex; /* PrimeNG vốn đã dùng flex, nhắc lại cho chắc */
    justify-content: center; /* <PERSON><PERSON> ngang giữa */
    align-items: center; /* <PERSON><PERSON> dọc gi<PERSON> (nếu bạn cần) */
}

:host ::ng-deep .center-menu .p-menuitem-text {
    width: 100%; /* Chiếm hết width cha */
    text-align: center; /* Canh text giữa */
}
:host ::ng-deep .current-version .p-card-body {
    background-color: #e0f7fa !important;
}
