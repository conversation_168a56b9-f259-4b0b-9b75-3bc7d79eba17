import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { map } from 'rxjs/operators';
import { Observable } from 'rxjs';

// import { CreateProject } from 'src/app/models/interface/ptm';

@Injectable({ providedIn: 'root' })
export class ReportsBorService {
    path = '/pr/api/production-instruction';
    #http = inject(HttpClient);

    compareProductionInstruction(payload: any): Observable<any> {
        return this.#http.get<any>(`${this.path}/compare`, payload);
    }
}
