<app-sub-header
    [items]="[
        { label: 'Quản lý thông tin mua hàng', url: '/sc/bom' },
        { label: 'SC BOM', url: '/sc/bom' },
        { label: scBomOld?.code ? scBomOld?.code : 'Thêm mới' },
    ]"
    [action]="action"
></app-sub-header>

<ng-template #action>
    <ng-container *ngIf="scBomOld?.status !== 1">
        <p-button label="Lưu" (click)="form.handleSubmit()" severity="success" size="small" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_bom_edit']" />
    </ng-container>
    <p-button label="Đóng" routerLink="/sc/bom" severity="secondary" size="small" />
</ng-template>

<div class="tw-p-4">
    <p-panel header="Thông tin chung" [toggleable]="true">
        <app-form *ngIf="scBomForm" #form [formGroup]="scBomForm" layout="vertical" (onSubmit)="onSubmitCreate()">
            <div class="tw-grid lg:tw-grid-cols-2 tw-grid-cols-1 tw-gap-4" *ngIf="!isLoading">
                <app-form-item label="Tên SC BOM">
                    <input type="text" class="tw-w-full" pInputText formControlName="code" placeholder="Tên SC BOM" />
                </app-form-item>
                <app-form-item label="R&D BOM Version Name">
                    <app-filter-table
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            rsql: true,
                            url: '/sc/api/sys-bom/search',
                            paramForm: 'id',
                        }"
                        formControlName="rdBomId"
                        (onChange)="handleChangeRdBom($event)"
                    ></app-filter-table>
                </app-form-item>
                <app-form-item label="Mã kế toán">
                    <input type="text" class="tw-w-full" pInputText formControlName="accountingCode" placeholder="Mã kế toán" />
                </app-form-item>
                <app-form-item label="Ghi chú">
                    <textarea placeholder="Ghi chú" rows="5" formControlName="note" pInputTextarea class="tw-w-full"></textarea>
                </app-form-item>
                <app-form-item label="BOM Cost">
                    <app-inputNumber class="tw-w-full" formControlName="bomCost"></app-inputNumber>
                </app-form-item>
                <app-form-item label="E BOM">
                    <app-inputNumber class="tw-w-full" formControlName="eBom"></app-inputNumber>
                </app-form-item>
                <app-form-item label="M BOM">
                    <app-inputNumber class="tw-w-full" formControlName="mBom"></app-inputNumber>
                </app-form-item>
                <app-form-item label="P BOM">
                    <app-inputNumber class="tw-w-full" formControlName="pBom"></app-inputNumber>
                </app-form-item>
            </div>
        </app-form>
    </p-panel>
    <br />
    <p-panel header="Thông tin chi tiết" [toggleable]="true">
        <div class="tw-flex tw-justify-between">
            <div>
                <div class="flex items-center gap-1">
                    <span>Dự án</span>
                    <span class="text-red-600">*</span>
                </div>
                <br />
                <app-button-group-file
                    (onFileSelected)="handleImportFile($event)"
                    [attachment]="scBomOld?.attachment"
                    [types]="['excel']"
                    urlTemplate="template_sc_bom.xlsx"
                    [urlError]="urlError"
                    [disabled]="scBomOld?.status === 1"
                ></app-button-group-file>
            </div>
            <p-button label="Mã vật tư thiếu so với R&D BOM" severity="secondary" size="small" (click)="isOpenLogLevel = true"></p-button>
        </div>
        <br />
        <ng-container>
            <app-form [formGroup]="filterForm" layout="vertical">
                <div class="tw-grid lg:tw-grid-cols-4 tw-grid-cols-2 tw-gap-6">
                    <app-form-item label="Mã VNPT theo nhà sản xuất">
                        <input type="text" class="tw-w-full" pInputText formControlName="internalReference" placeholder="Mã VNPT" />
                    </app-form-item>
                    <app-form-item label="Mã nhà sản xuất">
                        <input type="text" class="tw-w-full" pInputText formControlName="manPn" placeholder="Mã nhà sản xuất" />
                    </app-form-item>
                    <app-form-item label="Mô tả">
                        <input type="text" class="tw-w-full" pInputText formControlName="productDescription" placeholder="Mô tả" />
                    </app-form-item>
                    <app-form-item label="Nhà sản xuất">
                        <input type="text" class="tw-w-full" pInputText formControlName="manufacturer" placeholder="Nhà sản xuất" />
                    </app-form-item>
                    <app-form-item label="Nhà cung cấp">
                        <input type="text" class="tw-w-full" pInputText formControlName="supplierShortName" placeholder="Nhà cung cấp" />
                    </app-form-item>
                    <app-form-item label="Số hợp đồng/Đơn hàng">
                        <input type="text" class="tw-w-full" pInputText formControlName="contractNumber" placeholder="Số hợp đồng/Đơn hàng" />
                    </app-form-item>
                    <app-form-item label="Trạng thái">
                        <p-dropdown
                            [options]="statusOptions"
                            formControlName="statusInternalReference"
                            placeholder="Chọn trạng thái"
                            optionLabel="label"
                            optionValue="value"
                            [style]="{ width: '100%' }"
                            appendTo="body"
                        ></p-dropdown>
                    </app-form-item>
                    <div class="flex tw-items-end">
                        <p-button icon="pi pi-search" (click)="filterData()"></p-button>
                    </div>
                </div>
            </app-form>

            <br />
        </ng-container>
        <div *ngIf="!isLoading">
            <p-table [value]="filteredBomItems" styleClass="p-datatable-gridlines" [scrollable]="true" scrollHeight="700px" [resizableColumns]="true">
                <ng-template pTemplate="header">
                    <tr>
                        <th>Level</th>
                        <th>Định mức</th>
                        <th>Tỉ lệ tiêu hao</th>
                        <th>Tổng số lượng yêu cầu</th>
                        <th>BOM type</th>
                        <th>FOC</th>
                        <th>Tồn khả dụng</th>
                        <th>Số lượng đặt hàng</th>
                        <th>Mã VNPT theo nhà sản xuất</th>
                        <th>Mã nhà sản xuất</th>
                        <th>Mô tả</th>
                        <th>Nhà sản xuất</th>
                        <th>Đơn giá dự toán</th>
                        <th colspan="2">Đơn giá hỏi giá</th>
                        <th colspan="2">Đơn giá</th>
                        <th>Tăng/giảm đơn giá so với hỏi giá</th>
                        <th>Tăng/giảm đơn giá so với dự toán</th>
                        <th>SPQ</th>
                        <th>MOQ</th>
                        <th>Lead time/thời gian giao hàng</th>
                        <th>Tổng số lượng đặt hàng</th>
                        <th [colSpan]="uniqueDates.length">Thời gian giao hàng theo yêu cầu của VNPT Technology</th>
                        <th colspan="2">Tổng giá trị đặt hàng</th>
                        <th>Số lượng đặt hàng thừa so với nhu cầu</th>
                        <th>Tổng giá trị đặt hàng thừa so với nhu cầu</th>
                        <th>Tổng giá trị đặt hàng thực tế chênh lệch (theo đơn giá) so với dự toán</th>
                        <th>Điều kiện giao hàng</th>
                        <th>Nhà cung cấp</th>
                        <th>Số Hợp đồng/Đơn hàng</th>
                        <th>Trạng thái</th>
                        <th>Ghi chú</th>
                    </tr>
                    <tr>
                        <th *ngFor="let i of [].constructor(13); let index = index"></th>
                        <th>USD</th>
                        <th>VNĐ</th>
                        <th>USD</th>
                        <th>VNĐ</th>
                        <th>%</th>
                        <th>%</th>
                        <th></th>
                        <th></th>
                        <th>tuần</th>
                        <th>chiếc</th>
                        <th *ngIf="uniqueDates.length === 0"></th>
                        <th *ngFor="let date of uniqueDates">
                            {{ date | date: 'dd/MM/yyyy' }}
                        </th>
                        <th>USD</th>
                        <th>VNĐ</th>
                        <th>chiếc</th>
                        <th>VNĐ</th>
                        <th>VNĐ</th>
                        <th>Incotern</th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                    </tr>
                    <tr>
                        <th></th>
                        <th>chiếc/sản phẩm</th>
                        <th>chiếc/sản phẩm</th>
                        <th>chiếc</th>
                        <th></th>
                        <th></th>
                        <th>chiếc</th>
                        <th>chiếc</th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th>USD</th>
                        <th>USD</th>
                        <th>VNĐ</th>
                        <th>USD</th>
                        <th>VNĐ</th>
                        <th>%</th>
                        <th>%</th>
                        <th></th>
                        <th></th>
                        <th>tuần</th>
                        <th>chiếc</th>
                        <th *ngIf="uniqueDates.length === 0"></th>
                        <th *ngFor="let date of uniqueDates">chiếc</th>
                        <th>USD</th>
                        <th>VNĐ</th>
                        <th>chiếc</th>
                        <th>VNĐ</th>
                        <th>VNĐ</th>
                        <th>Incotern</th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-bomItem let-index="rowIndex">
                    <tr>
                        <td>{{ bomItem.level }}</td>
                        <td>{{ bomItem.qtyPerProduct }}</td>
                        <td>{{ bomItem.attritionRate }}</td>
                        <td>{{ bomItem.requiredQuantity }}</td>
                        <td>{{ bomItem.type }}</td>
                        <td>{{ bomItem.foc }}</td>
                        <td>{{ bomItem.availableQuantity }}</td>
                        <td>{{ bomItem.orderedQuantity }}</td>
                        <td>{{ bomItem.internalReference }}</td>
                        <td>{{ bomItem.manPn }}</td>
                        <td>{{ bomItem.productDescription }}</td>
                        <td>{{ bomItem.manufacturer }}</td>
                        <td>{{ bomItem.estimatedPrice }}</td>
                        <td>{{ bomItem.usdAskPrice }}</td>
                        <td>{{ bomItem.vndAskPrice }}</td>
                        <td>{{ bomItem.usdPrice }}</td>
                        <td>{{ bomItem.vndPrice }}</td>
                        <td>{{ bomItem.priceDifferenceAsk }}</td>
                        <td>{{ bomItem.priceDifference }}</td>
                        <td>{{ bomItem.spq }}</td>
                        <td>{{ bomItem.moq }}</td>
                        <td>{{ bomItem.deliveryTime }}</td>
                        <td>{{ bomItem.finalOrderedQuantity }}</td>
                        <th *ngIf="uniqueDates.length === 0"></th>
                        <td *ngFor="let date of uniqueDates">
                            {{ getQuantity(bomItem, date) }}
                        </td>
                        <td>{{ bomItem.usdValue }}</td>
                        <td>{{ bomItem.vndValue }}</td>
                        <td>{{ bomItem.excessQuantity }}</td>
                        <td>{{ bomItem.excessValue }}</td>
                        <td>{{ bomItem.valueDifference }}</td>
                        <td>{{ bomItem.deliveryCondition }}</td>
                        <td>{{ bomItem.supplierShortName }}</td>
                        <td>{{ bomItem.contractNumber }}</td>
                        <td>
                            <p-tag
                                [severity]="bomItem.statusInternalReference ? mapStatusSeverity[bomItem.statusInternalReference] : 'secondary'"
                                [value]="bomItem.statusInternalReference ? mapStatus[bomItem.statusInternalReference] : 'Chưa tạo đơn'"
                            ></p-tag>
                        </td>
                        <td>{{ bomItem.note }}</td>
                    </tr>
                </ng-template>
            </p-table>
        </div>
    </p-panel>
</div>

<p-dialog [style]="{ minWidth: '500px', maxWidth: '900px' }" [(visible)]="isOpenConfirmModal" [modal]="true" [closable]="true" [header]="'Cảnh báo'">
    <div class="tw-mt-1">
        <p-table [value]="warningEntries" styleClass="p-datatable-gridlines" [scrollable]="true" scrollHeight="500px" [resizableColumns]="true">
            <ng-template pTemplate="header">
                <tr>
                    <th>Level</th>
                    <th>Lỗi</th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-row>
                <tr>
                    <td>{{ row.level }}</td>
                    <td>{{ row.error }}</td>
                </tr>
            </ng-template>
        </p-table>
        <div class="tw-flex tw-justify-end tw-mt-4 tw-space-x-3">
            <p-button label="Thông qua" (click)="createBomData()" severity="primary" size="small"></p-button>
            <p-button label="Hủy" (click)="isOpenConfirmModal = false" severity="secondary" size="small"></p-button>
        </div>
    </div>
</p-dialog>

<p-dialog
    [style]="{ minWidth: '500px', maxWidth: '900px' }"
    [(visible)]="isOpenLogLevel"
    [modal]="true"
    [closable]="true"
    header="Danh sách mua thiếu (so với R&D BOM)"
>
    <div class="tw-mt-1">
        <p-table [value]="logLevels" styleClass="p-datatable-gridlines" [scrollable]="true" scrollHeight="500px" [resizableColumns]="true">
            <ng-template pTemplate="header">
                <tr>
                    <th>Level</th>
                    <th>Mã vật tư</th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-row let-index="rowIndex">
                <tr>
                    <td *ngIf="isFirstOccurrence(row.level, index)" [attr.rowspan]="getRowspan(row.level)">
                        {{ row.level }}
                    </td>
                    <td>{{ row.vnptManPn }}</td>
                </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="2" class="text-center">Không có dữ liệu</td>
                </tr>
            </ng-template>
        </p-table>
        <div class="tw-flex tw-justify-end tw-mt-4 tw-space-x-3">
            <p-button label="Đóng" (click)="isOpenLogLevel = false" severity="secondary" size="small"></p-button>
        </div>
    </div>
</p-dialog>
