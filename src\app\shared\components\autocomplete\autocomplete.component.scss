.editor-wrapper {
    position: relative;
    width: 100%;
  }
  
  .editor-tag {
    cursor: text;
    font-size: 16px;
    padding: 5px 0px 5px 8px;
    border-radius: 5px;
    background: white;
    border: 1px #cccccc solid;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    min-height: 38px;
  
    &:focus-within {
      border-color: #3B82F6;
      box-shadow: 0 0 0 2px rgba(0, 153, 255, 0.2);
    }
  
    & .tag-item {
      line-height: 20px;
      border-radius: 14px;
      height: 20px;
      font-size: 14px;
      display: inline-flex;
      align-items: center;
      background: #dee2e6;
      color: #495057;
      padding-left: 8px;
        
      
    }
  
    & input {
      flex: 1;
      min-width: 30px;
      margin: 0;
      padding: 3px 5px;
      border: none;
      outline: none;
      border-radius: 5px;
      height: 24px;
      font-size: 14px;
    }
  }
  
  .tag-dropdown {
    position: fixed;
    z-index: 1002;
    
    .dropdown-content {
      max-height: 200px;
      overflow-y: auto;
      background: white;
      border-radius: 5px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      border: 1px solid #ddd;
    }
    
    .dropdown-item {
      padding: 8px 12px;
      cursor: pointer;
      
      &:hover, &.active {
        background-color: #f0f0f0;
      }
    }
    
    .no-suggestions {
      padding: 10px;
      color: #666;
      font-style: italic;
      text-align: center;
    }
  }