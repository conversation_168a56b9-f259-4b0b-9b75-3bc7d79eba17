import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { LogisticsEvaluate } from '../../../models/interface/sc';
import { ApiResponse } from 'src/app/models/interface';

@Injectable()
export class LogisticsEvaluateService extends BaseService<LogisticsEvaluate> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/logistics-evaluate');
    }

    importFile(file: File) {
        const formData: FormData = new FormData();
        formData.append('file', file);
        return this.http.post<ApiResponse>('/sc/api/logistics-evaluate/import-file', formData);
    }
}
