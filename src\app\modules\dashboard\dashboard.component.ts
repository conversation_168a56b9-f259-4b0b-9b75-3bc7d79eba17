import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardModule } from 'primeng/card';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { RouterLink } from '@angular/router';
import { environment } from 'src/environments/environment';

@Component({
    selector: 'app-dashboard',
    templateUrl: './dashboard.component.html',
    standalone: true,
    imports: [CommonModule, HasAnyAuthorityDirective, CardModule, RouterLink],
})
export class DashboardComponent implements OnInit {
    itemsHeader = [{ label: '<PERSON>ệ thống điều hành kinh doanh' }];
    environment = environment;

    domain = window.location.origin;
    isDomainSmartQc = false;

    ngOnInit(): void {
        if (this.domain && this.domain.includes('smart-qc')) {
            this.isDomainSmartQc = true;
        } else {
            this.isDomainSmartQc = false;
        }
    }
}
