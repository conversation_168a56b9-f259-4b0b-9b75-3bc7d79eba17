import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { PaymentLotDeliverPort } from '../../../models/interface/sc';

@Injectable()
export class PaymentLotDeliveryPortService extends BaseService<PaymentLotDeliverPort> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/payment-lot-delivery-port');
    }
}
