import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { RangeDateContentComponent } from './range-date-content.component';
import { isArray, isDate } from 'lodash';
import Common from 'src/app/utils/common';
import { ConfigDate } from '../filter-table/filter-table.component';

@Component({
    selector: 'app-range-date',
    templateUrl: './range-date.component.html',
    styleUrls: ['./range-date.component.scss'],
})
export class RangeDateComponent implements OnInit, OnDestroy {
    @Input() disabled: boolean = false;
    @Input() placeholder: string = 'dd/MM/yyyy';
    @Input() configDate: ConfigDate;
    @Output() onChange: EventEmitter<Date[]> = new EventEmitter();

    dateString: string;
    private ref: DynamicDialogRef | undefined;

    constructor(private dialogService: DialogService) {}

    ngOnInit(): void {
        this.dateString = this.formatRangeDate(this.configDate.filter as Date[]);
    }

    focusInput(): void {
        this.ref = this.dialogService.open(RangeDateContentComponent, {
            header: '',
            modal: true,
            style: { top: '-10rem' },
            data: { configDate: this.configDate },
        });

        this.ref.onClose.subscribe((rangeDate: Date[]) => {
            this.dateString = this.formatRangeDate(rangeDate);

            this.onChange.emit(rangeDate);
        });
    }

    private formatRangeDate(rangeDate: Date[]): string {
        if (isArray(rangeDate)) {
            const startDate = this.formatDate(rangeDate[0]);
            const endDate = this.formatDate(rangeDate[1]);
            return !startDate && !endDate ? null : `${startDate} - ${endDate}`;
        }
        return null;
    }

    private formatDate(date: Date) {
        return isDate(date)
            ? Common.formatDateWithPattern(date.getTime(), this.configDate.dateFormat === 'dd/MM/yy' ? 'dd/MM/yyyy' : this.configDate.dateFormat)
            : '';
    }

    ngOnDestroy(): void {
        if (this.ref) {
            this.ref.close();
        }
    }
}
