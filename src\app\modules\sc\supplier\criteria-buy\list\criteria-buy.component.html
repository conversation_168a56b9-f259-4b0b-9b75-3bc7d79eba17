<app-sub-header
    [items]="[{ label: 'Quản lý nhà cung cấp' }, { label: 'Đánh giá trước mua' }]"
    [action]="actionHeader"
></app-sub-header>

<ng-template #actionHeader>
    <p-button *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_criteria_buy_edit']" routerLink="create" label="Tạo mới" severity="success" />
</ng-template>
<div style="padding: 1rem 1rem 0 1rem">
    <app-table-common
        stt="true"
        [tableId]="tableId"
        [columns]="columns"
        [data]="state.data"
        [loading]="state.isFetching"
        [filterTemplate]="filterTemplate"
        name="Danh sách đánh giá"
        selectionMode="multiple"
        [funcDelete]="deleteSelected"
        [authoritiesDelete]="['ROLE_SYSTEM_ADMIN', 'sc_criteria_buy_delete']"
    >
        <ng-template #filterTemplate>
            <tr>
                <th></th>
                <th></th>
                <th [appFilter]="[tableId, 'code']">
                    <app-filter-table [tableId]="tableId" field="code"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'createBy']">
                    <app-filter-table [tableId]="tableId" field="createBy"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'createDate']">
                    <app-filter-table
                        [tableId]="tableId"
                        [rsql]="true"
                        field="createDate"
                        type="date-range"
                        placeholder="Ngày đánh giá"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'note']">
                    <app-filter-table [tableId]="tableId" field="note"></app-filter-table>
                </th>
            </tr>
        </ng-template>
    </app-table-common>
</div>
