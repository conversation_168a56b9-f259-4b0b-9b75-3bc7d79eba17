import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { ButtonModule } from 'primeng/button';
import { CheckboxModule } from 'primeng/checkbox';
import { InputTextModule } from 'primeng/inputtext';
import { TagModule } from 'primeng/tag';
import { TABLE_KEY } from 'src/app/models/constant';
import { Column, EventPopupSubmit, State } from 'src/app/models/interface';
import { StateService } from 'src/app/services/administration/process/state.service';
import { PopupComponent } from 'src/app/shared/components/popup/popup.component';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { TableCommonService } from 'src/app/shared/table-module/table.common.service';
import { AutoCompleteCompleteEvent, AutoCompleteModule } from 'primeng/autocomplete';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { TrimmedFormControl } from 'src/app/utils/form';
import { InputNumberModule } from 'primeng/inputnumber';
@Component({
    selector: 'app-state',
    standalone: true,
    templateUrl: './state.component.html',
    imports: [
        TableCommonModule,
        CommonModule,
        SubHeaderComponent,
        HasAnyAuthorityDirective,
        TagModule,
        ButtonModule,
        CheckboxModule,
        PopupComponent,
        FormCustomModule,
        InputTextModule,
        AutoCompleteModule,
        ReactiveFormsModule,
        FormsModule,
        InputNumberModule,
    ],
    providers: [StateService],
})
export class StateComponent implements OnInit, AfterViewInit {
    @ViewChild('templateActive') templateActive: TemplateRef<Element>;
    @ViewChild('templateAction') templateAction: TemplateRef<Element>;
    formCreate: FormGroup;
    allKeyEntity: string[] = [];
    filteredKeyEntity: string[] = [];
    isCreate: boolean = true;
    modelEdit: State;
    @ViewChild('childApppopup') childApppopup: PopupComponent;

    tableId: string = TABLE_KEY.STATE;
    state: QueryObserverBaseResult<State[]>;
    columns: Column[] = [];
    itemsHeader = [{ label: 'Quản lý trạng thái' }, { label: 'Danh sách' }];
    constructor(
        private stateService: StateService,
        private tableCommonService: TableCommonService,
        private fb: FormBuilder,
        private loadingService: LoadingService,
    ) {}
    ngAfterViewInit(): void {
        setTimeout(() => {
            this.columns = [
                { field: 'name', header: 'Tên', default: true },
                { field: 'description', header: 'Mô tả' },
                { field: 'keyEntity', header: 'Key' },
                { field: 'value', header: 'Value' },
                { field: 'active', header: 'Active', body: this.templateActive },
                { field: 'action', header: '', body: this.templateAction },
            ];
        });
    }

    ngOnInit(): void {
        this.tableCommonService
            .init<State>({
                tableId: this.tableId,
                queryFn: (filter) => this.stateService.getPageTableCustom(filter),
                configFilterRSQL: {
                    name: 'Text',
                    description: 'Text',
                    keyEntity: 'Text',
                    active: 'Number',
                },
            })
            .subscribe((state) => {
                this.state = state;
            });
        this.getAllKey();
        this.formCreate = this.fb.group({
            name: new TrimmedFormControl(null, Validators.required),
            description: new TrimmedFormControl(null),
            keyEntity: new TrimmedFormControl(null, Validators.required),
            value: new FormControl(null, Validators.required),
            color: null,
        });
    }

    getAllKey() {
        this.stateService.getAllKeyEntity().subscribe({
            next: (res) => {
                this.allKeyEntity = res;
            },
        });
    }
    handleCreate(event: EventPopupSubmit<{ name: string; description: string; keyEntity: string }>) {
        this.loadingService.show();
        if (this.isCreate) {
            this.stateService.create({ ...event.value, active: false }).subscribe({
                next: () => {
                    this.loadingService.hide();
                    this.state.refetch();
                    this.getAllKey();

                    event.close();
                },
                error: () => {
                    this.loadingService.hide();
                },
            });
        } else {
            this.stateService.update({ ...this.modelEdit, ...event.value }).subscribe({
                next: () => {
                    this.loadingService.hide();
                    this.state.refetch();
                    this.getAllKey();

                    event.close();
                },
                error: () => {
                    this.loadingService.hide();
                },
            });
        }
    }

    rowSelectable = (rowData: State) => {
        return !rowData.active;
    };

    deleteSelectedState = (ids: number[]) => {
        return this.stateService.batchDelete(ids);
    };

    openEdit(value: State) {
        this.isCreate = false;
        this.modelEdit = value;
        this.formCreate = this.fb.group({
            name: new FormControl(value.name, Validators.required),
            description: new FormControl(value.description),
            keyEntity: new FormControl({ value: value.keyEntity, disabled: value.active }, Validators.required),
            value: new FormControl({ value: value.value, disabled: value.active }, Validators.required),
            color: new FormControl(value.color, Validators.required),
        });
        this.childApppopup.openDialog();
    }

    filterKey(event: AutoCompleteCompleteEvent) {
        const filtered: string[] = [];
        const query = event.query;

        for (let i = 0; i < this.allKeyEntity.length; i++) {
            const key = this.allKeyEntity[i];
            if (key.toLowerCase().indexOf(query.toLowerCase()) === 0) {
                filtered.push(key);
            }
        }
        this.filteredKeyEntity = filtered;
    }
}
