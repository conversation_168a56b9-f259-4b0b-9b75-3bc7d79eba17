<app-sub-header [items]="itemsHeader" [action]="actionHeader">
    <ng-template #actionHeader>
        <p-button size="small" label="Nhập excel" (click)="visible = true" severity="success" />
        <p-button size="small" routerLink="create" label="Tạo mới" />
    </ng-template>
</app-sub-header>
<div style="padding: 1rem 1rem 0 1rem">
    <app-table-common
        [tableId]="tableId"
        [columns]="columns"
        [data]="state.data"
        [loading]="state.isFetching"
        [filterTemplate]="filterTemplate"
        [funcDelete]="deleteSelectedUser"
        [authoritiesDelete]="['ROLE_SYSTEM_ADMIN']"
        [deleteButton]="true"
        name="Danh sách người dùng"
        [actionTemplate]="actionTemplate"
        [funcDownload]="deleteSelectedUser"
    >
        <ng-template #actionTemplate>
            <p-button
                icon="pi pi-lock"
                [severity]="idSelects.length === 0 ? 'secondary' : 'warning'"
                [outlined]="true"
                [size]="'small'"
                [disabled]="idSelects.length === 0"
                [ngClass]="{
                    'qc-disabled': idSelects.length === 0,
                    'table-btn-delete': true,
                }"
                (click)="funcDeactiveUser()"
            ></p-button>
            <div style="border-right: 1px solid #ebeff5; height: 2.25rem; width: 1px"></div>
        </ng-template>
        <ng-template #filterTemplate>
            <tr>
                <th></th>
                <th [appFilter]="[tableId, 'email']">
                    <app-filter-table [tableId]="tableId" field="email" placeholder="Tài khoản..."></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'fullName']">
                    <app-filter-table [tableId]="tableId" field="fullName" placeholder="Tên..."></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'phone']">
                    <app-filter-table [tableId]="tableId" field="phone" placeholder="Sdt..."></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'role']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="roleId"
                        type="select-one"
                        [rsql]="false"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'displayName',
                            param: 'displayName',
                            options: roleOption,
                            filterLocal: true,
                        }"
                        placeholder="Vai trò..."
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'area']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="areaId"
                        type="select-one"
                        [rsql]="false"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            param: 'name',
                            url: '/api/area/search',
                            size: 100,
                            filterLocal: true,
                        }"
                        placeholder="Khu vực..."
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'deparment']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="departmentId"
                        type="select-one"
                        [rsql]="false"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            param: 'name',
                            url: '/api/department/search',
                        }"
                        placeholder="Phòng ban..."
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'active']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="active"
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'label',
                            options: [
                                {
                                    label: 'Hoạt động',
                                    value: '1',
                                },
                                {
                                    label: 'Chưa hoạt động',
                                    value: '0',
                                },
                                {
                                    label: 'Đăng kí',
                                    value: 'null',
                                },
                            ],
                        }"
                        placeholder="tình trạng..."
                    ></app-filter-table>
                </th>
            </tr>
        </ng-template>
        <ng-template #templateEmail let-rowData>
            <a [routerLink]="rowData.id">{{ rowData.email }}</a>
        </ng-template>
        <ng-template #templateActive let-rowData>
            <p-tag [severity]="getSeverity(rowData.active)" [value]="getStateText(rowData.active)"></p-tag>
        </ng-template>

        <ng-template #templateDeparment let-rowData>
            <ng-container *ngFor="let item of rowData.departments">
                <p-tag [value]="item.name"></p-tag>
            </ng-container>
        </ng-template>
        <ng-template #templateArea let-rowData>
            <ng-container *ngFor="let item of rowData.areas">
                <p-tag severity="success" [value]="item.name"></p-tag>
            </ng-container>
        </ng-template>
        <ng-template #templateRole let-rowData>
            <ng-container *ngFor="let item of rowData.roles">
                <p-tag *ngIf="!item.type" ngClass="tw-mr-1" [value]="item.displayName"></p-tag>
            </ng-container>
        </ng-template>

        <ng-template #templateEmail let-rowData>
            <a></a>
        </ng-template>
    </app-table-common>

    <p-dialog
        header="Nhập thông tin tài khoản"
        [(visible)]="visible"
        [modal]="true"
        [breakpoints]="{ '1199px': '50vw', '575px': '30vw' }"
        [style]="{ width: '40vw' }"
        (onHide)="visible = false; fileUpload = null; errorFileUrl = null"
        #dialogFile
    >
        <div>
            <span class="qc-upload-bttn">
                <div style="display: flex">
                    <span class="material-icons input-upload-icon" style="color: #3490dc; margin: auto; font-size: x-large"> backup </span>
                    <span style="margin-left: 9px; margin-top: 4px">Chọn file</span>
                </div>
                <input #fileInput type="file" accept=".xls, .xlsx, xlsm" class="qc-upload-input" (input)="onSelectFile($event)" #inputFile />
            </span>
            <span style="padding-left: 3px">{{ fileUpload ? fileUpload.name : '' }}</span>
            <div *ngIf="!errorFileUrl" class="tw-mt-2">
                Vui lòng tải file mẫu
                <a class="tw-text-green-500" [href]="template">tại đây</a>
            </div>
            <div *ngIf="errorFileUrl" class="tw-mt-2">Tải xuống file lỗi <a download [href]="errorFileUrl" class="text-red-500">tại đây</a></div>
        </div>

        <ng-template pTemplate="footer">
            <div>
                <p-button [disabled]="!fileUpload" severity="primary" (click)="onUpload(fileInput)">Xác nhận</p-button>
                <p-button label="Hủy" [text]="true" [raised]="true" severity="secondary" (click)="visible = false; errorFileUrl = null"></p-button>
            </div>
        </ng-template>
    </p-dialog>
</div>
