import {AfterViewInit, Component, inject, Input, OnInit} from "@angular/core";
import {TableCommonModule} from "../../../../../shared/table-module/table.common.module";
import {CommonModule} from "@angular/common";
import {RouterModule} from "@angular/router";
import {ButtonModule} from "primeng/button";
import {TagModule} from "primeng/tag";
import {TooltipModule} from "primeng/tooltip";
import {ChipModule} from "primeng/chip";
import {DialogModule} from "primeng/dialog";
import {FormCustomModule} from "../../../../../shared/form-module/form.custom.module";
import {InputTextModule} from "primeng/inputtext";
import {InputTextareaModule} from "primeng/inputtextarea";
import {PaginatorModule} from "primeng/paginator";
import {FormBuilder, FormGroup, ReactiveFormsModule, Validators} from "@angular/forms";
import {PanelModule} from "primeng/panel";
import {ButtonGroupFileComponent} from "../../../../../shared/components/button-group-file/button-group-file.component";
import {TableModule} from "primeng/table";
import {ApproveService} from "../../../../../services/smart-qc/masterdata/approve.service";
import {TableCommonService} from "../../../../../shared/table-module/table.common.service";
import {PoService} from "../../../../../services/sc/po/po.service";
import {BoItem, Rfq, RfqInventory, RfqItem} from "../../../../../models/interface/sc";
import {RfqService} from "../../../../../services/sc/sc-bom/rfq.service";
import {ApiResponse} from "../../../../../models/interface";
import {LoadingService} from "../../../../../shared/services/loading.service";
import {AlertService} from "../../../../../shared/services/alert.service";
import {EditableInputComponent} from "../../../../../shared/edit/editable-input.component";
import {RFQ_ITEM_TYPE} from "../../../../../models/constant/sc";

@Component({
    selector: 'app-ask-price-state-one',
    standalone: true,
    imports: [
        TableCommonModule,
        CommonModule,
        RouterModule,
        ButtonModule,
        TagModule,
        TooltipModule,
        ChipModule,
        DialogModule,
        FormCustomModule,
        InputTextModule,
        InputTextareaModule,
        PaginatorModule,
        ReactiveFormsModule,
        PanelModule,
        ButtonGroupFileComponent,
        TableModule,
        EditableInputComponent,
    ],
    templateUrl: './state-one.component.html',
    providers: [ApproveService, TableCommonService, PoService, RfqService],
})
export class StateOneComponent implements OnInit, AfterViewInit {
    // Declare
    @Input() rfqOld: Rfq;
    rfqItemsDisplay?: RfqItem[];
    rfqForm: FormGroup;
    uniqueTimes = new Set<number>();
    sortedUniqueTimes: number[] = [];
    itemQuantityMap = new Map<string, number>();
    urlError: string = null;
    urlErrorInventory: string = null;
    // End Declare

    // Inject service
    rfqService = inject(RfqService);
    loadingService = inject(LoadingService);
    alertService = inject(AlertService);
    // End inject service

    constructor(private formBuilder: FormBuilder,) {
    }

    ngOnInit() {
        this.initForm();
        this.genRfqItemsData();
        // console.log(this.rfqOld.inventoryAttachment)
    }

    ngAfterViewInit() {

    }

    initForm(): void {
        this.rfqForm = this.formBuilder.group({
            id: [{ value: this.rfqOld?.id, disabled: false}, []],
            code: [{ value: this.rfqOld?.code, disabled: true }],
            name: [{ value: this.rfqOld?.name, disabled: false}, [Validators.maxLength(255)]],
            note: [{ value: this.rfqOld?.note, disabled: false}, []],
        });
    }

    genRfqItemsDisplay(rfqItems: RfqItem[]) {
        const rfqItemsScBom: RfqItem[] = rfqItems.filter(item => item.type === RFQ_ITEM_TYPE.SC_BOM);  // Loại BOM
        const rfqItemsRetail: RfqItem[] = rfqItems.filter(item => item.type === RFQ_ITEM_TYPE.RETAIL); // Loại Retail

        const uniqueRetailItems: RfqItem[] = [];
        rfqItemsRetail.forEach(item => {
            const isDuplicate = uniqueRetailItems.some(existingItem =>
                existingItem.project === item.project && existingItem.accountingCode === item.accountingCode
            );

            if (!isDuplicate) {
                uniqueRetailItems.push({ project: item.project, accountingCode: item.accountingCode }); // Tạo item mới và thêm vào danh sách
            }
        });

        this.rfqItemsDisplay = [
            ...rfqItemsScBom,
            ...uniqueRetailItems
        ];
    }

    handleDownloadFile() {

    }

    handleClearFile() {

    }

    handleUploadItemsFile(file: File) {
        this.loadingService.show();
        this.rfqService.importItemFile(file as File, this.rfqOld.id).subscribe({
            next: (res: ApiResponse) => {
                if (res.code === 1) {
                    this.rfqOld.items = res.data as unknown as RfqItem[];
                    this.alertService.success('Thành công');
                    this.genRfqItemsData();
                } else {
                    this.alertService.error('Có lỗi xảy ra khi đọc file');
                    this.urlError = res.message;
                }
                this.loadingService.hide();
            },
            error: (error) => {
                this.loadingService.hide();
                // this.alertService.handleError(error);
            },
        });
    }

    genRfqItemsData() {
        this.rfqOld?.items?.forEach(item => {
            item.rfqItemDates?.forEach(entry => {
                if (entry.date !== undefined) {
                    this.uniqueTimes.add(entry.date);

                    // Tạo key từ các trường của PriceAskItem + time
                    const key = `${item.project || ''}-${item.accountingCode || ''}-${item.productName || ''}-${item.productShortName || ''}-${entry.date}`;
                    this.itemQuantityMap.set(key, entry.quantity || 0);
                }
            });
        });
        this.sortedUniqueTimes = Array.from(this.uniqueTimes).sort((a, b) => a - b);

        this.genRfqItemsDisplay(this.rfqOld?.items);
    }

    getQuantity(item: RfqItem, time: number): number {
        return this.itemQuantityMap.get(this.generateKey(item, time)) || 0;
    }

    private generateKey(item: RfqItem, time: number): string {
        return [
            item.project || '',
            item.accountingCode || '',
            item.productName || '',
            item.productShortName || '',
            time
        ].join('-');
    }

    handleUploadInventoryFile(file: File) {
        this.loadingService.show();
        this.rfqService.importInventoryFile(file as File, this.rfqOld.id).subscribe({
            next: (res: ApiResponse) => {
                if (res.code === 1) {
                    this.rfqOld.inventories = res.data as unknown as RfqInventory[];
                    this.alertService.success('Thành công');
                } else {
                    this.alertService.error('Có lỗi xảy ra khi đọc file');
                    this.urlErrorInventory = res.message;
                }
                this.loadingService.hide();
            },
            error: (error) => {
                this.loadingService.hide();
                this.alertService.handleError(error);
            },
        });
    }

    saveGeneralInfo() {
        const rfqNewData = this.rfqForm.getRawValue();
        const dataUpdate = {...this.rfqOld, ...rfqNewData};
        this.loadingService.show();
        this.rfqService.update(dataUpdate).subscribe({
            next: (res) => {
                this.alertService.success('Cập nhật thông tin thành công');
                this.loadingService.hide();
                this.rfqOld = {...this.rfqOld, name: dataUpdate.name, note: dataUpdate.note};
            },
            error: (error) => {
                this.loadingService.hide();
                this.alertService.handleError(error);
            },
        });
    }

    protected readonly RFQ_ITEM_TYPE = RFQ_ITEM_TYPE;
}
