import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, OnInit, TemplateRef, ViewChild, inject } from '@angular/core';
import { RouterModule } from '@angular/router';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { ConfirmationService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';
import { ChipModule } from 'primeng/chip';
import { AuthService } from 'src/app/core/auth/auth.service';
import { TABLE_KEY } from 'src/app/models/constant';
import { ApiResponse, Column, User } from 'src/app/models/interface';
import { ApproveService } from 'src/app/services/smart-qc/masterdata/approve.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { TableCommonService } from 'src/app/shared/table-module/table.common.service';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { MonthlyExpenses, Po } from 'src/app/models/interface/sc';
import { PoService } from 'src/app/services/sc/po/po.service';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { FileService } from 'src/app/shared/services/file.service';
import { DialogModule } from 'primeng/dialog';
import { FormCustomModule } from '../../../../shared/form-module/form.custom.module';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { PaginatorModule } from 'primeng/paginator';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { TabViewModule } from 'primeng/tabview';
import { MonthlyExpensesService } from '../../../../services/sc/monthly-expenses/monthly-expenses.service';
import { LogisticsService } from '../../../../services/sc/logistics/logistics.service';
import { ButtonGroupFileComponent } from '../../../../shared/components/button-group-file/button-group-file.component';
import { CalendarModule } from 'primeng/calendar';
import Common from '../../../../utils/common';
import { FormComponent } from '../../../../shared/form-module/form-base/form.component';
import { BaseUserService } from '../../../../services/administration/admin/user.service';
import { SupplierPriority } from '../../../../models/constant/sc';

@Component({
    selector: 'app-sc-po',
    standalone: true,
    imports: [
        TableCommonModule,
        CommonModule,
        RouterModule,
        ButtonModule,
        TagModule,
        TooltipModule,
        ChipModule,
        SubHeaderComponent,
        HasAnyAuthorityDirective,
        DialogModule,
        FormCustomModule,
        InputTextModule,
        InputTextareaModule,
        PaginatorModule,
        ReactiveFormsModule,
        TabViewModule,
        ButtonGroupFileComponent,
        CalendarModule,
    ],
    templateUrl: './monthly-expenses.component.html',
    providers: [ApproveService, TableCommonService, PoService, MonthlyExpensesService, LogisticsService, BaseUserService],
})
export class MonthlyExpensesComponent implements OnInit, AfterViewInit {
    @ViewChild('templateName') templateName: TemplateRef<Element>;
    @ViewChild('templateState') templateState: TemplateRef<Element>;
    @ViewChild('form') formComponent!: FormComponent;

    fileService = inject(FileService);
    logisticsService = inject(LogisticsService);
    monthlyExpensesService = inject(MonthlyExpensesService);
    baseUserService = inject(BaseUserService);

    tableCommonService = inject(TableCommonService);
    loadingService = inject(LoadingService);
    confirmationService = inject(ConfirmationService);
    alertService = inject(AlertService);
    authService = inject(AuthService);
    columns: Column[] = [];

    userOptions: User[];

    // Table
    tableIdForwarder: string = TABLE_KEY.MONTHLY_EXPENSES_FORWARDER;
    stateForwarder: QueryObserverBaseResult<MonthlyExpenses[]>;

    tableIdExpressDelivery: string = TABLE_KEY.MONTHLY_EXPENSES_EXPRESS_DELIVERY;
    stateExpressDelivery: QueryObserverBaseResult<MonthlyExpenses[]>;

    tableIdInsurance: string = TABLE_KEY.MONTHLY_EXPENSES_INSURANCE;
    stateInsurance: QueryObserverBaseResult<MonthlyExpenses[]>;

    // End table

    itemsHeader = [{ label: 'Quản lý phê duyệt' }, { label: 'Danh sách phê duyệt', url: 'approve' }];
    actionHeader: TemplateRef<Element>;
    rowSelects: Po[] = [];

    // Popup create
    isOpenAddModal: boolean = false;
    addFormGroup: FormGroup;
    // End Popup create

    @ViewChild('templateFileName') templateFileName: TemplateRef<Element>;
    @ViewChild('templateAdditionUrl') templateAdditionUrl: TemplateRef<Element>;
    @ViewChild('templateLogistics') templateLogistics: TemplateRef<Element>;

    mapLogistics: Record<number, string> = {};
    selectedTab = 0;
    urlError: string;
    fileSelected: File;

    tabHeaders = ['Tổng hợp chi phí vận chuyển', 'Tổng hợp chi phí bảo hiểm', 'Tổng hợp chi phí chuyển phát nhanh'];

    constructor(private fb: FormBuilder) {}

    ngOnInit() {
        console.log('Init monthly expenses');
        this.logisticsService.getPage('query=&page=0&size=100&sort=id,desc').subscribe({
            next: (res) => {
                res.body.forEach((item) => {
                    this.mapLogistics[item.id] = item.shortName;
                });
            },
        });

        this.tableCommonService
            .init<MonthlyExpenses>({
                tableId: this.tableIdForwarder,
                queryFn: (filter) => this.monthlyExpensesService.getPageTableCustom(filter),
                defaultParamsRSQL: {
                    type: 0,
                },
                configFilterRSQL: {
                    type: 'Number',
                    fileName: 'Text',
                    logisticId: 'SetLong',
                    date: 'Month',
                    created: 'DateRange',
                    createdBy: 'Text',
                },
            })
            .subscribe((state) => {
                this.stateForwarder = state;
            });

        this.tableCommonService
            .init<MonthlyExpenses>({
                tableId: this.tableIdExpressDelivery,
                queryFn: (filter) => this.monthlyExpensesService.getPageTableCustom(filter),
                configFilterRSQL: {
                    fileName: 'Text',
                    logisticId: 'SetLong',
                    date: 'Month',
                    created: 'DateRange',
                    createdBy: 'Text',
                    type: 'Number',
                },
                defaultParamsRSQL: {
                    type: 2,
                },
            })
            .subscribe((state) => {
                this.stateExpressDelivery = state;
            });

        this.tableCommonService
            .init<MonthlyExpenses>({
                tableId: this.tableIdInsurance,
                queryFn: (filter) => this.monthlyExpensesService.getPageTableCustom(filter),
                configFilterRSQL: {
                    fileName: 'Text',
                    logisticId: 'SetLong',
                    date: 'Month',
                    created: 'DateRange',
                    createdBy: 'Text',
                    type: 'Number',
                },
                defaultParamsRSQL: {
                    type: 1,
                },
            })
            .subscribe((state) => {
                this.stateInsurance = state;
            });

        this.initAddForm();
        this.getUserHavePrivilege();
    }

    getUserHavePrivilege() {
        this.baseUserService.getUserHavePrivilege(['sc_monthly_expenses_edit', 'ROLE_SYSTEM_ADMIN']).subscribe({
            next: (res: User[]) => {
                this.userOptions = res;
                if (!this.userOptions.some((option) => option.fullName === 'admin')) {
                    this.userOptions.push({ fullName: 'admin', email: 'admin' });
                }
            },
            error: () => {},
        });
    }

    initAddForm() {
        this.addFormGroup = null;
        this.addFormGroup = this.fb.group({
            logisticId: [null, [Validators.required]],
            date: [null, [Validators.required]],
            type: [this.selectedTab],
            attachmentFile: [null, [Validators.required]],
        });
        this.urlError = null;
        this.fileSelected = null;

        Common.resetFormState(this.addFormGroup);
        if (this.formComponent) {
            this.formComponent.resetSubmitState();
        }
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.columns = [
                {
                    field: 'fileName',
                    header: 'Tên file',
                    body: this.templateFileName,
                },
                {
                    field: 'logisticId',
                    header: 'Nhà cung cấp',
                    body: this.templateLogistics,
                },
                {
                    field: 'date',
                    header: 'Tháng',
                    type: 'date',
                    format: 'MM/yyyy',
                    default: true,
                },
                {
                    field: 'created',
                    header: 'Thời gian nhập',
                    type: 'date',
                    format: 'HH:mm:ss dd/MM/yyyy',
                    default: true,
                },
                {
                    field: 'createdBy',
                    header: 'Người nhập',
                },
            ];
        });
    }

    handleDownloadForwarderFileName(url: string) {
        this.fileService.downLoadFileByService(url, '/sc/api');
    }

    deleteSelected = (ids: number[]) => {
        return this.monthlyExpensesService.batchDelete(ids);
    };

    onSubmitCreate(formData) {
        const data = this.addFormGroup.getRawValue();
        this.loadingService.show();
        this.monthlyExpensesService.importFile(this.fileSelected, data.date?.getTime(), data.type, data.logisticId).subscribe({
            next: (res: ApiResponse) => {
                if (res.code === 1) {
                    this.urlError = null;
                    this.alertService.success('Thành công');
                    this.isOpenAddModal = false;
                    this.reloadStateTable();
                } else {
                    this.urlError = res.message;
                    this.alertService.error('Có lỗi xảy ra khi đọc file');
                }
                this.loadingService.hide();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    reloadStateTable() {
        if (this.selectedTab === 0) {
            this.stateForwarder.refetch();
        } else if (this.selectedTab === 1) {
            this.stateInsurance.refetch();
        } else {
            this.stateExpressDelivery.refetch();
        }
    }

    handleUploadFile(file: File) {
        setTimeout(() => {
            this.fileSelected = file;
            this.addFormGroup.patchValue({
                attachmentFile: this.fileSelected,
            });
        });
    }

    handleTabChange(index: any) {
        this.selectedTab = index;
    }

    handleDownload() {
        this.fileService.downLoadFileByService('/template/monthly_expenses_delivery.xlsx', '/sc/api');
    }

    handleClearFile() {
        this.addFormGroup.patchValue({
            attachmentFile: null,
        });
    }

    changeLogistics(event) {
        if (event.value !== null && event.objects !== null && event.objects.length > 0) {
            const logistics = event.objects[0];
            this.addFormGroup.patchValue({
                logisticId: logistics.id,
            });
        } else {
            this.addFormGroup.patchValue({
                logisticId: null,
            });
        }
    }

    protected readonly priority = SupplierPriority;
}
