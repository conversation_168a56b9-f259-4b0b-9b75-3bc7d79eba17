.view-detail-btn {
    color: rgba(255, 255, 255, 0.8);
    background: rgba(0, 0, 0, 0.1);
    cursor: pointer;
    border-radius: 0 0 0.75rem 0.75rem;
    transition: color 0.2s, background 0.2s;
}

.view-detail-btn:hover {
    color: white;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 0 0 0.75rem 0.75rem;
}

.icon-card {
    width: 60px;
    height: 60px;
    color: rgba(0, 0, 0, 0.15);
    transition: transform .4s; /* Animation */
}

.icon-card:hover {
    transform: scale(1.2);
}

.notification-item {
    background-color: #f9f9f9;
    border-radius: 5px;
}
