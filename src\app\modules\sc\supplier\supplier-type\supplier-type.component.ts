import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, OnInit, TemplateRef, ViewChild, inject } from '@angular/core';
import { RouterLink } from '@angular/router';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { ButtonModule } from 'primeng/button';
import { TABLE_KEY } from 'src/app/models/constant';
import { Column } from 'src/app/models/interface';
import { TableCommonService } from 'src/app/shared/table-module/table.common.service';
import { TagModule } from 'primeng/tag';
import { SupplierTypeService } from 'src/app/services/sc/supplier/suppiler-type.service';
import { SupplierPriority } from 'src/app/models/constant/sc';
import { SupplierType } from '../../../../models/interface/sc';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';

@Component({
    selector: 'app-supplier-type',
    templateUrl: './supplier-type.component.html',
    standalone: true,
    imports: [RouterLink, TableCommonModule, ButtonModule, CommonModule, TagModule, SubHeaderComponent],
    providers: [SupplierTypeService],
})
export class SupplierTypeComponent implements OnInit, AfterViewInit {
    @ViewChild('templateStatus') templateStatus: TemplateRef<Element>;
    @ViewChild('templateAction') templateAction: TemplateRef<Element>;

    priority = SupplierPriority;

    tableCommonService = inject(TableCommonService);
    state: QueryObserverBaseResult<SupplierType[]>;
    columns: Column[] = [];
    tableId: string = TABLE_KEY.SUPPLIER_TYPE;
    supplierTypeServie = inject(SupplierTypeService);

    ngOnInit() {
        this.tableCommonService
            .init<SupplierType>({
                tableId: this.tableId,
                queryFn: (filter) => this.supplierTypeServie.getPageTableCustom(filter),
                configFilterRSQL: {
                    name: 'Text',
                    priority: 'Number',
                    note: 'Text',
                },
            })
            .subscribe((state) => {
                this.state = state;
            });
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.columns = [
                {
                    field: 'name',
                    header: 'Loại nhà cung cấp',
                },
                { field: 'priority', header: 'Phân loại', body: this.templateStatus, style: { 'max-width': '5rem' } },
                {
                    field: 'note',
                    header: 'Ghi chú',
                },
                {
                    header: 'Chi tiết',
                    body: this.templateAction,
                    default: true,
                    style: { 'max-width': '5rem' },
                    fixed: 'right',
                },
            ];
        }, 0);
    }

    delete = (ids: number[]) => {
        return this.supplierTypeServie.batchDelete(ids);
    };
}
