import { Directive, Input, OnInit, TemplateRef, ViewContainerRef } from '@angular/core';
import { AuthService } from '../../core/auth/auth.service';

@Directive({
    selector: '[appHasAnyAuthority]',
    standalone: true,
})
export class HasAnyAuthorityDirective implements OnInit {
    private authorities: string[] = [];

    constructor(
        private authService: AuthService,
        private templateRef: TemplateRef<Element>,
        private viewContainer: ViewContainerRef,
    ) {}

    @Input()
    set appHasAnyAuthority(value: string[]) {
        this.authorities = value;
    }

    ngOnInit(): void {
        this.authService.userObserver.subscribe(() => {
            if (this.authService.hasAuthority(this.authorities)) {
                this.viewContainer.createEmbeddedView(this.templateRef);
            }
        });
    }
}
