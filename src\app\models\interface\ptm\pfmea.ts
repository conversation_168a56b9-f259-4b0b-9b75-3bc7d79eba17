import { BaseEntity } from '../../BaseEntity';

export interface PFMEA  extends BaseEntity {
    listPFMEA: PFMEADetail[];
}

export interface PFMEADetail extends BaseEntity {
    id: number;
    line: number;
    oc: string;
    operationDescription: string;
    standard: string;   // Tiêu chuẩn/Yêu cầu/Mục tiêu

}

export interface DataPfmea extends BaseEntity {
    id: number;
    line: number;
    oc: string;
    operationDescription: string;
    standard: string;   // Tiêu chuẩn/Yêu cầu/Mục tiêu

}

export interface Pfmea {
    id?: number;
    instructionInfoId?: number;
    processFlowId?: number;
    oc: string;                // Mã OC
    od: string;                // Mô tả OC
    line: string;              // Dòng sản xuất
    target: string;           // Mục tiêu
    pfmeaErrors: PfmeaError[]; // Danh sách lỗi PFMEA
}

export interface PfmeaError {
    id: number; // ID PFMEA Error
    pfmeaId: number; // ID pfmea
    error: string; // Lỗi
    errorAffect: string; // Ảnh hưởng lỗi
    isSymbol: boolean; // Có phải ký hiệu không
    errorCause: string; // Nguyên nhân lỗi
    sev: number; // Mức độ nghiêm trọng
    prevention: string; // Phòng ngừa
    occ: number; // Tần suất xảy ra
    detection: number; // Khả năng phát hiện
    det: number; // Điểm phát hiện
    correctionAction: string; // Hành động khắc phục
    responsibility: string; // Trách nhiệm
    finishDate: number; // Ngày hoàn thành (timestamp millis)
    result: string; // Kết quả
    effectiveDate: number; // Ngày hiệu lực (timestamp millis)
    sevAfter: number; // Mức độ nghiêm trọng sau
    occAfter: number; // Tần suất xảy ra sau
    detAfter: number; // Điểm phát hiện sau
    action: number; // Action của cập nhật
    actionPayload: number; // Trạng thái cập nhật 1: Thêm, 2: Sửa, 3: Xóa
}

export interface PfmeaUpdate {
    instructionId        : number; // ID của Version HS CNSX, Thêm mới thì truyền nul
    reviewerIds          : number[]; // Danh sách người phê duyệt
    pfmeas : Pfmea[];
}
