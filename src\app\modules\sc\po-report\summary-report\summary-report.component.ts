/* eslint-disable @typescript-eslint/no-explicit-any */
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { ButtonModule } from 'primeng/button';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { CalendarModule } from 'primeng/calendar';
import { TableModule } from 'primeng/table';
import { PopupComponent } from 'src/app/shared/components/popup/popup.component';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { InputTextModule } from 'primeng/inputtext';
import { PoService } from 'src/app/services/sc/po/po.service';
import { ComboboxComponent } from 'src/app/shared/components/combobox/combobox.component';
import { EventPopupSubmit } from 'src/app/models/interface';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { FileService } from 'src/app/shared/services/file.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { Pagination, TableCommonService } from 'src/app/shared/table-module/table.common.service';
import { TABLE_KEY } from 'src/app/models/constant';
import { QueryObserverBaseResult } from '@ngneat/query';
import { OverlayPanel, OverlayPanelModule } from 'primeng/overlaypanel';
import { Column } from 'src/app/models/interface';

@Component({
    selector: 'app-po-summary-report',
    standalone: true,
    imports: [
        CommonModule,
        SubHeaderComponent,
        ButtonModule,
        TableCommonModule,
        FormCustomModule,
        CalendarModule,
        TableModule,
        PopupComponent,
        ReactiveFormsModule,
        InputTextModule,
        ComboboxComponent,
        OverlayPanelModule,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
    templateUrl: './summary-report.component.html',
    styleUrls: ['./summary-report.component.scss'],
    providers: [PoService],
})
export class SummaryReportComponent implements OnInit {
    formGroup: FormGroup;
    tableData: any[] = [];
    dateColumns: number[] = [];
    tableId: string = TABLE_KEY.SummaryReport;
    state: QueryObserverBaseResult<any[]>;
    pagination: Pagination;
    columns: Column[] = [
        { header: 'STT', field: 'poIndex', default: true, hide: false },
        { header: 'Số PO/Hợp đồng', field: 'orderNo', default: true, hide: false },
        { header: 'Ngày đặt hàng', field: 'orderDate', default: true, hide: false },
        { header: 'Nhà cung cấp', field: 'supplierShortName', default: true, hide: false },
        { header: 'Mã kế toán', field: 'accountingCode', default: true, hide: false },
        { header: 'Mã VNPT MAN PN', field: 'vnptMan', hide: false },
        { header: 'Mã MAN PN', field: 'manPn', hide: false },
        { header: 'Nhà sản xuất', field: 'manufacturer', hide: false },
        { header: 'Mô tả', field: 'description', hide: false },
        { header: 'SPQ', field: 'spq', hide: false },
        { header: 'MOQ', field: 'moq', hide: false },
        { header: 'Tổng số lượng yêu cầu', field: 'orderQuantity', hide: false },
        { header: 'Tồn khả dụng', field: 'availableQuantity', hide: false },
        { header: 'Đơn giá', field: 'orderPrice', hide: false },
        { header: 'Số lượng đặt hàng', field: 'orderedQuantity', hide: false },
        { header: 'Thành tiền đặt hàng', field: 'orderedTotal', hide: false },
        { header: 'Số lượng đã về', field: 'deliveredQuantity', hide: false },
        { header: 'Thành tiền đã về', field: 'deliveredAmount', hide: false },
        { header: 'Số lượng chưa về', field: 'remainingQuantity', hide: false },
        { header: 'Thành tiền chưa về', field: 'remainingAmount', hide: false },
        { header: 'Ngày hàng về', field: 'poDetailDayDTOS', hide: false },
    ];
    columnChoose: Column[] = [];

    @ViewChild('op') op: OverlayPanel;

    constructor(
        private fb: FormBuilder,
        private poService: PoService,
        private cdr: ChangeDetectorRef,
        private loadingService: LoadingService,
        private fileService: FileService,
        private alertService: AlertService,
        private tableCommonService: TableCommonService,
    ) {
        this.initForm();
        this.columnChoose = this.columns.filter((col: Column) => !col.hide);
    }

    ngOnInit(): void {
        this.tableCommonService
            .init<any>({
                tableId: this.tableId,
                queryFn: (filter) => this.poService.getMaterialTableData(filter, this.convertBody()),
            })
            .subscribe({
                next: (res) => {
                    this.state = res;
                    if (res.isSuccess) {
                        this.processData(res.data as any);
                    }
                },
            });

        this.tableCommonService.getPagination(this.tableId).subscribe((state) => {
            this.pagination = state;
            this.cdr.detectChanges();
        });
    }

    initForm() {
        const currentDate = new Date();
        const endDate = new Date(currentDate.getTime());
        const startDate = new Date(currentDate.getTime() - 6 * 24 * 60 * 60 * 1000);

        this.formGroup = new FormGroupCustom(this.fb, {
            startTimeCustom: [startDate],
            endTimeCustom: [endDate],
            manufacturerId: [null],
            vnptMan: [null],
            manufacturerCodes: [null],
            supplierIds: [null],
            poId: [null],
            accountingCode: [null],
        });
    }

    search() {
        const body = this.formGroup.getRawValue();

        let startDate: Date | null = null;
        let endDate: Date | null = null;

        if (body.startTimeCustom) {
            startDate = new Date(body.startTimeCustom);
            startDate.setHours(0, 0, 0, 0);
            body.startTime = startDate.getTime();
        }

        if (body.endTimeCustom) {
            endDate = new Date(body.endTimeCustom);
            endDate.setHours(23, 59, 59, 999);
            body.endTime = endDate.getTime();
        }

        if (startDate && endDate) {
            const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            if (diffDays > 90) {
                this.alertService.error('Có lỗi xảy ra', 'Khoảng thời gian lọc không được vượt quá 3 tháng (90 ngày)');
                return;
            }
        }

        this.state?.refetch();
    }

    convertBody() {
        const body = this.formGroup.getRawValue();

        let startDate: Date | null = null;
        let endDate: Date | null = null;

        if (body.startTimeCustom) {
            startDate = new Date(body.startTimeCustom);
            startDate.setHours(0, 0, 0, 0);
            body.startTime = startDate.getTime();
        }

        if (body.endTimeCustom) {
            endDate = new Date(body.endTimeCustom);
            endDate.setHours(23, 59, 59, 999);
            body.endTime = endDate.getTime();
        }

        return body;
    }

    processData(response: any[]) {
        this.tableData = [];
        this.dateColumns = [];

        response.forEach((po) => {
            po.indexLevels.forEach((level: any) => {
                const idxLevel = level.idxLevel;
                level.accountingCodeList.forEach((acc: any) => {
                    const accountingCode = acc.accountingCode;
                    const orderPrice = acc.orderPrice;
                    const orderQuantity = acc.orderQuantity;
                    const availableQuantity = acc.availableQuantity;
                    const spq = acc.spq;
                    const moq = acc.moq;
                    const deliveredQuantity = acc.deliveredQuantity;
                    const deliveredAmount = acc.deliveredAmount;
                    const remainingQuantity = acc.remainingQuantity;
                    const remainingAmount = acc.remainingAmount;
                    const unitPrice = acc.unitPrice;

                    acc.poVnptManList.forEach((man: any) => {
                        let allDates: number[] = [];
                        if (man.poDetailDayDTOS && man.poDetailDayDTOS.length > 0) {
                            allDates = man.poDetailDayDTOS.map((detail: any) => detail.date);
                            allDates.sort((a, b) => b - a);
                        }

                        const row = {
                            poId: po.poId,
                            orderNo: po.orderNo,
                            supplierId: po.supplierId,
                            supplierShortName: po.supplierShortName,
                            orderDate: po.orderDate,
                            idxLevel: idxLevel,
                            accountingCode: accountingCode,
                            vnptMan: man.vnptMan,
                            manPn: man.manPn || '',
                            manufacturer: man.manufacturer || null,
                            description: man.description,
                            spq: spq,
                            moq: moq,
                            unitPrice: unitPrice,
                            orderQuantity: orderQuantity,
                            availableQuantity: availableQuantity,
                            orderPrice: orderPrice,
                            deliveredQuantity: deliveredQuantity,
                            deliveredAmount: deliveredAmount,
                            remainingQuantity: remainingQuantity,
                            remainingAmount: remainingAmount,
                            orderAmount: orderPrice * orderQuantity,
                            rowspanPoInfo: 1,
                            rowspanLevel3: 1,
                            showPoInfo: false,
                            showLevel3: false,
                            poDetailDayDTOS: man.poDetailDayDTOS || [],
                        };
                        this.tableData.push(row);
                    });
                });
            });
        });

        this.calculateRowspan();
        this.dateColumns = [...new Set(this.tableData.flatMap((row) => row.poDetailDayDTOS.map((d) => d.date)))].sort((a, b) => b - a);
        this.cdr.detectChanges();
    }

    calculateRowspan() {
        let poInfoKey = '';
        let poInfoCount = 0;
        let level3Key = '';
        let level3Count = 0;
        let poIndex = 0;

        for (let i = 0; i < this.tableData.length; i++) {
            const row = this.tableData[i];
            const currentPoInfoKey = `${row.poId}|${row.orderNo}|${row.supplierId}|${row.orderDate}`;
            const currentLevel3Key = `${currentPoInfoKey}|${row.accountingCode}|${row.orderPrice}|${row.orderQuantity}|${row.availableQuantity}`;

            if (i === 0 || currentPoInfoKey !== poInfoKey) {
                if (i > 0) {
                    this.tableData[i - poInfoCount].rowspanPoInfo = poInfoCount;
                    this.tableData[i - poInfoCount].showPoInfo = true;
                }
                poInfoKey = currentPoInfoKey;
                poInfoCount = 1;
                poIndex++;
                this.tableData[i].poIndex = poIndex;
            } else {
                poInfoCount++;
                this.tableData[i].poIndex = poIndex;
            }

            if (i === 0 || currentLevel3Key !== level3Key) {
                if (i > 0) {
                    this.tableData[i - level3Count].rowspanLevel3 = level3Count;
                    this.tableData[i - level3Count].showLevel3 = true;
                }
                level3Key = currentLevel3Key;
                level3Count = 1;
            } else {
                level3Count++;
            }
        }

        if (this.tableData.length > 0) {
            this.tableData[this.tableData.length - poInfoCount].rowspanPoInfo = poInfoCount;
            this.tableData[this.tableData.length - poInfoCount].showPoInfo = true;
            this.tableData[this.tableData.length - level3Count].rowspanLevel3 = level3Count;
            this.tableData[this.tableData.length - level3Count].showLevel3 = true;
        }
    }

    getDailyQuantity(row: any, date: number): string {
        const detail = row.poDetailDayDTOS.find((d) => d.date === date);
        return detail ? detail?.quantity : '';
    }

    handleExportReport(event: EventPopupSubmit<unknown>) {
        this.loadingService.show();

        const exportData = this.formGroup.getRawValue();
        let startDate: Date | null = null;
        let endDate: Date | null = null;
        if (exportData.startTimeCustom) {
            startDate = new Date(exportData.startTimeCustom);
            startDate.setHours(0, 0, 0, 0); // Đặt giờ về 00:00:00
            exportData.startTime = startDate.getTime();
        }

        if (exportData.endTimeCustom) {
            endDate = new Date(exportData.endTimeCustom);
            endDate.setHours(23, 59, 59, 999); // Đặt giờ về 23:59:59
            exportData.endTime = endDate.getTime();
        }

        this.poService.exportReportPo(exportData).subscribe({
            next: (res) => {
                event.close();
                this.fileService.downLoadFileByService(res.url, '/sc/api');
                this.loadingService.hide();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    setColumnSelection(selectedColumns: Column[]) {
        if (selectedColumns.length === 0) {
            this.columns.forEach((c) => {
                if (!c.default) {
                    c.hide = true;
                }
            });
        } else {
            this.columns.forEach((c) => {
                if (!c.default) {
                    c.hide = !selectedColumns.some((s) => c.field === s.field);
                }
            });
        }
        this.columnChoose = selectedColumns;
        this.cdr.detectChanges();
    }
}
