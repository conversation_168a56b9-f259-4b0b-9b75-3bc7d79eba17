import { BaseEntity } from '../../BaseEntity';

export interface ManBom extends BaseEntity {
    reviewerIds: number[];
    versionId: number;
    phase: number;
    listManBom: ManBomDetail[];
}

export interface ManBomDetail extends BaseEntity {
    id: number;
    manBomId: number;
    instructionId: number;
    pfmeaId: number;
    workStandardId: number;
    description: string;
    unit: string;
    section: number;
    quantity: string;
    reference: string;
    materialType: number;
    consumptionRate: string;
    note: string;
    actionPayload: number;
}
