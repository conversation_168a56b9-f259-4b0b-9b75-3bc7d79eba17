<app-sub-header [items]="itemsHeader" [action]="actionHeader"></app-sub-header>

<ng-template #actionHeader>
    <p-button
        *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_supplier_kpi_edit']"
        label="Lưu"
        size="small"
        severity="primary"
        (onClick)="saveSupplierKpi()"
    />
</ng-template>
<div>
    <div class="tw-p-5">
        <form [formGroup]="deliveryProgressForm" class="tw-bg-white tw-p-5 tw-rounded-xl tw-mb-5">
            <div class="tw-mb-5 tw-font-bold">{{ deliveryProgressForm.get('name').value }}</div>
            <div class="tw-grid lg:tw-grid-cols-3 md:tw-grid-cols-1 sm:tw-grid-cols-1 tw-gap-4 tw-mb-3">
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <div class=""><PERSON><PERSON> ngày chậm <span class="tw-text-red-600">(*)</span></div>
                    <div>
                        <input formControlName="target" style="width: 100%" type="text" pInputText />
                    </div>
                    <app-input-validate
                        [control]="deliveryProgressForm.get('target')"
                        fieldName="số ngày chậm"
                        [validateMessage]="validateMessageTargetDeliveryProgress"
                    />
                </div>
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <div class="">Số điểm trừ <span class="tw-text-red-600">(*)</span></div>
                    <div>
                        <input formControlName="result" style="width: 100%" type="text" pInputText />
                    </div>
                    <app-input-validate
                        [control]="deliveryProgressForm.get('result')"
                        fieldName="số điểm trừ"
                        [validateMessage]="validateMessageResultDeliveryProgress"
                    />
                </div>
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <div class="">Điểm xuất phát <span class="tw-text-red-600">(*)</span></div>
                    <div>
                        <input formControlName="startPoint" style="width: 100%" type="text" pInputText />
                    </div>
                    <app-input-validate
                        [control]="deliveryProgressForm.get('startPoint')"
                        fieldName="điểm xuất phát"
                        [validateMessage]="validateMessageStartPoint"
                    />
                </div>
                <div class="tw-italic text-blue-500">
                    * Giải thích: Cứ mỗi <span>{{ deliveryProgressForm.get('target').value }}</span> ngày chậm thì trừ
                    <span>{{ deliveryProgressForm.get('result').value }}</span> điểm
                </div>
            </div>
        </form>
        <form [formGroup]="priceChangeForm" class="tw-bg-white tw-p-5 tw-rounded-xl tw-mb-5">
            <div class="tw-mb-5 tw-font-bold">{{ priceChangeForm.get('name').value }}</div>
            <div class="tw-grid lg:tw-grid-cols-3 md:tw-grid-cols-1 sm:tw-grid-cols-1 tw-gap-4 tw-mb-3">
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <div class="">Biến động giá <span class="tw-text-red-600">(*)</span></div>
                    <div>
                        <input formControlName="target" style="width: 100%" type="text" pInputText />
                    </div>
                    <app-input-validate
                        [control]="priceChangeForm.get('target')"
                        fieldName="biến động giá"
                        [validateMessage]="validateMessageTargetPriceChange"
                    />
                </div>
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <div class="">Số điểm trừ <span class="tw-text-red-600">(*)</span></div>
                    <div>
                        <input formControlName="result" style="width: 100%" type="text" pInputText />
                    </div>
                    <app-input-validate
                        [control]="priceChangeForm.get('result')"
                        fieldName="số điểm trừ"
                        [validateMessage]="validateMessageResultPriceChange"
                    />
                </div>
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <div class="">Điểm xuất phát <span class="tw-text-red-600">(*)</span></div>
                    <div>
                        <input formControlName="startPoint" style="width: 100%" type="text" pInputText />
                    </div>
                    <app-input-validate
                        [control]="priceChangeForm.get('startPoint')"
                        fieldName="điểm xuất phát"
                        [validateMessage]="validateMessageStartPoint"
                    />
                </div>
                <div class="tw-italic text-blue-500">
                    * Giải thích: Cứ mỗi <span>{{ priceChangeForm.get('target').value }}</span
                    >% thì trừ <span>{{ priceChangeForm.get('result').value }}</span> điểm
                </div>
            </div>
        </form>
        <div [formGroup]="evaluationTimeForm" class="tw-bg-white tw-p-5 tw-rounded-xl tw-mb-5">
            <div class="tw-mb-5 tw-font-bold">{{ evaluationTimeForm.get('name').value }}</div>
            <div class="tw-grid lg:tw-grid-cols-3 md:tw-grid-cols-1 sm:tw-grid-cols-1 tw-gap-4 tw-mb-3">
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <div>
                        <p-dropdown
                            [style]="{ width: '100%' }"
                            id="timeType"
                            [options]="timeTypesOption"
                            formControlName="timeType"
                            placeholder="Chọn cấu hình thời gian đánh giá"
                        >
                        </p-dropdown>
                    </div>
                    <app-input-validate
                        [control]="evaluationTimeForm.get('timeType')"
                        fieldName="cấu hình thời gian đánh giá"
                    />
                </div>
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <div *ngIf="evaluationTimeForm.get('timeType').value === 0">
                        <p-dropdown
                            [style]="{ width: '100%' }"
                            id="year"
                            [options]="yearsOption"
                            formControlName="year"
                            placeholder="Chọn năm"
                        >
                        </p-dropdown>
                        <app-input-validate [control]="evaluationTimeForm.get('year')" fieldName="năm" />
                    </div>
                    <div
                        class="tw-grid lg:tw-grid-cols-2 md:tw-grid-cols-1 sm:tw-grid-cols-1 tw-gap-4"
                        *ngIf="evaluationTimeForm.get('timeType').value === 1"
                    >
                        <div>
                            <div class="tw-mb-3">
                                <p-calendar
                                    formControlName="startTime"
                                    view="month"
                                    dateFormat="mm/yy"
                                    placeholder="Từ"
                                ></p-calendar>
                            </div>
                            <div>
                                <app-input-validate
                                    [formControlGroup]="evaluationTimeForm"
                                    [control]="evaluationTimeForm.get('startTime')"
                                    fieldName="thời gian bắt đầu"
                                    [validateMessage]="validateMessageTimeEvaluation"
                                />
                            </div>
                        </div>
                        <div>
                            <div class="tw-mb-3">
                                <p-calendar
                                    formControlName="endTime"
                                    view="month"
                                    dateFormat="mm/yy"
                                    placeholder="Đến"
                                ></p-calendar>
                            </div>
                            <div>
                                <app-input-validate
                                    [control]="evaluationTimeForm.get('endTime')"
                                    fieldName="thời gian kết thúc"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <form [formGroup]="weightForm" class="tw-bg-white tw-p-5 tw-rounded-xl tw-mb-5">
            <div class="tw-mb-5 tw-font-bold">{{ weightForm.get('name').value }}</div>
            <div class="tw-grid lg:tw-grid-cols-3 md:tw-grid-cols-1 sm:tw-grid-cols-1 tw-gap-4 tw-mb-3">
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <div class="">Chất lượng <span class="tw-text-red-600">(*)</span></div>
                    <div>
                        <input formControlName="quantity" style="width: 100%" type="text" pInputText />
                    </div>
                    <app-input-validate
                        [control]="weightForm.get('quantity')"
                        fieldName="chất lượng"
                        [validateMessage]="validateMessageQuantityWeight"
                    />
                </div>
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <div class="">Biến động giá <span class="tw-text-red-600">(*)</span></div>
                    <div>
                        <input formControlName="priceChange" style="width: 100%" type="text" pInputText />
                    </div>
                    <app-input-validate
                        [control]="weightForm.get('priceChange')"
                        fieldName="biến động giá"
                        [validateMessage]="validateMessagePriceChangeWeight"
                    />
                </div>
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <div class="">Tiến độ giao hàng <span class="tw-text-red-600">(*)</span></div>
                    <div>
                        <input formControlName="deliveryQuantity" style="width: 100%" type="text" pInputText />
                    </div>
                    <app-input-validate
                        [control]="weightForm.get('deliveryQuantity')"
                        fieldName="tiến độ giao hàng"
                        [validateMessage]="validateMessageDeliveryProgressWeight"
                    />
                </div>
            </div>
        </form>
    </div>
</div>
