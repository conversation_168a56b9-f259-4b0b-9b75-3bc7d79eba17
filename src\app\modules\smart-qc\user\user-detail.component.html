<form [formGroup]="userForm" #ngForm="ngForm" (ngSubmit)="save()" autocomplete="off">
    <app-sub-header
        [items]="
            editMode && user.id
                ? [
                      { label: 'Quản lý người dùng', url: '/sqc/user/' },
                      {
                          label: user.fullName,
                          url: '/sqc/user/' + user.id,
                      },
                      { label: 'Sửa' },
                  ]
                : [
                      { label: 'Quản lý người dùng', url: '/sqc/user/' },
                      { label: user.fullName ? user.fullName : 'Tạo mới' },
                  ]
        "
        [action]="actionHeader"
    >
        <ng-template #actionHeader>
            <p-button *ngIf="!editMode && userId" size="small" label="Sửa" routerLink="edit" severity="success" />
            <p-button *ngIf="editMode || !userId" size="small" label="Lưu" type="submit" severity="success" />
            <p-button *ngIf="!editMode" size="small" routerLink="/sqc/user" severity="secondary" label="Hủy" />
            <p-button
                *ngIf="editMode"
                size="small"
                [routerLink]="'/sqc/user/' + userId"
                severity="secondary"
                label="Hủy"
            />
        </ng-template>
    </app-sub-header>
    <div style="padding: 1rem 1rem 0 1rem">
        <div class="tw-p-4 tw-gap-4 tw-mx-auto tw-bg-white tw-rounded-md" style="max-width: 850px">
            <div class="tw-flex tw-justify-between tw-gap-4 tw-w-full tw-mb-5">
                <p-avatar [label]="user.email ? user.email[0] : ''" styleClass="mr-2" size="xlarge" shape="circle" />
                <div *ngIf="!editMode && userId">
                    <div
                        style="
                            display: flex;
                            background-color: #f5f5f5 !important;
                            border-right: 1px solid #e0e0e0;
                            padding: 2px 8px !important;
                            border-radius: 8px;
                        "
                    >
                        <p-inputSwitch
                            formControlName="active"
                            [ngClass]="iconClass"
                            (click)="setActiveStatus()"
                            [readonly]="true"
                        />
                        <span [ngStyle]="activeClass" style="margin-left: 4px; margin-top: 4px">{{ title }}</span>
                    </div>
                </div>
            </div>
            <div *ngIf="!editMode && userId" class="tw-grid md:tw-grid-cols-6 tw-grid-cols-2 tw-gap-4 tw-w-full">
                <b>Họ và tên:</b>
                <span class="md:tw-col-span-2 tw-col-span-1">{{ user?.fullName }}</span>
                <b>Tài khoản:</b>
                <span style="text-wrap: wrap" class="md:tw-col-span-2 tw-col-span-1">{{ user?.email }}</span>
                <b>Số điện thoại:</b>
                <span class="md:tw-col-span-2 tw-col-span-1">{{ user?.phone }}</span>
                <b>Ghi chú:</b>
                <span style="text-wrap: wrap" class="md:tw-col-span-2 tw-col-span-1">{{ user?.note }}</span>
                <b>Vai trò:</b>
                <div class="md:tw-col-span-2 tw-col-span-1 tw-gap-y-1 tw-flex tw-flex-col">
                    <p-tag
                        *ngFor="let item of user.normalRoles"
                        ngClass="tw-block  p-tag-cutom"
                        [value]="item.displayName"
                    ></p-tag>
                </div>
                <b *ngIf="user.qcContractIds && user.qcContractIds.length > 0">Dự án:</b>
                <div class="md:tw-col-span-2 tw-col-span-1 tw-gap-y-1 tw-flex tw-flex-col">
                    <p-tag
                        *ngFor="let item of user?.qcContractIds"
                        ngClass="tw-block  p-tag-cutom"
                        [value]="getContractName(item)"
                    ></p-tag>
                </div>
            </div>

            <div *ngIf="(editMode || !userId) && unEditedRoles?.length > 0" class="tw-gap-4 tw-w-full">
                <div>
                    <div class="tw-mb-3"><b>Vai trò đã tạo sẵn</b></div>
                    <div class="flex flex-wrap">
                        <p-tag *ngFor="let item of unEditedRoles" class="p-tag-cutom" [value]="item.name"></p-tag>
                    </div>
                </div>
                <hr />
            </div>

            <div *ngIf="editMode || !userId" class="tw-grid md:tw-grid-cols-2 tw-grid-cols-1 tw-gap-4 tw-w-full">
                <div>
                    <b class="block">Họ và tên<span class="text-red-400">*</span></b>
                    <input class="tw-w-full" type="text" pInputText formControlName="fullName" autocomplete />

                    <ng-component
                        *ngIf="
                            userForm.get('fullName').errors && (userForm.get('fullName').touched || ngForm.submitted)
                        "
                        class="text-red-600 tw-pt-1"
                    >
                        <span *ngIf="userForm.get('fullName').errors['required']">Trường này yêu cầu dữ liêu</span>
                    </ng-component>
                </div>
                <div>
                    <b class="block">Tài khoản/Email<span class="tw-text-red-400">*</span></b>
                    <input
                        class="tw-w-full"
                        type="text"
                        pInputText
                        formControlName="email"
                        (change)="onSetAccount($event)"
                    />
                    <ng-component
                        *ngIf="userForm.get('email').errors && (userForm.get('email').touched || ngForm.submitted)"
                        class="text-red-600 tw-pt-1"
                    >
                        <span *ngIf="userForm.get('email').errors['required']">Trường này yêu cầu dữ liêu</span>
                    </ng-component>
                </div>
                <div>
                    <b class="block">Số điện thoại:</b>
                    <input class="tw-w-full" type="number" pInputText formControlName="phone" />
                </div>
                <div>
                    <b *ngIf="!user.id" class="block">Mật khẩu</b>
                    <b *ngIf="user.id" class="block">Mật khẩu mới</b>
                    <div>
                        <p-password
                            class="tw-w-full"
                            formControlName="password"
                            autocomplete="new-password"
                            [toggleMask]="true"
                            [feedback]="false"
                            inputStyleClass="w-full"
                        />
                        <ng-component
                            *ngIf="
                                userForm.get('password').errors &&
                                (userForm.get('password').touched || ngForm.submitted)
                            "
                            class="text-red-600 tw-pt-1"
                        >
                            <span *ngIf="userForm.get('password').errors['required']"
                                >Tài khoản không có email phải đặt mật khẩu mặc định</span
                            >
                            <span *ngIf="userForm.get('password').errors['weakPassword']">
                                Mật khẩu ít nhất 8 ký tự gồm chữ thường, chữ in hoa, số và ký tự đặc biệt</span
                            >
                        </ng-component>
                    </div>
                </div>
                <div>
                    <b class="block">Vai trò<span class="text-red-400">*</span> </b>
                    <p-dropdown
                        class="tw-w-full"
                        [options]="normalRoles"
                        optionLabel="displayName"
                        dataKey="id"
                        display="chip"
                        formControlName="normalRoles"
                        (ngModelChange)="onSelectRole($event)"
                    ></p-dropdown>
                    <ng-component
                        *ngIf="
                            userForm.get('normalRoles').errors &&
                            (userForm.get('normalRoles').touched || ngForm.submitted)
                        "
                        class="text-red-600 tw-pt-1"
                    >
                        <span *ngIf="userForm.get('normalRoles').errors['required']">Trường này yêu cầu dữ liêu</span>
                    </ng-component>
                </div>
                <div>
                    <b class="block">Ghi chú:</b>
                    <input class="tw-w-full" type="text" pInputText formControlName="note" />
                </div>
                <div *ngIf="isRequiredContract">
                    <b class="block">Dự án<span class="text-red-400">*</span></b>
                    <p-multiSelect
                        class="tw-w-full"
                        [options]="contractOptions"
                        optionLabel="name"
                        optionValue="id"
                        dataKey="id"
                        display="chip"
                        formControlName="qcContractIds"
                    ></p-multiSelect>
                    <ng-component
                        *ngIf="
                            userForm.get('qcContractIds').errors &&
                            (userForm.get('qcContractIds').touched || ngForm.submitted)
                        "
                        class="text-red-600 tw-pt-1"
                    >
                        <span *ngIf="userForm.get('qcContractIds').errors['required']">Trường này yêu cầu dữ liêu</span>
                    </ng-component>
                </div>
            </div>
        </div>
    </div>
</form>
