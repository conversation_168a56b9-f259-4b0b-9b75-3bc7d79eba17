{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"smartqc-web": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/new-webapp", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*.json", "input": "src/assets", "output": "/assets/"}, "src/firebase-messaging-sw.js"], "styles": ["node_modules/primeicons/primeicons.css", "node_modules/primeng/resources/themes/lara-light-blue/theme.css", "node_modules/primeng/resources/primeng.min.css", "node_modules/primeflex/primeflex.css", "src/styles.scss"], "allowedCommonJsDependencies": ["lodash"], "scripts": []}, "configurations": {"prod": {"budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "assets": ["src/favicon.ico", "src/assets", {"glob": "firebase-messaging-sw.js", "input": "src/service-worker/pro", "output": "/"}], "outputHashing": "all"}, "staging": {"budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.staging.ts"}], "assets": ["src/favicon.ico", "src/assets", {"glob": "firebase-messaging-sw.js", "input": "src/service-worker/stag", "output": "/"}], "outputHashing": "all"}, "153": {"budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.153.ts"}], "assets": ["src/favicon.ico", "src/assets", {"glob": "firebase-messaging-sw.js", "input": "src/service-worker/stag", "output": "/"}], "outputHashing": "all"}, "151": {"budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.151.ts"}], "assets": ["src/favicon.ico", "src/assets", {"glob": "firebase-messaging-sw.js", "input": "src/service-worker/dev", "output": "/"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "assets": ["src/favicon.ico", "src/assets", {"glob": "firebase-messaging-sw.js", "input": "src/service-worker/dev", "output": "/"}]}, "hmr": {"optimization": false, "sourceMap": true, "namedChunks": true, "extractLicenses": false, "vendorChunk": true, "assets": ["src/favicon.ico", "src/assets", {"glob": "firebase-messaging-sw.js", "input": "src/service-worker/dev", "output": "/"}]}, "vivas": {"budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.vivas.ts"}], "assets": ["src/favicon.ico", "src/assets", {"glob": "firebase-messaging-sw.js", "input": "src/service-worker/dev", "output": "/"}], "outputHashing": "all", "serviceWorker": false, "optimization": true, "sourceMap": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true}}, "defaultConfiguration": "staging"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "smartqc-web:build:production"}, "development": {"browserTarget": "smartqc-web:build:development"}, "hmr": {"browserTarget": "smartqc-web:build:hmr"}, "staging": {"browserTarget": "smartqc-web:build:staging"}, "151": {"browserTarget": "smartqc-web:build:151"}, "153": {"browserTarget": "smartqc-web:build:153"}, "vivas": {"browserTarget": "smartqc-web:build:vivas"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "smartqc-web:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*.json", "input": "src/assets", "output": "/assets/"}], "styles": ["node_modules/primeicons/primeicons.css", "node_modules/primeng/resources/themes/saga-blue/theme.css", "node_modules/primeng/resources/themes/lara-light-blue/theme.css", "node_modules/primeng/resources/primeng.min.css", "node_modules/primeflex/primeflex.css", "src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": false}}