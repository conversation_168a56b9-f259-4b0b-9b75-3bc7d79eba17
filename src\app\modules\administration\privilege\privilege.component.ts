import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { RouterLink } from '@angular/router';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { ButtonModule } from 'primeng/button';
import { TagModule } from 'primeng/tag';
import { TABLE_KEY } from 'src/app/models/constant';
import { Column, Privilege } from 'src/app/models/interface';
import { PrivilegeService } from 'src/app/services/administration/admin/privilege.service';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { TableCommonService } from 'src/app/shared/table-module/table.common.service';

@Component({
    selector: 'app-privilege',
    standalone: true,
    templateUrl: './privilege.component.html',
    imports: [
        TableCommonModule,
        CommonModule,
        RouterLink,
        SubHeaderComponent,
        HasAnyAuthorityDirective,
        TagModule,
        ButtonModule,
    ],
    providers: [PrivilegeService],
})
export class PrivilegeComponent implements OnInit, AfterViewInit {
    tableId: string = TABLE_KEY.PRIVILEGE;
    @ViewChild('templateType') templateType: TemplateRef<Element>;
    state: QueryObserverBaseResult<Privilege[]>;
    columns: Column[] = [];
    itemsHeader = [{ label: 'Quản trị hệ thống' }, { label: 'Quyền' }];
    constructor(
        private privilegeService: PrivilegeService,
        private tableCommonService: TableCommonService,
    ) {}
    ngAfterViewInit(): void {
        setTimeout(() => {
            this.columns = [
                { field: 'displayName', header: 'Tên', default: true },
                { field: 'description', header: 'Mô tả' },
                { field: 'created', header: 'Ngày tạo', type: 'date', format: 'dd/MM/yyyy' },
                { field: 'createdBy', header: 'Người tạo' },
            ];
        });
    }

    ngOnInit(): void {
        this.tableCommonService
            .init<Privilege>({
                tableId: this.tableId,
                queryFn: (filter) => this.privilegeService.getPageTableCustom(filter),
                configFilterRSQL: {
                    name: 'Text',
                    description: 'Text',
                },
            })
            .subscribe((state) => {
                this.state = state;
            });
    }
}
