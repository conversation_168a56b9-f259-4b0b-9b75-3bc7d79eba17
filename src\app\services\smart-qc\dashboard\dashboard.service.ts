import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

@Injectable({
    providedIn: 'root',
})
export class DashboardService {
    constructor(protected http: HttpClient) {}

    getTaskProcess(filter: unknown) {
        return this.http.post<unknown>(`/smart-qc/api/dashboard/task-progress`, filter);
    }

    getTaskProcessChart(filter: unknown) {
        return this.http.post<unknown>(`/smart-qc/api/dashboard/task-progress-chart`, filter);
    }
}
