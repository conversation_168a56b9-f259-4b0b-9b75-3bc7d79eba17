<app-sub-header [items]="[{ label: 'Quản lý thông tin mua hàng' }, { label: 'Quản lý PO' }]" [action]="actionHeader"></app-sub-header>

<ng-template #actionHeader>
    <p-button routerLink="create" label="Tạo mới" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_po_edit']" />

    <app-popup severity="success" header="Xuất danh sách PO" label="Xuất danh sách" (onSubmit)="exportExcel($event)" typePopup="download"></app-popup>
</ng-template>
<div style="padding: 1rem 1rem 0 1rem">
    <app-table-common
        [tableId]="tableId"
        [columns]="columns"
        [data]="state.data"
        [loading]="state.isFetching"
        [filterTemplate]="filterTemplate"
        name="Danh sách PO"
        [funcDelete]="delete"
        [authoritiesDelete]="['ROLE_SYSTEM_ADMIN', 'sc_po_delete']"
        [summaryTemplate]="summaryTemplate"
    >
        <ng-template #summaryTemplate>
            <div class="tw-text-blue-600 tw-m-r-4">
                <p style="margin-bottom: 0.5rem">USD: {{ totalAmount.totalValueUds | number }}</p>
                <p>VNĐ: {{ totalAmount.totalValueVnd | number }}</p>
            </div>
        </ng-template>

        <ng-template #filterTemplate>
            <tr>
                <th></th>
                <th [appFilter]="[tableId, 'orderNo']">
                    <app-filter-table [tableId]="tableId" field="orderNo" placeholder="số đơn hàng"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'orderDate']" style="max-width: 8rem">
                    <app-filter-table [tableId]="tableId" [rsql]="true" field="orderDate" type="date-range" placeholder="ngày gửi"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'supplierName']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="supplierId"
                        type="select"
                        [rsql]="true"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            rsql: true,
                            param: 'name',
                            paramForm: 'id',
                            url: '/sc/api/supplier/search',
                        }"
                        placeholder="tên ncc"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'supplierNo']">
                    <app-filter-table [tableId]="tableId" field="supplierNo" placeholder="số NCC"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'state']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="state"
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'label',
                            options: optionPoState,
                            filterLocal: true,
                        }"
                        placeholder="trạng thái"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'accountingCode']">
                    <app-filter-table [tableId]="tableId" field="accountingCode" placeholder="Mã kế toán"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'unitPrice']">
                    <app-filter-table [tableId]="tableId" field="unitPrice" placeholder="Giá trị đơn hàng"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'totalValue']">
                    <app-filter-table [tableId]="tableId" type="number" field="totalValue" placeholder="Tổng giá trị"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'invTransferNumberImport']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="invTransferNumberImport"
                        type="select-one"
                        [rsql]="true"
                        [configSelect]="{
                            fieldValue: 'invTransferNumber',
                            fieldLabel: 'invTransferNumber',
                            rsql: true,
                            param: 'invTransferNumber',
                            url: '/sc/api/po-transfer/search',
                            body: {
                                type: 0,
                            },
                        }"
                        placeholder="phiếu chuyển giao"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'invTransferNumberExport']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="invTransferNumberExport"
                        type="select-one"
                        [rsql]="true"
                        [configSelect]="{
                            fieldValue: 'invTransferNumber',
                            fieldLabel: 'invTransferNumber',
                            rsql: true,
                            param: 'invTransferNumber',
                            url: '/sc/api/po-transfer/search',
                            body: {
                                type: 1,
                            },
                        }"
                        placeholder="phiếu xuất kho"
                    ></app-filter-table>
                </th>
            </tr>
        </ng-template>
        <ng-template #templateState let-rowData>
            <p-tag [value]="mapState[rowData.state]" [severity]="getSeverity(rowData.state)"></p-tag>
        </ng-template>
        <ng-template #templateTotalValue let-rowData>
            <span>{{ showTotalValue(rowData.unitPrice, rowData.totalValue) }}</span>
        </ng-template>
    </app-table-common>
</div>
