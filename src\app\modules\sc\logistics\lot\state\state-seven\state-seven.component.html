<app-form *ngIf="formPaymentLot" [formGroup]="formPaymentLot" layout="vertical">
    <p-fieldset legend="Thông tin lô hàng">
        <div class="tw-grid lg:tw-grid-cols-4 md:tw-grid-cols-2 tw-gap-4">
            <div>
                <b><PERSON><PERSON><PERSON> hoàn thành</b>
                <p-calendar
                    [showButtonBar]="true"
                    placeholder="dd/MM/yyyy"
                    [showIcon]="true"
                    dateFormat="dd/mm/yy"
                    formControlName="completeDateCustom"
                    appendTo="body"
                    (onClose)="savePaymentLot()"
                ></p-calendar>
            </div>
            <div>
                <b>Khối lượng tổng trên vận đơn (KG)</b>
                <p>{{ paymentLot?.totalWeightLot | number }}</p>
            </div>
            <div>
                <b>Điều kiện giao hàng</b>
                <p>{{ paymentLot?.deliveryCondition }}</p>
            </div>
            <div>
                <b><PERSON><PERSON> hợ<PERSON> đồng PO/DA</b>
                <p>{{ paymentLot?.da }}</p>
            </div>
            <div>
                <b>{{ lot?.type === 0 ? 'Tên người giao hàng' : 'Tên người nhận hàng' }}</b>
                <p>{{ paymentLot?.receiver }}</p>
            </div>
            <div>
                <b>Khối lượng tính phí</b>
                <p>{{ paymentLot?.weightFee | number }}</p>
            </div>
            <div>
                <b>Địa điểm giao hàng</b>
                <p>{{ paymentLot?.deliveryAddress }}</p>
            </div>
            <div>
                <b>Mã số kế toán</b>
                <p>{{ paymentLot?.accountingCode }}</p>
            </div>
            <div>
                <b>Số vận đơn</b>
                <p>{{ paymentLot?.lotCode }}</p>
            </div>
            <div>
                <b>ĐVT khối lượng tính phí</b>
                <p>{{ paymentLot?.weightFeeUnit }}</p>
            </div>
            <div>
                <b>Cảng xuất</b>
                <p>{{ paymentLot?.exportPort }}</p>
            </div>
            <div>
                <b>Tổng giá trị lô hàng (USD)</b>
                <p>{{ paymentLot?.totalAmount | number }}</p>
            </div>
            <div>
                <b>Số tờ khai</b>
                <p>{{ paymentLot?.declarationCodeImport ? paymentLot?.declarationCodeImport : paymentLot?.declarationCode }}</p>
            </div>
            <div>
                <b>Phương thức vận chuyển</b>
                <p>{{ paymentLot?.shippingMethod }}</p>
            </div>
            <div>
                <b>Cảng nhập</b>
                <p>{{ paymentLot?.importPort }}</p>
            </div>
            <div>
                <b>Tỷ giá (USD/VNĐ)</b>
                <p>{{ paymentLot?.rateMoney | number }}</p>
            </div>
            <div>
                <b>Khối lượng tổng trên Packing list (KG)</b>
                <p>{{ paymentLot?.totalWeightPck | number }}</p>
            </div>
            <div>
                <b>Hãng vận chuyển</b>
                <p>{{ paymentLot?.shippingBrand }}</p>
            </div>
            <div>
                <b>Địa điểm nhận hàng cuối cùng</b>
                <p>{{ paymentLot?.deliveryAddressFinal }}</p>
            </div>
            <!-- <div>
            <b>Số hợp đồng/DA/PO</b>
            <p>{{ paymentLot?.da }}</p>
        </div>
        <div>
            <b>Mã kế toán</b>
            <p>{{ paymentLot?.accountingCode }}</p>
        </div>
        <div>
            <b>Tổng giá trị lô hàng</b>
            <p>{{ paymentLot?.totalAmount }}</p>
        </div>
        <div>
            <b>Tỷ giá tiền (USD/VNĐ)</b>
            <p>{{ paymentLot?.rateMoney }}</p>
        </div>
        <div>
            <b>Tổng tiền phí vận chuyển</b>
            <p>{{ paymentLot?.totalShippingFee }}</p>
        </div>
        <div>
            <b>ID chi phí hàng tháng</b>
            <p>{{ paymentLot?.monthlyExpensesId }}</p>
        </div> -->
        </div>
    </p-fieldset>
</app-form>
<br />
<app-form *ngIf="formPaymentLotDeliverPortExport" [formGroup]="formPaymentLotDeliverPortExport" layout="vertical">
    <p-fieldset legend="Phí từ địa điểm giao hàng đến cảng xuất (VNĐ)">
        <div class="tw-grid lg:tw-grid-cols-3 md:tw-grid-cols-2 tw-gap-4">
            <div>
                <b>Giấy phép xuất khẩu</b>
                <p *ngIf="lot?.type === 0">{{ paymentLotDeliverPortExport?.exportLicenseFee | number }}</p>
                <app-inputNumber
                    *ngIf="lot?.type === 1"
                    class="tw-w-full"
                    formControlName="exportLicenseFee"
                    mode="decimal"
                    (onBlur)="savePaymentLotDeliverPort('export')"
                ></app-inputNumber>
            </div>
            <div>
                <b>Vận tải từ địa điểm giao hàng đến cảng xuất</b>
                <p *ngIf="lot?.type === 0">{{ paymentLotDeliverPortExport?.toExportFee | number }}</p>
                <app-inputNumber
                    *ngIf="lot?.type === 1"
                    class="tw-w-full"
                    formControlName="toExportFee"
                    mode="decimal"
                    (onBlur)="savePaymentLotDeliverPort('export')"
                ></app-inputNumber>
            </div>
            <div>
                <b>Phí tại cảng xuất</b>
                <p *ngIf="lot?.type === 0">{{ paymentLotDeliverPortExport?.exportFee | number }}</p>
                <app-inputNumber
                    *ngIf="lot?.type === 1"
                    class="tw-w-full"
                    formControlName="exportFee"
                    mode="decimal"
                    (onBlur)="savePaymentLotDeliverPort('export')"
                ></app-inputNumber>
            </div>
            <div>
                <b>C/0</b>
                <p *ngIf="lot?.type === 0">{{ paymentLotDeliverPortExport?.coFee | number }}</p>
                <app-inputNumber
                    *ngIf="lot?.type === 1"
                    class="tw-w-full"
                    formControlName="coFee"
                    mode="decimal"
                    (onBlur)="savePaymentLotDeliverPort('export')"
                ></app-inputNumber>
            </div>
            <div>
                <b>Dịch vụ hải quản</b>
                <p *ngIf="lot?.type === 0">{{ paymentLotDeliverPortExport?.customsFee | number }}</p>
                <app-inputNumber
                    *ngIf="lot?.type === 1"
                    class="tw-w-full"
                    formControlName="customsFee"
                    mode="decimal"
                    (onBlur)="savePaymentLotDeliverPort('export')"
                ></app-inputNumber>
            </div>
            <div>
                <b>Thuế GTGT</b>
                <p *ngIf="lot?.type === 0">{{ paymentLotDeliverPortExport?.vatTax | number }}</p>
                <app-inputNumber
                    *ngIf="lot?.type === 1"
                    class="tw-w-full"
                    formControlName="vatTax"
                    mode="decimal"
                    (onBlur)="savePaymentLotDeliverPort('export')"
                ></app-inputNumber>
            </div>
        </div>
    </p-fieldset>
</app-form>
<br />
<app-form *ngIf="formPaymentLotDeliverPortExport" [formGroup]="formPaymentLotDeliverPortExport" layout="vertical">
    <p-fieldset legend="Cước từ cảng xuất đến cảng nhập (VNĐ)">
        <div class="tw-grid lg:tw-grid-cols-3 md:tw-grid-cols-2 tw-gap-4">
            <div>
                <b>Cước hàng không/biển bộ qua biên giới</b>

                <p *ngIf="lot?.type === 0">{{ paymentLotDeliverPortExport?.toImportFee | number }}</p>
                <app-inputNumber
                    *ngIf="lot?.type === 1"
                    class="tw-w-full"
                    formControlName="toImportFee"
                    mode="decimal"
                    (onBlur)="savePaymentLotDeliverPort('export')"
                ></app-inputNumber>
            </div>
        </div>
    </p-fieldset>
    <br />
</app-form>
<p-fieldset legend="Phí cảng nhập đến địa điểm giao hàng cuối cùng (hóa đơn do bên B xuất trực tiếp) (VNĐ)">
    <div class="tw-grid lg:tw-grid-cols-3 md:tw-grid-cols-2 tw-gap-4">
        <app-form *ngIf="formPaymentLotDeliverPortImport" [formGroup]="formPaymentLotDeliverPortImport" layout="vertical">
            <div>
                <b>Giấy phép xuất khẩu</b>
                <p *ngIf="lot?.type === 0">{{ paymentLotDeliverPortImport?.exportLicenseFee | number }}</p>
                <app-inputNumber
                    *ngIf="lot?.type === 1"
                    class="tw-w-full"
                    formControlName="exportLicenseFee"
                    mode="decimal"
                    (onBlur)="savePaymentLotDeliverPort('import')"
                ></app-inputNumber>
            </div>
        </app-form>
        <app-form *ngIf="formPaymentLotDeliverPortImport" [formGroup]="formPaymentLotDeliverPortImport" layout="vertical">
            <div>
                <b>Phí tại cảng nhập</b>
                <p *ngIf="lot?.type === 0">{{ paymentLotDeliverPortImport?.exportFee | number }}</p>
                <app-inputNumber
                    *ngIf="lot?.type === 1"
                    class="tw-w-full"
                    formControlName="exportFee"
                    mode="decimal"
                    (onBlur)="savePaymentLotDeliverPort('import')"
                ></app-inputNumber>
            </div>
        </app-form>
        <app-form *ngIf="formPaymentLotImportPort" [formGroup]="formPaymentLotImportPort" layout="vertical">
            <div>
                <b>Thuế GTGT</b>
                <p *ngIf="lot?.type === 0">{{ paymentLotImportPort?.vatTax | number }}</p>
                <app-inputNumber
                    *ngIf="lot?.type === 1"
                    class="tw-w-full"
                    formControlName="vatTax"
                    mode="decimal"
                    (onBlur)="savePaymentLotImportPort()"
                ></app-inputNumber>
            </div>
        </app-form>
        <app-form *ngIf="formPaymentLotDeliverPortImport" [formGroup]="formPaymentLotDeliverPortImport" layout="vertical">
            <div>
                <b>Dịch vụ hải quan</b>
                <p *ngIf="lot?.type === 0">{{ paymentLotDeliverPortImport?.customsFee | number }}</p>
                <app-inputNumber
                    *ngIf="lot?.type === 1"
                    class="tw-w-full"
                    formControlName="customsFee"
                    mode="decimal"
                    (onBlur)="savePaymentLotDeliverPort('import')"
                ></app-inputNumber>
            </div>
        </app-form>
        <app-form *ngIf="formPaymentLotDeliverPortImport" [formGroup]="formPaymentLotDeliverPortImport" layout="vertical">
            <div>
                <b>Vận tải từ địa điểm giao hàng đến cảng xuất</b>
                <p *ngIf="lot?.type === 0">{{ paymentLotDeliverPortImport?.toExportFee | number }}</p>
                <app-inputNumber
                    *ngIf="lot?.type === 1"
                    class="tw-w-full"
                    formControlName="toExportFee"
                    mode="decimal"
                    (onBlur)="savePaymentLotDeliverPort('import')"
                ></app-inputNumber>
            </div>
        </app-form>
    </div>
</p-fieldset>
<br />
<p-fieldset legend="Phí chi hộ tại cảng nhập (hóa đơn do bên thứ 3 xuất) (VNĐ)">
    <div class="tw-grid lg:tw-grid-cols-3 md:tw-grid-cols-2 tw-gap-4">
        <app-form *ngIf="formPaymentLotImportPort" [formGroup]="formPaymentLotImportPort" layout="vertical">
            <div>
                <b>Hạ tầng</b>
                <p *ngIf="lot?.type === 0">{{ paymentLotImportPort?.infrastructureFee | number }}</p>
                <app-inputNumber
                    *ngIf="lot?.type === 1"
                    class="tw-w-full"
                    formControlName="infrastructureFee"
                    mode="decimal"
                    (onBlur)="savePaymentLotImportPort()"
                ></app-inputNumber>
            </div>
        </app-form>
        <app-form *ngIf="formPaymentLotImportPort" [formGroup]="formPaymentLotImportPort" layout="vertical">
            <div>
                <b>Nâng hạ sửa chữa container</b>
                <p *ngIf="lot?.type === 0">{{ paymentLotImportPort?.cleanFee | number }}</p>
                <app-inputNumber
                    *ngIf="lot?.type === 1"
                    class="tw-w-full"
                    formControlName="cleanFee"
                    mode="decimal"
                    (onBlur)="savePaymentLotImportPort()"
                ></app-inputNumber>
            </div>
        </app-form>
        <app-form *ngIf="formPaymentLotDeliverPortImport" [formGroup]="formPaymentLotDeliverPortImport" layout="vertical">
            <div>
                <b>Thuế GTGT</b>
                <p *ngIf="lot?.type === 0">{{ paymentLotDeliverPortImport?.vatTax | number }}</p>
                <app-inputNumber
                    *ngIf="lot?.type === 1"
                    class="tw-w-full"
                    formControlName="vatTax"
                    mode="decimal"
                    (onBlur)="savePaymentLotImportPort()"
                ></app-inputNumber>
            </div>
        </app-form>
        <app-form *ngIf="formPaymentLotImportPort" [formGroup]="formPaymentLotImportPort" layout="vertical">
            <div>
                <b>Lưu kho</b>
                <p *ngIf="lot?.type === 0">{{ paymentLotImportPort?.storageFee | number }}</p>
                <app-inputNumber
                    *ngIf="lot?.type === 1"
                    class="tw-w-full"
                    formControlName="storageFee"
                    mode="decimal"
                    (onBlur)="savePaymentLotImportPort()"
                ></app-inputNumber>
            </div>
        </app-form>
        <app-form *ngIf="formPaymentLotImportPort" [formGroup]="formPaymentLotImportPort" layout="vertical">
            <div>
                <b>Khác</b>
                <p *ngIf="lot?.type === 0">{{ paymentLotImportPort?.otherFee | number }}</p>
                <app-inputNumber
                    *ngIf="lot?.type === 1"
                    class="tw-w-full"
                    formControlName="otherFee"
                    mode="decimal"
                    (onBlur)="savePaymentLotImportPort()"
                ></app-inputNumber>
            </div>
        </app-form>
    </div>
</p-fieldset>
<br />
<p-fieldset legend="Thông tin thuế">
    <div class="tw-grid lg:tw-grid-cols-3 md:tw-grid-cols-2 tw-gap-4">
        <div>
            <b>Thuế GTGT</b>
            <p>{{ paymentLotTax?.vatTax | number }}</p>
        </div>
        <div>
            <b>Thuế nhập khẩu</b>
            <p>{{ paymentLotTax?.importTax | number }}</p>
        </div>
    </div>
</p-fieldset>

<br />
<app-form *ngIf="formPaymentLotDelivery" [formGroup]="formPaymentLotDelivery" layout="vertical">
    <p-fieldset legend="Chi phí chuyển phát nhanh(VNĐ)">
        <div class="tw-grid lg:tw-grid-cols-3 md:tw-grid-cols-2 tw-gap-4">
            <div>
                <b>Chi phí vận chuyển</b>
                <p *ngIf="lot?.type === 0">{{ paymentLotDeliver?.deliveryFee | number }}</p>
                <app-inputNumber
                    *ngIf="lot?.type === 1"
                    class="tw-w-full"
                    formControlName="deliveryFee"
                    mode="decimal"
                    (onBlur)="savePaymentLotDelivery()"
                ></app-inputNumber>
            </div>
            <div>
                <b>Phụ phí xăng dầu</b>
                <p *ngIf="lot?.type === 0">{{ paymentLotDeliver?.fuelFee | number }}</p>
                <app-inputNumber
                    *ngIf="lot?.type === 1"
                    class="tw-w-full"
                    formControlName="fuelFee"
                    mode="decimal"
                    (onBlur)="savePaymentLotDelivery()"
                ></app-inputNumber>
            </div>
            <div>
                <b>Chi phí khác</b>
                <p *ngIf="lot?.type === 0">{{ paymentLotDeliver?.otherFee | number }}</p>
                <app-inputNumber
                    *ngIf="lot?.type === 1"
                    class="tw-w-full"
                    formControlName="otherFee"
                    mode="decimal"
                    (onBlur)="savePaymentLotDelivery()"
                ></app-inputNumber>
            </div>
        </div>
    </p-fieldset>
</app-form>

<br />
<app-form *ngIf="formPaymentLotLocal" [formGroup]="formPaymentLotLocal" layout="vertical">
    <p-fieldset legend="Chi phí bảo hiểm(VNĐ)">
        <div class="tw-grid lg:tw-grid-cols-3 md:tw-grid-cols-2 tw-gap-4">
            <div>
                <b>Số phí bảo hiểm</b>
                <p *ngIf="lot?.type === 0">{{ paymentLotLocal?.insuranceFee | number }}</p>
                <app-inputNumber
                    *ngIf="lot?.type === 1"
                    class="tw-w-full"
                    formControlName="insuranceFee"
                    mode="decimal"
                    (onBlur)="savePaymentLotLocal()"
                ></app-inputNumber>
            </div>
        </div>
    </p-fieldset>
    <br />
    <p-fieldset legend="Chi phí local(VNĐ)">
        <div class="tw-grid lg:tw-grid-cols-3 md:tw-grid-cols-2 tw-gap-4">
            <div>
                <b>Chi phí</b>
                <!--<app-editable-input
                    [control]="formPaymentLotLocal.get('localFee')"
                    type="input-number"
                    placeholder="chi phí"
                    [trim]="true"
                    (save)="savePaymentLotLocal()"
                    fieldName="chi phí"
                >
                </app-editable-input>-->
                <app-inputNumber class="tw-w-full" formControlName="localFee" mode="decimal" (onBlur)="savePaymentLotLocal()"></app-inputNumber>
            </div>
            <div class="tw-col-span-2 tw-row-span-2">
                <b>Diễn giải</b>
                <textarea rows="6" pInputTextarea class="tw-w-full" formControlName="description"></textarea>
            </div>
            <app-form-item label="Đính kèm">
                <app-button-group-file
                    simpleUpload=""
                    (onFileSelected)="handleUploadFile($event)"
                    [attachments]="formPaymentLotLocal.getRawValue().attachments"
                    formControlName="attachmentIds"
                    [multiple]="true"
                ></app-button-group-file>

                <app-button-group-file
                    *ngIf="formPaymentLotLocal.getRawValue().attachments && formPaymentLotLocal.getRawValue().attachments.length > 0"
                    class="tw-col-span-2"
                    (onFileSelected)="handleUploadFile($event)"
                    [multiple]="true"
                    simpleUpload=""
                    formControlName="attachmentIds"
                ></app-button-group-file>
            </app-form-item>
        </div>
    </p-fieldset>
</app-form>
<br />
<p-fieldset legend="Tổng chi phí">
    <div class="tw-grid lg:tw-grid-cols-3 md:tw-grid-cols-2 tw-gap-4">
        <div>
            <b>Phí vận chuyển thực tế</b>
            <p>
                <ng-container *ngIf="!isLoadingPaymentSummary; else loading">
                    {{ paymentResult?.totalFeePreTax | number }}
                </ng-container>
            </p>
        </div>

        <div>
            <b>Chênh lệch so với dự kiến</b>
            <p>
                <ng-container *ngIf="!isLoadingPaymentSummary; else loading">
                    {{ paymentResult?.totalFeeDifference | number }}
                </ng-container>
            </p>
        </div>

        <div class="tw-text-red-500">
            <b>Tổng chi phí Logistic</b>
            <p>
                <ng-container *ngIf="!isLoadingPaymentSummary; else loading">
                    {{ paymentResult?.totalFeeAfterTax | number }}
                </ng-container>
            </p>
        </div>
    </div>
</p-fieldset>
<ng-template #loading>
    <i class="pi pi-spin pi-spinner"></i>
</ng-template>
<br />
