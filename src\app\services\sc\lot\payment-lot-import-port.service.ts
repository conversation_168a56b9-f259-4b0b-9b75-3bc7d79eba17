import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { PaymentLotImportPort } from '../../../models/interface/sc';

@Injectable()
export class PaymentLotImportPortService extends BaseService<PaymentLotImportPort> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/payment-lot-import-port');
    }
}
