import { Component, EventEmitter, Input, OnInit, Output, SimpleChanges, ViewChild, inject, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TableModule } from 'primeng/table';
import { CardModule } from 'primeng/card';
import { Menu, MenuModule } from 'primeng/menu';
import { ButtonModule } from 'primeng/button';
import { RadioButtonModule } from 'primeng/radiobutton';
import { FormsModule } from '@angular/forms';
import { ProductFileService } from 'src/app/services/pms/product-file/product-file.service';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmationService } from 'primeng/api';
import { AlertService } from 'src/app/shared/services/alert.service';
import { ProductDetail, ProductRecordVersion, ProductVersion, SecuredMenuItem } from 'src/app/models/interface/pms';
import { LIFECYCLE_STAGE_DOC_MAP, Status, STATUS_MAP } from 'src/app/models/constant/pms';
import { Router } from '@angular/router';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';

interface Row {
    prototype: ProductRecordVersion;
    pilot?: ProductRecordVersion;
    mp?: ProductRecordVersion;
}

@Component({
    selector: 'app-product-version',
    standalone: true,
    imports: [CommonModule, TableModule, CardModule, MenuModule, ButtonModule, RadioButtonModule, FormsModule, CheckboxModule, HasAnyAuthorityDirective],
    templateUrl: './product-version.component.html',
    styleUrls: ['./product-version.component.scss'],
    providers: [ProductFileService],
})
export class ProductVersionComponent implements OnInit, OnChanges {
    @Input() currentProduct: ProductDetail | null;
    @Output() openTransferPopup = new EventEmitter<ProductRecordVersion>();
    @Output() openApprovalPopup = new EventEmitter<ProductRecordVersion>();
    @Output() openLifecycleStagePopup = new EventEmitter<{ version: ProductRecordVersion; type: string }>();
    @Output() openSendApprovalPopup = new EventEmitter<ProductRecordVersion>();
    @Output() openHistoryChangedPopup = new EventEmitter<ProductRecordVersion>();
    @Output() openClonePopup = new EventEmitter<ProductRecordVersion>();
    @Output() loadVersionProfiles = new EventEmitter<ProductRecordVersion>();

    @ViewChild('menu') menu!: Menu;
    productFileService = inject(ProductFileService);

    columns = ['Prototype', 'Pilot', 'MP'];
    versionKeys: Array<'prototype' | 'pilot' | 'mp'> = ['prototype', 'pilot', 'mp'];
    tableRows: Row[] = [];
    menuItems: SecuredMenuItem[] = [];
    currentVersion!: ProductRecordVersion;
    constructor(
        private router: Router,
        private confirmationService: ConfirmationService,
        private alertService: AlertService,
    ) {}

    ngOnInit(): void {}
    ngOnChanges(changes: SimpleChanges) {
        if (changes['currentProduct'] && changes['currentProduct'].currentValue) {
            const stageKeys = Object.keys(LIFECYCLE_STAGE_DOC_MAP).map((k) => +k);
            const [prototypeStage, pilotStage, mpStage] = stageKeys;
            const prototypeVersions = this.currentProduct.productVersions.filter((v) => v.lifecycleStage === prototypeStage).map(this.mapToVersion);

            const pilotVersions = this.currentProduct.productVersions.filter((v) => v.lifecycleStage === pilotStage).map(this.mapToVersion);

            const mpVersions = this.currentProduct.productVersions.filter((v) => v.lifecycleStage === mpStage).map(this.mapToVersion);

            // 1. Gom tất cả version vào một Set
            const allVersions = new Set<string>();
            prototypeVersions.forEach((p) => allVersions.add(p.version));
            pilotVersions.forEach((p) => allVersions.add(p.version));
            mpVersions.forEach((m) => allVersions.add(m.version));

            // 2. Chuyển Set thành mảng và build tableRows
            this.tableRows = Array.from(allVersions).map((version) => {
                const proto = prototypeVersions.find((p) => p.version === version);
                const pilot = pilotVersions.find((v) => v.version === version);
                const mp = mpVersions.find((v) => v.version === version);

                return {
                    prototype: proto ? { ...proto, selected: false } : undefined,
                    pilot: pilot ? { ...pilot, selected: false } : undefined,
                    mp: mp ? { ...mp, selected: false } : undefined,
                };
            });

            this.tableRows = this.sortRows(this.tableRows);
        }
    }
    getVersionValue(obj) {
        if (obj.prototype && obj.prototype.version) {
            return parseFloat(obj.prototype.version);
        }
        if (obj.pilot && obj.pilot.version) {
            return parseFloat(obj.pilot.version);
        }
        if (obj.mp && obj.mp.version) {
            return parseFloat(obj.mp.version);
        }
        return Infinity;
    }

    hasCurrent(obj) {
        return (
            (obj.prototype != null && obj.prototype.isCurrent === 1) ||
            (obj.pilot != null && obj.pilot.isCurrent === 1) ||
            (obj.mp != null && obj.mp.isCurrent === 1)
        );
    }

    sortRows(arr) {
        return arr.slice().sort((a, b) => {
            const aIsCurr = this.hasCurrent(a) ? 0 : 1;
            const bIsCurr = this.hasCurrent(b) ? 0 : 1;
            if (aIsCurr !== bIsCurr) {
                return aIsCurr - bIsCurr; // các đối tượng có isCurrent===1 sẽ về đầu
            }
            return this.getVersionValue(a) - this.getVersionValue(b);
        });
    }

    //    Hàm mapper DTO → UI-model
    mapToVersion(v: ProductVersion): ProductRecordVersion {
        return {
            id: v.id,
            version: v.versionName,
            lastUpdate: new Date(v.updated),
            statusName: STATUS_MAP[v.status] ?? 'Draft',
            status: v.status,
            selected: false,
            note: v.note ?? '',
            lifecycleStage: v.lifecycleStage ?? 0,
            isCurrent: v.isCurrent,
            created: new Date(v.created),
            approvalTime: v.approvalTime ? new Date(v.approvalTime) : null,
            compared: v.compared,
            compareUrl: v.compareUrl,
        };
    }
    onMenuClick(event: MouseEvent, version: ProductRecordVersion, phase: 'prototype' | 'pilot' | 'mp') {
        this.currentVersion = version;

        this.menuItems = this.getMenuItems(version, phase);
        this.menu.toggle(event);
    }
    getMenuItems(version: ProductRecordVersion, phase: 'prototype' | 'pilot' | 'mp'): SecuredMenuItem[] {
        const allItems: SecuredMenuItem[] = [
            {
                label: 'Xem chi tiết',
                icon: 'pi pi-eye', // Icon xem chi tiết
                command: () => this.viewDetail(version),
                authority: ['ROLE_SYSTEM_ADMIN', 'version_detail_view'],
            },
            { label: 'Chỉnh sửa', icon: 'pi pi-pencil', command: () => this.editVersion(version), authority: ['ROLE_SYSTEM_ADMIN', 'version_update'] },
            {
                label: 'Nhân bản',
                icon: 'pi pi-clone', // Icon nhân bản
                command: () => this.cloneVersion(version),
                authority: ['ROLE_SYSTEM_ADMIN', 'version_create'],
            },
            {
                label: 'Chuyển giao',
                icon: 'pi pi-arrow-right', // Icon chuyển giao
                command: () => this.transferVersion(version),
                authority: ['ROLE_SYSTEM_ADMIN', 'version_transfer'],
            },
            {
                label: 'Gửi phê duyệt',
                icon: 'pi pi-paperclip', // Icon gửi phê duyệt (ví dụ dùng paperclip)
                command: () => this.sendRequestApproval(version),
                authority: ['ROLE_SYSTEM_ADMIN', 'version_submit'],
            },
            {
                label: 'Phê duyệt',
                icon: 'pi pi-check', // Icon phê duyệt
                command: () => this.approveVersion(version),
                authority: ['ROLE_SYSTEM_ADMIN', 'version_approve'],
            },
            {
                label: 'Chuyển giai đoạn Pilot',
                icon: 'pi pi-check-circle', // Icon Chuyển giai đoạn Pilot
                command: () => this.approvePilot(version),
                authority: ['ROLE_SYSTEM_ADMIN', 'version_pilot'],
            },
            {
                label: 'Chuyển giai đoạn MP',
                icon: 'pi pi-check-square', // Icon Chuyển giai đoạn MP
                command: () => this.approveMP(version),
                authority: ['ROLE_SYSTEM_ADMIN', 'version_mp'],
            },
            {
                label: 'Xóa',
                icon: 'pi pi-trash', // Icon xóa
                command: () => this.deleteVersion(version),
                authority: ['ROLE_SYSTEM_ADMIN', 'version_delete'],
            },
            {
                label: 'Lịch sử thay đổi',
                icon: 'pi pi-history', // Icon lịch sử
                command: () => this.viewHistory(version),
                authority: ['ROLE_SYSTEM_ADMIN', 'version_update_history'],
            },
        ];

        switch (true) {
            case version.status === Status['Draft'] && phase === 'prototype':
                return allItems.filter((item) => ['Xem chi tiết', 'Chỉnh sửa', 'Nhân bản', 'Gửi phê duyệt', 'Xóa', 'Lịch sử thay đổi'].includes(item.label));
            case version.status === Status['Draft'] && (phase === 'pilot' || phase === 'mp'):
                return allItems.filter((item) => ['Xem chi tiết', 'Chỉnh sửa', 'Nhân bản', 'Gửi phê duyệt', 'Lịch sử thay đổi'].includes(item.label));

            case version.status === Status['SentToApprove2'] && (phase === 'pilot' || phase === 'mp' || phase === 'prototype'):
                return allItems.filter((item) => ['Xem chi tiết', 'Nhân bản', 'Phê duyệt', 'Lịch sử thay đổi'].includes(item.label));
            case version.status === Status['SentToApprove'] && (phase === 'pilot' || phase === 'mp' || phase === 'prototype'):
                return allItems.filter((item) => ['Xem chi tiết', 'Nhân bản', 'Lịch sử thay đổi'].includes(item.label));
            case version.status === Status['Rejected'] && (phase === 'pilot' || phase === 'mp' || phase === 'prototype'):
                return allItems.filter((item) => ['Xem chi tiết', 'Chỉnh sửa', 'Nhân bản', 'Gửi phê duyệt', 'Lịch sử thay đổi'].includes(item.label));
            case version.status === Status['Approved'] && phase === 'prototype':
                return allItems.filter((item) =>
                    ['Xem chi tiết', 'Chuyển giai đoạn Pilot', 'Chuyển giai đoạn MP', 'Chuyển giao', 'Nhân bản', 'Lịch sử thay đổi'].includes(item.label),
                );
            case version.status === Status['Approved'] && phase === 'pilot':
                return allItems.filter((item) => ['Xem chi tiết', 'Chuyển giai đoạn MP', 'Chuyển giao', 'Nhân bản', 'Lịch sử thay đổi'].includes(item.label));
            case version.status === Status['Approved'] && phase === 'mp':
                return allItems.filter((item) => ['Xem chi tiết', 'Chuyển giao', 'Nhân bản', 'Lịch sử thay đổi'].includes(item.label));
            default:
                return [];
        }
    }
    editVersion(version: ProductRecordVersion): void {
        this.router.navigate(['/pms/product-file/edit', this.currentProduct.id, version.id], {
            state: {
                currentProduct: this.currentProduct,
                version,
            },
        });
    }

    viewDetail(version: ProductRecordVersion): void {
        this.router.navigate(['/pms/product-file/view', this.currentProduct.id, version.id], {
            state: {
                currentProduct: this.currentProduct,
                version,
            },
        });
    }
    approveMP(version: ProductRecordVersion): void {
        this.openLifecycleStagePopup.emit({
            version: version,
            type: 'MP',
        });
    }
    approvePilot(version: ProductRecordVersion): void {
        this.openLifecycleStagePopup.emit({ version, type: 'Pilot' });
    }
    deleteVersion(v: ProductRecordVersion): void {
        const versionId = v.id;
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.productFileService.deleteVersion(versionId).subscribe({
                    next: () => {
                        this.alertService.success('Thành công', 'Xóa thành công');

                        // reload dữ liệu
                        this.loadVersionProfiles.emit();
                    },
                    error: (err) => {
                        this.alertService.error('Lỗi', 'Xóa thất bại');
                        console.error('Delete error:', err);
                    },
                });
            },
        });
    }

    approveVersion(v: ProductRecordVersion): void {
        this.openApprovalPopup.emit(v);
    }
    sendRequestApproval(v: ProductRecordVersion): void {
        this.openSendApprovalPopup.emit(v);
    }
    getCheckedVersions(): ProductRecordVersion[] {
        return this.tableRows.flatMap((r) => this.versionKeys.map((k) => r[k])).filter((v): v is ProductRecordVersion => !!v && !!v.selected);
    }
    onCheckboxChange() {
        this.getCheckedVersions();
    }

    cloneVersion(v: ProductRecordVersion) {
        this.openClonePopup.emit(v);
    }
    transferVersion(v: ProductRecordVersion) {
        this.openTransferPopup.emit(v);
    }
    viewHistory(v: ProductRecordVersion) {
        this.openHistoryChangedPopup.emit(v);
    }
    compareVersions() {
        // Lấy tất cả Version từ các row, lọc những cái selected = true
        const selected: ProductRecordVersion[] = this.tableRows
            .flatMap((row) => this.versionKeys.map((key) => row[key]))
            .filter((v): v is ProductRecordVersion => !!v && v.selected === true);

        return selected;
    }
}
