import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, OnInit, TemplateRef, ViewChild, inject } from '@angular/core';
import { RouterModule } from '@angular/router';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { isArray } from 'lodash';
import { ConfirmationService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';
import { ChipModule } from 'primeng/chip';
import { AuthService } from 'src/app/core/auth/auth.service';
import { TABLE_KEY } from 'src/app/models/constant';
import { Column, EventPopupSubmit } from 'src/app/models/interface';
import { ApproveService } from 'src/app/services/smart-qc/masterdata/approve.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { TableCommonService, TableState } from 'src/app/shared/table-module/table.common.service';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { Po } from 'src/app/models/interface/sc';
import { PoService } from 'src/app/services/sc/po/po.service';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { MAP_PO_STATE, PO_STATE } from 'src/app/models/constant/sc';
import { PopupComponent } from 'src/app/shared/components/popup/popup.component';
import { FileService } from 'src/app/shared/services/file.service';

@Component({
    selector: 'app-sc-po',
    standalone: true,
    imports: [
        TableCommonModule,
        CommonModule,
        RouterModule,
        ButtonModule,
        TagModule,
        TooltipModule,
        ChipModule,
        SubHeaderComponent,
        HasAnyAuthorityDirective,
        PopupComponent,
    ],
    templateUrl: './list.component.html',
    providers: [ApproveService, TableCommonService, PoService],
})
export class SCCOrderListComponent implements OnInit, AfterViewInit {
    @ViewChild('templateName') templateName: TemplateRef<Element>;
    @ViewChild('templateState') templateState: TemplateRef<Element>;
    @ViewChild('templateTotalValue') templateTotalValue: TemplateRef<Element>;

    orderService = inject(PoService);
    fileService = inject(FileService);
    tableCommonService = inject(TableCommonService);
    loadingService = inject(LoadingService);
    confirmationService = inject(ConfirmationService);
    alertService = inject(AlertService);
    authService = inject(AuthService);
    state: QueryObserverBaseResult<Po[]>;
    columns: Column[] = [];
    tableId: string = TABLE_KEY.SCC_PO;
    itemsHeader = [{ label: 'Quản lý phê duyệt' }, { label: 'Danh sách phê duyệt', url: 'approve' }];
    actionHeader: TemplateRef<Element>;
    bodyFilter: Record<string, unknown> = {
        contractIds: null,
        actionIds: null,
    };
    rowSelects: Po[] = [];
    mapState = MAP_PO_STATE;
    optionPoState = PO_STATE;
    totalAmount: { totalValueVnd: number; totalValueUds: number } = {
        totalValueUds: null,
        totalValueVnd: null,
    };

    ngOnInit() {
        this.tableCommonService
            .init<Po>({
                tableId: this.tableId,
                queryFn: (filter) => this.orderService.getPageTableCustom(filter),
                configFilterRSQL: {
                    orderNo: 'Text',
                    orderDate: 'DateRange',
                    supplierId: 'SetLong',
                    imputation: 'Text',
                    requestNo: 'Text',
                    supplierNo: 'Text',
                    state: 'Number',
                    invTransferNumberImport: 'Text',
                    invTransferNumberExport: 'Text',
                    accountingCode: 'Text',
                    unitPrice: 'Text',
                    totalValue: 'Number',
                },
                defaultParams: {
                    sort: 'orderDate,desc',
                },
                filterUrl: true,
                after: (stateTable: TableState<Po>) => {
                    this.orderService.getTotalAmount(stateTable.params.rsql, stateTable.params.pageable).subscribe({
                        next: (res: { totalValueVnd: number; totalValueUds: number }) => {
                            this.totalAmount = res;
                        },
                    });
                },
            })
            .subscribe((state) => {
                this.state = state;
            });

        this.tableCommonService.getRowSelect(this.tableId).subscribe((state) => {
            if (isArray(state)) {
                this.rowSelects = state;
            }
        });
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.columns = [
                {
                    header: 'Số đơn hàng',
                    field: 'orderNo',
                    type: 'link',
                    url: './{id}',
                    default: true,
                },
                {
                    field: 'orderDate',
                    header: 'Ngày đặt hàng',
                    type: 'date',
                    format: 'dd/MM/yyyy',
                    sort: 'orderDate',
                    typeSort: 'desc',
                },
                { field: 'supplierName', header: 'Nhà cung cấp', style: { 'max-width': '8rem' } },
                { field: 'supplierNo', header: 'Số nhà cung cấp', style: { 'max-width': '20rem' } },
                { field: 'state', header: 'Trạng thái', style: { 'max-width': '20rem' }, body: this.templateState },
                { field: 'accountingCode', header: 'Mã kế toán' },
                { field: 'unitPrice', header: 'Giá trị đơn hàng' },
                { field: 'totalValue', header: 'Tổng giá trị', body: this.templateTotalValue },
                { field: 'invTransferNumberImport', header: 'Phiếu chuyển giao', style: { 'max-width': '40rem' } },
                { field: 'invTransferNumberExport', header: 'Phiếu xuất kho', style: { 'max-width': '40rem' } },
            ];
        }, 0);
    }

    delete = (ids: number[]) => {
        return this.orderService.batchDelete(ids);
    };

    getSeverity(state: number): string {
        switch (state) {
            case 3:
                return 'success';
            case 2:
                return 'primary';
            case 1:
                return 'info';
            case 0:
                return 'info';
            default:
                return 'info';
        }
    }

    exportExcel(event: EventPopupSubmit<unknown>) {
        this.loadingService.show();
        this.orderService.exportExcel(this.tableCommonService.getColumnVisible(this.tableId).value, this.tableCommonService.getParams(this.tableId)).subscribe({
            next: (res: Blob) => {
                // Gọi hàm downloadBlob để tải file
                this.fileService.downloadBlob(res, `Danh sách PO - ${new Date().getTime()}.xlsx`);
                this.loadingService.hide();
                event.close();
            },
            error: () => {
                this.alertService.error('Có lỗi xảy ra');
                this.loadingService.hide();
            },
        });
    }

    showTotalValue(unitPrice: string, totalValue: number) {
        if (totalValue === null) {
            return '';
        }

        const formattedValue = totalValue.toLocaleString('en-US');

        if (unitPrice === 'USD') {
            return `$${formattedValue}`;
        } else {
            return `đ${formattedValue}`;
        }
    }
}
