import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { TableModule } from 'primeng/table';
import { Po, PoDetailDTO, PoTransfer } from 'src/app/models/interface/sc';
import { TooltipModule } from 'primeng/tooltip';
import { ConfirmationService } from 'primeng/api';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { MAP_TRANSFER_STATE, PO_STATE_CONSTANT } from 'src/app/models/constant/sc';
import { TagModule } from 'primeng/tag';
import { PoTransferService } from 'src/app/services/sc/po/po-transfer.service';
import { EditPoTransferComponent } from '../popup-edit-import/edit.po-import.component';
import { environment } from './../../../../../../environments/environment';
import { FileService } from 'src/app/shared/services/file.service';
import { PoDetailService } from '../../../../../services/sc/po/po-detail.service';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { AttributeAuthorityDirective } from 'src/app/shared/directives/attribute-authority.directive';

@Component({
    selector: 'app-list-po-transfer',
    templateUrl: './po-transfer.component.html',
    styleUrls: ['./po-transfer.component.scss'],
    standalone: true,
    imports: [
        TableModule,
        InputTextModule,
        DropdownModule,
        CalendarModule,
        ButtonModule,
        CommonModule,
        EditPoTransferComponent,
        TooltipModule,
        TagModule,
        HasAnyAuthorityDirective,
        AttributeAuthorityDirective,
    ],
    providers: [PoTransferService, PoDetailService],
})
export class ListPoTransferComponent implements OnChanges {
    @Input() po: Po;
    @Output() updatePoDetails = new EventEmitter<PoDetailDTO>();
    @Output() reloadPo = new EventEmitter<void>();
    @Input() activeIndex: number;
    poTransfers: PoTransfer[] = [];
    poTransferView: PoTransfer;
    loading: boolean = false;
    isEdit: boolean = false;
    mapStateTransfer = MAP_TRANSFER_STATE;
    environment = environment;
    PO_STATE_CONSTANT = PO_STATE_CONSTANT;
    constructor(
        private poDetailService: PoDetailService,
        private poTransferService: PoTransferService,
        private confirmationService: ConfirmationService,
        private alertService: AlertService,
        private loadingService: LoadingService,
        private fileService: FileService,
    ) {}

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['activeIndex'] && changes['activeIndex'].currentValue === 2) {
            this.getListTransfer();
        }
    }

    getListTransfer() {
        this.poTransferService.getPage(`query=poId==${this.po.id};type==0&page=0&size=100&sort=id,desc`).subscribe({
            next: (res) => {
                this.poTransfers = res.body;
                this.loading = false;
            },
        });
    }
    confirmDelete(poTransfer: PoTransfer, index: number) {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.loadingService.show();
                this.poTransferService.delete(poTransfer.id).subscribe({
                    next: () => {
                        // Call API lay bang ke
                        this.poDetailService.getPoDetailByPo(poTransfer.poId).subscribe({
                            next: (res) => {
                                this.updatePoDetails.emit(res as PoDetailDTO);
                                this.alertService.success('Thành công');
                                this.loadingService.hide();
                                this.poTransfers.splice(index, 1);
                            },
                            error: () => {
                                this.loadingService.hide();
                            },
                        });
                    },
                    error: () => {
                        this.loadingService.hide();
                    },
                });
            },
        });
    }
    handleClose() {
        this.isEdit = false;
        this.getListTransfer();
    }

    addTransfer() {
        this.poTransferView = null;
        this.isEdit = true;
    }
    viewDetail(poTransfer: PoTransfer) {
        this.poTransferView = poTransfer;
        this.isEdit = true;
    }

    getSeverity(poTransfer: PoTransfer) {
        switch (poTransfer.state) {
            case 3:
                return 'danger';
            case 0:
                return 'primary';
            case 1:
                return 'primary';
            case 2:
                return 'success';
            default:
                return 'primary';
        }
    }

    getValueTagState(poTransfer: PoTransfer) {
        return this.mapStateTransfer[poTransfer.state];
    }

    export(id: number) {
        this.loadingService.show();
        this.poTransferService.export(id).subscribe({
            next: (res) => {
                this.loadingService.hide();
                this.fileService.downLoadFileByService(res.url, '/sc/api');
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    updateDetails(poDetailDTO: PoDetailDTO) {
        this.updatePoDetails.emit(poDetailDTO);
    }

    reSyncInv(id: number) {
        this.loadingService.show();
        this.poTransferService.reSyncInv(id).subscribe({
            next: (res) => {
                this.loadingService.hide();
                this.alertService.success('Thành công');
                this.getListTransfer();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }
}
