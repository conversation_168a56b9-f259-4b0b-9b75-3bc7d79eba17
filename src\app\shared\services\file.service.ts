import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { GeneralEntity } from '../../models/interface';

@Injectable()
export class FileService {
    downLoadFile(file: GeneralEntity) {
        const a = document.createElement('a');
        a.href = file.url.startsWith('http') ? file.url : environment.HOST_GW + '/smart-qc/api/download?filePath=' + file.url;
        a.download = 'download';
        a.click();
        a.remove();
    }

    downLoadFileByService(filePath: string, service: '/auth/api' | '/smart-qc/api' | '/sc/api') {
        const a = document.createElement('a');
        a.href = filePath.startsWith('http') ? filePath : environment.HOST_GW + service + '/download?filePath=' + filePath;
        a.download = 'download';
        a.click();
        a.remove();
    }

    downLoadSampleFileByService(filePath: string, service: '/auth/api' | '/smart-qc/api' | '/sc/api') {
        const a = document.createElement('a');
        a.href = filePath.startsWith('http') ? filePath : environment.HOST_GW + service + '/sample?fileName=' + filePath;
        a.download = 'download';
        a.click();
        a.remove();
    }

    updateUrlTemplate(filePath: string, service: '/auth/api' | '/smart-qc/api' | '/sc/api') {
        return filePath.startsWith('http') ? filePath : environment.HOST_GW + service + '/sample?fileName=' + filePath;
    }

    updateUrlDownload(filePath: string, service: '/auth/api' | '/smart-qc/api' | '/sc/api') {
        return filePath?.startsWith('http') ? filePath : environment.HOST_GW + service + '/download?filePath=' + filePath;
    }

    // Hàm tải xuống file từ Blob
    downloadBlob(blob: Blob, fileName: string) {
        const downloadURL = window.URL.createObjectURL(blob);

        // Tạo link để tải file
        const link = document.createElement('a');
        link.href = downloadURL;
        link.download = fileName; // Đặt tên file khi tải về
        link.click(); // Tự động nhấn vào link để tải file
        link.remove();
    }
}
