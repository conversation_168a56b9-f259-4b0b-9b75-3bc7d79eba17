import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

@Injectable({
    providedIn: 'root',
})
export class GeneralReportService {
    constructor(protected http: HttpClient) {}

    getReport(filter: unknown) {
        return this.http.post<unknown>(`/smart-qc/api/report/general`, filter);
    }

    exportGeneralReport(filter) {
        return this.http.post<unknown[]>(`/smart-qc/api/report/export-general`, filter, {
            observe: 'response',
        });
    }
}
