.wizard-double-row-item {
    margin-left: 20px;
}

.wizard-double-row {
    padding: 7px 8px 7px 0px;
    margin-right: 5px;
    position: relative;
    display: inline-block;
    margin-left: 2px;
}

.wizard-double-row::before {
    width: 0;
    height: 0;
    border-top: 25px inset transparent;
    border-bottom: 25px inset transparent;
    border-left: 15px solid #fff;
    position: absolute;
    content: "";
    top: 0;
    left: 0;
}

.wizard-double-row::after {
    width: 0;
    height: 0;
    border-top: 25px inset transparent;
    border-bottom: 24px inset transparent;
    position: absolute;
    content: "";
    top: 0;
    right: -16px;
    z-index: 2;
}

.wizard-double-row-dr {
    background: rgba(189, 224, 235, 1);
    color: rgba(68, 119, 169, 1) !important;
}

.wizard-double-row-dr::after {
    border-left: 16px solid rgba(189, 224, 235, 1);
}

.wizard-double-row-dr-active {
    background: rgba(54, 107, 161, 1);
    color: white !important;
}

.wizard-double-row-dr-active::after {
    border-left: 16px solid rgba(54, 107, 161, 1);
}
