<app-topbar class="tw-fixed tw-w-full tw-top-0 tw-left-0" style="z-index: 997"></app-topbar>
<p-confirmDialog key="app-confirm" [style]="{ width: '350px' }" #cd>
    <ng-template pTemplate="footer">
        <p-button type="button" [raised]="true" label="Đồng ý" (click)="cd.accept()"></p-button>
        <p-button type="button" [text]="true" [raised]="true" label="Hủy bỏ" (click)="cd.reject()"></p-button>
    </ng-template>
</p-confirmDialog>
<div
    class="tw-grid tw-pt-14 tw-duration-150"
    style="background-color: #f2f5fa"
    [ngClass]="{
        'tw-grid-cols-sidebar-expand': sideBarState.device === 'desktop' && sideBarState.state === 'expand',
        'tw-grid-cols-sidebar-collapse': sideBarState.device === 'desktop' && sideBarState.state === 'collapse',
        'tw-grid-cols-sidebar-close':
            sideBarState.device === 'mobile' || (sideBarState.device === 'desktop' && sideBarState.state === 'close'),
    }"
>
    <!-- Sidebar -->
    <ng-container *ngTemplateOutlet="templateSidebar"></ng-container>
    <!-- Content -->
    <div>
        <router-outlet></router-outlet>
    </div>
</div>
<div
    style="position: fixed; top: 0; left: 0; z-index: 101; width: 100%; height: 100%; background-color: var(--maskbg)"
    [ngClass]="{
        'tw-visible': sideBarState.device === 'mobile' && sideBarState.state === 'expand',
        'tw-invisible':
            (sideBarState.device === 'mobile' && sideBarState.state === 'close') || sideBarState.device === 'desktop',
    }"
    (click)="hiddenMark()"
>
    <ng-container *ngTemplateOutlet="templateSidebar"></ng-container>
</div>
<ng-template #templateSidebar>
    <app-sidebar
        class="tw-sticky tw-transition-all tw-duration-300 tw-ease-out"
        [ngClass]="{
            'sidebar-mobile': sideBarState.device === 'mobile',
            'tw-sidebar-mobile-shown': sideBarState.device === 'mobile' && sideBarState.state === 'expand',
            'tw-sidebar-mobile-hidden': sideBarState.device === 'mobile' && sideBarState.state === 'close',
        }"
        style="height: calc(100vh - 3.5rem); top: 3.5rem; left: 0; z-index: 100"
    ></app-sidebar>
</ng-template>
