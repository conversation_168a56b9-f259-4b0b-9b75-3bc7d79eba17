<app-sub-header [items]="itemsHeader" [action]="actionHeader">
    <ng-template #actionHeader> </ng-template>
</app-sub-header>
<div style="padding: 1rem 1rem 0 1rem">
    <app-table-common
        [tableId]="tableId"
        [columns]="columns"
        [data]="state.data"
        [loading]="state.isFetching"
        [authoritiesDelete]="['ROLE_SYSTEM_ADMIN']"
        [deleteButton]="true"
        [inactiveBtn]="true"
        [actionTemplate]="actionsTpl"
        [hideButtonHeader]="true"
        [selectionMode]="null"
        name=""
    >
        <ng-template #requestTimeValue let-row> {{ row.requestTime | date: 'HH:mm:ss dd/MM/yyyy' }} </ng-template>
        <ng-template #createdValue let-row> {{ row.created | date: ' dd/MM/yyyy' }} </ng-template>
        <ng-template #statusValue let-row> {{ STATUS_TRANSFER_MAP[row.status] || '' }} </ng-template>
        <ng-template #actionsTpl let-row>
            <td style="white-space: nowrap; text-align: center">
                <p-menu #ActionMenu [popup]="true" [model]="itemsAction" appendTo="body">
                    <ng-template let-item pTemplate="item">
                        <ng-container *appHasAnyAuthority="item.authorities">
                            <div class="flex items-center gap-2 px-3 py-2 hover:bg-gray-100 rounded cursor-pointer">
                                <i [ngClass]="item.icon" class="text-gray-600"></i>
                                <span>{{ item.label }}</span>
                            </div>
                        </ng-container>
                    </ng-template>
                </p-menu>
                <!-- Nút bật menu -->
                <button
                    pButton
                    type="button"
                    icon="pi pi-ellipsis-h"
                    class="p-button-text p-button-sm"
                    (click)="onMenuClick($event, row)"
                    title="Thao tác"
                ></button>
            </td>
        </ng-template>
    </app-table-common>

    <!-- popup-Edit -->
    <app-popup
        #EditPopup
        [dialogWidth]="'60vw'"
        [header]="title"
        [isButtonVisible]="isButtonVisible"
        [formGroup]="formEditPopup"
        (onSubmit)="submitFormEdit($event)"
        (onClose)="handlePopupClose()"
    >
        <app-form [formGroup]="formEditPopup" styleClass="tw-grid tw-grid-cols-1 tw-gap-4" *ngIf="formEditPopup">
            <div class="tw-grid tw-grid-cols-2 tw-gap-4">
                <app-custom-form-item class="tw-break-normal" label="Mã lệnh sản xuất">
                    <span class="tw-leading-8"> {{ formEditPopup.get('workOrderCode')?.value || '' }}</span>
                </app-custom-form-item>

                <app-custom-form-item class="tw-break-normal" label="Ngày sản xuất">
                    <span class="tw-leading-8"> {{ formEditPopup.get('requestTime')?.value | date: 'HH:mm:ss dd/MM/yyyy' || '' }}</span>
                </app-custom-form-item>
                <app-custom-form-item class="tw-break-normal" label="VNPT Man P/N">
                    <span class="tw-leading-8"> {{ formEditPopup.get('vnptManPn')?.value || '' }}</span>
                </app-custom-form-item>
                <app-custom-form-item class="tw-break-normal" label="Tên sản phẩm">
                    <span class="tw-leading-8"> {{ formEditPopup.get('nameProduct')?.value || '' }}</span>
                </app-custom-form-item>
                <app-custom-form-item label="HS CNSX" [control]="formEditPopup.get('versionId')">
                    <app-combobox-nonRSQL
                        #productLinesPopup
                        [fetchOnInit]="true"
                        type="select"
                        formControlName="productLines"
                        fieldValue="id"
                        (panelShow)="handlePanelShow(productLinesPopup)"
                        fieldLabel="name"
                        url="/pr/api/product-line/filter"
                        param="name"
                        placeholder="Chọn dòng sản phẩm"
                        [additionalParams]="{ unpaged: false }"
                    >
                    </app-combobox-nonRSQL>
                </app-custom-form-item>
                <app-custom-form-item class="tw-break-normal" label="Đơn vị tạo phiếu">
                    <span class="tw-leading-8">PEO</span>
                </app-custom-form-item>
                <app-custom-form-item class="tw-break-normal" label="Đơn vị nhận phiếu">
                    <span class="tw-leading-8">NMĐT</span>
                </app-custom-form-item>
                <app-custom-form-item class="tw-break-normal" label="Người tạo phiếu">
                    <span class="tw-leading-8"> {{ formEditPopup.get('creator')?.value || '' }}</span>
                </app-custom-form-item>
                <app-custom-form-item label="Người nhận phiếu" [control]="formEditPopup.get('receiver')">
                    <app-combobox-nonRSQL
                        #productLinesPopup
                        [fetchOnInit]="true"
                        type="select"
                        formControlName="productLines"
                        fieldValue="id"
                        (panelShow)="handlePanelShow(productLinesPopup)"
                        fieldLabel="name"
                        url="/pr/api/product-line/filter"
                        param="name"
                        placeholder="Chọn dòng sản phẩm"
                        [additionalParams]="{ unpaged: false }"
                    >
                    </app-combobox-nonRSQL>
                </app-custom-form-item>
                <app-custom-form-item class="tw-break-normal" label="Ngày tạo phiếu">
                    <span class="tw-leading-8"> {{ formEditPopup.get('created')?.value | date: 'HH:mm:ss dd/MM/yyyy' || '' }}</span>
                </app-custom-form-item>
                <app-custom-form-item label="Ghi chú" [control]="formEditPopup.get('note')">
                    <app-upload-custom
                        [loading]="loadingInstruction"
                        [filePath]="formEditPopup.get('userGuide')?.value"
                        #DocUploader
                        [limit]="1"
                        [fileNamePattern]="fileNamePattern"
                        fileNamePatternMessage="Tên file không đúng format, vui lòng kiểm tra lại theo format Tên sản phẩm_Tên tài liệu_Version_Buildtime"
                        (onChange)="handleChangeFile($event, 'SOFTWARE_INSTRUCTION')"
                        (onClear)="handleClearFile('SOFTWARE_INSTRUCTION')"
                    ></app-upload-custom>
                </app-custom-form-item>
            </div>
        </app-form>
    </app-popup>
    <!-- popup theo dõi lịch sử thay đổi -->
    <app-popup #HistoryPopup header="Theo dõi lịch sử cập nhật phần mềm" [isButtonVisible]="false" dialogWidth="80vw" [showConfirmButton]="false">
        <app-table-custom [columns]="trackCols" [data]="trackData">
            <ng-template TableCell="details" let-row>
                <a
                    *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'software_applied_profiles']"
                    href="#"
                    (click)="openProfileList(row); $event.preventDefault()"
                    style="text-decoration: underline; cursor: pointer"
                >
                    Xem danh sách
                </a>
            </ng-template>
            <ng-template TableCell="userGuide" let-row>
                <a [href]="STORAGE_BASE_URL + '/' + row.userGuide" rel="noopener" target="_blank">
                    {{ row.instructionName }}
                </a>
            </ng-template>

            Theo dõi lịch sử cập nhật phần mềm
        </app-table-custom>
    </app-popup>

    <!-- popup danh sách hồ sơ áp dụng -->
    <app-popup #ListProfilePopup header="Danh sách hồ sơ áp dụng" [isButtonVisible]="false" dialogWidth="60vw" [showConfirmButton]="false">
        <app-table-custom [columns]="profileCols" [data]="profileData">
            <ng-template TableCell="action" let-row>
                <a
                    [routerLink]="['/pms/product-file/view', row.productId, row.productVersion]"
                    target="_blank"
                    rel="noopener"
                    style="text-decoration: underline; cursor: pointer"
                >
                    Mở hồ sơ
                </a>
            </ng-template>
        </app-table-custom>
    </app-popup>

    <app-popup
        #SubmitChangeVersion
        [dialogWidth]="'50vw'"
        header="Chọn hồ sơ cập nhật version phần mềm"
        [isButtonVisible]="isButtonVisible"
        (onSubmit)="submitChangeVersion()"
        (onClose)="closeChangeVersionPopup()"
    >
        <p-table [value]="dataTableVersionCols" [(selection)]="selectedRowsToSubmit" [tableStyle]="{ 'min-width': '50rem' }">
            <ng-template pTemplate="header">
                <tr>
                    <th style="width: 4rem">
                        <p-tableHeaderCheckbox />
                    </th>
                    <th *ngFor="let row of tableVersionCols">{{ row.header }}</th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-rowData>
                <tr>
                    <td>
                        <p-tableCheckbox [value]="rowData" />
                    </td>
                    <td *ngFor="let col of tableVersionCols">
                        <ng-container [ngSwitch]="col.field">
                            <ng-container *ngSwitchCase="'lifeCycleStageSelect'">
                                <p-dropdown
                                    [options]="stageKeys"
                                    optionLabel="name"
                                    optionValue="id"
                                    [(ngModel)]="rowData.lifeCycleStageSelect"
                                    [filter]="true"
                                    filterBy="name"
                                    [showClear]="true"
                                    appendTo="body"
                                    placeholder="Chọn LifecycleStage"
                                ></p-dropdown>
                            </ng-container>
                            <ng-container *ngSwitchDefault>
                                {{ col.field === 'lifecycleStage' ? mapLifecycle(rowData[col.field]) : rowData[col.field] }}
                            </ng-container>
                        </ng-container>
                    </td>
                </tr>
            </ng-template>
        </p-table>
    </app-popup>
</div>
