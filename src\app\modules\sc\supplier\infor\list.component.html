<app-sub-header [items]="itemsHeader" [action]="actionHeader">
    <ng-template #actionHeader>
        <p-button size="small" label="Tạo mới" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_supplier_edit']" routerLink="/sc/supplier-infor/create" />
        <p-button (onClick)="op.toggle($event)" label="Nhập Excel" />

        <app-popup
            header="Xuất báo cáo đánh giá thường niên"
            severity="success"
            label="Xuất báo cáo"
            [formGroup]="formPopupExport"
            *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_supplier_export']"
            (onSubmit)="handleExportReport($event)"
        >
            <app-form [formGroup]="formPopupExport" styleClass="tw-grid tw-grid-cols-1 tw-gap-4" *ngIf="formPopupExport">
                <app-form-item label="Thời gian giao dịch">
                    <p-calendar
                        [showButtonBar]="true"
                        [showIcon]="true"
                        view="year"
                        dateFormat="yy"
                        appendTo="body"
                        formControlName="year"
                        placeholder="từ"
                    ></p-calendar>
                </app-form-item>

                <app-form-item label="Định dạng">
                    <p-dropdown
                        [options]="[
                            {
                                label: 'Word',
                                value: 'WORD',
                            },
                            {
                                label: 'Pdf',
                                value: 'PDF',
                            },
                        ]"
                        optionLabel="label"
                        optionValue="value"
                        appendTo="body"
                        formControlName="type"
                /></app-form-item>
            </app-form>
        </app-popup>
        <app-popup
            header="Xuất danh sách nhà cung cấp"
            severity="success"
            label="Xuất danh sách"
            *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_supplier_export']"
            (onSubmit)="exportExcel($event)"
            typePopup="download"
        ></app-popup>

        <p-button (onClick)="result.toggle($event)" label="Kết quả đánh giá" />
    </ng-template>
</app-sub-header>
<div style="padding: 1rem 1rem 0 1rem">
    <app-table-multi
        [tableIds]="[tableId, tableIdDelete]"
        [configMulti]="{
            SUPPLIER_INFOR: {
                name: 'Danh sách nhà cung cấp hiện hữu',
                columns: columns,
                selectionMode: 'multiple',
                loading: state && state?.isFetching,
                funcDelete: softDeleteSelected,
                authoritiesDelete: ['ROLE_SYSTEM_ADMIN', 'sc_supplier_delete'],
                data: state && state?.data,
                filterTemplate: filterTemplate,
            },
            SUPPLIER_INFOR_DELETE: {
                name: 'Danh sách nhà cung cấp đã xóa',
                columns: columns,
                selectionMode: 'multiple',
                loading: stateDelete && stateDelete?.isFetching,
                funcDelete: null,
                authoritiesDelete: ['ROLE_SYSTEM_ADMIN', 'sc_supplier_rollback'],
                data: stateDelete && stateDelete?.data,
                filterTemplate: filterTemplateDelete,
                actionTemplate: actionTemplateDelete,
            },
        }"
    >
        <ng-template #actionTemplateDelete>
            <p-button
                title="Khôi phục"
                icon="pi pi-undo"
                [outlined]="true"
                size="small"
                (click)="restore()"
                [ngClass]="{
                    'qc-disabled': !idSelectDeletes || idSelectDeletes.length === 0,
                }"
            ></p-button>
            <div style="border-right: 1px solid #ebeff5; height: 2.25rem; width: 1px"></div>
        </ng-template>
        <ng-template #filterTemplate>
            <tr>
                <th></th>
                <th [appFilter]="[tableId, 'name']">
                    <app-filter-table [tableId]="tableId" field="name" placeholder="Tên ncc"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'shortName']">
                    <app-filter-table [tableId]="tableId" field="shortName" placeholder="viết tắt"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'code']">
                    <app-filter-table [tableId]="tableId" field="code" placeholder="id"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'supplierTypeId']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="supplierTypeId"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'displayName',
                            rsql: true,
                            param: 'name',
                            paramForm: 'id',
                            url: '/sc/api/supplier-type/search',
                        }"
                        placeholder="loai ncc"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'productSupplied']">
                    <app-filter-table [tableId]="tableId" field="productSupplied" placeholder="hàng hóa"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'address']">
                    <app-filter-table [tableId]="tableId" field="address" placeholder="địa chỉ"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'country']">
                    <app-filter-table [tableId]="tableId" field="country" placeholder="quốc gia"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'website']">
                    <app-filter-table [tableId]="tableId" field="website" placeholder="website"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'contact']">
                    <app-filter-table [tableId]="tableId" field="contact" placeholder="liên hệ"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'position']">
                    <app-filter-table [tableId]="tableId" field="position" placeholder="chức vụ"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'email']">
                    <app-filter-table [tableId]="tableId" field="email" placeholder="email"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'phone']">
                    <app-filter-table [tableId]="tableId" field="phone" placeholder="sđt"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'yearBegin']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="yearBegin"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'label',
                            filterLocal: true,
                            options: yearRange,
                        }"
                        placeholder="năm giao dịch"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'profileLink']"></th>
                <th [appFilter]="[tableId, 'profileStatus']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="profileStatus"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'label',
                            filterLocal: true,
                            options: profileStateOption,
                        }"
                        placeholder="trạng thái hsnl"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'point']">
                    <app-filter-table [tableId]="tableId" field="point" type="number" placeholder=""></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'rate']">
                    <app-filter-table [tableId]="tableId" field="rate" placeholder=""></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'state']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="state"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'label',
                            filterLocal: true,
                            options: supplierStateOption,
                        }"
                        placeholder="trạng thái"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'note']">
                    <app-filter-table [tableId]="tableId" field="note" placeholder=""></app-filter-table>
                </th>
            </tr>
        </ng-template>

        <ng-template #filterTemplateDelete>
            <tr>
                <th></th>
                <th [appFilter]="[tableIdDelete, 'name']">
                    <app-filter-table [tableId]="tableIdDelete" field="name" placeholder="Tên ncc"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdDelete, 'shortName']">
                    <app-filter-table [tableId]="tableIdDelete" field="shortName" placeholder="viết tắt"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdDelete, 'code']">
                    <app-filter-table [tableId]="tableIdDelete" field="code" placeholder="id"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdDelete, 'supplierTypeId']">
                    <app-filter-table
                        [tableId]="tableIdDelete"
                        field="supplierTypeId"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'displayName',
                            rsql: true,
                            param: 'name',
                            paramForm: 'id',
                            url: '/sc/api/supplier-type/search',
                        }"
                        placeholder="loai ncc"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableIdDelete, 'productSupplied']">
                    <app-filter-table [tableId]="tableIdDelete" field="productSupplied" placeholder="hàng hóa"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdDelete, 'address']">
                    <app-filter-table [tableId]="tableIdDelete" field="address" placeholder="địa chỉ"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdDelete, 'country']">
                    <app-filter-table [tableId]="tableIdDelete" field="country" placeholder="quốc gia"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdDelete, 'website']">
                    <app-filter-table [tableId]="tableIdDelete" field="website" placeholder="website"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdDelete, 'contact']">
                    <app-filter-table [tableId]="tableIdDelete" field="contact" placeholder="liên hệ"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdDelete, 'position']">
                    <app-filter-table [tableId]="tableIdDelete" field="position" placeholder="chức vụ"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdDelete, 'email']">
                    <app-filter-table [tableId]="tableIdDelete" field="email" placeholder="email"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdDelete, 'phone']">
                    <app-filter-table [tableId]="tableIdDelete" field="phone" placeholder="sđt"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdDelete, 'yearBegin']">
                    <app-filter-table
                        [tableId]="tableIdDelete"
                        field="yearBegin"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'label',
                            filterLocal: true,
                            options: yearRange,
                        }"
                        placeholder="năm giao dịch"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableIdDelete, 'profileLink']"></th>
                <th [appFilter]="[tableIdDelete, 'profileStatus']">
                    <app-filter-table
                        [tableId]="tableIdDelete"
                        field="profileStatus"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'label',
                            filterLocal: true,
                            options: profileStateOption,
                        }"
                        placeholder="trạng thái hsnl"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableIdDelete, 'point']">
                    <app-filter-table [tableId]="tableIdDelete" field="point" type="number" placeholder=""></app-filter-table>
                </th>
                <th [appFilter]="[tableIdDelete, 'rate']">
                    <app-filter-table [tableId]="tableIdDelete" field="rate" placeholder=""></app-filter-table>
                </th>
                <th [appFilter]="[tableIdDelete, 'state']">
                    <app-filter-table
                        [tableId]="tableIdDelete"
                        field="state"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'label',
                            filterLocal: true,
                            options: supplierStateOption,
                        }"
                        placeholder="trạng thái"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableIdDelete, 'note']">
                    <app-filter-table [tableId]="tableIdDelete" field="note" placeholder=""></app-filter-table>
                </th>
            </tr>
        </ng-template>

        <ng-template #templateSupplierType let-rowData>
            {{ mapSupplieType[rowData.supplierTypeId] }}
        </ng-template>
        <ng-template #templateProfileStatus let-rowData>
            <p-tag
                [value]="mapStateProfileSupplier[rowData.profileStatus]"
                [severity]="rowData.profileStatus === 0 ? 'secondary' : rowData.profileStatus === 1 ? 'success' : 'warning'"
            ></p-tag>
        </ng-template>
        <ng-template #templateStateSupplier let-rowData>
            <p-tag [value]="mapStateSupplier[rowData.state]" [severity]="getSeverity(rowData.state)"></p-tag>
        </ng-template>
    </app-table-multi>

    <app-popup-upload
        header="Nhập thông tin NCC"
        label="Danh sách nhà cung cấp"
        [types]="['excel']"
        (onUpload)="handleUploadImportCreate($event)"
        [urlError]="urlErrorCreate"
        service="/sc/api"
        [urlTemplate]="urlTemplateImportCreate"
        classButton=" hidden "
        [isVisible]="isVisibleList"
        (onClose)="isVisibleList = false"
    ></app-popup-upload>

    <app-popup-upload
        header="Nhập danh sách linh kiện cho NCC"
        description="Nhập danh sách linh kiện NCC có thể cung cấp nhưng chưa phát sinh giao dịch với Tech"
        [types]="['excel']"
        (onUpload)="handleUploadImportCreateItem($event)"
        [urlError]="urlErrorCreateItem"
        service="/sc/api"
        urlTemplate="supplier_item_template.xlsx"
        classButton=" hidden "
        [isVisible]="isVisibleAccessory"
        (onClose)="isVisibleAccessory = false"
    ></app-popup-upload>

    <app-popup-upload
        [header]="'Báo cáo chất lượng'"
        [types]="['excel']"
        [formItems]="itemsFormPopup"
        [formTemplate]="formTemplate"
        (onUpload)="handleUploadReportQuality($event)"
        [urlError]="urlErrorReport"
        service="/sc/api"
        severity="secondary"
        [urlTemplate]="urlTemplateReportQuality"
        classButton=" hidden "
        [isVisible]="isVisibleReportQuality"
        (onClose)="isVisibleReportQuality = false"
    >
        <ng-template #formTemplate let-formGroup>
            <app-form *ngIf="formGroup" [formGroup]="formGroup">
                <div class="tw-flex tw-flex-col tw-gap-4">
                    <app-form-item label="Năm" layout="horizontal">
                        <p-dropdown
                            [options]="yearRange"
                            optionLabel="label"
                            labelCol="tw-col-span-2"
                            wrapperCol="tw-col-span-10"
                            optionValue="value"
                            (onChange)="changeYearAndMonth($event, formGroup, 'year')"
                            formControlName="year"
                            appendTo="body"
                        ></p-dropdown>
                    </app-form-item>
                    <app-form-item label="Tháng" layout="horizontal">
                        <p-dropdown
                            [options]="monthRange"
                            labelCol="tw-col-span-2"
                            wrapperCol="tw-col-span-10"
                            optionLabel="label"
                            appendTo="body"
                            (onChange)="changeYearAndMonth($event, formGroup, 'month')"
                            formControlName="month"
                            optionValue="value"
                        ></p-dropdown>
                    </app-form-item>
                </div>
            </app-form>
        </ng-template>
    </app-popup-upload>

    <app-popup-upload
        header="Tiến độ giao hàng"
        description="Nhập danh sách những nhà cung cấp chậm tiến độ"
        [types]="['excel']"
        (onUpload)="handleUploadImportQualityDelivery($event)"
        [urlError]="urlErrorQualityDelivery"
        service="/sc/api"
        urlTemplate="template_quality_delivery.xlsx"
        classButton=" hidden "
        [isVisible]="isVisibleDelivery"
        (onClose)="isVisibleDelivery = false"
    ></app-popup-upload>
</div>

<p-overlayPanel #op>
    <div class="tw-flex tw-flex-col">
        <span
            (click)="isVisibleList = true"
            *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_supplier_import']"
            class="tw-cursor-pointer hover:tw-bg-blue-500 tw-px-2 tw-py-1 tw-rounded-md hover:tw-text-white"
            >Danh sách nhà cung cấp</span
        >
        <span (click)="isVisibleAccessory = true" class="tw-cursor-pointer hover:tw-bg-blue-500 tw-px-2 tw-py-1 tw-rounded-md hover:tw-text-white"
            >Linh kiện</span
        >
    </div>
</p-overlayPanel>

<p-overlayPanel #result>
    <div class="tw-flex tw-flex-col">
        <span
            (click)="isVisibleReportQuality = true"
            *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_supplier_import']"
            class="tw-cursor-pointer hover:tw-bg-blue-500 tw-p-1 tw-rounded-md hover:tw-text-white"
            >Đánh giá chất lượng</span
        >
        <span (click)="isVisibleDelivery = true" class="tw-cursor-pointer hover:tw-bg-blue-500 tw-p-1 tw-rounded-md hover:tw-text-white"
            >Tiến độ giao hàng</span
        >
    </div>
</p-overlayPanel>
