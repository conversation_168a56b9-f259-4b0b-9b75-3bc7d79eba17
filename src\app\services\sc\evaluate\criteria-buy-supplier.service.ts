import { Injectable } from '@angular/core';
import {HttpClient, HttpParams} from '@angular/common/http';
import { BaseService } from '../../base.service';
import {CriteriaBuySupplier, SupplierDocumentRes} from '../../../models/interface/sc';
import {ApiResponse, GeneralEntity} from "../../../models/interface";

@Injectable({
    providedIn: 'root',
})
export class CriteriaBuySupplierService extends BaseService<CriteriaBuySupplier> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/criteria-buy-supplier');
    }

    exportTemplateCriteria(supplierTypeId: number) {
        return this.http.get<GeneralEntity>(`/sc/api/criteria-buy-supplier/export-template-criteria?supplierTypeId=${supplierTypeId}`);
    }


    importCriteriaBuy(file: File, criteriaBuySupplierId: number) {
        const formData: FormData = new FormData();
        formData.append('file', file);
        formData.append('criteriaBuySupplierId', criteriaBuySupplierId.toString());

        return this.http.post<ApiResponse>('/sc/api/criteria-buy-supplier/import-criteria', formData);
    }

    createSimple(supplierIds: number[], criteriaBuyId: number) {
        const params = new HttpParams()
            .set('criteriaBuyId', criteriaBuyId.toString())
            .set('supplierIds', supplierIds.join(',')); // Chuyển mảng thành chuỗi

        return this.http.post<void>(
            `/sc/api/criteria-buy-supplier/create-simple?${params.toString()}`,
            null
        );
    }
}
