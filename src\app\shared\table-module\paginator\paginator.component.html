<div class="paginator-container">
    <div class="paginator-info tw-hidden md:tw-block">
        Hi<PERSON><PERSON> thị {{ (currentPage - 1) * size + 1 }} -
        {{ currentPage * size < totalCount ? currentPage * size : totalCount }}
        /
        {{ totalCount }}
    </div>
    <div class="paginator-controls">
        <div class="paginator-controls-item">
            <p-button
                icon="pi pi-angle-left"
                severity="secondary"
                [outlined]="true"
                size="small"
                class="bg-white tw-rounded-md"
                (click)="previousPage()"
            ></p-button>
            <span>{{ currentPage }}/{{ totalPages }}</span>
            <!-- Thay đổi ở đây -->
            <p-button
                icon="pi pi-angle-right"
                severity="secondary"
                [outlined]="true"
                size="small"
                class="bg-white tw-rounded-md"
                (click)="nextPage()"
            ></p-button>
        </div>
        <div class="paginator-goto-page paginator-controls-item">
            <label for="gotoPage">Đi đến</label>
            <p-inputNumber
                [(ngModel)]="newPage"
                inputId="gotoPage"
                mode="decimal"
                [useGrouping]="false"
                (onBlur)="goToPage(newPage)"
            ></p-inputNumber>

            <!-- [maxFractionDigits]="totalPages" -->

            <!-- [minFractionDigits]="0" -->
            <!-- Thêm sự kiện onBlur -->
        </div>
        <div class="paginator-items-per-page paginator-controls-item">
            <label class="hidden md:inline" for="itemsPerPage">Trên 1 trang</label>
            <p-dropdown
                id="choose-size"
                [options]="sizeOption"
                [(ngModel)]="size"
                placeholder="Select size"
                (onChange)="changePageSize(size)"
            ></p-dropdown>
            <!-- Thêm sự kiện onChange -->
        </div>
        <div class="paginator-reload bg-white" style="border-radius: 4px">
            <p-button
                severity="secondary"
                [outlined]="true"
                size="small"
                class="bg-white tw-rounded-md"
                icon="pi pi-refresh"
                (onClick)="refetch()"
            ></p-button>
        </div>
    </div>
</div>
