// <PERSON><PERSON><PERSON>ng cách gi<PERSON>a các panel
.section-wrapper {
    margin-bottom: 20px;
}

// Container chia 2 cột cho RDBOM
.rdbom-wrapper {
    display: grid;
    grid-template-columns: minmax(0, 8fr) minmax(0, 1fr); /* Tỉ lệ 2:1 */
    gap: 16px;
    align-items: start;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
}

// Cột trái: bảng chiếm ~2/3
.rdbom-left {
    overflow-x: auto; /* Cho phép scroll ngang nếu cần */
    max-width: 100%;
    min-width: 0;
}

// Cột phải: chiếm ~1/3
.rdbom-right {
    padding: 1rem;
    position: sticky;
    top: 0;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: flex-end;
}
@media (max-width: 768px) {
    .rdbom-wrapper {
        grid-template-columns: 1fr;
    }
    
    .rdbom-right {
        position: static;
        align-items: flex-start;
    }
}

// Bên trong cột phải, căn gi<PERSON>a dọc
.rdbom-actions {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
}
