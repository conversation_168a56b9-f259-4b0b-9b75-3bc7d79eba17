import { Component, EventEmitter, inject, Input, OnInit, Output } from '@angular/core';
import { DialogModule } from 'primeng/dialog';
import { TableModule } from 'primeng/table';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { PtmSharedService } from '../../../../../services/ptm/ptm-shared.service';

@Component({
    selector: 'app-track-changes',
    templateUrl: './track-changes.component.html',
    styleUrls: ['./track-changes.component.scss'],
    standalone: true,
    imports: [DialogModule, TableModule, CommonModule, ButtonModule],
})
export class TrackChangesComponent implements OnInit {
    @Input() visible = false;
    @Input() recordId!: number;
    @Input() idInstruction!: number;
    @Output() closed = new EventEmitter<void>();

    ptmSharedService = inject(PtmSharedService);

    trackChanges: {
        action: string;
        detail: string;
        user: string;
        time: string;
    }[] = [];

    ngOnInit(): void {
        console.log(this.recordId);

        this.getDataTrackChanges();
    }

    getDataTrackChanges() {
        const param = {
            tab: this.recordId,
        };
        this.ptmSharedService.getTrackChanges(this.idInstruction, param).subscribe({
            next: (res) => {
                if (res) {
                    this.trackChanges = res;
                } else {
                    this.trackChanges = [];
                }
            },
            error: (err) => {
                console.log(err);
            },
        });
    }

    getActionLabel(action: number): string {
        switch (action) {
            case 0:
            case 3:
                return 'Thêm mới';
            case 1:
            case 2:
                return 'Chỉnh sửa';
            case 5:
            case 6:
                return 'Phê duyệt';
            default:
                return 'Không';
        }
    }

    handleClose(): void {
        this.visible = false;
        this.closed.emit();
    }
}
