:host ::ng-deep {
    // 1. Fixed‐width inputs
    .p-multiselect,
    .p-dropdown,
    .p-password {
        width: 100%;
    }

    // 2. Custom tag styling
    .p-tag-cutom .p-tag {
        background-color: var(--surface-100);
        color: #1c1a1a;
        border: 1px solid #bbaaab;
        margin-left: 0.5rem;
    }

    // 3. Disabled input full-opacity
    .p-inputtext[disabled] {
        opacity: 1;
        color: #333;
    }

    // 4. InputSwitch common slider styles
    .p-inputswitch {
        &-slider {
            background: #d4d0d0;
            border: 1px solid currentColor;
        }
        &.p-inputswitch-checked .p-inputswitch-slider {
            background: #00b87b;
        }
    }

    // 5. State‐specific colors
    .success {
        color: #00b87b;
    }
    .danger {
        color: #f4516c;
    }
    .warning {
        color: #fba65d;
    }
}

.filter-container {
    display: flex;
    align-items: center;
    background: #f5f7fa;
    padding: 1rem;
    border-radius: 4px;
}

.filter-item {
    & label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }
    &.search {
        justify-self: end;
    }
}
