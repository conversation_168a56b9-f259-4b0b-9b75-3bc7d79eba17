<app-sub-header
    [items]="[{ label: 'Quản lý yêu cầu vận chuyển', url: '/sc/request-shipping' }, { label: oldbo ? oldbo.code : 'Tạo mới' }]"
    [action]="action"
></app-sub-header>

<ng-template #action>
    <p-button label="Lưu" (click)="form.handleSubmit()" severity="success" size="small" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_bo_edit']" />
    <p-button label="Đóng" routerLink="/sc/request-shipping" severity="secondary" size="small" />
</ng-template>

<div class="tw-p-4" *ngIf="formGroup">
    <app-form #form [formGroup]="formGroup" layout="vertical" (onSubmit)="onSubmit($event)">
        <p-panel header="Thông tin booking" [toggleable]="true">
            <div class="tw-grid lg:tw-grid-cols-2 tw-grid-cols-1 tw-gap-4">
                <app-form-item label="Mã BO">
                    <input type="text" class="tw-w-full" pInputText formControlName="code" placeholder="Hệ thống tự sinh" />
                </app-form-item>

                <app-form-item label="Tổng giá trị shipment dự kiến (USD)">
                    <app-inputNumber class="tw-w-full" formControlName="shipmentValue" mode="decimal" maxlength="20"></app-inputNumber>
                </app-form-item>
                <app-form-item label="Số hợp đồng/đơn hàng (PO)">
                    <app-combobox
                        type="select"
                        fieldValue="id"
                        fieldLabel="orderNo"
                        url="/sc/api/po/search"
                        formControlName="poIds"
                        [body]="{
                            state: poStateNotDraft,
                        }"
                        (onChange)="handleChangePo($event)"
                    ></app-combobox>
                </app-form-item>
                <app-form-item label="Điều kiện thanh toán" class="tw-row-span-2">
                    <textarea rows="6" pInputTextarea class="tw-w-full" formControlName="paymentCondition"></textarea>
                </app-form-item>
                <app-form-item label="Phân loại">
                    <app-filter-table
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'label',
                            options: TYPE_SHIPPING,
                            filterLocal: true,
                        }"
                        formControlName="type"
                    ></app-filter-table>
                </app-form-item>

                <app-form-item label="Tên hàng">
                    <input type="text" class="tw-w-full" pInputText formControlName="goodsName" />
                </app-form-item>

                <app-form-item label="Tên NCC">
                    <input type="text" class="tw-w-full" pInputText formControlName="supplierName" />
                </app-form-item>

                <app-form-item label="Số PO">
                    <input type="text" class="tw-w-full" pInputText formControlName="poNumber" />
                </app-form-item>

                <app-form-item label="Địa chỉ NCC">
                    <input type="text" class="tw-w-full" pInputText formControlName="supplierAddress" />
                </app-form-item>

                <app-form-item label="Số thứ tự shipment của PO">
                    <app-inputNumber class="tw-w-full" formControlName="indexShipment" [maxLength]="9"></app-inputNumber>
                </app-form-item>

                <app-form-item label="Thông tin NCC" class="tw-row-span-2">
                    <textarea rows="6" pInputTextarea class="tw-w-full" formControlName="supplierInfo"></textarea>
                </app-form-item>

                <app-form-item label="Mã kế toán/Mã vụ việc">
                    <input type="text" class="tw-w-full" pInputText formControlName="accountingCode" />
                </app-form-item>

                <app-form-item label="Khối lượng tổng của lô hàng (kg)">
                    <app-inputNumber class="tw-w-full" formControlName="totalWeight" mode="decimal" [maxLength]="20"></app-inputNumber>
                </app-form-item>

                <app-form-item label="Thời gian hàng hóa ready tại kho">
                    <p-calendar
                        [showButtonBar]="true"
                        placeholder="dd/MM/yyyy"
                        [showIcon]="true"
                        dateFormat="dd/mm/yy"
                        class="tw-w-full"
                        appendTo="body"
                        [formControl]="formGroup.get('readyDateCustom')"
                    ></p-calendar>
                </app-form-item>

                <div class="tw-grid tw-grid-flow-col tw-gap-4">
                    <app-form-item label="Số kiện của lô hàng (carton/pallet)">
                        <app-inputNumber class="tw-w-full" formControlName="packageNumber" mode="decimal" [maxLength]="20"></app-inputNumber>
                    </app-form-item>
                    <app-form-item label="&nbsp;" class="tw-col-span-2">
                        <app-autocomplete
                            optionLabel="name"
                            optionValue="id"
                            formControlName="unit"
                            [options]="packageTypes"
                            (onAdd)="handleAddPackagegType($event)"
                            (onDelete)="handleDeletePackagegType($event)"
                            [rowDelete]="rowDelete"
                        ></app-autocomplete>
                    </app-form-item>
                </div>

                <app-form-item label="Thời gian yêu cầu về tới nhà máy">
                    <p-calendar
                        [showButtonBar]="true"
                        placeholder="dd/MM/yyyy"
                        [showIcon]="true"
                        dateFormat="dd/mm/yy"
                        appendTo="body"
                        [formControl]="formGroup.get('requiredArrivedDateCustom')"
                        class="tw-w-full"
                    ></p-calendar>
                </app-form-item>

                <app-form-item label="Điều kiện giao hàng">
                    <input type="text" class="tw-w-full" pInputText formControlName="deliveryCondition" />
                </app-form-item>

                <app-form-item label="Phòng/Ban">
                    <app-filter-table
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            options: optionsDepartment,
                        }"
                        formControlName="departmentId"
                    ></app-filter-table>
                </app-form-item>

                <app-form-item label="Thông tin đính kèm">
                    <app-button-group-file
                        simpleUpload=""
                        (onFileSelected)="handleUploadFile($event, 'bo')"
                        [attachments]="formGroup.getRawValue().attachments"
                        formControlName="attachmentIds"
                        [multiple]="true"
                    ></app-button-group-file>
                    <app-button-group-file
                        *ngIf="formGroup.getRawValue().attachments && formGroup.getRawValue().attachments.length > 0"
                        class="tw-col-span-2"
                        (onFileSelected)="handleUploadFile($event, 'bo')"
                        [multiple]="true"
                        simpleUpload=""
                        formControlName="attachmentIds"
                    ></app-button-group-file>
                </app-form-item>
                <app-form-item class="tw-col-span-2" label="Người nhận hàng (Consignee)" *ngIf="formGroup?.get('type').value === 1">
                    <textarea rows="5" cols="30" pInputTextarea class="tw-w-full" formControlName="consignee"></textarea>
                </app-form-item>

                <app-form-item class="tw-col-span-2" label="Địa chỉ nhận hàng cuối cùng" *ngIf="formGroup?.get('type').value === 0">
                    <textarea rows="5" cols="30" pInputTextarea class="tw-w-full" formControlName="finalDeliveryAddress"></textarea>
                </app-form-item>
                <app-form-item label="Ghi chú" class="tw-col-span-2">
                    <textarea rows="5" cols="30" pInputTextarea class="tw-w-full" formControlName="note"></textarea>
                </app-form-item>
            </div>
        </p-panel>

        <br />
        <app-form-item label="">
            <div formArrayName="boItems">
                <p-panel header="Thông tin vật tư">
                    <ng-template pTemplate="icons">
                        <app-button-group-file
                            [urlError]="urlErrorBoItems"
                            (onFileSelected)="handleUploadFile($event, 'boItems')"
                            (onClearFile)="handleClearFile('boItems')"
                            [attachment]="formGroup.getRawValue().attachmentItem"
                            [types]="['excel']"
                            errorWrongFileMessage="File import sai định dạng, vui lòng thử lại với file excel"
                            (onClickDowload)="handleDownloadTemplate()"
                        ></app-button-group-file>
                    </ng-template>
                    <p-table [value]="boItems.controls" styleClass="p-datatable-gridlines  " [scrollable]="true" scrollHeight="400px">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="width: 5rem" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_bo_edit']"></th>
                                <th style="min-width: 150px">PO <span class="tw-text-red-500">*</span></th>
                                <th style="min-width: 250px">VNPT MAN PN <span class="tw-text-red-500">*</span></th>
                                <th class="tw-whitespace-nowrap">Mã NSX</th>
                                <th class="tw-whitespace-nowrap">Mô tả</th>
                                <th class="tw-whitespace-nowrap">Đơn giá <span class="tw-text-red-500">*</span></th>
                                <th class="tw-whitespace-nowrap">Tổng số lượng <span class="tw-text-red-500">*</span></th>
                                <th class="tw-whitespace-nowrap">FOC</th>
                                <th class="tw-whitespace-nowrap">Ghi chú</th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                            <tr *ngIf="item.value.isEdit" [formGroupName]="rowIndex">
                                <td *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_bo_edit']">
                                    <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                        <button
                                            type="button"
                                            class="p-link tw-p-2 bg-green-300 hover:tw-bg-green-400 tw-text-white"
                                            (click)="saveBoItem(rowIndex)"
                                            pTooltip="Lưu"
                                            tooltipPosition="top"
                                        >
                                            <span class="pi pi-save"></span>
                                        </button>
                                        <button
                                            type="button"
                                            class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                            (click)="cancelCreate(rowIndex)"
                                            pTooltip="Hủy"
                                            tooltipPosition="top"
                                        >
                                            <span class="pi pi-times"></span>
                                        </button>
                                    </div>
                                </td>
                                <td>
                                    <app-form-item label="">
                                        <app-filter-table
                                            type="select-one"
                                            [configSelect]="{
                                                fieldValue: 'id',
                                                fieldLabel: 'orderNo',
                                                options: poOptions,
                                                filterLocal: true,
                                            }"
                                            (onChange)="handleSelectPoForBoItem($event, item)"
                                            formControlName="poId"
                                        ></app-filter-table>
                                    </app-form-item>
                                </td>
                                <td>
                                    <div *ngIf="item.getRawValue()?.isSaved">
                                        <app-form-item label="">
                                            <input type="text" class="tw-w-full" pInputText formControlName="internalReference" placeholder="VNPT MAN PN" />
                                        </app-form-item>
                                    </div>
                                    <div *ngIf="!item.getRawValue()?.isSaved">
                                        <app-form-item label="">
                                            <app-filter-table
                                                type="select-one"
                                                [configSelect]="{
                                                    fieldValue: 'internalReference',
                                                    fieldLabel: 'display',
                                                    options: getOptionsForInternalReference(item),
                                                    filterLocal: true,
                                                }"
                                                (onChange)="handleChangeInternalReference($event, item)"
                                                formControlName="internalReference"
                                            ></app-filter-table>
                                        </app-form-item>
                                    </div>
                                </td>
                                <td>
                                    <!--<app-form-item label="">
                                    <app-filter-table
                                        type="select-one"
                                        [configSelect]="{
                                            fieldValue: 'manufacturerPn',
                                            fieldLabel: 'manufacturerPn',
                                            rsql: true,
                                            url: '/sc/api/product-manpn/search',
                                            paramForm: 'manufacturerPn',
                                        }"
                                        formControlName="manPn"
                                        (onChange)="handleChangeManufacture($event, rowIndex)"
                                    ></app-filter-table>
                                </app-form-item>-->
                                    <app-form-item label="">
                                        <input type="text" class="tw-w-full" pInputText formControlName="manPn" placeholder="Mã NSX" />
                                    </app-form-item>
                                </td>
                                <td>
                                    <app-form-item label="">
                                        <input type="text" class="tw-w-full" pInputText formControlName="productDescription" placeholder="Mô tả" />
                                    </app-form-item>
                                </td>
                                <td>
                                    <div class="tw-grid tw-grid-flow-col tw-gap-4">
                                        <app-form-item label="" class="tw-col-span-10">
                                            <app-inputNumber class="tw-w-full" formControlName="price" mode="decimal" placeholder="giá thành" maxlength="20" />
                                        </app-form-item>
                                        <app-form-item label="" class="tw-col-span-2">
                                            <p-dropdown
                                                [options]="UnitPriceArr"
                                                optionLabel="label"
                                                optionValue="value"
                                                appendTo="body"
                                                formControlName="unit"
                                                placeholder="tiền tệ"
                                                class="tw-w-full"
                                            />
                                        </app-form-item>
                                    </div>
                                </td>
                                <td>
                                    <app-form-item label="">
                                        <app-inputNumber class="tw-w-full" formControlName="quantity" placeholder="Số lượng" />
                                    </app-form-item>
                                </td>
                                <td>
                                    <app-form-item label="">
                                        <app-inputNumber class="tw-w-full" formControlName="foc" placeholder="Số lượng FOC" />
                                    </app-form-item>
                                </td>
                                <td>
                                    <app-form-item label="">
                                        <input type="text" class="tw-w-full" pInputText formControlName="note" placeholder="Ghi chú" />
                                    </app-form-item>
                                </td>
                            </tr>
                            <tr *ngIf="!item.value.isEdit">
                                <td *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_bo_edit']">
                                    <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                        <button
                                            type="button"
                                            class="p-link tw-p-2 bg-green-300 hover:tw-bg-green-400 tw-text-white"
                                            (click)="editBoItem(rowIndex)"
                                            pTooltip="Sửa"
                                            tooltipPosition="top"
                                            *ngIf="!isAddingBoItem && !isEditingBoItem"
                                        >
                                            <span class="pi pi-pencil"></span>
                                        </button>
                                        <button
                                            type="button"
                                            class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                            (click)="removeBoItem(rowIndex)"
                                            pTooltip="Xóa"
                                            tooltipPosition="top"
                                            *ngIf="!isAddingBoItem && !isEditingBoItem"
                                        >
                                            <span class="pi pi-trash"></span>
                                        </button>
                                    </div>
                                </td>
                                <td>{{ item.getRawValue()?.orderNo }}</td>
                                <td>{{ item.getRawValue()?.internalReference }}</td>
                                <td class="tw-whitespace-nowrap">{{ item.getRawValue()?.manPn }}</td>
                                <td class="tw-whitespace-nowrap">{{ item.getRawValue()?.productDescription }}</td>
                                <td class="tw-whitespace-nowrap">
                                    {{ item.getRawValue()?.price | currency: (item.getRawValue()?.unit === 0 ? 'USD' : 'VND') : 'symbol' : '1.0-8' }}
                                    {{ MAP_TYPE_MONEY[item.getRawValue()?.unit] }}
                                </td>
                                <td class="tw-whitespace-nowrap">{{ item.getRawValue()?.quantity | number }}</td>
                                <td class="tw-whitespace-nowrap">{{ item.getRawValue()?.foc | number }}</td>
                                <td class="tw-whitespace-nowrap">{{ item.getRawValue()?.note }}</td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <div class="tw-mt-4">
                        <p-button
                            size="small"
                            [disabled]="isEditingBoItem || isAddingBoItem"
                            label="Thêm mới"
                            (onClick)="addBoItem()"
                            *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_bo_edit']"
                        ></p-button>
                    </div>
                </p-panel>
            </div>
        </app-form-item>
    </app-form>
</div>

<p-confirmDialog key="app-confirm-pass" [style]="{ width: '350px' }" #acp>
    <ng-template pTemplate="footer">
        <p-button type="button" [raised]="true" label="Thông qua" (click)="acp.accept()"></p-button>
        <p-button type="button" [text]="true" [raised]="true" label="Hủy bỏ" (click)="acp.reject()"></p-button>
    </ng-template>
</p-confirmDialog>
