<app-sub-header [items]="[{ label: 'Qu<PERSON>n lý yêu cầu vận chuyển' }, { label: '<PERSON>h sách' }]" [action]="actionHeader"></app-sub-header>

<ng-template #actionHeader>
    <p-button routerLink="create" label="Tạo mới" severity="primary" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_bo_edit']" />
    <app-popup
        header="Xuất danh sách YCVC"
        severity="success"
        label="Xuất Excel"
        (onSubmit)="exportRequestShipping($event)"
        typePopup="download"
    ></app-popup>
</ng-template>
<div style="padding: 1rem 1rem 0 1rem">
    <app-table-common
        [tableId]="tableId"
        [columns]="columns"
        [data]="state.data"
        [loading]="state.isFetching"
        [filterTemplate]="filterTemplate"
        name="<PERSON><PERSON> sách yêu cầu vận chuyển"
        [funcDelete]="null"
        [selectionMode]="null"
        [stt]="true"
        [authoritiesDelete]="['ROLE_SYSTEM_ADMIN', 'sc_shipping_delete']"
    >
        <ng-template #filterTemplate>
            <tr>
                <th></th>
                <th></th>
                <th [appFilter]="[tableId, 'boCode']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="boIds"
                        type="select"
                        [rsql]="false"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'code',
                            rsql: true,
                            param: 'code',
                            paramForm: 'id',
                            url: '/sc/api/bo/search',
                            body: {
                                typeModule: 1,
                            },
                        }"
                        placeholder="Số BO"
                    ></app-filter-table>
                </th>
                <!--<th [appFilter]="[tableId, 'boCode']">
                    <app-filter-table [tableId]="tableId" field="boCode" placeholder="Số BO"></app-filter-table>
                </th>-->
                <th [appFilter]="[tableId, 'poNumber']">
                    <app-filter-table [tableId]="tableId" [rsql]="false" field="poNumber" placeholder="Số PO"></app-filter-table>
                </th>

                <!--<th [appFilter]="[tableId, 'poNumber']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="poNumber"
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'poNumber',
                            fieldLabel: 'poNumber',
                            rsql: true,
                            param: 'poNumber',
                            paramForm: 'poNumber',
                            url: '/sc/api/bo/search',
                        }"
                        [removeNullLabel]="true"
                        placeholder="số PO"
                    ></app-filter-table>
                </th>-->

                <th [appFilter]="[tableId, 'indexShipment']">
                    <app-filter-table [rsql]="false" type="number" [tableId]="tableId" field="indexShipment" placeholder="STT shipment Po"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'accountingCode']">
                    <app-filter-table [rsql]="false" [tableId]="tableId" field="accountingCode" placeholder="mã kế toán"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'readyDate']">
                    <app-filter-table
                        [rsql]="false"
                        [tableId]="tableId"
                        field="startReadyDate&endReadyDate"
                        type="date-range"
                        placeholder="Thời gian hàng hóa ready"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'requiredArrivedDate']">
                    <app-filter-table
                        [rsql]="false"
                        [tableId]="tableId"
                        field="startRequiredArrived&endRequiredArrived"
                        type="date-range"
                        placeholder="Thời gian yêu cầu hàng về"
                    ></app-filter-table>
                </th>

                <th [appFilter]="[tableId, 'totalWeight']">
                    <app-filter-table [rsql]="false" type="number" [tableId]="tableId" field="totalWeight" placeholder="khối lượng"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'packageNumber']">
                    <app-filter-table [rsql]="false" type="number" [tableId]="tableId" field="packageNumber" placeholder="số kiện"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'shipmentValue']">
                    <app-filter-table [rsql]="false" type="number" [tableId]="tableId" field="shipmentValue" placeholder="shipment"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'completeDate']">
                    <app-filter-table
                        [rsql]="false"
                        [tableId]="tableId"
                        field="startCompleteDate&endCompleteDate"
                        type="date-range"
                        placeholder="ngày hoàn thành"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'dateArrive']">
                    <app-filter-table
                        [rsql]="false"
                        [tableId]="tableId"
                        field="startArrivedDate&endArrivedDate"
                        type="date-range"
                        placeholder="thời gian dự kiến về kho"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'note']">
                    <app-filter-table [rsql]="false" [tableId]="tableId" field="note" placeholder="ghi chú"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'status']">
                    <app-filter-table
                        [rsql]="false"
                        [tableId]="tableId"
                        field="status"
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'label',
                            options: boStatus,
                        }"
                        placeholder="trạng thái"
                    ></app-filter-table>
                </th>
            </tr>
        </ng-template>

        <ng-template #templateAction let-rowData>
            <div class="flex gap-2">
                <i
                    *ngIf="rowData?.status === BO_STATUS_CONSTANT.WAITING"
                    class="pi pi-pencil tw-text-xl tw-text-blue-500 tw-p-1 tw-cursor-pointer"
                    title="Sửa thông tin Booking"
                    (click)="onEdit(rowData)"
                ></i>
                <i
                    *ngIf="rowData?.status === BO_STATUS_CONSTANT.WAITING"
                    class="pi pi-reply tw-text-xl tw-text-green-500 tw-p-1 tw-cursor-pointer"
                    title="Chuyển tới bộ phận Logistics"
                    (click)="onForwardLogistic(rowData)"
                ></i>
                <i
                    *ngIf="rowData?.status === BO_STATUS_CONSTANT.WAITING"
                    class="pi pi-trash tw-text-xl tw-text-red-500 tw-p-1 tw-cursor-pointer"
                    title="Xóa"
                    (click)="onDelete(rowData)"
                ></i>
                <i
                    *ngIf="rowData?.status === BO_STATUS_CONSTANT.NEW"
                    class="pi pi-undo tw-text-xl tw-text-yellow-500 tw-p-1 tw-cursor-pointer"
                    title="Thu hồi Yêu cầu vận chuyển"
                    (click)="onUndo(rowData)"
                ></i>
            </div>
        </ng-template>

        <ng-template #templateStatus let-rowData>
            <p-tag [style]="{ width: '100%' }" [severity]="getSeverityStatus(rowData.status)" [value]="getStatusLabel(rowData.status)"></p-tag>
        </ng-template>

        <ng-template #templateCode let-rowData>
            <ng-container *ngIf="rowData.status !== -1; else plainText">
                <a [href]="'sc/bo/' + rowData.id" target="_blank">{{ rowData.boCode }}</a>
            </ng-container>
            <ng-template #plainText>
                {{ rowData.boCode }}
            </ng-template>
        </ng-template>
    </app-table-common>
</div>

<p-dialog
    header="Thông báo cho người tiếp nhận"
    [(visible)]="isOpenModalNotification"
    [style]="{ width: '80vw' }"
    [breakpoints]="{ '1199px': '80vw', '575px': '100vw' }"
    (onHide)="formNotification.reset()"
>
    <hr style="margin: 0" />
    <br />
    <app-form #formSubmit *ngIf="isOpenModalNotification" [formGroup]="formNotification" layout="vertical" (onSubmit)="sendNotification($event)">
        <app-form-item label="Nội dung">
            <textarea rows="5" formControlName="content" pInputTextarea class="tw-w-full"></textarea>
        </app-form-item>
        <app-form-item label="Người tiếp nhận" [isRequired]="true">
            <app-filter-table
                type="select"
                [configSelect]="{
                    fieldValue: 'id',
                    fieldLabel: 'email',
                    options: receivers,
                    filterLocal: true,
                }"
                (onChange)="handleChangeReceivers($event)"
            ></app-filter-table>
        </app-form-item>
    </app-form>
    <ng-template pTemplate="footer">
        <div>
            <p-button
                type="button"
                severity="primary"
                size="small"
                (click)="formSubmit.handleSubmit()"
                label="Xác nhận gửi"
                [disabled]="formNotification.invalid"
                *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_lot_edit']"
            ></p-button>
            <p-button
                type="button"
                label="Hủy"
                [text]="true"
                [raised]="true"
                size="small"
                severity="secondary"
                (click)="isOpenModalNotification = false; formNotification.reset()"
            ></p-button>
        </div>
    </ng-template>
</p-dialog>
