import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import {BaseDepartment, InternationalRateUnit} from '../../../models/interface/sc';

@Injectable()
export class InternationalRateUnitService extends BaseService<InternationalRateUnit> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/international-rate-unit');
    }
}
