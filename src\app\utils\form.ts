import { QueryList } from '@angular/core';
import { FormComponent } from '../shared/form-module/form-base/form.component';
import { FormControl } from '@angular/forms';

export function submitSpecificForm(forms: QueryList<FormComponent>, formId: string): void {
    const formToSubmit = forms.find((form) => form.formId === formId);
    if (formToSubmit) {
        formToSubmit.handleSubmit();
    }
}

export class TrimmedFormControl extends FormControl {} // method no overRide -> để nhận diện lo<PERSON>i control
