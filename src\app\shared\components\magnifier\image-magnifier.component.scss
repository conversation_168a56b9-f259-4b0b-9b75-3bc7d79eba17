.img-magnifier-container {
    position: relative;
    width: 100%;
    min-height: 300px; /* <PERSON><PERSON><PERSON><PERSON> lập chiều cao tối thiểu */
    display: flex;
    justify-content: center;
    align-items: center;
}

:host ::ng-deep .p-image > img {
    width: 100%;
}
:host ::ng-deep .p-dialog {
    height: 100%;
    max-height: 100%;
    width: 100%;
    background-color: transparent;
}

:host ::ng-deep .p-dialog-content {
    padding: 1rem 0;
    background-color: transparent;
    margin: 0 auto;
}

:host ::ng-deep .ngxImageZoomContainer {
    width: auto !important;
    height: calc(100vh - 2rem) !important;
}

:host ::ng-deep .ngxImageZoomThumbnail {
    width: auto !important;
    height: 100% !important;
    object-fit: contain;
}
.loading-spinner {
    position: absolute;
    font-size: 1.5em;
    background-color: #a8a8a8;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.absolute-right-top {
    position: absolute;
    right: 0;
    top: 0;
    transform: translate(50%, -50%);
}

.absolute-left-bottom {
    position: absolute;
    left: 0;
    top: 0;
}
