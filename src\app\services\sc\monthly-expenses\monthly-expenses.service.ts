import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import {Logistics, MonthlyExpenses} from '../../../models/interface/sc';
import {ApiResponse} from "../../../models/interface";

@Injectable()
export class MonthlyExpensesService extends BaseService<MonthlyExpenses> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/monthly-expenses');
    }

    importFile(file: File, date: number, type: number, logisticId: number) {
        const formData: FormData = new FormData();
        formData.append('file', file);
        formData.append('date', date.toString());
        formData.append('type', type.toString());
        formData.append('logisticId', logisticId.toString());
        return this.http.post<ApiResponse>('/sc/api/monthly-expenses/import', formData);
    }
}
