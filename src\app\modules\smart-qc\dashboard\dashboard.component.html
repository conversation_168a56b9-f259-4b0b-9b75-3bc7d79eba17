<app-sub-header [items]="itemsHeader" [action]="actionHeader"></app-sub-header>
<ng-template #actionHeader> </ng-template>
<div class="tw-p-5">
    <form [formGroup]="taskProcessForm" class="tw-bg-white tw-p-5 tw-rounded-xl tw-mb-5">
        <div class="tw-grid lg:tw-grid-cols-3 md:tw-grid-cols-1 sm:tw-grid-cols-1 tw-gap-4 tw-mb-3">
            <div class="tw-flex tw-flex-col tw-space-y-3">
                <div class="tw-font-bold">Dự án <span class="tw-text-red-600">(*)</span></div>
                <div *ngIf="authService.isAdminOrPM()">
                    <app-filter-table
                        [selectFirstValue]="true"
                        (onApiCallback)="onSelectFirstContractDone($event)"
                        field="name"
                        rsql="true"
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            param: 'name',
                            rsql: false,
                            body: {},
                            url: '/smart-qc/api/combobox/contract-pm',
                        }"
                        (onChange)="selectContract($event)"
                        [initValue]="taskProcessForm.get('contractId').value"
                        placeholder="Tên dự án"
                        #filerArea
                    ></app-filter-table>
                </div>
                <div *ngIf="authService.isSubPM()">
                    <app-filter-table
                        [selectFirstValue]="true"
                        (onApiCallback)="onSelectFirstContractDone($event)"
                        field="name"
                        rsql="true"
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            param: 'name',
                            rsql: false,
                            body: {},
                            url: '/smart-qc/api/combobox/contract-sub-pm',
                        }"
                        (onChange)="selectContract($event)"
                        [initValue]="taskProcessForm.get('contractId').value"
                        placeholder="Tên dự án"
                        #filerArea
                    ></app-filter-table>
                </div>
                <div *ngIf="authService.isCustomer()">
                    <app-filter-table
                        [selectFirstValue]="true"
                        (onApiCallback)="onSelectFirstContractDone($event)"
                        field="name"
                        rsql="true"
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            param: 'name',
                            rsql: false,
                            url: '/smart-qc/api/combobox/contract-customer',
                        }"
                        (onChange)="selectContract($event)"
                        [initValue]="taskProcessForm.get('contractId').value"
                        placeholder="Tên dự án"
                        #filerArea
                    ></app-filter-table>
                </div>
            </div>
            <div class="tw-flex tw-flex-col tw-space-y-3">
                <div class="tw-font-bold">Công việc <span class="tw-text-red-600">(*)</span></div>
                <div>
                    <app-filter-table
                        [disabled]="!taskProcessForm.get('contractId').value"
                        [selectFirstValue]="true"
                        (onApiCallback)="onselectFirstActionDone($event)"
                        field="name"
                        rsql="true"
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            param: 'name',
                            rsql: true,
                            body: {
                                contractId: taskProcessForm.get('contractId').value,
                            },
                            url: '/smart-qc/api/action/search',
                        }"
                        [initValue]="taskProcessForm.get('actionId').value"
                        (onChange)="selectAction($event)"
                        placeholder="Công việc"
                        #filerArea
                    ></app-filter-table>
                </div>
            </div>
            <div *ngIf="authService.isAdminOrPM() || authService.isCustomer()" class="tw-flex tw-flex-col tw-space-y-3">
                <div class="tw-font-bold">Sub PM</div>
                <div>
                    <app-filter-table
                        [disabled]="!taskProcessForm.get('actionId').value"
                        field="name"
                        rsql="true"
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            param: 'name',
                            rsql: false,
                            body: {
                                actionId: taskProcessForm.get('actionId').value,
                            },
                            url: '/smart-qc/api/combobox/sub-pm',
                        }"
                        [initValue]="taskProcessForm.get('subPmId').value"
                        (onChange)="selectSubPm($event)"
                        placeholder="Sub PM"
                        #filerArea
                    ></app-filter-table>
                </div>
            </div>
        </div>
    </form>

    <div class="tw-grid lg:tw-grid-cols-3 md:tw-grid-cols-1 sm:tw-grid-cols-1 tw-gap-4 tw-mb-5">
        <div *ngIf="isLoadingTaskProcess" class="tw-flex tw-items-center tw-justify-center tw-rounded-xl" style="background-color: #08549e">
            <p-progressSpinner styleClass="w-4rem h-4rem" strokeWidth="4" animationDuration=".5s" />
        </div>
        <div *ngIf="!isLoadingTaskProcess" class="tw-text-white tw-rounded-xl tw-flex tw-flex-col tw-justify-between" style="background-color: #08549e">
            <div class="tw-p-5 tw-flex tw-justify-between">
                <div class="tw-flex tw-flex-col tw-justify-between">
                    <div class="tw-text-4xl tw-font-bold">{{ taskProcessData?.totalQuantity }}</div>
                    <div>Tổng số trạm</div>
                </div>
                <!--<div class="pi pi-user-plus icon-card"></div>-->
                <!--                <img class="icon-card" src="assets/images/dashboard/construction.png" alt="construction" />-->
                <img class="icon-card" src="../../../assets/images/dashboard/antena_total.png" alt="construction" />
            </div>
            <!--<div
                (click)="navigateContract()"
                class="tw-flex tw-justify-center tw-items-center tw-space-x-1 tw-p-2 view-detail-btn"
            >
                <div>Xem chi tiết</div>
                <div class="pi pi-arrow-circle-right"></div>
            </div>-->
        </div>
        <div *ngIf="isLoadingTaskProcess" class="tw-flex tw-items-center tw-justify-center tw-rounded-xl" style="background-color: #00b87b">
            <p-progressSpinner styleClass="w-4rem h-4rem" strokeWidth="4" animationDuration=".5s" />
        </div>
        <div *ngIf="!isLoadingTaskProcess" class="tw-text-white tw-rounded-xl tw-flex tw-flex-col tw-justify-between" style="background-color: #00b87b">
            <div class="tw-p-5 tw-flex tw-justify-between">
                <div class="tw-flex tw-flex-col tw-justify-between">
                    <div class="tw-text-4xl tw-font-bold">{{ taskProcessData?.completeQuantity }}</div>
                    <div>Tổng số trạm hoàn thành</div>
                </div>
                <img class="icon-card" src="../../../assets/images/dashboard/antenna.png" alt="construction" />
            </div>
            <!--<div
                (click)="navigateApprovePage()"
                class="tw-flex tw-justify-center tw-items-center tw-space-x-1 tw-p-2 view-detail-btn"
            >
                <div>Xem chi tiết</div>
                <div class="pi pi-arrow-circle-right"></div>
            </div>-->
        </div>
        <div *ngIf="isLoadingTaskProcess" class="tw-flex tw-items-center tw-justify-center tw-rounded-xl" style="background-color: #ffc94a">
            <p-progressSpinner styleClass="w-4rem h-4rem" strokeWidth="4" animationDuration=".5s" />
        </div>
        <div *ngIf="!isLoadingTaskProcess" class="tw-text-white tw-rounded-xl tw-flex tw-flex-col tw-justify-between" style="background-color: #ffc94a">
            <div class="tw-p-5 tw-flex tw-justify-between">
                <div class="tw-flex tw-flex-col tw-justify-between">
                    <div class="tw-text-4xl tw-font-bold">{{ taskProcessData?.completeRate }}%</div>
                    <div>Tiến độ tổng thể</div>
                </div>
                <img class="icon-card" src="assets/images/dashboard/processing-time.png" alt="construction" />
            </div>
            <!--<div
                (click)="navigateApprovePage()"
                class="tw-flex tw-justify-center tw-items-center tw-space-x-1 tw-p-2 view-detail-btn"
            >
                <div>Xem chi tiết</div>
                <div class="pi pi-arrow-circle-right"></div>
            </div>-->
        </div>
    </div>

    <div class="tw-grid lg:tw-grid-cols-2 md:tw-grid-cols-1 sm:tw-grid-cols-1 tw-gap-4">
        <div class="tw-bg-white tw-p-5 tw-rounded-xl">
            <div class="tw-flex tw-items-center tw-space-x-1 tw-text-xl tw-font-medium tw-mb-5 tw-text-gray-500">
                <div class="pi pi-percentage"></div>
                <div>Tiến độ dự án</div>
            </div>
            <form [formGroup]="chartForm" class="tw-grid lg:tw-grid-cols-3 md:tw-grid-cols-1 sm:tw-grid-cols-1 tw-gap-4 tw-mb-5">
                <div class="tw-flex tw-items-center tw-space-x-4">
                    <div>
                        <p-calendar
                            formControlName="startTime"
                            [showIcon]="true"
                            [showOnFocus]="false"
                            dateFormat="dd/mm/yy"
                            placeholder="Từ"
                            inputId="buttondisplay"
                        />
                    </div>
                </div>
                <div class="tw-flex tw-items-center tw-space-x-4">
                    <div>
                        <p-calendar
                            formControlName="endTime"
                            [showIcon]="true"
                            [showOnFocus]="false"
                            dateFormat="dd/mm/yy"
                            placeholder="Đến"
                            inputId="buttondisplay"
                        />
                    </div>
                </div>
            </form>
            <div *ngIf="isLoadingChart" class="tw-flex tw-justify-center tw-items-center tw-h-full">
                <p-progressSpinner styleClass="w-4rem h-4rem" strokeWidth="4" animationDuration=".5s" />
            </div>
            <div [ngClass]="{ hidden: isLoadingChart }">
                <canvas #chartCanvas id="chart" width="400"></canvas>
            </div>
        </div>
        <div class="tw-bg-white tw-p-5 tw-rounded-xl">
            <div class="tw-flex tw-justify-between tw-items-center tw-mb-4 tw-font-medium">
                <div class="tw-flex tw-items-center tw-space-x-1 tw-text-xl tw-text-gray-500">
                    <div class="pi pi-bell tw-text-xl"></div>
                    <div>Thông báo</div>
                </div>
                <p-button
                    (click)="navigateNotificationPage()"
                    label="Xem tiếp"
                    size="small"
                    icon="pi pi-arrow-right"
                    iconPos="right"
                    [text]="true"
                    [raised]="true"
                />
            </div>
            <div *ngIf="isLoadingNotification" class="tw-flex tw-items-center tw-justify-center" style="height: 450px">
                <p-progressSpinner styleClass="w-4rem h-4rem" strokeWidth="4" animationDuration=".5s" />
            </div>
            <div *ngIf="!isLoadingNotification" class="tw-flex tw-flex-col tw-space-y-2 tw-overflow-y-auto" style="height: 450px">
                <div class="notification-item tw-p-4" *ngFor="let notification of notifications; let i = index">
                    <div class="tw-p-1 tw-mb-2">{{ notification.contentWeb }}</div>
                    <div class="tw-p-1 tw-text-blue-400 tw-inline-flex tw-rounded tw-items-center" style="font-size: 13px">
                        <i class="pi pi-clock tw-mr-1"></i>
                        {{ Common.formatDate(notification.created) }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
