import { Component, ViewChild, Input, AfterViewInit, OnInit, Output, EventEmitter, ViewChildren, OnChanges, SimpleChanges, QueryList } from '@angular/core';
import { PanelModule } from 'primeng/panel';
import { FormsModule, FormArray, AbstractControl, FormBuilder, ValidatorFn, ValidationErrors } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { TableModule } from 'primeng/table';
import { CalendarModule } from 'primeng/calendar';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { CheckboxModule } from 'primeng/checkbox';
import { FormGroup, FormControl, Validators, ReactiveFormsModule } from '@angular/forms';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { Menu, MenuModule } from 'primeng/menu';
import { ProjectPlanService } from 'src/app/services/ptm/project-plan/project-plan.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { ComboboxNonRSQLComponent } from 'src/app/shared/components/combobox-nonRSQL/combobox-nonRSQL.component';
import { ConfirmationService } from 'primeng/api';
import { MenuItem } from 'primeng/api';
import { CommonModule } from '@angular/common';
import { environment } from 'src/environments/environment';
import { HttpClient } from '@angular/common/http';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { Task, IAssignee, TaskStatus } from 'src/app/models/interface/ptm';
import { ActionForTab, ProjectWorkingSetting, ProjectWorkingSettingRequest, SettingType } from 'src/app/models/interface/ptm/project-plan';
import { PopupComponent } from 'src/app/shared/components/popup/popup.component';
@Component({
    selector: 'app-pro-detail',
    templateUrl: './pro-detail.component.html',
    styleUrls: ['./pro-detail.component.scss'],
    standalone: true,
    imports: [
        PanelModule,
        PopupComponent,
        ButtonModule,
        TableModule,
        FormCustomModule,
        DropdownModule,
        CalendarModule,
        FormsModule,
        InputTextModule,
        CheckboxModule,
        MenuModule,
        CommonModule,
        ComboboxNonRSQLComponent,
        ReactiveFormsModule,
    ],
})
export class ProDetailComponent implements OnInit, AfterViewInit {
    @Input() formArray!: FormArray;
    @Input() projectId: number;
    @Input() isViewOnly: boolean;
    @Output() workingDayUpdated = new EventEmitter<void>();
    settingsForm: FormArray;
    @ViewChild('WorkingDayPopup', { static: true }) private workingDayPopup!: PopupComponent;
    @ViewChild('menu') menu: Menu;
    @ViewChild('proDetailComponentRef') proDetailComponentRef: ProDetailComponent;
    // formGroup: FormGroup;
    @ViewChildren('userFilterRef') userFilterRefs!: QueryList<ComboboxNonRSQLComponent>;
    tasks: Task[] = [];
    selectedDates: Date[] = [];
    disabledDays: number[] = [0, 6];
    rangeDates: null;
    mode!: 'create' | 'view' | 'edit';
    firstTimeFlags = new WeakMap();
    selectedDateDetails: any[] = [];
    selectedSettingContent = '';
    selectedDate: Date | null = null;
    submittedMap = new WeakMap<FormGroup, boolean>();
    downloadBaseUrl: string = environment.STORAGE_BASE_URL;
    userList: any[] = [];

    statusOptions = [
        { label: 'Đã hoàn thành', value: TaskStatus.COMPLETED },
        { label: 'Chưa hoàn thành', value: TaskStatus.NOT_COMPLETED },
        { label: 'Đang chờ xử lý', value: TaskStatus.PENDING },
        { label: 'Đã hủy', value: TaskStatus.CANCELLED },
    ];

    menuItems: MenuItem[] = [];
    dayTypes = [
        { label: 'Working', value: 1 },
        { label: 'Nonworking', value: 2 },
    ];

    constructor(
        private pps: ProjectPlanService,
        private fb: FormBuilder,
        private cf: ConfirmationService,
        private http: HttpClient,
        private route: ActivatedRoute,
        private alertService: AlertService,
    ) {}

    ngOnInit() {
        this.settingsForm = new FormArray([]);
        this.addSettingRow();
    }
    ngAfterViewInit(): void {
        this.userFilterRefs.changes.subscribe((refs) => {
            const combos = refs.toArray();
            this.setUserFilterOptions(...combos);

            // Patch giá trị hiển thị mà KHÔNG gọi API
            this.formArray.controls.forEach((control, index) => {
                const combo = combos[index];
                const assigneeId = control.get('assignee')?.value;
                const assigneeName = control.get('assigneeName')?.value;

                if (combo && assigneeId && assigneeName) {
                    combo.objectValue = [{ id: assigneeId, fullName: assigneeName }];
                    combo.value = assigneeId;
                    combo.filteredOptions = [{ id: assigneeId, fullName: assigneeName }];
                }
            });
        });

        setTimeout(() => {
            if (this.formArray?.length > 0) {
                this.formArray.controls.forEach((control) => {
                    const formGroup = control as FormGroup;
                    this.setupTaskChangeListener(formGroup);
                });
            }
        });
    }
    setUserFilterOptions(...combos: ComboboxNonRSQLComponent[]) {
        combos.forEach((combo) => {
            if (!combo || typeof combo.debouncedGetOptions !== 'function') return;

            const origDebounce = combo.debouncedGetOptions.bind(combo);
            combo.filterOptions = (term: string) => {
                const rsql = `fullName==*${term}*`;
                origDebounce(rsql);
            };
        });
    }
    allowOnlyPredecessorChars(event: KeyboardEvent): void {
        const allowed = /[0-9,]/;
        const key = event.key;

        if (!allowed.test(key)) {
            event.preventDefault(); // chặn ký tự không hợp lệ
        }
    }
    onDateRangeToggle(formGroup: FormGroup): void {
        const enableRange = formGroup.get('enableDateRange')?.value;
        const enableSpecific = formGroup.get('enableSpecificDate')?.value;

        if (enableRange && !formGroup.get('rangeDates')?.value) {
            formGroup.get('rangeDates')?.setErrors({ required: true });
        }

        if (enableSpecific && !formGroup.get('specificDate')?.value) {
            formGroup.get('specificDate')?.setErrors({ required: true });
        }
    }

    exportExcel() {
        this.pps.exportProject(this.projectId).subscribe();
    }
    validateProgress(rowIndex: number): void {
        const formGroup = this.formArray.at(rowIndex) as FormGroup;
        const raw = String(formGroup.get('progress')?.value || '').replace(',', '.');
        const value = parseFloat(raw);

        if (isNaN(value)) {
            formGroup.get('progress')?.setValue(null);
            return;
        }

        const finalValue = Math.min(value, 100); // không vượt quá 100
        const predecessorStr = formGroup.get('predecessor')?.value?.trim();

        if (finalValue === 100 && predecessorStr) {
            const predecessors = predecessorStr
                .split(',')
                .map((s: string) => s.trim())
                .filter((s) => !!s);

            let hasInvalid = false;

            for (const stt of predecessors) {
                const matched = this.formArray.controls.find(
                    (ctrl) => ctrl.get('displaySTT')?.value === stt && ctrl.get('action')?.value !== ActionForTab.DELETE,
                );

                const status = matched?.get('status')?.value;
                if (status !== TaskStatus.COMPLETED) {
                    hasInvalid = true;
                    break;
                }
            }

            if (hasInvalid) {
                this.alertService.warning('Không thể đặt tiến độ 100% khi task tiền nhiệm chưa hoàn thành');
                formGroup.get('progress')?.setValue(null);
                return;
            }
        }

        // Cho phép nhập < 100 hoặc đã hợp lệ
        formGroup.get('progress')?.setValue(finalValue);

        // Nếu đạt 100% và chưa set completed → tự cập nhật trạng thái
        if (finalValue === 100 && formGroup.get('status')?.value !== TaskStatus.COMPLETED) {
            formGroup.get('status')?.setValue(TaskStatus.COMPLETED);
        }
    }
    onStatusChange(rowIndex: number): void {
        const formGroup = this.formArray.at(rowIndex) as FormGroup;
        const selectedStatus = formGroup.get('status')?.value;
        const predecessorStr = formGroup.get('predecessor')?.value?.trim();
        if (selectedStatus === TaskStatus.COMPLETED) {
            // ✅ Autofill progress = "100%"
            formGroup.get('progress')?.setValue('100');
        }
        if (selectedStatus === TaskStatus.COMPLETED && predecessorStr) {
            const predecessors = predecessorStr
                .split(',')
                .map((s: string) => s.trim())
                .filter((s) => !!s);

            let hasInvalid = false;

            for (const stt of predecessors) {
                const matched = this.formArray.controls.find(
                    (ctrl) => ctrl.get('displaySTT')?.value === stt && ctrl.get('action')?.value !== ActionForTab.DELETE,
                );

                const status = matched?.get('status')?.value;
                if (status !== TaskStatus.COMPLETED) {
                    hasInvalid = true;
                    break;
                }
            }

            if (hasInvalid) {
                this.alertService.warning('Không thể chọn trạng thái "Đã hoàn thành" khi task tiền nhiệm chưa hoàn thành');
                formGroup.get('status')?.setValue(2); // hoặc đặt lại về trạng thái trước đó nếu có lưu
            }
        }
    }

    isProgressReadOnly(rowIndex: number): boolean {
        return this.isViewOnly;
    }
    // registerActionWatcher(): void {
    //     this.formArray.controls.forEach((ctrl: AbstractControl) => {
    //         const formGroup = ctrl as FormGroup;
    //         if (!(formGroup as any)._actionWatcherRegistered) {
    //             formGroup.valueChanges.subscribe(() => {
    //                 const actionCtrl = formGroup.get('action');
    //                 if (actionCtrl && actionCtrl.value !== 3) {
    //                     actionCtrl.setValue(ActionForTab.UPDATE, { emitEvent: false });
    //                 }
    //             });
    //             (formGroup as any)._actionWatcherRegistered = true;
    //         }
    //     });
    // }
    onDateClick(date: Date) {
        this.selectedDate = date;
        const selectedTime = new Date(date);
        selectedTime.setHours(0, 0, 0, 0);

        let matched = false;
        let matchedContent = '';

        for (const control of this.settingsForm.controls) {
            const fg = control as FormGroup;
            const specificDate = fg.get('specificDate')?.value;
            const rangeDates = fg.get('rangeDates')?.value;

            if (specificDate instanceof Date && specificDate.setHours(0, 0, 0, 0) === selectedTime.getTime()) {
                matched = true;
                matchedContent = fg.get('content')?.value || '';
                break;
            }

            if (Array.isArray(rangeDates) && rangeDates.length === 2) {
                const from = new Date(rangeDates[0]).setHours(0, 0, 0, 0);
                const to = new Date(rangeDates[1]).setHours(0, 0, 0, 0);
                if (selectedTime.getTime() >= from && selectedTime.getTime() <= to) {
                    matched = true;
                    matchedContent = fg.get('content')?.value || '';
                    break;
                }
            }
        }

        if (matched) {
            this.selectedSettingContent = matchedContent;
        } else {
            const isWeekend = selectedTime.getDay() === 0 || selectedTime.getDay() === 6;
            this.selectedSettingContent = isWeekend ? '[Default]' : '';
        }
    }
    resetSelectedDateDetails(): void {
        this.selectedDate = null;
        this.selectedDates = null;
        this.selectedSettingContent = '';
    }
    setupTaskChangeListener(formGroup: FormGroup): void {
        const editableFields = ['name', 'timeline', 'duration', 'predecessor', 'assignee', 'progress', 'status', 'note'];

        editableFields.forEach((field) => {
            formGroup.get(field)?.valueChanges.subscribe(() => {
                const raw = formGroup.get('rawData')?.value;
                if (!raw) return;

                const previous = JSON.parse(raw);
                const previousFieldValue = previous[field];
                const currentFieldValue = formGroup.get(field)?.value;

                const actionCtrl = formGroup.get('action');
                const actionVal = actionCtrl?.value;

                // Nếu chỉ trường hiện tại thay đổi → mới set UPDATE
                const changed = JSON.stringify(previousFieldValue) !== JSON.stringify(currentFieldValue);

                if (changed && actionVal !== ActionForTab.CREATE && actionVal !== ActionForTab.DELETE) {
                    actionCtrl?.setValue(ActionForTab.UPDATE, { emitEvent: false });
                }

                if (!changed && actionVal === ActionForTab.UPDATE) {
                    // Check lại toàn bộ các trường khác xem có gì thực sự thay đổi không
                    const fullCurrent = {
                        name: formGroup.get('name')?.value,
                        timeline: formGroup.get('timeline')?.value,
                        duration: formGroup.get('duration')?.value,
                        predecessor: formGroup.get('predecessor')?.value,
                        assignee: formGroup.get('assignee')?.value,
                        assigneeName: formGroup.get('assigneeName')?.value,
                        progress: formGroup.get('progress')?.value,
                        status: formGroup.get('status')?.value,
                        note: formGroup.get('note')?.value,
                    };

                    const stillChanged = JSON.stringify(fullCurrent) !== JSON.stringify(previous);
                    if (!stillChanged) {
                        actionCtrl?.setValue(ActionForTab.NO_CHANGE, { emitEvent: false });
                    }
                }
            });
        });

        formGroup.get('timeline')?.valueChanges.subscribe(() => {
            this.validateSingleTaskTimeline(formGroup, this.getProjectStartDate(), this.getProjectEndDate(), new Date());
        });
    }

    validatePredecessor(): void {
        // Lấy danh sách STT của các task main (level === 1)
        const mainTaskSTTs = this.formArray.controls
            .filter((ctrl) => (ctrl.get('level')?.value ?? 1) === 1)
            .map((ctrl) => ctrl.get('displaySTT')?.value)
            .filter(Boolean);

        this.formArray.controls.forEach((ctrl: AbstractControl) => {
            const fg = ctrl as FormGroup;
            const predValue = fg.get('predecessor')?.value;

            if (!predValue || predValue.trim() === '') {
                fg.get('predecessor')?.setErrors(null); // không bắt buộc
                return;
            }

            const parts = predValue.split(',').map((p) => p.trim());
            const isValid = parts.every((p) => /^[1-9]\d*$/.test(p) && mainTaskSTTs.includes(p));

            if (!isValid) {
                fg.get('predecessor')?.setErrors({ invalidSTT: true });
            } else {
                fg.get('predecessor')?.setErrors(null);
            }
        });
    }

    getExistingTaskNames(): string[] {
        return this.formArray.controls.map((control) => control.get('name')?.value).filter((name) => !!name);
    }

    addTask() {
        const existingNames = this.getAllTaskNames();
        const newTask = new FormGroup({
            name: new FormControl('', [Validators.required, this.uniqueTaskNameValidator(this.formArray)]),
            timeline: new FormControl([], Validators.required),
            duration: new FormControl(null),
            predecessor: new FormControl(''),
            assignee: new FormControl(null),
            progress: new FormControl(null),
            status: new FormControl(TaskStatus.NOT_COMPLETED),
            note: new FormControl(''),
            level: new FormControl(1),
            displaySTT: new FormControl(''),
            action: new FormControl(ActionForTab.CREATE),
        });
        this.setupTaskChangeListener(newTask);
        this.formArray.push(newTask);
        this.rebuildSTT(); // cập nhật lại displaySTT
        this.validateAllTaskTimelines();
    }

    getAllTaskNames(): string[] {
        return this.visibleTasks.map((ctrl) => ctrl.get('name')?.value?.trim()).filter((name) => !!name);
    }

    addNewTask() {
        const newTask: Task = {
            name: '',
            timeLine: { start: null, end: null },
            duration: 0,
            assignee: { id: '', name: 'Chọn người phụ trách' },
            progress: 0,
            status: TaskStatus.NOT_COMPLETED,
        };
        this.tasks.push(newTask);
    }
    addSettingRow() {
        const isDisabled = this.isViewOnly;

        const settingGroup = this.fb.group(
            {
                content: [{ value: '', disabled: isDisabled }, Validators.required],
                dayType: [{ value: null, disabled: isDisabled }, Validators.required],
                rangeDates: [{ value: null, disabled: isDisabled }],
                specificDate: [{ value: null, disabled: isDisabled }],
                enableDateRange: [{ value: false, disabled: isDisabled }],
                enableSpecificDate: [{ value: false, disabled: isDisabled }],
            },
            { validators: this.workingSettingValidator() },
        );

        this.settingsForm.push(settingGroup);
        this.submittedMap.set(settingGroup, false);
    }
    workingSettingValidator(): ValidatorFn {
        return (group: AbstractControl): { [key: string]: any } | null => {
            const enableRange = group.get('enableDateRange')?.value;
            const rangeDates = group.get('rangeDates')?.value;
            const enableSpecific = group.get('enableSpecificDate')?.value;
            const specificDate = group.get('specificDate')?.value;

            const errors: any = {};

            if (!enableRange && !enableSpecific) {
                errors.atLeastOneRequired = true;
            }

            if (enableRange && (!Array.isArray(rangeDates) || rangeDates.length !== 2 || !rangeDates[0] || !rangeDates[1])) {
                errors.rangeDatesRequired = true;
            }

            if (enableSpecific && !specificDate) {
                errors.specificDateRequired = true;
            }

            return Object.keys(errors).length > 0 ? errors : null;
        };
    }
    deleteRow(index: number): void {
        if (index === 0) {
            // Reset lại giá trị dòng đầu tiên
            const row = this.settingsForm.at(0) as FormGroup;
            row.reset({
                content: '',
                dayType: null,
                rangeDates: null,
                specificDate: null,
                enableDateRange: false,
                enableSpecificDate: false,
            });
        } else {
            // Xóa dòng bình thường
            this.settingsForm.removeAt(index);
        }
    }
    onApply() {
        // this.settingsForm.markAllAsTouched();
        // if (this.settingsForm.valid) {
        // } else {
        // this.alertService.warning('Vui lòng điền đầy đủ thông tin bắt buộc');
        this.settingsForm.controls.forEach((ctrl) => {
            if (ctrl instanceof FormGroup) {
                this.submittedMap.set(ctrl, true); // ✅ đánh dấu dòng đã submitted
            }
        });
        this.markFormGroupTouched(this.settingsForm);
        // }
        if (this.settingsForm.invalid) {
            return;
        }
        this.saveWorkingSettings();
    }
    isSettingSubmitted(setting: AbstractControl): boolean {
        return this.submittedMap.get(setting as FormGroup) === true;
    }

    markFormGroupTouched(formArray: FormArray) {
        formArray.controls.forEach((control) => {
            if (control instanceof FormGroup) {
                // control.markAllAsTouched();
                Object.values(control.controls).forEach((c) => c.markAsTouched());
            }
        });
    }

    isTaskOverdue(control: AbstractControl): boolean {
        const timeline = control.get('timeline')?.value;
        const status = control.get('status')?.value;

        if (!Array.isArray(timeline) || timeline.length !== 2) return false;

        const endDate = timeline[1];
        if (!(endDate instanceof Date)) return false;

        const today = new Date();
        today.setHours(0, 0, 0, 0);

        return endDate < today && status !== TaskStatus.COMPLETED;
    }

    validateAllTaskTimelines(): void {
        const projectStart = this.getProjectStartDate();
        const projectEnd = this.getProjectEndDate();
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        this.formArray.controls.forEach((control) => {
            this.validateSingleTaskTimeline(control as FormGroup, projectStart, projectEnd, today);
        });
    }

    // Validate timeline cho từng task
    private validateSingleTaskTimeline(task: FormGroup, projectStart: Date, projectEnd: Date, today: Date): void {
        const timeline = task.get('timeline')?.value;
        const level = task.get('level')?.value ?? 1;
        const status = task.get('status')?.value;

        const timelineControl = task.get('timeline');

        // ✅ Nếu timeline không hợp lệ (null, không phải mảng, thiếu ngày) thì không can thiệp
        if (!Array.isArray(timeline) || timeline.length !== 2 || !timeline[0] || !timeline[1]) {
            return; // để mặc cho Validators.required xử lý
        }

        const [startDate, endDate] = timeline;
        const errors: any = {};

        // Main task phải nằm trong thời gian dự án
        if (level === 1) {
            if (startDate < projectStart) {
                errors.outsideProject = `Ngày bắt đầu phải sau ${this.formatDate(projectStart)}`;
            }
            if (endDate > projectEnd) {
                errors.outsideProject = `Ngày kết thúc phải trước ${this.formatDate(projectEnd)}`;
            }
        }

        // Subtask phải nằm trong task cha
        if (level > 1) {
            const parentTimeline = this.getParentTimeline(task);
            if (parentTimeline) {
                if (startDate < parentTimeline[0]) {
                    errors.outsideParent = 'Ngày bắt đầu phải sau ngày task cha';
                }
                if (endDate > parentTimeline[1]) {
                    errors.outsideParent = 'Ngày kết thúc phải trước ngày task cha';
                }
            }
        }

        if (Object.keys(errors).length > 0) {
            timelineControl?.setErrors(errors);
        } else {
            // ❌ Không reset lỗi nếu timeline rỗng - để lỗi required còn giữ nguyên
            if (timelineControl?.errors && !timelineControl.hasError('required')) {
                timelineControl.setErrors(null);
            }
        }
    }

    private formatDate(date: Date): string {
        return date.toLocaleDateString('vi-VN');
    }

    // Lấy ngày bắt đầu dự án từ component cha
    private getProjectStartDate(): Date {
        const startDate = this.formArray.parent?.get('startDate')?.value;
        return startDate ? new Date(startDate) : new Date();
    }

    // Lấy ngày kết thúc dự án từ component cha
    private getProjectEndDate(): Date {
        const endDate = this.formArray.parent?.get('endDate')?.value;
        return endDate ? new Date(endDate) : new Date();
    }

    // Lấy timeline của task cha
    private getParentTimeline(task: FormGroup): [Date, Date] | null {
        const displaySTT = task.get('displaySTT')?.value;
        if (!displaySTT) return null;

        const parentSTT = displaySTT.substring(0, displaySTT.lastIndexOf('.'));
        const parentTask = this.formArray.controls.find((ctrl) => ctrl.get('displaySTT')?.value === parentSTT);

        return parentTask?.get('timeline')?.value;
    }

    uniqueTaskNameValidator(formArray: FormArray): ValidatorFn {
        return (control: AbstractControl): ValidationErrors | null => {
            if (!control?.parent) return null;

            const currentValue = control.value?.trim();
            if (!currentValue) return null;

            const formArray = control.parent?.parent as FormArray;
            if (!(formArray instanceof FormArray)) return null;

            const isDuplicate = formArray.controls.some((ctrl) => {
                if (ctrl === control.parent) return false;

                const name = ctrl.get('name')?.value?.trim();
                const action = ctrl.get('action')?.value;

                return action !== ActionForTab.DELETE && name === currentValue;
            });

            return isDuplicate ? { duplicateTaskName: 'Tên task đã tồn tại' } : null;
        };
    }
    onTaskNameChange(changedIndex: number): void {
        const changedControl = this.formArray.at(changedIndex);

        // Cập nhật cho chính task đang sửa
        changedControl.get('name')?.updateValueAndValidity({ onlySelf: true });

        // Cập nhật lại tất cả task khác để phát hiện trùng tên
        this.formArray.controls.forEach((ctrl, idx) => {
            if (idx !== changedIndex) {
                ctrl.get('name')?.updateValueAndValidity({ onlySelf: true });
            }
        });
    }

    saveWorkingSettings() {
        const settingsPayload: ProjectWorkingSetting[] = this.settingsForm.controls.map((control) => {
            const formGroup = control as FormGroup;
            const value = formGroup.value;

            const rangeDates = Array.isArray(value.rangeDates) ? value.rangeDates : [];
            const dateFrom = rangeDates[0] ? new Date(rangeDates[0]).getTime() : null;
            const dateTo = rangeDates[1] ? new Date(rangeDates[1]).getTime() : null;

            const specificDateArr: number[] = [];

            if (value.specificDate instanceof Date) {
                specificDateArr.push(new Date(value.specificDate).getTime());
            } else if (Array.isArray(value.specificDate)) {
                value.specificDate.forEach((d: Date) => {
                    if (d instanceof Date) {
                        specificDateArr.push(new Date(d).getTime());
                    }
                });
            }
            const settingType =
                value.enableDateRange && value.enableSpecificDate
                    ? SettingType.ALL_TYPE_DAY
                    : value.enableDateRange
                      ? SettingType.RANGE
                      : value.enableSpecificDate
                        ? SettingType.SPECIFIC_DAY
                        : null;

            return {
                id: value.id || 0,
                projectId: this.projectId,
                settingType,
                dayType: value.dayType,
                content: value.content || '',
                dateFrom,
                dateTo,
                specificDate: specificDateArr.join(','),
            };
        });

        const requestBody: ProjectWorkingSettingRequest = { settings: settingsPayload };

        this.pps.updateProjectWorkingSettings(this.projectId, requestBody).subscribe({
            next: () => {
                this.alertService.success('Cập nhật cấu hình làm việc thành công');
                this.workingDayPopup.closeDialog();
                this.workingDayUpdated.emit();
            },
            // error: (error) => {
            //     this.alertService.error('Có lỗi xảy ra khi cập nhật cấu hình làm việc');
            //     console.error('Lỗi khi cập nhật cài đặt làm việc:', error);
            // },
        });
    }

    getRowClassBySTT(control: any): string {
        if (!control || typeof control.get !== 'function') {
            return '';
        }

        const stt = control.get('displaySTT')?.value;

        const levelDepth = stt?.split('.').length || 0;
        switch (levelDepth) {
            case 1:
                return 'task-level-1';
            case 2:
                return 'task-level-2';
            // case 3:
            //     return 'task-level-3';
            default:
                return '';
        }
    }

    onDateRangeSelect(rowIndex: number): void {
        const control = this.formArray.at(rowIndex)?.get('timeline');
        const value = control?.value;
        if (Array.isArray(value) && value[0] && value[1]) {
            // PrimeNG không tự đóng nếu dùng formControl ⇒ giả lập click ngoài để đóng
            setTimeout(() => {
                const event = new MouseEvent('mousedown', { bubbles: true });
                document.body.dispatchEvent(event);
            }, 100); // đợi 1 chút để không bị conflict với UI cập nhật
        }
    }

    onMenuClick(event: MouseEvent, rowIndex: number, menu: Menu) {
        this.menuItems = [
            {
                label: 'Thêm subtask',
                icon: 'pi pi-plus',
                command: () => this.addSubTask(rowIndex),
            },
            {
                label: 'Xóa',
                icon: 'pi pi-trash',
                command: () => this.removeTask(rowIndex),
            },
        ];
        menu.toggle(event);
    }

    addSubTask(parentIndex: number): void {
        const parentControl = this.visibleTasks[parentIndex] as FormGroup;
        const parentSTT = parentControl.get('displaySTT')?.value;
        const parentLevel = parentControl.get('level')?.value || 1;
        const newLevel = parentLevel + 1;

        const parentFormIndex = this.formArray.controls.indexOf(parentControl);
        let insertIndex = parentFormIndex + 1;

        // Tìm vị trí sau tất cả các subtasks để chèn
        for (let i = insertIndex; i < this.formArray.length; i++) {
            const ctrl = this.formArray.at(i);
            const stt = ctrl.get('displaySTT')?.value;
            const lvl = ctrl.get('level')?.value || 1;

            if (!stt?.startsWith(parentSTT + '.') || lvl <= parentLevel) {
                break;
            }

            insertIndex = i + 1;
        }

        const existingNames = this.getAllTaskNames();

        const newSubtask = new FormGroup({
            name: new FormControl('', [Validators.required, this.uniqueTaskNameValidator(this.formArray)]),
            timeline: new FormControl(null),
            duration: new FormControl(null),
            predecessor: new FormControl(''),
            assignee: new FormControl(null),
            progress: new FormControl(null),
            status: new FormControl(TaskStatus.NOT_COMPLETED),
            note: new FormControl(''),
            level: new FormControl(newLevel),
            displaySTT: new FormControl(''), // STT sẽ được set lại trong rebuildSTT
            action: new FormControl(ActionForTab.CREATE),
        });
        this.setupTaskChangeListener(newSubtask);
        this.validateAllTaskTimelines();
        this.formArray.insert(insertIndex, newSubtask);
        this.rebuildSTT(); // Cập nhật lại toàn bộ STT
    }

    rebuildSTT(): void {
        const levelCounters: number[] = [];

        this.visibleTasks.forEach((task) => {
            const level: number = task.get('level')?.value ?? 1;
            levelCounters.length = level;

            if (!levelCounters[level - 1]) {
                levelCounters[level - 1] = 1;
            } else {
                levelCounters[level - 1]++;
            }

            const stt = levelCounters.slice(0, level).join('.');
            task.get('displaySTT')?.setValue(stt);
        });
    }
    get visibleTasks() {
        return this.formArray.controls.filter((c) => c.get('action')?.value !== ActionForTab.DELETE);
    }

    removeTask(rowIndex: number): void {
        const task = this.visibleTasks[rowIndex];
        const stt = task.get('displaySTT')?.value;
        const level = task.get('level')?.value ?? 1;

        const hasSubTasks = this.visibleTasks.some((ctrl) => {
            const childSTT = ctrl.get('displaySTT')?.value;
            const childLevel = ctrl.get('level')?.value ?? 1;
            return childSTT?.startsWith(`${stt}.`) && childLevel > level;
        });

        if (hasSubTasks && level === 1) {
            this.cf.confirm({
                key: 'app-confirm',
                header: 'Xác nhận',
                message: 'Việc xóa task main sẽ đồng thời xóa các subtask của nó. Vẫn muốn xóa?',
                icon: 'pi pi-exclamation-triangle',
                rejectLabel: 'Hủy',
                acceptLabel: 'Xóa',
                acceptIcon: 'pi pi-check mr-2',
                rejectIcon: 'pi pi-times mr-2',
                rejectButtonStyleClass: 'p-button-sm',
                acceptButtonStyleClass: 'p-button-outlined p-button-sm',
                accept: () => this.performTaskRemoval(stt),
            });
        } else {
            this.performTaskRemoval(stt);
        }
    }
    performTaskRemoval(parentSTT: string): void {
        const sttsToRemove: string[] = [];

        // Tìm tất cả STT cha + con
        for (let i = 0; i < this.formArray.length; i++) {
            const stt = this.formArray.at(i).get('displaySTT')?.value;
            if (stt === parentSTT || stt?.startsWith(`${parentSTT}.`)) {
                sttsToRemove.push(stt);
            }
        }

        // Gỡ khỏi predecessor
        this.formArray.controls.forEach((ctrl) => {
            const predCtrl = ctrl.get('predecessor');
            if (predCtrl?.value) {
                const filtered = predCtrl.value
                    .split(',')
                    .map((x) => x.trim())
                    .filter((x) => !sttsToRemove.includes(x));
                predCtrl.setValue(filtered.join(','));
            }
        });

        // Đánh dấu action = UPDATE cho các task sau
        // const indexesToRemove: number[] = [];

        // for (let i = 0; i < this.formArray.length; i++) {
        //     const stt = this.formArray.at(i).get('displaySTT')?.value;
        //     if (stt === parentSTT || stt?.startsWith(`${parentSTT}.`)) {
        //         sttsToRemove.push(stt);
        //         indexesToRemove.push(i);
        //     }
        // }

        // const lastRemovedIndex = Math.max(...indexesToRemove);
        // for (let i = lastRemovedIndex + 1; i < this.formArray.length; i++) {
        //     const ctrl = this.formArray.at(i);
        //     const action = ctrl.get('action')?.value;
        //     if (action !== ActionForTab.CREATE && action !== ActionForTab.DELETE) {
        //         ctrl.get('action')?.setValue(ActionForTab.UPDATE);
        //     }
        // }

        // Xoá từ dưới lên
        for (let i = this.formArray.length - 1; i >= 0; i--) {
            const ctrl = this.formArray.at(i);
            const stt = ctrl.get('displaySTT')?.value;
            if (sttsToRemove.includes(stt)) {
                const isNew = !ctrl.get('id')?.value || ctrl.get('action')?.value === ActionForTab.CREATE;
                if (isNew) {
                    this.formArray.removeAt(i);
                } else {
                    ctrl.get('action')?.setValue(ActionForTab.DELETE);
                    ctrl.disable({ emitEvent: false });
                }
            }
        }

        this.rebuildSTT();
    }

    highlightWeekends(event: any) {
        if (event.day === 0 || event.day === 6) {
            event.styleClass = 'weekend';
        }
    }

    // handlePanelShow(ref: any) {
    //     if (!this.firstTimeFlags.has(ref)) {
    //         this.firstTimeFlags.set(ref, true);
    //         return;
    //     }
    //     ref.fetchOptions(null);
    // }

    openWorkingDay() {
        this.workingDayPopup.openDialog();
        this.loadWorkingSettings();
    }
    loadWorkingSettings(): void {
        if (!this.projectId) return;

        this.pps.getProjectWorkingSettings(this.projectId).subscribe((response: any[]) => {
            this.settingsForm.clear(); // reset form cũ
            if (response && response.length > 0) {
                response.forEach((item) => {
                    const isDisabled = this.isViewOnly;
                    // const matchedDayType = this.dayTypes.find((d) => d.value === item.dayType);
                    const settingGroup = new FormGroup({
                        id: new FormControl(item.id),
                        content: new FormControl({ value: item.content || '', disabled: isDisabled }, Validators.required),
                        dayType: new FormControl({ value: item.dayType, disabled: isDisabled }, Validators.required),
                        rangeDates: new FormControl({
                            value: item.dateFrom && item.dateTo ? [new Date(item.dateFrom), new Date(item.dateTo)] : null,
                            disabled: isDisabled,
                        }),
                        specificDate: new FormControl({
                            value: item.specificDate ? new Date(Number(item.specificDate.split(',')[0])) : null,
                            disabled: isDisabled,
                        }),
                        enableDateRange: new FormControl({ value: !!item.dateFrom && !!item.dateTo, disabled: isDisabled }),
                        enableSpecificDate: new FormControl({ value: !!item.specificDate, disabled: isDisabled }),
                    });

                    this.settingsForm.push(settingGroup);
                });
            } else {
                this.addSettingRow();
            }
        });
    }
}
