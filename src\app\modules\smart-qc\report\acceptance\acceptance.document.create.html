<app-sub-header [items]="itemsHeader" [action]="actionHeader"></app-sub-header>
<ng-template #actionHeader>
    <p-button
        label="Lưu"
        (click)="createAcceptanceDocs()"
        severity="success"
        size="small"
        [disabled]="selectedImages.length === 0"
    />
    <p-button label="Đóng" (click)="gotoListPage()" severity="secondary" size="small" />
</ng-template>
<div class="tw-p-5">
    <form [formGroup]="reportForm">
        <div class="tw-bg-white tw-p-5 tw-rounded-xl tw-mb-5">
            <div class="tw-grid lg:tw-grid-cols-5 md:tw-grid-cols-1 sm:tw-grid-cols-1 tw-gap-4 tw-mb-3">
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <div class="tw-font-bold">Dự án <span class="tw-text-red-600">(*)</span>:</div>
                    <div *ngIf="authService.isPM() || authService.isAdmin()">
                        <app-filter-table
                            field="name"
                            rsql="true"
                            type="select-one"
                            select-first-value="true"
                            [configSelect]="{
                                fieldValue: 'id',
                                fieldLabel: 'name',
                                rsql: true,
                                param: 'name',
                                url: '/smart-qc/api/contract/search',
                            }"
                            (onChange)="selectContract($event.value, false)"
                            placeholder="Tên dự án"
                            [initValue]="reportForm.get('contractId').value"
                            #filerArea
                        ></app-filter-table>
                    </div>
                    <div *ngIf="authService.isSubPM()">
                        <app-filter-table
                            field="name"
                            [rsql]="false"
                            type="select-one"
                            select-first-value="true"
                            [configSelect]="{
                                fieldValue: 'id',
                                fieldLabel: 'name',
                                rsql: false,
                                url: '/smart-qc/api/contract/combobox',
                            }"
                            (onChange)="selectContract($event.value, false)"
                            placeholder="Tên dự án"
                            [initValue]="reportForm.get('contractId').value"
                            #filerArea
                        ></app-filter-table>
                    </div>
                    <div
                        *ngIf="reportForm.get('contractId').errors && reportForm.get('contractId').touched"
                        class="text-red-600"
                    >
                        <div *ngIf="reportForm.get('contractId').errors['required']">Tên dự án là trường bắt buộc</div>
                    </div>
                </div>
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <div class="tw-font-bold">Công việc <span class="tw-text-red-600">(*)</span>:</div>
                    <div *ngIf="authService.isPM() || authService.isAdmin()">
                        <app-filter-table
                            [disabled]="!reportForm.get('contractId').value"
                            field="name"
                            rsql="true"
                            type="select-one"
                            [configSelect]="{
                                fieldValue: 'id',
                                fieldLabel: 'name',
                                param: 'name',
                                rsql: true,
                                body: {
                                    contractId: reportForm.get('contractId').value,
                                },
                                url: '/smart-qc/api/action/search',
                            }"
                            (onChange)="selectAction($event.value)"
                            [initValue]="reportForm.get('actionId').value"
                            placeholder="Công việc"
                            #filerArea
                        ></app-filter-table>
                    </div>
                    <div *ngIf="authService.isSubPM()">
                        <app-filter-table
                            [disabled]="!reportForm.get('contractId').value"
                            field="name"
                            [rsql]="false"
                            type="select-one"
                            [configSelect]="{
                                fieldValue: 'id',
                                fieldLabel: 'name',
                                param: 'name',
                                rsql: false,
                                body: {
                                    contractId: reportForm.get('contractId').value,
                                },
                                url: '/smart-qc/api/action/combobox',
                            }"
                            (onChange)="selectAction($event.value)"
                            [initValue]="reportForm.get('actionId').value"
                            placeholder="Công việc"
                            #filerArea
                        ></app-filter-table>
                    </div>
                    <div
                        *ngIf="reportForm.get('contractId').errors && reportForm.get('contractId').touched"
                        class="text-red-600"
                    >
                        <div *ngIf="reportForm.get('contractId').errors['required']">
                            Tên công việc là trường bắt buộc
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tw-grid lg:tw-grid-cols-2 tw-gap-4">
            <div class="tw-bg-white tw-p-5 tw-rounded-xl">
                <div class="tw-font-bold tw-mb-5">Mẫu nghiệm thu</div>
                <div class="tw-mb-5">Tiêu đề</div>
                <div class="tw-mb-5">
                    <input
                        style="width: 100%"
                        placeholder="Nhập tiêu đề"
                        type="text"
                        pInputText
                        formControlName="title"
                    />
                </div>
                <div class="tw-overflow-y-auto" style="height: 500px">
                    <div class="tw-font-bold tw-mb-5">Danh sách hình ảnh</div>
                    <div class="tw-flex tw-justify-end tw-mb-5">
                        <p-button
                            [disabled]="!reportForm.get('actionId').value"
                            (click)="openModalAddImage()"
                            icon="pi pi-plus"
                            label="Thêm ảnh"
                        />
                    </div>
                    <div
                        cdkDropList
                        [cdkDropListData]="selectedImages"
                        class="drag-custom-list"
                        (cdkDropListDropped)="dropImage($event)"
                    >
                        <p-table [columns]="colsImage" [value]="selectedImages" [tableStyle]="{ 'min-width': '50rem' }">
                            <ng-template pTemplate="header" let-columns>
                                <tr>
                                    <th>STT</th>
                                    <th *ngFor="let col of columns">
                                        {{ col.header }}
                                    </th>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="body" let-rowData let-columns="columns" let-rowIndex="rowIndex">
                                <tr cdkDrag [cdkDragData]="rowData" [cdkDragPreviewClass]="'drag-preview'">
                                    <td>{{ rowIndex + 1 }}</td>
                                    <td *ngFor="let col of columns">
                                        <ng-container *ngIf="col.field !== 'action'; else actionTemplate">
                                            {{ rowData[col.field] }}
                                        </ng-container>
                                        <ng-template #actionTemplate>
                                            <p-button
                                                (click)="deleteImage(rowData['detailId'])"
                                                severity="danger"
                                                icon="pi pi-trash"
                                                label=""
                                            ></p-button>
                                        </ng-template>
                                    </td>
                                </tr>
                            </ng-template>
                        </p-table>
                    </div>
                </div>
            </div>
            <div class="tw-relative tw-bg-white tw-p-5 tw-rounded-xl">
                <div class="tw-overflow-y-auto" style="height: 500px">
                    <div class="tw-font-bold tw-mb-5">Danh sách trạm</div>
                    <div class="tw-flex tw-justify-end tw-mb-5">
                        <p-button
                            [disabled]="!reportForm.get('actionId').value"
                            (click)="openModalAddStation()"
                            icon="pi pi-plus"
                            label="Thêm trạm"
                        />
                    </div>
                    <p-table [columns]="colsStation" [value]="selectedStations" [tableStyle]="{ 'min-width': '50rem' }">
                        <ng-template pTemplate="header" let-columns>
                            <tr>
                                <th>STT</th>
                                <th *ngFor="let col of columns">
                                    {{ col.header }}
                                </th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-rowData let-columns="columns" let-rowIndex="rowIndex">
                            <tr>
                                <td>{{ rowIndex + 1 }}</td>
                                <td *ngFor="let col of columns">
                                    <ng-container *ngIf="col.field !== 'action'; else actionTemplate">
                                        {{ rowData[col.field] }}
                                    </ng-container>
                                    <ng-template #actionTemplate>
                                        <p-button
                                            (click)="deleteStation(rowData['taskId'])"
                                            severity="danger"
                                            icon="pi pi-trash"
                                            label=""
                                        />
                                    </ng-template>
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                </div>
                <div class="tw-absolute tw-bottom-5 tw-right-5">
                    <p-button
                        class="tw-mr-2"
                        [disabled]="!reportForm.get('contractId').value || !reportForm.get('actionId').value"
                        severity="primary"
                        (click)="exportApproveData()"
                        label="Xuất dữ liệu văn bản"
                    />
                    <p-button
                        [disabled]="selectedImages.length === 0 || selectedStations.length === 0"
                        severity="success"
                        (click)="isOpenModalExport = true;"
                        label="Xuất dữ liệu ảnh"
                    />
                </div>
            </div>
        </div>
    </form>
</div>

<p-dialog
    header="Thêm hình ảnh"
    [modal]="true"
    [(visible)]="isOpenModalAddImage"
    [style]="{ minWidth: '1200px', top: '' }"
>
    <div>
        <div class="tw-flex tw-justify-end tw-mb-5 tw-space-x-3">
            <p-button [disabled]="numberImagesSelected === 0" (click)="selectImage()" icon="pi pi-plus" label="Chọn" />
            <p-button (click)="selectAllImage()" icon="pi pi-plus" label="Chọn tất cả" />
        </div>
        <app-table-common
            [tableId]="tableIdImage"
            [columns]="columnsImageImportTable"
            [data]="stateImage.data"
            [loading]="stateImage.isFetching"
            title=""
            [stt]="true"
            [hideButtonHeader]="true"
            fieldSelect="detailId"
        >
        </app-table-common>
    </div>
</p-dialog>
<p-dialog
    header="Thêm trạm"
    [modal]="true"
    [(visible)]="isOpenModalAddStation"
    [style]="{ minWidth: '1200px', top: '' }"
>
    <div>
        <div class="tw-flex tw-justify-end tw-mb-5 tw-space-x-3">
            <p-button
                [disabled]="numberStationsSelected === 0"
                (click)="selectStation()"
                icon="pi pi-plus"
                label="Chọn"
            />
            <p-button (click)="selectAllStation()" icon="pi pi-plus" label="Chọn tất cả" />
        </div>
        <app-table-common
            [tableId]="tableIdStation"
            [columns]="columnsStationImportTable"
            [data]="stateStation.data"
            [loading]="stateStation.isFetching"
            title=""
            [stt]="true"
            [hideButtonHeader]="true"
            fieldSelect="taskId"
            [filterTemplate]="filterStation"
        >
            <ng-template #filterStation>
                <tr>
                    <th></th>
                    <th></th>
                    <th *ngIf="!authService.isSubPM()">
                        <app-filter-table
                            [tableId]="tableIdStation"
                            field="subPmIds"
                            [rsql]="false"
                            type="select"
                            [configSelect]="{
                                fieldValue: 'id',
                                fieldLabel: 'name',
                                rsql: false,
                                param: 'name',
                                url: '/smart-qc/api/combobox/get-sub-pm',
                                body: {
                                    contractId: reportForm.get('contractId').value,
                                    actionId: reportForm.get('actionId').value,
                                    stationIds: stationIdsAddStation,
                                    areaIds: areaIdsAddStation,
                                },
                            }"
                            placeholder="SubPM"
                        ></app-filter-table>
                    </th>
                    <th>
                        <app-filter-table
                            [tableId]="tableIdStation"
                            field="stationIds"
                            [rsql]="false"
                            type="select"
                            [configSelect]="{
                                fieldValue: 'id',
                                fieldLabel: 'name',
                                rsql: false,
                                param: 'name',
                                url: '/smart-qc/api/combobox/get-station',
                                body: {
                                    contractId: reportForm.get('contractId').value,
                                    actionId: reportForm.get('actionId').value,
                                    areaIds: areaIdsAddStation,
                                },
                            }"
                            (onChange)="selectStationAddStation($event)"
                            placeholder="Tên trạm"
                        ></app-filter-table>
                    </th>
                    <th>
                        <app-filter-table
                            [tableId]="tableIdStation"
                            field="areaIds"
                            [rsql]="false"
                            type="select"
                            [configSelect]="{
                                fieldValue: 'id',
                                fieldLabel: 'name',
                                rsql: false,
                                param: 'name',
                                url: '/smart-qc/api/combobox/get-area',
                                body: {
                                    contractId: reportForm.get('contractId').value,
                                    actionId: reportForm.get('actionId').value,
                                },
                            }"
                            (onChange)="selectAreaAddStation($event)"
                            placeholder="Tỉnh/ Thành phố"
                        ></app-filter-table>
                    </th>
                </tr>
            </ng-template>
        </app-table-common>
    </div>
</p-dialog>
<p-dialog header="Xuất dữ liệu" [modal]="true" [(visible)]="isOpenModalExport" [style]="{ width: '45rem' }">
    <a (click)="downloadFileFromLinkArray()" class="tw-cursor-pointer">Bấm vào đây để tải xuống</a>
    <div class="flex justify-content-end gap-2">
        <p-button label="Đóng" severity="secondary" (click)="isOpenModalExport = false" />
    </div>
</p-dialog>
