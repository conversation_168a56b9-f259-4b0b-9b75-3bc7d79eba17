<form #ngForm="ngForm" (ngSubmit)="(saveObject)">
    <app-sub-header
        [items]="[{ label: 'Quản lý loại nhà cung cấp', url: './..' }, { label: supplierType.id ? 'Sửa' : 'Tạo mới' }]"
        [action]="action"
    ></app-sub-header>

    <ng-template #action>
        <!--<p-button
            (click)="visibleFileDialog = true"
            label="Xuất excel"
            size="small"
            icon="pi pi-file-export"
            severity="success"
        ></p-button>-->
        <p-button label="Lưu" (click)="saveObject()" severity="success" size="small" />
        <p-button label="Đóng" (click)="cancelUpdate()" severity="secondary" size="small" />
    </ng-template>

    <div class="tw-p-5">
        <p-panel header="Thông tin chung" [toggleable]="true">
            <div class="tw-mb-3">
                <div class="tw-font-bold tw-mb-3">
                    Loai nhà cung cấp
                    <span class="tw-text-red-600">(*)</span>
                    :
                </div>
                <input
                    [(ngModel)]="supplierType.name"
                    [disabled]="true"
                    name="name"
                    style="width: 100%"
                    pInputText
                    type="text"
                    class="flex-auto tw-border-2 tw-border-gray-300"
                />
            </div>
            <div class="tw-mb-3">
                <div class="tw-font-bold tw-mb-3">
                    Phân loại
                    <span class="tw-text-red-600">(*)</span>
                    :
                </div>
                <p-dropdown
                    [(ngModel)]="supplierType.priority"
                    name="priority"
                    [disabled]="true"
                    [options]="priority"
                    optionLabel="value"
                    optionValue="id"
                    [style]="{ width: '100%' }"
                    [showClear]="true"
                ></p-dropdown>
            </div>

            <div class="tw-mb-3">
                <div class="tw-font-bold tw-mb-3">Ghi chú :</div>
                <textarea [(ngModel)]="supplierType.note" name="note" style="width: 100%" rows="4.5" cols="30" pInputTextarea [autoResize]="true"></textarea>
            </div>
        </p-panel>

        <br />
        <p-panel header="Tiêu chí đánh giá NCC mới" [toggleable]="true">
            <ng-template pTemplate="icons">
                <app-popup
                    header="Xuất tiêu chí đánh giá NCC mới"
                    severity="success"
                    label="Xuất Excel"
                    (onSubmit)="exportData($event, CriteriaType.NEW)"
                    typePopup="download"
                ></app-popup>
            </ng-template>
            <ng-container>
                <p-table [rowHover]="true" styleClass="p-datatable-striped" responsiveLayout="scroll" [value]="filterCriteriaByType(criteriaType.NEW)">
                    <ng-template pTemplate="header">
                        <tr>
                            <th>STT</th>
                            <th style="min-width: 9rem">
                                <div class="tw-flex">Tỉêu chí đánh giá</div>
                            </th>

                            <th style="min-width: 9rem">
                                <div class="tw-flex">Điều kiện</div>
                            </th>

                            <th style="min-width: 10rem">
                                <div class="tw-flex">Tiêu chuẩn</div>
                            </th>

                            <th style="min-width: 10rem">
                                <div class="tw-flex">Ghi chú</div>
                            </th>

                            <th style="min-width: 10rem">
                                <div class="tw-flex tw-justify-center"><span>Thao tác</span></div>
                            </th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-criteria let-rowIndex="rowIndex">
                        <tr>
                            <td>{{ rowIndex + 1 }}</td>
                            <td>{{ criteria.name }}</td>
                            <td>{{ getOperator(criteria.operator) }}</td>
                            <td>
                                {{
                                    (criteria.value != null || criteria.value != undefined ? criteria.value : '') +
                                        ' ' +
                                        (criteria.unit != null || criteria.unit != undefined ? criteria.unit : '')
                                }}
                            </td>
                            <td>{{ criteria.note }}</td>
                            <td style="max-width: 150px">
                                <div class="tw-flex tw-justify-center">
                                    <i
                                        class="pi pi-file-edit tw-text-2xl tw-text-green-400 tw-p-1 tw-cursor-pointer"
                                        title="Sửa"
                                        (click)="openDialog(criteria, getOriginalIndex(criteria), false, criteriaType.NEW)"
                                    ></i>
                                    <i
                                        class="pi pi-trash tw-text-2xl tw-text-red-500 tw-p-1 tw-cursor-pointer"
                                        title="Xóa"
                                        (click)="deleteObject(rowIndex, criteriaType.NEW)"
                                    ></i>
                                </div>
                            </td>
                        </tr>
                    </ng-template>
                </p-table>
                <div class="tw-mt-3">
                    <p-button
                        type="button"
                        label="Thêm"
                        icon="pi pi-plus"
                        severity="info"
                        size="small"
                        (onClick)="openDialog({}, -1, true, criteriaType.NEW); ngFormDialog.resetForm()"
                    ></p-button>
                </div>
            </ng-container>
        </p-panel>

        <br />

        <p-panel header="Tiêu chí đánh giá NCC trước mua" [toggleable]="true">
            <ng-template pTemplate="icons">
                <app-popup
                    header="Xuất tiêu chí đánh giá NCC trước mua"
                    severity="success"
                    label="Xuất Excel"
                    (onSubmit)="exportData($event, CriteriaType.BUY)"
                    typePopup="download"
                ></app-popup>
            </ng-template>
            <ng-container>
                <p-table [rowHover]="true" styleClass="p-datatable-striped" responsiveLayout="scroll" [value]="filterCriteriaByType(criteriaType.BUY)">
                    <ng-template pTemplate="header">
                        <tr>
                            <th>STT</th>
                            <th style="min-width: 9rem">
                                <div class="tw-flex">Tỉêu chí đánh giá</div>
                            </th>

                            <th style="min-width: 9rem">
                                <div class="tw-flex">Điều kiện</div>
                            </th>

                            <th style="min-width: 10rem">
                                <div class="tw-flex">Tiêu chuẩn</div>
                            </th>

                            <th style="min-width: 10rem">
                                <div class="tw-flex">Ghi chú</div>
                            </th>

                            <th style="min-width: 10rem">
                                <div class="tw-flex tw-justify-center"><span>Thao tác</span></div>
                            </th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-criteria let-rowIndex="rowIndex">
                        <tr>
                            <td>{{ rowIndex + 1 }}</td>
                            <td>{{ criteria.name }}</td>
                            <td>{{ getOperator(criteria.operator) }}</td>
                            <td>
                                {{
                                    (criteria.value != null || criteria.value != undefined ? criteria.value : '') +
                                        ' ' +
                                        (criteria.unit != null || criteria.unit != undefined ? criteria.unit : '')
                                }}
                            </td>
                            <td>{{ criteria.note }}</td>
                            <td style="max-width: 150px">
                                <div class="tw-flex tw-justify-center">
                                    <i
                                        class="pi pi-file-edit tw-text-2xl tw-text-green-400 tw-p-1 tw-cursor-pointer"
                                        title="Sửa"
                                        (click)="openDialog(criteria, getOriginalIndex(criteria), false, criteriaType.BUY)"
                                    ></i>
                                    <i
                                        class="pi pi-trash tw-text-2xl tw-text-red-500 tw-p-1 tw-cursor-pointer"
                                        title="Xóa"
                                        (click)="deleteObject(rowIndex, criteriaType.BUY)"
                                    ></i>
                                </div>
                            </td>
                        </tr>
                    </ng-template>
                </p-table>
                <div class="tw-mt-3">
                    <p-button
                        type="button"
                        label="Thêm"
                        icon="pi pi-plus"
                        severity="info"
                        size="small"
                        (onClick)="openDialog({}, -1, true, criteriaType.BUY); ngFormDialog.resetForm()"
                    ></p-button>
                </div>
            </ng-container>
        </p-panel>
    </div>
</form>

<form [formGroup]="criteriaForm" #ngFormDialog="ngForm" (ngSubmit)="saveDialogObject()">
    <p-dialog
        [header]="isEditingNewCriteria ? 'Thêm tiêu chí đánh giá' : 'Chỉnh sửa tiêu chí đánh giá'"
        [(visible)]="visibleDialog"
        [modal]="true"
        [breakpoints]="{ '1199px': '50vw', '575px': '30vw' }"
        [style]="{ width: '40vw' }"
        [draggable]="true"
        [resizable]="true"
    >
        <div style="border: 1.5px solid #bdbec0; padding-top: 0.5rem">
            <label class="tw-font-bold tw-mb-1 tw-m-2">Định nghĩa tiêu chí </label>
            <div style="padding: 1.25rem">
                <div class="tw-grid tw-gap-4 lg:tw-grid-cols-3 sm:tw-grid-cols-1">
                    <div class="tw-flex tw-flex-col">
                        <div>Tiêu đề<span class="tw-text-red-600">*</span>:</div>
                    </div>
                    <div class="tw-flex tw-flex-col tw-space-y-3 md:tw-col-span-2">
                        <input formControlName="name" style="width: 100%" pInputText type="text" class="flex-auto tw-border-2 tw-border-gray-300" />
                        <div
                            *ngIf="criteriaForm.get('name').errors && (criteriaForm.get('name').touched || ngFormDialog.submitted)"
                            class="text-red-600 tw-pt-1"
                        >
                            <div *ngIf="criteriaForm.get('name').errors['required']">Tỉêu đề là trường bắt buộc</div>
                        </div>
                    </div>
                </div>

                <div class="tw-grid tw-gap-4 lg:tw-grid-cols-3 sm:tw-grid-cols-1 tw-pt-2">
                    <div class="tw-flex tw-flex-col">
                        <div>Kiểu dữ liệu<span class="tw-text-red-600">*</span>:</div>
                    </div>
                    <div class="tw-flex tw-flex-col tw-space-y-3 md:tw-col-span-2">
                        <p-dropdown
                            formControlName="dataType"
                            [options]="[
                                { value: 0, name: 'Số' },
                                { value: 1, name: 'Văn bản' },
                            ]"
                            optionLabel="name"
                            optionValue="value"
                            [style]="{ width: '100%' }"
                            appendTo="body"
                            [showClear]="true"
                        >
                        </p-dropdown>
                        <div
                            *ngIf="criteriaForm.get('dataType').errors && (criteriaForm.get('dataType').touched || ngFormDialog.submitted)"
                            class="text-red-600 tw-pt-1"
                        >
                            <div *ngIf="criteriaForm.get('dataType').errors['required']">Kiểu dữ liệu là trường bắt buộc</div>
                        </div>
                    </div>
                </div>

                <div class="tw-grid tw-gap-4 lg:tw-grid-cols-3 sm:tw-grid-cols-1 tw-pt-2">
                    <div class="tw-flex tw-flex-col">
                        <div>Đơn vị</div>
                    </div>
                    <div class="tw-flex tw-flex-col tw-space-y-3 md:tw-col-span-2">
                        <input formControlName="unit" style="width: 100%" pInputText type="text" class="flex-auto tw-border-2 tw-border-gray-300" />
                        <div
                            *ngIf="criteriaForm.get('unit').errors && (criteriaForm.get('unit').touched || ngFormDialog.submitted)"
                            class="text-red-600 tw-pt-1"
                        >
                            <!-- <div *ngIf="criteriaForm.get('unit').errors['required']">Đơn vị là trường bắt buộc</div> -->
                            <div *ngIf="criteriaForm.get('unit').errors['maxlength']">Tối đa 50 ký tự</div>
                        </div>
                    </div>
                </div>

                <div class="tw-grid tw-gap-4 lg:tw-grid-cols-3 sm:tw-grid-cols-1 tw-pt-2">
                    <div class="tw-flex tw-flex-col">
                        <div>Ghi chú</div>
                    </div>
                    <div class="tw-flex tw-flex-col tw-space-y-3 md:tw-col-span-2">
                        <textarea formControlName="note" style="width: 100%" rows="2" cols="30" pInputTextarea></textarea>
                    </div>
                </div>
            </div>
        </div>

        <div style="border: 1.5px solid #bdbec0; margin-top: 1.5rem; padding-top: 0.5rem">
            <label class="tw-font-bold tw-mb-1 tw-m-2"> Tiêu chuẩn </label>
            <div class="tw-grid tw-gap-4 lg:tw-grid-cols-3 sm:tw-grid-cols-1 tw-pt-2" style="padding: 1.25rem">
                <div class="tw-flex tw-flex-col">
                    <div>Điều kiện</div>
                </div>
                <div class="tw-flex tw-flex-col tw-space-y-3 md:tw-col-span-2">
                    <p-dropdown
                        formControlName="operator"
                        [options]="operators"
                        optionLabel="name"
                        optionValue="value"
                        [style]="{ width: '100%' }"
                        appendTo="body"
                        [showClear]="true"
                    ></p-dropdown>
                    <div>
                        <input
                            *ngIf="valueType === null"
                            disabled="true"
                            style="width: 100%"
                            pInputText
                            type="text"
                            class="flex-auto tw-border-2 tw-border-gray-300"
                        />
                        <input
                            *ngIf="valueType === 1 || valueType === 2 || valueType === 3 || valueType === 4"
                            formControlName="value"
                            style="width: 100%"
                            pInputText
                            type="number"
                            class="flex-auto tw-border-2 tw-border-gray-300"
                        />
                        <input
                            *ngIf="valueType === 0 || valueType === 5"
                            formControlName="value"
                            style="width: 100%"
                            pInputText
                            type="text"
                            class="flex-auto tw-border-2 tw-border-gray-300"
                        />
                    </div>
                    <div *ngIf="criteriaForm.get('value').errors && (criteriaForm.get('value').touched || ngFormDialog.submitted)" class="text-red-600 tw-pt-1">
                        <div *ngIf="criteriaForm.get('value').errors['required']">Điều kiện là trường bắt buộc</div>
                    </div>
                </div>
            </div>
        </div>

        <ng-template pTemplate="footer">
            <div class="flex justify-content-end gap-2">
                <p-button label="Lưu" severity="success" type="submit" />
                <p-button label="Đóng" severity="secondary" (click)="visibleDialog = false" />
            </div>
        </ng-template>
    </p-dialog>
</form>

<!--<p-dialog
    *ngIf="supplierType.id"
    header="Xuất dữ liệu tiêu chí đánh giá "
    [(visible)]="visibleFileDialog"
    [modal]="true"
    [breakpoints]="{ '1199px': '50vw', '575px': '30vw' }"
    [style]="{ width: '40vw' }"
    #dialogFile
>
    <a
        href="javascript:void(0)"
        (click)="exportData()"
        class="tw-text-green-500"
    >Bấm vào đây để tải xuống</a
    >
    <ng-template pTemplate="footer">
        <div>
            <p-button
                label="Hủy"
                [text]="true"
                [raised]="true"
                severity="secondary"
                (click)="visibleFileDialog = false"
            ></p-button>
        </div>
    </ng-template>
</p-dialog>-->
