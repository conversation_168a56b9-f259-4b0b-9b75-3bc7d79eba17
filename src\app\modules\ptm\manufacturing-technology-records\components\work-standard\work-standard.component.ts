import { Component, EventEmitter, inject, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { FormGroupCustom } from '../../../../../shared/form-module/from-group.custom';
import { AbstractControl, FormArray, FormBuilder, FormGroup, FormsModule, Validators } from '@angular/forms';
import { LINE_PFMEA, PRODUCT_PROCESS_TYPE, TAB_TYPE } from '../../../../../models/constant/ptm';
import { FormComponent } from '../../../../../shared/form-module/form-base/form.component';
import { FormArrayCustom } from '../../../../../shared/form-module/from-array.custom';
import { PFMEA, PFMEADetail, ProcessFlow } from '../../../../../models/interface/ptm';
import { CommonModule } from '@angular/common';
import { InputTextModule } from 'primeng/inputtext';
import { InfoShareComponent } from '../info-share/info-share.component';
import { FormCustomModule } from '../../../../../shared/form-module/form.custom.module';
import { PanelModule } from 'primeng/panel';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { DropdownModule } from 'primeng/dropdown';
import { CheckboxModule } from 'primeng/checkbox';
import { OverlayPanel, OverlayPanelModule } from 'primeng/overlaypanel';
import { CalendarModule } from 'primeng/calendar';
import { numberValidator, numberValidatorWithDot } from '../../../../../utils/validator';
import { WorkStandard, WorkStandardDetailDto } from '../../../../../models/interface/ptm/work-standard';
import { PfmeaService } from '../../../../../services/ptm/pfmea/pfmea.service';
import { WorkStandardService } from '../../../../../services/ptm/work-standard/work-standard.service';
import { TrackChangesComponent } from '../track-changes/track-changes.component';
import { Observable, Subject, takeUntil } from 'rxjs';
import { PtmSharedService } from '../../../../../services/ptm/ptm-shared.service';
import { MessageService } from 'primeng/api';
import { TabSharedStateService } from '../../../../../services/ptm/manufacturing-technology-records/tab-shared-state/tab-shared-state.service';
import { environment } from '../../../../../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { FullscreenToggleDirective } from '../../../../../shared/directives/fullscreen-toggle.directive';

@Component({
    selector: 'app-work-standard',
    templateUrl: './work-standard.component.html',
    styleUrls: ['./work-standard.component.scss'],
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        InputTextModule,
        InfoShareComponent,
        FormCustomModule,
        PanelModule,
        TableModule,
        ButtonModule,
        DropdownModule,
        CheckboxModule,
        OverlayPanelModule,
        CalendarModule,
        TrackChangesComponent,
        FullscreenToggleDirective,
    ],
})
export class WorkStandardComponent implements OnInit, OnDestroy {
    // 🧠 INPUTS từ cha truyền xuống
    @Input() title: string = '';
    @Input() content: string = '';
    @Input() config: { type: string } = { type: 'default' };
    @Input() items: string[] = [];
    @Input() editable: boolean = true;
    @Input() currentProduct: any;
    @Input() isSaving: Observable<boolean>;
    @Input() isSubmitting: Observable<boolean>;
    @Input() isApproving: Observable<boolean>;

    // 📤 OUTPUTS emit về cha
    @Output() submitted = new EventEmitter<any>();
    @Output() changed = new EventEmitter<string>();

    workStandardService = inject(WorkStandardService);
    ptmSharedService = inject(PtmSharedService);
    messageService = inject(MessageService);
    tabSharedState = inject(TabSharedStateService);
    http = inject(HttpClient);

    columnErrorTable = [
        { field: 'operationDescription', header: 'Operation Details' },

        { field: 'in', header: 'In (s)', group: 'Normal Time' },
        { field: 'labor', header: 'Labor (s)', group: 'Normal Time' },
        { field: 'machine', header: 'Machine (s)', group: 'Normal Time' },
        { field: 'out', header: 'Out (s)', group: 'Normal Time' },

        { field: 'equip', header: 'Equip', group: 'Sub' },
        { field: 'qty1', header: 'Qty', group: 'Sub' },
        { field: 'materialPN', header: 'Consumable Material PN', group: 'Sub' },
        { field: 'materialDesc', header: 'Consumable description', group: 'Sub' },
        { field: 'qty2', header: 'Qty', group: 'Sub' },
        { field: 'unit', header: 'Unit', group: 'Sub' },

        { field: 'laborAllowance', header: 'Labor Allowance (%)', group: 'Time Rate' },
        { field: 'laborUtil', header: 'Labor Utilization (%)', group: 'Time Rate' },
        { field: 'machineUtil', header: 'Machine Utilization (%)', group: 'Time Rate' },
        { field: 'btpTram', header: 'BTP/Trạm', group: 'Time Rate' },

        { field: 'laborTime', header: 'Labor Time (s)', group: 'Standard Time' },
        { field: 'machineTime', header: 'Machine (s)', group: 'Standard Time' },
    ];

    columns = [
        { field: 'oc', header: 'OC', minWidth: '9rem' },
        { field: 'step', header: 'Next OC', minWidth: '9rem' },
        { field: 'nextOC', header: 'Operation Description', minWidth: '9rem' },
        { field: 'symbol', header: 'Công đoạn', minWidth: '9rem' },
    ];

    formGroup: FormGroup;

    @ViewChild('form', { static: false }) formComponent!: FormComponent;
    private destroy$ = new Subject<void>();
    mode: 'view' | 'create' | 'edit' = 'create';
    productVersionId: number;
    phase: number;
    productInstructionId: number;
    lineMap = LINE_PFMEA;
    clonedPfmeaErrors: { [index: number]: any[] } = {};
    nameTab: string;
    detailWorkStandard: any;
    showTrackDialog: boolean;
    selectedTab: number = 1;
    listOptionEquip = [];
    listOptionConsumable = [];
    downloadBaseUrl: string = environment.STORAGE_BASE_URL;

    getVisibleDetails(errors: AbstractControl | null | undefined): FormGroup[] {
        if (!errors || !(errors instanceof FormArray)) return [];
        return errors.controls.filter((ctrl) => ctrl.get('action')?.value !== 3) as FormGroup[];
    }

    get listWorkStandard(): FormArray {
        return this.formGroup.get('workStandards') as FormArray;
    }

    constructor(private fb: FormBuilder) {
        this.formGroup = this.fb.group({
            instructionId: [''],
            reviewers: [''],
            workStandards: this.fb.array([]),
        });
    }

    groupedWorkStandards: {
        line: number;
        lineName: string;
        items: FormArray; // mỗi nhóm là một FormArray
    }[] = [];

    ngOnInit(): void {
        this.handleListenerService();
        this.formGroup.get('instructionId').setValue(this.productInstructionId);
        this.nameTab = 'WS-' + this.currentProduct?.tradeName + '-' + this.currentProduct?.vnptManPn;
        // this.getDataWorkStandardUpdate(this.productInstructionId);
        this.getOptionEquip();
        this.getListOptionConsumable();
    }

    handleListenerService() {
        this.tabSharedState
            .getProductInstructionId$()
            .pipe(takeUntil(this.destroy$))
            .subscribe((id) => {
                this.productInstructionId = id;
                if (id) {
                    this.getDataWorkStandardUpdate(this.productInstructionId);
                }
            });

        this.tabSharedState
            .getMode$()
            .pipe(takeUntil(this.destroy$))
            .subscribe((mode) => {
                this.mode = mode;
            });

        this.tabSharedState
            .getProductVersionId$()
            .pipe(takeUntil(this.destroy$))
            .subscribe((verId) => {
                this.productVersionId = verId;
            });

        this.tabSharedState
            .getPhase$()
            .pipe(takeUntil(this.destroy$))
            .subscribe((phase) => {
                this.phase = phase;
            });
    }

    setWorkStandardsToForm(workStandards: any[]): void {
        this.listWorkStandard.clear();

        workStandards.forEach((ws) => {
            this.listWorkStandard.push(this.createWorkStandardGroup(ws));
        });
    }

    getDataWorkStandardUpdate(id) {
        console.log('chạy init');
        this.workStandardService.getWorkStandard(id).subscribe({
            next: (res) => {
                const dataInstructionInfo = { instructionInfo: res.instructionInfo };
                this.detailWorkStandard = { ...dataInstructionInfo };
                const reviewers = res?.instructionInfo?.reviewers?.map((r) => r.id).join(',');
                this.formGroup.get('reviewers').setValue(reviewers);
                this.setWorkStandardsToForm(res.workStandards);
                this.groupWorkStandardsByLine(res.workStandards);
            },
            error: (err) => {
                console.log(err);
            },
        });
    }

    updateWorkStandard(id, data) {
        const payload = {
            instructionId: id,
            workStandards: data,
            reviewerIds: this.formGroup.get('reviewers').value,
        };
        this.workStandardService.updateWorkStandard(id, payload).subscribe({
            next: (res) => {
                if (this.productInstructionId !== 0) {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'success',
                        summary: 'Thành công',
                        detail: 'Cập nhật Work Standard thành công',
                    });
                } else {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'success',
                        summary: 'Thành công',
                        detail: 'Tạo Work Standard thành công',
                    });
                }
                this.getDataWorkStandardUpdate(this.productInstructionId);
            },
            error: (err) => {
                console.log(err);
            },
        });
    }

    getOptionEquip() {
        this.ptmSharedService.getListOptionEquip().subscribe({
            next: (res) => {
                this.listOptionEquip = res;
            },
            error: (err) => {
                console.log(err);
            },
        });
    }

    getListOptionConsumable() {
        this.ptmSharedService.getListOptionConsumable().subscribe({
            next: (res) => {
                this.listOptionConsumable = res;
            },
            error: (err) => {
                console.log(err);
            },
        });
    }

    handleChangeConsumable(event, index, row: FormGroup) {
        const consumable = this.listOptionConsumable.find((item) => item.id === event.value);
        row.get('consumableDescription').setValue(consumable.descriptionPn);
    }

    createWorkStandardGroup(data: WorkStandard): FormGroup {
        return this.fb.group({
            id: [data.id],
            instructionInfoId: [data.instructionId],
            processFlowId: [data.processFlowId],
            oc: [data.oc],
            nextOc: [data.nextOc],
            od: [data.od],
            pfmeaId: [data.pfmeaId],
            line: [data.line],
            workStandardDetails: this.fb.array((data.workStandardDetailDtos || []).map((d) => this.createWorkStandardDetailGroup(d))),
            laborUtilization: [data.laborUtilization, [numberValidatorWithDot()]],
            machineUtilization: [data.machineUtilization, [numberValidatorWithDot()]],
            laborAllowance: [data.laborAllowance, [numberValidatorWithDot()]],
            btp: [data.btp],
            laborTime: [data.laborTime],
            machine: [data.machine],
            action: [data.action],
        });
    }

    createWorkStandardDetailGroup(detail: WorkStandardDetailDto): FormGroup {
        return this.fb.group({
            od: [detail.od],
            inTime: [detail.inTime, [numberValidatorWithDot()]],
            laborTime: [detail.laborTime, [numberValidatorWithDot()]],
            machineTime: [detail.machineTime, [numberValidatorWithDot()]],
            outTime: [detail.outTime, [numberValidatorWithDot()]],
            equipId: [detail.equipId],
            equipQty: [detail.equipQty],
            consumableMaterialPnId: [detail.consumableMaterialPnId],
            consumableDescription: [detail.consumableDescription],
            qty: [detail.qty, [numberValidatorWithDot()]],
            unit: [detail.unit],
            action: [detail.action],
        });
    }

    createWorkStandardDetailRow(): FormGroup {
        return this.fb.group({
            operationDescription: [''],
            in: [''],
            labor: [''],
            machine: [''],
            out: [''],
            equip: [null],
            equipQty: [''],
            consumablePn: [null],
            consumableDesc: [''],
            consumableQty: [''],
            unit: [''],
            laborAllowance: [''],
            laborUtil: [''],
            machineUtil: [''],
            btpTram: [''],
        });
    }

    groupWorkStandardsByLine(data: any[]): void {
        const groupedMap = new Map<number, { line: number; lineName: string; items: FormGroup[] }>();

        data.forEach((item) => {
            const formGroup = this.fb.group({
                id: [item.id],
                instructionInfoId: [item.instructionId],
                processFlowId: [item.processFlowId],
                oc: [item.oc],
                nextOc: [item.nextOc],
                od: [item.od],
                pfmeaId: [item.pfmeaId],
                line: [item.line],
                lineName: [item.lineName],
                workStandardDetails: this.fb.array((item.workStandardDetailDtos || []).map((d) => this.createWorkStandardDetailGroup(d))), // nếu có nested array
                laborAllowance: [item.laborAllowance, [numberValidatorWithDot()]],
                laborUtilization: [item.laborUtilization, [numberValidatorWithDot()]],
                machineUtilization: [item.machineUtilization, [numberValidatorWithDot()]],
                btp: [item.btp, [numberValidatorWithDot()]],
                laborTime: [item.laborTime],
                machine: [item.machine],
            });

            if (!groupedMap.has(item.line)) {
                groupedMap.set(item.line, {
                    line: item.line,
                    lineName: item.lineName,
                    items: [],
                });
            }

            groupedMap.get(item.line)!.items.push(formGroup);
        });

        this.groupedWorkStandards = Array.from(groupedMap.values()).map((group) => ({
            line: group.line,
            lineName: group.lineName,
            items: this.fb.array(group.items),
        }));

        this.listenToWorkStandardChanges();
    }

    initRowErrorTable(): FormGroup {
        return new FormGroupCustom(this.fb, {
            od: [''],
            inTime: ['', [numberValidatorWithDot()]],
            laborTime: ['', [numberValidatorWithDot()]],
            machineTime: ['', [numberValidatorWithDot()]],
            outTime: ['', [numberValidatorWithDot()]],
            equipId: [null],
            equipQty: [''],
            consumableMaterialPnId: [null],
            consumableDescription: [''], // readonly/hiển thị mô tả
            qty: ['', [numberValidatorWithDot()]],
            unit: [''],
            action: [1],
        });
    }

    ngOnDestroy(): void {
        console.log('🧹 [ProcessFlowComponent] Unmounted');
    }

    onChange(newVal: string) {
        this.changed.emit(newVal);
    }

    onSubmit(value: ProcessFlow): void {
        console.log(value, value.listProcessFlow);
    }

    openPanel(groupIndex: number, rowIndex: number, overlay: OverlayPanel, event: MouseEvent): void {
        const workStandardForm = this.groupedWorkStandards[groupIndex].items.at(rowIndex) as FormGroup;
        const errors = workStandardForm.get('workStandardDetails') as FormArray;

        // Deep copy để rollback nếu cần
        if (!this.clonedPfmeaErrors) {
            this.clonedPfmeaErrors = {};
        }

        const key = `${groupIndex}_${rowIndex}`;
        this.clonedPfmeaErrors[key] = errors.value.map((e) => ({ ...e }));

        setTimeout(() => {
            overlay.toggle(event); // Hiển thị overlay panel
        }, 0);
    }

    addRow(groupIndex: number, rowIndex: number): void {
        const workStandardForm = this.groupedWorkStandards[groupIndex].items.at(rowIndex) as FormGroup;
        const details = workStandardForm.get('workStandardDetails') as FormArray;

        const visibleControls = details.controls.filter((c) => c.get('action')?.value !== 3);

        // Tạo dòng mới
        const newRow = this.initRowErrorTable();

        // Thêm dòng mới vào ngay sau các dòng đang hiển thị
        details.insert(visibleControls.length, newRow);
    }

    removeRow(groupIndex: number, itemIndex: number, rowIndex: number): void {
        const group = this.groupedWorkStandards[groupIndex].items.at(itemIndex);
        const details = group.get('workStandardDetails') as FormArray;

        const visibleRows = details.controls.filter((ctrl) => ctrl.get('action')?.value !== 3);

        const actualRow = visibleRows[rowIndex]; // dòng hiển thị tương ứng

        if (!actualRow) return;

        const actualIndex = details.controls.indexOf(actualRow);

        if (actualIndex !== -1) {
            const id = actualRow.get('id')?.value;
            if (id) {
                // Có id => cập nhật action = 3
                actualRow.get('action')?.setValue(3);
            } else {
                // Không có id => xóa hẳn khỏi FormArray
                details.removeAt(actualIndex);
            }
        }
    }

    getData() {
        return this.formGroup.getRawValue();
    }

    validateProcess() {
        // console.log('validateProcess', this.formGroup.valid);
        return this.formGroup.valid;
    }

    errorProcess() {
        console.log('errorProcess');
    }

    handleSubmit() {
        // Gọi callback nếu có

        // Emit event nếu cha lắng nghe
        this.submitted.emit({
            content: this.content,
            config: this.config,
            items: this.items,
        });

        const allWorkStandards = this.groupedWorkStandards.flatMap((group) => group.items.value);
        const updatedWorkStandards = allWorkStandards.map(({ oc, nextOc, od, line, lineName, ...rest }) => ({
            ...rest,
        }));
        this.updateWorkStandard(this.productInstructionId, updatedWorkStandards);
    }

    handleComplete() {
        const payload = {
            tabType: TAB_TYPE.workStandard,
            approvalStatus: 8,
        };
        this.ptmSharedService.confirm(payload, this.productInstructionId).subscribe({
            next: (res) => {
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'success',
                    summary: 'Thành công',
                    detail: 'Phê duyệt thành công',
                });
            },
            error: () => {},
        });
    }

    handleReject() {
        const payload = {
            tabType: TAB_TYPE.workStandard,
            approvalStatus: 4,
        };
        this.ptmSharedService.confirm(payload, this.productInstructionId).subscribe({
            next: (res) => {
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'success',
                    summary: 'Thành công',
                    detail: 'Từ chối thành công',
                });
            },
            error: () => {},
        });
    }

    handlePreview() {
        const payload = {
            tabType: TAB_TYPE.workStandard,
        };
        this.ptmSharedService.preview(payload, this.productInstructionId).subscribe({
            next: (res) => {
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'success',
                    summary: 'Thành công',
                    detail: 'Gửi preview thành công',
                });
            },
            error: () => {},
        });
    }

    save() {}

    savePanel(groupIndex: number, rowIndex: number, overlay: OverlayPanel): void {
        const workStandardForm = this.groupedWorkStandards[groupIndex].items.at(rowIndex) as FormGroup;
        const details = workStandardForm.get('workStandardDetails') as FormArray;

        if (details.invalid) {
            details.markAllAsTouched();
            return;
        }

        const payload = details.value;
        console.log(`💾 Lưu workStandardDetails của group ${groupIndex}, row ${rowIndex}:`, payload);

        overlay.hide(); // ✅ Đóng overlay sau khi lưu
    }

    cancelPanel(groupIndex: number, rowIndex: number, overlay: OverlayPanel): void {
        const key = `${groupIndex}_${rowIndex}`;
        const workStandardForm = this.groupedWorkStandards[groupIndex].items.at(rowIndex) as FormGroup;
        const details = workStandardForm.get('workStandardDetails') as FormArray;

        // Xoá toàn bộ dữ liệu hiện tại
        details.clear();

        // Lấy dữ liệu cũ từ bản sao
        const oldErrors = this.clonedPfmeaErrors[key] || [];

        for (const err of oldErrors) {
            details.push(this.fb.group({ ...err })); // hoặc this.createPFMEAErrorGroup(err) nếu có
        }

        overlay.hide(); // Đóng panel
    }

    updateTotalTimes(groupIndex: number, itemIndex: number): void {
        console.log('chạy hàm này');
        const item = (this.groupedWorkStandards[groupIndex].items as FormArray).at(itemIndex);
        if (!item) return;

        const totalLabor = this.calculateGroupLaborTime(item);
        const totalMachine = this.calculateGroupMachineTime(item);

        item.get('laborTime')?.setValue(totalLabor, { emitEvent: false });
        item.get('machine')?.setValue(totalMachine, { emitEvent: false });
    }

    listenToWorkStandardChanges(): void {
        this.groupedWorkStandards.forEach((groupWrapper, groupIndex) => {
            const groupArray = groupWrapper.items as FormArray;

            groupArray.controls.forEach((group: AbstractControl, itemIndex: number) => {
                const details = group.get('workStandardDetails') as FormArray;

                // Lắng nghe các trường dùng chung trong nhóm
                ['laborAllowance', 'laborUtil', 'machineUtil', 'btpTram'].forEach((field) => {
                    const control = group.get(field);
                    if (control) {
                        control.valueChanges.subscribe(() => this.updateTotalTimes(groupIndex, itemIndex));
                    }
                });

                // Nếu đã có sẵn workStandardDetails, lắng nghe từng dòng
                details.controls.forEach((row: AbstractControl) => {
                    ['inTime', 'laborTime', 'outTime', 'machineTime'].forEach((field) => {
                        const control = row.get(field);
                        if (control) {
                            control.valueChanges.subscribe(() => this.updateTotalTimes(groupIndex, itemIndex));
                        }
                    });
                });

                // Lắng nghe khi thêm/xoá dòng
                details.valueChanges.subscribe(() => this.updateTotalTimes(groupIndex, itemIndex));
            });
        });
        console.log('nghe ');
    }

    calculateGroupLaborTime(item: AbstractControl): number {
        const details = item.get('workStandardDetails') as FormArray;
        if (!details || details.length === 0) return 0;

        const allowance = +item.get('laborAllowance')?.value || 0;
        const utilization = +item.get('laborUtil')?.value || 1;
        const btp = +item.get('btpTram')?.value || 1;

        // Lọc các dòng có action !== 3
        const visibleDetails = details.controls.filter((ctrl) => ctrl.get('action')?.value !== 3);

        const sum = visibleDetails.reduce((total, row) => {
            const inTime = +row.get('inTime')?.value || 0;
            const laborTime = +row.get('laborTime')?.value || 0;
            const outTime = +row.get('outTime')?.value || 0;
            return total + inTime + laborTime + outTime;
        }, 0);

        const result = (sum * (1 + allowance / 100)) / (utilization || 1) / (btp || 1);
        return isNaN(result) ? 0 : +result.toFixed(1);
    }

    calculateGroupMachineTime(item: AbstractControl): number {
        const details = item.get('workStandardDetails') as FormArray;
        if (!details || details.length === 0) return 0;

        const utilization = +item.get('machineUtil')?.value || 1;
        const btp = +item.get('btpTram')?.value || 1;

        const visibleDetails = details.controls.filter((ctrl) => ctrl.get('action')?.value !== 3);
        const hasMachine = visibleDetails.some((ctrl) => !!ctrl.get('machineTime')?.value);

        if (!hasMachine) return 0;

        const sum = visibleDetails.reduce((total, row) => {
            const inTime = +row.get('inTime')?.value || 0;
            const machineTime = +row.get('machineTime')?.value || 0;
            const outTime = +row.get('outTime')?.value || 0;
            return total + inTime + machineTime + outTime;
        }, 0);

        const result = sum / (utilization || 1) / (btp || 1);
        return isNaN(result) ? 0 : +result.toFixed(1);
    }

    calculateWorkStandardGroupTotals(groupIndex: number): { totalLabor: number; totalMachine: number } {
        const group = this.groupedWorkStandards[groupIndex];
        if (!group || !group.items) return { totalLabor: 0, totalMachine: 0 };

        const items = group.items as FormArray;

        let totalLabor = 0;
        let totalMachine = 0;

        items.controls.forEach((item: AbstractControl) => {
            const labor = +item.get('laborTime')?.value || 0;
            const machine = +item.get('machine')?.value || 0;

            totalLabor += labor;
            totalMachine += machine;
        });

        return {
            totalLabor: +totalLabor.toFixed(1),
            totalMachine: +totalMachine.toFixed(1),
        };
    }

    calculateGroupTotalMachineTime(groupIndex: number): string {
        const group = this.groupedWorkStandards[groupIndex];

        let total = 0;

        group.items.controls.forEach((item: AbstractControl) => {
            const details = item.get('workStandardDetails') as FormArray;

            details.controls.forEach((detail) => {
                const machine = +detail.get('machine')?.value || 0;
                const util = +detail.get('machineUtil')?.value || 0;

                const result = machine / (util || 1);
                total += isNaN(result) ? 0 : result;
            });
        });

        return total.toFixed(1); // Làm tròn 1 chữ số sau dấu phẩy
    }

    getColumnGroups() {
        const grouped = new Map<string | null, any[]>();

        for (const col of this.columnErrorTable) {
            const key = col.group || null;
            if (!grouped.has(key)) grouped.set(key, []);
            grouped.get(key).push(col);
        }

        return Array.from(grouped.entries()).map(([name, headers]) => ({
            name,
            headers,
            colSpan: headers.length,
        }));
    }

    calculateLaborTime(groupIndex: number, rowIndex: number, detailIndex: number): string {
        const workStandardForm = this.groupedWorkStandards[groupIndex].items.at(rowIndex) as FormGroup;
        const details = workStandardForm.get('workStandardDetails') as FormArray;

        const detail = details.at(detailIndex) as FormGroup;
        const labor = +detail.get('labor')?.value || 0;
        const allowance = +detail.get('laborAllowance')?.value || 0;
        const util = +detail.get('laborUtil')?.value || 0;

        const result = (labor * (1 + allowance / 100)) / (util || 1);
        return isNaN(result) ? '-' : result.toFixed(2);
    }

    calculateMachineTime(groupIndex: number, rowIndex: number, detailIndex: number): string {
        const workStandardForm = this.groupedWorkStandards[groupIndex].items.at(rowIndex) as FormGroup;
        const details = workStandardForm.get('workStandardDetails') as FormArray;

        const detail = details.at(detailIndex) as FormGroup;
        const machine = +detail.get('machine')?.value || 0;
        const util = +detail.get('machineUtil')?.value || 0;

        const result = machine / (util || 1);
        return isNaN(result) ? '-' : result.toFixed(2);
    }

    handleApproverChange(event: any) {
        const reviewers = event.map((item: any) => item.id).join(',');
        console.log(reviewers);
        this.formGroup.get('reviewers').setValue(reviewers);
    }

    getLineLabel(value: number): string {
        const found = this.lineMap.find((line) => line.value === value);
        return found ? found.label : '';
    }

    openTrackDialog(): void {
        this.showTrackDialog = true;
        this.selectedTab = TAB_TYPE.workStandard;
    }

    handleCloseTrackDialog(): void {
        this.showTrackDialog = false;
    }

    exportWorkStandard() {
        this.workStandardService.exportWorkStandard(this.productInstructionId).subscribe({
            next: (res) => {
                const url = `${this.downloadBaseUrl}/${res}`;

                this.http.get(url, { responseType: 'blob' }).subscribe((blob) => {
                    const objectUrl = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = objectUrl;
                    a.download = `${this.nameTab}.xlsx`;
                    a.click();
                    URL.revokeObjectURL(objectUrl);
                });
            },
            error: () => {},
        });
    }

    exportWorkStandardLine(lineId: number, lineName: string) {
        this.workStandardService.exportWorkStandardLine(this.productInstructionId, lineId).subscribe({
            next: (res) => {
                const url = `${this.downloadBaseUrl}/${res}`;

                this.http.get(url, { responseType: 'blob' }).subscribe((blob) => {
                    const objectUrl = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = objectUrl;
                    a.download = `${this.nameTab}.${lineName}.xlsx`;
                    a.click();
                    URL.revokeObjectURL(objectUrl);
                });
            },
            error: () => {},
        });
    }
}
