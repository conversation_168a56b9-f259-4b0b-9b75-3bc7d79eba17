import { Component, Input, OnInit, SimpleChanges, OnChanges, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';

import { PanelModule } from 'primeng/panel';
import { ButtonModule } from 'primeng/button';
import { ProductFileService } from 'src/app/services/pms/product-file/product-file.service';
import { ProductDetail, ProductDocumentInput, ColumnTableSection, ProductRecordVersion } from 'src/app/models/interface/pms';

import { ConfirmationService } from 'primeng/api';
import { ActivatedRoute, Router } from '@angular/router';
import { AlertService } from 'src/app/shared/services/alert.service';
import { EventBusService } from 'src/app/services/eventBus.service';
import { ParamsTable } from 'src/app/shared/table-module/table.common.service';
import { ProductSoftwareService } from 'src/app/services/pms/production-software/product-software.service';
import { Subscription, take } from 'rxjs';
import { EditTableSectionComponent } from 'src/app/modules/pms/components/edit-table-section/edit-table-section.component';
interface SectionConfig {
    category: string;
    title: string;
    columns: Array<ColumnTableSection>;
    rows: any[];
}
@Component({
    selector: 'app-production-profile',
    standalone: true,
    imports: [CommonModule, EditTableSectionComponent, PanelModule, ButtonModule],
    templateUrl: './production-profile.component.html',
    styleUrls: ['./production-profile.component.scss'],
})
export class ProductionProfileComponent implements OnInit, OnChanges, OnDestroy {
    @Input() isCheckNote: boolean = false;
    @Input() data!: any;
    @Input() version!: ProductRecordVersion;
    @Input() currentProduct!: ProductDetail;
    @Input() mode!: string;
    @Input() productSoftwareOptions: any[] = [];
    private requestSub!: Subscription;
    isEditMode: boolean = false;
    reloadTrigger = 0;
    sections: SectionConfig[] = [
        {
            category: '0',
            title: 'Quy trình sản xuất',
            columns: [
                { header: 'Tài liệu', field: 'document', type: 'text' },
                { header: 'File', field: 'file', type: 'file' },
                { header: 'Tên tài liệu', field: 'name', type: 'readonly' },
                { header: 'MD5', field: 'md5', type: 'readonly' },
                { header: 'Build time', field: 'buildtime', type: 'readonly' },
                { header: 'Version', field: 'version', type: 'readonly' },
            ],
            rows: [
                // {
                //     document: '',
                //     file: null,
                //     name: '',
                //     md5: '',
                //     buildtime: '',
                //     version: '',
                //     isDefault: 1,
                // },
            ],
        },
        {
            category: '1',
            title: 'Công cụ dụng cụ sản xuất phần mềm',
            columns: [
                { header: 'Tài liệu', field: 'document', type: 'text' },
                { header: 'File', field: 'file', type: 'file' },
                { header: 'Tên tài liệu', field: 'name', type: 'readonly' },
                { header: 'MD5', field: 'md5', type: 'readonly' },
                { header: 'Build time', field: 'buildtime', type: 'readonly' },
                { header: 'Version', field: 'version', type: 'readonly' },
            ],
            rows: [],
        },
        {
            category: '2',
            title: 'Phần mềm hỗ trợ sản xuất',
            columns: [
                {
                    header: 'Tên phần mềm',
                    field: 'softwareResource',
                    type: 'select-one',
                    minLength: 2, // chỉ gọi API khi nhập >= 2 ký tự
                    options: [], // để parent fill
                    totalRecords: 0, // để parent fill
                    optionLabel: 'name',
                    optionValue: 'value',
                    placeholder: 'Chọn tên..',
                    // quan trọng: bind đúng this để load query
                    lazyLoadFn: (q: string) => this.loadSoftwareOptions(q),
                },
                {
                    header: 'Version',
                    field: 'version',
                    type: 'select',
                    optionLabel: 'shortName',
                    optionValue: 'id',
                    placeholder: 'Chọn version',
                },
                { header: 'File', field: 'file', type: 'readonly' },
                { header: 'MD5', field: 'md5', type: 'readonly' },
                { header: 'Build time', field: 'buildtime', type: 'readonly' },
            ],
            rows: [],
        },
    ];
    constructor(
        private productFileService: ProductFileService,
        private confirmationService: ConfirmationService,
        private productSoftwareService: ProductSoftwareService,
        private alertService: AlertService,
        private router: Router,
        private route: ActivatedRoute,
        private bus: EventBusService,
    ) {}
    ngOnInit(): void {
        this.buildSections();
        const firstSegment = this.route.snapshot.url[0]?.path;
        this.isEditMode = firstSegment === 'edit';
        this.requestSub = this.bus
            .on<void>('REQUEST_PAYLOAD_ALL')
            .pipe(take(1)) // chỉ xử lý 1 lần rồi tự huỷ
            .subscribe(() => {
                const payload = this.handleSave(); // trả về mảng payload
                this.bus.emit('RESPONSE_PAYLOAD_PRODUCTION', payload);
            });
    }
    ngOnDestroy(): void {
        this.requestSub.unsubscribe();
    }
    ngOnChanges(changes: SimpleChanges) {
        if (changes['currentProduct'] && changes['currentProduct'].currentValue) {
            // mỗi khi parent truyền vào giá trị mới

            if (this.currentProduct.productVersions && this.currentProduct.productVersions.length > 0) {
                this.isCheckNote = true;
            } else {
                this.isCheckNote = false;
            }
        }
        if (changes['data'] && this.data) {
            this.updateRows();
        }
    }
    loadSoftwareOptions(query: string) {
        const sec = this.sections.find((s) => s.category === '2');
        if (!sec) return;

        const col = sec.columns.find((c) => c.field === 'softwareResource') as any;
        if (!col) return;

        col.loading = true;

        const params: ParamsTable = {
            native: '',
            pageable: '&page=0&size=10000',
        };

        this.productSoftwareService.getPage(params).subscribe({
            next: (res) => {
                col.options = (res.body || []).map((item: any) => ({
                    name: item.name?.split('_')[0] || '',
                    value: item.id,
                    rootId: item.rootId,
                    // Thêm các field khác nếu cần
                }));
            },
            error: (err) => {
                console.error('Error loading software options:', err);
                col.options = [];
            },
            complete: () => {
                col.loading = false;
            },
        });
    }
    handleSoftwareSelected(event: { rowIndex: number; field: string; rootId: number }, section: SectionConfig) {
        this.productSoftwareService.getVersionsByRootId(event.rootId).subscribe((versions) => {
            const versionOptions = versions.map((v: any) => ({
                label: v.name,
                value: v.id,
                md5: v.md5,
                file: v.name,
                buildTime: v.buildTime,
            }));
            this.productSoftwareOptions = versionOptions;

            // Tìm đúng section
            const section = this.sections.find((s) => s.title === 'Phần mềm hỗ trợ sản xuất');
            const versionCol = section?.columns.find((c) => c.field === 'version');

            if (versionCol) {
                versionCol.options = versionOptions;
            }
        });
    }
    private updateRows() {
        this.sections.forEach((section) => {
            const docs = this.data[section.category] || [];

            if (docs && docs.length > 0) {
                if (section.rows.length === 0) {
                    section.rows = docs.map((doc) => {
                        // Phần chung
                        const base = {
                            id: doc.id,
                            document: doc.description || '',
                            name: doc.fileName ? doc.fileName.split('_')[1] : '',
                            md5: doc.md5 || '',
                            buildtime: doc.buildTime || '',
                            version: doc.versionName || '',
                            isDefault: doc.isDefault || 0,
                            fileName: doc.fileName || '',
                            note: doc.note || '',
                        };

                        // Nếu category === '2', file lấy từ fileName và thêm filePath
                        if (section.category === '2') {
                            return {
                                ...base,
                                file: doc.fileName || '',
                                filePath: doc.filePath || '',
                                softwareVersionName: doc?.softwareVersionName || '',
                                softwareName: doc?.softwareName || '',
                                softwareId: doc?.softwareId || '',
                                documentType: doc?.documentType || 2,
                            };
                        }

                        // Với category khác, file lấy từ filePath, không có filePath riêng
                        return {
                            ...base,
                            file: doc.filePath ?? null,
                        };
                    });
                } else {
                    const newRows = docs.map((doc, index) => {
                        const oldRow = section.rows[index];

                        // Phần chung
                        const base = {
                            id: oldRow ? oldRow.id : null,
                            document: doc.description || '',
                            name: doc.fileName ? doc.fileName.split('_')[1] : '',
                            md5: doc.md5 || '',
                            buildtime: doc.buildTime || '',
                            version: doc.versionName || '',
                            isDefault: doc.isDefault || 0,
                            fileName: doc.fileName || '',
                            note: doc.note || '',
                        };

                        // Nếu category === '2', file lấy từ fileName và thêm filePath
                        if (section.category === '2') {
                            return {
                                ...base,
                                file: doc.fileName || '',
                                filePath: doc.filePath || '',
                                softwareVersionName: doc?.softwareVersionName || '',
                                softwareName: doc?.softwareName || '',
                                softwareId: doc?.softwareId || '',
                                documentType: doc?.documentType || 2,
                            };
                        }

                        // Với category khác, file lấy từ filePath, không có filePath riêng
                        return {
                            ...base,
                            file: doc.filePath ?? null,
                        };
                    });
                    // Gán lại section.rows
                    section.rows = newRows;
                }
            }
        });
        this.reloadTrigger++;
    }

    private buildSections(): void {
        if (!this.isCheckNote) {
            // Không cần cột note → giữ nguyên
            this.sections = [...this.sections];
            return;
        }

        // Khi cần thêm cột Note
        this.sections = this.sections.map((section) => ({
            ...section,
            // chèn cột Ghi chú vào cuối mảng columns
            columns: [...section.columns, { header: 'Ghi chú', field: 'note', type: 'text' }],
            // clone mỗi row và khởi tạo luôn thuộc tính `note`
            // rows: section.rows.map((row) => ({
            //     ...row,
            //     note: '', // hoặc gán giá trị cụ thể nếu có
            // })),
        }));
    }
    // onSaveSectionRow(section: SectionConfig, e: { index: number; row: any; done: (updated: any) => void }) {
    //     const { index, row, done } = e;

    //     // 1) Chuẩn bị payload cho tạo mới
    //     const createDto: CreateProductDocumentDto = {
    //         versionId: this.version.id,
    //         description: row.document,
    //         type: 2, // tab hồ sơ sản xuất
    //         category: +section.category,
    //         documentType: 2,
    //         fileName: row.name || '',
    //         filePath: row.filePath || '',
    //         md5: row.md5 || '',
    //         buildTime: Number(row.buildtime) || 0,
    //         versionName: row.version || '',
    //         note: row.note || '',
    //     };

    //     // 2) Nếu đã có row.id → build Update DTO
    //     let obs$;
    //     if (row.id) {
    //         const updateDto: UpdateProductDocumentDto = {
    //             description: createDto.description,
    //             fileName: createDto.fileName,
    //             filePath: createDto.filePath,
    //             md5: createDto.md5,
    //             buildTime: createDto.buildTime,
    //             versionName: createDto.versionName,
    //             note: createDto.note,
    //         };
    //         obs$ = this.productFileService.updateDocumentRow(row.id, updateDto);
    //     } else {
    //         // 3) Ngược lại call create
    //         obs$ = this.productFileService.createDocumentRow(createDto);
    //     }

    //     // 4) Subscribe chung
    //     obs$.subscribe({
    //         next: (updated) => {
    //             this.alertService.success('Thành công', 'Lưu bản ghi thành công');

    //             // cập nhật mảng JS
    //             section.rows[index] = { ...section.rows[index], ...updated };

    //             // callback để con patch FormGroup
    //             done(updated);
    //         },
    //         error: (err) => {
    //             this.alertService.error('Lỗi', 'Lưu bản ghi thất bại');
    //             console.error('save error:', err);
    //         },
    //     });
    // }

    onDeleteRow(e: { index: number; row: any; done: () => void }) {
        const { index, row, done } = e;
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.productFileService.delete(row.id).subscribe({
                    next: () => {
                        this.alertService.success('Thành công', 'Xóa bản ghi thành công');
                        done();
                        // reload dữ liệu
                    },
                    error: (err) => {
                        this.alertService.error('Lỗi', 'Xóa bản ghi thất bại');
                        console.error('Delete error:', err);
                    },
                });
            },
        });
    }

    private handleSave(): ProductDocumentInput[] {
        const payload: ProductDocumentInput[] = [];

        this.sections.forEach((section, sectionIndex) => {
            const isSoftwareSection = section.category === '2';

            section.rows.forEach((row) => {
                // documentType: nếu đúng category 2 (Phần mềm hỗ trợ sản xuất) → 2, còn lại → 1
                const documentType = isSoftwareSection ? 2 : 1;

                // Phần chung cho mọi trường hợp
                const common: Omit<ProductDocumentInput, 'fileName' | 'filePath' | 'description' | 'versionName'> = {
                    id: row.id || 0,
                    versionId: this.version?.id || 0,
                    type: 2, // Tab hồ sơ thiết kế (giữ nguyên như trước)
                    category: +section.category,
                    md5: row.md5 || '',
                    buildTime: row.buildtime || 0,
                    note: row.note || '',
                    isDefault: row.isDefault || 0,
                    documentType,
                };

                let doc: ProductDocumentInput;

                if (isSoftwareSection) {
                    // Trường hợp Phần mềm hỗ trợ sản xuất: sử dụng thông tin từ row.version (object)
                    doc = {
                        ...common,
                        description: 'Phần mềm hỗ trợ sản xuất',
                        fileName: row.version?.name || '',
                        filePath: `${row.version?.id ?? ''}`,
                        versionName: row?.version?.shortName || null,
                    };
                } else {
                    // Trường hợp thông thường: dùng thông tin row.document, row.fileName, row.filePath, row.version
                    doc = {
                        ...common,
                        description: row.document || '',
                        fileName: row.fileName || '',
                        filePath: row.filePath || '',
                        versionName: null,
                    };
                }

                payload.push(doc);
            });
        });

        return payload;
    }

    handleCancel() {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn Hủy',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.router.navigate(['/pms/product-file'], {
                    replaceUrl: true, // tránh tạo thêm history entry
                });
            },
        });
    }
    handleDeleteVersion(v: ProductRecordVersion): void {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.productFileService.deleteVersion(this.version.id).subscribe({
                    next: () => {
                        this.alertService.success('Thành công', 'Xóa thành công');
                        this.router.navigate(['/pms/product-file'], {
                            replaceUrl: true, // tránh tạo thêm history entry
                        });
                    },
                    error: (err) => {
                        this.alertService.error('Lỗi', 'Xóa thất bại');
                        console.error('Delete error:', err);
                    },
                });
            },
        });
    }
    onSectionRowsChange(section: SectionConfig, newRows: any[]) {
        section.rows = newRows;
    }
}
