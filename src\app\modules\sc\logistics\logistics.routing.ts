import { canAuthorize } from '../../../core/auth/auth.guard';

export const LogisticsRouting = {
    path: '',
    title: 'Quản lý nhà cung cấp dịch vụ Logistics',
    children: [
        {
            path: 'logistics',
            children: [
                {
                    path: '',
                    title: 'Dịch vụ Logistics',
                    data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_logistics_view'] },
                    canActivate: [canAuthorize],
                    loadComponent: () => import('./supplier/list/list.component').then((c) => c.ListComponent),
                },
                {
                    path: 'create',
                    title: 'Tạo nhà cung cấp dịch vụ Logistics',
                    canActivate: [canAuthorize],
                    data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_logistics_edit'] },
                    loadComponent: () => import('./supplier/detail/logistics-supplier.edit.component').then((c) => c.LogisticsEditComponent),
                },
                {
                    path: ':id',
                    title: '<PERSON> tiết nhà cung cấp dịch vụ Logistics',
                    canActivate: [canAuthorize],
                    data: {
                        authorize: ['ROLE_SYSTEM_ADMIN', 'sc_logistics_edit'],
                    },
                    loadComponent: () => import('./supplier/detail/logistics-supplier.edit.component').then((c) => c.LogisticsEditComponent),
                },
            ],
        },
        {
            path: '',
            title: 'Quản lý nhà cung cấp dịch vụ Logistics',
            children: [
                {
                    path: 'domestic-price',
                    children: [
                        {
                            path: '',
                            title: 'Báo giá nội địa tháng',
                            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_monthly_expenses_view'] },
                            canActivate: [canAuthorize],
                            loadComponent: () => import('./domestic-price/domestic-price.component').then((c) => c.DomesticPriceComponent),
                        },
                    ],
                },
            ],
        },
        {
            path: 'bo',
            title: 'Quản lý Yêu cầu vận chuyển',
            canActivate: [canAuthorize],
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_bo_view'] },
            children: [
                {
                    path: '',
                    title: 'Danh sách yêu cầu vận chuyển',
                    data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_bo_view', 'sc_bo_edit'] },
                    loadComponent: () => import('./bo/list/list.component').then((c) => c.ListComponent),
                },
                {
                    path: ':id',
                    title: 'Chi tiết yêu cầu vận chuyển',
                    data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_bo_view', 'sc_bo_edit'] },
                    loadComponent: () => import('./bo/detail/detail.component').then((c) => c.DetailComponent),
                },
                {
                    path: 'create',
                    title: 'Tạo mới yêu cầu vận chuyển',
                    data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_bo_view', 'sc_bo_edit'] },
                    loadComponent: () => import('./bo/detail/detail.component').then((c) => c.DetailComponent),
                },
            ],
        },
        {
            path: 'lot',
            title: 'Theo dõi yêu cầu vận chuyển',
            canActivate: [canAuthorize],
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_lot_view'] },
            children: [
                {
                    path: '',
                    title: 'Theo dõi yêu cầu vận chuyển',
                    data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_lot_view', 'sc_lot_edit'] },
                    loadComponent: () => import('./lot/list/list.component').then((c) => c.ListComponent),
                },
                {
                    path: ':id',
                    title: 'Theo dõi yêu cầu vận chuyển',
                    data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_lot_view', 'sc_lot_edit'] },
                    loadComponent: () => import('./lot/detail/detail.component').then((c) => c.DetailComponent),
                },
                {
                    path: 'create',
                    title: 'Theo dõi yêu cầu vận chuyển',
                    data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_lot_view', 'sc_lot_edit'] },
                    loadComponent: () => import('./lot/detail/detail.component').then((c) => c.DetailComponent),
                },
            ],
        },
        {
            path: 'monthly-expenses',
            title: 'Tổng hợp chi phí',
            canActivate: [canAuthorize],
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_monthly_expenses_view'] },
            children: [
                {
                    path: '',
                    title: 'Tổng hợp chi phí',
                    data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_monthly_expenses_view', 'sc_monthly_expenses_edit'] },
                    loadComponent: () => import('./monthly-expenses/monthly-expenses.component').then((c) => c.MonthlyExpensesComponent),
                },
            ],
        },
        {
            path: 'logistic-report',
            title: 'Báo cáo tổng hợp',
            canActivate: [canAuthorize],
            children: [
                {
                    path: 'price-change',
                    title: 'Biến động giá hàng nhập',
                    data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_report_logistic_view'] },
                    loadComponent: () => import('./report/price-change/price-report.component').then((c) => c.ReportComponent),
                },
                {
                    path: 'budget',
                    title: 'Báo cáo ngân sách',
                    data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_report_logistic_view'] },
                    loadComponent: () => import('./report/budget/budget-report.component').then((c) => c.ReportComponent),
                },
            ],
        },
    ],
};
