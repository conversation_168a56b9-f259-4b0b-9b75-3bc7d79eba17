<p-table [value]="[]" [tableStyle]="{ 'table-layout': 'fixed', width: '100%' }"
         class="header-table"
         styleClass="p-datatable-gridlines"
>
    <!-- Khóa width để align với 5-cột tables bên dưới:
           20% cho <PERSON><PERSON><PERSON> đoạn, 40% cho Version 1.0, 40% cho Version 1.1 -->
    <ng-template pTemplate="colgroup">
        <colgroup>
            <col style="width: 20%" />
            <col style="width: 40%" />
            <col style="width: 40%" />
        </colgroup>
    </ng-template>

    <!-- Dòng 1: Version headers -->
    <ng-template pTemplate="header">
        <tr>
            <th></th>
            <th colspan="1">{{ version1 }}</th>
            <th colspan="1">{{ version2 }}</th>
        </tr>
        <!-- Dòng 2: MP dưới mỗi version -->
        <tr>
            <th><PERSON>iai đoạn</th>
            <th>{{ getLifecycleStage(versions[0]?.lifecycleStage) }}</th>
            <th>{{ getLifecycleStage(versions[1]?.lifecycleStage) }}</th>
        </tr>
    </ng-template>
</p-table>
<app-tab-view [tabs]="itemsTab" (tabChange)="onTabChange($event)"> </app-tab-view>
