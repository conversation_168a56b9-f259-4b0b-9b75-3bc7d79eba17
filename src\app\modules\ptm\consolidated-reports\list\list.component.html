<app-sub-header [items]="itemsHeader" [action]="actionHeader">
    <ng-template #actionHeader>
        <p-button routerLink="create" label="Thêm mới" />
    </ng-template>
</app-sub-header>
<div class="tw-p-5">
    <form [formGroup]="filterForm" layout="vertical" class="tw-bg-white tw-p-5 tw-rounded-xl tw-mb-5">
        <div class="tw-grid tw-grid-cols-[2fr_2fr_2fr_1fr] tw-gap-4 tw-my-4">
            <app-custom-form-item [noGrid]="true" label="Khoảng thời gian">
                <p-calendar
                    formControlName="dateRange"
                    selectionMode="range"
                    [readonlyInput]="true"
                    dateFormat="dd/mm/yy"
                    [showIcon]="true"
                    [showButtonBar]="true"
                    placeholder="Chọn khoảng thời gian"
                    appendTo="body"
                >
                </p-calendar>
            </app-custom-form-item>

            <app-custom-form-item [noGrid]="true" label="Dòng sản phẩm">
                <app-combobox-nonRSQL
                    [fetchOnInit]="true"
                    type="select-one"
                    formControlName="productLineIds"
                    fieldValue="id"
                    fieldLabel="name"
                    url="/pr/api/product-line/filter"
                    param="name"
                    placeholder=""
                    [additionalParams]="{ size: 100 }"
                >
                </app-combobox-nonRSQL>
            </app-custom-form-item>

            <app-custom-form-item [noGrid]="true" label="VNPT Man P/N">
                <app-combobox-nonRSQL
                    [fetchOnInit]="false"
                    type="select-one"
                    formControlName="vnptManPn"
                    fieldValue="id"
                    fieldLabel="vnptManPn"
                    url="/pr/api/product"
                    param="vnptManPn"
                    placeholder=""
                    [additionalParams]="{ size: 100 }"
                >
                </app-combobox-nonRSQL>
            </app-custom-form-item>

            <!-- Button -->
            <div>
                <div style="height: 32px"></div>
                <p-button label="Tạo báo cáo" size="small" severity="info" (onClick)="generateReport()"> </p-button>
            </div>
        </div>
    </form>
</div>
