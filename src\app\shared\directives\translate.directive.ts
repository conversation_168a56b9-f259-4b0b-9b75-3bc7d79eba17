import { Directive, ElementRef, Input, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Directive({
    selector: '[appTranslate]',
    standalone: true,
})
export class TranslateDirective implements OnInit {
    @Input('appTranslate') translateKey: string;
    @Input() translateParams: unknown;

    constructor(
        private translateService: TranslateService,
        private el: ElementRef,
    ) {}

    ngOnInit() {
        this.el.nativeElement.innerHTML = this.translateService.instant(this.translateKey, this.translateParams);
    }
}
