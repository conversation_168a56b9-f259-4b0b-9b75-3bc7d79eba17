import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PanelModule } from 'primeng/panel';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { TableModule } from 'primeng/table';
import { FormCustomModule } from '../../../../../../shared/form-module/form.custom.module';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { CalendarModule } from 'primeng/calendar';
import { DropdownModule } from 'primeng/dropdown';
import { ButtonModule } from 'primeng/button';
import { BoGoodsArrive, BoPayTax, Lot } from '../../../../../../models/interface/sc';
import { DialogModule } from 'primeng/dialog';
import { ButtonGroupFileComponent } from '../../../../../../shared/components/button-group-file/button-group-file.component';
import { LotService } from 'src/app/services/sc/lot/lot.service';
import { AttachmentComponent } from 'src/app/shared/components/attachment/attachment.component';
import { FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { ApiResponse, User } from 'src/app/models/interface';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { InputNumberModule } from 'primeng/inputnumber';
import { BoGoodsArriveService } from 'src/app/services/sc/lot/bo-goods-arrive.service';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { debounce } from 'lodash';

@Component({
    selector: 'app-lot-state-six',
    standalone: true,
    templateUrl: './state-six.component.html',
    styleUrls: ['./state-six.component.scss'],
    imports: [
        CommonModule,
        PanelModule,
        InputTextModule,
        InputNumberModule,
        InputTextareaModule,
        TableModule,
        TableCommonModule,
        FormCustomModule,
        ReactiveFormsModule,
        OverlayPanelModule,
        CalendarModule,
        FormCustomModule,
        DropdownModule,
        ButtonModule,
        DialogModule,
        ButtonGroupFileComponent,
        AttachmentComponent,
        HasAnyAuthorityDirective,
    ],
    providers: [LotService, BoGoodsArriveService],
})
export class StateSixComponent implements OnInit, OnChanges {
    @Input() lot: Lot;
    @Input() receivers: User[];

    // Form group
    formGroup: FormGroup;

    @ViewChild('form', { static: false }) form: FormComponent;
    @ViewChild('formSubmit', { static: false }) formSubmit: FormComponent;

    boGoodsArrive: BoGoodsArrive;

    //
    visibleSubmit: boolean = false;
    formGroupSubmit: FormGroup;
    @Output('onComplete') onComplete: EventEmitter<Lot> = new EventEmitter();

    constructor(
        private fb: FormBuilder,
        private loadingService: LoadingService,
        private alertService: AlertService,
        private lotService: LotService,
        private boGoodsArriveService: BoGoodsArriveService,
    ) {
        this.initForm(null);
    }
    ngOnChanges(changes: SimpleChanges): void {
        if (changes['lot'] && !changes['lot'].previousValue && changes['lot'].currentValue) {
            this.loadingService.show();
            this.boGoodsArriveService.getByLot(this.lot.id).subscribe({
                next: (res) => {
                    this.boGoodsArrive = res;
                    this.loadingService.hide();
                    this.initForm(res);
                },
                error: () => {
                    this.loadingService.hide();
                },
            });
        }
    }
    ngOnInit() {
        this.formGroupSubmit = new FormGroupCustom(this.fb, {
            receiverIds: [null, Validators.required],
            content: [null],
        });
    }

    initForm(data: BoGoodsArrive) {
        this.formGroup = null;
        this.formGroup = new FormGroupCustom(this.fb, {
            lotId: [data?.lotId ?? this.lot?.id],
            boId: [data?.boId ?? this.lot?.boId],
            dateArriveCustom: [data?.dateArrive ? new Date(data?.dateArrive) : null],
            note: [data?.note],
            attachmentIds: [data?.attachmentIds],
            attachments: [data?.attachments],
        });

        this.formGroup.valueChanges.subscribe(() => {
            this.debouncedUpdateState();
        });
    }

    handleUploadFile(files: File[]) {
        this.loadingService.show();
        this.boGoodsArriveService.importFile(files).subscribe({
            next: (res: ApiResponse) => {
                const itemValue = this.formGroup.getRawValue() as BoPayTax;

                const attachmentIds = itemValue?.attachmentIds ?? [];
                const attachments = itemValue?.attachments ?? [];
                if (res.code === 1) {
                    // Nối mảng cũ và mảng mới
                    const newAttachmentIds = [...attachmentIds, ...(res.data['attachmentIds'] || [])];
                    const newAttachments = [...attachments, ...(res.data['attachments'] || [])];
                    this.formGroup.patchValue({
                        attachmentIds: newAttachmentIds,
                        attachments: newAttachments,
                    });
                } else {
                    this.alertService.error('Có lỗi xảy ra khi lưu file');
                    this.formGroup.patchValue({ attachmentIds: attachmentIds, attachments: attachments });
                }

                this.loadingService.hide();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    onSubmitState() {
        if (this.formGroup.status !== 'VALID') return;
        const boGoodsArrive = this.formGroup.getRawValue() as BoGoodsArrive;
        boGoodsArrive.dateArrive = boGoodsArrive.dateArriveCustom ? boGoodsArrive.dateArriveCustom.getTime() : null;

        this.lotService.stateSixUpdate({ ...this.boGoodsArrive, ...boGoodsArrive }).subscribe({
            next: (res) => {
                this.onComplete.emit(res);
            },
            error: () => {},
        });
    }

    debouncedUpdateState = debounce(() => {
        this.onSubmitState();
    }, 500);

    sendNotification(value: { receiverIds: number[]; content: string }) {
        this.loadingService.show();
        this.lotService.sendNotification(this.lot.id, value).subscribe({
            next: () => {
                this.visibleSubmit = false;
                this.loadingService.hide();
                this.alertService.success('Thành công');
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    handleChangeReceivers(event) {
        if (event.value !== null && event.objects !== null && event.objects.length > 0) {
            const ids = event.objects.map((obj) => obj.id);
            this.formGroupSubmit.patchValue({
                receiverIds: ids,
            });
        } else {
            this.formGroupSubmit.patchValue({
                receiverIds: null,
            });
        }
    }
}
