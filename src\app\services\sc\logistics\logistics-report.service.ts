import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ApiResponse } from 'src/app/models/interface';

@Injectable()
export class LogisticsReportService {
    constructor(private http: HttpClient) {}

    getPriceChangeReport(filter: any) {
        return this.http.post<ApiResponse>('/sc/api/logistic-report/bo', filter);
    }

    getBudgetReport(time: number) {
        return this.http.post<ApiResponse>('/sc/api/logistic-report/budget?time=' + time, {});
    }

    exportBudgetReport(time: number) {
        return this.http.post<ApiResponse>('/sc/api/logistic-report/budget-export?time=' + time, {});
    }

    updateMonthlyProcess(data: any) {
        return this.http.post<ApiResponse>('/sc/api/report-process/create-custom', data);
    }

    updateMonthlyNote(data: any) {
        return this.http.post<ApiResponse>('/sc/api/report-note/create-custom', data);
    }

    updateMonthlyTax(data: any) {
        return this.http.post<ApiResponse>('/sc/api/report-tax/create-custom', data);
    }
}
