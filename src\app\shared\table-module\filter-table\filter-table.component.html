<input *ngIf="type === 'text'" type="text" pInputText [placeholder]="placeholder" [(ngModel)]="value" (ngModelChange)="onValueChange($event)" />

<p-inputNumber
    [disabled]="disabled"
    *ngIf="type === 'number'"
    inputId="integeronly"
    [(ngModel)]="value"
    (ngModelChange)="onValueChange($event)"
    [placeholder]="placeholder"
/>

<p-dropdown
    [disabled]="disabled"
    *ngIf="type === 'select-one'"
    [options]="resultSelect.data"
    [(ngModel)]="value"
    (onShow)="onDropdowShow()"
    (onFilter)="onDropdownSearch($event)"
    (onChange)="onChangeDropdown($event)"
    [filter]="filterCombobox"
    filterBy="label"
    [checkmark]="true"
    [optionLabel]="configSelect.fieldLabel"
    [optionValue]="configSelect.fieldValue"
    [loading]="resultSelect.isFetching"
    [showClear]="true"
    [placeholder]="placeholder"
    [dataKey]="configSelect.dataKey"
    appendTo="body"
></p-dropdown>

<p-multiSelect
    [disabled]="disabled"
    *ngIf="type === 'select'"
    [options]="resultSelect.data"
    [(ngModel)]="value"
    (onFilter)="onDropdownSearch($event)"
    (onPanelShow)="onDropdowShow()"
    (onChange)="onSelectChange($event)"
    (onClear)="onSelectClear($event)"
    [filter]="filterCombobox"
    display="chip"
    [selectionLimit]="2"
    [optionLabel]="configSelect.fieldLabel"
    [optionValue]="configSelect.fieldValue"
    [loading]="resultSelect.isFetching"
    [showClear]="true"
    [placeholder]="placeholder"
    appendTo="body"
>
    <ng-template let-value pTemplate="selectedItems">
        <div class="p-multiselect-token ng-star-inserted" *ngFor="let object of value">
            <span class="p-multiselect-token-label">{{ object[configSelect.fieldLabel] }}</span>
            <i class="pi pi-times-circle" style="width: 1rem; height: 1rem; margin-left: 0.5rem; cursor: pointer" (click)="remove($event, object)"></i>
        </div>
        <div *ngIf="!value || value.length === 0" style="overflow: hidden" class="tw-h-5">{{ placeholder || '' }}</div>
    </ng-template>
</p-multiSelect>

<p-calendar
    [disabled]="disabled"
    *ngIf="type === 'date'"
    [(ngModel)]="value"
    [iconDisplay]="'input'"
    [showButtonBar]="true"
    [minDate]="configDate.minDate"
    [maxDate]="configDate.maxDate"
    [dateFormat]="configDate.dateFormat"
    (ngModelChange)="onValueChange($event)"
    (onClose)="onDateHide()"
    [placeholder]="placeholder"
    [touchUI]="true"
    appendTo="body"
></p-calendar>

<app-range-date
    [disabled]="disabled"
    *ngIf="type === 'date-range'"
    (onChange)="onValueChange($event)"
    [placeholder]="placeholder"
    [configDate]="configDate"
></app-range-date>

<p-calendar
    [disabled]="disabled"
    *ngIf="type === 'month'"
    [(ngModel)]="value"
    view="month"
    [showButtonBar]="true"
    [dateFormat]="'mm/yy'"
    (ngModelChange)="onSelectMonth($event)"
    [placeholder]="placeholder"
    appendTo="body"
></p-calendar>
