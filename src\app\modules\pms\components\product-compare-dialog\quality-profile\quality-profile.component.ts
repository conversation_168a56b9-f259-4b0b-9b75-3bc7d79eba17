import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { TableModule } from 'primeng/table';
import { CommonModule } from '@angular/common';
import { environment } from 'src/environments/environment';

interface HsspProduct {
    id: number;
     name: string;
    
    // Thông tin sản phẩm
    productInfo?: string;
    productSpecs?: string;
    productDFMEA?: string;
    
    // RDBOM
    rdbom?: string;
    ecrEcn?: string;
    
    // Thiết kế HW
    schematicDesign?: string;
    pcbLayout?: string;
    pcbGerber?: string;
    cadFile?: string;
    panelLayout?: string;
    panelGerber?: string;
    dfmChecklist?: string;
    hwTestCases?: string;
    hwPrototypeReport?: string;
    
    // Thiết kế ID/MD
    mdDesign?: string;
    printingDesign?: string;
    idMdTestItems?: string;
    assemblyTestReport?: string;
    
    // Thiết kế Accessories
    labelDesign?: string;
    giftboxDesign?: string;
    accessoryPrinting?: string;
    accessoryTestItems?: string;
    accessoryTestReport?: string;
    
    // Firmware
    bootloader?: string;
    fwBasic?: string;
    fwCommercial1?: string;
    fwCommercial2?: string;
    fwCommercial3?: string;
    releaseNote?: string;
}

interface RowData {
    section: string;
    name: string;
    note1?: string;
    note2?: string;
    path1?: string;
    path2?: string;
}

@Component({
  selector: 'app-quality-profile',
  templateUrl: './quality-profile.component.html',
  styleUrls: ['./quality-profile.component.scss'],
  standalone: true,
      imports: [TableModule, CommonModule],
})
export class QualityProfileComponent implements OnChanges {
    @Input() docRes1: any;
    @Input() docRes2: any;
    groupedRows: { name: string; items: RowData[] }[] = [];
    public STORAGE_BASE_URL = environment.STORAGE_BASE_URL;

@Input() comparedProducts: HsspProduct[] = [];
    ngOnChanges(changes: SimpleChanges) {
            if (changes['docRes1'] || changes['docRes2']) {
                this.updateGroupedRows();
            }
        }
    
        private updateGroupedRows() {
            
                // Kiểm tra dữ liệu đầu vào
                if (!this.docRes1 && !this.docRes2) {
                    this.groupedRows = [];
                    return;
                }
    
                const sectionMap: { [key: number]: string } = {
                    0: 'Tài liệu kiểm soát chất lượng',
                    1: 'Test report',
                    2: 'Sample',
                };
    
                const data1 = this.docRes1 ?? {};
                const data2 = this.docRes2 ?? {};
    
                const allSectionKeys = new Set([...Object.keys(data1), ...Object.keys(data2)]);
    
                const map = new Map<string, RowData[]>();
    
                for (const sectionKey of allSectionKeys) {
                    const sectionNum = Number(sectionKey);
                    const sectionName = sectionMap[sectionNum] || 'Khác';
    
                    const list1 = data1[sectionKey] || [];
                    const list2 = data2[sectionKey] || [];
    
                    const maxLength = Math.max(list1.length, list2.length);
                    const rows: RowData[] = [];
    
                    for (let i = 0; i < maxLength; i++) {
                        const item1 = list1[i] || {};
                        const item2 = list2[i] || {};
    
                        rows.push({
                            section: sectionName,
                            name: item1.description || item2.description || '',
                            note1: item1.fileName || '',
                            note2: item2.fileName || '',
                            path1: item1.filePath || '',
                            path2: item2.filePath || '',
                        });
                    }
    
                    if (rows.length > 0) {
                        map.set(sectionName, rows);
                    }
                }
    
                this.groupedRows = Array.from(map, ([name, items]) => ({ name, items }));
            
        }
        downloadFile(url: string, namePath: string) {
        const a = document.createElement('a');
        a.href = this.STORAGE_BASE_URL + '/' + url;
        a.download = `${namePath}`;
        a.click();
        a.remove();
    }
}
