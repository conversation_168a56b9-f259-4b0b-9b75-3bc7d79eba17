import { LocationStrategy, PathLocationStrategy } from '@angular/common';
import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { AuthInterceptor } from './interceptor/auth.interceptor';
import { HostInterceptor } from './interceptor/host.interceptor';

import { ResponseInterceptor } from './interceptor/response.interceptor';
import { MessageService } from 'primeng/api';
import { provideQueryClientOptions } from '@ngneat/query';

// import ngx-translate and the http loader
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { HttpClient } from '@angular/common/http';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { LoadingInterceptor } from './interceptor/loading.interceptor';

const queryClient = {
    defaultOptions: {
        queries: {
            staleTime: 0,
            retry: 2, // Retries the query twice before failing
            retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
        },
        refetchOnWindowFocus: false,
    },
};

export function HttpLoaderFactory(http: HttpClient) {
    return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

@NgModule({
    providers: [
        { provide: LocationStrategy, useClass: PathLocationStrategy },
        { provide: HTTP_INTERCEPTORS, useClass: HostInterceptor, multi: true },
        { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },
        { provide: HTTP_INTERCEPTORS, useClass: ResponseInterceptor, multi: true },
        { provide: HTTP_INTERCEPTORS, useClass: LoadingInterceptor, multi: true },
        // provideHttpClient(                        // bỏ comment khi muốn cấu hình httpclient sử dụng fetchAPI thay vì XMLHttpRequest
        //     withFetch(),
        //     withInterceptorsFromDi()
        // ),
        provideQueryClientOptions(queryClient),
        MessageService, // message service provide tại core vì ResponseInterceptor và AlertService sử dụng service này
    ],
    imports: [
        TranslateModule.forRoot({
            loader: {
                provide: TranslateLoader,
                useFactory: HttpLoaderFactory,
                deps: [HttpClient],
            },
        }),
        HttpClientModule,
        BrowserModule,
        BrowserAnimationsModule,
    ],
})
export class CoreModule {}
