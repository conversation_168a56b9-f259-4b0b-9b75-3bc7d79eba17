import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '../../base.service';
import { ErrorSmartQC } from '../../../models/interface/smart-qc';

@Injectable({
    providedIn: 'root',
})
export class ErrorSmartQCService extends BaseService<ErrorSmartQC> {
    constructor(protected override http: HttpClient) {
        super(http, '/smart-qc/api/error');
    }
}
