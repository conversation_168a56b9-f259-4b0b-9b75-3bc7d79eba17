import { <PERSON>mpo<PERSON>, On<PERSON><PERSON><PERSON>, Query<PERSON><PERSON>, <PERSON>Child, ViewChildren, AfterViewInit, inject } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { WizardComponent } from 'src/app/shared/components/wizard/wizard.component';
import { ITEMS_STEP, STATUS_MAP } from 'src/app/models/constant/pms';
import { PanelModule } from 'primeng/panel';
import { TagModule } from 'primeng/tag';
import { ProDetailComponent } from '../pro-detail/pro-detail.component';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { InputTextModule } from 'primeng/inputtext';
import { ComboboxNonRSQLComponent } from 'src/app/shared/components/combobox-nonRSQL/combobox-nonRSQL.component';
import { InputValidationComponent } from 'src/app/shared/components/input-validation/input.validation.component';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { submitSpecificForm, TrimmedFormControl } from 'src/app/utils/form';
import { AbstractControlCustom, FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { FormArrayCustom } from 'src/app/shared/form-module/from-array.custom';
import { ProjectPlanService } from 'src/app/services/ptm/project-plan/project-plan.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { Router } from '@angular/router';
import { ActionForTab } from 'src/app/models/interface/ptm/project-plan';
import { Task, IAssignee, TaskStatus } from 'src/app/models/interface/ptm';
import { SafeApiCallerService } from 'src/app/services/SafeApiCaller.service';

import { FormsModule, FormBuilder, FormControl, FormGroup, Validators, FormArray, ValidatorFn, AbstractControl } from '@angular/forms';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';

import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';

@Component({
    selector: 'app-detail',
    templateUrl: 'detail.component.html',
    styleUrls: ['detail.component.scss'],
    standalone: true,
    imports: [
        SubHeaderComponent,
        WizardComponent,
        ComboboxNonRSQLComponent,
        TagModule,
        FormCustomModule,
        PanelModule,
        ButtonModule,
        RouterLink,
        InputTextModule,
        CalendarModule,
        ProDetailComponent,
        CommonModule,
        InputValidationComponent,
        FormsModule,
    ],
})
export class ProjectPlanDetailComponent implements OnInit, AfterViewInit {
    @ViewChild('customerFilter', { static: false })
    customerFilter!: ComboboxNonRSQLComponent;
    @ViewChild('projectNameFilter', { static: false })
    projectNameFilter!: ComboboxNonRSQLComponent;
    @ViewChild('picIdFilter', { static: false })
    picIdFilter!: ComboboxNonRSQLComponent;
    @ViewChildren(FormComponent)
    forms: QueryList<FormComponent>;
    @ViewChild(ProDetailComponent)
    proDetailComponentRef: ProDetailComponent;
    safeCaller = inject(SafeApiCallerService);

    itemsHeader = [{ label: 'Kế hoạch dự án' }, { label: 'Danh sách kế hoạch dự án', url: '/ptm/project-plans' }, { label: 'Tạo mới' }];
    mode!: 'create' | 'view' | 'edit';
    formGroup: FormGroup;
    Info: string = '0 / 0';
    firstTimeFlags = new WeakMap();
    projectId: number = 0;
    activeIndex: number = 0;
    approvalStatus: number = 0;
    itemsStep = ITEMS_STEP;
    isCloning = false;
    isViewOnly: boolean = false;

    constructor(
        private route: ActivatedRoute,
        private fb: FormBuilder,
        private pps: ProjectPlanService,
        private alertService: AlertService,
        private router: Router,
    ) {}

    ngOnInit() {
        this.route.url.subscribe((segments) => {
            this.mode = segments[0].path as 'create' | 'edit' | 'view';
        });

        this.route.paramMap.subscribe((params) => {
            const id = params.get('productId');
            if (id) {
                this.projectId = Number(id);
                const cloneData = history.state?.cloneFrom;
                if (cloneData) {
                    this.isCloning = true;
                }
                if (this.mode === 'edit' || this.mode === 'view') {
                    this.pps.getProject(this.projectId).subscribe({
                        next: (projectDetail) => {
                            // Xử lý đặc biệt cho trường hợp clone
                            if (cloneData && this.mode === 'edit') {
                                const mergedData = this.mergeCloneData(projectDetail, cloneData);
                                this.patchFormWithApiData(mergedData);
                            } else {
                                this.patchFormWithApiData(projectDetail);
                            }

                            this.isViewOnly = this.mode === 'view';
                        },
                    });
                }
            }
        });
        const lastLabel = this.mode === 'create' ? 'Tạo mới' : this.mode === 'edit' ? 'Chỉnh sửa' : 'Chi tiết';

        this.itemsHeader = [{ label: 'Kế hoạch dự án' }, { label: 'Danh sách kế hoạch dự án', url: '/ptm/project-plans' }, { label: lastLabel }];
        const isViewMode = this.mode === 'view';

        this.formGroup = this.fb.group({
            projectName: [
                {
                    value: '',
                    disabled: isViewMode,
                },
                Validators.required,
            ],
            startDate: [
                {
                    value: null,
                    disabled: isViewMode,
                },
                Validators.required,
            ],
            endDate: [
                {
                    value: null,
                    disabled: isViewMode,
                },
                Validators.required,
            ],
            picOrPm: [
                {
                    value: null,
                    disabled: isViewMode,
                },
            ],
            productName: [
                {
                    value: null,
                    disabled: isViewMode,
                },
            ],
            vnptManPn: [
                {
                    value: null,
                    disabled: isViewMode,
                },
                Validators.required,
            ],
            completionRate: [
                {
                    value: '',
                    disabled: true,
                },
            ],
            completedTasks: [
                {
                    value: '',
                    disabled: true,
                },
            ],
            planDetails: new FormArrayCustom([]),
        });

        this.approvalStatus = history.state?.approvalStatus;
    }
    private mergeCloneData(originalData: any, cloneData: any): any {
        return {
            ...originalData, // Giữ nguyên tất cả data gốc từ API
            name: this.generateClonedProjectName(originalData.name), // Tạo tên mới
            tasks: originalData.tasks?.map((task) => ({
                ...task,
                progressPercent: 0, // Reset tiến độ
                status: TaskStatus.NOT_COMPLETED, // Reset trạng thái
            })),
            workingSettings: originalData.workingSettings, // Giữ nguyên thiết lập
        };
    }
    ngAfterViewInit(): void {
        // this.picIdFilter.debouncedGetOptions('');
        this.setUserFilterOptions(this.picIdFilter);
    }
    setUserFilterOptions(...combos: ComboboxNonRSQLComponent[]) {
        combos.forEach((combo) => {
            const origDebounce = combo.debouncedGetOptions.bind(combo);
            combo.filterOptions = (term: string) => {
                const rsql = `fullName==*${term}*`;
                origDebounce(rsql);
            };
        });
    }
    private patchFormFromClone(cloneData: any): void {
        // Tạo tên dự án mới với số tăng dần
        const newProjectName = this.generateClonedProjectName(cloneData.projectName);

        // Patch thông tin chung
        this.formGroup.patchValue({
            projectName: newProjectName,
            startDate: new Date(cloneData.startDate),
            endDate: new Date(cloneData.endDate),
            productName: cloneData.productId,
            vnptManPn: cloneData.productId,
            picOrPm: cloneData.picId || null,
            completionRate: '', // Reset tỷ lệ hoàn thành
        });

        // Xử lý bảng chi tiết
        const planDetailsFormArray = this.formGroup.get('planDetails') as FormArrayCustom;
        planDetailsFormArray.clear();

        if (Array.isArray(cloneData.tasks)) {
            cloneData.tasks.forEach((task: any) => {
                const clonedTask = new FormGroup({
                    name: new FormControl(task.name || '', Validators.required),
                    timeline: new FormControl([new Date(task.startDate), new Date(task.endDate)], Validators.required),
                    duration: new FormControl(task.duration || null),
                    predecessor: new FormControl(task.predecessor || ''),
                    assignee: new FormControl(task.assigneeId || null),
                    progress: new FormControl(null), // Reset tiến độ
                    status: new FormControl(TaskStatus.NOT_COMPLETED), // Reset trạng thái
                    note: new FormControl(task.note || ''),
                    level: new FormControl(task.level || 1),
                    displaySTT: new FormControl(task.multilevelNumbering || '1'),
                    action: new FormControl(ActionForTab.CREATE),
                });

                planDetailsFormArray.push(clonedTask);
                this.proDetailComponentRef?.setupTaskChangeListener(clonedTask);
            });
        }

        // Load thiết lập ngày làm việc
        this.proDetailComponentRef?.loadWorkingSettings();
    }
    private generateClonedProjectName(originalName: string): string {
        // Kiểm tra nếu tên đã có (x) ở cuối
        const regex = /^(.*?)\s*(\((\d+)\))?$/;
        const matches = originalName.match(regex);

        let baseName = matches[1] || originalName;
        let currentNumber = matches[3] ? parseInt(matches[3]) : 0;

        // Tăng số lên 1
        const newNumber = currentNumber + 1;

        return `${baseName.trim()} (${newNumber})`;
    }
    onWorkingDayUpdated(): void {
        this.ngOnInit(); // Gọi lại toàn bộ logic
    }
    createPlanDetailRow(level = 1, displaySTT = '1', data: any = {}): FormGroup {
        const isDisabled = this.mode === 'view';
        return new FormGroupCustom(this.fb, {
            id: [data.id || null],
            name: [{ value: data.name || '', disabled: isDisabled }, this.mode !== 'view' ? Validators.required : []],
            timeline: [{ value: data.timeline || null, disabled: isDisabled }, this.mode !== 'view' ? Validators.required : []],
            duration: [{ value: data.duration || null, disabled: isDisabled }],
            predecessor: [{ value: data.predecessor || '', disabled: isDisabled }],
            assignee: [{ value: data.assignee || null, disabled: isDisabled }],
            assigneeName: [{ value: data.assigneeName || '', disabled: isDisabled }],
            progress: [{ value: data.progress ?? null, disabled: isDisabled }],
            status: [{ value: data.status ?? 2, disabled: isDisabled }],
            note: [{ value: data.note || '', disabled: isDisabled }],
            level: [level],
            displaySTT: [displaySTT],
            action: [data.action ?? 1],
            rawData: [JSON.stringify(data)],
        });
    }

    onProductSelect(event: any): void {
        if (!event?.objects?.length) {
            this.formGroup.patchValue({
                productName: null,
                vnptManPn: null,
            });

            // Clear hiển thị combobox
            this.projectNameFilter?.filterOptions('');
            this.customerFilter?.filterOptions('');
            return;
        }

        const selected = event.objects[0];

        // Gán cho productName (nếu chưa có)
        if (selected.name) {
            this.formGroup.patchValue({
                productName: selected.id,
            });
            this.projectNameFilter.filterOptions(selected.name);
        }
        if (selected.vnptManPn) {
            this.formGroup.patchValue({
                vnptManPn: selected.id,
            });
            this.customerFilter.filterOptions(selected.vnptManPn);
        }
    }

    createTaskForm(): FormGroup {
        return this.fb.group({
            name: ['', Validators.required],
            timeline: [null, Validators.required], // ✅ timeline bắt buộc
            duration: [null],
            predecessor: [''],
            assignee: [null],
            progress: [0],
            status: [null],
            note: [''],
        });
    }

    submitForm(formPL: string): void {
        this.formGroup.markAllAsTouched();
        submitSpecificForm(this.forms, formPL); // Gọi hàm util
        this.proDetailComponentRef?.validateAllTaskTimelines();
        this.proDetailComponentRef?.validatePredecessor();

        if (this.formGroup.invalid) return;

        const formValue = this.formGroup.getRawValue();

        const payload: any = {
            name: formValue.projectName,
            startDate: new Date(formValue.startDate).getTime(),
            endDate: new Date(formValue.endDate).getTime(),
            productId: formValue.productName || 78,
            status: 1,
            picId: formValue.picOrPm || 0,
        };

        if (this.mode === 'create') {
            this.handleCreateProject(payload);
        } else if (this.mode === 'edit') {
            payload.tasks = this.mapTasks(formValue.planDetails);
            if (this.isCloning) {
                this.handleCloneProject(this.projectId, payload); // ✅ Gọi API clone
            } else {
                this.handleUpdateProject(this.projectId, payload); // ✅ Gọi API update thường
            }
        }
    }
    handleCloneProject(projectId: number, payload: any): void {
        this.pps.cloneProjectPlan(projectId, payload).subscribe({
            next: (res) => {
                const newProjectId = res.id;
                this.alertService.success('Thành công', 'Nhân bản kế hoạch thành công');
                this.isCloning = false;
                this.projectId = newProjectId;
                this.handleUpdateProject(newProjectId, payload, true);
                // this.ngOnInit();
                this.router.navigate(['/ptm/project-plans/edit', newProjectId]);
            },
            error: (err) => {
                this.alertService.error('Lỗi', 'Không thể nhân bản kế hoạch');
                console.error(err);
            },
        });
    }

    handleUpdateProject(projectId: number, payload: any, suppressAlert: boolean = false): void {
        this.safeCaller.call({
            apiCall: () => this.pps.updateProjectPlan(projectId, payload),
            onSuccess: () => {
                if (!suppressAlert) {
                    this.alertService.success('Thành công', 'Cập nhật kế hoạch thành công');
                }
                this.ngOnInit();
            },
            onError: (err) => {
                console.error('Lỗi cập nhật kế hoạch:', err);
                this.alertService.error('Lỗi', 'Không thể cập nhật kế hoạch');
            },
        });
    }

    private mapTasks(tasks: any[]): any[] {
        let currentBaseLevel = 0;
        return tasks.map((task: any) => {
            const [startDate, endDate] = task.timeline || [];
            const displaySTT = task.displaySTT || '1';

            const levelDepth = displaySTT.split('.').length;

            // Nếu là cấp 1 (ví dụ: "1", "2", "3") => reset lại level tính từ 1
            if (!displaySTT.includes('.')) {
                currentBaseLevel = 1;
            }

            let level = currentBaseLevel + (levelDepth - 1);
            let action = task.action ?? ActionForTab.UPDATE;
            const mappedTask: any = {
                multilevelNumbering: task.displaySTT,
                level: level,
                name: task.name,
                startDate: startDate ? new Date(startDate).getTime() : null,
                endDate: endDate ? new Date(endDate).getTime() : null,
                duration: task.duration,
                status: task.status ?? 2,
                predecessor: task.predecessor,
                progressPercent: task.progress,
                assigneeId: task.assignee,
                assigneeName: String(task.assignee),
                note: task.note,
                action, // action default nếu không có
            };
            if (task.id) {
                mappedTask.id = task.id;
            }
            return mappedTask;
        });
    }
    handleCreateProject(payload) {
        this.pps.createProjectPlan(payload).subscribe({
            next: (res) => {
                this.alertService.success('Thành công', 'Tạo kế hoạch thành công');
                this.mode = 'edit';
                // this.projectId = res.id;
                this.projectId = res.id;
                this.router.navigate(['/ptm/project-plans/edit', res.id]);
                return this.pps.getProject(res.id).subscribe({
                    next: (projectDetail) => {
                        this.patchFormWithApiData(projectDetail);
                    },
                });
            },
        });
    }
    private patchFormWithApiData(project: any): void {
        if (!project) return;

        this.formGroup.patchValue({
            projectName: project.name,
            startDate: new Date(project.startDate),
            endDate: new Date(project.endDate),
            productName: project.productId,
            vnptManPn: project.productId,
            // completionRate: project.rate,
            picOrPm: project.picId,
        });
        this.Info = project.rate;
        if (project.productName) {
            this.projectNameFilter.filterOptions(project.productName);
        }
        if (project.picName) {
            // this.picIdFilter.filterOptions(project.picName);
            this.picIdFilter.objectValue = [{ id: project.picId, fullName: project.picName }];
            this.picIdFilter.value = project.picId;
            this.picIdFilter.filteredOptions = [{ id: project.picId, fullName: project.picName }];
        }
        this.customerFilter.filterOptions(project.manPN);

        const statusLabel = STATUS_MAP[this.approvalStatus];
        const idx = ITEMS_STEP.findIndex((item) => item.name === statusLabel);
        this.activeIndex = idx !== -1 ? idx : 0;

        const taskFormArray = this.formGroup.get('planDetails') as FormArrayCustom;
        taskFormArray.clear();
        if (Array.isArray(project.tasks) && project.tasks.length > 0) {
            for (const task of project.tasks) {
                const formGroup = this.createPlanDetailRow(task.level, task.multilevelNumbering, {
                    id: task.id,
                    name: task.name || '',
                    timeline: [new Date(task.startDate), new Date(task.endDate)],
                    duration: task.duration || null,
                    predecessor: task.predecessor || '',
                    assignee: task.assigneeId || null,
                    assigneeName: task.assigneeName || '',
                    progress: task.progressPercent || null,
                    status: task.status ?? 2,
                    note: task.note || '',
                    action: task.action ?? 0, // ✅ mặc định là NO_CHANGE
                });

                formGroup.get('name')?.setValidators([Validators.required, this.proDetailComponentRef.uniqueTaskNameValidator(taskFormArray)]);
                formGroup.get('name')?.updateValueAndValidity({ onlySelf: true });

                formGroup.addControl('id', new FormControl(task.id));
                this.proDetailComponentRef.setupTaskChangeListener(formGroup);
                // formGroup.addControl('rawData', new FormControl(JSON.stringify(formGroup.value)));
                taskFormArray.push(formGroup);
            }
            setTimeout(() => {
                this.proDetailComponentRef?.validateAllTaskTimelines();
            });
        }
        // else {
        //     const defaultTask = this.createPlanDetailRow(1, '1');
        //     taskFormArray.push(defaultTask);
        // }
    }
    get planDetailsArray(): FormArrayCustom {
        return this.formGroup.get('planDetails') as FormArrayCustom;
    }
    handlePanelShow(ref: any) {
        if (!this.firstTimeFlags.has(ref)) {
            this.firstTimeFlags.set(ref, true);
            return;
        }
        ref.fetchOptions(null);
    }
}
