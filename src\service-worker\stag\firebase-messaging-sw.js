// Import the scripts needed
importScripts('https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js');
importScripts('https://www.gstatic.com/firebasejs/8.10.1/firebase-messaging.js');

// Initialize Firebase
firebase.initializeApp({
    apiKey: "AIzaSyD0Q7kDPsgzFBMPyZX1jOFGh6U1ma1XYJA",
    authDomain: "smartqc-8cd89.firebaseapp.com",
    projectId: "smartqc-8cd89",
    storageBucket: "smartqc-8cd89.appspot.com",
    messagingSenderId: "982731833006",
    appId: "1:982731833006:web:970d4de29ca5618cddf73f",
    measurementId: "G-3YCDKQMV7L"
});

const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage(function (payload) {
    // Send event to component
    return clients
        .matchAll({
            type: 'window',
            includeUncontrolled: true,
        })
        .then((windowClients) => {
            //console.log(windowClients)
            for (let i = 0; i < windowClients.length; i++) {
                const windowClient = windowClients[i];
                windowClient.postMessage({
                    type: 'NOTIFICATION_FIREBASE_RECEIVER',
                    data: payload.data,
                });
            }
        });
});
