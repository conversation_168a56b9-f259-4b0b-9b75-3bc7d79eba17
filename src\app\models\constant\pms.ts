export const ITEMS_STEP = [
    {
        id: 0,
        name: 'Draft',
    },
    {
        id: 1,
        name: 'Sent to Approve',
    },
    {
        id: 2,
        name: 'Rejected',
    },
    {
        id: 3,
        name: 'Approved',
    },
];
export const Prototype = 1;
export const Pilot = 2;
export const MP = 4;
export const LIFECYCLE_STAGE_PRODUCT_MAP: Record<number, string> = {
    1: 'Prototype',
    2: 'Pilot (RnD)',
    4: 'MP',
    8: 'Pilot (SX)',
};
export const LIFECYCLE_STAGE_DOC_MAP: Record<number, string> = {
    1: 'Prototype',
    2: 'Pilot',
    4: 'MP',
};
export const STATUS_MAP: Record<number, string> = {
    1: 'Draft',
    2: 'Sent to Approve',
    4: 'Rejected',
    8: 'Approved',
    16: 'Sent to Approve',
};
export enum Status {
    Draft = 1,
    SentToApprove = 2,
    Rejected = 4,
    Approved = 8,
    SentToApprove2 = 16,
}
export const FILE_ACCEPT_PATTERNS = [
    '.pdf',
    '.xls',
    '.xlsx',
    '.doc',
    '.docx',
    '.csv',
    '.ppt',
    '.pptx',
    '.rtf',
    '.odt',
    '.ods',
    '.odp',
    '.txt',
    '.md',
    '.zip',
    '.rar',
    '.7z',
    '.tar',
    '.gz',
    '.bz2',
    '.tgz',
    '.xz',
    '.jar',
    '.gbr',
    '.bin',
    '.exe',
];
export const FILE_ACCEPT_STRING = FILE_ACCEPT_PATTERNS.join(',');
