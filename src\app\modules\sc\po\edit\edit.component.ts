import { Compo<PERSON>, On<PERSON><PERSON><PERSON>, Query<PERSON>ist, ViewChildren } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { isArray } from 'lodash';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { InputTextModule } from 'primeng/inputtext';
import { PanelModule } from 'primeng/panel';
import { Attachment } from 'src/app/models/interface';
import { Po, PoDetailDTO, Supplier } from 'src/app/models/interface/sc';
import { PoService } from 'src/app/services/sc/po/po.service';
import { ButtonGroupFileComponent } from 'src/app/shared/components/button-group-file/button-group-file.component';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { AlertService } from 'src/app/shared/services/alert.service';
import { FileService } from 'src/app/shared/services/file.service';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { FilterChangeEvent } from 'src/app/shared/table-module/filter-table/filter-table.component';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { submitSpecificForm, TrimmedFormControl } from 'src/app/utils/form';
import { PoBOQComponent } from '../po-boq/po-boq.component';
import { TabViewModule } from 'primeng/tabview';
import { NgIf } from '@angular/common';
import { ListPoDetailComponent } from '../po-detail/list-po-detail.component';
import { ListPoTransferExportComponent } from '../po-transfer/list-export/po-transfer.component';
import { ListPoTransferComponent } from '../po-transfer/list-import/po-transfer.component';
import { PO_STATE_CONSTANT, PoStates } from 'src/app/models/constant/sc';
import { WizardComponent } from '../../../../shared/components/wizard/wizard.component';
import {ConfirmationService} from "primeng/api";
import {AuthService} from "../../../../core/auth/auth.service";

@Component({
    selector: 'app-po-edit',
    templateUrl: './edit.component.html',
    styleUrls: ['./edit.component.scss'],
    standalone: true,
    imports: [
        SubHeaderComponent,
        FormCustomModule,
        TableCommonModule,
        PanelModule,
        CalendarModule,
        InputTextModule,
        ButtonModule,
        HasAnyAuthorityDirective,
        ReactiveFormsModule,
        RouterLink,
        ButtonGroupFileComponent,
        PoBOQComponent,
        TabViewModule,
        NgIf,
        ListPoDetailComponent,
        ListPoTransferComponent,
        ListPoTransferExportComponent,
        WizardComponent,
    ],
    providers: [PoService],
})
export class OrderEditComponent implements OnInit {
    // header
    itemsHeader = [];

    //form
    formGroup: FormGroup;
    orderId: number;
    oldOrder: Po;
    @ViewChildren(FormComponent) forms: QueryList<FormComponent>;

    // boq
    boqAttachment: Attachment;
    // tabView
    activeIndex: number = 0;
    isDisable: boolean = false;

    poStates = PoStates;
    isAdmin: boolean = false;

    PO_STATE_CONSTANT = PO_STATE_CONSTANT;
    constructor(
        private fb: FormBuilder,
        private route: ActivatedRoute,
        private loadingService: LoadingService,
        private alertService: AlertService,
        private fileService: FileService,
        private router: Router,
        private poService: PoService,
        private confirmationService: ConfirmationService,
        private authService: AuthService
    ) {
        this.initForm();
        this.oldOrder = this.formGroup.value;
    }

    ngOnInit(): void {
        this.route.paramMap.subscribe((params) => {
            this.orderId = params.get('id') ? Number(params.get('id')) : null;

            if (!this.orderId) {
                this.itemsHeader = [
                    { label: 'Quản lý nhà cung cấp' },
                    { label: 'Quản lý PO', url: './..' },
                    { label: 'Tạo mới' },
                ];
            } else {
                this.initPoData();
            }
        });

        this.isAdmin = this.authService.isAdmin();
    }

    private initPoData() {
        this.loadingService.show();

        this.poService.getOne(this.orderId).subscribe({
            next: (res) => {
                this.oldOrder = res.body;
                if (this.oldOrder.state === PO_STATE_CONSTANT.COMPLETED) {
                    this.isDisable = true;
                }
                this.initForm(this.oldOrder);
                this.loadingService.hide();
                this.itemsHeader = [
                    { label: 'Quản lý nhà cung cấp' },
                    { label: 'Quản lý PO', url: './..' },
                    { label: this.oldOrder.orderNo },
                ];
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    initForm(po: Po = null): void {
        this.formGroup = this.fb.group({
            orderNo: new TrimmedFormControl(
                { value: po ? po.orderNo : null, disabled: this.isDisable },
                Validators.required,
            ),
            orderDateCustom: new FormControl(
                { value: po && po.orderDate ? new Date(po.orderDate) : new Date(), disabled: this.isDisable },
                Validators.required,
            ),
            supplierId: new FormControl(
                { value: po ? po.supplierId : null, disabled: !!po?.id || this.isDisable },
                Validators.required,
            ),
            imputation: new FormControl({ value: po ? po.imputation : null, disabled: this.isDisable }),
            requestNo: new TrimmedFormControl({ value: po ? po.requestNo : null, disabled: this.isDisable }),
            supplierNo: new FormControl({ value: po ? po.supplierNo : null, disabled: true }, Validators.required),
        });
    }

    handleChangeSupplier(e: FilterChangeEvent) {
        const objects = e.objects as unknown as Supplier[];
        const supplierNoValue = isArray(objects) && objects[0] ? objects[0].code : null;

        this.formGroup.get('supplierNo').setValue(supplierNoValue);
    }

    submitForm(formId: string): void {
        submitSpecificForm(this.forms, formId); // Gọi hàm util
    }

    onSubmit(value: Po): void {
        this.loadingService.show();
        if (this.orderId) {
            this.poService
                .update({
                    ...this.oldOrder,
                    ...value,
                    orderDate: value.orderDateCustom ? value.orderDateCustom.getTime() : null,
                })
                .subscribe({
                    next: () => {
                        this.alertService.success('Thành công');
                        this.poService.getOne(this.orderId).subscribe({
                            next: (res) => {
                                this.oldOrder = res.body;
                                this.loadingService.hide();
                            },
                            error: () => {
                                this.loadingService.hide();
                            },
                        });
                    },
                    error: () => {
                        this.loadingService.hide();
                    },
                });
        } else {
            this.poService
                .create({
                    ...this.oldOrder,
                    ...value,
                    orderDate: value.orderDateCustom ? value.orderDateCustom.getTime() : null,
                })
                .subscribe({
                    next: (res) => {
                        this.alertService.success('Thành công');
                        this.loadingService.hide();
                        this.router.navigate([`/sc/po/${res.body.id}`]);
                    },
                    error: () => {
                        this.loadingService.hide();
                    },
                });
        }
    }

    updatePoDetails(poDetailDTO: PoDetailDTO) {
        this.oldOrder = {
            ...this.oldOrder,
            poDetails: poDetailDTO.poDetails,
            poDetailDays: poDetailDTO.poDetailDays,
            detailDaysMap: poDetailDTO.detailDaysMap,
            invNumberTransferByDate: poDetailDTO.invNumberTransferByDate,
        };

        // Cap nhat trang thai PO
        this.poService.getPoStatus(this.orderId).subscribe({
            next: (res) => {
                this.oldOrder.state = res.body as number;
                if (this.oldOrder.state !== PO_STATE_CONSTANT.COMPLETED && this.isDisable) {
                    this.isDisable = false;
                    this.initForm(this.oldOrder);
                }
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    reloadPo(data) {
        this.poService.getOne(this.orderId).subscribe({
            next: (res) => {
                this.oldOrder = res.body;
                if (this.oldOrder.state === PO_STATE_CONSTANT.COMPLETED) {
                    this.isDisable = true;
                }
                this.initForm(this.oldOrder);
                this.loadingService.hide();
                this.itemsHeader = [
                    { label: 'Quản lý nhà cung cấp' },
                    { label: 'Quản lý PO', url: './..' },
                    { label: this.oldOrder.orderNo },
                ];
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    onChangeState(stateId) {
        /*console.log("Change state ", stateId);
        console.log(this.oldOrder.state)*/
        if (this.oldOrder.state !== stateId && this.isAdmin && this.oldOrder.id != null) {
            this.confirmationService.confirm({
                key: 'app-confirm',
                header: 'Xác nhận',
                message: 'Bạn có chắc chắn muốn chuyển trạng thái',
                icon: 'pi pi-exclamation-triangle',
                rejectLabel: 'Hủy',
                acceptLabel: 'Xác nhận',
                acceptIcon: 'pi pi-check mr-2',
                rejectIcon: 'pi pi-times mr-2',
                rejectButtonStyleClass: 'p-button-sm',
                acceptButtonStyleClass: 'p-button-outlined p-button-sm',
                accept: () => {
                    const data = {
                        poId: this.oldOrder.id,
                        state: stateId
                    }
                    this.loadingService.show();
                    this.poService.updateState(data).subscribe({
                        next: (res) => {
                            this.alertService.success('Thành công');
                            this.loadingService.hide();
                            this.initPoData();
                        },
                        error: () => {
                            this.loadingService.hide();
                        },
                    });
                },
            });
        }
    }
}
