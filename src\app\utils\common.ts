/* eslint-disable @typescript-eslint/no-explicit-any */
import { Column } from '../models/interface';

import { FormControl, FormGroup } from '@angular/forms';
import { SupplierKpi } from '../models/interface/sc';
export default class Common {
    static mergeArray(a, b, predicate = (a, b) => a === b) {
        const c = [...a];
        b.forEach((bItem) => (c.some((cItem) => predicate(bItem, cItem)) ? null : c.push(bItem)));
        return c;
    }

    static doSomethingElse(val: string) {
        return val;
    }

    static sortByOriginalOrder(originalArray: Column[], elements: Column[]) {
        return elements.sort((a, b) => {
            const indexA = originalArray.findIndex((obj) => obj.index === a.index);
            const indexB = originalArray.findIndex((obj) => obj.index === b.index);
            return indexA - indexB;
        });
    }

    static isNotEmptyArray(value: unknown): boolean {
        return Array.isArray(value) && value.length !== 0;
    }

    static formatDate(date: number): string {
        if (typeof date === 'string') {
            date = parseInt(date, 10);
        }
        const leadingZero = (num: number) => (num < 10 ? '0' : '') + num;

        const d = new Date(date);
        const day = leadingZero(d.getDate());
        const month = leadingZero(d.getMonth() + 1);
        const year = d.getFullYear();
        const hours = leadingZero(d.getHours());
        const minutes = leadingZero(d.getMinutes());
        const seconds = leadingZero(d.getSeconds());

        return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
    }

    static formatDateWithPattern(date: number, pattern: string): string {
        const leadingZero = (num: number) => (num < 10 ? '0' : '') + num;

        const d = new Date(date);
        const day = leadingZero(d.getDate());
        const month = leadingZero(d.getMonth() + 1);
        const year = d.getFullYear();
        const hours = leadingZero(d.getHours());
        const minutes = leadingZero(d.getMinutes());
        const seconds = leadingZero(d.getSeconds());

        if (pattern === 'dd/mm/yyyy') {
            return `${day}/${month}/${year}`;
        }
        return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
    }

    static getTimeFromHCMTimeZone(date: Date) {
        // Chuyển đổi thời gian hiện tại thành UTC
        const utcDate = new Date(date.getTime() + date.getTimezoneOffset() * 60000);

        // Thiết lập thời gian UTC+7 (HCM Timezone)
        const timezoneOffset = 7 * 60; // HCM là UTC+7
        const hcmDate = new Date(utcDate.getTime() + timezoneOffset * 60000);

        // Lấy timestamp (milliseconds since epoch)
        return hcmDate.getTime();
    }

    static getCurrentDateFromHCMTimeZone(): Date {
        const now = new Date();
        now.setHours(0, 0, 0, 0);

        // Lấy thời gian hiện tại tính bằng milliseconds kể từ epoch (01/01/1970)
        const utcTime = now.getTime() + now.getTimezoneOffset() * 60000;

        // HCM là UTC+7, chuyển đổi độ lệch này sang milliseconds
        const hcmOffset = 7 * 60 * 60000;

        // Tạo đối tượng Date mới với thời gian UTC+7
        const hcmTime = new Date(utcTime + hcmOffset);

        // Thiết lập thời điểm bắt đầu của ngày (00:00:00)
        return new Date(hcmTime.getFullYear(), hcmTime.getMonth(), hcmTime.getDate());
    }

    static getDateFromHCMTimeZone(timestamp: number): Date {
        const now = new Date();
        now.setHours(0, 0, 0, 0);

        // Lấy thời gian hiện tại tính bằng milliseconds kể từ epoch (01/01/1970)
        const utcTime = timestamp + now.getTimezoneOffset() * 60000;

        // HCM là UTC+7, chuyển đổi độ lệch này sang milliseconds
        const hcmOffset = 7 * 60 * 60000;

        // Tạo đối tượng Date mới với thời gian UTC+7
        const hcmTime = new Date(utcTime + hcmOffset);

        // Thiết lập thời điểm bắt đầu của ngày (00:00:00)
        return new Date(hcmTime.getFullYear(), hcmTime.getMonth(), hcmTime.getDate());
    }

    static trimValidator(control: FormControl) {
        return control.value !== null && control.value.trim() === '' ? { required: true } : null;
    }

    static naturalNumber2CharacterValidator(control: FormControl) {
        const value = control.value;
        if (!value) {
            return null;
        }
        const isInteger = Number.isInteger(Number(value));
        const isPositive = Number(value) >= 0;
        const isTwoDigit = Number(value) < 100;

        if (!isInteger || !isPositive || !isTwoDigit) {
            return { naturalNumber2Character: true };
        }
        return null;
    }

    static multipleValuesValidator(control: FormControl) {
        // Validate trường text giá trị, các giá trị cách nhau bởi dấu ,
        const value = control.value;
        if (!value) {
            return null; // handle empty values based on your requirements
        }

        // Split the input value by commas
        const parts = value.split(',');

        // Trim whitespace from each part and check if they are non-empty
        const nonEmpty = parts.every((part) => part.trim() !== '');

        if (!nonEmpty) {
            return { multipleValues: true };
        }

        return null;
    }

    static twoValuesValidator(control: FormControl) {
        // Validate trường text giá trị, hai giá trị cách nhau bởi dấu ,
        const value = control.value;
        if (!value) {
            return null; // handle empty values based on your requirements
        }

        // Split the input value by commas
        const parts = value.split(',');

        // Check if there are exactly 2 parts
        if (parts.length !== 2) {
            return { twoValues: true };
        }

        // Trim whitespace from each part and check if they are non-empty
        const nonEmpty = parts.every((part) => part.trim() !== '');

        if (!nonEmpty) {
            return { twoValues: true };
        }

        return null;
    }

    static trimValueInForm(form: FormGroup, fieldNames: string[]) {
        // Trim list field value in form
        fieldNames.forEach((fieldName) => {
            const control = form.get(fieldName);
            if (control && control.value) {
                const trimmedValue = control.value.trim();
                control.patchValue(trimmedValue);
            }
        });
    }

    static markAllAsTouchedForm(form: FormGroup) {
        Object.keys(form.controls).forEach((field) => {
            const control = form.get(field);
            control?.markAsTouched({ onlySelf: true });
        });
    }

    static convertDotToComma(number: number): string {
        if (!number) return null;
        return number.toString().replace('.', ',');
    }

    static convertCommaToDot(value: string): number {
        if (!value) return null;
        return parseFloat(value.toString().replace(',', '.'));
    }

    static arrayToMap(arr: { label: string; value: number | string }[]) {
        return arr.reduce(
            (map, obj) => {
                map[obj.value] = obj.label;
                return map;
            },
            {} as { [key: number]: string },
        );
    }

    static calculateAverageByConfigWeight(quality: number | null, price: number | null, delivery: number | null, config: SupplierKpi): number | string {
        let totalWeightedSum = 0; // Tổng tích trọng số × giá trị
        let totalWeight = 0; // Tổng trọng số

        // Lấy trọng số từ config
        const t1 = config?.quantity ?? 0;
        const t2 = config?.priceChange ?? 0;
        const t3 = config?.deliveryQuantity ?? 0;

        // Kiểm tra và cộng vào tổng nếu giá trị không null, lớn hơn 0 và có trọng số
        if (quality !== null && quality > 0 && t1 > 0) {
            totalWeightedSum += t1 * quality;
            totalWeight += t1;
        }

        if (price !== null && price > 0 && t2 > 0) {
            totalWeightedSum += t2 * price;
            totalWeight += t2;
        }

        if (delivery !== null && delivery > 0 && t3 > 0) {
            totalWeightedSum += t3 * delivery;
            totalWeight += t3;
        }

        // Nếu tất cả đầu vào đều null, trả về "_"
        if (quality === null && price === null && delivery === null) {
            return '_';
        }

        // Nếu tổng trọng số bằng 0, trả về giá trị mặc định là 0
        if (totalWeight === 0) {
            return 0;
        }

        // Tính trung bình có trọng số
        return parseFloat((totalWeightedSum / totalWeight).toFixed(2));
    }

    static getGrade(score: number | string, isQuality: boolean): string {
        if (typeof score === 'string') return '_';
        const thresholds: number[] = isQuality ? [90, 80, 70, 60] : [95, 80, 70, 60];

        const grades: string[] = ['A', 'B', 'C', 'D', 'E'];

        for (let i = 0; i < thresholds.length; i++) {
            if (score >= thresholds[i]) {
                return grades[i];
            }
        }

        return grades[grades.length - 1]; // Return "E" if score is less than all thresholds
    }

    static naturalNumberValidator(control: FormControl) {
        const value = control.value;
        const isInteger = Number.isInteger(Number(value));
        const isPositive = Number(value) >= 0;
        const isTwoDigit = Number(value) < 100;

        if (!isInteger || !isPositive || !isTwoDigit) {
            return { positiveInteger: true };
        }
        return null;
    }

    static formatDecimalToString(number: number, unitPrice: string, type: string): string {
        /*console.log(number)
        console.log(unitPrice)
        console.log(type)*/
        const formatWithComma = (num: number, decimals: number): string => {
            const parts = num.toFixed(decimals).split('.');
            parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ','); // Thêm dấu phẩy vào phần nguyên
            return parts.join('.');
        };

        if (unitPrice === 'USD') {
            if (type === 'price') {
                return formatWithComma(number, 6); // 6 chữ số sau dấu thập phân
            } else if (type === 'amount') {
                return formatWithComma(number, 2); // 2 chữ số sau dấu thập phân
            }
        } else if (unitPrice === 'VNĐ') {
            return formatWithComma(Math.round(number), 0); // Không có phần thập phân
        }
        return formatWithComma(number, 0);
    }
    static formatAmPmDate(ms: number): string {
        const d = new Date(ms);
        const hours12 = d.getHours() % 12 || 12;
        const minutes = d.getMinutes().toString().padStart(2, '0');
        const ampm = d.getHours() < 12 ? 'am' : 'pm';
        const datePart = Common.formatDateWithPattern(ms, 'dd/mm/yyyy');
        return `${hours12}:${minutes}${ampm} ${datePart}`;
    }

    static resetFormState(form: FormGroup): void {
        Object.keys(form.controls).forEach((key) => {
            const control = form.get(key);
            if (control instanceof FormControl) {
                control.markAsPristine(); // Đặt dirty = false
                control.markAsUntouched(); // Đặt touched = false
            } else if (control instanceof FormGroup) {
                this.resetFormState(control); // Đệ quy cho các FormGroup con
            }
        });
    }

    static cleanObject(obj: Record<string, unknown>): Record<string, unknown> | null {
        if (!obj) return null;
        for (const key in obj) {
            if (obj[key] === null || obj[key] === undefined) {
                delete obj[key];
            }
        }
        return obj;
    }

    static isDeepEqual(a: any, b: any): boolean {
        // Strict equality for primitives
        if (a === b) {
            return true;
        }

        // Check if both are objects (and not null)
        if (typeof a !== 'object' || typeof b !== 'object' || a === null || b === null) {
            return false;
        }

        // Handle arrays
        if (Array.isArray(a) && Array.isArray(b)) {
            if (a.length !== b.length) {
                return false;
            }
            return a.every((item, index) => this.isDeepEqual(item, b[index]));
        }

        // Handle objects
        const keysA = Object.keys(a);
        const keysB = Object.keys(b);
        if (keysA.length !== keysB.length) {
            return false;
        }

        return keysA.every((key) => this.isDeepEqual(a[key], b[key]));
    }
}
