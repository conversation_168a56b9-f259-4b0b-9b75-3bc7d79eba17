<style>
    .custom-table th,
    .custom-table td {
        border: 1px solid #ddd;
    }

    .custom-table {
        border-collapse: collapse;
    }
</style>

<app-sub-header [items]="itemsHeader" [action]="actionHeader"></app-sub-header>
<ng-template #actionHeader>
    <p-button [disabled]="!reportForm?.valid" (click)="getReport()" size="small" label="Xem báo cáo" severity="success" />
</ng-template>
<div class="tw-p-5">
    <form [formGroup]="reportForm" class="tw-bg-white tw-p-5 tw-rounded-xl tw-mb-5">
        <div class="tw-grid lg:tw-grid-cols-3 md:tw-grid-cols-1 sm:tw-grid-cols-1 tw-gap-4 tw-mb-3">
            <div class="tw-flex tw-flex-col tw-space-y-3">
                <div class="tw-font-bold">Ng<PERSON>y hoàn thành</div>
                <div class="p-inputgroup tw-space-x-4">
                    <p-calendar
                        [showButtonBar]="true"
                        formControlName="startDate"
                        [showIcon]="true"
                        [showOnFocus]="true"
                        dateFormat="dd/mm/yy"
                        inputId="buttondisplay"
                        placeholder="Từ ngày"
                        appendTo="body"
                        [maxDate]="reportForm.get('endDate')?.value"
                    />
                    <p-calendar
                        [showButtonBar]="true"
                        formControlName="endDate"
                        [showIcon]="true"
                        [showOnFocus]="true"
                        dateFormat="dd/mm/yy"
                        inputId="buttondisplay"
                        placeholder="Đến ngày"
                        appendTo="body"
                        [minDate]="reportForm.get('startDate')?.value"
                    />
                </div>
            </div>
            <div class="tw-flex tw-flex-col tw-space-y-3">
                <div class="tw-font-bold">Phương thức vận chuyển <span class="tw-text-red-600">(*)</span>:</div>
                <div>
                    <app-filter-table
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            paramForm: 'id',
                            rsql: true,
                            url: '/sc/api/shipping-method',
                        }"
                        [initValue]="reportForm.get('shippingMethodId').value"
                        placeholder="Chon phương thức vận chuyển"
                        formControlName="shippingMethodId"
                    ></app-filter-table>
                </div>
            </div>
            <div class="tw-flex tw-flex-col tw-space-y-3">
                <div class="tw-font-bold">Điều kiện giao hàng</div>
                <div>
                    <app-filter-table
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'deliveryCondition',
                            fieldLabel: 'deliveryCondition',
                            rsql: true,
                            url: '/sc/api/bo/search',
                            body: {
                                deliveryCondition: '!=null',
                            }
                        }"
                        formControlName="deliveryCondition"
                        placeholder="Chon điều kiện giao hàng"
                    ></app-filter-table>
                </div>
            </div>
        </div>
        <div class="tw-grid lg:tw-grid-cols-3 md:tw-grid-cols-1 sm:tw-grid-cols-1 tw-gap-4 tw-mb-3">
            <div class="tw-flex tw-flex-col tw-space-y-3">
                <div class="tw-font-bold">Đơn vị <span class="tw-text-red-600">(*)</span>:</div>
                <div>
                    <app-filter-table
                        rsql="true"
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            rsql: true,
                            paramForm: 'id',
                            sort: 'id,asc',
                            url: '/sc/api/international-rate-unit/search',
                            body: { shippingMethodId: reportForm.get('shippingMethodId').value },
                        }"
                        formControlName="rateUnitId"
                        [disabled]="!reportForm.get('shippingMethodId').value"
                        placeholder="Chọn đơn vị"
                    ></app-filter-table>
                </div>
                <!-- <div *ngIf="reportForm.get('contractId').errors && reportForm.get('contractId').touched" class="text-red-600">
                    <div *ngIf="reportForm.get('contractId').errors['required']">Tên dự án là trường bắt buộc</div>
                </div> -->
            </div>

            <div class="tw-flex tw-flex-col tw-space-y-3">
                <div class="tw-font-bold">Nhà cung cấp</div>
                <div>
                    <app-filter-table
                        field="name"
                        rsql="true"
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'shortName',
                            rsql: true,
                            param: 'name',
                            url: '/sc/api/logistics/search',
                            body: { type: 0 },
                        }"
                        formControlName="logisticId"
                        placeholder="Chọn nhà cung cấp"
                    ></app-filter-table>
                </div>
            </div>
        </div>
    </form>

    <div class="tw-p-4 tw-relative">
        <p-tabView>
            <p-tabPanel header="Biểu đồ">
                <div>
                    <div [hidden]="data != null" class="text-center">Không có dữ liệu</div>
                    <div [hidden]="data == null">
                        <canvas #chartCanvas id="line" width="100%"></canvas>
                    </div>
                </div>
            </p-tabPanel>
            <p-tabPanel header="Bảng">
                <p-table
                    [value]="data"
                    [paginator]="true"
                    [rows]="pageable.size"
                    [rowHover]="true"
                    styleClass="p-datatable-striped"
                    responsiveLayout="scroll"
                    (onPage)="logEvent($event)"
                    [(first)]="pageable.first"
                >
                    <ng-template pTemplate="header">
                        <tr>
                            <th>STT</th>
                            <th style="min-width: 9rem">
                                <div class="tw-flex">Mã BO</div>
                            </th>

                            <th style="min-width: 9rem">
                                <div class="tw-flex">Phương thức vận chuyển</div>
                            </th>

                            <th style="min-width: 10rem">
                                <div class="tw-flex">Điều kiện giao hàng</div>
                            </th>

                            <th style="min-width: 10rem">
                                <div class="tw-flex">Nhà cung cấp</div>
                            </th>

                            <th style="min-width: 16rem">
                                <div class="tw-flex">Đơn giá (USD)</div>
                            </th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-report let-rowIndex="rowIndex">
                        <tr>
                            <td>
                                {{ rowIndex + 1 }}
                            </td>
                            <td style="max-width: 200px" [title]="report.boCode">
                                <a routerLink="/sc/bo/{{ report.boId }}">{{ report.boCode }}</a>
                            </td>
                            <td style="max-width: 200px" [title]="report.shippingMethod">
                                {{ report.shippingMethod }}
                            </td>
                            <td style="max-width: 200px" [title]="report.deliveryCondition">
                                {{ report.deliveryCondition }}
                            </td>
                            <td style="max-width: 200px" [title]="report.logisticName">
                                {{ report.logisticName }}
                            </td>
                            <td style="max-width: 300px" [title]="report.internationalPrice">
                                {{ report.internationalPrice }}
                            </td>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="emptymessage">
                        <tr>
                            <td colspan="6" class="text-center">
                                Không có dữ liệu
                            </td>
                        </tr>
                    </ng-template>
                </p-table>
            </p-tabPanel>
        </p-tabView>
    </div>
</div>
