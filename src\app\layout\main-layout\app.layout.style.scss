.tw-grid-cols-sidebar-expand {
    grid-template-columns: 18rem minmax(0, 1fr) ;
  }

  .tw-grid-cols-sidebar-collapse {
    grid-template-columns: 4rem minmax(0, 1fr) ;
  }

  .tw-grid-cols-sidebar-close {
    grid-template-columns: 0rem minmax(0, 1fr) ;
  }


  .tw-sidebar-mobile-hidden {
    transform: translateX(-100%);
  }

  .tw-sidebar-mobile-shown {
    transform: translateX(0);
  }


  .sidebar-mobile {
    display: block;
    width: 18rem;
    top: 0 !important;
    height: 100vh !important
  }
