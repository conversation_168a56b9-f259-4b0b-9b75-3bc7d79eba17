/* eslint-disable @typescript-eslint/no-explicit-any */
import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { NgForOf, NgIf } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { TableModule } from 'primeng/table';
import { DialogModule } from 'primeng/dialog';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { Column } from '../../../../models/interface';
import { LoadingService } from '../../../../shared/services/loading.service';
import { finalize } from 'rxjs';
import { environment } from '../../../../../environments/environment';
import { ConfirmationService, MessageService, SharedModule } from 'primeng/api';
import { CdkDrag, CdkDragDrop, CdkDropList } from '@angular/cdk/drag-drop';
import { ActivatedRoute, Router } from '@angular/router';
import { AcceptanceDocumentService } from '../../../../services/smart-qc/acceptance-document/acceptance-document.service';
import { AuthService } from '../../../../core/auth/auth.service';
import { HasAnyAuthorityDirective } from '../../../../shared/directives/has-any-authority.directive';
import { TableCommonService } from '../../../../shared/table-module/table.common.service';
import { AcceptanceDocument, Template } from '../../../../models/interface/smart-qc';
import { TableCommonModule } from '../../../../shared/table-module/table.common.module';
import { SubHeaderComponent } from '../../../../shared/components/sub-header/sub-header.component';
import { firstValueFrom } from 'rxjs';

@Component({
    selector: 'app-acceptance-create',
    templateUrl: './acceptance.document.create.html',
    styleUrls: ['./acceptance.document.create.style.scss'],
    standalone: true,
    imports: [
        SharedModule,
        DropdownModule,
        HasAnyAuthorityDirective,
        InputTextModule,
        InputTextareaModule,
        NgIf,
        ReactiveFormsModule,
        ButtonModule,
        TableModule,
        NgForOf,
        DialogModule,
        CdkDrag,
        CdkDropList,
        TableCommonModule,
        SubHeaderComponent,
    ],
})
// eslint-disable-next-line @angular-eslint/component-class-suffix
export class AcceptanceDocumentCreate implements OnInit {
    id: number = null;
    @ViewChild('actionImage') actionImage: TemplateRef<Element>;

    reportForm: FormGroup;
    itemsHeader = [{ label: 'Báo cáo' }, { label: 'Hồ sơ nghiệm thu', url: '' }];

    downloadExportFileLink: string = '';
    downloadExportFileLinkArray: string[] = [];

    // Image
    selectedImages: any[] = [];
    numberImagesSelected: number;
    selectedImagesTemp: any[] = [];
    colsImage = [
        { field: 'checklistName', header: 'Tiêu đề' },
        { field: 'checklistDescription', header: 'Nội dung' },
        { field: 'action', header: 'Thao tác' },
    ];

    isOpenModalAddImage: boolean = false;
    isOpenModalConfirmDeleteImage: boolean = false;
    deleteImageIdTemp: number;
    removeIdsImage: number[];

    // Station
    colsStation = [
        { field: 'areaName', header: 'Tỉnh/ thành phố' },
        { field: 'stationName', header: 'Tên trạm' },
        { field: 'fullName', header: 'SubPM' },
        { field: 'action', header: 'Thao tác' },
    ];
    numberStationsSelected: number;
    isOpenModalAddStation: boolean = false;
    deleteStationIdTemp: number;
    removeIdsStation: number[];
    selectedStations: any[] = [];
    selectedStationsTemp: any[] = [];
    isOpenModalConfirmDeleteStation: boolean = false;
    areaIdsAddStation: number[] = [];
    stationIdsAddStation: number[] = [];

    isOpenModalExport: boolean = false;

    constructor(
        private formBuilder: FormBuilder,
        private tableCommonService: TableCommonService,
        private loadingService: LoadingService,
        private acceptanceDocumentService: AcceptanceDocumentService,
        private confirmationService: ConfirmationService,
        private route: ActivatedRoute,
        private router: Router,
        private messageService: MessageService,
        protected authService: AuthService,
    ) {
        this.reportForm = this.formBuilder.group({
            contractId: [null, [Validators.required]],
            actionId: [null, [Validators.required]],
            subPmId: [null, []],
            title: [null, [Validators.required]],
        });
    }

    stateImage: QueryObserverBaseResult<Template[]>;
    stateStation: QueryObserverBaseResult<Template[]>;

    columnsImageImportTable: Column[] = [
        { field: 'checklistName', header: 'Tiêu đề' },
        { field: 'checklistDescription', header: 'Nội dung' },
    ];
    columnsStationImportTable: Column[] = [
        { field: 'fullName', header: 'SubPM' },
        { field: 'stationName', header: 'Tên trạm' },
        { field: 'areaName', header: 'Tỉnh/ thành phố' },
    ];
    tableIdImage: string = 'tableImage';
    tableIdStation: string = 'tableStation';

    acceptanceDocument: AcceptanceDocument;

    ngOnInit() {
        if (this.authService.isSubPM()) {
            this.columnsStationImportTable = [
                { field: 'stationName', header: 'Tên trạm' },
                { field: 'areaName', header: 'Tỉnh/ thành phố' },
            ];
            this.colsStation = [
                { field: 'areaName', header: 'Tỉnh/ thành phố' },
                { field: 'stationName', header: 'Tên trạm' },
                { field: 'action', header: 'Thao tác' },
            ];
        }
        this.route.paramMap.subscribe((params) => {
            const id = params.get('id');
            if (id) {
                this.itemsHeader = [
                    { label: 'Quản lý hồ sơ nghiệm thu', url: '/sqc/report/acceptance-document' },
                    { label: 'Chỉnh sửa' },
                ];
                this.id = Number(id);
                // Get data
                this.loadingService.show();
                this.acceptanceDocumentService.getOne(this.id).subscribe({
                    next: (res) => {
                        this.acceptanceDocument = res.body as AcceptanceDocument;
                        this.selectContract(this.acceptanceDocument.contractId, true);
                        this.selectAction(this.acceptanceDocument.actionId);
                        if (
                            this.acceptanceDocument.checklistDetailIds &&
                            this.acceptanceDocument.checklistDetailIds.length > 0
                        ) {
                            this.acceptanceDocumentService
                                .getImageById(this.acceptanceDocument.checklistDetailIds)
                                .subscribe({
                                    next: (res) => {
                                        this.selectedImages = res.body;
                                        this.updateRemoveIdsImage();
                                    },
                                    error: () => {},
                                });
                        }
                        if (this.acceptanceDocument.taskIds && this.acceptanceDocument.taskIds.length > 0) {
                            this.acceptanceDocumentService.getStationById(this.acceptanceDocument.taskIds).subscribe({
                                next: (res) => {
                                    this.selectedStations = res.body;
                                    this.updateRemoveIdsStation();
                                },
                                error: () => {},
                            });
                        }
                        this.reportForm.patchValue({
                            title: this.acceptanceDocument.title,
                        });
                        this.loadingService.hide();
                    },
                    error: () => {
                        this.loadingService.hide();
                        this.messageService.add({
                            key: 'app-alert',
                            severity: 'warn',
                            detail: 'Không thể truy cập thông tin mẫu kiểm tra',
                        });
                    },
                    complete: () => {
                        // Check Auth
                    },
                });
            } else {
                this.id = null;
                this.itemsHeader = [
                    { label: 'Quản lý hồ sơ nghiệm thu', url: '/sqc/report/acceptance-document' },
                    { label: 'Tạo mới' },
                ];
            }
        });
        this.tableCommonService
            .init<Template>({
                tableId: this.tableIdImage,
                queryFn: (filter, body) => this.acceptanceDocumentService.getImage(filter, body),
                configFilter: ['contractId', 'actionId', 'removeIds', 'detailIds'],
                requiredFilter: ['contractId', 'actionId'],
                initialData: [],
            })
            .subscribe((state) => {
                this.stateImage = state;
            });

        this.tableCommonService
            .init<Template>({
                tableId: this.tableIdStation,
                queryFn: (filter, body) => this.acceptanceDocumentService.getStation(filter, body),
                configFilter: ['contractId', 'actionId', 'removeIds', 'subPmIds', 'stationIds', 'areaIds', 'id'],
                requiredFilter: ['contractId', 'actionId'],
                initialData: [],
            })
            .subscribe((state) => {
                this.stateStation = state;
            });

        this.tableCommonService.getRowSelect(this.tableIdImage).subscribe((res: any[]) => {
            this.selectedImagesTemp = res;
            this.numberImagesSelected = res.length;
        });

        this.tableCommonService.getRowSelect(this.tableIdStation).subscribe((res: any[]) => {
            this.selectedStationsTemp = res;
            this.numberStationsSelected = res.length;
        });
    }

    selectContract(value: any, init: boolean) {
        const currentValue = this.reportForm.get('contractId')?.value;
        if (value !== currentValue) {
            if (init) {
                this.reportForm.patchValue({
                    contractId: value,
                });
            } else {
                this.reportForm.patchValue({
                    contractId: value,
                    actionId: null,
                });
            }
        }
    }

    selectAction(value: any) {
        const currentValue = this.reportForm.get('actionId')?.value;
        if (value !== currentValue) {
            this.reportForm.patchValue({
                actionId: value,
            });
            this.tableCommonService.updateFilter(this.tableIdImage, {
                contractId: this.reportForm.get('contractId').value,
                actionId: this.reportForm.get('actionId').value,
            });
            this.tableCommonService.updateFilter(this.tableIdStation, {
                contractId: this.reportForm.get('contractId').value,
                actionId: this.reportForm.get('actionId').value,
            });

            this.selectedImages = [];
            this.selectedImagesTemp = [];
            this.updateRemoveIdsImage();
            this.tableCommonService.updateRowSelect(this.tableIdImage, []);
            this.selectedStations = [];
            this.selectedStationsTemp = [];
            this.updateRemoveIdsStation();
            this.tableCommonService.updateRowSelect(this.tableIdStation, []);
        }
    }

    openModalAddImage() {
        this.isOpenModalAddImage = true;
    }

    openModalAddStation() {
        this.isOpenModalAddStation = true;
    }

    selectImage() {
        this.isOpenModalAddImage = false;
        this.selectedImages = this.selectedImagesTemp;
        this.updateRemoveIdsImage();
    }

    selectAllImage() {
        this.loadingService.show();
        const filter = {
            contractId: this.reportForm.get('contractId').value,
            actionId: this.reportForm.get('actionId').value,
        };
        this.acceptanceDocumentService
            .getAllImage(filter)
            .pipe(
                finalize(() => {
                    this.loadingService.hide();
                }),
            )
            .subscribe({
                next: (res) => {
                    this.tableCommonService.updateRowSelect(this.tableIdImage, res.body);
                    this.selectedImages = res.body;
                    this.isOpenModalAddImage = false;
                    this.updateRemoveIdsImage();
                },
                error: () => {},
                complete: () => {},
            });
    }

    selectStation() {
        this.isOpenModalAddStation = false;
        this.selectedStations = this.selectedStationsTemp;
        /*this.tableCommonService.getRowSelect(this.tableId).subscribe({
            next: (rows) => {
                if (isArray(rows) && rows.length > 0) {
                    this.removeIdsImage = rows.map(row => row.taskDetailId)
                }
            }
        })*/
        this.updateRemoveIdsStation();
    }

    selectAllStation() {
        this.loadingService.show();
        const filter = {
            contractId: this.reportForm.get('contractId').value,
            actionId: this.reportForm.get('actionId').value,
        };
        this.acceptanceDocumentService
            .getAllStation(filter)
            .pipe(
                finalize(() => {
                    this.loadingService.hide();
                }),
            )
            .subscribe({
                next: (res) => {
                    this.tableCommonService.updateRowSelect(this.tableIdStation, res.body);
                    this.selectedStations = res.body;
                    this.isOpenModalAddStation = false;
                    this.selectedStations = res.body;
                    this.updateRemoveIdsStation();
                },
                error: () => {},
                complete: () => {},
            });
    }

    deleteImage(detailId: number) {
        this.deleteImageIdTemp = detailId;
        //this.isOpenModalConfirmDeleteImage = true;
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận xoá hình ảnh',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.confirmDeleteImage();
            },
        });
    }

    confirmDeleteImage() {
        this.selectedImages = this.selectedImages.filter((image) => image.detailId !== this.deleteImageIdTemp);
        this.tableCommonService.updateRowSelect(this.tableIdImage, this.selectedImages);
        this.isOpenModalConfirmDeleteImage = false;
        this.updateRemoveIdsImage();
    }

    deleteStation(taskId: number) {
        this.deleteStationIdTemp = taskId;
        //this.isOpenModalConfirmDeleteStation = true;
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận xoá trạm',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.confirmDeleteStation();
            },
        });
    }

    confirmDeleteStation() {
        this.selectedStations = this.selectedStations.filter((image) => image.taskId !== this.deleteStationIdTemp);
        this.tableCommonService.updateRowSelect(this.tableIdStation, this.selectedStations);
        this.isOpenModalConfirmDeleteStation = false;
        this.updateRemoveIdsStation();
    }

    updateRemoveIdsImage() {
        //this.tableCommonService.updateRowSelect(this.tableIdImage, [])
        this.numberImagesSelected = 0;
        this.removeIdsImage = this.selectedImages.map((row) => row.detailId);
        this.tableCommonService.updateFilter(this.tableIdImage, {
            removeIds: this.removeIdsImage,
        });
    }

    updateRemoveIdsStation() {
        this.numberStationsSelected = 0;
        this.removeIdsStation = this.selectedStations.map((row) => row.taskId);
        this.tableCommonService.updateFilter(this.tableIdStation, {
            removeIds: this.removeIdsStation,
        });
    }

    async downloadFileFromLinkArray() {
        const checklistDetailIds = this.selectedImages.map((image) => image.detailId);
        const taskIds = this.selectedStations.map((station) => station.taskId);
        const chunkedTaskIds = this.splitIntoChunks(taskIds, 15);

        this.loadingService.show();

        try {
            /*console.log(taskIds)
            console.log(chunkedTaskIds)*/
            this.downloadExportFileLinkArray = [];
            for (const chunk of chunkedTaskIds) {
                const filter = {
                    contractId: this.reportForm.get('contractId').value,
                    actionId: this.reportForm.get('actionId').value,
                    title: this.reportForm.get('title').value,
                    checklistDetailIds: checklistDetailIds,
                    taskIds: chunk,
                };

                const res: any = await firstValueFrom(this.acceptanceDocumentService.exportAcceptanceDocument(filter));
                this.downloadFromPath(res.body.message);
                // this.downloadExportFileLinkArray.push(res.body.message)
                //this.downloadExportFileLinkArray.push(environment.HOST_GW + '/smart-qc/api/download?filePath=' + res.body.message);
            }
            this.isOpenModalExport = false;
            // console.log(this.downloadExportFileLinkArray)
        } catch (error) {
            console.error('Export failed:', error);
        } finally {
            this.loadingService.hide();
        }
    }

    downloadFromPath(path: string) {
        const anchor = document.createElement('a');
        anchor.href = environment.HOST_GW + '/smart-qc/api/download?filePath=' + path;
        anchor.download = '';
        document.body.appendChild(anchor);
        anchor.click();
        this.delay(1000).then(r => {
            document.body.removeChild(anchor);
        });
    }

    delay(ms: number): Promise<void> {
        return new Promise((resolve) => setTimeout(resolve, ms));
    }

    private splitIntoChunks<T>(arr: any[], chunkSize: number): any[][] {
        const chunks: any[][] = [];
        for (let i = 0; i < arr.length; i += chunkSize) {
            chunks.push(arr.slice(i, i + chunkSize));
        }
        return chunks;
    }

    exportApproveData() {
        // Init filter
        const stationIds = this.selectedStations.map((station) => station.stationId);
        const filter = {
            contractId: this.reportForm.get('contractId').value,
            actionId: this.reportForm.get('actionId').value,
            stationIds: stationIds,
        };
        this.loadingService.show();
        this.acceptanceDocumentService
            .exportApproveData(filter)
            .pipe(
                finalize(() => {
                    this.loadingService.hide();
                }),
            )
            .subscribe({
                next: (res: any) => {
                    this.isOpenModalExport = true;
                    this.downloadExportFileLink =
                        environment.HOST_GW + '/smart-qc/api/download?filePath=' + res.body.message;
                },
                error: () => {},
                complete: () => {},
            });
    }

    selectAreaAddStation(data: any) {
        this.areaIdsAddStation = data.value;
    }

    selectStationAddStation(data: any) {
        this.stationIdsAddStation = data.value;
    }

    dropImage(event: CdkDragDrop<any[]>) {
        this.moveItemInArray(this.selectedImages, event.previousIndex, event.currentIndex);
    }

    moveItemInArray(array: any[], fromIndex: number, toIndex: number): void {
        if (fromIndex === toIndex) {
            return;
        }
        const element = array[fromIndex];
        array.splice(fromIndex, 1);
        array.splice(toIndex, 0, element);
    }

    createAcceptanceDocs() {
        const checklistDetailIds = this.selectedImages.map((image) => image.detailId);
        const taskIds = this.selectedStations.map((station) => station.taskId);
        const acceptanceDocumentData = {
            ...this.acceptanceDocument,
            contractId: this.reportForm.get('contractId').value,
            actionId: this.reportForm.get('actionId').value,
            title: this.reportForm.get('title').value,
            taskIds: taskIds,
            checklistDetailIds: checklistDetailIds,
        };
        this.loadingService.show();
        if (this.id) {
            // Save
            this.acceptanceDocumentService.update(acceptanceDocumentData).subscribe({
                next: () => {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'success',
                        summary: 'Thành công',
                        detail: 'Cập nhật hồ sơ nghiệm thu thành công',
                    });
                    this.loadingService.hide();
                },
                error: () => {
                    this.loadingService.hide();
                },
            });
        } else {
            // Create
            this.acceptanceDocumentService.create(acceptanceDocumentData).subscribe({
                next: () => {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'success',
                        summary: 'Thành công',
                        detail: 'Tạo hồ sơ nghiệm thu thành công',
                    });
                    this.loadingService.hide();
                },
                error: (err) => {
                    // Check error
                    this.loadingService.hide();
                    if (err.error.errorKey === 'acceptanceDocsExisted') {
                        // Show popup ask override
                        this.confirmationService.confirm({
                            key: 'app-confirm',
                            header: 'Xác nhận ghi đè thông tin',
                            message: 'Bạn có chắc muốn ghi thông tin đè hồ sơ nghiệm thu',
                            icon: 'pi pi-exclamation-triangle',
                            rejectLabel: 'Hủy',
                            acceptLabel: 'Ghi đè',
                            acceptIcon: 'pi pi-check mr-2',
                            rejectIcon: 'pi pi-times mr-2',
                            rejectButtonStyleClass: 'p-button-sm',
                            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
                            accept: () => {
                                this.overrideAcceptanceDocument(acceptanceDocumentData);
                            },
                        });
                    }
                },
            });
        }
    }

    overrideAcceptanceDocument(acceptanceDocumentData: AcceptanceDocument) {
        this.loadingService.show();
        this.acceptanceDocumentService.overrideAcceptanceDocument(acceptanceDocumentData).subscribe({
            next: () => {
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'success',
                    summary: 'Thành công',
                    detail: 'Ghi đè thông tin hồ sơ nghiệm thu thành công',
                });
                this.loadingService.hide();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    gotoListPage() {
        this.router.navigate(['/sqc/report/acceptance-document']);
    }
}
