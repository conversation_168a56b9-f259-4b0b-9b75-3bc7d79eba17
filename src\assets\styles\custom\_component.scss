.p-calendar-w-btn .p-datepicker-trigger {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    background: white;
    color: #444;
    border: 1px solid #ced4da;
    border-left: none;
}
.p-button {
    color: white;
    background: #08549e;
    border: 1px solid #08549e;
    padding: 0.75rem 1.25rem;
    font-size: 1rem;
    transition:
        background-color 0.2s,
        color 0.2s,
        border-color 0.2s,
        box-shadow 0.2s;
    border-radius: 6px;
}
.p-button:enabled:hover {
    background: #4283c5;
    color: white;
    border-color: #4283c5;
}
.p-button:enabled:active {
    background: #4283c5;
    color: #ffffff;
    border-color: #4283c5;
}
.p-button.p-button-outlined {
    background-color: transparent;
    color: #08549e;
    border: 1px solid;
}
.p-button.p-button-outlined:enabled:hover {
    background: rgba(59, 130, 246, 0.04);
    color: #08549e;
    border: 1px solid;
}
.p-button.p-button-outlined:enabled:active {
    background: rgba(59, 130, 246, 0.16);
    color: #08549e;
    border: 1px solid;
}
.p-button.p-button-outlined.p-button-plain {
    color: #6c757d;
    border-color: #6c757d;
}
.p-button.p-button-outlined.p-button-plain:enabled:hover {
    background: #e9ecef;
    color: #6c757d;
}
.p-button.p-button-outlined.p-button-plain:enabled:active {
    background: #dee2e6;
    color: #6c757d;
}
.p-button.p-button-text {
    background-color: transparent;
    color: #444;
    border-color: transparent;
}
.p-button.p-button-text:enabled:hover {
    background: rgba(59, 130, 246, 0.04);
    color: #08549e;
    border-color: transparent;
}
.p-button.p-button-text:enabled:active {
    background: rgba(59, 130, 246, 0.16);
    color: #08549e;
    border-color: transparent;
}
.p-button.p-button-text.p-button-plain {
    color: #6c757d;
}
.p-button.p-button-text.p-button-plain:enabled:hover {
    background: #e9ecef;
    color: #6c757d;
}
.p-button.p-button-text.p-button-plain:enabled:active {
    background: #dee2e6;
    color: #6c757d;
}
.p-button:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 0.2rem #bfdbfe;
}
.p-button .p-button-label {
    transition-duration: 0.2s;
}
.p-button .p-button-icon-left {
    margin-right: 0.5rem;
}
.p-button .p-button-icon-right {
    margin-left: 0.5rem;
}
.p-button .p-button-icon-bottom {
    margin-top: 0.5rem;
}
.p-button .p-button-icon-top {
    margin-bottom: 0.5rem;
}
.p-button .p-badge {
    margin-left: 0.5rem;
    min-width: 1rem;
    height: 1rem;
    line-height: 1rem;
    color: #08549e;
    background-color: #ffffff;
}
.p-button.p-button-raised {
    box-shadow:
        0 3px 1px -2px rgba(0, 0, 0, 0.2),
        0 2px 2px 0 rgba(0, 0, 0, 0.14),
        0 1px 5px 0 rgba(0, 0, 0, 0.12);
}
.p-button.p-button-rounded {
    border-radius: 2rem;
}
.p-button.p-button-icon-only {
    width: 3rem;
    padding: 0.75rem 0;
}
.p-button.p-button-icon-only .p-button-icon-left,
.p-button.p-button-icon-only .p-button-icon-right {
    margin: 0;
}
.p-button.p-button-icon-only.p-button-rounded {
    border-radius: 50%;
    height: 3rem;
}
.p-button.p-button-sm {
    font-size: 0.875rem;
    padding: 0.65625rem 1.09375rem;
}
.p-button.p-button-sm .p-button-icon {
    font-size: 0.875rem;
}
.p-button.p-button-lg {
    font-size: 1.25rem;
    padding: 0.9375rem 1.5625rem;
}
.p-button.p-button-lg .p-button-icon {
    font-size: 1.25rem;
}
.p-button.p-button-loading-label-only .p-button-label {
    margin-left: 0.5rem;
}
.p-button.p-button-loading-label-only .p-button-loading-icon {
    margin-right: 0;
}

.p-fluid .p-button {
    width: 100%;
}
.p-fluid .p-button-icon-only {
    width: 3rem;
}
.p-fluid .p-buttonset {
    display: flex;
}
.p-fluid .p-buttonset .p-button {
    flex: 1;
}

.p-button.p-button-secondary,
.p-buttonset.p-button-secondary > .p-button,
.p-splitbutton.p-button-secondary > .p-button {
    color: #ffffff;
    background: #64748b;
    border: 1px solid #64748b;
}
.p-button.p-button-secondary:enabled:hover,
.p-buttonset.p-button-secondary > .p-button:enabled:hover,
.p-splitbutton.p-button-secondary > .p-button:enabled:hover {
    background: #475569;
    color: #ffffff;
    border-color: #475569;
}
.p-button.p-button-secondary:enabled:focus,
.p-buttonset.p-button-secondary > .p-button:enabled:focus,
.p-splitbutton.p-button-secondary > .p-button:enabled:focus {
    box-shadow: 0 0 0 0.2rem #e2e8f0;
}
.p-button.p-button-secondary:enabled:active,
.p-buttonset.p-button-secondary > .p-button:enabled:active,
.p-splitbutton.p-button-secondary > .p-button:enabled:active {
    background: #334155;
    color: #ffffff;
    border-color: #334155;
}
.p-button.p-button-secondary.p-button-outlined,
.p-buttonset.p-button-secondary > .p-button.p-button-outlined,
.p-splitbutton.p-button-secondary > .p-button.p-button-outlined {
    background-color: transparent;
    color: #64748b;
    border: 1px solid;
}
.p-button.p-button-secondary.p-button-outlined:enabled:hover,
.p-buttonset.p-button-secondary > .p-button.p-button-outlined:enabled:hover,
.p-splitbutton.p-button-secondary > .p-button.p-button-outlined:enabled:hover {
    background: rgba(100, 116, 139, 0.04);
    color: #64748b;
    border: 1px solid;
}
.p-button.p-button-secondary.p-button-outlined:enabled:active,
.p-buttonset.p-button-secondary > .p-button.p-button-outlined:enabled:active,
.p-splitbutton.p-button-secondary > .p-button.p-button-outlined:enabled:active {
    background: rgba(100, 116, 139, 0.16);
    color: #64748b;
    border: 1px solid;
}
.p-button.p-button-secondary.p-button-text,
.p-buttonset.p-button-secondary > .p-button.p-button-text,
.p-splitbutton.p-button-secondary > .p-button.p-button-text {
    background-color: transparent;
    color: #64748b;
    border-color: transparent;
}
.p-button.p-button-secondary.p-button-text:enabled:hover,
.p-buttonset.p-button-secondary > .p-button.p-button-text:enabled:hover,
.p-splitbutton.p-button-secondary > .p-button.p-button-text:enabled:hover {
    background: rgba(100, 116, 139, 0.04);
    border-color: transparent;
    color: #64748b;
}
.p-button.p-button-secondary.p-button-text:enabled:active,
.p-buttonset.p-button-secondary > .p-button.p-button-text:enabled:active,
.p-splitbutton.p-button-secondary > .p-button.p-button-text:enabled:active {
    background: rgba(100, 116, 139, 0.16);
    border-color: transparent;
    color: #64748b;
}

.p-button.p-button-info,
.p-buttonset.p-button-info > .p-button,
.p-splitbutton.p-button-info > .p-button {
    color: #ffffff;
    background: #08549e;
    border: 1px solid #08549e;
}
.p-button.p-button-info:enabled:hover,
.p-buttonset.p-button-info > .p-button:enabled:hover,
.p-splitbutton.p-button-info > .p-button:enabled:hover {
    background: #4283c5;
    color: #ffffff;
    border-color: #4283c5;
}
.p-button.p-button-info:enabled:focus,
.p-buttonset.p-button-info > .p-button:enabled:focus,
.p-splitbutton.p-button-info > .p-button:enabled:focus {
    box-shadow: 0 0 0 0.2rem #bfdbfe;
}
.p-button.p-button-info:enabled:active,
.p-buttonset.p-button-info > .p-button:enabled:active,
.p-splitbutton.p-button-info > .p-button:enabled:active {
    background: #4283c5;
    color: #ffffff;
    border-color: #4283c5;
}
.p-button.p-button-info.p-button-outlined,
.p-buttonset.p-button-info > .p-button.p-button-outlined,
.p-splitbutton.p-button-info > .p-button.p-button-outlined {
    background-color: transparent;
    color: #08549e;
    border: 1px solid;
}
.p-button.p-button-info.p-button-outlined:enabled:hover,
.p-buttonset.p-button-info > .p-button.p-button-outlined:enabled:hover,
.p-splitbutton.p-button-info > .p-button.p-button-outlined:enabled:hover {
    background: rgba(59, 130, 246, 0.04);
    color: #08549e;
    border: 1px solid;
}
.p-button.p-button-info.p-button-outlined:enabled:active,
.p-buttonset.p-button-info > .p-button.p-button-outlined:enabled:active,
.p-splitbutton.p-button-info > .p-button.p-button-outlined:enabled:active {
    background: rgba(59, 130, 246, 0.16);
    color: #08549e;
    border: 1px solid;
}
.p-button.p-button-info.p-button-text,
.p-buttonset.p-button-info > .p-button.p-button-text,
.p-splitbutton.p-button-info > .p-button.p-button-text {
    background-color: transparent;
    color: #444;
    border-color: transparent;
}
.p-button.p-button-info.p-button-text:enabled:hover,
.p-buttonset.p-button-info > .p-button.p-button-text:enabled:hover,
.p-splitbutton.p-button-info > .p-button.p-button-text:enabled:hover {
    background: rgba(59, 130, 246, 0.04);
    border-color: transparent;
    color: #444;
}
.p-button.p-button-info.p-button-text:enabled:active,
.p-buttonset.p-button-info > .p-button.p-button-text:enabled:active,
.p-splitbutton.p-button-info > .p-button.p-button-text:enabled:active {
    background: rgba(59, 130, 246, 0.16);
    border-color: transparent;
    color: #444;
}

.p-button.p-button-success,
.p-buttonset.p-button-success > .p-button,
.p-splitbutton.p-button-success > .p-button {
    color: #ffffff;
    background: #00b87b;
    border: 1px solid #00b87b;
}
.p-button.p-button-success:enabled:hover,
.p-buttonset.p-button-success > .p-button:enabled:hover,
.p-splitbutton.p-button-success > .p-button:enabled:hover {
    background: #02cd8a;
    color: #ffffff;
    border-color: #02cd8a;
}
.p-button.p-button-success:enabled:focus,
.p-buttonset.p-button-success > .p-button:enabled:focus,
.p-splitbutton.p-button-success > .p-button:enabled:focus {
    box-shadow: 0 0 0 0.2rem #bbf7d0;
}
.p-button.p-button-success:enabled:active,
.p-buttonset.p-button-success > .p-button:enabled:active,
.p-splitbutton.p-button-success > .p-button:enabled:active {
    background: #02cd8a;
    color: #ffffff;
    border-color: #02cd8a;
}
.p-button.p-button-success.p-button-outlined,
.p-buttonset.p-button-success > .p-button.p-button-outlined,
.p-splitbutton.p-button-success > .p-button.p-button-outlined {
    background-color: transparent;
    color: #00b87b;
    border: 1px solid;
}
.p-button.p-button-success.p-button-outlined:enabled:hover,
.p-buttonset.p-button-success > .p-button.p-button-outlined:enabled:hover,
.p-splitbutton.p-button-success > .p-button.p-button-outlined:enabled:hover {
    background: rgba(34, 197, 94, 0.04);
    color: #00b87b;
    border: 1px solid;
}
.p-button.p-button-success.p-button-outlined:enabled:active,
.p-buttonset.p-button-success > .p-button.p-button-outlined:enabled:active,
.p-splitbutton.p-button-success > .p-button.p-button-outlined:enabled:active {
    background: rgba(34, 197, 94, 0.16);
    color: #00b87b;
    border: 1px solid;
}
.p-button.p-button-success.p-button-text,
.p-buttonset.p-button-success > .p-button.p-button-text,
.p-splitbutton.p-button-success > .p-button.p-button-text {
    background-color: transparent;
    color: #00b87b;
    border-color: transparent;
}
.p-button.p-button-success.p-button-text:enabled:hover,
.p-buttonset.p-button-success > .p-button.p-button-text:enabled:hover,
.p-splitbutton.p-button-success > .p-button.p-button-text:enabled:hover {
    background: rgba(34, 197, 94, 0.04);
    border-color: transparent;
    color: #00b87b;
}
.p-button.p-button-success.p-button-text:enabled:active,
.p-buttonset.p-button-success > .p-button.p-button-text:enabled:active,
.p-splitbutton.p-button-success > .p-button.p-button-text:enabled:active {
    background: rgba(34, 197, 94, 0.16);
    border-color: transparent;
    color: #00b87b;
}

.p-button.p-button-warning,
.p-buttonset.p-button-warning > .p-button,
.p-splitbutton.p-button-warning > .p-button {
    color: #ffffff;
    background: #f59e0b;
    border: 1px solid #f59e0b;
}
.p-button.p-button-warning:enabled:hover,
.p-buttonset.p-button-warning > .p-button:enabled:hover,
.p-splitbutton.p-button-warning > .p-button:enabled:hover {
    background: #d97706;
    color: #ffffff;
    border-color: #d97706;
}
.p-button.p-button-warning:enabled:focus,
.p-buttonset.p-button-warning > .p-button:enabled:focus,
.p-splitbutton.p-button-warning > .p-button:enabled:focus {
    box-shadow: 0 0 0 0.2rem #fde68a;
}
.p-button.p-button-warning:enabled:active,
.p-buttonset.p-button-warning > .p-button:enabled:active,
.p-splitbutton.p-button-warning > .p-button:enabled:active {
    background: #b45309;
    color: #ffffff;
    border-color: #b45309;
}
.p-button.p-button-warning.p-button-outlined,
.p-buttonset.p-button-warning > .p-button.p-button-outlined,
.p-splitbutton.p-button-warning > .p-button.p-button-outlined {
    background-color: transparent;
    color: #f59e0b;
    border: 1px solid;
}
.p-button.p-button-warning.p-button-outlined:enabled:hover,
.p-buttonset.p-button-warning > .p-button.p-button-outlined:enabled:hover,
.p-splitbutton.p-button-warning > .p-button.p-button-outlined:enabled:hover {
    background: rgba(245, 158, 11, 0.04);
    color: #f59e0b;
    border: 1px solid;
}
.p-button.p-button-warning.p-button-outlined:enabled:active,
.p-buttonset.p-button-warning > .p-button.p-button-outlined:enabled:active,
.p-splitbutton.p-button-warning > .p-button.p-button-outlined:enabled:active {
    background: rgba(245, 158, 11, 0.16);
    color: #f59e0b;
    border: 1px solid;
}
.p-button.p-button-warning.p-button-text,
.p-buttonset.p-button-warning > .p-button.p-button-text,
.p-splitbutton.p-button-warning > .p-button.p-button-text {
    background-color: transparent;
    color: #f59e0b;
    border-color: transparent;
}
.p-button.p-button-warning.p-button-text:enabled:hover,
.p-buttonset.p-button-warning > .p-button.p-button-text:enabled:hover,
.p-splitbutton.p-button-warning > .p-button.p-button-text:enabled:hover {
    background: rgba(245, 158, 11, 0.04);
    border-color: transparent;
    color: #f59e0b;
}
.p-button.p-button-warning.p-button-text:enabled:active,
.p-buttonset.p-button-warning > .p-button.p-button-text:enabled:active,
.p-splitbutton.p-button-warning > .p-button.p-button-text:enabled:active {
    background: rgba(245, 158, 11, 0.16);
    border-color: transparent;
    color: #f59e0b;
}

.p-button.p-button-help,
.p-buttonset.p-button-help > .p-button,
.p-splitbutton.p-button-help > .p-button {
    color: #ffffff;
    background: #a855f7;
    border: 1px solid #a855f7;
}
.p-button.p-button-help:enabled:hover,
.p-buttonset.p-button-help > .p-button:enabled:hover,
.p-splitbutton.p-button-help > .p-button:enabled:hover {
    background: #9333ea;
    color: #ffffff;
    border-color: #9333ea;
}
.p-button.p-button-help:enabled:focus,
.p-buttonset.p-button-help > .p-button:enabled:focus,
.p-splitbutton.p-button-help > .p-button:enabled:focus {
    box-shadow: 0 0 0 0.2rem #e9d5ff;
}
.p-button.p-button-help:enabled:active,
.p-buttonset.p-button-help > .p-button:enabled:active,
.p-splitbutton.p-button-help > .p-button:enabled:active {
    background: #7e22ce;
    color: #ffffff;
    border-color: #7e22ce;
}
.p-button.p-button-help.p-button-outlined,
.p-buttonset.p-button-help > .p-button.p-button-outlined,
.p-splitbutton.p-button-help > .p-button.p-button-outlined {
    background-color: transparent;
    color: #a855f7;
    border: 1px solid;
}
.p-button.p-button-help.p-button-outlined:enabled:hover,
.p-buttonset.p-button-help > .p-button.p-button-outlined:enabled:hover,
.p-splitbutton.p-button-help > .p-button.p-button-outlined:enabled:hover {
    background: rgba(168, 85, 247, 0.04);
    color: #a855f7;
    border: 1px solid;
}
.p-button.p-button-help.p-button-outlined:enabled:active,
.p-buttonset.p-button-help > .p-button.p-button-outlined:enabled:active,
.p-splitbutton.p-button-help > .p-button.p-button-outlined:enabled:active {
    background: rgba(168, 85, 247, 0.16);
    color: #a855f7;
    border: 1px solid;
}
.p-button.p-button-help.p-button-text,
.p-buttonset.p-button-help > .p-button.p-button-text,
.p-splitbutton.p-button-help > .p-button.p-button-text {
    background-color: transparent;
    color: #a855f7;
    border-color: transparent;
}
.p-button.p-button-help.p-button-text:enabled:hover,
.p-buttonset.p-button-help > .p-button.p-button-text:enabled:hover,
.p-splitbutton.p-button-help > .p-button.p-button-text:enabled:hover {
    background: rgba(168, 85, 247, 0.04);
    border-color: transparent;
    color: #a855f7;
}
.p-button.p-button-help.p-button-text:enabled:active,
.p-buttonset.p-button-help > .p-button.p-button-text:enabled:active,
.p-splitbutton.p-button-help > .p-button.p-button-text:enabled:active {
    background: rgba(168, 85, 247, 0.16);
    border-color: transparent;
    color: #a855f7;
}

.p-button.p-button-danger,
.p-buttonset.p-button-danger > .p-button,
.p-splitbutton.p-button-danger > .p-button {
    color: #ffffff;
    background: #f4516c;
    border: 1px solid #f4516c;
}
.p-button.p-button-danger:enabled:hover,
.p-buttonset.p-button-danger > .p-button:enabled:hover,
.p-splitbutton.p-button-danger > .p-button:enabled:hover {
    background: #dc2626;
    color: #ffffff;
    border-color: #dc2626;
}
.p-button.p-button-danger:enabled:focus,
.p-buttonset.p-button-danger > .p-button:enabled:focus,
.p-splitbutton.p-button-danger > .p-button:enabled:focus {
    box-shadow: 0 0 0 0.2rem #fecaca;
}
.p-button.p-button-danger:enabled:active,
.p-buttonset.p-button-danger > .p-button:enabled:active,
.p-splitbutton.p-button-danger > .p-button:enabled:active {
    background: #b91c1c;
    color: #ffffff;
    border-color: #b91c1c;
}
.p-button.p-button-danger.p-button-outlined,
.p-buttonset.p-button-danger > .p-button.p-button-outlined,
.p-splitbutton.p-button-danger > .p-button.p-button-outlined {
    background-color: transparent;
    color: #f4516c;
    border: 1px solid;
}
.p-button.p-button-danger.p-button-outlined:enabled:hover,
.p-buttonset.p-button-danger > .p-button.p-button-outlined:enabled:hover,
.p-splitbutton.p-button-danger > .p-button.p-button-outlined:enabled:hover {
    background: rgba(239, 68, 68, 0.04);
    color: #f4516c;
    border: 1px solid;
}
.p-button.p-button-danger.p-button-outlined:enabled:active,
.p-buttonset.p-button-danger > .p-button.p-button-outlined:enabled:active,
.p-splitbutton.p-button-danger > .p-button.p-button-outlined:enabled:active {
    background: rgba(239, 68, 68, 0.16);
    color: #f4516c;
    border: 1px solid;
}
.p-button.p-button-danger.p-button-text,
.p-buttonset.p-button-danger > .p-button.p-button-text,
.p-splitbutton.p-button-danger > .p-button.p-button-text {
    background-color: transparent;
    color: #f4516c;
    border-color: transparent;
}
.p-button.p-button-danger.p-button-text:enabled:hover,
.p-buttonset.p-button-danger > .p-button.p-button-text:enabled:hover,
.p-splitbutton.p-button-danger > .p-button.p-button-text:enabled:hover {
    background: rgba(239, 68, 68, 0.04);
    border-color: transparent;
    color: #f4516c;
}
.p-button.p-button-danger.p-button-text:enabled:active,
.p-buttonset.p-button-danger > .p-button.p-button-text:enabled:active,
.p-splitbutton.p-button-danger > .p-button.p-button-text:enabled:active {
    background: rgba(239, 68, 68, 0.16);
    border-color: transparent;
    color: #f4516c;
}

.p-button.p-button-link {
    color: #4283c5;
    background: transparent;
    border: transparent;
}
.p-button.p-button-link:enabled:hover {
    background: transparent;
    color: #4283c5;
    border-color: transparent;
}
.p-button.p-button-link:enabled:hover .p-button-label {
    text-decoration: underline;
}
.p-button.p-button-link:enabled:focus {
    background: transparent;
    box-shadow: 0 0 0 0.2rem #bfdbfe;
    border-color: transparent;
}
.p-button.p-button-link:enabled:active {
    background: transparent;
    color: #4283c5;
    border-color: transparent;
}

.p-speeddial-button.p-button.p-button-icon-only {
    width: 4rem;
    height: 4rem;
}
.p-speeddial-button.p-button.p-button-icon-only .p-button-icon {
    font-size: 1.3rem;
}
.p-speeddial-button.p-button.p-button-icon-only .p-icon {
    width: 1.3rem;
    height: 1.3rem;
}

.p-speeddial-list {
    outline: 0 none;
}

.p-speeddial-item.p-focus > .p-speeddial-action {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 0.2rem #bfdbfe;
}

.p-speeddial-action {
    width: 3rem;
    height: 3rem;
    background: #495057;
    color: #fff;
}
.p-speeddial-action:hover {
    background: #022354;
    color: #fff;
}

.p-speeddial-direction-up .p-speeddial-item {
    margin: 0.25rem 0;
}
.p-speeddial-direction-up .p-speeddial-item:first-child {
    margin-bottom: 0.5rem;
}

.p-speeddial-direction-down .p-speeddial-item {
    margin: 0.25rem 0;
}
.p-speeddial-direction-down .p-speeddial-item:first-child {
    margin-top: 0.5rem;
}

.p-speeddial-direction-left .p-speeddial-item {
    margin: 0 0.25rem;
}
.p-speeddial-direction-left .p-speeddial-item:first-child {
    margin-right: 0.5rem;
}

.p-speeddial-direction-right .p-speeddial-item {
    margin: 0 0.25rem;
}
.p-speeddial-direction-right .p-speeddial-item:first-child {
    margin-left: 0.5rem;
}

.p-speeddial-circle .p-speeddial-item,
.p-speeddial-semi-circle .p-speeddial-item,
.p-speeddial-quarter-circle .p-speeddial-item {
    margin: 0;
}
.p-speeddial-circle .p-speeddial-item:first-child,
.p-speeddial-circle .p-speeddial-item:last-child,
.p-speeddial-semi-circle .p-speeddial-item:first-child,
.p-speeddial-semi-circle .p-speeddial-item:last-child,
.p-speeddial-quarter-circle .p-speeddial-item:first-child,
.p-speeddial-quarter-circle .p-speeddial-item:last-child {
    margin: 0;
}

.p-speeddial-mask {
    background-color: rgba(0, 0, 0, 0.4);
}

.p-splitbutton {
    border-radius: 6px;
}
.p-splitbutton.p-button-outlined > .p-button {
    background-color: transparent;
    color: #08549e;
    border: 1px solid;
}
.p-splitbutton.p-button-outlined > .p-button:enabled:hover,
.p-splitbutton.p-button-outlined > .p-button:not(button):not(a):not(.p-disabled):hover {
    background: rgba(59, 130, 246, 0.04);
    color: #08549e;
}
.p-splitbutton.p-button-outlined > .p-button:enabled:active,
.p-splitbutton.p-button-outlined > .p-button:not(button):not(a):not(.p-disabled):active {
    background: rgba(59, 130, 246, 0.16);
    color: #08549e;
}
.p-splitbutton.p-button-outlined.p-button-plain > .p-button {
    color: #6c757d;
    border-color: #6c757d;
}
.p-splitbutton.p-button-outlined.p-button-plain > .p-button:enabled:hover,
.p-splitbutton.p-button-outlined.p-button-plain > .p-button:not(button):not(a):not(.p-disabled):hover {
    background: #e9ecef;
    color: #6c757d;
}
.p-splitbutton.p-button-outlined.p-button-plain > .p-button:enabled:active,
.p-splitbutton.p-button-outlined.p-button-plain > .p-button:not(button):not(a):not(.p-disabled):active {
    background: #dee2e6;
    color: #6c757d;
}
.p-splitbutton.p-button-text > .p-button {
    background-color: transparent;
    color: #08549e;
    border-color: transparent;
}
.p-splitbutton.p-button-text > .p-button:enabled:hover,
.p-splitbutton.p-button-text > .p-button:not(button):not(a):not(.p-disabled):hover {
    background: rgba(59, 130, 246, 0.04);
    color: #08549e;
    border-color: transparent;
}
.p-splitbutton.p-button-text > .p-button:enabled:active,
.p-splitbutton.p-button-text > .p-button:not(button):not(a):not(.p-disabled):active {
    background: rgba(59, 130, 246, 0.16);
    color: #08549e;
    border-color: transparent;
}
.p-splitbutton.p-button-text.p-button-plain > .p-button {
    color: #6c757d;
}
.p-splitbutton.p-button-text.p-button-plain > .p-button:enabled:hover,
.p-splitbutton.p-button-text.p-button-plain > .p-button:not(button):not(a):not(.p-disabled):hover {
    background: #e9ecef;
    color: #6c757d;
}
.p-splitbutton.p-button-text.p-button-plain > .p-button:enabled:active,
.p-splitbutton.p-button-text.p-button-plain > .p-button:not(button):not(a):not(.p-disabled):active {
    background: #dee2e6;
    color: #6c757d;
}
.p-splitbutton.p-button-raised {
    box-shadow:
        0 3px 1px -2px rgba(0, 0, 0, 0.2),
        0 2px 2px 0 rgba(0, 0, 0, 0.14),
        0 1px 5px 0 rgba(0, 0, 0, 0.12);
}
.p-splitbutton.p-button-rounded {
    border-radius: 2rem;
}
.p-splitbutton.p-button-rounded > .p-button {
    border-radius: 2rem;
}
.p-splitbutton.p-button-sm > .p-button {
    font-size: 0.875rem;
    padding: 0.65625rem 1.09375rem;
}
.p-splitbutton.p-button-sm > .p-button .p-button-icon {
    font-size: 0.875rem;
}
.p-splitbutton.p-button-lg > .p-button {
    font-size: 1.25rem;
    padding: 0.9375rem 1.5625rem;
}
.p-splitbutton.p-button-lg > .p-button .p-button-icon {
    font-size: 1.25rem;
}

.p-splitbutton.p-button-secondary.p-button-outlined > .p-button {
    background-color: transparent;
    color: #64748b;
    border: 1px solid;
}
.p-splitbutton.p-button-secondary.p-button-outlined > .p-button:enabled:hover,
.p-splitbutton.p-button-secondary.p-button-outlined > .p-button:not(button):not(a):not(.p-disabled):hover {
    background: rgba(100, 116, 139, 0.04);
    color: #64748b;
}
.p-splitbutton.p-button-secondary.p-button-outlined > .p-button:enabled:active,
.p-splitbutton.p-button-secondary.p-button-outlined > .p-button:not(button):not(a):not(.p-disabled):active {
    background: rgba(100, 116, 139, 0.16);
    color: #64748b;
}
.p-splitbutton.p-button-secondary.p-button-text > .p-button {
    background-color: transparent;
    color: #64748b;
    border-color: transparent;
}
.p-splitbutton.p-button-secondary.p-button-text > .p-button:enabled:hover,
.p-splitbutton.p-button-secondary.p-button-text > .p-button:not(button):not(a):not(.p-disabled):hover {
    background: rgba(100, 116, 139, 0.04);
    border-color: transparent;
    color: #64748b;
}
.p-splitbutton.p-button-secondary.p-button-text > .p-button:enabled:active,
.p-splitbutton.p-button-secondary.p-button-text > .p-button:not(button):not(a):not(.p-disabled):active {
    background: rgba(100, 116, 139, 0.16);
    border-color: transparent;
    color: #64748b;
}

.p-splitbutton.p-button-info.p-button-outlined > .p-button {
    background-color: transparent;
    color: #08549e;
    border: 1px solid;
}
.p-splitbutton.p-button-info.p-button-outlined > .p-button:enabled:hover,
.p-splitbutton.p-button-info.p-button-outlined > .p-button:not(button):not(a):not(.p-disabled):hover {
    background: rgba(59, 130, 246, 0.04);
    color: #08549e;
}
.p-splitbutton.p-button-info.p-button-outlined > .p-button:enabled:active,
.p-splitbutton.p-button-info.p-button-outlined > .p-button:not(button):not(a):not(.p-disabled):active {
    background: rgba(59, 130, 246, 0.16);
    color: #08549e;
}
.p-splitbutton.p-button-info.p-button-text > .p-button {
    background-color: transparent;
    color: #08549e;
    border-color: transparent;
}
.p-splitbutton.p-button-info.p-button-text > .p-button:enabled:hover,
.p-splitbutton.p-button-info.p-button-text > .p-button:not(button):not(a):not(.p-disabled):hover {
    background: rgba(59, 130, 246, 0.04);
    border-color: transparent;
    color: #08549e;
}
.p-splitbutton.p-button-info.p-button-text > .p-button:enabled:active,
.p-splitbutton.p-button-info.p-button-text > .p-button:not(button):not(a):not(.p-disabled):active {
    background: rgba(59, 130, 246, 0.16);
    border-color: transparent;
    color: #08549e;
}

.p-splitbutton.p-button-success.p-button-outlined > .p-button {
    background-color: transparent;
    color: #00b87b;
    border: 1px solid;
}
.p-splitbutton.p-button-success.p-button-outlined > .p-button:enabled:hover,
.p-splitbutton.p-button-success.p-button-outlined > .p-button:not(button):not(a):not(.p-disabled):hover {
    background: rgba(34, 197, 94, 0.04);
    color: #00b87b;
}
.p-splitbutton.p-button-success.p-button-outlined > .p-button:enabled:active,
.p-splitbutton.p-button-success.p-button-outlined > .p-button:not(button):not(a):not(.p-disabled):active {
    background: rgba(34, 197, 94, 0.16);
    color: #00b87b;
}
.p-splitbutton.p-button-success.p-button-text > .p-button {
    background-color: transparent;
    color: #00b87b;
    border-color: transparent;
}
.p-splitbutton.p-button-success.p-button-text > .p-button:enabled:hover,
.p-splitbutton.p-button-success.p-button-text > .p-button:not(button):not(a):not(.p-disabled):hover {
    background: rgba(34, 197, 94, 0.04);
    border-color: transparent;
    color: #00b87b;
}
.p-splitbutton.p-button-success.p-button-text > .p-button:enabled:active,
.p-splitbutton.p-button-success.p-button-text > .p-button:not(button):not(a):not(.p-disabled):active {
    background: rgba(34, 197, 94, 0.16);
    border-color: transparent;
    color: #00b87b;
}

.p-splitbutton.p-button-warning.p-button-outlined > .p-button {
    background-color: transparent;
    color: #f59e0b;
    border: 1px solid;
}
.p-splitbutton.p-button-warning.p-button-outlined > .p-button:enabled:hover,
.p-splitbutton.p-button-warning.p-button-outlined > .p-button:not(button):not(a):not(.p-disabled):hover {
    background: rgba(245, 158, 11, 0.04);
    color: #f59e0b;
}
.p-splitbutton.p-button-warning.p-button-outlined > .p-button:enabled:active,
.p-splitbutton.p-button-warning.p-button-outlined > .p-button:not(button):not(a):not(.p-disabled):active {
    background: rgba(245, 158, 11, 0.16);
    color: #f59e0b;
}
.p-splitbutton.p-button-warning.p-button-text > .p-button {
    background-color: transparent;
    color: #f59e0b;
    border-color: transparent;
}
.p-splitbutton.p-button-warning.p-button-text > .p-button:enabled:hover,
.p-splitbutton.p-button-warning.p-button-text > .p-button:not(button):not(a):not(.p-disabled):hover {
    background: rgba(245, 158, 11, 0.04);
    border-color: transparent;
    color: #f59e0b;
}
.p-splitbutton.p-button-warning.p-button-text > .p-button:enabled:active,
.p-splitbutton.p-button-warning.p-button-text > .p-button:not(button):not(a):not(.p-disabled):active {
    background: rgba(245, 158, 11, 0.16);
    border-color: transparent;
    color: #f59e0b;
}

.p-splitbutton.p-button-help.p-button-outlined > .p-button {
    background-color: transparent;
    color: #a855f7;
    border: 1px solid;
}
.p-splitbutton.p-button-help.p-button-outlined > .p-button:enabled:hover,
.p-splitbutton.p-button-help.p-button-outlined > .p-button:not(button):not(a):not(.p-disabled):hover {
    background: rgba(168, 85, 247, 0.04);
    color: #a855f7;
}
.p-splitbutton.p-button-help.p-button-outlined > .p-button:enabled:active,
.p-splitbutton.p-button-help.p-button-outlined > .p-button:not(button):not(a):not(.p-disabled):active {
    background: rgba(168, 85, 247, 0.16);
    color: #a855f7;
}
.p-splitbutton.p-button-help.p-button-text > .p-button {
    background-color: transparent;
    color: #a855f7;
    border-color: transparent;
}
.p-splitbutton.p-button-help.p-button-text > .p-button:enabled:hover,
.p-splitbutton.p-button-help.p-button-text > .p-button:not(button):not(a):not(.p-disabled):hover {
    background: rgba(168, 85, 247, 0.04);
    border-color: transparent;
    color: #a855f7;
}
.p-splitbutton.p-button-help.p-button-text > .p-button:enabled:active,
.p-splitbutton.p-button-help.p-button-text > .p-button:not(button):not(a):not(.p-disabled):active {
    background: rgba(168, 85, 247, 0.16);
    border-color: transparent;
    color: #a855f7;
}

.p-splitbutton.p-button-danger.p-button-outlined > .p-button {
    background-color: transparent;
    color: #f4516c;
    border: 1px solid;
}
.p-splitbutton.p-button-danger.p-button-outlined > .p-button:enabled:hover,
.p-splitbutton.p-button-danger.p-button-outlined > .p-button:not(button):not(a):not(.p-disabled):hover {
    background: rgba(239, 68, 68, 0.04);
    color: #f4516c;
}
.p-splitbutton.p-button-danger.p-button-outlined > .p-button:enabled:active,
.p-splitbutton.p-button-danger.p-button-outlined > .p-button:not(button):not(a):not(.p-disabled):active {
    background: rgba(239, 68, 68, 0.16);
    color: #f4516c;
}
.p-splitbutton.p-button-danger.p-button-text > .p-button {
    background-color: transparent;
    color: #f4516c;
    border-color: transparent;
}
.p-splitbutton.p-button-danger.p-button-text > .p-button:enabled:hover,
.p-splitbutton.p-button-danger.p-button-text > .p-button:not(button):not(a):not(.p-disabled):hover {
    background: rgba(239, 68, 68, 0.04);
    border-color: transparent;
    color: #f4516c;
}
.p-splitbutton.p-button-danger.p-button-text > .p-button:enabled:active,
.p-splitbutton.p-button-danger.p-button-text > .p-button:not(button):not(a):not(.p-disabled):active {
    background: rgba(239, 68, 68, 0.16);
    border-color: transparent;
    color: #f4516c;
}
.p-datatable.p-datatable-striped .p-datatable-tbody > tr:nth-child(even) {
    background: #f1f1f1;
}

.p-datatable.p-datatable-striped .p-datatable-tbody > tr:hover {
    background-color: #ded9d9;
}

.p-datatable {
    .p-button {
        font-size: 0.8rem !important;
        padding: 0.3rem 0.6rem !important;
    }
}

.p-component-overlay.p-datepicker-mask.p-datepicker-mask-scrollblocker.p-component-overlay-enter {
    z-index: 1000;
}
.gird .col-padding-0 .col {
    padding: 0;
}

.p-button .p-button-label {
    font-weight: 400;
    font-size: 14px;
}

.p-button.p-button-sm {
    font-size: 0.875rem;
    padding: 0.4375rem 0.875rem;
}
.p-button.p-button-lg {
    font-size: 1.25rem;
    padding: 0.625rem 1.25rem;
}

.ng-select {
    flex: 1 1 auto !important;
    width: 100%;
    border: 1px solid #ced4da;
    border-radius: 6px;
}

.ng-select.ng-select-single .ng-select-container {
    height: 40px;
    border: 1px solid #ced4da;
}

.tw-text-green-400 {
    --tw-text-opacity: 1;
    color: #00b87b;
}

.tw-text-vertical-center {
    margin: auto;
    margin-left: 0px;
}

.p-datatable .p-datatable-tbody > tr > td {
    text-align: left;
    border: 1px solid #e9ecef;
    border-width: 0 0 1px 0;
    padding: 1rem 1rem;
}

.p-datatable .p-datatable-thead > tr > th {
    text-align: left;
    padding: 1rem 1rem;
    border: 1px solid #e9ecef;
    border-width: 0 0 1px 0;
    font-weight: 700;
    color: #343a40;
    background: #f8f9fa;
    transition: box-shadow 0.2s;
}

.c-pointer {
    cursor: pointer;
}

.p-tag {
    background: white;
    color: #444;
    font-size: 0.75rem;
    font-weight: 700;
    padding: 0.25rem 0.4rem;
    border-radius: 6px;
    border: 1px solid #ced4da;
}
.p-tag.p-tag-success {
    background-color: #00b87b;
    color: #ffffff;
    border: none;
}
.p-tag.p-tag-info {
    background-color: #08549e;
    color: #ffffff;
    border: none;
}
.p-tag.p-tag-warning {
    background-color: #f59e0b;
    color: #ffffff;
    border: none;
}
.p-tag.p-tag-danger {
    background-color: #f4516c;
    color: #ffffff;
    border: none;
}

.p-tag.p-tag-muted {
    background-color: #d5d5d5;
    color: #444;
    border: none;
}

.p-tag-value {
    font-size: 12px;
    font-weight: 500;
}

.login-image {
    display: none;
}

@media (min-width: 1024px) {
    .login-image {
        display: inline-block;
    }
}

.p-datatable .p-datatable-header {
    background: white !important;
    color: #343a40;
    border: none;
    border-width: 1px 0 1px 0;
    padding: 1rem 1rem;
    font-weight: 700;
    border-radius: 8px 8px 0px 0px;
}

.p-toast {
    &.p-toast-top-right,
    &.p-toast-top-left,
    &.p-toast-top-center {
        top: 100px;
    }
    z-index: 11000 !important; /* cao hơn 10000 của .fullscreen */
}

.p-datatable .p-datatable-loading-icon {
    font-size: 2rem;
}
.p-datatable.p-datatable-gridlines .p-datatable-header {
    border-width: 1px 1px 0 1px;
}
.p-datatable.p-datatable-gridlines .p-datatable-footer {
    border-width: 0 1px 1px 1px;
}
.p-datatable.p-datatable-gridlines .p-paginator-top {
    border-width: 0 1px 0 1px;
}
.p-datatable.p-datatable-gridlines .p-paginator-bottom {
    border-width: 0 1px 1px 1px;
}
.p-datatable.p-datatable-gridlines .p-datatable-thead > tr > th {
    border-width: 1px 0 1px 1px;
}
.p-datatable.p-datatable-gridlines .p-datatable-thead > tr > th:last-child {
    border-width: 1px;
}
.p-datatable.p-datatable-gridlines .p-datatable-tbody > tr > td {
    border-width: 1px 0 0 1px;
}
.p-datatable.p-datatable-gridlines .p-datatable-tbody > tr > td:last-child {
    border-width: 1px 1px 0 1px;
}
.p-datatable.p-datatable-gridlines .p-datatable-tbody > tr:last-child > td {
    border-width: 1px 0 1px 1px;
}
.p-datatable.p-datatable-gridlines .p-datatable-tbody > tr:last-child > td:last-child {
    border-width: 1px;
}
.p-datatable.p-datatable-gridlines .p-datatable-tfoot > tr > td {
    border-width: 1px 0 1px 1px;
}
.p-datatable.p-datatable-gridlines .p-datatable-tfoot > tr > td:last-child {
    border-width: 1px 1px 1px 1px;
}
.p-datatable.p-datatable-gridlines .p-datatable-thead + .p-datatable-tfoot > tr > td {
    border-width: 0 0 1px 1px;
}
.p-datatable.p-datatable-gridlines .p-datatable-thead + .p-datatable-tfoot > tr > td:last-child {
    border-width: 0 1px 1px 1px;
}
.p-datatable.p-datatable-gridlines:has(.p-datatable-thead):has(.p-datatable-tbody) .p-datatable-tbody > tr > td {
    border-width: 0 0 1px 1px;
}
.p-datatable.p-datatable-gridlines:has(.p-datatable-thead):has(.p-datatable-tbody) .p-datatable-tbody > tr > td:last-child {
    border-width: 0 1px 1px 1px;
}
.p-datatable.p-datatable-gridlines:has(.p-datatable-tbody):has(.p-datatable-tfoot) .p-datatable-tbody > tr:last-child > td {
    border-width: 0 0 0 1px;
}
.p-datatable.p-datatable-gridlines:has(.p-datatable-tbody):has(.p-datatable-tfoot) .p-datatable-tbody > tr:last-child > td:last-child {
    border-width: 0 1px 0 1px;
}
.p-multiselect-label {
    flex-grow: 1;
    overflow-x: auto;
    text-overflow: unset;
}

.p-multiselect-token-label {
    max-width: none;
}
