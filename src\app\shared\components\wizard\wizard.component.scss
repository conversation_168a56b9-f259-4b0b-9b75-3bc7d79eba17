.wizard a {
    padding: 7px 7px 10px 0px;
    margin-right: 3px;
    background: rgba(173, 216, 230, 0.8);
    position: relative;
    display: inline-block;
}
.wizard a:before {
    width: 0;
    height: 0;
    border-top: 16px inset transparent;
    border-bottom: 18px inset transparent;
    border-left: 16px solid #fff;
    position: absolute;
    content: "";
    top: 0;
    left: 0;
}
.wizard a:after {
    width: 0;
    height: 0;
    border-top: 16px inset transparent;
    border-bottom: 18px inset transparent;
    border-left: 16px solid rgba(173, 216, 230, 0.8);;
    position: absolute;
    content: "";
    top: 0;
    right: -16px;
    z-index: 2;
}


/*.wizard a:last-child {*/
/*    -webkit-border-radius: 0 4px 4px 0;*/
/*    -moz-border-radius: 0 4px 4px 0;*/
/*    border-radius: 0 8px 8px 0;*/
/*}*/

.current-last-child {
    border-top-right-radius: 8px !important;
    border-bottom-right-radius: 8px !important;
}

.wizard .badge {
    margin: 0 5px 0 18px;
    position: relative;
    top: -1px;
}
.wizard a:first-child .badge {
    margin-left: 0;
    padding-left: 16px;
}
.wizard .current {
    background: #366BA1;
    color: #fff;
}
/*.wizard .current:first-child {*/
/*    padding-left: 16px;*/
/*}*/

.wizard-last {
    border-radius: 0px 8px 8px 0px;
}

.wizard .current:after {
    border-left-color:#366BA1;
}


.wizard .blue {
    background: #366BA1;
    color: #fff;
}
.wizard .blue:after {
    border-left-color:#366BA1;
}

.wizard .grey {
    background: darkgrey;
    color: #fff;
}
.wizard .grey:after {
    border-left-color:darkgrey;
}

.wizard .black {
    background: #363636;
    color: #00b87b;
}
.wizard .black:after {
    border-left-color:#363636;
}

.wizard .orange {
    background: #fba65d;
    color: #fff;
}
.wizard .orange:after {
    border-left-color:#fba65d;
}

.wizard .red {
    background: #f4516c;
    color: #fff;
}
.wizard .red:after {
    border-left-color:#f4516c;
}

.bs-wizard {margin-top: 40px;}

/*Form Wizard*/
.bs-wizard {border-bottom: solid 1px #e0e0e0; padding: 0 0 10px 0;}
.bs-wizard > .bs-wizard-step {padding: 0; position: relative;}
.bs-wizard > .bs-wizard-step + .bs-wizard-step {}
.bs-wizard > .bs-wizard-step .bs-wizard-stepnum {color: #595959; font-size: 16px; margin-bottom: 5px;}
.bs-wizard > .bs-wizard-step .bs-wizard-info {color: #999; font-size: 14px;}
.bs-wizard > .bs-wizard-step > .bs-wizard-dot {position: absolute; width: 30px; height: 30px; display: block; background: #3598dc; top: 60px; left: 50%; margin-top: -15px; margin-left: -15px; border-radius: 50%;}
.bs-wizard > .bs-wizard-step > .bs-wizard-dot:after {content: ' '; width: 14px; height: 14px; background: blue; border-radius: 50px; position: absolute; top: 8px; left: 8px; }
.bs-wizard > .bs-wizard-step > .progress {position: relative; border-radius: 0px; height: 8px; box-shadow: none; margin: 20px 0;}
.bs-wizard > .bs-wizard-step > .progress > .progress-bar {width:0px; box-shadow: none; background: #3598dc;}
.bs-wizard > .bs-wizard-step.complete > .progress > .progress-bar {width:100%;}
.bs-wizard > .bs-wizard-step.active > .progress > .progress-bar {width:50%;}
.bs-wizard > .bs-wizard-step:first-child.active > .progress > .progress-bar {width:0%;}
.bs-wizard > .bs-wizard-step:last-child.active > .progress > .progress-bar {width: 100%;}
.bs-wizard > .bs-wizard-step.disabled > .bs-wizard-dot {background-color: #f5f5f5;}
.bs-wizard > .bs-wizard-step.disabled > .bs-wizard-dot:after {opacity: 0;}
.bs-wizard > .bs-wizard-step:first-child  > .progress {left: 50%; width: 50%;}
.bs-wizard > .bs-wizard-step:last-child  > .progress {width: 50%;}
.bs-wizard > .bs-wizard-step.disabled a.bs-wizard-dot{ pointer-events: none; }
.bs-wizard-stepnum { height: 37px;}
