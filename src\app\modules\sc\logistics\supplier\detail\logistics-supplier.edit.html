<app-sub-header
    [items]="[{ label: 'Quản lý dịch vụ nhà cung cấp Logistics', url: './..' }, { label: oldLogistics?.id ? oldLogistics.fullName : 'Tạo mới' }]"
    [action]="action"
></app-sub-header>

<ng-template #action>
    <p-button
        label="Lưu"
        (click)="formComponent.handleSubmit()"
        severity="success"
        size="small"
        *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_logistics_edit']"
    />
    <p-button label="Đóng" routerLink="/sc/logistics" severity="secondary" size="small" />
</ng-template>

<div class="tw-p-4">
    <app-form #form [formGroup]="formGroup" layout="vertical" (onSubmit)="onSubmit($event)">
        <p-panel header="Thông tin cơ bản" [toggleable]="true">
            <div class="tw-grid lg:tw-grid-cols-2 tw-grid-cols-1 tw-gap-4">
                <app-form-item label="Tên đầy đủ">
                    <input type="text" class="tw-w-full" pInputText formControlName="fullName" />
                </app-form-item>

                <app-form-item label="Viết tắt">
                    <input type="text" class="tw-w-full" pInputText formControlName="shortName" />
                </app-form-item>

                <app-form-item label="Loại hình NCC">
                    <p-dropdown [options]="optionType" optionLabel="label" optionValue="value" [formControl]="formGroup.get('type')" class="tw-w-full" />
                </app-form-item>

                <app-form-item label="Dịch vụ NCC">
                    <input pInputText class="tw-w-full" formControlName="serviceProvided" />
                </app-form-item>

                <app-form-item label="Địa chỉ">
                    <input pInputText class="tw-w-full" formControlName="address" />
                </app-form-item>

                <app-form-item label="Quốc gia">
                    <input type="text" class="tw-w-full" pInputText formControlName="national" />
                </app-form-item>

                <app-form-item label="Website">
                    <input type="text" class="tw-w-full" pInputText formControlName="website" />
                </app-form-item>

                <app-form-item label="Người liên hệ">
                    <input type="text" class="tw-w-full" pInputText formControlName="contactPerson" />
                </app-form-item>

                <app-form-item label="Chức vụ">
                    <input type="text" class="tw-w-full" pInputText formControlName="position" />
                </app-form-item>

                <app-form-item label="Email">
                    <input type="text" class="tw-w-full" pInputText formControlName="email" />
                </app-form-item>

                <app-form-item label="Số điện thoại">
                    <input type="text" class="tw-w-full" pInputText formControlName="phone" />
                </app-form-item>

                <app-form-item label="Năm bắt đầu giao dịch">
                    <p-dropdown
                        [showClear]="true"
                        [options]="yearRange"
                        optionLabel="label"
                        optionValue="value"
                        class="tw-w-full"
                        [formControl]="formGroup.get('tradingYear')"
                    />
                </app-form-item>

                <app-form-item label="Trạng thái nhà cung cấp">
                    <p-dropdown [options]="optionStatus" optionLabel="label" optionValue="value" [formControl]="formGroup.get('status')" class="tw-w-full" />
                </app-form-item>

                <app-form-item label="Ghi chú" class="tw-col-span-2">
                    <textarea rows="5" cols="30" pInputTextarea class="tw-w-full" formControlName="note"></textarea>
                </app-form-item>
            </div>
        </p-panel>

        <br />
        <app-form-item label="">
            <div formArrayName="logisticsContracts">
                <p-panel header="Thông tin hợp đồng" [toggleable]="true">
                    <p-table styleClass="p-datatable-gridlines" [value]="contracts.controls">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="max-width: 5rem" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_logistics_edit']">Thao tác</th>
                                <th style="min-width: 9rem">Ngày kí hợp đồng <span class="tw-text-red-500">*</span></th>
                                <th style="min-width: 9rem">Ngày hết hạn hợp đồng</th>
                                <th>Đính kèm <span class="tw-text-red-500">*</span></th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                            <tr *ngIf="item.value.isEdit" [formGroupName]="rowIndex">
                                <td *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_logistics_edit']">
                                    <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                        <button
                                            class="p-link tw-p-2 bg-green-300 hover:tw-bg-green-400 tw-text-white"
                                            (click)="saveItem(rowIndex, 'contract')"
                                            pTooltip="Lưu"
                                            tooltipPosition="top"
                                            type="button"
                                        >
                                            <span class="pi pi-save"></span>
                                        </button>
                                        <button
                                            class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                            (click)="cancelCreate(rowIndex, 'contract')"
                                            pTooltip="Hủy"
                                            tooltipPosition="top"
                                            type="button"
                                        >
                                            <span class="pi pi-times"></span>
                                        </button>
                                    </div>
                                </td>
                                <td>
                                    <app-form-item label="">
                                        <p-calendar
                                            [showButtonBar]="true"
                                            placeholder="dd/MM/yyyy"
                                            [showIcon]="true"
                                            dateFormat="dd/mm/yy"
                                            class="tw-w-full"
                                            appendTo="body"
                                            formControlName="startTimeCustom"
                                        ></p-calendar>
                                    </app-form-item>
                                </td>
                                <td>
                                    <app-form-item label="">
                                        <p-calendar
                                            [showButtonBar]="true"
                                            placeholder="dd/MM/yyyy"
                                            [showIcon]="true"
                                            dateFormat="dd/mm/yy"
                                            class="tw-w-full"
                                            appendTo="body"
                                            formControlName="endTimeCustom"
                                        ></p-calendar>
                                    </app-form-item>
                                </td>
                                <td style="min-width: 15rem">
                                    <app-form-item label="">
                                        <app-button-group-file
                                            class="tw-col-span-2"
                                            (onFileSelected)="handleUploadFileContract($event, rowIndex)"
                                            [attachments]="item.getRawValue().attachments"
                                            simpleUpload=""
                                            formControlName="attachmentIds"
                                            [multiple]="true"
                                        ></app-button-group-file>

                                        <app-button-group-file
                                            *ngIf="item.getRawValue().attachments && item.getRawValue().attachments.length > 0"
                                            class="tw-col-span-2"
                                            (onFileSelected)="handleUploadFileContract($event, rowIndex)"
                                            simpleUpload=""
                                            formControlName="attachmentIds"
                                            [multiple]="true"
                                        ></app-button-group-file>
                                    </app-form-item>
                                </td>
                            </tr>
                            <tr *ngIf="!item.value.isEdit">
                                <td *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_logistics_edit']">
                                    <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                        <button
                                            class="p-link tw-p-2 bg-green-300 hover:tw-bg-green-400 tw-text-white"
                                            (click)="editItem(rowIndex, 'contract')"
                                            pTooltip="Sửa"
                                            tooltipPosition="top"
                                            type="button"
                                            *ngIf="!isAddingContract && !isEditingContract"
                                        >
                                            <span class="pi pi-pencil"></span>
                                        </button>
                                        <button
                                            class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                            (click)="deleteItem(rowIndex, 'contract')"
                                            pTooltip="Xóa"
                                            tooltipPosition="top"
                                            type="button"
                                            *ngIf="!isAddingContract && !isEditingContract"
                                        >
                                            <span class="pi pi-trash"></span>
                                        </button>
                                    </div>
                                </td>
                                <td>{{ item.getRawValue().startTimeCustom | date: 'dd/MM/yyyy' }}</td>
                                <td>
                                    <ng-container *ngIf="item.getRawValue().endTimeCustom; else noEndTime">
                                        {{ item.getRawValue().endTimeCustom | date: 'dd/MM/yyyy' }}
                                    </ng-container>
                                    <ng-template #noEndTime>Không thời hạn</ng-template>
                                </td>
                                <td>
                                    <div class="tw-flex tw-flex-col tw-gap-4">
                                        <ng-container *ngFor="let att of item.getRawValue().attachments">
                                            <app-attachment [attachment]="att"></app-attachment>
                                        </ng-container>
                                    </div>
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <div class="tw-mt-3">
                        <p-button
                            label="Thêm"
                            *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_logistics_edit']"
                            icon="pi pi-plus"
                            severity="info"
                            size="small"
                            [disabled]="isEditingContract || isAddingContract"
                            (click)="addItem('contract')"
                        ></p-button>
                    </div>
                </p-panel>
            </div>
        </app-form-item>
        <br />
        <ng-container formArrayName="logisticsDocuments">
            <p-panel header="Hồ sơ năng lực" [toggleable]="true">
                <p-table styleClass="p-datatable-gridlines" [value]="documents.controls">
                    <ng-template pTemplate="header">
                        <tr>
                            <th style="max-width: 5rem" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_logistics_edit']">Thao tác</th>
                            <th style="min-width: 9rem">Danh mục <span class="tw-text-red-500">*</span></th>
                            <th style="min-width: 10rem">Đính kèm <span class="tw-text-red-500">*</span></th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                        <tr *ngIf="item.value.isEdit" [formGroupName]="rowIndex">
                            <td *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_logistics_edit']">
                                <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                    <button
                                        class="p-link tw-p-2 bg-green-300 hover:tw-bg-green-400 tw-text-white"
                                        (click)="saveItem(rowIndex, 'document')"
                                        pTooltip="Lưu"
                                        tooltipPosition="top"
                                        type="button"
                                    >
                                        <span class="pi pi-save"></span>
                                    </button>
                                    <button
                                        class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                        (click)="cancelCreate(rowIndex, 'document')"
                                        pTooltip="Hủy"
                                        tooltipPosition="top"
                                        type="button"
                                    >
                                        <span class="pi pi-times"></span>
                                    </button>
                                </div>
                            </td>
                            <td>
                                <ng-container *ngIf="!item.getRawValue().active; else templateNameActive">
                                    {{ item.getRawValue().name }}
                                </ng-container>
                                <ng-template #templateNameActive>
                                    <app-form-item label="">
                                        <input pInputText class="tw-w-full" formControlName="name" />
                                    </app-form-item>
                                </ng-template>
                            </td>
                            <td>
                                <app-form-item label="">
                                    <app-button-group-file
                                        class="tw-col-span-2"
                                        (onFileSelected)="handleUploadFileDocument($event, rowIndex)"
                                        [attachments]="item.getRawValue().attachments"
                                        [simpleUpload]="item.getRawValue().attachments && item.getRawValue().attachments.length > 0 ? '' : 'Vui lòng chọn file'"
                                        [multiple]="true"
                                        formControlName="attachmentIds"
                                    ></app-button-group-file>

                                    <app-button-group-file
                                        *ngIf="item.getRawValue().attachments && item.getRawValue().attachments.length > 0"
                                        class="tw-col-span-2"
                                        (onFileSelected)="handleUploadFileDocument($event, rowIndex)"
                                        [simpleUpload]="item.getRawValue().attachments && item.getRawValue().attachments.length > 0 ? '' : 'Vui lòng chọn file'"
                                        [multiple]="true"
                                        formControlName="attachmentIds"
                                    ></app-button-group-file>
                                </app-form-item>
                            </td>
                        </tr>
                        <tr *ngIf="!item.value.isEdit" [formGroupName]="rowIndex">
                            <td *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_logistics_edit']">
                                <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                    <button
                                        class="p-link tw-p-2 bg-green-300 hover:tw-bg-green-400 tw-text-white"
                                        (click)="editItem(rowIndex, 'document')"
                                        pTooltip="Sửa"
                                        tooltipPosition="top"
                                        type="button"
                                        *ngIf="!isAddingDocument && !isEditingDocument && (logisticId || item.value.active)"
                                    >
                                        <span class="pi pi-pencil"></span>
                                    </button>
                                    <button
                                        class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                        (click)="deleteItem(rowIndex, 'document')"
                                        pTooltip="Xóa"
                                        tooltipPosition="top"
                                        type="button"
                                        *ngIf="!isAddingDocument && !isEditingDocument"
                                    >
                                        <span class="pi pi-trash"></span>
                                    </button>
                                </div>
                            </td>
                            <td>{{ item.getRawValue().name }}</td>
                            <td>
                                <ng-container *ngIf="!item.getRawValue().active && !logisticId; else templateDocumentActive">
                                    <app-form-item label="">
                                        <app-button-group-file
                                            class="tw-col-span-2"
                                            (onFileSelected)="handleUploadFileDocument($event, rowIndex)"
                                            [attachments]="item.getRawValue().attachments"
                                            [simpleUpload]="
                                                item.getRawValue().attachments && item.getRawValue().attachments.length > 0 ? '' : 'Vui lòng chọn file'
                                            "
                                            [multiple]="true"
                                            formControlName="attachmentIds"
                                        ></app-button-group-file>
                                        <app-button-group-file
                                            *ngIf="item.getRawValue().attachments && item.getRawValue().attachments.length > 0"
                                            class="tw-col-span-2"
                                            (onFileSelected)="handleUploadFileDocument($event, rowIndex)"
                                            [simpleUpload]="
                                                item.getRawValue().attachments && item.getRawValue().attachments.length > 0 ? '' : 'Vui lòng chọn file'
                                            "
                                            [multiple]="true"
                                            formControlName="attachmentIds"
                                        ></app-button-group-file>
                                    </app-form-item>
                                </ng-container>
                                <ng-template #templateDocumentActive>
                                    <div class="tw-flex tw-flex-col tw-gap-4">
                                        <ng-container *ngFor="let att of item.getRawValue().attachments">
                                            <app-attachment [attachment]="att"></app-attachment>
                                        </ng-container>
                                    </div>
                                </ng-template>
                            </td>
                        </tr>
                    </ng-template>
                </p-table>
                <div class="tw-mt-3">
                    <p-button
                        label="Thêm"
                        *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_logistics_edit']"
                        icon="pi pi-plus"
                        severity="info"
                        size="small"
                        [disabled]="isEditingDocument || isAddingDocument"
                        (click)="addItem('document')"
                    ></p-button>
                </div>
            </p-panel>
        </ng-container>

        <br *ngIf="formGroup.getRawValue().type === optionType[0].value" />
        <ng-container formArrayName="logisticsAgents" *ngIf="formGroup.getRawValue().type === optionType[0].value">
            <p-panel header="Danh sách đại lý" [toggleable]="true">
                <p-table styleClass="p-datatable-gridlines" [value]="agents.controls">
                    <ng-template pTemplate="header">
                        <tr>
                            <th style="max-width: 5rem" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_logistics_edit']">Thao tác</th>
                            <th style="max-width: 30rem">Tên <span class="tw-text-red-500">*</span></th>
                            <th style="min-width: 10rem">Địa chỉ <span class="tw-text-red-500">*</span></th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                        <tr *ngIf="item.value.isEdit" [formGroupName]="rowIndex">
                            <td>
                                <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                    <button
                                        class="p-link tw-p-2 bg-green-300 hover:tw-bg-green-400 tw-text-white"
                                        (click)="saveItem(rowIndex, 'agent')"
                                        pTooltip="Lưu"
                                        tooltipPosition="top"
                                        type="button"
                                    >
                                        <span class="pi pi-save"></span>
                                    </button>
                                    <button
                                        class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                        (click)="cancelCreate(rowIndex, 'agent')"
                                        pTooltip="Hủy"
                                        tooltipPosition="top"
                                        type="button"
                                    >
                                        <span class="pi pi-times"></span>
                                    </button>
                                </div>
                            </td>
                            <td>
                                <app-form-item label="">
                                    <input pInputText class="tw-w-full" formControlName="name" />
                                </app-form-item>
                            </td>
                            <td>
                                <app-form-item label="">
                                    <textarea rows="2" pInputTextarea class="tw-w-full" formControlName="address"></textarea>
                                </app-form-item>
                            </td>
                        </tr>
                        <tr *ngIf="!item.value.isEdit">
                            <td *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_logistics_edit']">
                                <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                    <button
                                        class="p-link tw-p-2 bg-green-300 hover:tw-bg-green-400 tw-text-white"
                                        (click)="editItem(rowIndex, 'agent')"
                                        pTooltip="Sửa"
                                        tooltipPosition="top"
                                        type="button"
                                        *ngIf="!isAddingAgent && !isEditingAgent"
                                    >
                                        <span class="pi pi-pencil"></span>
                                    </button>
                                    <button
                                        class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                        (click)="deleteItem(rowIndex, 'agent')"
                                        pTooltip="Xóa"
                                        tooltipPosition="top"
                                        type="button"
                                        *ngIf="!isAddingAgent && !isEditingAgent"
                                    >
                                        <span class="pi pi-trash"></span>
                                    </button>
                                </div>
                            </td>
                            <td>{{ item.getRawValue().name }}</td>
                            <td>{{ item.getRawValue().address }}</td>
                        </tr>
                    </ng-template>
                </p-table>
                <div class="tw-mt-3">
                    <p-button
                        label="Thêm"
                        icon="pi pi-plus"
                        severity="info"
                        *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_logistics_edit']"
                        size="small"
                        [disabled]="isEditingAgent || isAddingAgent"
                        (click)="addItem('agent')"
                    ></p-button>
                </div>
            </p-panel>
        </ng-container>

        <br *ngIf="formGroup.getRawValue().type === optionType[0].value" />
        <ng-container formArrayName="logisticsEvaluates" *ngIf="formGroup.getRawValue().type === optionType[0].value">
            <p-panel header="Đánh giá định kỳ" [toggleable]="true">
                <p-table styleClass="p-datatable-gridlines" [value]="evaluates.controls">
                    <ng-template pTemplate="header">
                        <tr>
                            <th style="max-width: 5rem" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_logistics_edit']">Thao tác</th>
                            <th>Năm đánh giá <span class="tw-text-red-500">*</span></th>
                            <th>Phiếu đánh giá <span class="tw-text-red-500">*</span></th>
                            <th *ngIf="!isEditingEvaluate && !isAddingEvaluate">Kết quả</th>
                            <th *ngIf="!isEditingEvaluate && !isAddingEvaluate">Xếp hạng</th>
                            <th *ngIf="!isEditingEvaluate && !isAddingEvaluate">Ghi chú</th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                        <tr *ngIf="item.value.isEdit" [formGroupName]="rowIndex">
                            <td *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_logistics_edit']">
                                <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                    <button
                                        class="p-link tw-p-2 bg-green-300 hover:tw-bg-green-400 tw-text-white"
                                        (click)="saveItem(rowIndex, 'evaluate')"
                                        pTooltip="Lưu"
                                        tooltipPosition="top"
                                        type="button"
                                    >
                                        <span class="pi pi-save"></span>
                                    </button>
                                    <button
                                        class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                        (click)="cancelCreate(rowIndex, 'evaluate')"
                                        pTooltip="Hủy"
                                        tooltipPosition="top"
                                        type="button"
                                    >
                                        <span class="pi pi-times"></span>
                                    </button>
                                </div>
                            </td>
                            <td>
                                <app-form-item label="">
                                    <p-dropdown
                                        [showClear]="true"
                                        [options]="yearRange"
                                        optionLabel="label"
                                        optionValue="value"
                                        appendTo="body"
                                        formControlName="year"
                                        class="tw-w-full"
                                    />
                                </app-form-item>
                            </td>
                            <td>
                                <app-form-item label="">
                                    <app-button-group-file
                                        class="tw-col-span-2"
                                        (onClearFile)="handleClearFile(rowIndex, 'evaluate')"
                                        (onFileSelected)="handleUploadFileEvaluate($event, rowIndex)"
                                        [attachment]="item.getRawValue().attachment"
                                        urlTemplate="template_logistics_evaluate.xlsx"
                                        formControlName="attachmentId"
                                        [types]="['excel']"
                                    ></app-button-group-file>
                                </app-form-item>
                            </td>
                        </tr>
                        <tr *ngIf="!item.value.isEdit">
                            <td>
                                <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                    <button
                                        class="p-link tw-p-2 bg-green-300 hover:tw-bg-green-400 tw-text-white"
                                        (click)="editItem(rowIndex, 'evaluate')"
                                        pTooltip="Sửa"
                                        tooltipPosition="top"
                                        type="button"
                                        *ngIf="!isAddingEvaluate && !isEditingEvaluate"
                                    >
                                        <span class="pi pi-pencil"></span>
                                    </button>
                                    <button
                                        class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                        (click)="deleteItem(rowIndex, 'evaluate')"
                                        pTooltip="Xóa"
                                        tooltipPosition="top"
                                        type="button"
                                        *ngIf="!isAddingEvaluate && !isEditingEvaluate"
                                    >
                                        <span class="pi pi-trash"></span>
                                    </button>
                                </div>
                            </td>
                            <td>{{ item.getRawValue().year }}</td>
                            <td>
                                <app-attachment [attachment]="item.getRawValue().attachment"></app-attachment>
                            </td>
                            <td>{{ item.getRawValue().result }}</td>
                            <td>{{ item.getRawValue().rank }}</td>
                            <td>{{ item.getRawValue().note }}</td>
                        </tr>
                    </ng-template>
                </p-table>
                <div class="tw-mt-3">
                    <p-button
                        label="Thêm"
                        *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_logistics_edit']"
                        icon="pi pi-plus"
                        severity="info"
                        size="small"
                        [disabled]="isEditingEvaluate || isAddingEvaluate"
                        (click)="addItem('evaluate')"
                    ></p-button>
                </div>
            </p-panel>
        </ng-container>

        <br *ngIf="formGroup.getRawValue().type === optionType[1].value" />
        <ng-container formGroupName="logisticsFee" *ngIf="formGroup.getRawValue().type === optionType[1].value">
            <p-panel header="Phí bảo hiểm đang áp dụng" [toggleable]="true">
                <div class="tw-grid lg:tw-grid-cols-2 tw-grid-cols-1 tw-gap-4">
                    <app-form-item label="Đường SEA">
                        <div class="p-inputgroup">
                            <app-inputNumber mode="decimal" formControlName="seaRoad" decimalMaxLength="5"></app-inputNumber>
                            <span class="p-inputgroup-addon">%</span>
                        </div>
                    </app-form-item>

                    <app-form-item label="Đường AIR">
                        <div class="p-inputgroup">
                            <app-inputNumber mode="decimal" formControlName="airRoad" decimalMaxLength="5"></app-inputNumber>
                            <span class="p-inputgroup-addon">%</span>
                        </div>
                    </app-form-item>

                    <app-form-item label="LC (AIR-SEA)">
                        <div class="p-inputgroup">
                            <app-inputNumber mode="decimal" formControlName="lcAirSea" decimalMaxLength="5"></app-inputNumber>
                            <span class="p-inputgroup-addon">%</span>
                        </div>
                    </app-form-item>

                    <app-form-item label="Đường bộ nội địa">
                        <div class="p-inputgroup">
                            <app-inputNumber mode="decimal" formControlName="internalRoad" decimalMaxLength="5"></app-inputNumber>
                            <span class="p-inputgroup-addon">%</span>
                        </div>
                    </app-form-item>

                    <app-form-item label="Đường bộ quốc tế">
                        <div class="p-inputgroup">
                            <app-inputNumber mode="decimal" formControlName="internationalRoad" decimalMaxLength="5"></app-inputNumber>
                            <span class="p-inputgroup-addon">%</span>
                        </div>
                    </app-form-item>

                    <app-form-item label="Phí tối thiểu">
                        <div class="tw-flex tw-gap-6">
                            <app-inputNumber mode="decimal" formControlName="minFee" decimalMaxLength="5"></app-inputNumber>
                            <span class="tw-max-w-52">
                                <p-dropdown
                                    [options]="[
                                        { value: 'USD', label: 'USD' },
                                        { value: 'VND', label: 'VNĐ' },
                                    ]"
                                    optionLabel="label"
                                    optionValue="value"
                                    formControlName="currency"
                                />
                            </span>
                        </div>
                    </app-form-item>
                </div>
            </p-panel>
        </ng-container>
        <ng-container *ngIf="logisticId && formGroup.getRawValue().type === optionType[0].value">
            <br />
            <p-panel header="Lịch sử giao dịch" [toggleable]="true">
                <ng-template pTemplate="content">
                    <div class="tw-flex tw-justify-end tw-mb-4 tw-mx-4 tw-gap-4">
                        <p-button icon="pi pi-filter" [outlined]="true" [size]="'small'" (click)="op.toggle($event)"></p-button>
                    </div>

                    <p-table styleClass="p-datatable-gridlines" [scrollable]="true" [value]="oldLogistics?.expensesFwdLst" scrollHeight="700px">
                        <ng-template pTemplate="header">
                            <tr>
                                <th *ngIf="isShipmentInfoVisible" [attr.colspan]="shipmentInfoColspan" class="!tw-text-center">Thông tin lô hàng</th>
                                <th *ngIf="isFeeDeliveryVisible" [attr.colspan]="feeDeliveryColspan" class="!tw-text-center">
                                    Phí từ địa điểm giao hàng đến cảng xuất (VND)
                                </th>
                                <th *ngIf="isExportToImportCostVisible" class="!tw-text-center">Cước từ cảng xuất đến cảng nhập (VND)</th>
                                <th *ngIf="isFeeToFinalDeliveryColspanVisible" [attr.colspan]="feeToFinalDeliveryColspan" class="!tw-text-center">
                                    Phí từ cảng nhập đến địa điểm giao hàng cuối cùng (hóa đơn do Bên B xuất trực tiếp) (VNĐ)
                                </th>
                                <th *ngIf="isImportPortFeeVisible" [attr.colspan]="importPortFeeColspan" class="!tw-text-center">
                                    Phí chi hộ tại cảng nhập (hóa đơn do bên thứ ba xuất)(VNĐ)
                                </th>

                                <th *ngIf="isTotalLogisticsCostVisible" class="!tw-text-center">Tổng chi phí logistic</th>
                            </tr>
                            <tr>
                                <th *ngIf="!columns[0].hide">STT</th>
                                <th *ngIf="!columns[1].hide">Số BO</th>
                                <th *ngIf="!columns[2].hide">Số PO</th>
                                <th *ngIf="!columns[3].hide">Ngày hoàn thành</th>
                                <th *ngIf="!columns[4].hide">Tên người giao hàng(với lô hàng nhập khẩu), Tên người nhận hàng(với lô hàng xuất khẩu)</th>
                                <th *ngIf="!columns[5].hide">Số vận đơn</th>
                                <th *ngIf="!columns[6].hide">Số tờ khai</th>
                                <th *ngIf="!columns[7].hide">Khối lượng tổng trên packing list(KG)</th>
                                <th *ngIf="!columns[8].hide">Khối lượng tổng trên vận đơn(KG)</th>
                                <th *ngIf="!columns[9].hide">Khối lượng tính phí</th>
                                <th *ngIf="!columns[10].hide">DVT khối lượng tính phí</th>
                                <th *ngIf="!columns[11].hide">Phương thức vận chuyển</th>
                                <th *ngIf="!columns[12].hide">Hãng vận chuyển</th>
                                <th *ngIf="!columns[13].hide">Điều kiện giao hàng</th>
                                <th *ngIf="!columns[14].hide">Địa điểm giao hàng</th>
                                <th *ngIf="!columns[15].hide">Cảng xuất</th>
                                <th *ngIf="!columns[16].hide">Cảng nhập</th>
                                <th *ngIf="!columns[17].hide">Địa điểm nhận hàng cuối cùng</th>
                                <th *ngIf="!columns[18].hide">Số hợp đồng/PO/DA</th>
                                <th *ngIf="!columns[19].hide">Mã số kế toán</th>
                                <th *ngIf="!columns[20].hide">Tổng giá trị lô hàng (USD)</th>
                                <th *ngIf="!columns[21].hide">Tỷ giá(USD/VNĐ)</th>
                                <th *ngIf="!columns[22].hide">Giấy phép xuất khẩu</th>
                                <th *ngIf="!columns[23].hide">C/O</th>
                                <th *ngIf="!columns[24].hide">Vận tải từ địa điểm giao hàng đến cảng xuất</th>
                                <th *ngIf="!columns[25].hide">Dịch vụ hải quan</th>
                                <th *ngIf="!columns[26].hide">Phí tại cảng xuất</th>
                                <th *ngIf="!columns[27].hide">Thuế GTGT</th>
                                <th *ngIf="!columns[28].hide">Cước hàng không/biển/bộ qua biên giới</th>
                                <th *ngIf="!columns[29].hide">Giấy phép nhập khẩu</th>
                                <th *ngIf="!columns[30].hide">Dịch vụ hải quan</th>
                                <th *ngIf="!columns[31].hide">Phí tại cảng nhập</th>
                                <th *ngIf="!columns[32].hide">Vận tải từ địa điểm giao hàng đến cảng xuất</th>
                                <th *ngIf="!columns[33].hide">Thuế GTGT</th>
                                <th *ngIf="!columns[34].hide">Hạ tầng</th>
                                <th *ngIf="!columns[35].hide">Lưu kho</th>
                                <th *ngIf="!columns[36].hide">Nâng hạ sửa chữa vệ sinh container</th>
                                <th *ngIf="!columns[37].hide">Khác</th>
                                <th *ngIf="!columns[38].hide">Thuế GTGT</th>
                                <th *ngIf="!columns[39].hide">Tổng chi phí logistic</th>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="body" let-expenses let-rowIndex="rowIndex">
                            <tr>
                                <td *ngIf="!columns[0].hide">{{ rowIndex + 1 }}</td>
                                <td *ngIf="!columns[1].hide">
                                    <a [routerLink]="'/sc/bo/' + expenses.boId">{{ expenses.boCode }}</a>
                                </td>
                                <td *ngIf="!columns[2].hide">{{ expenses.poNumber }}</td>
                                <td *ngIf="!columns[3].hide">{{ expenses.completeDate | date: 'dd/MM/yyyy' }}</td>
                                <td *ngIf="!columns[4].hide">{{ expenses.shipperName }}</td>
                                <td *ngIf="!columns[5].hide">{{ expenses.trackingNumber }}</td>
                                <td *ngIf="!columns[6].hide">{{ expenses.declarationNumber }}</td>
                                <td *ngIf="!columns[7].hide">{{ expenses.totalWeightKgPacking }}</td>
                                <td *ngIf="!columns[8].hide">{{ expenses.totalWeightKgOrder }}</td>
                                <td *ngIf="!columns[9].hide">{{ expenses.chargeableWeight }}</td>
                                <td *ngIf="!columns[10].hide">{{ expenses.chargeableWeightUnit }}</td>
                                <td *ngIf="!columns[11].hide">{{ expenses.deliveryMethod }}</td>
                                <td *ngIf="!columns[12].hide">{{ expenses.deliveryProvider }}</td>
                                <td *ngIf="!columns[13].hide">{{ expenses.deliveryConditions }}</td>
                                <td *ngIf="!columns[14].hide">{{ expenses.shippingAddress }}</td>
                                <td *ngIf="!columns[15].hide">{{ expenses.departurePort }}</td>
                                <td *ngIf="!columns[16].hide">{{ expenses.importPort }}</td>
                                <td *ngIf="!columns[17].hide">{{ expenses.finalDeliveryLocation }}</td>
                                <td *ngIf="!columns[18].hide">{{ expenses.contractNumber }}</td>
                                <td *ngIf="!columns[19].hide">{{ expenses.accountingCode }}</td>
                                <td *ngIf="!columns[20].hide">{{ expenses.totalShipmentValueInUsd }}</td>
                                <td *ngIf="!columns[21].hide">{{ expenses.exchangeRateUsdToVnd | number }}</td>
                                <td *ngIf="!columns[22].hide">{{ expenses.exportLicenseFee }}</td>
                                <td *ngIf="!columns[23].hide">{{ expenses.coFee | number }}</td>
                                <td *ngIf="!columns[24].hide">{{ expenses.transportFeeToExportPort | number }}</td>
                                <td *ngIf="!columns[25].hide">{{ expenses.customsClearanceFee | number }}</td>
                                <td *ngIf="!columns[26].hide">{{ expenses.portHandlingFeeAtExport | number }}</td>
                                <td *ngIf="!columns[27].hide">{{ expenses.vatTaxToExportPort | number }}</td>
                                <td *ngIf="!columns[28].hide">{{ expenses.crossBorderFreightCost | number }}</td>
                                <td *ngIf="!columns[29].hide">{{ expenses.importLicenseFee }}</td>
                                <td *ngIf="!columns[30].hide">{{ expenses.customsServiceFee | number }}</td>
                                <td *ngIf="!columns[31].hide">{{ expenses.importPortFee | number }}</td>
                                <td *ngIf="!columns[32].hide">{{ expenses.inlandTransportFee | number }}</td>
                                <td *ngIf="!columns[33].hide">{{ expenses.vatTax | number }}</td>
                                <td *ngIf="!columns[34].hide">{{ expenses.infrastructureFee | number }}</td>
                                <td *ngIf="!columns[35].hide">{{ expenses.storageFee | number }}</td>
                                <td *ngIf="!columns[36].hide">{{ expenses.containerFee | number }}</td>
                                <td *ngIf="!columns[37].hide">{{ expenses.otherFees | number }}</td>
                                <td *ngIf="!columns[38].hide">{{ expenses.importPortVat | number }}</td>
                                <td *ngIf="!columns[39].hide">{{ expenses.totalLogisticsCost | number }}</td>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="emptymessage">
                            <tr>
                                <td colspan="40" class="text-center">Không có dữ liệu</td>
                            </tr>
                        </ng-template>
                    </p-table>
                </ng-template>
            </p-panel>
        </ng-container>

        <ng-container *ngIf="logisticId && formGroup.getRawValue().type === optionType[0].value">
            <br />
            <p-panel header="Chi phí khác" [toggleable]="true">
                <p-table styleClass="p-datatable-gridlines" [scrollable]="true" [value]="oldLogistics?.expensesFwdOtherLst" scrollHeight="500px">
                    <ng-template pTemplate="header">
                        <tr>
                            <th>STT</th>
                            <th>Tháng</th>
                            <th>Chi phí (VNĐ)</th>
                            <th>Loại chi phí</th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-expenses let-rowIndex="rowIndex">
                        <tr>
                            <td>{{ rowIndex + 1 }}</td>
                            <td>{{ expenses.date | date: 'MM/yyyy' }}</td>
                            <td>{{ expenses.fee | number }}</td>
                            <td>{{ expenses.feeName }}</td>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="emptymessage">
                        <tr>
                            <td colspan="10" class="text-center">Không có dữ liệu</td>
                        </tr>
                    </ng-template>
                </p-table>
            </p-panel>
        </ng-container>

        <ng-container *ngIf="logisticId && formGroup.getRawValue().type === optionType[1].value">
            <br />
            <p-panel header="Lịch sử giao dịch" [toggleable]="true">
                <ng-template pTemplate="content">
                    <div class="tw-flex tw-justify-end tw-mb-4 tw-mx-4 tw-gap-4">
                        <p-button icon="pi pi-filter" [outlined]="true" [size]="'small'" (click)="opInsurance.toggle($event)"></p-button>
                    </div>

                    <p-table styleClass="p-datatable-gridlines" [scrollable]="true" [value]="oldLogistics?.expensesInsuranceLst" scrollHeight="500px">
                        <ng-template pTemplate="header">
                            <tr>
                                <th>STT</th>
                                <th>Số BO</th>
                                <th>Ngày cấp</th>
                                <th>Tỷ lệ phí (%)</th>
                                <th>Phương thức vận chuyển</th>
                                <th>Giá trị mua bảo hiểm (USD)</th>
                                <th>Số chi phí BH (VNĐ)</th>
                                <th *ngIf="!columnsInsurance[7].hide">Số vận đơn</th>
                                <th *ngIf="!columnsInsurance[8].hide">Số hóa đơn</th>
                                <th *ngIf="!columnsInsurance[9].hide">Trọng lượng (kg)</th>
                                <th *ngIf="!columnsInsurance[10].hide">Vận chuyển từ</th>
                                <th *ngIf="!columnsInsurance[11].hide">Đến</th>
                                <th *ngIf="!columnsInsurance[12].hide">Hợp đồng mua bán</th>
                                <th *ngIf="!columnsInsurance[13].hide">Số PO</th>
                                <th *ngIf="!columnsInsurance[14].hide">Mã KT</th>
                                <th *ngIf="!columnsInsurance[15].hide">Phòng ban</th>
                                <th *ngIf="!columnsInsurance[16].hide">Ghi chú</th>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="body" let-expenses let-rowIndex="rowIndex">
                            <tr>
                                <td>{{ rowIndex + 1 }}</td>
                                <td>
                                    <a [routerLink]="'/sc/bo/' + expenses.boId">{{ expenses.boCode }}</a>
                                </td>
                                <td>{{ expenses.issueDate | date: 'dd/MM/yyyy' }}</td>
                                <td>{{ expenses.feeRate | number }}</td>
                                <td>{{ expenses.deliveryMethod }}</td>
                                <td>{{ expenses.insuranceValue }}</td>
                                <td>{{ expenses.insuranceFee | number }}</td>
                                <td *ngIf="!columnsInsurance[7].hide">{{ expenses.trackingNumber }}</td>
                                <td *ngIf="!columnsInsurance[8].hide">{{ expenses.billNumber }}</td>
                                <td *ngIf="!columnsInsurance[9].hide">{{ expenses.totalWeight }}</td>
                                <td *ngIf="!columnsInsurance[10].hide">{{ expenses.shippingFrom }}</td>
                                <td *ngIf="!columnsInsurance[11].hide">{{ expenses.shippingTo }}</td>
                                <td *ngIf="!columnsInsurance[12].hide">{{ expenses.salesContract }}</td>
                                <td *ngIf="!columnsInsurance[13].hide">{{ expenses.poNumber }}</td>
                                <td *ngIf="!columnsInsurance[14].hide">{{ expenses.technicalCode }}</td>
                                <td *ngIf="!columnsInsurance[15].hide">{{ expenses.department }}</td>
                                <td *ngIf="!columnsInsurance[16].hide">{{ expenses.note }}</td>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="emptymessage">
                            <tr>
                                <td colspan="15" class="text-center">Không có dữ liệu</td>
                            </tr>
                        </ng-template>
                    </p-table>
                </ng-template>
            </p-panel>
        </ng-container>

        <ng-container *ngIf="logisticId && formGroup.getRawValue().type === optionType[2].value">
            <br />
            <p-panel header="Lịch sử giao dịch" [toggleable]="true">
                <ng-template pTemplate="content">
                    <div class="tw-flex tw-justify-end tw-mb-4 tw-mx-4 tw-gap-4">
                        <p-button icon="pi pi-filter" [outlined]="true" [size]="'small'" (click)="opDelivery.toggle($event)"></p-button>
                    </div>

                    <p-table styleClass="p-datatable-gridlines" [scrollable]="true" [value]="oldLogistics?.expensesDeliveryLst" scrollHeight="700px">
                        <ng-template pTemplate="header">
                            <tr>
                                <th>STT</th>
                                <th>Số BO</th>
                                <th>Ngày giao hàng</th>
                                <th>Chi phí vận chuyển</th>
                                <th>Chi phí khác</th>
                                <th>Phụ phí xăng dầu</th>
                                <th>Tổng cộng</th>
                                <th *ngIf="!columnsDelivery[7].hide">Số vận đơn</th>
                                <th *ngIf="!columnsDelivery[8].hide">Tên người gửi</th>
                                <th *ngIf="!columnsDelivery[9].hide">SỐ PO/ HÀNG MẪU/ TÀI LIỆU</th>
                                <th *ngIf="!columnsDelivery[10].hide">MÃ DỰ ÁN (NẾU CÓ PO) HOẶC PHÒNG BAN NHẬN HÀNG (NẾU LÀ HÀNG MẪU/ TÀI LIỆU)</th>
                                <th *ngIf="!columnsDelivery[11].hide">ĐƠN VỊ PHỤ TRÁCH</th>
                                <th *ngIf="!columnsDelivery[12].hide">NƯỚC XUẤT</th>
                                <th *ngIf="!columnsDelivery[13].hide">LOẠI DỊCH VỤ HÀNG NHANH</th>
                                <th *ngIf="!columnsDelivery[14].hide">KHỐI LƯỢNG</th>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="body" let-expenses let-rowIndex="rowIndex">
                            <tr>
                                <td>{{ rowIndex + 1 }}</td>
                                <td>
                                    <a [routerLink]="'/sc/bo/' + expenses.boId">{{ expenses.boCode }}</a>
                                </td>
                                <td>{{ expenses.deliveryDate | date: 'dd/MM/yyyy' }}</td>
                                <td>{{ expenses.deliveryFee | number }}</td>
                                <td>{{ expenses.otherCosts | number }}</td>
                                <td>{{ expenses.fuelFee | number }}</td>
                                <td>{{ expenses.totalAmount | number }}</td>
                                <td *ngIf="!columnsDelivery[7].hide">{{ expenses.awb }}</td>
                                <td *ngIf="!columnsDelivery[8].hide">{{ expenses.fullName }}</td>
                                <td *ngIf="!columnsDelivery[9].hide">{{ expenses.po }}</td>
                                <td *ngIf="!columnsDelivery[10].hide">{{ getProjectCodeOrManagingDepartment(expenses) }}</td>
                                <td *ngIf="!columnsDelivery[11].hide">{{ expenses.managingDepartment }}</td>
                                <td *ngIf="!columnsDelivery[12].hide">{{ expenses.exportCountry }}</td>
                                <td *ngIf="!columnsDelivery[13].hide">{{ expenses.expressServiceType }}</td>
                                <td *ngIf="!columnsDelivery[14].hide">{{ expenses.weight | number }}</td>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="emptymessage">
                            <tr>
                                <td [attr.colspan]="visibleColumnCount" class="text-center">Không có dữ liệu</td>
                            </tr>
                        </ng-template>
                    </p-table>
                </ng-template>
            </p-panel>
        </ng-container>
    </app-form>
</div>
<p-overlayPanel #op>
    <p-table [value]="columns" [(selection)]="columnChoose" (selectionChange)="setColumnSelection($event, columns)" [scrollable]="true" scrollHeight="500px">
        <ng-template pTemplate="header">
            <tr>
                <th>
                    <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                </th>
                <th>Tất cả</th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rowData>
            <tr>
                <td style="font-size: 14px; padding: 8px 12px">
                    <p-tableCheckbox [value]="rowData" [hidden]="rowData.default" />
                </td>
                <td style="font-size: 14px; padding: 8px 12px">
                    {{ rowData.header }}
                </td>
            </tr>
        </ng-template>
    </p-table>
</p-overlayPanel>

<p-overlayPanel #opInsurance>
    <p-table
        [value]="columnsInsurance"
        [(selection)]="columnChooseInsurance"
        (selectionChange)="setColumnSelection($event, columnsInsurance)"
        [scrollable]="true"
        scrollHeight="500px"
    >
        <ng-template pTemplate="header">
            <tr>
                <th>
                    <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                </th>
                <th>Tất cả</th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rowData>
            <tr>
                <td style="font-size: 14px; padding: 8px 12px">
                    <p-tableCheckbox [value]="rowData" [hidden]="rowData.default" />
                </td>
                <td style="font-size: 14px; padding: 8px 12px">
                    {{ rowData.header }}
                </td>
            </tr>
        </ng-template>
    </p-table>
</p-overlayPanel>

<p-overlayPanel #opDelivery>
    <p-table
        [value]="columnsDelivery"
        [(selection)]="columnsChoseDelivery"
        (selectionChange)="setColumnSelection($event, columnsDelivery)"
        [scrollable]="true"
        scrollHeight="500px"
    >
        <ng-template pTemplate="header">
            <tr>
                <th>
                    <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                </th>
                <th>Tất cả</th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rowData>
            <tr>
                <td style="font-size: 14px; padding: 8px 12px">
                    <p-tableCheckbox [value]="rowData" [hidden]="rowData.default" />
                </td>
                <td style="font-size: 14px; padding: 8px 12px">
                    {{ rowData.header }}
                </td>
            </tr>
        </ng-template>
    </p-table>
</p-overlayPanel>
