import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { SupplierMaterial } from '../../../models/interface/sc';

@Injectable()
export class SupplierMaterialService extends BaseService<SupplierMaterial> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/supplier-material');
    }

    getSupplierMaterials(id: number) {
        return this.http.get<SupplierMaterial[]>(`/sc/api/supplier-material/report/${id}`);
    }
}
