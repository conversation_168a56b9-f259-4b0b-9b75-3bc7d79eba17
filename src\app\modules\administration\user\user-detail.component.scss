:host ::ng-deep .p-tag-cutom .p-tag {
    background-color: var(--surface-100);
    color: #1c1a1a;
    border: 0.005px solid rgb(187, 171, 171);
    margin-left: 2px;
}

:host ::ng-deep .p-multiselect {
    width: 100%;
}

:host ::ng-deep .p-dropdown {
    width: 100%;
}

:host ::ng-deep .p-password {
    width: 100%;
}

::ng-deep .success > .p-inputswitch.p-inputswitch-checked .p-inputswitch-slider {
    background: #00b87b;
}


::ng-deep .success > .p-inputswitch .p-inputswitch-slider {
    border: 1px #00b87b;
    border-style: solid;
    background: rgb(212, 208, 208);
}

::ng-deep .danger > .p-inputswitch .p-inputswitch-slider {
    border: 1px #f4516c;
    border-style: solid;
    background: rgb(212, 208, 208);
}

::ng-deep .warning > .p-inputswitch .p-inputswitch-slider {
    border: 1px #fba65d;
    border-style: solid;
    background: rgb(212, 208, 208);
}
