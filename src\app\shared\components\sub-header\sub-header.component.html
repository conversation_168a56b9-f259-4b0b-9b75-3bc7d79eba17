<header
    class="tw-grid tw-grid-cols-[1fr_auto] tw-items-center tw-bg-white tw-border-b tw-border-slate-200"
    style="height: 48px; width: 100%; position: sticky; top: 49px; z-index: 100"
>
    <p-breadcrumb [model]="items" [home]="home" [ngClass]="{ 'p-breadcrumb-sm': true }">
        <ng-template pTemplate="item" let-item>
            <ng-container *ngIf="item.route; else elseBlock">
                <a [routerLink]="item.route" style="font-size: 14px; line-height: 20px">
                    <span [ngClass]="[item.icon ? item.icon : '', 'text-color']"></span>
                    <span class="text-color">{{ item.label }}</span>
                </a>
            </ng-container>
            <ng-template #elseBlock>
                <a [routerLink]="item.url" style="font-size: 14px; line-height: 20px">
                    <span class="text-color">{{ item.label }}</span>
                </a>
            </ng-template>
        </ng-template>
    </p-breadcrumb>
    <div class="mx-3">
        <div class="tw-grid tw-grid-flow-col tw-gap-2 tw-items-center tw-h-full sub-header-lg md:tw-grid">
            <ng-container *ngTemplateOutlet="action"></ng-container>
        </div>

        <p-button
            (click)="op.toggle($event)"
            icon="pi pi-chevron-down"
            class="tw-inline-block md:tw-hidden tw-mt-3"
            severity="secondary"
            [text]="true"
            size="small"
        />
        <p-overlayPanel #op>
            <div class="flex flex-column gap-2">
                <ng-container *ngTemplateOutlet="action"></ng-container>
            </div>
        </p-overlayPanel>
    </div>
</header>
