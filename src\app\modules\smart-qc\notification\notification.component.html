<app-sub-header [items]="itemsHeader" [action]="actionHeader"></app-sub-header>

<ng-template #actionHeader>
    <!--<p-button size="small" (click)="goToCreateTemplatePage()" label="Thêm mới" severity="success" />-->
</ng-template>
<div style="padding: 1rem 1rem 0 1rem">
    <app-table-common
        [tableId]="tableId"
        [columns]="columns"
        [data]="state.data"
        [loading]="state.isFetching"
        [filterTemplate]="filterTemplate"
        [funcDelete]="deleteSelectedNotification"
        [funcDeleteAll]="deleteAllNotification"
        [rowSelectable]="isRowSelectable"
        deleteAllButton="true"
        selectionMode="multiple"
        name="Danh sách thông báo"
    >
        <ng-template #filterTemplate>
            <tr>
                <th></th>
                <th>
                    <app-filter-table
                        [tableId]="tableId"
                        field="contentWeb"
                        [rsql]="false"
                        type="text"
                        placeholder="Nội dung"
                    ></app-filter-table>
                </th>
                <th style="max-width: 10rem">
                    <app-filter-table
                        [tableId]="tableId"
                        field="startTime&endTime"
                        [rsql]="false"
                        type="date-range"
                        placeholder="Thời gian"
                    ></app-filter-table>
                </th>
            </tr>
        </ng-template>
    </app-table-common>
    <ng-template #notificationTime let-rowData>
        <div>{{ Common.formatDate(rowData.created) }}</div>
    </ng-template>
    <ng-template #notificationContent let-rowData>
        <p
            (click)="navigateToApprove(rowData)"
            class="tw-text-blue-600 tw-cursor-pointer tw-truncate"
            [pTooltip]="rowData.contentWeb"
            style="max-width: 1350px"
        >
            {{ rowData.contentWeb }}
        </p>
    </ng-template>
</div>
