import { canAuthorize } from '../../../core/auth/auth.guard';

export const PORouting = {
    path: 'po',
    title: '<PERSON><PERSON><PERSON><PERSON> lý <PERSON>',
    canActivate: [canAuthorize],
    data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_po_view'] },
    children: [
        {
            path: '',
            title: '<PERSON><PERSON> sách PO',
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_po_view', 'sc_po_edit'] },
            loadComponent: () => import('./list/list.component').then((c) => c.SCCOrderListComponent),
        },
        {
            path: ':id',
            title: 'Chi tiết PO',
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_po_view', 'sc_po_edit'] },
            loadComponent: () => import('./edit/edit.component').then((c) => c.OrderEditComponent),
        },
        {
            path: 'create',
            title: 'Tạo PO',
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_po_view', 'sc_po_edit'] },
            loadComponent: () => import('./edit/edit.component').then((c) => c.OrderEditComponent),
        },
    ],
};
