import { Component, ViewChild, ElementRef, OnInit, Input, AfterViewInit } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { TreeModule } from 'primeng/tree';
import { CardModule } from 'primeng/card';
import { MenuModule } from 'primeng/menu';
import { DropdownModule } from 'primeng/dropdown';
import { CommonModule } from '@angular/common';
import { FormsModule, FormGroup, ReactiveFormsModule, FormBuilder, Validators } from '@angular/forms';
import { TreeNode, MenuItem } from 'primeng/api';
import { Menu } from 'primeng/menu';
import { RadioButtonModule } from 'primeng/radiobutton';
import { MultiSelectModule } from 'primeng/multiselect';
import { Router } from '@angular/router';
import { InputTextModule } from 'primeng/inputtext';
import { TableModule } from 'primeng/table';
import { MessageService } from 'primeng/api';
import { CheckboxModule } from 'primeng/checkbox';
import { HttpClientModule } from '@angular/common/http';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { TableCellDirective } from 'src/app/shared/directives/table-cell.directive';
import { TableCustomComponent, TrackColumn } from 'src/app/shared/components/table-custom/table-custom.component';
import { ProductLineService } from 'src/app/services/pms/product-line/product-line.service';
import { HsspCompareDialogComponent } from 'src/app/modules/pms/components/product-compare-dialog/hssp-compare-dialog.component';
import { UploadCustomComponent } from 'src/app/shared/components/upload-custom/upload-custom.component';
import { ContextMenuModule } from 'primeng/contextmenu';
import { ConfirmationService } from 'primeng/api';
import { AlertService } from 'src/app/shared/services/alert.service';
import { PaginatorModule } from 'primeng/paginator';
import { environment } from 'src/environments/environment';
import { ButtonGroupFileComponent } from 'src/app/shared/components/button-group-file/button-group-file.component';
import { InputValidationComponent } from 'src/app/shared/components/input-validation/input.validation.component';
import { PopupComponent } from 'src/app/shared/components/popup/popup.component';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { AutoCompleteCompleteEvent, AutoCompleteModule } from 'primeng/autocomplete';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { AuthService } from '../../../../core/auth/auth.service';
import { ComboboxNonRSQLComponent } from 'src/app/shared/components/combobox-nonRSQL/combobox-nonRSQL.component';

interface CustomerType {
    name: string;
    id: number;
}

interface Model {
    label: string;
    value: number;
}

interface ProductVersion {
    id: number;
    versionName: string;
    versionEnum: number;
    status: number;
    lifecycleStage: number;
    // ... các field khác nếu cần
}

interface Product {
    id: number;
    name: string;
    model: string;
    modelId: string;
    modelName: string;
    tradeCode: string;
    tradeName: string;
    vnptManPn: string;
    lifecycleStage: number;
    customerId: number[];
    image?: File;
    imageName?: string;
    description: string;
    generation: string;
    hardwareVersion: string;
    firmwareVersion: string;
    lineId: number;
    versionName: string;
    productVersions: ProductVersion[];
}

interface CompareField {
    label: string;
    key: string;
}

interface ProductHistory {
    stt: number;
    action: string;
    details: string;
    user: string;
    timestamp: Date;
}

interface PartManufacturer {
    value: string;
    label: string;
}

@Component({
    selector: 'app-list',
    templateUrl: 'list.component.html',
    styleUrls: ['list.component.scss'],
    standalone: true,
    imports: [
        SubHeaderComponent,
        TableCommonModule,
        ButtonModule,
        DropdownModule,
        TreeModule,
        CardModule,
        MenuModule,
        CommonModule,
        FormsModule,
        RadioButtonModule,
        MultiSelectModule,
        ReactiveFormsModule,
        PopupComponent,
        InputTextModule,
        ButtonGroupFileComponent,
        FormCustomModule,
        TableModule,
        CheckboxModule,
        PaginatorModule,
        HsspCompareDialogComponent,
        InputValidationComponent,
        UploadCustomComponent,
        HttpClientModule,
        TableCustomComponent,
        ContextMenuModule,
        TableCellDirective,
        AutoCompleteModule,
        ComboboxNonRSQLComponent,
        HasAnyAuthorityDirective,
    ],
    providers: [ProductLineService],
})
export class ProductLineListComponent implements OnInit, AfterViewInit {
    @ViewChild('menuButton', { read: ElementRef }) menuButton: ElementRef | undefined; // Lấy tham chiếu đến nút
    @ViewChild('HistoryPopup', { static: true }) private historyPopup!: PopupComponent;
    @ViewChild('HSSPPopup', { static: true }) private HSSPPopup!: PopupComponent;
    @ViewChild('EditPopup', { static: true }) editPopup!: PopupComponent;
    @ViewChild('ComparePopup') comparePopup!: PopupComponent;
    @ViewChild('uploadRef') uploadRef!: UploadCustomComponent;
    @ViewChild('menu') menu: Menu | undefined;
    @ViewChild('editInput') editInput: ElementRef<HTMLInputElement>;
    @ViewChild('modelSelectFilter', { static: false })
    modelSelectFilter!: ComboboxNonRSQLComponent;
    @ViewChild('modelSelectPopup', { static: false })
    modelSelectPopup!: ComboboxNonRSQLComponent;
    private _customerFilterOverridden = false;
    private _customerPopupOverridden = false;
    private customerDataLoaded = false;
    private _customerPopupRef: ComboboxNonRSQLComponent | undefined;
    @ViewChild('customerFilter', { static: false })
    set customerFilter(ref: ComboboxNonRSQLComponent | undefined) {
        if (ref && !this._customerFilterOverridden) {
            this._customerFilterOverridden = true;
            // giữ lại debounce gốc
            const origDebounce = ref.debouncedGetOptions.bind(ref);
            // override filterOptions
            ref.filterOptions = (term: string) => {
                const rsql = `name==*${term}*`;
                origDebounce(rsql);
            };
        }
    }
    @ViewChild('customerPopup', { static: false })
    set customerPopup(ref: ComboboxNonRSQLComponent | undefined) {
        this._customerPopupRef = ref;
        if (!ref) {
            // popup bị destroy → reset flag
            this._customerPopupOverridden = false;
            return;
        }
        if (!this._customerPopupOverridden) {
            this._customerPopupOverridden = true;
            const origDebounce = ref.debouncedGetOptions.bind(ref);
            ref.filterOptions = (term: string) => {
                const rsql = `name==*${term}*`;
                origDebounce(rsql);
            };
        }
    }
    @ViewChild(HsspCompareDialogComponent) compareProfileComp!: HsspCompareDialogComponent;
    public STORAGE_BASE_URL = environment.STORAGE_BASE_URL;
    currentPage = 0;
    pageSize = 15;
    loading = false;
    allDataLoaded = false;

    @ViewChild('scrollContainer') scrollContainer: ElementRef;
    @Input() tableId: string;
    itemsHeader = [{ label: 'Hồ sơ sản phẩm' }, { label: 'Dòng sản phẩm' }];
    productModels: Model[] = [];
    products: Product[] = [];
    first = 0;
    rows = 10;
    showTechnicalFields: boolean = false;
    uploadedImageUrl: string = '';
    private originalLabel: string = '';
    customerTypes: CustomerType[] = [];
    title: string = 'Thêm mới';
    isButtonVisible: boolean = false;
    isViewOnly: boolean = false;
    isHSSPDisabled = false;
    searchQuery: string = '';
    searchDebounceTimer: any;
    totalRecords = 0;
    productsToCompare: any[] = [];
    selectedModels: Model[] = [];
    versionLink: any[] | null = null;
    selectedCustomer: CustomerType[] = [];
    editingNode: TreeNode | null = null;
    size = false;
    optionManPn: PartManufacturer[] = [];
    selectedProductLineId: number = 0;
    filterForm: FormGroup;
    addProductForm: FormGroup;
    private uploadedImageInfo: { imageName: string; imageUrl: string } | null = null;
    productMenu: MenuItem[] = [];
    treeData: TreeNode[] = [];
    contextMenuItems: MenuItem[] = [];
    selectedNode: TreeNode | null = null;
    selectedProduct: Product | null = null;
    isSubFolder: boolean = false;
    isEdit: boolean = false;
    isDetail: boolean = false;
    optionModel: [];
    selectedProductId: number;
    editingLabel: string = '';
    selectedProducts: Product[] = [];
    comparedProducts: Product[] = [];
    compareFields: CompareField[] = [
        { label: 'VNPT Man P/N', key: 'vnptManPn' }, // Giả sử 'id' tương ứng với VNPT Man P/N
        { label: 'Giai đoạn SX', key: 'lifecycleStage' },
        { label: 'Model', key: 'model' },
        { label: 'Tên thương mại', key: 'tradeName' },
        { label: 'Mã thương mại', key: 'tradeCode' },
        { label: 'Mô tả', key: 'description' },
        { label: 'Generation', key: 'generation' },
        { label: 'Khách hàng', key: 'customerId' },
        { label: 'RD BOM', key: 'rdbom' },
        { label: 'Hardware version', key: 'hardwareVersion' },
        { label: 'Firmware version', key: 'firmwareVersion' },
        { label: 'Version hồ sơ', key: 'versionName' },
    ];
    searchName: string = '';
    isAddingNewNode: boolean = false;
    currentSearchQuery: string = '';
    trackCols: TrackColumn[] = [
        { field: 'stt', header: 'STT' },
        { field: 'action', header: 'Thao tác thay đổi', width: '150px' },
        {
            field: 'details',
            header: 'Chi tiết cập nhật',
            width: '300px',
        },
        { field: 'user', header: 'Người thực hiện', width: '150px' },
        { field: 'timestamp', header: 'Thời điểm thực hiện' },
    ];
    historyData: ProductHistory[] = [];
    firstTimeFlags = new WeakMap();
    constructor(
        private fb: FormBuilder,
        private messageService: MessageService,
        private confirmationService: ConfirmationService,
        private alertService: AlertService,
        private productLineService: ProductLineService,
        private router: Router,
        private authService: AuthService,
    ) {}
    ngAfterViewInit() {}

    ngOnInit() {
        this.getProductLines();
        this.loadCustomers(10000);
        this.addProductForm = this.fb.group({
            productName: ['', Validators.required],
            generation: [''],
            brandName: ['', Validators.required],
            codeName: ['', Validators.required],
            manPn: ['', Validators.required],
            modelId: [null],
            customer: [null],
            description: [''],
            image: [''],
            refProduct: [''],
            firmwareVersion: [''],
            rdBom: [''],
            lifecycleStage: [''],
            hardwareVersion: [''],
            version: [''],
        });
        this.filterForm = this.fb.group({
            selectedModels: [''],
            selectedCustomer: [''],
        });
        this.products = [];
    }
    handlePanelShow(ref: any) {
        if (!this.firstTimeFlags.has(ref)) {
            this.firstTimeFlags.set(ref, true);
        }

        ref.additionalParams = {
            ...ref.additionalParams,
            size: 100,
            page: 0,
            unpaged: false,
        };

        ref.fetchOptions(null);
    }
    searchProducts() {
        const page = Math.floor(this.first / this.rows);
        const formValue = this.filterForm.value;
        const payload = {
            page: page,
            size: this.rows,
            lineId: this.selectedProductLineId || undefined,
            ...(formValue.selectedModels && { modelId: formValue.selectedModels }),
            ...(formValue.selectedCustomer && { customerId: formValue.selectedCustomer }),
        };

        this.productLineService.getFilteredProducts(payload).subscribe({
            next: (res) => {
                const rawData = res.body;
                this.products = rawData.map((item: any) => {
                    const stage = item.lifecycleStage ?? null;
                    return {
                        model: item.modelName,
                        image: item.imageUrl ? `${this.STORAGE_BASE_URL}${item.imageUrl}` : '',
                        customerId: item.customerIds || [],
                        lifecycleStage: stage,
                        lifecycleStageLabel: this.getLifecycleStageLabel(stage),
                        ...item,
                    };
                });

                const totalCount = res.headers.get('X-Total-Count');
                this.totalRecords = totalCount ? +totalCount : 0;
            },
            error: (err) => {
                console.error('Lỗi khi tìm kiếm sản phẩm:', err);
            },
        });
    }

    onPageChange(event: any) {
        this.first = event.first || 0;
        this.searchProducts();
    }

    openMenu(event: MouseEvent, product: Product, menu: Menu) {
        this.selectedProduct = product;
        const menuItemsSubFolder = [
            {
                label: 'Xem chi tiết',
                command: () => this.openProduct(product, 'detail'),
                visible: this.authService.hasAuthority(['ROLE_SYSTEM_ADMIN', 'product_detail_view']),
            },
            {
                label: 'Chỉnh sửa',
                command: () => this.openProduct(product, 'edit'),
                visible: this.authService.hasAuthority(['ROLE_SYSTEM_ADMIN', 'product_line_update']),
            },
            {
                label: 'Xem lịch sử thay đổi',
                command: () => this.openHistoryDialog(product),
                visible: this.authService.hasAuthority(['ROLE_SYSTEM_ADMIN', 'product_history']),
            },
            { label: 'Gỡ khỏi model', command: () => this.removeFromModel(product), visible: true },
        ];

        const menuItems = [
            {
                label: 'Xem chi tiết',
                command: () => this.openProduct(product, 'detail'),
                visible: this.authService.hasAuthority(['ROLE_SYSTEM_ADMIN', 'product_detail_view']),
            },
            {
                label: 'Chỉnh sửa',
                command: () => this.openProduct(product, 'edit'),
                visible: this.authService.hasAuthority(['ROLE_SYSTEM_ADMIN', 'product_line_update']),
            },
            {
                label: 'Nhân bản',
                command: () => this.duplicateProduct(product),
                visible: this.authService.hasAuthority(['ROLE_SYSTEM_ADMIN', 'product_line_create']),
            },
            {
                label: 'Xem lịch sử thay đổi',
                command: () => this.openHistoryDialog(product),
                visible: this.authService.hasAuthority(['ROLE_SYSTEM_ADMIN', 'product_history']),
            },
            ...(!product.productVersions?.length && product.lifecycleStage === 0
                ? [
                      {
                          label: 'Xóa',
                          command: () => this.deleteProduct(product),
                          visible: this.authService.hasAuthority(['ROLE_SYSTEM_ADMIN', 'product_line_delete']),
                      },
                  ]
                : []),
        ];

        this.productMenu = this.isSubFolder ? menuItemsSubFolder.filter((item) => item.visible !== false) : menuItems.filter((item) => item.visible !== false);
        menu.toggle(event);
    }
    // Mở dialog lịch sử
    openHistoryDialog(product: Product) {
        this.loadProductModelsHistory(product.lineId, '');
        this.productLineService.getProductHistory(product.id).subscribe({
            next: (res: any) => {
                this.historyData = res.map((item, index) => {
                    const hasImageUrl = item.changeFieldDetails.some((c) => c.description === 'imageUrl');
                    const hasImageName = item.changeFieldDetails.some((c) => c.description === 'imageName');
                    const details = item.changeFieldDetails
                        .filter((c) => {
                            // Nếu có cả 2 thì bỏ qua imageUrl
                            if (hasImageUrl && hasImageName && c.description === 'imageUrl') {
                                return false;
                            }
                            return true;
                        })
                        .map((c: any) => this.formatChangeDetail(c)) // gọi hàm format từng field
                        .filter(Boolean)
                        .join('\n');

                    return {
                        stt: index + 1,
                        action: item.action === 0 ? 'Tạo mới' : 'Chỉnh sửa',
                        details,
                        user: item.createdBy,
                        timestamp: this.formatDateTime(item.updated),
                    };
                });
                this.historyPopup.openDialog(); // Mở popup khi có dữ liệu
            },
            error: (err) => {
                console.error('Lỗi khi tải lịch sử thay đổi sản phẩm:', err);
            },
        });
    }
    loadProductModelsHistory(productLineId: number, name: string) {
        const body = {
            name: '',
            page: 0,
            size: 0,
            unpaged: false,
            productLineId: productLineId,
        };

        this.productLineService.getProductModelsHistory(body).subscribe({
            next: (res) => {
                this.productModels = res.map((model) => ({
                    label: model.name,
                    value: model.id,
                }));
            },
            error: (err) => {
                console.error('Lỗi khi tải danh sách model', err);
            },
        });
    }
    formatChangeDetail(change: any): string | null {
        const { description, oldValue, newValue } = change;
        if ((oldValue === null || oldValue === '') && (newValue === null || newValue === '')) {
            return null;
        }
        const label = this.mapFieldLabel(description);

        switch (description) {
            case 'modelId': {
                const newLabel = this.getModelLabel(+newValue, +oldValue);
                return `${label}: ${newLabel}`;
            }

            case 'customer':
            case 'customerId': {
                const parseCustomer = (val: any): string => {
                    try {
                        const parsed = JSON.parse(val);
                        if (Array.isArray(parsed)) {
                            return parsed
                                .map((id: number) => this.getCustomerLabel(id))
                                .filter(Boolean)
                                .join(', ');
                        }
                    } catch {
                        return '';
                    }
                    return '';
                };

                // const oldNames = parseCustomer(oldValue);
                const newNames = parseCustomer(newValue);
                return `${label}: ${newNames}`;
            }

            default:
                return `${label}: ${newValue}`;
        }
    }

    getCustomerLabel(customerId: number): string | null {
        if (!this.customerTypes || !customerId) return null;

        const customer = this.customerTypes.find((c) => +c.id === +customerId);

        return customer ? customer.name : null;
    }

    getDisplayValue(fieldKey: string, value: any, position: number): string {
        if (fieldKey === 'model') {
            return this.getModelLabelCompare(Number(this.selectedProducts[`${position}`]?.modelId)) || '';
        }

        if (fieldKey === 'customerId') {
            try {
                const ids = Array.isArray(value) ? value : JSON.parse(value);
                if (Array.isArray(ids)) {
                    return ids
                        .map((id: number) => this.getCustomerLabel(id))
                        .filter(Boolean)
                        .join(', ');
                }
            } catch {
                return '';
            }
        }

        if (fieldKey === 'lifecycleStage') {
            return this.getLifecycleStageLabel(value);
        }

        return value;
    }

    getLifecycleStageLabel(lifecycleStage: number | string): string {
        switch (lifecycleStage) {
            case 1:
                return 'Prototype';
            case 2:
                return 'Pilot (RnD)';
            case 4:
                return 'MP';
            case 8:
                return 'Pilot (SX)';
            default:
                return '';
        }
    }
    mapFieldLabel(field: string): string {
        const map: Record<string, string> = {
            name: 'Tên sản phẩm',
            tradeName: 'Tên thương mại',
            tradeCode: 'Mã thương mại',
            vnptManPn: 'VNPT Man P/N',
            description: 'Mô tả',
            lifecycleStage: 'Giai đoạn SX',
            modelId: 'Model',
            customerId: 'Khách hàng',
            generation: 'Generation',
            hardwareVersion: 'Hardware version',
            version: 'Version hồ sơ',
            imageName: 'Hình ảnh sản phẩm',
        };
        return map[field] || field;
    }

    formatDateTime(timestamp: number): string {
        const date = new Date(timestamp);
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${hours}:${minutes} ${day}/${month}/${year}`;
    }

    isChildNode(node: TreeNode): boolean {
        return !!node.data?.isSubFolder || !!node.parent;
    }
    startEdit(node: TreeNode) {
        this.editingNode = node;
        this.editingLabel = node.label || '';
        setTimeout(() => {
            if (this.editInput && this.editInput.nativeElement) {
                this.editInput.nativeElement.focus();
            }
        });
    }

    saveNodeEdit(node: TreeNode) {
        const trimmedLabel = this.editingLabel.trim();
        if (!trimmedLabel) return;
        if (node.data?.isNew) {
            // ✅ Thêm mới
            this.productLineService.createProductLine(trimmedLabel).subscribe({
                next: (res) => {
                    node.label = trimmedLabel;
                    node.data.id = res.id;
                    node.data.isNew = false;
                    this.allDataLoaded = false;
                    this.treeData = [];
                    this.currentPage = 0;
                    this.isAddingNewNode = false;
                    this.messageService.add({
                        severity: 'success',
                        summary: 'Thành công',
                        detail: 'Tạo dòng sản phẩm thành công',
                    });

                    this.getProductLines(); // cập nhật lại danh sách
                },
                error: () => {
                    this.messageService.add({
                        severity: 'error',
                        summary: 'Lỗi',
                        detail: 'Không thể tạo dòng sản phẩm',
                    });
                },
            });
        } else if (this.isProductLineNode(node)) {
            this.updateProductLine(node);
        } else if (this.isProductModelNode(node)) {
            this.updateProductModel(node);
        }

        this.cancelEdit();
    }

    removeFromModel(product: Product & { imageUrl?: string; customerIds?: number[] }) {
        let processedImageUrl = product.imageUrl || '';

        if (processedImageUrl.startsWith(this.STORAGE_BASE_URL)) {
            processedImageUrl = processedImageUrl.substring(this.STORAGE_BASE_URL.length);
        }
        const payload = {
            modelId: null, // Gửi null để xóa modelId
            name: product.name,
            tradeName: product.tradeName,
            tradeCode: product.tradeCode,
            description: product.description,
            generation: product.generation,
            hardwareVersion: product.hardwareVersion,
            vnptManPn: product.vnptManPn,
            imageName: product.imageName || '',
            imageUrl: processedImageUrl,
            customerId: product.customerIds ? JSON.stringify(product.customerIds) : '[]',
            // imageUrl: product.imageUrl || '',
            // imageName: product.imageName || ''
        };
        // const payload = { ...product };
        if ('modelId' in payload) {
            delete payload.modelId;
        }
        // Tạo FormData
        const formData = new FormData();

        // Gán các field khác vào FormData
        for (const [key, value] of Object.entries(payload)) {
            if (value !== undefined && value !== null) {
                // Nếu là mảng (như customerId), stringify lại
                if (key === 'customerId' && Array.isArray(value)) {
                    formData.append(key, JSON.stringify(value));
                } else {
                    formData.append(key, value as string);
                }
            }
        }

        this.productLineService.updateProduct(product.id, formData).subscribe({
            next: () => {
                this.messageService.add({
                    severity: 'success',
                    summary: 'Thành công',
                    detail: 'Đã gỡ khỏi model',
                });
                this.handleSubFolderSelection(this.selectedNode); // refresh lại danh sách nếu cần
            },
            error: (err) => {
                this.messageService.add({
                    severity: 'error',
                    summary: 'Lỗi',
                    detail: 'Không thể gỡ sản phẩm khỏi model',
                });
            },
        });
    }

    isProductLineNode(node: TreeNode): boolean {
        return !node.parent && node.data?.type === 'product-line';
    }

    // Kiểm tra node có phải là Product Model (sub-model)
    isProductModelNode(node: TreeNode): boolean {
        return !!node.parent || node.data?.type === 'sub-folder';
    }

    updateProductLine(node: TreeNode) {
        this.productLineService.updateProductLine(node.data.id, this.editingLabel).subscribe({
            next: () => {
                this.treeData = [];
                this.currentPage = 0;
                this.allDataLoaded = false;
                this.getProductLines();
            },
            error: () => {},
        });
    }

    updateProductModel(node: TreeNode) {
        const payload = {
            id: node.data.id, // Lấy từ node hiện tại
            lineId: node.parent.data.id, // Lấy từ parent node
            name: this.editingLabel, // Tên mới từ input
            code: node.data.code || '', // Giữ nguyên code cũ hoặc rỗng
            description: node.data.description || '', // Giữ nguyên mô tả hoặc rỗng
        };
        this.productLineService.updateProductModel(node.data.id, payload).subscribe({
            next: () => {
                node.label = payload.name;
                this.cancelEdit();
                setTimeout(() => {
                    this.selectedNode = node;
                    this.scrollToNode(node);
                }, 100);
                // this.getProductLines();
            },
            error: () => {},
        });
    }
    private scrollToNode(node: TreeNode) {
        setTimeout(() => {
            const el = document.querySelector(`[data-node-id="${node.data.id}"]`);
            if (el) {
                (el as HTMLElement).scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                });
            }
        }, 100);
    }
    cancelEdit() {
        this.editingNode = null;
        this.editingLabel = '';
    }

    handleCompare(event: any, product: any): void {}

    hasUpdatePermission(): boolean {
        return this.authService.hasAuthority(['ROLE_SYSTEM_ADMIN', 'product_line_update']);
    }

    // Khi nhấn nút "So sánh"
    openCompareDialog(): void {
        if (this.selectedProducts.length === 0 || this.selectedProducts.length === 1 || this.selectedProducts.length > 2) {
            this.messageService.add({
                key: 'app-alert',
                severity: 'warn',
                summary: 'Cảnh báo',
                detail: 'Vui lòng chỉ chọn tối đa 2 sản phẩm cần so sánh!',
            });
            return;
        }
        this.loadProductModelsHistory(this.selectedProducts[0].lineId, '');
        this.loadProductModelsHistory(this.selectedProducts[1].lineId, '');

        this.comparedProducts = this.selectedProducts.map((product) => {
            return {
                ...product,
                versionName: product.productVersions?.[0]?.versionName ?? '',
                versionId: product.productVersions?.[0]?.id ?? '',
                lifecycleStage: product.lifecycleStage ?? 0,
            };
        });
        this.isHSSPDisabled = this.comparedProducts.some((product) => !product.productVersions || product.productVersions.length === 0);
        this.HSSPPopup.openDialog(this.comparedProducts);
    }
    cancel() {
        // Khôi phục giá trị cũ
        this.editingNode.label = this.originalLabel;
        this.editingNode = null;
    }
    handleUploadFile(files: File[]) {
        const file = files[0];

        if (!file) {
            this.addProductForm.get('image')?.setValue(null);
            return;
        }

        // Lấy phần mở rộng của file
        const fileExtension = file.name.split('.').pop()?.toLowerCase();
        const allowedExtensions = ['png', 'jpg', 'jpeg'];

        // Kiểm tra định dạng file
        if (!fileExtension || !allowedExtensions.includes(fileExtension)) {
            // Hiển thị thông báo lỗi
            this.alertService.error('Lỗi', 'File import sai định dạng, vui lòng thử lại với file JPG, JPEG, PNG');

            // Xóa file đã chọn (nếu có)
            this.uploadRef.uploadedFileName = '';
            this.addProductForm.get('image')?.setValue(null);
            return;
        }

        this.addProductForm.get('image')?.setValue({
            file: file,
            name: file.name,
            url: URL.createObjectURL(file),
            size: file.size,
            type: file.type,
        });
        this.uploadRef.uploadedFileName = file.name;
    }
    isNewUpload(): boolean {
        const imageValue = this.addProductForm.get('image')?.value;
        return imageValue instanceof File || (imageValue && imageValue.file instanceof File);
    }

    getTempImageUrl(): string {
        const imageValue = this.addProductForm.get('image')?.value;
        if (imageValue instanceof File) {
            return URL.createObjectURL(imageValue);
        }
        if (imageValue?.file instanceof File) {
            return URL.createObjectURL(imageValue.file);
        }
        return '';
    }

    private handleUploadSuccess(file: File) {
        this.addProductForm.get('image')?.setValue(file);
        this.uploadedImageUrl = this.uploadedImageInfo.imageUrl;
        this.uploadRef.uploadedFileName = file.name;
    }

    handleClearFile() {
        if (this.isDetail || this.addProductForm.disabled) {
            return;
        }
        this.addProductForm.get('image')?.setValue('');
    }
    onNodeSelect(node: TreeNode) {
        if (node?.data?.isNew) return;
        this.first = 0;
        this.selectedNode = node;
        this._customerFilterOverridden = false;
        this._customerPopupOverridden = false;
        this.isSubFolder = node?.data?.type === 'sub-folder';
        this.selectedProducts = [];
        this.selectedModels = [];
        this.filterForm.get('selectedModels')?.setValue(null);
        this.selectedCustomer = [];
        this.filterForm.get('selectedModels')?.setValue(null);
        this.filterForm.get('selectedCustomer')?.setValue(null);

        if (node?.data?.type === 'product-line') {
            const productLineId = node.data.id;
            this.selectedProductLineId = productLineId;
            // this.loadProductModels(productLineId);
            this.handleProductLineSelection(node);
        } else if (node?.data?.type === 'sub-folder') {
            // Xử lý khi chọn sub-folder (model)
            this.handleSubFolderSelection(node);
        }
    }

    private handleProductLineSelection(node: TreeNode) {
        if (!node?.data?.id) return;

        this.productLineService
            .getFilteredProducts({
                lineId: node.data.id,
                page: 0,
                size: 10,
            })
            .subscribe({
                next: (res) => {
                    const rawData = res.body;
                    this.products = rawData.map((item: any) => {
                        const stage = item.lifecycleStage ?? null;
                        return {
                            model: item.modelName,
                            image: item.imageUrl ? `${this.STORAGE_BASE_URL}${item.imageUrl}` : '',
                            customerId: item.customerIds || [],
                            lifecycleStage: stage,
                            lifecycleStageLabel: this.getLifecycleStageLabel(stage),
                            ...item,
                        };
                    });
                    const totalCount = res.headers.get('X-Total-Count');
                    this.totalRecords = totalCount ? +totalCount : 0;
                },
                error: (err) => {
                    console.error('Lỗi khi lấy chi tiết dòng sản phẩm:', err);
                },
            });
    }
    onContextMenuSelect(event: any) {
        event.originalEvent.preventDefault(); // Ngăn menu trình duyệt
        this.onMenuClick(event.originalEvent, event.node); // Chuyển về hàm xử lý chung
    }

    private handleSubFolderSelection(node: TreeNode) {
        if (!node?.data?.id || !node.parent?.data?.id) return;

        // Gọi API filter products với modelId
        this.productLineService
            .getFilteredProducts({
                modelId: node.data.id,
                lineId: node.parent.data.id,
            })
            .subscribe({
                next: (res) => {
                    const rawData = res.body;
                    this.products = rawData.map((item: any) => {
                        const stage = item.lifecycleStage ?? null;
                        return {
                            model: item.modelName,
                            image: item.imageUrl ? `${this.STORAGE_BASE_URL}${item.imageUrl}` : '',
                            customer: item.customerIds || [],
                            lifecycleStage: stage,
                            lifecycleStageLabel: this.getLifecycleStageLabel(stage),
                            ...item,
                        };
                    });
                    const totalCount = res.headers.get('X-Total-Count');
                    this.totalRecords = totalCount ? +totalCount : 0;
                },
                error: (err) => {
                    this.messageService.add({
                        severity: 'error',
                        summary: 'Lỗi',
                        detail: 'Không thể tải danh sách sản phẩm',
                    });
                },
            });
    }

    loadProductModels(productLineId: number) {
        const body = {
            page: 0,
            size: 0,
            unpaged: false,
            productLineId: productLineId,
        };

        this.productLineService.getProductModels(body).subscribe({
            next: (res) => {
                this.productModels = res.map((model) => ({
                    label: model.name,
                    value: model.id,
                }));
            },
            error: (err) => {
                console.error('Lỗi khi tải danh sách model', err);
            },
        });
    }
    getModelLabel(modelId: number, oldModelId: number): string | null {
        if (!this.productModels || !modelId) {
            if (oldModelId) {
                const foundModel = this.productModels.find((m) => m.value === oldModelId);
                return foundModel ? `${foundModel.label} đã bị gỡ` : 'Model đã bị xóa';
            }
            return 'Model đã bị xóa';
        }

        const foundModel = this.productModels.find((m) => m.value === modelId);
        return foundModel ? foundModel.label : 'Model đã bị xóa';
    }
    getModelLabelCompare(modelId: number): string | null {
        if (!this.productModels || !modelId) {
            return '';
        }

        const foundModel = this.productModels.find((m) => m.value === modelId);
        return foundModel ? foundModel.label : '';
    }
    onMenuClick(event: MouseEvent, node: TreeNode) {
        this.selectedNode = node;
        this._customerFilterOverridden = false;
        this._customerPopupOverridden = false;
        const menuItems = [
            {
                label: 'Thêm Model',
                command: () => this.addSubFolderModel(),
                visible: true,
            },
            {
                label: 'Nhân bản Folder',
                command: () => this.cloneFolder(),
                visible: this.authService.hasAuthority(['ROLE_SYSTEM_ADMIN', 'product_line_create']),
            },
            ...(node.data.hasVersion === 0
                ? [
                      {
                          label: 'Xóa Folder',
                          command: () => this.deleteFolder(),
                          visible: this.authService.hasAuthority(['ROLE_SYSTEM_ADMIN', 'product_line_delete']),
                      },
                  ]
                : []),
        ];
        const menuItemsSubFolder = [
            { label: 'Nhân bản', command: () => this.cloneNode(), visible: this.authService.hasAuthority(['ROLE_SYSTEM_ADMIN', 'product_line_create']) },
            { label: 'Xóa', command: () => this.deleteNode(), visible: this.authService.hasAuthority(['ROLE_SYSTEM_ADMIN', 'product_line_delete']) },
        ];

        if (node.data && node.data.isSubFolderModel) {
            // this.contextMenuItems = [
            //     { label: 'Nhân bản', command: () => this.cloneNode() },
            //     { label: 'Xóa', command: () => this.deleteNode() },
            // ];
            this.contextMenuItems = menuItemsSubFolder.filter((item) => item.visible !== false);
        } else {
            // this.contextMenuItems = [
            //     {
            //         label: 'Thêm Model',
            //         command: () => this.addSubFolderModel(),
            //     },
            //     { label: 'Nhân bản Folder', command: () => this.cloneFolder() },
            //     ...(node.data.hasVersion === 0 ? [{ label: 'Xóa Folder', command: () => this.deleteFolder() }] : []),
            // ];
            this.contextMenuItems = menuItems.filter((item) => item.visible !== false);
        }

        this.menu?.toggle(event);
    }
    onBlurTrim(controlName: string) {
        const control = this.addProductForm.get(controlName);
        const value = control?.value || '';
        control?.setValue(value.trim().replace(/\s+/g, ' '));
    }
    openProduct(row: any, type: string) {
        console.log('openProduct', this.addProductForm);
        console.log('openProduct1', this.selectedNode?.data?.id);
        this.isEdit = type === 'edit';
        this.isDetail = type === 'detail';
        this.title = this.isEdit ? 'Chỉnh sửa' : this.isDetail ? 'Xem chi tiết' : 'Thêm mới';

        const lineId = this.selectedNode?.data?.type === 'product-line' ? this.selectedNode.data.id : this.selectedNode?.data?.lineId;
        this.selectedProductLineId = lineId ?? null;

        if (type === 'detail' || type === 'edit') {
            this.productLineService.getProductById(row.id).subscribe({
                next: (res: any) => {
                    const versionName = res.productVersions?.[0]?.versionName ?? null;
                    const showTechFields = res.productVersions.some((v) => v.status === 8);
                    // Thiết lập liên kết version
                    if (res.productVersions && res.productVersions.length > 0) {
                        this.versionLink = ['/pms/product-file/view', res.productVersions[0].productId, res.productVersions[0].id];
                    }
                    const imageFile = res.imageName ? { name: res.imageName, url: res.imageUrl, fullUrl: this.getFullImageUrl(res.imageUrl) } : null;
                    this.addProductForm.patchValue({
                        productName: res.name,
                        brandName: res.tradeName,
                        codeName: res.tradeCode,
                        manPn: res.vnptManPn, // phải trùng giá trị với value trong options
                        modelId: this.isSubFolder ? this.selectedNode?.data?.id : res.modelId,
                        customer: res.customerIds,
                        description: res.description,
                        image: imageFile,
                        refProduct: res.refProduct,
                        firmwareVersion: res.firmwareVersion,
                        rdBom: res.rdbom,
                        version: versionName,
                        lifecycleStage: res.lifecycleStage,
                        hardwareVersion: res.hardwareVersion,
                        generation: res.generation,
                    });
                    if (!this.isDetail) {
                        this.uploadRef.uploadedFileName = res.imageName;
                    }
                    if (this.isEdit) {
                        this.addProductForm.enable();
                        this.isViewOnly = false;
                    } else {
                        this.isViewOnly = true;
                    }

                    this.showTechnicalFields = showTechFields;
                    this.editPopup.openDialog(res);

                    this.modelSelectPopup.filterOptions(res.modelName);
                    if (this._customerPopupRef) {
                        this._customerPopupRef.additionalParams = {
                            ...this._customerPopupRef.additionalParams,
                            size: 10000, // Đảm bảo size 10000 cho lần đầu
                        };
                        this._customerPopupRef.filterOptions('');
                    }
                },
                error: (err) => {
                    console.error('Lỗi khi lấy chi tiết sản phẩm:', err);
                },
            });
        } else {
            this.addProductForm.enable();
            this.editPopup.openDialog(row);
        }
    }
    getFullImageUrl(relativeUrl: string): string {
        if (!relativeUrl) return '';

        // Kiểm tra nếu URL đã có baseUrl thì không thêm nữa
        if (relativeUrl.startsWith(this.STORAGE_BASE_URL)) {
            return relativeUrl;
        }

        // Thêm baseUrl vào trước nếu là relative path
        return relativeUrl.startsWith('/') ? `${this.STORAGE_BASE_URL}${relativeUrl}` : `${this.STORAGE_BASE_URL}/${relativeUrl}`;
    }

    enableEditForm() {
        this.isViewOnly = false;
        this.isDetail = false;
        this.isEdit = true;
        this.title = 'Chỉnh sửa';
        this.addProductForm.enable(); // 👉 bật lại form
    }
    loadCustomers(size?: number) {
        const payload = {
            page: 0,
            unpaged: false,
            ...(size && { size }),
        };
        this.productLineService.getCustomers(payload).subscribe({
            next: (res) => {
                this.customerTypes = res.map((model) => ({
                    name: model.name,
                    id: model.id,
                }));
            },
        });
    }
    removeVietnameseTones(str: string): string {
        return str
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '') // xóa dấu
            .replace(/đ/g, 'd')
            .replace(/Đ/g, 'D');
    }

    loadUnusedManufacturers(event: AutoCompleteCompleteEvent) {
        const query = event.query;
        this.productLineService
            .getUnusedManufacturers({
                page: 0,
                size: 100,
                unpaged: false,
                name: query,
            })
            .subscribe({
                next: (res) => {
                    this.optionManPn = res.map((model) => ({
                        label: model.vnptManPn,
                        value: model.vnptManPn,
                    }));
                },
                error: (err) => {
                    console.error('Lỗi khi tải vnpt man p/n', err);
                },
            });
    }
    onManPnSelect(event: any) {
        // Cập nhật form control với giá trị đã chọn
        this.addProductForm.get('manPn')?.setValue(event.value);
    }
    submitFormEdit() {
        console.log('submitFormEdit', this.addProductForm.value);
        const formValue = this.addProductForm.value;
        if (this.isSubFolder && this.selectedNode?.data?.id) {
            formValue.modelId = this.selectedNode.data.id;
        }
        const manPnValue = typeof formValue.manPn === 'object' ? formValue.manPn.value : formValue.manPn;

        const formData = new FormData();
        const simpleFields = {
            modelId: formValue.modelId,
            name: formValue.productName,
            vnptManPn: manPnValue,
            tradeName: formValue.brandName,
            tradeCode: formValue.codeName,
            description: formValue.description,
            generation: formValue.generation,
            refProduct: formValue.refProduct,
            hardwareVersion: formValue.hardwareVersion,
            //     ...(this.uploadedImageInfo && {
            //     imageName: this.uploadedImageInfo.imageName,
            //     imageUrl: this.uploadedImageInfo.imageUrl
            // })
        };

        Object.entries(simpleFields).forEach(([key, value]) => {
            if (value !== undefined && value !== null && value !== '') {
                formData.append(key, value);
            }
        });

        // Field lineId từ biến ngoài
        formData.append('lineId', this.selectedProductLineId?.toString() || '');

        // Field customerId (mảng, cần stringify)
        formData.append('customerId', JSON.stringify(formValue.customer || []));
        const imageValue = formValue.image;
        if (imageValue instanceof File) {
            // Trường hợp 1: File mới upload - gửi binary
            formData.append('image', imageValue);
        } else if (imageValue?.file instanceof File) {
            // Trường hợp 2: File từ form value (nếu bạn lưu cả object)
            formData.append('image', imageValue.file);
        } else if (imageValue && typeof imageValue === 'object' && imageValue.name) {
            formData.append('imageName', imageValue.name);
            if (imageValue.url) {
                formData.append('imageUrl', imageValue.url);
            }
        }

        const callback = () => {
            if (this.isEdit) {
                this.alertService.success('Thành công', 'Đã cập nhật sản phẩm thành công');
            } else {
                this.alertService.success('Thành công', 'Thêm sản phẩm thành công');
            }
            this.editPopup.closeDialog();
            this.addProductForm.reset();
            this.selectedProducts = [];
            this.uploadRef?.clearAll();
            if (this.isSubFolder && this.selectedNode) {
                this.handleSubFolderSelection(this.selectedNode);
            } else {
                this.searchProducts();
            }
        };

        const errorHandler = (err: any) => {
            if (this.isEdit) {
                this.alertService.error('Lỗi', 'Không thể cập nhật sản phẩm');
            } else {
            }
        };

        if (this.isEdit && this.selectedProduct?.id) {
            this.productLineService.updateProduct(this.selectedProduct.id, formData).subscribe({
                next: callback,
                error: errorHandler,
            });
        } else {
            this.productLineService.createProduct(formData).subscribe({
                next: callback,
                error: errorHandler,
            });
        }
    }
    handlePopupClose() {
        this.isEdit = false;
        this.isDetail = false;
        this.isViewOnly = false;
        this.uploadRef?.clearAll();
    }
    cloneNode() {
        if (this.selectedNode && this.selectedNode.data?.id) {
            const modelId = this.selectedNode.data.id;

            this.productLineService.duplicateProductModel(modelId).subscribe({
                next: (res) => {
                    this.treeData = [];
                    this.currentPage = 0;
                    this.allDataLoaded = false;
                    const newModel = res.data; // kết quả từ API
                    this.getProductLines();
                },
                error: () => {
                    this.messageService.add({
                        severity: 'error',
                        summary: 'Lỗi',
                        detail: 'Không thể nhân bản model',
                    });
                },
            });
        }
    }
    deleteNode() {
        if (!this.selectedNode) return;

        const isModelNode = this.selectedNode.data?.type === 'sub-folder'; // giả sử type 'sub-folder' là model

        if (isModelNode) {
            // Gọi API xóa model
            this.productLineService.deleteProductModel(this.selectedNode.data.id).subscribe({
                next: () => {
                    this.treeData = [];
                    this.currentPage = 0;
                    this.allDataLoaded = false;
                    this.getProductLines();
                    this.selectedNode = null;
                    this._customerFilterOverridden = false;
                    this._customerPopupOverridden = false;
                },
                error: (err) => {
                    this.messageService.add({
                        severity: 'error',
                        summary: 'Lỗi',
                        detail: 'Không thể xóa sản phẩm model',
                    });
                },
            });
        } else {
            // Nếu là node cha (dòng sản phẩm), xóa local hoặc gọi API dòng sản phẩm nếu có
            // this.removeNodeFromTree();
        }
    }
    addSubFolderModel() {
        if (!this.selectedNode) return;

        const originalLabel = 'Model';

        // Nếu đang ở sub-folder, tìm parent node là dòng sản phẩm
        const parentNode = this.selectedNode.data?.isSubFolderModel ? this.findParentNode(this.treeData, this.selectedNode) : this.selectedNode;

        if (!parentNode || !parentNode.data?.id) return;

        // ✅ Chỉ lấy các label trong cùng 1 dòng sản phẩm
        const siblingLabels = (parentNode.children || []).map((child) => child.label);

        let index = 1;
        let newLabel = `${originalLabel} (${index})`;
        while (siblingLabels.includes(newLabel)) {
            index++;
            newLabel = `${originalLabel} (${index})`;
        }

        const newModelPayload = {
            lineId: parentNode.data.id,
            name: newLabel,
            code: newLabel,
            description: '',
        };

        this.productLineService.createProductModel(newModelPayload).subscribe({
            next: (response) => {
                const newModel: TreeNode = {
                    label: newLabel,
                    data: {
                        isSubFolderModel: true,
                        type: 'sub-folder',
                        id: response.id,
                        lineId: parentNode.data.id,
                        parentNodeId: parentNode.data.id,
                    },
                    expanded: true,
                };

                parentNode.children = [...(parentNode.children || []), newModel];
                parentNode.expanded = true;
                this.selectedNode = newModel;
                this._customerFilterOverridden = false;
                this._customerPopupOverridden = false;
                this.isSubFolder = true; // Đặt lại trạng thái sub-folder
                this.handleSubFolderSe(response); // hoặc cập nhật node cụ thể nếu bạn không muốn gọi lại toàn bộ
                setTimeout(() => {
                    this.scrollToNewModel(newModel, parentNode.data.id);
                }, 0);
            },
            error: () => {
                this.messageService.add({
                    severity: 'error',
                    summary: 'Lỗi',
                    detail: 'Không thể tạo Model',
                });
            },
        });
    }
    private scrollToNewModel(newNode: TreeNode, parentNodeId: string): void {
        if (!this.scrollContainer) return;

        const container = this.scrollContainer.nativeElement;

        // Tìm node DOM có data-id và data-line-id tương ứng
        const targetNode = container.querySelector(`[data-id="${newNode.data.id}"][data-line-id="${parentNodeId}"]`) as HTMLElement;

        if (targetNode) {
            const containerTop = container.getBoundingClientRect().top;
            const targetTop = targetNode.getBoundingClientRect().top;

            // Tính khoảng cách giữa node và vùng scroll
            const offset = targetTop - containerTop;

            // Scroll vùng scrollContainer đến đúng vị trí node
            container.scrollBy({
                top: offset - 300, // trừ thêm 100px để node nằm giữa (tuỳ chỉnh)
                behavior: 'smooth',
            });
        }
    }
    handleSubFolderSe(response: any) {
        this.productLineService
            .getFilteredProducts({
                modelId: response.id,
                lineId: response.lineId,
            })
            .subscribe({
                next: (res) => {
                    const rawData = res.body;
                    this.products = rawData.map((item: any) => {
                        const stage = item.lifecycleStage ?? null;
                        return {
                            model: item.modelName,
                            image: item.imageUrl ? `${this.STORAGE_BASE_URL}${item.imageUrl}` : '',
                            customer: item.customerIds || [],
                            lifecycleStage: stage,
                            lifecycleStageLabel: this.getLifecycleStageLabel(stage),
                            ...item,
                        };
                    });
                    const totalCount = res.headers.get('X-Total-Count');
                    this.totalRecords = totalCount ? +totalCount : 0;
                },
                error: (err) => {
                    this.messageService.add({
                        severity: 'error',
                        summary: 'Lỗi',
                        detail: 'Không thể tải danh sách sản phẩm',
                    });
                },
            });
    }

    findParentNode(nodes: TreeNode[], target: TreeNode): TreeNode | null {
        for (const node of nodes) {
            if (node.children?.includes(target)) {
                return node;
            }
            const found = this.findParentNode(node.children || [], target);
            if (found) return found;
        }
        return null;
    }
    addSubFolder() {
        if (this.selectedNode) {
            const newSubFolder: TreeNode = {
                label: 'New Sub-Folder',
                data: { isSubFolderModel: true, type: 'sub-folder' },
                children: [],
            };
            if (this.selectedNode.children) {
                this.selectedNode.children = [...this.selectedNode.children, newSubFolder];
            } else {
                this.selectedNode.children = [newSubFolder];
            }
            this.treeData = [...this.treeData]; // Trigger change detection
        }
    }
    cloneFolder() {
        if (!this.selectedNode || !this.selectedNode.data?.id) return;

        this.selectedModels = []; // Reset model
        this.selectedCustomer = []; // Reset khách hàng
        const originalLabel = this.selectedNode.label;
        const parentChildren = this.selectedNode.parent ? this.selectedNode.parent.children : this.treeData;

        // Gọi API duplicate
        this.productLineService.duplicateProductLine(this.selectedNode.data.id).subscribe({
            next: (response) => {
                // Tạo label không trùng
                const baseName = originalLabel.replace(/\s\(\d+\)$/, '');
                const existingLabels = parentChildren.map((node) => node.label);
                let index = 1;
                let newLabel = `${baseName} (${index})`;

                while (existingLabels.includes(newLabel)) {
                    index++;
                    newLabel = `${baseName} (${index})`;
                }

                // Tạo node mới
                const newNode: TreeNode = {
                    label: newLabel,
                    data: {
                        type: 'product-line',
                        id: response.id, // từ API duplicate
                    },
                    children: [],
                };

                if (this.selectedNode.parent) {
                    this.selectedNode.parent.children = [...this.selectedNode.parent.children, newNode];
                } else {
                    this.treeData = [...this.treeData, newNode];
                }

                this.treeData = []; // Trigger change detection
                this.currentPage = 0;
                this.allDataLoaded = false;
                // this.selectedNode = newNode;
                this.selectedNode = newNode; // Chọn node mới
                this._customerFilterOverridden = false;
                this._customerPopupOverridden = false;
                // 2. Load danh sách sản phẩm của folder vừa tạo
                this.onNodeSelect(newNode);
                this.getProductLines();
            },
            error: (err) => {
                this.messageService.add({
                    severity: 'error',
                    summary: 'Lỗi',
                    detail: 'Không thể sao chép dòng sản phẩm',
                });
            },
        });
    }
    deleteFolder() {
        const id = this.selectedNode.data.id;
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn xóa bản ghi này?',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.productLineService.deleteProductLine(id).subscribe({
                    next: () => {
                        // Xóa trong cây treeData
                        if (this.selectedNode.parent) {
                            this.selectedNode.parent.children = this.selectedNode.parent.children?.filter((node) => node !== this.selectedNode);
                        } else {
                            this.treeData = this.treeData.filter((node) => node !== this.selectedNode);
                        }

                        this.treeData = [];
                        this.currentPage = 0;
                        this.allDataLoaded = false;
                        this.alertService.success('Thành công', 'Xóa thành công');
                        this.getProductLines();
                        this.selectedNode = null;
                        this._customerFilterOverridden = false;
                        this._customerPopupOverridden = false;
                    },
                    error: () => {
                        this.alertService.error('Lỗi', 'Xóa thất bại');
                    },
                });
            },
        });
    }

    addNewNode() {
        if (this.isAddingNewNode) return;
        this.isAddingNewNode = true;
        const baseName = 'Dòng sản phẩm';
        const existingLabels = this.treeData.map((node) => node.label.trim());

        let index = 0;
        let finalLabel = baseName;
        while (existingLabels.includes(finalLabel)) {
            index++;
            finalLabel = `${baseName} (${index})`;
        }

        const tempNode: TreeNode = {
            label: '',
            data: {
                type: 'product-line',
                isNew: true, // ✅ node mới
            },
        };

        this.editingLabel = finalLabel;
        this.editingNode = tempNode;
        setTimeout(() => {
            if (this.editInput && this.editInput.nativeElement) {
                this.editInput.nativeElement.focus();
            }
        });

        this.treeData = [tempNode, ...this.treeData]; // thêm vào đầu
    }
    handleBlur(node: TreeNode) {
        if (node.data?.isNew) {
            // Xử lý khi thêm mới
            if (!this.editingLabel.trim()) {
                node.data = {
                    ...node.data,
                    errors: { labelRequired: true },
                };
            } else {
                // Nếu có nội dung thì lưu
                this.saveNodeEdit(node);
            }
        } else {
            // Xử lý khi chỉnh sửa (giữ nguyên)
            this.cancelEdit();
        }
    }
    onSearchInput() {
        clearTimeout(this.searchDebounceTimer);

        this.searchDebounceTimer = setTimeout(() => {
            this.currentSearchQuery = this.searchQuery.trim();
            this.currentPage = 0;
            this.allDataLoaded = false;
            this.treeData = [];
            this.getProductLines(this.currentSearchQuery);
        }, 500);
    }

    onScroll(event: Event) {
        if (this.loading || this.allDataLoaded) return;
        const element = this.scrollContainer.nativeElement;
        const atBottom = element.scrollHeight - element.scrollTop <= element.clientHeight + 50;

        if (atBottom) {
            this.currentPage++;
            this.getProductLines(this.currentSearchQuery);
        }
    }
    getProductLines(searchName?: string) {
        this.loading = true;
        const payload = {
            page: this.currentPage,
            size: this.pageSize,
            name: this.searchName,
        };

        // Chỉ thêm name nếu có giá trị
        if (searchName && searchName.trim()) {
            payload.name = searchName.trim();
        }
        this.productLineService.getProductLine(payload).subscribe({
            next: (data) => {
                if (data.length === 0) {
                    // Khi API trả về mảng rỗng
                    this.allDataLoaded = true; // Đánh dấu đã hết dữ liệu
                    this.loading = false;
                    return;
                }

                const newNodes = data.map((item) => ({
                    label: item.name,
                    data: {
                        id: item.id,
                        type: 'product-line',
                        hasVersion: item.hasVersion,
                    },
                    children: (item.productModels || []).map((model: any) => ({
                        label: model.name,
                        data: {
                            id: model.id,
                            lineId: model.lineId,
                            type: 'sub-folder',
                            isSubFolderModel: true,
                        },
                    })),
                }));

                this.treeData = [...(this.treeData || []), ...newNodes];
                this.loading = false;
            },
            error: () => {
                this.loading = false;
            },
        });
    }
    onMenu(event: MouseEvent, product: Product) {
        this.selectedProduct = product;
        this.menu?.show(event);
    }
    duplicateProduct(product: Product & { imageUrl?: string; customerIds?: number[] }) {
        this.isEdit = false; // mở popup ở chế độ thêm mới
        this.title = 'Thêm mới';
        this.addProductForm.reset(); // reset form trước khi patch
        let processedImageUrl = product.imageUrl || '';

        if (processedImageUrl.startsWith(this.STORAGE_BASE_URL)) {
            processedImageUrl = processedImageUrl.substring(this.STORAGE_BASE_URL.length);
        }
        if (this._customerPopupRef) {
            this._customerPopupRef.additionalParams = {
                ...this._customerPopupRef.additionalParams,
                size: 10000, // Đảm bảo size 10000 cho lần đầu
            };
            this._customerPopupRef.filterOptions('');
        }
        const imageFile = product.imageName ? { name: product.imageName, url: processedImageUrl } : null;
        this.addProductForm.patchValue({
            productName: product.name,
            brandName: product.tradeName,
            codeName: product.tradeCode,
            description: product.description,
            modelId: product.modelId,
            customer: product.customerId,
            image: imageFile,
            generation: product.generation,
            lifecycleStage: product.lifecycleStage,
        });

        this.uploadRef.uploadedFileName = product.imageName;

        this.editPopup.openDialog(); // mở form popup
        this.modelSelectPopup.filterOptions(product.model);
    }

    deleteProduct(product: Product) {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn xóa bản ghi này?',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.productLineService.deleteProduct(product.id).subscribe({
                    next: () => {
                        // Cập nhật lại danh sách
                        this.searchProducts(); // hàm này nên gọi lại danh sách
                        this.alertService.success('Thành công', 'Đã xóa sản phẩm thành công');
                    },
                    error: () => {
                        this.alertService.error('Lỗi', 'Xóa thất bại');
                    },
                });
            },
        });
    }

    compareHSSP() {
        this.productsToCompare = this.comparedProducts.map((p) => ({
            id: p.productVersions?.[0]?.id, // cần có
            name: p.name,
            version: p.versionName, // cần có
            lifecycleStage: this.getLifecycleStageLabel(p.lifecycleStage), // cần có
            // versionId: p.productVersions?.map((v) => Number(v.id)),
        }));
        if (this.compareProfileComp?.onTabChange) {
            this.compareProfileComp.onTabChange(1);
        }
        this.comparePopup.openDialog();
        setTimeout(() => {
            if (this.compareProfileComp) {
                this.compareProfileComp.initializeData();
            }
        });
    }
    openHsspDialog(event?: Event) {
        // Ngăn chặn hành vi mặc định nếu có event (tránh reload trang)
        if (event) {
            event.preventDefault();
        }
        // Kiểm tra xem versionLink đã được khởi tạo chưa
        if (this.versionLink && this.versionLink.length >= 3) {
            // Tạo URL từ router
            const url = this.router.createUrlTree(this.versionLink).toString();

            // Mở URL trong tab mới
            window.open(url, '_blank');
        } else {
            console.error('Không thể mở HSSP: versionLink không hợp lệ');
        }
    }
}
