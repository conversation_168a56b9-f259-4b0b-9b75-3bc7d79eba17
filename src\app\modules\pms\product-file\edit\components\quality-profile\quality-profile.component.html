<ng-container *ngFor="let section of sections; let idx = index">
    <div class="section-wrapper">
        <p-panel [header]="idx + 1 + '. ' + section.title" [toggleable]="true">
            <app-edit-table-section
                [columns]="section.columns"
                [initialRows]="section.rows"
                (rowsChange)="onSectionRowsChange(section, $event)"
                (saveRowEvent)="onSaveSectionRow(section, $event)"
                (deleteRowEvent)="onDeleteRow($event)"
                [mode]="mode"
                [reloadTrigger]="reloadTrigger"
            >
            </app-edit-table-section>
        </p-panel>
    </div>
</ng-container>
<!-- <div class="tw-flex tw-gap-4 tw-justify-center"> -->
<!-- <p-button label="Lưu" size="small" severity="success" (click)="handleSave()"></p-button> -->

<!-- <p-button
        *ngIf="!isEditMode; else deleteBtn"
        label="Hủy"
        size="small"
        styleClass="p-button-danger"
        (click)="handleCancel()"
    ></p-button> -->

<!-- <ng-template #deleteBtn>
        <p-button label="Xóa" size="small" severity="danger" (click)="handleDeleteVersion()"></p-button>
    </ng-template> -->
<!-- </div> -->
