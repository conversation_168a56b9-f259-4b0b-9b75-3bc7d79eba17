import { Component, Input, OnInit, SimpleChanges, OnChanges, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PanelModule } from 'primeng/panel';
import { ButtonModule } from 'primeng/button';
import { ProductFileService } from 'src/app/services/pms/product-file/product-file.service';
import { ProductDetail, ProductDocumentInput, ProductRecordVersion, SectionConfig } from 'src/app/models/interface/pms';
import { ConfirmationService } from 'primeng/api';
import { ActivatedRoute, Router } from '@angular/router';
import { AlertService } from 'src/app/shared/services/alert.service';
import { EventBusService } from 'src/app/services/eventBus.service';
import { Subscription, take } from 'rxjs';
import { EditTableSectionComponent } from 'src/app/modules/pms/components/edit-table-section/edit-table-section.component';
@Component({
    selector: 'app-quality-profile',
    standalone: true,
    imports: [CommonModule, EditTableSectionComponent, PanelModule, ButtonModule],
    templateUrl: './quality-profile.component.html',
    styleUrls: ['./quality-profile.component.scss'],
})
export class QualityProfileComponent implements OnInit, OnChanges, OnDestroy {
    @Input() isCheckNote: boolean = false;
    @Input() data!: any;
    @Input() version!: ProductRecordVersion;
    @Input() currentProduct!: ProductDetail;
    isEditMode: boolean = false;
    reloadTrigger: number = 0;
    @Input() mode!: string;
    private requestSub!: Subscription;
    sections: SectionConfig[] = [
        {
            category: '0',
            title: 'Tài liệu kiểm soát chất lượng',
            columns: [
                { header: 'Mã tài liệu', field: 'fileCode', type: 'readonly' },
                { header: 'Tài liệu', field: 'document', type: 'text' },
                { header: 'File', field: 'file', type: 'file' },
                { header: 'Tên tài liệu', field: 'name', type: 'text' },
                { header: 'MD5', field: 'md5', type: 'readonly' },
                { header: 'Build time', field: 'buildtime', type: 'text' },
                { header: 'Version', field: 'version', type: 'text' },
            ],
            rows: [],
        },
        {
            category: '1',
            title: 'Test report',
            columns: [
                { header: 'Mã tài liệu', field: 'fileCode', type: 'readonly' },
                { header: 'Tài liệu', field: 'document', type: 'text' },
                { header: 'File', field: 'file', type: 'file' },
                { header: 'Tên tài liệu', field: 'name', type: 'text' },
                { header: 'MD5', field: 'md5', type: 'readonly' },
                { header: 'Build time', field: 'buildtime', type: 'text' },
                { header: 'Version', field: 'version', type: 'text' },
            ],
            rows: [],
        },
        {
            category: '2',
            title: 'Sample',
            columns: [
                { header: 'Mã tài liệu', field: 'fileCode', type: 'readonly' },
                { header: 'Tài liệu', field: 'document', type: 'text' },
                { header: 'File', field: 'file', type: 'file' },
                { header: 'Tên tài liệu', field: 'name', type: 'text' },
                { header: 'MD5', field: 'md5', type: 'readonly' },
                { header: 'Build time', field: 'buildtime', type: 'text' },
                { header: 'Version', field: 'version', type: 'text' },
            ],
            rows: [],
        },
    ];
    constructor(
        private productFileService: ProductFileService,
        private confirmationService: ConfirmationService,
        private alertService: AlertService,
        private router: Router,
        private route: ActivatedRoute,
        private bus: EventBusService,
    ) {}
    ngOnInit(): void {
        this.buildSections();
        const firstSegment = this.route.snapshot.url[0]?.path;
        this.isEditMode = firstSegment === 'edit';
        this.requestSub = this.bus
            .on<void>('REQUEST_PAYLOAD_ALL')
            .pipe(take(1)) // chỉ xử lý 1 lần rồi tự huỷ
            .subscribe(() => {
                const payload = this.handleSave(); // trả về mảng payload
                this.bus.emit('RESPONSE_PAYLOAD_QUALITY', payload);
            });
    }
    ngOnChanges(changes: SimpleChanges) {
        if (changes['currentProduct'] && changes['currentProduct'].currentValue) {
            // mỗi khi parent truyền vào giá trị mới

            if (this.currentProduct.productVersions && this.currentProduct.productVersions.length > 0) {
                this.isCheckNote = true;
            } else {
                this.isCheckNote = false;
            }
        }
        if (changes['data'] && this.data) {
            this.updateRows();
        }
    }
    private updateRows() {
        this.sections.forEach((section) => {
            const docs = this.data[section.category] || [];

            if (section.rows.length === 0) {
                section.rows = docs.map((doc) => ({
                    id: doc.id,
                    document: doc.description || '',
                    file: doc.filePath || null,
                    name: doc.documentName || '',
                    md5: doc.md5 || '',
                    buildtime: doc.buildTime || '',
                    version: doc.versionName || '',
                    isDefault: doc.isDefault || 0,
                    fileName: doc.fileName || '',
                    note: doc.note || '',
                    filePath: doc?.filePath || '',
                    fileCode: doc.fileCode || '',
                }));
            } else {
                const newRows = docs.map((doc, index) => {
                    const oldRow = section.rows[index];

                    return {
                        // Giữ id cũ nếu có, nếu không thì null
                        id: oldRow ? oldRow.id : null,
                        document: doc.description || '',
                        file: doc.filePath || null,
                        name: doc.documentName || '',
                        md5: doc.md5 || '',
                        buildtime: doc.buildTime || '',
                        version: doc.versionName || '',
                        isDefault: doc.isDefault || 0,
                        fileName: doc.fileName || '',
                        note: doc.note || '',
                        filePath: doc?.filePath || '',
                        fileCode: oldRow?.fileCode || doc.fileCode || '',
                    };
                });

                // Gán lại section.rows
                section.rows = newRows;
            }
        });
        this.reloadTrigger++;
    }

    private buildSections(): void {
        if (!this.isCheckNote) {
            // Không cần cột note → giữ nguyên
            this.sections = [...this.sections];
            return;
        }

        // Khi cần thêm cột Note
        this.sections = this.sections.map((section) => ({
            // category: section.category,
            // title: section.title,
            ...section,
            // chèn cột Ghi chú vào cuối mảng columns
            columns: [...section.columns, { header: 'Ghi chú', field: 'note', type: 'text' }],
        }));
    }
    // onSaveSectionRow(section: SectionConfig, e: { index: number; row: any; done: (updated: any) => void }) {
    //     const { index, row, done } = e;

    //     // 1) Chuẩn bị payload cho tạo mới
    //     const createDto: CreateProductDocumentDto = {
    //         versionId: this.version.id,
    //         description: row.document,
    //         type: 4, // tab hồ sơ thiết kế
    //         category: +section.category,
    //         documentType: 4,
    //         fileName: row.name || '',
    //         filePath: row.filePath || '',
    //         md5: row.md5 || '',
    //         buildTime: Number(row.buildtime) || '',
    //         versionName: row.version || '',
    //         note: row.note || '',
    //     };

    //     // 2) Nếu đã có row.id → build Update DTO
    //     let obs$;
    //     if (row.id) {
    //         const updateDto: UpdateProductDocumentDto = {
    //             description: createDto.description,
    //             fileName: createDto.fileName,
    //             filePath: createDto.filePath,
    //             md5: createDto.md5,
    //             buildTime: createDto.buildTime,
    //             versionName: createDto.versionName,
    //             note: createDto.note,
    //         };
    //         obs$ = this.productFileService.updateDocumentRow(row.id, updateDto);
    //     } else {
    //         // 3) Ngược lại call create
    //         obs$ = this.productFileService.createDocumentRow(createDto);
    //     }

    //     // 4) Subscribe chung
    //     obs$.subscribe({
    //         next: (updated) => {
    //             this.alertService.success('Thành công', 'Lưu bản ghi thành công');

    //             // cập nhật mảng JS
    //             section.rows[index] = { ...section.rows[index], ...updated };

    //             // callback để con patch FormGroup
    //             done(updated);
    //         },
    //         error: (err) => {
    //             this.alertService.error('Lỗi', 'Lưu bản ghi thất bại');
    //             console.error('save error:', err);
    //         },
    //     });
    // }

    onDeleteRow(e: { index: number; row: any; done: () => void }) {
        const { index, row, done } = e;
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.productFileService.delete(row.id).subscribe({
                    next: () => {
                        this.alertService.success('Thành công', 'Xóa bản ghi thành công');
                        done();
                        // reload dữ liệu
                    },
                    error: (err) => {
                        this.alertService.error('Lỗi', 'Xóa bản ghi thất bại');
                        console.error('Delete error:', err);
                    },
                });
            },
        });
    }

    handleSave() {
        // Chuyển đổi dữ liệu thành định dạng phù hợp với API
        const payload: ProductDocumentInput[] = [];
        this.sections.forEach((section) => {
            section.rows.forEach((row) => {
                const doc: ProductDocumentInput = {
                    id: row.id || 0, // nếu không có id thì sẽ là null
                    versionId: this.version?.id || 0,
                    description: row.document || '',
                    type: 4, // tab hồ sơ chất lượng
                    documentName: row.name || '',
                    category: +section.category,
                    fileName: row.fileName || '',
                    filePath: row.filePath || '',
                    md5: row.md5 || '',
                    buildTime: row.buildtime || '',
                    versionName: row.version || null,
                    note: row.note || '',
                    isDefault: row.isDefault || 0,
                    documentType: 1,
                    fileCode: row.fileCode || '',
                };
                payload.push(doc);
            });
        });
        return payload;
        // this.productFileService.createUpdateProductDocument(this.currentProduct.id, payload).subscribe({
        //     next: (versionId) => {
        //         this.alertService.success('Thành công', 'Lưu hồ sơ chất lượng thành công');
        //         // Kiểm tra xem URL đã chứa segment 'edit' chưa
        //         const urlSegments = this.route.snapshot.url.map((s) => s.path);
        //         const alreadyEdit = urlSegments[0] === 'edit';
        //         if (!alreadyEdit) {
        //             this.router.navigate(['/pms/product-file/edit', this.currentProduct.id, versionId], {
        //                 state: {
        //                     currentProduct: this.currentProduct,
        //                     version: this.version,
        //                     mode: 'edit',
        //                 },
        //                 replaceUrl: true, // tránh tạo thêm history entry
        //             });
        //         }
        //     },
        // });
    }
    ngOnDestroy(): void {
        this.requestSub.unsubscribe();
    }
    handleCancel() {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn Hủy',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.router.navigate(['/pms/product-file'], {
                    replaceUrl: true, // tránh tạo thêm history entry
                });
            },
        });
    }
    handleDeleteVersion(v: ProductRecordVersion): void {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.productFileService.deleteVersion(this.version.id).subscribe({
                    next: () => {
                        this.alertService.success('Thành công', 'Xóa thành công');
                        this.router.navigate(['/pms/product-file'], {
                            replaceUrl: true, // tránh tạo thêm history entry
                        });
                    },
                    error: (err) => {
                        this.alertService.error('Lỗi', 'Xóa thất bại');
                        console.error('Delete error:', err);
                    },
                });
            },
        });
    }
    onSectionRowsChange(section: SectionConfig, newRows: any[]) {
        section.rows = newRows;
        this.updateDocumentCodes(section);
    }
    private updateDocumentCodes(section: SectionConfig): void {
        const prefix = 'CL'; // hoặc động theo tab nếu cần
        const sectionCode = `${prefix}${+section.category + 1}`;

        // Dùng counter để sinh mã tiếp theo
        if (!section['documentCodeCounter']) {
            const existingCodes = section.rows.map((row) => row.fileCode).filter((code) => code?.startsWith(sectionCode));
            const max = existingCodes.map((code) => parseInt(code.slice(sectionCode.length), 10)).filter((num) => !isNaN(num));
            section['documentCodeCounter'] = max.length > 0 ? Math.max(...max) : 100;
        }

        // Gán mã cho các row chưa có
        section.rows.forEach((row) => {
            if (!row.fileCode) {
                section['documentCodeCounter']++;
                row.fileCode = `${sectionCode}${section['documentCodeCounter'].toString().padStart(2, '0')}`; // ✅ FIX
            }
        });
    }
}
