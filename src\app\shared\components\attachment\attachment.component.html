<a class="attachment-item-name tw-cursor-pointer" (click)="download()" *ngIf="attachment" title="{{ attachment.name }}">
    <ng-container [ngSwitch]="attachment.type">
        <span *ngSwitchCase="3" class="excel-color"><b>Excel</b></span>
        <span *ngSwitchCase="2" class="docx-color"><b>Docx</b></span>
        <span *ngSwitchCase="1" class="pdf-color"><b>PDF</b></span>
        <span *ngSwitchDefault class="default-color"><b>File</b></span>
    </ng-container>

    <span style="border-left: 1px solid #efefef; padding-left: 4px">
        {{ attachment.name }}
    </span>
</a>
