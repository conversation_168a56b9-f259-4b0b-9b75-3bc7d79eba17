import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { LogisticsAgent } from '../../../models/interface/sc';
import { ApiResponse } from 'src/app/models/interface';

@Injectable()
export class LogisticsAgentService extends BaseService<LogisticsAgent> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/logistics-agent');
    }

    importFile(file: File) {
        const formData: FormData = new FormData();
        formData.append('file', file);
        return this.http.post<ApiResponse>('/sc/api/logistics-agent/import-file', formData);
    }
}
