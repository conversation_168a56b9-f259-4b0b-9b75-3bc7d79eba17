import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/common';
import { Component, Input, ElementRef } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { ValidateMessage } from 'src/app/models/interface';

@Component({
    standalone: true,
    selector: 'app-input-validate',
    templateUrl: './input.validation.component.html',
    imports: [NgIf, NgClass, NgStyle, NgForOf],
})
export class InputValidationComponent {
    @Input('formControlGroup') formControlGroup: FormGroup;
    @Input('control') control;
    @Input('fieldName') fieldName: string;
    @Input('pattern') pattern: string;
    @Input('validateMessage') validateMessages: ValidateMessage[];
    @Input('class') class;
    @Input('style') style;
    @Input('type') type: 'number' | 'text' = 'text';

    constructor(public el: ElementRef) {}
}
