<ng-container *ngIf="type === 'text'">
    <input *ngIf="control; else templateDriven" [formControl]="control" pInputText [placeholder]="placeholder" [disabled]="disabled" />
    <ng-template #templateDriven>
        <input type="text" pInputText [placeholder]="placeholder" [(ngModel)]="value" (ngModelChange)="onValueChange($event)" [disabled]="disabled" />
    </ng-template>
</ng-container>
<ng-container *ngIf="type === 'number'">
    <p-inputNumber *ngIf="control; else numberNgModel" [formControl]="control" [placeholder]="placeholder" [disabled]="disabled"></p-inputNumber>
    <ng-template #numberNgModel>
        <p-inputNumber [(ngModel)]="value" (ngModelChange)="onValueChange($event)" [placeholder]="placeholder" [disabled]="disabled"></p-inputNumber>
    </ng-template>
</ng-container>

<p-dropdown
    [disabled]="disabled"
    [readonly]="viewOnly"
    *ngIf="type === 'select-one'"
    [options]="resultSelect.data"
    [(ngModel)]="value"
    (onShow)="onDropdowShow()"
    (onFilter)="onDropdownSearch($event)"
    (onChange)="onChangeDropdown($event)"
    [filter]="filterCombobox"
    filterBy="label"
    [checkmark]="true"
    [optionLabel]="configSelect.fieldLabel"
    [optionValue]="configSelect.fieldValue"
    [loading]="resultSelect.isFetching"
    [showClear]="!viewOnly"
    [placeholder]="placeholder"
    [dataKey]="configSelect.dataKey"
    appendTo="body"
    [formControl]="control"
    [virtualScroll]="true"
    [virtualScrollItemSize]="38"
></p-dropdown>

<p-multiSelect
    [showToggleAll]="showToggleAll"
    [disabled]="disabled"
    [readonly]="viewOnly"
    *ngIf="type === 'select'"
    [options]="resultSelect.data"
    [(ngModel)]="value"
    (onFilter)="onDropdownSearch($event)"
    (onPanelShow)="onDropdowShow()"
    (onChange)="onSelectChange($event)"
    (onClear)="onSelectClear($event)"
    [filter]="filterCombobox"
    display="chip"
    [selectionLimit]="selectionLimit"
    [optionValue]="configSelect.fieldValue"
    [optionLabel]="configSelect.fieldLabel"
    [loading]="resultSelect.isFetching"
    [showClear]="!viewOnly"
    [placeholder]="placeholder"
    appendTo="body"
    [formControl]="control"
    dropdownIcon="pi pi-angle-down"
>
    <ng-template let-option pTemplate="item">
        {{ getOptionLabel(option) }}
    </ng-template>
    <ng-template let-value pTemplate="selectedItems">
        <div class="p-multiselect-token ng-star-inserted" *ngFor="let object of value">
            <span class="p-multiselect-token-label">{{ getOptionLabel(object) }}</span>
            <i class="pi pi-times-circle" style="width: 1rem; height: 1rem; margin-left: 0.5rem; cursor: pointer" (click)="remove($event, object)"></i>
        </div>
        <div *ngIf="!value || value.length === 0" style="overflow: hidden">{{ placeholder || 'lựa chọn' }}</div>
    </ng-template>
</p-multiSelect>

<ng-container *ngIf="type === 'date'">
    <p-calendar
        *ngIf="control; else dateNgModel"
        [formControl]="control"
        [placeholder]="placeholder"
        [disabled]="disabled"
        [iconDisplay]="'input'"
        [showButtonBar]="true"
        [minDate]="configDate.minDate"
        [maxDate]="configDate.maxDate"
        [dateFormat]="configDate.dateFormat"
        (onClose)="onDateHide()"
        [touchUI]="true"
        appendTo="body"
    ></p-calendar>
    <ng-template #dateNgModel>
        <p-calendar
            [(ngModel)]="value"
            [placeholder]="placeholder"
            [disabled]="disabled"
            [iconDisplay]="'input'"
            [showButtonBar]="true"
            [minDate]="configDate.minDate"
            [maxDate]="configDate.maxDate"
            [dateFormat]="configDate.dateFormat"
            (ngModelChange)="onValueChange($event)"
            (onClose)="onDateHide()"
            [touchUI]="true"
            appendTo="body"
        ></p-calendar>
    </ng-template>
</ng-container>
<ng-container *ngIf="type === 'date-range'">
    <app-range-date
        *ngIf="!control; else rangeNgModel"
        [configDate]="configDate"
        [placeholder]="placeholder"
        [disabled]="disabled"
        (onChange)="onValueChange($event)"
    ></app-range-date>
    <ng-template #rangeNgModel>
        <app-range-date
            [formControl]="control"
            [configDate]="configDate"
            [placeholder]="placeholder"
            [disabled]="disabled"
            (onChange)="onValueChange($event)"
        ></app-range-date>
    </ng-template>
</ng-container>
