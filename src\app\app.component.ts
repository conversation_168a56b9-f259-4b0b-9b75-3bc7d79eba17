import { Component, NgZone, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { LoadingService } from './shared/services/loading.service';
import { PrimeNGConfig } from 'primeng/api';

@Component({
    selector: 'app-root',
    templateUrl: './app.component.html',
})
export class AppComponent implements OnInit {
    loading: boolean = false;

    constructor(
        private translate: TranslateService,
        private loadingService: LoadingService,
        private primengConfig: PrimeNGConfig,
        private ngZone: NgZone,
    ) {
        this.translate.setDefaultLang('vi');
        this.translate.use('vi');

        this.primengConfig.setTranslation({
            dayNames: ['Ch<PERSON> nhật', 'Thứ hai', 'Thứ ba', 'Thứ tư', 'Thứ năm', 'Thứ sáu', 'Th<PERSON> bảy'],
            dayNamesShort: ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'],
            dayNamesMin: ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'],
            monthNames: [
                'Tháng 1',
                'Tháng 2',
                'Tháng 3',
                'Tháng 4',
                'Tháng 5',
                'Tháng 6',
                'Tháng 7',
                'Tháng 8',
                'Tháng 9',
                'Tháng 10',
                'Tháng 11',
                'Tháng 12',
            ],
            monthNamesShort: ['Th1', 'Th2', 'Th3', 'Th4', 'Th5', 'Th6', 'Th7', 'Th8', 'Th9', 'Th10', 'Th11', 'Th12'],
            today: 'Hôm nay',
            clear: 'Xóa',
            dateFormat: 'mm/yy',
            weekHeader: 'Tuần',
        });
    }

    ngOnInit(): void {
        this.loadingService.loading$.subscribe((loading) => {
            this.ngZone.run(() => {
                this.loading = loading;
            });
        });
    }
}
