import { After<PERSON>iewInit, Component, <PERSON><PERSON><PERSON>t, ViewChild, inject, ChangeDete<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChildren } from '@angular/core';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { CommonModule } from '@angular/common';
import { ProductInfoComponent } from 'src/app/modules/pms/components/product-info/product-info.component';
import { ProductVersionComponent } from 'src/app/modules/pms/components/product-version/product-version.component';
import { Router, RouterLink } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { DropdownModule } from 'primeng/dropdown';
import { PopupComponent } from 'src/app/shared/components/popup/popup.component';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { CalendarModule } from 'primeng/calendar';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { HasAnyAuthorityDirective } from '../../../../shared/directives/has-any-authority.directive';
import { FormBuilder, FormGroup, FormsModule, Validators } from '@angular/forms';
import { EventPopupSubmit } from 'src/app/models/interface';
import { DialogModule } from 'primeng/dialog';
import { TableModule } from 'primeng/table';
import { CheckboxModule } from 'primeng/checkbox';
import { ChangeHistoryPopupComponent } from 'src/app/modules/pms/product-file/edit/components/change-history-popup/change-history-popup.component';
import { CompareProfileComponent } from 'src/app/modules/pms/product-file/edit/components/compare-profile/compare-profile.component';
import { ProductLineService } from 'src/app/services/pms/product-line/product-line.service';
import { ProductFileService } from 'src/app/services/pms/product-file/product-file.service';
import { ProductFileStateService } from '../../../../services/pms/product-file/product-file-state.service';
import { Observable } from 'rxjs';
import {
    ApprovalRequest,
    ChangeHistoryRecord,
    CloneItem,
    DropdownOption,
    ProductDetail,
    ProductRecordVersion,
    SendApprovalRequest,
    VersionRecord,
} from 'src/app/models/interface/pms';
import { AlertService } from 'src/app/shared/services/alert.service';
import { FilterChangeEvent, FilterTableComponent } from 'src/app/shared/table-module/filter-table/filter-table.component';
import { LIFECYCLE_STAGE_DOC_MAP, LIFECYCLE_STAGE_PRODUCT_MAP } from 'src/app/models/constant/pms';
import { AuthService } from 'src/app/core/auth/auth.service';
import { environment } from 'src/environments/environment';

import { ComboboxNonRSQLComponent } from 'src/app/shared/components/combobox-nonRSQL/combobox-nonRSQL.component';
import { ConfigSelectProps } from 'src/app/shared/table-module/custom-filter-table/custom-filter-table.component';

@Component({
    selector: 'app-list',
    templateUrl: './list.component.html',
    styleUrls: ['./list.component.scss'],
    standalone: true,
    imports: [
        SubHeaderComponent,
        ProductInfoComponent,
        ButtonModule,
        ProductVersionComponent,
        TableCommonModule,
        DropdownModule,
        FormsModule,
        PopupComponent,
        FormCustomModule,
        CalendarModule,
        HasAnyAuthorityDirective,
        CommonModule,
        InputTextModule,
        RouterLink,
        InputTextareaModule,
        DialogModule,
        TableModule,
        ChangeHistoryPopupComponent,
        CheckboxModule,
        CompareProfileComponent,
        ComboboxNonRSQLComponent,
    ],
    providers: [ProductLineService, ProductFileService],
})
export class ProductFileListComponent implements OnInit, AfterViewInit, OnDestroy {
    @ViewChild('ProductVersion') productVersion!: ProductVersionComponent;
    @ViewChild('TransferPopup') transferPopup!: PopupComponent;
    @ViewChild('LifecycleStagePopup') lifecycleStagePopup!: PopupComponent;
    @ViewChild('ApprovalPopup') approvalPopup!: PopupComponent;
    @ViewChild('SendApprovalPopup') sendApprovalPopup!: PopupComponent;
    @ViewChild('HistoryVersionPopup') historyVersionPopup!: PopupComponent;
    @ViewChild('ExportFilePopup') exportFilePopup!: PopupComponent;
    @ViewChild('ClonePopup') clonePopup!: PopupComponent;
    @ViewChild('ComparePopup') comparePopup!: PopupComponent;
    @ViewChild(CompareProfileComponent) compareProfileComp!: CompareProfileComponent;
    @ViewChild('ProductNameFilter') productNameFilter!: FilterTableComponent;
    @ViewChild('productSelect', { static: true }) productSelect!: ComboboxNonRSQLComponent;
    @ViewChild('lineSelect', { static: true }) lineSelect!: ComboboxNonRSQLComponent;
    @ViewChild('userSelect') userSelect!: ComboboxNonRSQLComponent;
    @ViewChild('userApproval') userApproval!: ComboboxNonRSQLComponent;
    @ViewChild('userCC') userCC!: ComboboxNonRSQLComponent;
    @ViewChild('modelSelectFilter', { static: false }) modelSelectFilter!: ComboboxNonRSQLComponent;

    public STORAGE_BASE_URL = environment.STORAGE_BASE_URL;
    //inject deps
    private fb = inject(FormBuilder);
    productLineService = inject(ProductLineService);
    productFileService = inject(ProductFileService);
    productFileStateService = inject(ProductFileStateService);
    // Fake data cho ProductInfoComponent
    // productService = inject(ProductService);
    currentProduct: ProductDetail | null = null;

    // Form builder
    productLineOptions$: Observable<DropdownOption[]>;
    productNameOptions$: Observable<DropdownOption[]>;
    partNumberOptions$: Observable<DropdownOption[]>;
    filterForm!: FormGroup;
    formApprovalPopup: FormGroup;
    formSendApprovalPopup: FormGroup;
    formTransferPopup: FormGroup;
    isVisibleTransferPopup: boolean = false;
    formExportFilePopup: FormGroup;
    cloneForm!: FormGroup;
    // Các giá trị lựa chọn cho từng dropdown
    resultUrl: string | null = null;
    records: VersionRecord[] = [];
    cloneItems: CloneItem[] = [
        { label: 'Tất cả', selected: false, value: 7 },
        { label: 'Hồ sơ thiết kế', selected: false, value: 1 },
        { label: 'Hồ sơ sản xuất', selected: false, value: 2 },
        { label: 'Hồ sơ chất lượng', selected: false, value: 4 },
    ];
    selectedVersions: any[] = [];
    selectedVersion: ProductRecordVersion;
    selectedStageType: string;
    versionOptions: { label: string; value: string | number }[] = [];
    historyRecords: ChangeHistoryRecord[] = [];
    isButtonVisible: boolean = false;
    visibleHistoryVersion: boolean = false;
    visibleApprovalPopup: boolean = false;
    itemsHeader = [{ label: 'Quản lý hồ sơ sản phẩm' }, { label: 'Danh sách hồ sơ sản phẩm' }];
    visible: boolean = false;
    visibleHistoryChanged: boolean = false;
    selectedLineId: number | null = null;
    productLineId: number | null = null;

    today = new Date();
    user$ = this.authService.userObserver.asObservable();

    constructor(
        private pls: ProductLineService,
        private alertService: AlertService,
        private authService: AuthService,
        private cdRef: ChangeDetectorRef,
        private router: Router,
    ) {}

    get stageName(): string {
        return LIFECYCLE_STAGE_DOC_MAP[this.selectedVersion?.lifecycleStage] || '';
    }

    get productSelectConfig(): ConfigSelectProps {
        return {
            url: `/pr/api/product?page=0&size=100&lineId=${this.selectedLineId}`,
            fieldValue: 'id',
            fieldLabel: 'name',
            dataKey: 'id',
            usePost: false,
            rsql: false,
        };
    }

    get productParams() {
        return {
            size: 100,
            // chỉ thêm lineId khi đã chọn (non-null và >0)
            ...(this.selectedLineId != null && this.selectedLineId > 0 ? { lineId: this.selectedLineId } : {}),
            modelId: this.filterForm.get('model')?.value || null,
        };
    }

    get modelParams() {
        return {
            size: 100,
            // chỉ thêm lineId khi đã chọn (non-null và >0)
            ...(this.productLineId != null && this.productLineId > 0 ? { productLineId: this.productLineId } : {}),
        };
    }

    ngOnInit() {
        this.initFormPopup();
        const savedData = this.productFileStateService.getCurrentProduct();
        if (savedData) {
            this.lineSelect.filterOptions(savedData.currentProduct.productLine);
            this.productSelect.filterOptions(savedData.currentProduct.productId);
            this.filterForm.patchValue({
                productLine: savedData.productLine,
                productId: savedData.productId,
                model: savedData.model,
            });
            this.selectedLineId = savedData.productLine;
            this.onSearch(null);
        }
    }

    ngAfterViewInit(): void {
        // const savedData = this.productFileStateService.getCurrentProduct();
        // if (savedData) {
        //     this.filterForm.get('vnptManPn')!.setValue(savedData.vnptManPn);
        //     this.cdRef.detectChanges(); // Trigger update UI ngay
        // }
        const origDebounce = this.userSelect.debouncedGetOptions.bind(this.userSelect);

        // override filterOptions: bọc term thành RSQL rồi gọi debounce
        this.setUserFilterOptions(this.userSelect, this.userApproval, this.userCC);
        this.lineSelect.fetchOptions(null);
        this.modelSelectFilter.additionalParams = {
            ...this.modelSelectFilter.additionalParams,
            productLineId: this.filterForm.get('productLine').value,
        };
        this.modelSelectFilter.fetchOptions(null);

        this.productSelect.fetchOptions(null);
    }

    setUserFilterOptions(...combos: ComboboxNonRSQLComponent[]) {
        combos.forEach((combo) => {
            const origDebounce = combo.debouncedGetOptions.bind(combo);
            combo.filterOptions = (term: string) => {
                const rsql = `email==*${term}*`;
                origDebounce(rsql);
            };
        });
    }

    onSearch(formValue: any): void {
        const { productLine, productId } = this.filterForm.value;

        this.productFileService.getDetailProduct(productId).subscribe({
            next: (res) => {
                this.currentProduct = {
                    id: res.id,
                    lineName: res.lineName ? `${res.lineName}` : '',
                    lineId: res.lineId || 0,
                    name: res.name,
                    description: res.description ?? '',
                    vnptManPn: res.vnptManPn,
                    hardwareVersion: res.hardwareVersion ?? '',
                    firmwareVersion: res.firmwareVersion ?? '',
                    tradeName: res.tradeName,
                    tradeCode: res.tradeCode,
                    modelName: res.modelName || '',
                    generation: res.generation || '',
                    imageUrl: res.imageUrl || '',
                    imageName: res.imageName || '',
                    stage: LIFECYCLE_STAGE_PRODUCT_MAP[res.lifecycleStage] || '',
                    productVersions: res.productVersions,
                    softwareResourceDtos: res.softwareResourceDtos,
                };
                // gan gia tri vao service
                this.productFileStateService.setCurrentProduct({
                    ...this.filterForm.value,
                    currentProduct: this.currentProduct,
                });
            },
            error: () => {},
        });
    }

    handleExportReport() {}

    initFormPopup() {
        this.cloneForm = this.fb.group({
            hasChecked: [false, Validators.requiredTrue],
        });
        this.filterForm = this.fb.group({
            productLine: [null],
            productId: [null, Validators.required],
            model: [null],
            // vnptManPn: [{ value: '', disabled: true }],
        });
        this.formTransferPopup = this.fb.group({
            toEmails: [null, Validators.required],
            note: [''],
        });
        this.formSendApprovalPopup = this.fb.group({
            note: [''],
            email: [null, Validators.required],
            ccEmail: [null],
        });
        this.formApprovalPopup = this.fb.group({
            note: [''],
            noteOfSendApproval: [''],
            fromUser: [''],
            created: [''],
        });
        this.formExportFilePopup = this.fb.group({
            selectedVersion: [null],
        });
        // Khi người dùng chọn Dòng sản phẩm → bật Tên sản phẩm; reset và khoá lại PartNumber
        this.filterForm.get('productLine')!.valueChanges.subscribe((line) => {
            // const nameCtrl = this.filterForm.get('productId')!;
            // nameCtrl.reset({ value: null, disabled: !line }, { emitEvent: false });
            // if (line) {
            //     nameCtrl.enable({ emitEvent: false });
            // }
        });
    }

    onLifecycleStagePopup(data: { version: ProductRecordVersion; type: string }) {
        this.selectedVersion = data.version;
        this.selectedStageType = data.type;
        this.lifecycleStagePopup.openDialog();
    }

    onOpenTransfer(version: ProductRecordVersion) {
        this.selectedVersion = version;
        this.isVisibleTransferPopup = true;
    }

    onOpenApproval(version: ProductRecordVersion) {
        this.formApprovalPopup.reset();
        this.productFileService.getInfoApprovalVersion(version.id).subscribe({
            next: (res) => {
                this.formApprovalPopup.patchValue({
                    noteOfSendApproval: res.note,
                    fromUser: res.fromUser,
                    created: res.created,
                });
            },
            error: (err) => {
                console.error('Lỗi ', err);
            },
        });

        this.selectedVersion = version;
        this.visibleApprovalPopup = true;
    }

    onOpenSendApproval(version: ProductRecordVersion) {
        this.selectedVersion = version;
        this.sendApprovalPopup.openDialog();
    }

    onOpenHistoryVersion() {
        this.visibleHistoryVersion = true;
        this.loadHistoryVersion();
    }

    loadHistoryVersion() {
        if (!this.currentProduct?.id) return;

        this.productFileService.getVersionHistory(this.currentProduct.id).subscribe({
            next: (res) => {
                this.records = this.transformApiData(res);
            },
            error: (err) => {
                console.error('Lỗi khi tải lịch sử version:', err);
            },
        });
    }

    private transformApiData(apiData: any[]): VersionRecord[] {
        return apiData.map((item) => {
            // Xử lý nội dung cập nhật
            let updateContent = '';

            // Nếu có changeFieldDetails, lấy thông tin từ các field thay đổi
            if (item.changeFieldDetails && item.changeFieldDetails.length > 0) {
                updateContent = item.changeFieldDetails
                    .map((change: any) => {
                        // Hiển thị thẳng description và newValue không qua mapping
                        return `${change.description}: ${change.newValue}`;
                    })
                    .join('; ');
            }

            return {
                id: item.id,
                date: this.formatDate(item.created || item.updated), // Sử dụng cả created và updated
                version: item.numberVersion || item.version || '-', // Thêm fallback cho version
                author: item.createdBy || '-',
                updateContent: updateContent,
                cloneVersion: item.cloneNumberVersion || item.cloneVersion || '-', // Thêm fallback cho clone version
            };
        });
    }

    // handlePanelShow(ref: any) {
    //     const term = ref.searchValue ?? '';
    //     ref.filterOptions(term);
    // }

    private formatDate(timestamp: number): string {
        if (!timestamp) return '-';
        const date = new Date(timestamp);
        return date.toISOString().split('T')[0]; // Format YYYY-MM-DD
    }

    submitFormSendApproval(event: EventPopupSubmit<{ note: string; approver: any; ccEmail: any; email: any }>): void {
        console.log(event.value);
        const [userObj] = this.userSelect.objectValue;
        console.log(userObj);
        const req: SendApprovalRequest = {
            productVersionId: this.selectedVersion.id,
            email: event.value.email,
            note: event.value.note,
            ccEmail: event.value.ccEmail,
        };

        this.productFileService.sendApprovalRequest(req).subscribe({
            next: (resp) => {
                this.alertService.success('Thành công', 'Gửi phê duyệt thành công');
                event.close();
                this.loadVersionProfiles();
            },
            error: (err) => {
                console.log(err);
            },
        });
    }

    submitFormApproval(action: string): void {
        let type: number;
        if (action === 'CONFIRM') {
            type = 3;
        } else {
            type = 4;
        }
        const note = this.formApprovalPopup.get('note')!.value;

        const req: ApprovalRequest = {
            productVersionId: this.selectedVersion.id,
            type: type,
            note: note,
        };

        this.productFileService.confirmApprovalRequest(req).subscribe({
            next: (resp) => {
                if (action === 'CONFIRM') {
                    this.alertService.success('Thành công', 'Phê duyệt thành công');
                } else {
                    this.alertService.success('Thành công', 'Từ chối thành công');
                }
                this.loadVersionProfiles();
                this.formApprovalPopup.reset();
                this.visibleApprovalPopup = false;
            },
            error: (err) => {
                this.formApprovalPopup.reset();
                this.visibleApprovalPopup = false;

                console.error('Lỗi khi gọi API phê duyệt:', err);
            },
        });
    }

    submitFileTransfer(version: ProductRecordVersion) {
        const selectedVersionId = this.formExportFilePopup.get('selectedVersion').value;

        // Lấy thông tin version đã chọn từ versionOptions
        const selectedVersion = this.versionOptions.find((v) => v.value === selectedVersionId);

        this.productFileService
            .exportProductVersionHistory({
                productId: this.currentProduct.id, // ID sản phẩm hiện tại
                historyId: selectedVersionId, // ID phiên bản đã chọn
            })
            .subscribe({
                next: (url) => this.downloadFile(url, selectedVersion.label),
                error: (err) => {
                    console.error('Lỗi khi tải xuống file:', err);
                },
            });
    }

    private downloadFile(url, versionName: string) {
        // const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = this.STORAGE_BASE_URL + url;
        a.download = `product_version_${versionName}_history.xlsx`; // Tên file khi tải về

        a.click();
        a.remove();
    }

    submitFormTransfer(action: number) {
        console.log(this.formTransferPopup.value);
        const data = {
            ...this.formTransferPopup.value,
            action,
        };
        console.log(data);
        this.productFileService.transferVersion(this.selectedVersion.id, data).subscribe({
            next: (resp) => {
                this.alertService.success('Thành công', 'Chuyển giao hồ sơ thành công');
                this.closeDialogTransfer();
            },
            error: (err) => {
                console.error('Lỗi:', err);
            },
        });
    }

    closeDialogTransfer() {
        this.isVisibleTransferPopup = false;
        this.formTransferPopup.reset();
    }

    close(type?: string) {
        if (type === 'visibleHistoryVersion') {
            this.visibleHistoryVersion = false;
        } else if (type === 'visibleApprovalPopup') {
            this.formApprovalPopup.reset();
            this.visibleApprovalPopup = false;
        }
    }

    onOpenExportExcel(version: ProductRecordVersion) {
        this.versionOptions = this.records
            .filter((item) => item.version && item.version !== '-')
            .map((item) => {
                return {
                    label: item.version,
                    value: item.id,
                };
            });
        if (this.versionOptions.length > 0) {
            this.formExportFilePopup.get('selectedVersion').setValue(this.versionOptions[0].value);
        }
        this.exportFilePopup.openDialog(version);
    }

    onOpenHistoryChanged(version: ProductRecordVersion) {
        const documentId = version.id;
        this.productFileService.getChangeHistory(documentId).subscribe({
            next: (res) => {
                this.historyRecords = res.map((item: any, index: number) => {
                    const detail = item.changeFieldDetails?.[0];

                    const oldVal = detail?.oldValue ? JSON.parse(detail.oldValue) : {};
                    const newVal = detail?.newValue ? JSON.parse(detail.newValue) : {};
                    return {
                        stt: index + 1,
                        action: this.getActionLabel(item.action),
                        detail: item.changeFieldDetails?.[0]?.description ?? '',
                        beforeFile: oldVal.fileName ?? '',
                        afterFile: newVal.fileName ?? '',
                        beforeVersion: oldVal.versionName ?? '',
                        afterVersion: newVal.versionName ?? '',
                        beforeBuildtime: oldVal.buildTime ?? '',
                        afterBuildtime: newVal.buildTime ?? '',
                        note: item.note || '',
                        user: item.updatedBy || '',
                        timestamp: this.formatTimestamp(item.created),
                    };
                });
            },
        });
        this.visibleHistoryChanged = true;
    }

    private getActionLabel(action: number): string {
        switch (action) {
            case 0:
                return 'Thêm mới';
            case 1:
                return 'Chỉnh sửa';
            case 2:
                return 'Xóa';
            case 3:
                return 'Sao chép';
            case 4:
                return 'Chuyển phase';
            case 5:
                return 'Phê duyệt';
            default:
                return '';
        }
    }

    formatTimestamp(unix: number): string {
        const date = new Date(unix);

        // Định dạng giờ 24h: HH:mm
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const ampm = date.getHours() >= 12 ? 'pm' : 'am'; // Xác định am/pm

        // Định dạng ngày: dd/mm/yyyy
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0'); // Tháng bắt đầu từ 0
        const year = date.getFullYear();

        return `${hours}:${minutes} ${ampm} ${day}/${month}/${year}`;
    }

    onOpenClonePopup(version: ProductRecordVersion) {
        this.selectedVersion = version;

        // reset lại trạng thái checkbox và form trước khi mở
        this.cloneItems.forEach((it) => (it.selected = false));
        this.cloneForm.reset({ hasChecked: false });
        this.clonePopup.openDialog(version);
    }

    handlePopupClose() {
        this.compareProfileComp?.resetState();
    }

    onOpenComparePopup(event: MouseEvent) {
        const selected = this.productVersion.compareVersions();

        if (selected.length === 2) {
            this.selectedVersions = selected;

            if (this.compareProfileComp?.onTabChange) {
                this.compareProfileComp.onTabChange(1);
            }
            this.comparePopup.openDialog();
            setTimeout(() => {
                if (this.compareProfileComp) {
                    this.compareProfileComp.initializeData();
                }
            });
        } else {
            // Thiếu hoặc thừa → thông báo thất bại
            this.alertService.error(`Vui lòng chỉ chọn tối đa 2 version hồ sơ cần so sánh!`);
        }
    }

    onCheckboxChange(index: number, checked: boolean) {
        if (index === 0) {
            // Tích/bỏ hết
            this.cloneItems.forEach((it) => (it.selected = checked));
        } else {
            // Chỉ tích “Tất cả” khi mọi ô con đều được chọn
            const all = this.cloneItems.slice(1).every((it) => it.selected);
            this.cloneItems[0].selected = all;
        }
        const anyChild = this.cloneItems.slice(1).some((it) => it.selected);
        this.cloneForm.get('hasChecked')!.setValue(anyChild);
    }

    submitLifeCycleStage(event: EventPopupSubmit<unknown>, selectedStageType: string) {
        let lifecycleStage: number;
        if (selectedStageType === 'MP') {
            lifecycleStage = 4; // MP
        } else {
            lifecycleStage = 2; // Pilot
        }

        this.productFileService.nextPhase(this.selectedVersion.id, lifecycleStage).subscribe({
            next: (res) => {
                this.alertService.success('Thành công', 'Chuyển giao giai đoạn thành công');
                event.close();
                this.loadVersionProfiles();
            },
            error: (err) => {
                console.log(err);
            },
        });
    }

    submitClonePopup(event: any) {
        const picked = this.cloneItems.filter((i) => i.selected).map((i) => i.value);
        const newArr = picked.filter((n) => n !== 7);
        const option = newArr.reduce((sum, n) => sum + n, 0);

        this.router.navigate(['/pms/product-file/create'], {
            state: {
                option: option,
                currentProduct: this.currentProduct,
                version: this.selectedVersion,
            },
        });
        // this.productFileService.cloneVersionId(this.selectedVersion.id, option).subscribe({
        //     next: (res) => {
        //         console.log('res', res);
        //         event.close();
        //         this.router.navigate(['/pms/product-file/edit', res.productId, res.id]);
        //         // this.loadVersionProfiles();
        //     },
        //     error: () => {},
        // });
    }

    loadVersionProfiles() {
        this.productFileService.getDetailProduct(this.currentProduct.id).subscribe({
            next: (res) => {
                this.currentProduct = {
                    id: res.id,
                    lineName: res.lineName ? `${res.lineName}` : '',
                    lineId: res.lineId || 0,
                    name: res.name,
                    description: res.description ?? '',
                    vnptManPn: res.vnptManPn,
                    hardwareVersion: res.hardwareVersion ?? '',
                    firmwareVersion: res.firmwareVersion ?? '',
                    tradeName: res.tradeName,
                    tradeCode: res.tradeCode,
                    modelName: res.modelName ?? '',
                    generation: res.generation ?? '',
                    imageUrl: res.imageUrl ?? '',
                    imageName: res.imageName || '',
                    stage: LIFECYCLE_STAGE_PRODUCT_MAP[res.lifecycleStage] || '',
                    productVersions: res.productVersions,
                    softwareResourceDtos: res.softwareResourceDtos,
                };
            },
            error: () => {},
        });
    }

    onProductLineSelect(event: FilterChangeEvent) {
        // this.filterForm.get('productId')!.reset();
        // this.filterForm.get('vnptManPn')!.reset();

        // const lineId = event.value as number;
        // if (!lineId) {
        //     this.productSelect.clearSelection(new MouseEvent('click'));
        //     return;
        // }

        // this.selectedLineId = event.value as number;

        this.selectedLineId = event.value as number;
        this.productLineId = event.value as number;
        this.filterForm.get('productId')!.reset(null, { emitEvent: false });
        this.filterForm.get('model')!.reset(null, { emitEvent: false });

        // this.modelSelectFilter.additionalParams = {
        //     ...this.modelSelectFilter.additionalParams,
        //     size: 100,
        //     page: 0,
        //     unpaged: false,
        //     productLineId : this.filterForm.get('productLine').value
        // };
        // this.modelSelectFilter.fetchOptions(null);
    }

    onChangeModel(event: any) {
        this.filterForm.get('productId')!.reset();
        this.productSelect.additionalParams = {
            ...this.productSelect.additionalParams,
            size: 100,
            page: 0,
            unpaged: false,
            modelId: this.filterForm.get('model')?.value || null,
        };
        // this.productSelect.fetchOptions(null);
    }

    handlePanelShow(ref: any) {
        ref.additionalParams = {
            ...this.productParams,
            size: 100,
            page: 0,
            unpaged: false,
        };

        ref.fetchOptions(null);
    }

    onProductSelect(event: FilterChangeEvent) {
        // const prod = event.objects[0] as ProductDetail;
        // if (!event.value) {
        //     this.filterForm.get('vnptManPn')!.setValue('');
        //     return;
        // }
        // this.filterForm.get('vnptManPn')!.setValue(prod.vnptManPn);
    }

    onApproverSelect(event: FilterChangeEvent): void {}

    onViewHistory(event: Event): void {
        event.preventDefault(); // bỏ hành động điều hướng mặc định của <a>
        this.onOpenHistoryVersion();
    }

    ngOnDestroy() {
        if (!this.router.url.includes('product-file')) {
            this.productFileStateService.clear(); // clear cache fiter
        }
    }
}
