import { Component, OnInit } from '@angular/core';
import { TemplateService } from '../../../services/smart-qc/template/template.service';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { CdkDragDrop, DragDropModule } from '@angular/cdk/drag-drop';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ConfirmationService, MessageService, SharedModule } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { DropdownModule } from 'primeng/dropdown';
import { DialogModule } from 'primeng/dialog';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { InputValidationComponent } from '../../../shared/components/input-validation/input.validation.component';
import { ChecklistService } from '../../../services/smart-qc/checklist/checklist.service';
import { AuthService } from '../../../core/auth/auth.service';
import { LoadingService } from '../../../shared/services/loading.service';
import { finalize } from 'rxjs';
import Common from '../../../utils/common';
import { CheckListType } from '../../../models/constant';
import { HasAnyAuthorityDirective } from '../../../shared/directives/has-any-authority.directive';
import { Checklist, Template } from '../../../models/interface/smart-qc';
import { ValidateMessage } from '../../../models/interface';
import { BaseUserService } from '../../../services/administration/admin/user.service';
import { SubHeaderComponent } from '../../../shared/components/sub-header/sub-header.component';

@Component({
    selector: 'app-template-create',
    templateUrl: './template.create.component.html',
    styleUrls: ['./template.style.scss'],
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        SharedModule,
        ButtonModule,
        DropdownModule,
        DragDropModule,
        DialogModule,
        InputTextModule,
        InputTextareaModule,
        InputValidationComponent,
        SharedModule,
        HasAnyAuthorityDirective,
        SubHeaderComponent,
    ],
    providers: [TemplateService],
})
export class TemplateCreateComponent implements OnInit {
    CheckListType = CheckListType;

    checklists: Checklist[] = [];

    id: number = null;
    templateForm: FormGroup;
    checklistHeaderForm: FormGroup;
    subHeaderForm: FormGroup;
    checklistDetailForm: FormGroup;
    template: Template = {
        checklists: [],
        checklistsRemove: [],
    };

    isEdit = false;
    isCopy = false;
    isViewOnly = false;

    itemsHeader = [{ label: 'Quản lý mẫu kiểm tra', url: '/sqc/template' }, { label: 'Tạo mới' }];

    isEditHeader = false;
    isEditSubHeader = false;
    isEditChecklistDetail = false;

    selectedChecklistHeader: Checklist;
    selectedChecklistHeaderIndex: number;
    selectedSubHeader: Checklist;
    selectedSubHeaderIndex: number;
    selectedChecklistDetail: Checklist;
    selectedChecklistDetailIndex: number;

    loading: boolean = true;
    isOpenModalAddCheckHeader: boolean = false;
    isOpenModalAddSubHeader: boolean = false;
    isOpenModalAddCheckDetail: boolean = false;

    isOpenModalConfirmDeleteHeader = false;
    isOpenModalConfirmDeleteSubHeader = false;
    isOpenModalConfirmDeleteCheckDetail = false;

    dataTypeOptions: { label: string; value: number }[] = [
        { label: 'Hình ảnh', value: 1 },
        { label: 'Văn bản', value: 2 },
        { label: 'Lựa chọn', value: 3 },
        { label: 'Số', value: 5 },
        { label: 'Góc độ', value: 6 },
        { label: 'Yes/No', value: 7 },
        { label: 'Barcode', value: 8 },
        { label: 'QR Code ', value: 9 },
    ];

    workTypes: { label: string; value: number }[] = [
        { label: 'Khảo sát', value: 0 },
        { label: 'Lắp đặt', value: 1 },
        { label: 'QA-QC', value: 2 },
        { label: 'Hiệu chỉnh', value: 3 },
        { label: 'Chuyển hàng', value: 4 },
        { label: 'Phát sóng', value: 5 },
    ];

    // Validate messages
    validateMessageNumberImage: ValidateMessage[] = [
        {
            type: 'positiveInteger',
            message: 'Trường số lượng hình ảnh phải lớn hơn hoặc bằng 0 và nhỏ hơn 100',
        },
    ];

    validateMessageValue: ValidateMessage[] = [
        {
            type: 'twoValues',
            message: 'Trường giá trị phải có 2 giá trị, cách nhau bởi dấu ,',
        },
        {
            type: 'multipleValues',
            message: 'Trường giá trị phải có các giá trị cách nhau bởi dấu ,',
        },
    ];

    constructor(
        private user: BaseUserService,
        private templateService: TemplateService,
        private formBuilder: FormBuilder,
        private route: ActivatedRoute,
        private messageService: MessageService,
        private router: Router,
        private checklistService: ChecklistService,
        protected authService: AuthService,
        private loadingService: LoadingService,
        private confirmationService: ConfirmationService,
    ) {
        this.templateForm = this.formBuilder.group({
            workType: [null, [Validators.required, Validators.maxLength(255)]],
            code: [''],
            description: [''],
            name: ['', [Validators.required, Common.trimValidator, Validators.maxLength(255)]],
        });

        this.checklistHeaderForm = this.formBuilder.group({
            name: ['', [Validators.required, Common.trimValidator, Validators.maxLength(255)]],
            description: ['', [Validators.maxLength(255)]],
        });

        this.subHeaderForm = this.formBuilder.group({
            name: ['', [Validators.required, Common.trimValidator, Validators.maxLength(255)]],
            description: ['', [Validators.maxLength(255)]],
        });

        this.checklistDetailForm = this.formBuilder.group({
            name: ['', [Validators.required, Common.trimValidator, Validators.maxLength(255)]],
            description: ['', [Validators.maxLength(255)]],
            type: [null, [Validators.required, Validators.maxLength(255)]],
            numberImage: 0,
            value: ['', [Validators.required, Common.trimValidator, Validators.maxLength(255)]],
        });

        this.checklistDetailForm.get('type')?.valueChanges.subscribe((value) => {
            this.setConditionalValidatorsDataType(value);
        });
    }

    ngOnInit() {
        this.route.paramMap.subscribe((params) => {
            const idEdit = params.get('id');
            const idCopy = params.get('idCopy');
            if (idEdit) {
                this.itemsHeader = [
                    { label: 'Quản lý mẫu kiểm tra', url: '/sqc/template' },
                    { label: 'Chỉnh sửa', url: '' },
                ];
                this.isEdit = true;
                this.id = Number(idEdit);
                // Get data
                this.loadingService.show();
                this.templateService.getOne(this.id).subscribe({
                    next: (res) => {
                        this.template = res.body as Template;
                        this.templateForm.patchValue({
                            workType: this.template.workType,
                            code: this.template.code,
                            name: this.template.name,
                            description: this.template.description,
                        });
                        this.setSelectedChecklist(0);
                        this.loadingService.hide();
                    },
                    error: () => {
                        this.loadingService.hide();
                        this.messageService.add({
                            key: 'app-alert',
                            severity: 'warn',
                            detail: 'Không thể truy cập thông tin mẫu kiểm tra',
                        });
                        this.router.navigate(['/sqc/template']);
                    },
                    complete: () => {
                        // Check Auth
                        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
                        this.authService.isAdminOrPM() ? (this.isViewOnly = false) : (this.isViewOnly = true);
                    },
                });
            } else {
                this.id = null;
            }

            if (idCopy) {
                this.itemsHeader = [{ label: 'Quản lý mẫu kiểm tra', url: '/sqc/template' }, { label: 'Copy' }];
                this.isCopy = true;
                this.loadingService.show();
                this.templateService.getOne(Number(idCopy)).subscribe({
                    next: (res) => {
                        this.template = res.body as Template;
                        this.setTemplateIdAndIdToNull(this.template.checklists);
                        this.template.id = null;
                        this.template.code = null;
                        this.template.used = 0;
                        this.templateForm.patchValue({
                            workType: this.template.workType,
                            code: this.template.code,
                            name: this.template.name,
                            description: this.template.description,
                        });
                        this.setSelectedChecklist(0);
                        this.loadingService.hide();
                    },
                    error: () => {
                        this.loadingService.hide();
                        this.messageService.add({
                            key: 'app-alert',
                            severity: 'warn',
                            summary: 'Warning',
                            detail: 'Mẫu kiểm tra đã bị xoá',
                        });
                        this.router.navigate(['/sqc/template']);
                    },
                    complete: () => {},
                });
            }
        });
    }

    setConditionalValidatorsDataType(dataType: number) {
        const numberImageControl = this.checklistDetailForm.get('numberImage');
        const valueControl = this.checklistDetailForm.get('value');
        valueControl?.clearValidators();
        numberImageControl?.clearValidators();
        if (dataType === CheckListType.OPTION) {
            // Lựa chọn
            valueControl?.setValidators([
                Validators.required,
                Common.multipleValuesValidator,
                Validators.maxLength(255),
            ]);
        } else if (dataType === CheckListType.YES_NO) {
            // Yes/No
            valueControl?.setValidators([Validators.required, Common.twoValuesValidator, Validators.maxLength(255)]);
        } else if (dataType === CheckListType.IMAGE) {
            // Hình ảnh
            numberImageControl?.setValidators([
                Validators.required,
                Common.naturalNumberValidator,
                Validators.maxLength(255),
            ]);
        } else {
            valueControl?.setValidators([]);
            numberImageControl?.setValidators([]);
        }
        valueControl?.updateValueAndValidity();
        numberImageControl?.updateValueAndValidity();
    }

    addChecklistHeader() {
        Common.trimValueInForm(this.checklistHeaderForm, ['name', 'description']);
        if (this.checklistHeaderForm.valid) {
            const header = this.checklistHeaderForm.getRawValue();
            const newHeader = {
                ...header,
                position: this.template.checklists.length + 1,
                level: 1,
                checklists: [],
                templateId: this.template.id,
            };
            if (this.isEdit) {
                // save Checklist
                this.loadingService.show();
                this.checklistService
                    .create(newHeader)
                    .pipe(
                        finalize(() => {
                            this.loadingService.hide();
                        }),
                    )
                    .subscribe({
                        next: (res) => {
                            this.addChecklistHeaderAndSetSelected(res.body as Checklist);
                            this.isOpenModalAddCheckHeader = false;
                            this.messageService.add({
                                key: 'app-alert',
                                severity: 'success',
                                summary: 'Thành công',
                                detail: 'Thêm danh mục thành công',
                            });
                        },
                        error: () => {},
                        complete: () => {},
                    });
            } else {
                this.addChecklistHeaderAndSetSelected(newHeader);
            }
        } else {
            this.markAllAsTouched(this.checklistHeaderForm);
        }
    }

    saveChecklistHeader() {
        Common.trimValueInForm(this.checklistHeaderForm, ['name', 'description']);
        if (this.checklistHeaderForm.valid) {
            const header = this.checklistHeaderForm.getRawValue();
            const updateHeader = {
                ...this.template.checklists[this.selectedChecklistHeaderIndex],
                ...header,
            };
            if (this.isEdit) {
                this.loadingService.show();
                this.checklistService
                    .update(updateHeader)
                    .pipe(
                        finalize(() => {
                            this.loadingService.hide();
                        }),
                    )
                    .subscribe({
                        next: (res) => {
                            this.updateChecklistHeaderAndSetSelected(res.body as Checklist);
                            this.isOpenModalAddCheckHeader = false;
                            this.isEditHeader = false;
                            this.messageService.add({
                                key: 'app-alert',
                                severity: 'success',
                                summary: 'Thành công',
                                detail: 'Cập nhật danh mục thành công',
                            });
                        },
                        error: () => {},
                        complete: () => {},
                    });
            } else {
                this.updateChecklistHeaderAndSetSelected(updateHeader);
                this.isOpenModalAddCheckHeader = false;
                this.isEditHeader = false;
            }
        } else {
            this.markAllAsTouched(this.checklistHeaderForm);
        }
    }

    selectChecklistHeader(index: number) {
        if (this.selectedChecklistHeaderIndex !== index) {
            this.selectedChecklistHeader = this.template.checklists[index];
            this.selectedChecklistHeaderIndex = index;
            if (
                this.template.checklists[index].checklists.length > 0 &&
                this.template.checklists[index].checklists[0].level === 2
            ) {
                this.selectedSubHeader = this.template.checklists[index].checklists[0];
                this.selectedSubHeaderIndex = 0;
                if (this.selectedSubHeader.checklists.length > 0) {
                    this.selectedChecklistDetail = this.selectedSubHeader.checklists[0];
                    this.selectedChecklistDetailIndex = 0;
                } else {
                    this.selectedChecklistDetail = null;
                    this.selectedChecklistDetailIndex = null;
                }
            } else {
                this.selectedSubHeader = null;
                this.selectedSubHeaderIndex = null;
                // Check if have check detail -> set selected
                if (this.selectedChecklistHeader.checklists.length > 0) {
                    this.selectedChecklistDetail = this.selectedChecklistHeader.checklists[0];
                    this.selectedChecklistDetailIndex = 0;
                } else {
                    this.selectedChecklistDetail = null;
                    this.selectedChecklistDetailIndex = null;
                }
            }
        }
    }

    deleteChecklistHeader(index: number) {
        this.isOpenModalConfirmDeleteHeader = true;
        this.selectedChecklistHeaderIndex = index;
        this.selectedChecklistHeader = this.template.checklists[index];
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận xoá danh mục',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.confirmDeleteChecklistHeader();
            },
        });
    }

    openModalAddChecklistHeader() {
        this.checklistHeaderForm.reset();
        this.isOpenModalAddCheckHeader = true;
        this.isEditHeader = false;
    }

    openModalEditHeader(index: number) {
        this.isEditHeader = true;
        this.setSelectedChecklist(index);
        /*this.selectedChecklistHeaderIndex = index;
        this.selectedChecklistHeader = this.template.checklists[index];*/
        this.checklistHeaderForm.reset();
        this.checklistHeaderForm.patchValue(this.selectedChecklistHeader);
        this.isOpenModalAddCheckHeader = true;
    }

    openModalEditSubHeader(index: number) {
        this.isEditSubHeader = true;
        this.selectedSubHeaderIndex = index;
        this.selectedSubHeader = this.selectedChecklistHeader.checklists[this.selectedSubHeaderIndex];
        this.subHeaderForm.reset();
        this.subHeaderForm.patchValue(this.selectedSubHeader);
        this.isOpenModalAddSubHeader = true;
    }

    openAddSubHeaderModal() {
        this.subHeaderForm.reset();
        this.isOpenModalAddSubHeader = true;
        this.isEditSubHeader = false;
    }

    addSubHeader() {
        Common.trimValueInForm(this.subHeaderForm, ['name', 'description']);
        if (this.subHeaderForm.valid) {
            const subHeader = this.subHeaderForm.getRawValue();
            const newSubHeader = {
                position: this.template.checklists[this.selectedChecklistHeaderIndex].checklists.length + 1,
                ...subHeader,
                level: 2,
                checklists: [],
                parentId: this.selectedChecklistHeader.id,
                templateId: this.template.id,
            };
            if (this.isEdit) {
                // save Checklist
                this.loadingService.show();
                this.checklistService
                    .create(newSubHeader)
                    .pipe(
                        finalize(() => {
                            this.loadingService.hide();
                        }),
                    )
                    .subscribe({
                        next: (res) => {
                            this.addSubHeaderAndSetSelected(res.body as Checklist);
                            this.isOpenModalAddCheckHeader = false;
                            this.loadingService.hide();
                            this.messageService.add({
                                key: 'app-alert',
                                severity: 'success',
                                summary: 'Thành công',
                                detail: 'Thêm thành phần thành công',
                            });
                        },
                        error: () => {},
                        complete: () => {},
                    });
            } else {
                this.addSubHeaderAndSetSelected(newSubHeader);
                this.isOpenModalAddCheckHeader = false;
            }
        } else {
            this.markAllAsTouched(this.subHeaderForm);
        }
    }

    saveSubHeader() {
        Common.trimValueInForm(this.subHeaderForm, ['name', 'description']);
        if (this.subHeaderForm.valid) {
            const subHeader = this.subHeaderForm.getRawValue();
            const updateSubHeader = {
                ...this.template.checklists[this.selectedChecklistHeaderIndex].checklists[this.selectedSubHeaderIndex],
                ...subHeader,
            };
            if (this.isEdit) {
                this.loadingService.show();
                this.checklistService
                    .update(updateSubHeader)
                    .pipe(
                        finalize(() => {
                            this.loadingService.hide();
                        }),
                    )
                    .subscribe({
                        next: (res) => {
                            this.updateSubHeaderAndSetSelected(res.body as Checklist);
                            this.isOpenModalAddSubHeader = false;
                            this.isEditSubHeader = false;
                            this.messageService.add({
                                key: 'app-alert',
                                severity: 'success',
                                summary: 'Thành công',
                                detail: 'Cập nhật thành phần thành công',
                            });
                        },
                        error: () => {},
                        complete: () => {},
                    });
            } else {
                this.updateSubHeaderAndSetSelected(updateSubHeader);
                this.isOpenModalAddSubHeader = false;
                this.isEditSubHeader = false;
            }
        } else {
            this.markAllAsTouched(this.subHeaderForm);
        }
    }

    selectSubHeader(index: number) {
        this.selectedSubHeaderIndex = index;
        this.selectedSubHeader = this.template.checklists[this.selectedChecklistHeaderIndex].checklists[index];
        if (this.selectedSubHeader.checklists.length > 0) {
            this.selectedChecklistDetail = this.selectedSubHeader.checklists[0];
            this.selectedChecklistDetailIndex = 0;
        } else {
            this.selectedChecklistDetail = null;
            this.selectedChecklistDetailIndex = null;
        }
    }

    openAddCheckDetailModal() {
        this.checklistDetailForm.reset();
        this.isOpenModalAddCheckDetail = true;
        this.isEditChecklistDetail = false;
    }

    addChecklistDetail() {
        Common.trimValueInForm(this.checklistDetailForm, ['name', 'value', 'description']);
        // Check select sub header
        if (this.checklistDetailForm.valid) {
            const checkDetail = this.checklistDetailForm.getRawValue();
            let newChecklistDetail: Checklist;
            if (this.selectedSubHeader) {
                this.template.checklists[this.selectedChecklistHeaderIndex].nonSubHeader = 0;
                if (
                    !this.template.checklists[this.selectedChecklistHeaderIndex].checklists[this.selectedSubHeaderIndex]
                        .checklists
                ) {
                    this.template.checklists[this.selectedChecklistHeaderIndex].checklists[
                        this.selectedSubHeaderIndex
                    ].checklists = [];
                }
                newChecklistDetail = {
                    position:
                        this.template.checklists[this.selectedChecklistHeaderIndex].checklists[
                            this.selectedSubHeaderIndex
                        ].checklists.length + 1,
                    ...checkDetail,
                    level: 3,
                    parentId: this.selectedSubHeader.id,
                    templateId: this.template.id,
                };
            } else {
                // Add check detail without sub header
                this.template.checklists[this.selectedChecklistHeaderIndex].nonSubHeader = 1;
                if (!this.template.checklists[this.selectedChecklistHeaderIndex].checklists) {
                    this.template.checklists[this.selectedChecklistHeaderIndex].checklists = [];
                }
                newChecklistDetail = {
                    position: this.template.checklists[this.selectedChecklistHeaderIndex].checklists.length + 1,
                    ...checkDetail,
                    level: 3,
                    parentId: this.selectedChecklistHeader.id,
                    templateId: this.template.id,
                };
            }
            if (this.isEdit) {
                this.loadingService.show();
                this.checklistService
                    .create(newChecklistDetail)
                    .pipe(
                        finalize(() => {
                            this.loadingService.hide();
                        }),
                    )
                    .subscribe({
                        next: (res) => {
                            this.addChecklistDetailAndSetSelected(res.body as Checklist);
                            this.isOpenModalAddCheckDetail = false;
                            this.loadingService.hide();
                            this.messageService.add({
                                key: 'app-alert',
                                severity: 'success',
                                summary: 'Thành công',
                                detail: 'Thêm chi tiết thành công',
                            });
                        },
                        error: () => {},
                        complete: () => {},
                    });
            } else {
                this.addChecklistDetailAndSetSelected(newChecklistDetail);
                this.isOpenModalAddCheckDetail = false;
            }
        } else {
            this.markAllAsTouched(this.checklistDetailForm);
        }
    }

    saveChecklistDetail() {
        if (this.checklistDetailForm.valid) {
            const checkDetail = this.checklistDetailForm.getRawValue();
            const updateChecklistDetail = {
                ...this.selectedChecklistDetail,
                ...checkDetail,
            };
            if (this.isEdit) {
                this.loadingService.show();
                this.checklistService
                    .update(updateChecklistDetail)
                    .pipe(
                        finalize(() => {
                            this.loadingService.hide();
                        }),
                    )
                    .subscribe({
                        next: (res) => {
                            this.saveChecklistDetailAndSetSelected(res.body as Checklist);
                            this.isOpenModalAddCheckDetail = false;
                            this.isEditChecklistDetail = false;
                            this.messageService.add({
                                key: 'app-alert',
                                severity: 'success',
                                summary: 'Thành công',
                                detail: 'Cập nhật chi tiết thành công',
                            });
                        },
                        error: () => {},
                        complete: () => {},
                    });
            } else {
                this.saveChecklistDetailAndSetSelected(updateChecklistDetail);
                this.isOpenModalAddCheckDetail = false;
                this.isEditChecklistDetail = false;
            }
        } else {
            this.markAllAsTouched(this.checklistDetailForm);
        }
    }

    openModalEditChecklistDetail(index: number, checklistDetail: unknown) {
        this.isEditChecklistDetail = true;
        this.selectedChecklistDetailIndex = index;
        this.selectedChecklistDetail = checklistDetail;
        this.checklistDetailForm.reset();
        this.checklistDetailForm.patchValue(this.selectedChecklistDetail);
        this.isOpenModalAddCheckDetail = true;
    }

    selectChecklistDetail(index: number) {
        if (this.selectedChecklistDetailIndex !== index) {
            this.selectedChecklistDetailIndex = index;
            if (this.template.checklists[this.selectedChecklistHeaderIndex].nonSubHeader === 0) {
                this.selectedChecklistDetail =
                    this.template.checklists[this.selectedChecklistHeaderIndex].checklists[
                        this.selectedSubHeaderIndex
                    ].checklists[index];
            } else {
                this.selectedChecklistDetail =
                    this.template.checklists[this.selectedChecklistHeaderIndex].checklists[index];
            }
        }
    }

    deleteChecklistDetail(index: number, checklistDetail: Checklist) {
        this.isOpenModalConfirmDeleteCheckDetail = true;
        this.selectedChecklistDetailIndex = index;
        this.selectedChecklistDetail = checklistDetail;
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận xoá chi tiết',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.confirmDeleteChecklistDetail();
            },
        });
    }

    confirmDeleteChecklistDetail() {
        let deletedItem: Checklist;
        if (this.selectedChecklistHeader.nonSubHeader === 0) {
            deletedItem = this.template.checklists[this.selectedChecklistHeaderIndex].checklists[
                this.selectedSubHeaderIndex
            ].checklists.splice(this.selectedChecklistDetailIndex, 1)[0];
            this.template.checklists[this.selectedChecklistHeaderIndex].checklists[
                this.selectedSubHeaderIndex
            ].checklists = this.reloadPositions(
                this.template.checklists[this.selectedChecklistHeaderIndex].checklists[this.selectedSubHeaderIndex]
                    .checklists,
            );
        } else {
            deletedItem = this.template.checklists[this.selectedChecklistHeaderIndex].checklists.splice(
                this.selectedChecklistDetailIndex,
                1,
            )[0];
            this.template.checklists[this.selectedChecklistHeaderIndex].checklists = this.reloadPositions(
                this.template.checklists[this.selectedChecklistHeaderIndex].checklists,
            );
        }
        if (this.isEdit) {
            const ids: number[] = [];
            this.getAllChildIdFromChecklist(deletedItem, ids);
            this.loadingService.show();
            this.checklistService
                .batchDelete(ids)
                .pipe(
                    finalize(() => {
                        this.loadingService.hide();
                    }),
                )
                .subscribe({
                    next: () => {
                        this.setSelectedAfterDeleteChecklistDetail();
                        this.isOpenModalConfirmDeleteCheckDetail = false;
                        this.messageService.add({
                            key: 'app-alert',
                            severity: 'success',
                            summary: 'Thành công',
                            detail: 'Xoá chi tiết thành công',
                        });
                    },
                    error: () => {},
                    complete: () => {},
                });
        } else {
            this.setSelectedAfterDeleteChecklistDetail();
            this.isOpenModalConfirmDeleteCheckDetail = false;
        }
    }

    deleteSubHeader(index: number) {
        this.isOpenModalConfirmDeleteSubHeader = true;
        this.selectedSubHeaderIndex = index;
        this.selectedSubHeader = this.template.checklists[this.selectedChecklistHeaderIndex].checklists[index];
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận xoá thành phần',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.confirmDeleteSubHeader();
            },
        });
    }

    confirmDeleteSubHeader() {
        const deletedItem = this.template.checklists[this.selectedChecklistHeaderIndex].checklists.splice(
            this.selectedSubHeaderIndex,
            1,
        )[0];
        this.template.checklists[this.selectedChecklistHeaderIndex].checklists = this.reloadPositions(
            this.template.checklists[this.selectedChecklistHeaderIndex].checklists,
        );
        if (this.isEdit) {
            const ids: number[] = [];
            this.getAllChildIdFromChecklist(deletedItem, ids);
            this.loadingService.show();
            this.checklistService
                .batchDelete(ids)
                .pipe(
                    finalize(() => {
                        this.loadingService.hide();
                    }),
                )
                .subscribe({
                    next: () => {
                        this.setSelectedAfterDeleteSubHeader();
                        this.isOpenModalConfirmDeleteSubHeader = false;
                        this.messageService.add({
                            key: 'app-alert',
                            severity: 'success',
                            summary: 'Thành công',
                            detail: 'Xoá thành phần thành công',
                        });
                    },
                    error: () => {},
                    complete: () => {},
                });
        } else {
            this.setSelectedAfterDeleteSubHeader();
            this.isOpenModalConfirmDeleteSubHeader = false;
        }
    }

    confirmDeleteChecklistHeader() {
        const deletedItem = this.template.checklists.splice(this.selectedChecklistHeaderIndex, 1)[0];
        this.template.checklists = this.reloadPositions(this.template.checklists);
        if (this.isEdit) {
            const ids: number[] = [];
            this.getAllChildIdFromChecklist(deletedItem, ids);
            this.loadingService.show();
            this.checklistService
                .batchDelete(ids)
                .pipe(
                    finalize(() => {
                        this.loadingService.hide();
                    }),
                )
                .subscribe({
                    next: () => {
                        this.setSelectedAfterDeleteChecklistHeader();
                        this.isOpenModalConfirmDeleteHeader = false;
                        this.messageService.add({
                            key: 'app-alert',
                            severity: 'success',
                            summary: 'Thành công',
                            detail: 'Xoá danh mục thành công',
                        });
                    },
                    error: () => {},
                    complete: () => {},
                });
        } else {
            this.setSelectedAfterDeleteChecklistHeader();
            this.isOpenModalConfirmDeleteHeader = false;
        }
    }

    dropChecklistHeader(event: CdkDragDrop<string[]>) {
        if (event.previousIndex === event.currentIndex) {
            return;
        }
        if (event.previousContainer === event.container) {
            const newIndex = event.currentIndex;
            // Swap
            const element = this.template.checklists.splice(event.previousIndex, 1)[0];
            this.template.checklists.splice(newIndex, 0, element);

            // Update the selected checklist header index and item
            this.selectedChecklistHeaderIndex = newIndex;
            this.selectedChecklistHeader = this.template.checklists[newIndex];
            this.template.checklists.forEach((item, index) => {
                item.position = index + 1;
            });

            if (this.isEdit) {
                this.loadingService.show();
                this.checklistService
                    .updatePosition(this.template.checklists)
                    .pipe(
                        finalize(() => {
                            this.loadingService.hide();
                        }),
                    )
                    .subscribe({
                        next: () => {
                            this.messageService.add({
                                key: 'app-alert',
                                severity: 'success',
                                summary: 'Thành công',
                                detail: 'Cập nhật danh mục thành công',
                            });
                        },
                        error: () => {},
                        complete: () => {},
                    });
            }
        }
    }

    dropSubHeader(event: CdkDragDrop<string[]>) {
        if (event.previousIndex === event.currentIndex) {
            return;
        }
        if (event.previousContainer === event.container) {
            const newIndex = event.currentIndex;
            // Swap
            const element = this.template.checklists[this.selectedChecklistHeaderIndex].checklists.splice(
                event.previousIndex,
                1,
            )[0];
            this.template.checklists[this.selectedChecklistHeaderIndex].checklists.splice(newIndex, 0, element);

            // Update the selected checklist header index and item
            this.selectedSubHeaderIndex = newIndex;
            this.selectedSubHeader = this.template.checklists[this.selectedChecklistHeaderIndex].checklists[newIndex];
            this.template.checklists[this.selectedChecklistHeaderIndex].checklists.forEach((item, index) => {
                item.position = index + 1;
            });

            if (this.isEdit) {
                this.loadingService.show();
                this.checklistService
                    .updatePosition(this.template.checklists[this.selectedChecklistHeaderIndex].checklists)
                    .pipe(
                        finalize(() => {
                            this.loadingService.hide();
                        }),
                    )
                    .subscribe({
                        next: () => {
                            this.messageService.add({
                                key: 'app-alert',
                                severity: 'success',
                                summary: 'Thành công',
                                detail: 'Cập nhật thành phần thành công',
                            });
                        },
                        error: () => {},
                        complete: () => {},
                    });
            }
        }
    }

    dropChecklistDetail(event: CdkDragDrop<string[]>) {
        if (event.previousIndex === event.currentIndex) {
            return;
        }
        if (event.previousContainer === event.container) {
            let checklistUpdate = [];
            if (this.template.checklists[this.selectedChecklistHeaderIndex].nonSubHeader === 0) {
                const newIndex = event.currentIndex;
                // Swap
                const element = this.template.checklists[this.selectedChecklistHeaderIndex].checklists[
                    this.selectedSubHeaderIndex
                ].checklists.splice(event.previousIndex, 1)[0];
                this.template.checklists[this.selectedChecklistHeaderIndex].checklists[
                    this.selectedSubHeaderIndex
                ].checklists.splice(newIndex, 0, element);

                // Update the selected checklist header index and item
                this.template.checklists[this.selectedChecklistHeaderIndex].checklists[
                    this.selectedSubHeaderIndex
                ].checklists.forEach((item, index) => {
                    item.position = index + 1;
                });

                this.selectedChecklistHeader = this.template.checklists[this.selectedChecklistHeaderIndex];
                this.selectedSubHeader =
                    this.template.checklists[this.selectedChecklistHeaderIndex].checklists[this.selectedSubHeaderIndex];
                this.selectedChecklistDetailIndex = newIndex;
                this.selectedChecklistDetail = this.selectedSubHeader.checklists[newIndex];
                checklistUpdate =
                    this.template.checklists[this.selectedChecklistHeaderIndex].checklists[this.selectedSubHeaderIndex]
                        .checklists;
            } else {
                const newIndex = event.currentIndex;
                // Swap
                const element = this.template.checklists[this.selectedChecklistHeaderIndex].checklists.splice(
                    event.previousIndex,
                    1,
                )[0];
                this.template.checklists[this.selectedChecklistHeaderIndex].checklists.splice(newIndex, 0, element);

                // Update the selected checklist header index and item
                this.template.checklists[this.selectedChecklistHeaderIndex].checklists.forEach((item, index) => {
                    item.position = index + 1;
                });

                this.selectedChecklistHeader = this.template.checklists[this.selectedChecklistHeaderIndex];
                this.selectedChecklistDetailIndex = newIndex;
                this.selectedChecklistDetail =
                    this.template.checklists[this.selectedChecklistHeaderIndex].checklists[newIndex];
                this.template.checklists[this.selectedChecklistHeaderIndex].checklists.forEach((item, index) => {
                    item.position = index + 1;
                });
                checklistUpdate = this.template.checklists[this.selectedChecklistHeaderIndex].checklists;
            }
            if (this.isEdit) {
                this.loadingService.show();
                this.checklistService
                    .updatePosition(checklistUpdate)
                    .pipe(
                        finalize(() => {
                            this.loadingService.hide();
                        }),
                    )
                    .subscribe({
                        next: () => {
                            this.messageService.add({
                                key: 'app-alert',
                                severity: 'success',
                                summary: 'Thành công',
                                detail: 'Cập nhật chi tiết thành công',
                            });
                        },
                        error: () => {},
                        complete: () => {},
                    });
            }
        }
    }

    setSelectedChecklist(index: number) {
        if (this.template.checklists.length > 0 && this.template.checklists[index]) {
            this.selectedChecklistHeader = this.template.checklists[index];
            this.selectedChecklistHeaderIndex = index;
            if (this.selectedChecklistHeader.nonSubHeader === 0) {
                if (this.selectedChecklistHeader.checklists && this.selectedChecklistHeader.checklists.length > 0) {
                    this.selectedSubHeader = this.selectedChecklistHeader.checklists[0];
                    this.selectedSubHeaderIndex = 0;
                    if (this.selectedSubHeader.checklists && this.selectedSubHeader.checklists.length > 0) {
                        this.selectedChecklistDetail = this.selectedSubHeader.checklists[0];
                        this.selectedChecklistDetailIndex = 0;
                    } else {
                        this.selectedChecklistDetail = null;
                        this.selectedChecklistDetailIndex = null;
                    }
                } else {
                    this.selectedSubHeader = null;
                    this.selectedSubHeaderIndex = null;
                }
            } else {
                if (this.selectedChecklistHeader.checklists && this.selectedChecklistHeader.checklists.length > 0) {
                    this.selectedChecklistDetail = this.selectedChecklistHeader.checklists[0];
                    this.selectedChecklistDetailIndex = 0;
                } else {
                    this.selectedChecklistDetail = null;
                    this.selectedChecklistDetailIndex = null;
                }
            }
        } else {
            this.selectedChecklistHeader = null;
            this.selectedChecklistHeaderIndex = null;
        }
    }

    saveTemplate() {
        Common.trimValueInForm(this.templateForm, ['name']);
        if (this.templateForm.valid) {
            const templateData = this.templateForm.getRawValue();
            // Bind Data from Form
            this.template.code = templateData.code;
            this.template.name = templateData.name;
            this.template.workType = templateData.workType;
            this.template.description = templateData.description;
            this.loadingService.show();
            if (this.isEdit) {
                this.templateService
                    .update(this.template)
                    .pipe(
                        finalize(() => {
                            this.loadingService.hide();
                        }),
                    )
                    .subscribe({
                        next: () => {
                            this.messageService.add({
                                key: 'app-alert',
                                severity: 'success',
                                summary: 'Thành công',
                                detail: 'Cập nhật mẫu kiểm tra thành công',
                            });
                        },
                        error: () => {},
                        complete: () => {},
                    });
            } else {
                this.templateService
                    .create(this.template)
                    .pipe(
                        finalize(() => {
                            this.loadingService.hide();
                        }),
                    )
                    .subscribe({
                        next: (res: unknown) => {
                            this.messageService.add({
                                key: 'app-alert',
                                severity: 'success',
                                summary: 'Thành công',
                                detail: 'Tạo mẫu kiểm tra thành công',
                            });
                            this.router.navigate(['/sqc/template/' + res['body']['id'] + '/edit']);
                        },
                    });
            }
        } else {
            this.markAllAsTouched(this.templateForm);
        }
    }

    updateChecklistHeaderAndSetSelected(updateHeader: Checklist) {
        this.template.checklists[this.selectedChecklistHeaderIndex] = updateHeader;
        this.selectedChecklistHeader = updateHeader;
    }

    addChecklistHeaderAndSetSelected(newHeader: Checklist) {
        this.template.checklists.push(newHeader);
        this.selectedChecklistHeaderIndex = this.template.checklists.length - 1;
        this.selectedChecklistHeader = newHeader;
        this.selectedSubHeader = null;
        this.selectedSubHeaderIndex = null;
        this.selectedChecklistDetail = null;
        this.selectedChecklistDetailIndex = null;
        this.isOpenModalAddCheckHeader = false;
    }

    updateSubHeaderAndSetSelected(updateSubHeader: Checklist) {
        this.template.checklists[this.selectedChecklistHeaderIndex].checklists[this.selectedSubHeaderIndex] =
            updateSubHeader;
        this.selectedSubHeader = updateSubHeader;
    }

    addSubHeaderAndSetSelected(newSubHeader: Checklist) {
        this.template.checklists[this.selectedChecklistHeaderIndex].nonSubHeader = 0;
        this.template.checklists[this.selectedChecklistHeaderIndex].checklists.push(newSubHeader);
        this.selectedSubHeader = newSubHeader;
        this.selectedSubHeaderIndex = this.template.checklists[this.selectedChecklistHeaderIndex].checklists.length - 1;
        this.selectedChecklistHeader = this.template.checklists[this.selectedChecklistHeaderIndex];
        this.isOpenModalAddSubHeader = false;
    }

    addChecklistDetailAndSetSelected(newChecklistDetail: Checklist) {
        if (this.selectedSubHeader) {
            this.template.checklists[this.selectedChecklistHeaderIndex].checklists[
                this.selectedSubHeaderIndex
            ].checklists.push(newChecklistDetail);
            this.selectedChecklistHeader = this.template.checklists[this.selectedChecklistHeaderIndex];
            this.selectedSubHeader =
                this.template.checklists[this.selectedChecklistHeaderIndex].checklists[this.selectedSubHeaderIndex];
            this.selectedChecklistDetail = newChecklistDetail;
            this.selectedChecklistDetailIndex =
                this.template.checklists[this.selectedChecklistHeaderIndex].checklists[this.selectedSubHeaderIndex]
                    .checklists.length - 1;
        } else {
            this.template.checklists[this.selectedChecklistHeaderIndex].checklists.push(newChecklistDetail);
            this.selectedChecklistHeader = this.template.checklists[this.selectedChecklistHeaderIndex];
            this.selectedChecklistDetail = newChecklistDetail;
            this.selectedChecklistDetailIndex =
                this.template.checklists[this.selectedChecklistHeaderIndex].checklists.length - 1;
        }
    }

    saveChecklistDetailAndSetSelected(updateChecklistDetail: Checklist) {
        if (this.selectedChecklistHeader.nonSubHeader === 0) {
            this.template.checklists[this.selectedChecklistHeaderIndex].checklists[
                this.selectedSubHeaderIndex
            ].checklists[this.selectedChecklistDetailIndex] = updateChecklistDetail;
            this.selectedChecklistHeader = this.template.checklists[this.selectedChecklistHeaderIndex];
            this.selectedSubHeader =
                this.template.checklists[this.selectedChecklistHeaderIndex].checklists[this.selectedSubHeaderIndex];
            this.selectedChecklistDetail = updateChecklistDetail;
        } else {
            this.template.checklists[this.selectedChecklistHeaderIndex].checklists[this.selectedChecklistDetailIndex] =
                updateChecklistDetail;
            this.selectedChecklistHeader = this.template.checklists[this.selectedChecklistHeaderIndex];
            this.selectedChecklistDetail = updateChecklistDetail;
        }
    }

    setSelectedAfterDeleteChecklistHeader() {
        if (this.template.checklists.length > 0) {
            this.selectedChecklistHeaderIndex = this.template.checklists.length - 1;
            this.selectedChecklistHeader = this.template.checklists[this.selectedChecklistHeaderIndex];
            if (this.selectedChecklistHeader.nonSubHeader === 0) {
                if (this.selectedChecklistHeader.checklists.length > 0) {
                    this.selectedSubHeader = this.selectedChecklistHeader.checklists[0];
                    this.selectedSubHeaderIndex = 0;
                    if (this.selectedSubHeader.checklists.length > 0) {
                        this.selectedChecklistDetail = this.selectedSubHeader.checklists[0];
                        this.selectedChecklistDetailIndex = 0;
                    } else {
                        this.selectedChecklistDetail = null;
                        this.selectedChecklistDetailIndex = null;
                    }
                } else {
                    this.selectedSubHeader = null;
                    this.selectedSubHeaderIndex = null;
                    this.selectedChecklistDetail = null;
                    this.selectedChecklistDetailIndex = null;
                }
            } else {
                this.selectedSubHeader = null;
                this.selectedSubHeaderIndex = null;
                if (this.selectedChecklistHeader.checklists.length > 0) {
                    this.selectedChecklistDetail = this.checklists[0];
                    this.selectedChecklistDetailIndex = 0;
                } else {
                    this.selectedChecklistDetail = null;
                    this.selectedChecklistDetailIndex = null;
                }
            }
        } else {
            // Set all selected to null
            this.selectedChecklistHeader = null;
            this.selectedChecklistHeaderIndex = null;

            this.selectedSubHeader = null;
            this.selectedSubHeaderIndex = null;

            this.selectedChecklistDetail = null;
            this.selectedChecklistDetailIndex = null;
        }
    }

    setSelectedAfterDeleteSubHeader() {
        this.selectedChecklistHeader = this.template.checklists[this.selectedChecklistHeaderIndex];
        if (this.selectedChecklistHeader.checklists.length > 0) {
            this.selectedSubHeaderIndex = this.selectedChecklistHeader.checklists.length - 1;
            this.selectedSubHeader = this.selectedChecklistHeader.checklists[this.selectedSubHeaderIndex];
            if (this.selectedSubHeader.checklists.length > 0) {
                this.selectedChecklistDetailIndex = 0;
                this.selectedChecklistDetail = this.selectedSubHeader.checklists[0];
            } else {
                this.selectedChecklistDetail = null;
                this.selectedChecklistDetailIndex = null;
            }
        } else {
            this.selectedSubHeader = null;
            this.selectedSubHeaderIndex = null;
        }
    }

    setSelectedAfterDeleteChecklistDetail() {
        if (this.selectedChecklistHeader.nonSubHeader === 0) {
            this.selectedChecklistHeader = this.template.checklists[this.selectedChecklistHeaderIndex];
            this.selectedSubHeader = this.selectedChecklistHeader.checklists[this.selectedSubHeaderIndex];
            if (this.selectedSubHeader.checklists.length > 0) {
                this.selectedChecklistDetailIndex = this.selectedSubHeader.checklists.length - 1;
                this.selectedChecklistDetail = this.selectedSubHeader.checklists[this.selectedChecklistDetailIndex];
            } else {
                this.selectedChecklistDetail = null;
                this.selectedChecklistDetailIndex = null;
            }
        } else {
            this.selectedChecklistHeader = this.template.checklists[this.selectedChecklistHeaderIndex];
            if (this.selectedChecklistHeader.checklists.length > 0) {
                this.selectedChecklistDetailIndex = this.selectedChecklistHeader.checklists.length - 1;
                this.selectedChecklistDetail =
                    this.selectedChecklistHeader.checklists[this.selectedChecklistDetailIndex];
            } else {
                this.selectedChecklistDetail = null;
                this.selectedChecklistDetailIndex = null;
                // Can add Sub-header
                this.selectedChecklistHeader.nonSubHeader = 0;
            }
        }
    }

    getAllChildIdFromChecklist(checklistHeader: Checklist, ids: number[]) {
        if (checklistHeader.id !== null) {
            ids.push(checklistHeader.id);
        }
        if (Array.isArray(checklistHeader.checklists)) {
            for (const item of checklistHeader.checklists) {
                this.getAllChildIdFromChecklist(item, ids);
            }
        }
    }

    markAllAsTouched(form: FormGroup) {
        Object.keys(form.controls).forEach((field) => {
            const control = form.get(field);
            control?.markAsTouched({ onlySelf: true });
        });
    }

    gotoListPage() {
        this.router.navigate(['/sqc/template']);
    }

    reloadPositions(list: unknown[]) {
        return list.map((item, index) => {
            item['position'] = index + 1;
            return item;
        });
    }

    // Function reset id and template id when copy template
    setTemplateIdAndIdToNull(checklists: Checklist[]): void {
        if (checklists && checklists.length > 0) {
            checklists.forEach((checklist) => {
                // Set templateId to null
                checklist.templateId = null;
                checklist.id = null;
                this.setTemplateIdAndIdToNull(checklist.checklists);
            });
        }
    }
}
