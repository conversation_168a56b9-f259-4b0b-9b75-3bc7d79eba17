import { Component, AfterView<PERSON>nit, ViewChild, TemplateRef, OnInit, inject } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { Column, ProjectPlan, SendApprovalRequest, ApprovalRequest, Version } from 'src/app/models/interface/ptm/project-plan';
import { TABLE_KEY } from 'src/app/models/constant';
import { Menu, MenuModule } from 'primeng/menu';
import { MenuItem } from 'primeng/api';
import { ProductSoftware } from 'src/app/models/interface/pms';
import { ProductSoftwareService } from 'src/app/services/pms/production-software/product-software.service';
import { Subject, takeUntil } from 'rxjs';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { BaseUserService } from 'src/app/services/administration/admin/user.service';
import { AuthService } from 'src/app/core/auth/auth.service';
import { TagModule } from 'primeng/tag';
import { ConfirmationService } from 'primeng/api';
import { FormBuilder, FormGroup, FormsModule, Validators } from '@angular/forms';
import { ComboboxNonRSQLComponent } from 'src/app/shared/components/combobox-nonRSQL/combobox-nonRSQL.component';
import { ProductVersionComponent } from 'src/app/modules/pms/components/product-version/product-version.component';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { CommonModule } from '@angular/common';
import { DialogModule } from 'primeng/dialog';
import { ProjectPlanService } from 'src/app/services/ptm/project-plan/project-plan.service';
import { Router } from '@angular/router';
import { AlertService } from 'src/app/shared/services/alert.service';
import { TableCommonService } from 'src/app/shared/table-module/table.common.service';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { TableCustomComponent, TrackColumn } from 'src/app/shared/components/table-custom/table-custom.component';
import { PopupComponent } from 'src/app/shared/components/popup/popup.component';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';

interface ProductHistory {
    stt: number;
    action: string;
    details: string;
    user: string;
    note: string;
    timestamp: Date;
}

interface EventPopupSubmit<T> {
    value: T;
    close: () => void;
}
@Component({
    selector: 'app-list',
    standalone: true,
    templateUrl: './list.component.html',
    styleUrls: ['./list.component.scss'],
    imports: [
        SubHeaderComponent,
        ButtonModule,
        FormCustomModule,
        TableCommonModule,
        MenuModule,
        TagModule,
        PopupComponent,
        TableCustomComponent,
        ComboboxNonRSQLComponent,
        InputTextareaModule,
        CommonModule,
        DialogModule,
    ],
    providers: [BaseUserService],
})
export class ProjectPlanListComponent implements OnInit, AfterViewInit {
    @ViewChild('ActionMenu') actionMenu!: Menu;
    @ViewChild('actionsTpl', { static: true }) actionsTpl!: TemplateRef<any>;
    @ViewChild('templateActive') templateActive: TemplateRef<Element>;
    @ViewChild('timelineTpl', { static: true }) timelineTpl!: TemplateRef<any>;
    @ViewChild('approvalStatusTpl', { static: true }) approvalStatusTpl!: TemplateRef<any>;
    @ViewChild('HistoryPopup', { static: true }) private historyPopup!: PopupComponent;
    @ViewChild('SendApprovalPopup', { static: true }) private sendApprovalPopup!: PopupComponent;
    @ViewChild('userSelect')
    userSelect!: ComboboxNonRSQLComponent;

    productSoftwareService = inject(ProductSoftwareService);

    userService = inject(BaseUserService);
    today = new Date();
    user$ = this.authService.userObserver.asObservable();
    formSendApprovalPopup: FormGroup;
    visibleApprovalPopup: boolean = false;
    formApprovalPopup: FormGroup;
    itemsHeader = [{ label: 'Kế hoạch dự án' }, { label: 'Danh sách Quản lý Kế hoạch dự án' }];
    columns: Column[] = [];
    selectedProjectId: number;
    itemsAction: MenuItem[] = [];
    tableId: string = TABLE_KEY.PROJECT_PLAN;
    state!: QueryObserverBaseResult<ProjectPlan[]>;
    private destroy$ = new Subject<void>();
    historyData: ProductHistory[] = [];
    private fb = inject(FormBuilder);

    constructor(
        private tableCommonService: TableCommonService,
        private pps: ProjectPlanService,
        private cf: ConfirmationService,
        private authService: AuthService,
        private router: Router,
        private alertService: AlertService,
    ) {}

    ngOnInit() {
        this.initFormPopup();
        this.tableCommonService
            .init<ProjectPlan>({
                tableId: this.tableId,
                queryFn: (filter, body) => this.pps.searchProjects(filter, body),

                configFilter: ['projectName', 'productName', 'vnptManPn', 'startDate', 'rate', 'milestoneMultilevelNumbering', 'approvalStatus'],
                configFilterRSQL: {
                    projectName: 'Text',
                    productName: 'Text',
                    vnptManPn: 'Text',
                    startDate: 'Text',
                    rate: 'Text',
                    milestoneMultilevelNumbering: 'Text',
                    approvalStatus: 'Text',
                },
            })
            .pipe(takeUntil(this.destroy$))
            .subscribe((state) => {
                const transformedData = state.data.map((item) => ({
                    ...item,
                }));
                this.state = {
                    ...state,
                    data: transformedData,
                };
            });
    }

    initFormPopup() {
        this.formSendApprovalPopup = this.fb.group({
            note: [''],
            approver: [null, Validators.required],
        });
        this.formApprovalPopup = this.fb.group({
            note: [''],
            noteOfSendApproval: [''],
            fromUser: [''],
            created: [''],
        });
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.columns = [
                { field: 'projectName', header: 'Tên dự án' },
                { field: 'productName', header: 'Tên sản phẩm' },
                { field: 'vnptManPn', header: 'VNPT Man P/N' },
                { field: 'startDate', header: 'Timeline', body: this.timelineTpl },
                { field: 'rate', header: 'Tỷ lệ hoàn thành' },
                { field: 'milestoneMultilevelNumbering', header: 'Milestone' },
                { field: 'approvalStatus', header: 'Trạng thái phê duyệt', body: this.approvalStatusTpl },
                { field: 'action', header: 'Thao tác', bodyWrapper: this.actionsTpl },
            ];
        });
        const origDebounce = this.userSelect.debouncedGetOptions.bind(this.userSelect);

        this.userSelect.filterOptions = (term: string) => {
            const rsql = `email==*${term}*`;
            origDebounce(rsql);
        };
    }

    trackCols: TrackColumn[] = [
        { field: 'stt', header: 'STT' },
        { field: 'action', header: 'Thao tác thay đổi', width: '150px' },
        {
            field: 'details',
            header: 'Chi tiết cập nhật',
            width: '300px',
        },
        { field: 'note', header: 'Ghi chú', width: '100px' },
        { field: 'user', header: 'Người thực hiện', width: '150px' },
        { field: 'timestamp', header: 'Thời điểm thực hiện' },
    ];

    statusMap: Record<number, string> = {
        1: 'Draft',
        2: 'Sent to approved',
        3: 'Rejected',
        4: 'Approved',
    };

    onMenuClick(event: MouseEvent, row: any) {
        this.itemsAction = this.getActionsByStatus(row);

        if (this.actionMenu?.toggle) {
            this.actionMenu.toggle(event);
        } else {
            console.error('ActionMenu not initialized');
        }
    }

    private getActionsByStatus(row: any): MenuItem[] {
        const status = +row.approvalStatus;

        const commonActions = [
            {
                label: 'Xem chi tiết',
                command: () => this.openEditDialog(row, 'view'),
            },
        ];

        const actionMap: Record<number, MenuItem[]> = {
            1: [
                { label: 'Chỉnh sửa', command: () => this.openEditDialog(row, 'edit') },
                { label: 'Nhân bản', command: () => this.openEditDialog(row, 'clone') },
                { label: 'Gửi phê duyệt', command: () => this.submitForApproval(row) },
                { label: 'Xóa', command: () => this.deleteDialog(row) },
                { label: 'Theo dõi lịch sử thay đổi', command: () => this.viewHistory(row) },
            ],
            2: [
                { label: 'Nhân bản', command: () => this.openEditDialog(row, 'clone') },
                { label: 'Phê duyệt', command: () => this.approveRequest(row) },
                { label: 'Theo dõi lịch sử thay đổi', command: () => this.viewHistory(row) },
            ],
            3: [
                { label: 'Chỉnh sửa', command: () => this.openEditDialog(row, 'edit') },
                { label: 'Nhân bản', command: () => this.openEditDialog(row, 'clone') },
                { label: 'Gửi lại phê duyệt', command: () => this.submitForApproval(row) },
                { label: 'Theo dõi lịch sử thay đổi', command: () => this.viewHistory(row) },
            ],
            4: [
                { label: 'Chỉnh sửa', command: () => this.openEditDialog(row, 'edit') },
                { label: 'Nhân bản', command: () => this.openEditDialog(row, 'clone') },
                { label: 'Theo dõi lịch sử thay đổi', command: () => this.viewHistory(row) },
            ],
        };

        return [...commonActions, ...(actionMap[status] || [])];
    }

    submitForApproval(row: any) {
        this.selectedProjectId = row.id;
        this.sendApprovalPopup.openDialog();
    }

    approveRequest(row: any) {
        this.formApprovalPopup.reset();
        this.pps.getInfoApprovalProject(row.id).subscribe({
            next: (res) => {
                this.formApprovalPopup.patchValue({
                    noteOfSendApproval: res.note,
                    fromUser: res.fromUser,
                    created: res.created,
                });
            },
            error: (err) => {
                console.error('Lỗi ', err);
            },
        });

        this.selectedProjectId = row.id;
        this.visibleApprovalPopup = true;
    }

    submitFormApproval(action: string): void {
        let type: number;
        if (action === 'CONFIRM') {
            type = 3;
        } else {
            type = 4;
        }
        const note = this.formApprovalPopup.get('note')!.value;

        const req: ApprovalRequest = {
            projectId: this.selectedProjectId,
            type: type,
            note: note,
        };

        this.pps.confirmApprovalRequest(req).subscribe({
            next: (resp) => {
                if (action === 'CONFIRM') {
                    this.alertService.success('Thành công', 'Phê duyệt thành công');
                } else {
                    this.alertService.success('Thành công', 'Từ chối thành công');
                }
                this.formApprovalPopup.reset();
                this.visibleApprovalPopup = false;
                this.ngOnInit();
            },
            error: (err) => {
                this.formApprovalPopup.reset();
                this.visibleApprovalPopup = false;

                console.error('Lỗi khi gọi API phê duyệt:', err);
            },
        });
    }

    openEditDialog(row: any, type: string) {
        if (type === 'edit') {
            this.router.navigate(['/ptm/project-plans/edit', row.id], {
                state: { approvalStatus: row.approvalStatus },
            });
        } else if (type === 'view') {
            this.router.navigate(['/ptm/project-plans/view', row.id], {
                state: { approvalStatus: row.approvalStatus },
            });
        } else if (type === 'clone') {
            this.router.navigate(['/ptm/project-plans/edit', row.id], {
                state: { cloneFrom: row },
            });
        }
    }

    deleteDialog(row: any) {
        this.cf.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn xóa bản ghi này?',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.pps.deleteProject(row.id).subscribe({
                    next: () => {
                        // Cập nhật lại danh sách
                        this.alertService.success('Thành công', 'Đã xóa kế hoạch dự án thành công');
                        this.ngOnInit(); // hàm này nên gọi lại danh sách
                    },
                    error: () => {
                        this.alertService.error('Lỗi', 'Xóa thất bại');
                    },
                });
            },
        });
    }

    submitFormSendApproval(event: EventPopupSubmit<{ note: string; approver: any }>): void {
        const [userObj] = this.userSelect.objectValue;
        const req: SendApprovalRequest = {
            projectId: this.selectedProjectId,
            email: userObj.email,
            note: event.value.note,
        };
        this.pps.sendApprovalRequest(req).subscribe({
            next: (resp) => {
                this.alertService.success('Thành công', 'Gửi phê duyệt thành công');
                event.close();
                this.ngOnInit();
            },
            error: (err) => {
                console.log(err);
            },
        });
    }

    close(type?: string) {
        // if (type === 'visibleHistoryVersion') {
        //     this.visibleHistoryVersion = false;
        // } else if (type === 'visibleApprovalPopup') {
        this.formApprovalPopup.reset();
        this.visibleApprovalPopup = false;
        // }
    }

    onViewHistory(event: Event): void {
        event.preventDefault(); // bỏ hành động điều hướng mặc định của <a>
        this.viewHistory({ id: this.selectedProjectId });
    }

    viewHistory(row: any) {
        this.pps.getProjectHistory(row.id).subscribe({
            next: (res: any) => {
                this.historyData = res.map((item, index) => {
                    const details = (item.changeFieldDetails || [])
                        .map((c: any) => `${c.description}`)
                        .filter(Boolean)
                        .join('\n');
                    return {
                        stt: index + 1,
                        action: item.action === 0 ? 'Tạo mới' : 'Chỉnh sửa',
                        details,
                        user: item.createdBy,
                        timestamp: this.formatDateTime(item.updated),
                    };
                });
            },
        });
        this.historyPopup.openDialog();
    }
    formatDateTime(timestamp: number): string {
        const date = new Date(timestamp);
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${hours}:${minutes} ${day}/${month}/${year}`;
    }
    getSeverity(state: boolean): string {
        switch (state) {
            case true:
                return 'success';
            case false:
                return 'danger';
            default:
                return 'warning';
        }
    }

    getStateText(state: boolean): string {
        switch (state) {
            case true:
                return 'Hoạt động';
            case false:
                return 'Ngừng hoạt động';
            default:
                return 'Đăng kí';
        }
    }
}
