<app-sub-header
    [items]="[
        { label: 'Quản lý loại nhà cung cấp', url: './..' },
        { label: !isCreate ? 'Chọn NCC đánh giá trước mua' : 'Tạo mới' },
    ]"
    [action]="action"
></app-sub-header>

<ng-template #action>
    <p-button (click)="createCriteriaBuy()" label="Lưu" severity="success" size="small" *ngIf="isCreate" />
    <p-button (click)="close()" label="Đóng" severity="secondary" size="small" />
</ng-template>
<div style="padding-top: 1rem; padding-left: 1rem; padding-right: 1rem">
    <div *ngIf="!isLoadingFirstData">
        <div>
            <p-panel header="Thông tin chung" [toggleable]="true">
                <div [formGroup]="criteriaBuyForm">
                    <div class="tw-grid tw-grid-cols-2 tw-gap-4" *ngIf="isCreate">
                        <div>
                            <div class="tw-font-bold tw-mb-3">Mã</div>
                            <input
                                type="text"
                                placeholder="Hệ thống tự sinh"
                                class="tw-w-full"
                                pInputText
                                formControlName="code"
                            />
                        </div>
                        <div>
                            <div class="tw-font-bold tw-mb-3">Ghi chú</div>
                            <textarea
                                type="text"
                                class="tw-w-full"
                                formControlName="note"
                                rows="3"
                                pInputTextarea
                                placeholder="Ghi chú"
                            ></textarea>
                        </div>
                    </div>
                </div>
                <div class="tw-grid tw-grid-cols-2 tw-gap-4" *ngIf="!isCreate">
                    <div>
                        <div class="tw-font-bold tw-mb-3">Mã</div>
                        <input
                            [value]="criteriaBuy?.code ?? ''"
                            type="text"
                            placeholder="Hệ thống tự sinh"
                            class="tw-w-full"
                            pInputText
                            disabled
                        />
                    </div>
                    <div>
                        <div class="tw-font-bold tw-mb-3">Ghi chú</div>
                        <div>
                            <textarea
                                id="criteriaNoteTextarea"
                                [(ngModel)]="criteriaBuy.note"
                                class="tw-w-full"
                                pInputText
                                [readonly]="!isEditingCriteriaNote"
                                (dblclick)="startEditCriteriaNote()"
                                (blur)="onBlurCriteriaNote()"
                            >
                            </textarea>
                        </div>
                    </div>
                    <div>
                        <div class="tw-font-bold tw-mb-3">Người tạo</div>
                        <input
                            [value]="criteriaBuy?.createBy ?? ''"
                            type="text"
                            class="tw-w-full"
                            pInputText
                            disabled
                        />
                    </div>
                    <div>
                        <div class="tw-font-bold tw-mb-3">Ngày đánh giá</div>
                        <input
                            [value]="DateUtils.formatDateWithPattern(criteriaBuy?.createDate, 'dd/mm/yyyy')"
                            type="text"
                            class="tw-w-full"
                            pInputText
                            disabled
                        />
                    </div>
                </div>
            </p-panel>
        </div>
        <br />
        <p-panel header="Chọn nhà cung cấp" *ngIf="isCreate" [toggleable]="true">
            <ng-container>
                <div class="tw-mb-4">
                    <p-button size="small" label="Thêm mới" (onClick)="isOpenModalAddSupplier = true"> </p-button>
                </div>

                <p-table [value]="selectedSuppliers" styleClass="p-datatable-gridlines">
                    <ng-template pTemplate="header">
                        <tr>
                            <th>STT</th>
                            <th>Tên đầy đủ</th>
                            <th>Tên viết tắt</th>
                            <th>ID</th>
                            <th>Loại hình</th>
                            <th>Thao tác</th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                        <tr>
                            <td>{{ rowIndex + 1 }}</td>
                            <td>
                                {{ item.name }}
                            </td>

                            <td>
                                {{ item.shortName }}
                            </td>

                            <td>
                                {{ item.code }}
                            </td>

                            <td>
                                {{ getSupplierType(item.supplierTypeId) }}
                            </td>
                            <td>
                                <div class="">
                                    <button
                                        class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                        (click)="removeSupplier(rowIndex)"
                                        pTooltip="Xóa"
                                        tooltipPosition="top"
                                        *ngIf="!isCreateAdding && !isCreateEditing"
                                    >
                                        <span class="pi pi-trash"></span>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </ng-template>
                </p-table>
            </ng-container>
        </p-panel>
        <p-panel header="Danh sách nhà cung cấp" *ngIf="!isCreate" [toggleable]="true">
            <ng-container>
                <div class="tw-mb-4">
                    <p-button size="small" label="Thêm mới" (onClick)="isOpenModalAddSupplier = true"> </p-button>
                </div>
                <p-table [value]="criteriaBuy?.criteriaBuySuppliers" styleClass="p-datatable-gridlines">
                    <ng-template pTemplate="header">
                        <tr>
                            <th>Thao tác</th>
                            <th>STT</th>
                            <th>Tên đầy đủ</th>
                            <th>Tên viết tắt</th>
                            <th>ID</th>
                            <th>Loại hình</th>
                            <th>Phiếu đánh giá</th>
                            <th>Ngày đánh giá</th>
                            <th>Kết quả đánh giá</th>
                            <th>Ghi chú</th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                        <tr>
                            <td>
                                <div class="">
                                    <button
                                        class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                        (click)="removeSupplier(rowIndex)"
                                        pTooltip="Xóa"
                                        tooltipPosition="top"
                                        *ngIf="!isCreateAdding && !isCreateEditing"
                                    >
                                        <span class="pi pi-trash"></span>
                                    </button>
                                </div>
                            </td>
                            <td>{{ rowIndex + 1 }}</td>
                            <td>
                                <a [routerLink]="'/sc/supplier-infor/' + item.supplierId" class="tw-text-blue-600 tw-underline hover:tw-text-blue-800">
                                    {{ item.supplierName }}
                                </a>
                            </td>

                            <td>
                                {{ item.supplierShortName }}
                            </td>

                            <td>
                                {{ item.supplierCode }}
                            </td>

                            <td>
                                {{ getSupplierType(item.supplierTypeId) }}
                            </td>
                            <td style="">
                                <div>
                                    <app-button-group-file
                                        [urlError]="urlErrorSupplier.get(item.id)"
                                        service="/sc/api"
                                        [disabled]="false"
                                        (onFileSelected)="handleSelectFile($event, item)"
                                        (onClearFile)="handleClearFile(item)"
                                        (onClickDowload)="handleDownloadFile(item.supplierTypeId)"
                                        [types]="typesAccept"
                                        [attachment]="item.evaluationDoc"
                                        errorWrongFileMessage="Vui lòng thử lại với file excel"
                                    >
                                    </app-button-group-file>
                                </div>
                            </td>
                            <td (dblclick)="startEditingDate(item)">
                                <div *ngIf="editingDateItemId !== item.id">
                                    {{ DateUtils.formatDateWithPattern(item?.evaluateDate, 'dd/mm/yyyy') }}
                                </div>

                                <p-calendar
                                    *ngIf="editingDateItemId === item.id"
                                    #dateInput
                                    id="date-{{ item.id }}"
                                    [showIcon]="true"
                                    [showOnFocus]="false"
                                    appendTo="body"
                                    dateFormat="dd/mm/yy"
                                    [ngModel]="editingDate"
                                    (onSelect)="onSelectDate($event, item)"
                                ></p-calendar>
                            </td>
                            <td class="">
                                <div class="tw-text-center" *ngIf="!item.evaluationDoc">_</div>
                                <div *ngIf="item.evaluationDoc">
                                    <div
                                        class="tw-inline-block bg-green-400 tw-text-white tw-px-8 tw-py-2 tw-min-w-[100px] tw-text-center"
                                        *ngIf="item.result === true"
                                    >
                                        PASS
                                    </div>
                                    <div class="tw-flex tw-space-x-2 tw-items-center" *ngIf="item.result === false">
                                        <div
                                            class="tw-inline-block bg-red-400 tw-text-white tw-px-8 tw-py-2 tw-min-w-[100px] tw-text-center"
                                        >
                                            FAIL
                                        </div>
                                        <button
                                            class="tw-cursor-pointer
                                            tw-w-6 tw-h-6 tw-rounded-full tw-bg-gray-800 tw-text-white tw-flex tw-items-center tw-justify-center hover:tw-bg-gray-600"
                                            (click)="showDetailResult(item)"
                                            pTooltip="Chi tiết"
                                            tooltipPosition="top"
                                        >
                                            <span class="pi pi-info tw-text-xs"></span>
                                        </button>
                                    </div>
                                </div>
                            </td>
                            <td (dblclick)="startEditing(item)">
                                <div *ngIf="editingNoteItemId !== item.id">
                                    {{ item.note || '' }}
                                </div>

                                <input
                                    *ngIf="editingNoteItemId === item.id"
                                    id="note-{{ item.id }}"
                                    (blur)="saveNote(item)"
                                    [value]="item.note ?? ''"
                                    (input)="updateNote(item.id, $event)"
                                    type="text"
                                    placeholder="Ghi chú"
                                    class="tw-w-full"
                                    pInputText
                                />
                            </td>
                        </tr>
                    </ng-template>
                </p-table>
            </ng-container>
        </p-panel>
    </div>
</div>

<p-dialog
    header="Chọn Nhà cung cấp"
    [modal]="true"
    [(visible)]="isOpenModalAddSupplier"
    [style]="{ width: '1400px', top: '' }"
>
    <div>
        <div class="tw-flex tw-justify-end tw-mb-5 tw-space-x-3">
            <p-button
                [disabled]="numberSuppliersSelected === 0"
                (click)="selectSupplier()"
                icon="pi pi-plus"
                label="Chọn"
            />
            <!--<p-button (click)="selectAllStation()" icon="pi pi-plus" label="Chọn tất cả" />-->
        </div>
        <app-table-common
            [tableId]="tableIdSupplier"
            [columns]="columnsSupplierImportTable"
            [data]="stateSupplier.data"
            [loading]="stateSupplier.isFetching"
            name="Nhà cung cấp"
            [stt]="true"
            selectionMode="multiple"
            fieldSelect="id"
            [filterTemplate]="filterSupplier"
        >
            <ng-template #filterSupplier>
                <tr>
                    <th></th>
                    <th></th>
                    <th [appFilter]="[tableIdSupplier, 'name']">
                        <app-filter-table
                            [tableId]="tableIdSupplier"
                            field="name"
                            placeholder="Tên ncc"
                        ></app-filter-table>
                    </th>
                    <th [appFilter]="[tableIdSupplier, 'shortName']">
                        <app-filter-table
                            [tableId]="tableIdSupplier"
                            field="shortName"
                            placeholder="viết tắt"
                        ></app-filter-table>
                    </th>
                    <th [appFilter]="[tableIdSupplier, 'code']">
                        <app-filter-table [tableId]="tableIdSupplier" field="code" placeholder="ID"></app-filter-table>
                    </th>
                    <th [appFilter]="[tableIdSupplier, 'supplierTypeId']">
                        <app-filter-table
                            [tableId]="tableIdSupplier"
                            field="supplierTypeId"
                            type="select"
                            [configSelect]="{
                                fieldValue: 'id',
                                fieldLabel: 'displayName',
                                rsql: true,
                                param: 'name',
                                paramForm: 'id',
                                url: '/sc/api/supplier-type/search',
                            }"
                            placeholder="Loai ncc"
                        ></app-filter-table>
                    </th>
                    <th [appFilter]="[tableIdSupplier, 'productSupplied']">
                        <app-filter-table
                            [tableId]="tableIdSupplier"
                            field="productSupplied"
                            placeholder="hàng hóa"
                        ></app-filter-table>
                    </th>
                </tr>
            </ng-template>
            <ng-template #templateSupplierType let-rowData>
                {{ mapSupplierType[rowData.supplierTypeId] }}
            </ng-template>
        </app-table-common>
    </div>
</p-dialog>

<p-dialog
    header="Chi tiết kết quả đánh giá"
    [modal]="true"
    [(visible)]="isOpenModalViewResult"
    [style]="{ minWidth: '40vw', top: '' }"
>
    <div>
        <div class="tw-flex tw-justify-end tw-items-center tw-mb-5">
            <div class="tw-inline-block bg-red-400 tw-text-white tw-px-8 tw-py-2 tw-min-w-[100px] tw-text-center">
                FAIL
            </div>
        </div>
        <p-table [value]="resultsDetail" styleClass="p-datatable-gridlines">
            <ng-template pTemplate="header">
                <tr>
                    <th>Tiêu chí</th>
                    <th>Giá trị</th>
                    <th>Tiêu chuẩn</th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                <tr>
                    <td>
                        {{ item.name }}
                    </td>
                    <td [ngClass]="{ 'tw-text-red-500': item.result === false }">
                        {{ item.value }}
                    </td>
                    <td>
                        {{ operators[item.operator].name + ' ' + item.valueCompare }}
                    </td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</p-dialog>
