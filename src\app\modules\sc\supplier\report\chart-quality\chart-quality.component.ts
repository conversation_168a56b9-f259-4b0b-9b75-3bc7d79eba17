import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, ElementRef, Input, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { ChartConfiguration } from 'chart.js';
import Chart from 'chart.js/auto';
import { TableModule } from 'primeng/table';
import { catchError, Observable, tap, throwError } from 'rxjs';
import { LineSupplierQuality } from 'src/app/models/interface/sc';
import { SupplierQualityService } from 'src/app/services/sc/supplier-report/suppiler-quality.service';
import { SkeletonLoadingComponent } from 'src/app/shared/components/skeleton-loading/skeleton-loading.component';

@Component({
    selector: 'app-supplier-chart-quality',
    templateUrl: './chart-quality.component.html',
    styleUrls: ['./chart-quality.component.scss'],
    standalone: true,
    imports: [CommonModule, TableModule, SkeletonLoadingComponent],
    providers: [SupplierQualityService],
})
export class ChartQualityComponent implements AfterViewInit {
    @Input() formReport: FormGroup;
    @Input() supplierId: number;

    lineSupplierQuality: LineSupplierQuality;
    @ViewChild('templateChartLine') templateChartLine: ElementRef<HTMLCanvasElement>;
    chartLineSupplierQuality: Chart;
    isLoading = true;

    constructor(private supplierQualityService: SupplierQualityService) {}

    ngAfterViewInit() {
        if (!this.isLoading) {
            this.initializeChart();
        }
    }

    initializeChart() {
        if (this.chartLineSupplierQuality) {
            this.chartLineSupplierQuality.destroy();
        }

        const ctx: HTMLCanvasElement = this.templateChartLine.nativeElement;

        const config: ChartConfiguration<'line', number[], string> = {
            type: 'line',
            data: {
                labels: this.lineSupplierQuality.labels,
                datasets: [
                    {
                        label: 'Điểm theo tháng',
                        data: this.lineSupplierQuality.points,
                    },
                ],
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    filler: {
                        propagate: false,
                    },
                    title: {
                        display: true,
                        text: 'Biểu đồ phân hạng',
                    },
                },
                interaction: {
                    intersect: false,
                },
            },
        };

        this.chartLineSupplierQuality = new Chart(ctx, config);
    }

    callApiReportQuality(): Observable<LineSupplierQuality> {
        let startTime = new Date();
        let endTime = new Date();
        this.isLoading = true;

        if (this.formReport.value['type'] === 'year') {
            startTime.setFullYear(this.formReport.value['year'], 0, 1);
            endTime.setFullYear(this.formReport.value['year'], 11, 1);
        } else {
            startTime = this.formReport.value['startTime'];
            endTime = this.formReport.value['endTime'];
        }
        return this.supplierQualityService
            .lineSupplierQuality(this.supplierId, startTime.getTime(), endTime.getTime())
            .pipe(
                tap((res: LineSupplierQuality) => {
                    this.lineSupplierQuality = res;
                    this.isLoading = false;

                    // If the chart template exists, initialize the chart
                    if (this.templateChartLine) {
                        this.initializeChart();
                    }
                }),
                catchError((error) => {
                    this.isLoading = false;
                    return throwError(error);
                }),
            );
    }
}
