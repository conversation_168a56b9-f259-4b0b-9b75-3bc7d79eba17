<style>
    :host ::ng-deep .p-autocomplete {
        width: 100%;
    }
    :host ::ng-deep .p-autocomplete-input {
        width: 100%;
    }

    :host ::ng-deep .p-inputnumber {
        width: 100%;
    }
</style>

<app-sub-header [items]="itemsHeader" [action]="actionHeader"></app-sub-header>
<ng-template #actionHeader>
    <app-popup
        #childApppopup
        [header]="isCreate ? 'Tạo mới trạng thái' : 'Sửa trạng thái'"
        severity="success"
        label="Tạo mới"
        [formGroup]="formCreate"
        *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN']"
        (onSubmit)="handleCreate($event)"
    >
        <app-form [formGroup]="formCreate" styleClass="tw-grid tw-grid-cols-1 tw-gap-4" *ngIf="formCreate">
            <app-form-item label="Tên">
                <input type="text" class="tw-w-full" pInputText formControlName="name" />
            </app-form-item>

            <app-form-item label="Mô tả">
                <input type="text" class="tw-w-full" pInputText formControlName="description" />
            </app-form-item>
            <app-form-item label="Key">
                <p-autoComplete
                    formControlName="keyEntity"
                    [suggestions]="filteredKeyEntity"
                    (completeMethod)="filterKey($event)"
                    appendTo="body"
                    [dropdown]="true"
                />
            </app-form-item>
            <app-form-item label="Value">
                <p-inputNumber class="tw-w-full" inputId="integeronly" formControlName="value" />
            </app-form-item>
        </app-form>
    </app-popup>
</ng-template>
<div style="padding: 1rem 1rem 0 1rem">
    <app-table-common
        [tableId]="tableId"
        [columns]="columns"
        [data]="state.data"
        [loading]="state.isFetching"
        [filterTemplate]="filterTemplate"
        [funcDelete]="deleteSelectedState"
        [authoritiesDelete]="['ROLE_SYSTEM_ADMIN']"
        [rowSelectable]="rowSelectable"
        name="Danh sách trạng thái"
    >
        <ng-template #filterTemplate>
            <tr>
                <th></th>
                <th [appFilter]="[tableId, 'name']">
                    <app-filter-table [tableId]="tableId" field="name"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'description']">
                    <app-filter-table [tableId]="tableId" field="description"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'keyEntity']">
                    <app-filter-table [tableId]="tableId" field="keyEntity"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'value']"></th>
                <th [appFilter]="[tableId, 'active']"></th>
                <th></th>
            </tr>
        </ng-template>
        <ng-template #templateActive let-rowData>
            <p-checkbox [(ngModel)]="rowData.active" [binary]="true" [disabled]="true" inputId="binary" />
        </ng-template>
        <ng-template #templateAction let-rowData>
            <p-button
                *ngIf="!rowData.active"
                icon="pi pi-pen-to-square
                "
                severity="secondary"
                (click)="openEdit(rowData)"
            />
        </ng-template>
    </app-table-common>
</div>
