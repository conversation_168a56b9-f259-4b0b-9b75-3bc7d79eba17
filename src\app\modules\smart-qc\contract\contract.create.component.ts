import { CommonModule } from '@angular/common';
import {Component, OnInit, ViewChild, inject, ElementRef} from '@angular/core';
import {FormArray, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators} from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { DialogModule } from 'primeng/dialog';
import { TableModule } from 'primeng/table';
import { PaginatorModule } from 'primeng/paginator';
import { Calendar, CalendarModule } from 'primeng/calendar';
import { CardModule } from 'primeng/card';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { DragDropService } from 'src/app/shared/services/drag.drop.service';
import {Action, Error, Card} from 'src/app/models/interface/smart-qc';
import { NgSelectModule } from '@ng-select/ng-select';
import { FormInputComponent } from 'src/app/shared/components/form-input/form-input.component';
import { ContractService } from 'src/app/services/smart-qc/masterdata/contract.service';
import { AreaService } from 'src/app/services/administration/area/area.service';
import { DistrictService } from 'src/app/services/administration/district/district.service';
import { BaseUserService } from 'src/app/services/administration/admin/user.service';
import * as _ from 'lodash';
import { Router, RouterLink } from '@angular/router';
import { TaskCreateTableComponent } from './task-create.table.component';
import { AuthService } from 'src/app/core/auth/auth.service';
import { ConfirmationService, MessageService } from 'primeng/api';
import { trimValidator } from 'src/app/shared/directives/time.span.validator';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { ActionService } from 'src/app/services/smart-qc/masterdata/action.service';
import {FormCustomModule} from "../../../shared/form-module/form.custom.module";
import {TooltipModule} from "primeng/tooltip";
import {ButtonGroupFileComponent} from "../../../shared/components/button-group-file/button-group-file.component";
import {InputValidationComponent} from "../../../shared/components/input-validation/input.validation.component";
import {finalize} from "rxjs";
import {LoadingService} from "../../../shared/services/loading.service";
import {CardService} from "../../../services/smart-qc/masterdata/card.service";
import {ErrorSmartQCService} from "../../../services/smart-qc/masterdata/error.service";
import {FileService} from "../../../shared/services/file.service";
import {ApiResponse} from "../../../models/interface";
import {AlertService} from "../../../shared/services/alert.service";

@Component({
    selector: 'app-contract.create',
    standalone: true,
    templateUrl: './contract.create.component.html',
    imports: [
        CommonModule,
        ReactiveFormsModule,
        ButtonModule,
        InputTextModule,
        DragDropModule,
        DialogModule,
        TableModule,
        RouterLink,
        PaginatorModule,
        CalendarModule,
        CardModule,
        InputTextareaModule,
        FormsModule,
        NgSelectModule,
        FormInputComponent,
        TaskCreateTableComponent,
        SubHeaderComponent,
        FormCustomModule,
        TooltipModule,
        ButtonGroupFileComponent,
        InputValidationComponent,
    ],
    providers: [DragDropService, DistrictService, AreaService, ActionService],
})
export class ContractCreateComponent implements OnInit {
    areaList = [];
    districtList = [];
    districtListFilter = [];
    userList = [];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    contract: any = { actions: [], status: 0 };

    actionForm: FormGroup;
    taskForm: FormGroup;

    visibleActionForm = false;
    visibleTaskForm = false;
    visibleImportForm = false;
    visiblemFileForm = false;
    selectedAction: Action = { templateId: null, tasks: [] };
    selectedTask = { area: null, district: null };
    isNewAction = true;
    isNewTask = true;
    authService = inject(AuthService);
    messageService = inject(MessageService);
    confirmationService = inject(ConfirmationService);
    selectedTasks = [];
    isAdminOrPM = true;

    // Error, Card
    @ViewChild('scrollErrorContainer') scrollErrorContainer!: ElementRef;
    @ViewChild('scrollCardContainer') scrollCardContainer!: ElementRef;
    errors: Error[] = [];
    errorsFormArray: FormArray = this.formBuilder.array([]);
    addingError = false;
    editingError = false;
    errorDeleteIndex: number;
    backupError: Error;

    errorLevelOptions: { label: string; value: number }[] = [
        { label: '1', value: 1 },
        { label: '2', value: 2 },
        { label: '3', value: 3 },
        { label: '4', value: 4 },
    ];

    cards: Card[] = [];
    cardDeleteIndex: number;
    cardsFormArray: FormArray = this.formBuilder.array([]);
    addingCard = false;
    editingCard = false;
    backupCard: Card;

    urlError: string;

    typesAccept = ["excel"]

    constructor(
        public router: Router,
        public dradropService: DragDropService,
        private formBuilder: FormBuilder,
        private contractService: ContractService,
        private area: AreaService,
        private district: DistrictService,
        private user: BaseUserService,
        private loadingService: LoadingService,
        private cardService: CardService,
        private errorService: ErrorSmartQCService,
        private fileService: FileService,
        private alertService: AlertService,
    ) {
        this.isAdminOrPM = this.authService.isAdminOrPM();
        this.area.getAll().subscribe((data) => {
            this.areaList = data.filter((d) => d.id !== 100 && d.id !== 99);
        });

        this.district.getAll().subscribe((data) => {
            this.districtList = data;
            this.districtListFilter = data;
        });

        this.user.getAllQcUser('ROLE_QC_SUBPM').subscribe((data) => {
            this.userList = data.map((d) => ({ ...d, displayName: d.fullName + ' - ' + d.email }));
        });
    }

    ngOnInit(): void {
        this.actionForm = this.formBuilder.group({
            name: [null, [Validators.required, Validators.maxLength(255), trimValidator()]],
            templateId: [null, Validators.required],
            description: [null, trimValidator()],
        });
    }

    trimName(control) {
        if (typeof control.value === 'string') {
            control.control.setValue(control.value.trim());
        }
    }

    dropAction(event) {
        this.dradropService.dropElement(event, this.contract.actions, () => {
            this.setActionPosition();
        });
    }

    setActionPosition = () => {
        this.contract.actions.forEach((item, index) => {
            item.position = index + 1;
        });
    };

    selectAction(action) {
        this.selectedAction = action;
    }

    openEditAction(action, isNewAction) {
        this.actionForm.reset();
        this.actionForm.markAsUntouched();

        if (!isNewAction) {
            this.actionForm.patchValue(action);
        }
        this.visibleActionForm = true;
        this.isNewAction = isNewAction;
    }

    onSelectTemplate(value) {
        this.actionForm.patchValue({
            templateId: value,
        });
    }

    deleteAction(action) {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận xóa công việc',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.contract.actions = this.contract.actions.filter((a) => a !== action);
                if (this.contract.actions.length > 0) {
                    this.selectedAction = this.contract.actions[this.contract.actions.length - 1];
                } else {
                    this.selectedAction = { templateId: null, tasks: [] };
                }
                this.setActionPosition();
            },
        });
    }

    setActionTemplate = (e) => {
        this.actionForm.patchValue({ templateId: e?.id });
    };

    saveAction() {
        if (!this.actionForm.valid) return;

        if (this.isNewAction) {
            if (this.contract.actions.some((a) => a.name === this.actionForm.getRawValue().name)) {
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'error',
                    summary: 'Lỗi thêm công việc',
                    detail: 'Công việc cùng tên đã tồn tại trong dự án',
                });
                return;
            }

            this.contract.actions.push({ ...this.selectedAction, ...this.actionForm.getRawValue(), tasks: [] });
            this.selectedAction = this.contract.actions[this.contract.actions.length - 1];
        } else {
            this.contract.actions = this.contract.actions.map((a) => {
                if (this.selectedAction === a) {
                    this.selectedAction = { ...this.selectedAction, ...this.actionForm.getRawValue(), tasks: a.tasks };
                    return this.selectedAction;
                }
                return a;
            });
        }
        this.visibleActionForm = false;
        this.setActionPosition();
    }

    setTask = (tasks) => {
        this.selectedAction.tasks = tasks;
    };

    @ViewChild('startTime') startTimeModel: Calendar;
    @ViewChild('endTime') endTimeModel: Calendar;

    saveContract = (contractForm) => {
        if (
            !contractForm.valid ||
            this.contract.name?.length > 255 ||
            this.startTimeModel?.value?.length ||
            this.endTimeModel?.value?.length ||
            this.startTimeModel?.value?.getTime() > this.endTimeModel?.value?.getTime()
        ) {
            return;
        }

        const body = _.cloneDeep(this.contract);

        body.startTime = body.startTime instanceof Date ? body.startTime.getTime() : body.startTime;
        body.endTime = body.endTime instanceof Date ? body.endTime.getTime() : body.endTime;

        body.actions.forEach((a) => {
            a.contractId = body.id;
            a.tasks.forEach((task) => {
                task.startTime = task.startTime instanceof Date ? task.startTime.getTime() : task.startTime;
                task.endTime = task.endTime instanceof Date ? task.endTime.getTime() : task.endTime;
                task.templateId = a.templateId;
            });
        });

        // card, error
        body.errors = this.errorsFormArray.value;
        body.cards = this.cardsFormArray.value;

        this.contractService.createOne(body).subscribe((data) => {
            this.router.navigate([`/sqc/contract/${data.id}/edit`]);
            this.messageService.add({
                key: 'app-alert',
                severity: 'success',
                summary: 'Thành công',
                detail: 'Tạo dự án thành công',
            });
        });
    };

    handleSelectFile(file: File) {
        this.loadingService.show();
        this.contractService
            .importError(file, null)
            .subscribe({
                next: (res: ApiResponse) => {
                    if (res.code === 1) {
                        this.errors = res.data['errors'] as Error[];
                        this.cards = res.data['cards'] as Card[];
                        this.alertService.success('Thành công');
                        this.urlError = null;

                        this.initFormError();
                        this.initFormCard();
                    } else {
                        this.urlError = res.message;
                    }
                    this.loadingService.hide();
                },
                error: (e) => {
                    this.loadingService.hide();
                    this.alertService.handleError(e);
                },
            });
    }

    initFormError() {
        this.errorsFormArray = this.formBuilder.array(
            this.errors.map(error =>
                this.formBuilder.group({
                    id: [error.id],
                    name: [error.name, [Validators.required]],
                    level: [error.level, [Validators.required]],
                    isEdit: [false]
                })
            )
        );
    }

    initFormCard() {
        this.cardsFormArray = this.formBuilder.array(
            this.cards.map(card =>
                this.formBuilder.group({
                    id: [card.id],
                    name: [card.name, [Validators.required]],
                    isEdit: [false]
                })
            )
        );
    }

    handleClearFile() {

    }

    handleDownloadFile() {
        this.fileService.downLoadSampleFileByService('template_error_card.xlsx', '/smart-qc/api');
    }

    removeError(index: number) {
        this.errorDeleteIndex = index;
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận xoá phân loại lỗi',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.confirmDeleteError();
            },
        });
    }

    addError(): void {
        const newItem = this.formBuilder.group({
            name: [null, [Validators.required]],
            level: [null, [Validators.required]],
            contractId: [this.contract.id],
            isEdit: [true]
        });
        this.errorsFormArray.push(newItem);
        this.addingError = true;
        this.scrollToBottomError();
    }

    editError(index: number) {
        const control = this.errorsFormArray.at(index) as FormGroup;
        this.backupError = control.getRawValue();
        control.get("isEdit")?.setValue(true);
        this.editingError = true;
    }

    saveError(index: number) {
        const control = this.errorsFormArray.at(index) as FormGroup;
        this.markAllAsTouched(control);
        if (control.valid) {
            if (this.editingError) {
                // Edit error
                const newName = control.get('name')?.value;
                const newLevel = control.get('level')?.value;

                const isDuplicate = this.errorsFormArray.controls.some(((control, errorIndex) =>
                        index !== errorIndex && control.get('name')?.value === newName && control.get('level')?.value === newLevel
                ));

                if (isDuplicate) {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'error',
                        summary: 'Lỗi',
                        detail: 'Phân loại lỗi – Mức độ đã tồn tại',
                    });
                    return; // Không thêm nếu đã tồn tại
                }
                control.get("isEdit")?.setValue(false);
                this.editingError = false;
            } else {
                // Add new error
                const newName = control.get('name')?.value;
                const newLevel = control.get('level')?.value;

                const isDuplicate = this.errorsFormArray.controls.some(((control, errorIndex) =>
                        index !== errorIndex && control.get('name')?.value === newName && control.get('level')?.value === newLevel
                ));

                if (isDuplicate) {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'error',
                        summary: 'Lỗi',
                        detail: 'Phân loại lỗi – Mức độ đã tồn tại',
                    });
                    return; // Không thêm nếu đã tồn tại
                }

                this.scrollToBottomError();
                control.get("isEdit")?.setValue(false);
                this.addingError = false;
                setTimeout(() => {
                    this.scrollToBottomError();
                }, 200);
            }
        }
    }

    cancelEditError(index: number) {
        if (this.editingError) {
            const control = this.errorsFormArray.at(index) as FormGroup;
            control.patchValue(this.backupError);
            this.editingError = false;
        } else {
            this.errorsFormArray.removeAt(index);
            this.addingError = false;
        }
    }

    confirmDeleteError() {
        this.errorsFormArray.removeAt(this.errorDeleteIndex);
    }


    scrollToBottomError(): void {
        if (this.scrollErrorContainer) {
            const container = this.scrollErrorContainer.nativeElement;
            container.scrollTop = container.scrollHeight;
        }
    }

    // Card

    removeCard(index: number) {
        this.cardDeleteIndex = index;
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận xoá card',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.confirmDeleteCard();
            },
        });
    }

    addCard(): void {
        const newItem = this.formBuilder.group({
            name: [null, [Validators.required]],
            isEdit: [true],
        });
        this.cardsFormArray.push(newItem);
        this.addingCard = true;
        this.scrollToBottomCard();
    }

    editCard(index: number) {
        const control = this.cardsFormArray.at(index) as FormGroup;
        this.backupCard = control.getRawValue();
        control.get("isEdit")?.setValue(true);
        this.editingCard = true;
    }

    saveCard(index: number) {
        const control = this.cardsFormArray.at(index) as FormGroup;
        this.markAllAsTouched(control);
        if (control.valid) {
            if (this.editingCard) {
                // Edit error
                const newName = control.get('name')?.value;
                const isDuplicate = this.cardsFormArray.controls.some((cardControl, cardIndex) =>
                    index !== cardIndex && cardControl.get('name')?.value === newName
                );
                if (isDuplicate) {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'error',
                        summary: 'Lỗi',
                        detail: 'Tên card đã tồn tại',
                    });
                    return;
                }
                control.get("isEdit")?.setValue(false);
                this.editingCard = false;
            } else {
                // Add new error
                const newName = control.get('name')?.value;
                const isDuplicate = this.cardsFormArray.controls.some((cardControl, cardIndex) =>
                    index !== cardIndex && cardControl.get('name')?.value === newName
                );
                if (isDuplicate) {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'error',
                        summary: 'Lỗi',
                        detail: 'Tên card đã tồn tại',
                    });
                    return;
                }
                control.get("isEdit")?.setValue(false);
                this.addingCard = false;
                setTimeout(() => {
                    this.scrollToBottomCard();
                }, 200);
            }
        }
    }

    cancelEditCard(index: number) {
        if (this.editingCard) {
            const control = this.cardsFormArray.at(index) as FormGroup;
            control.patchValue(this.backupCard);
            this.editingCard = false;
        } else {
            this.cardsFormArray.removeAt(index);
            this.addingCard = false;
        }
    }


    callApiUpdateCard(control: FormGroup) {
        const card = control.getRawValue();
        this.loadingService.show();
        this.cardService.update(card)
            .pipe(
                finalize(() => {
                    this.loadingService.hide();
                }),
            )
            .subscribe({
                next: () => {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'success',
                        summary: 'Thành công',
                        detail: 'Cập nhật card thành công',
                    });
                    control.get("isEdit")?.setValue(false);
                    this.editingCard = false;
                },
                error: () => {},
                complete: () => {},
            });
    }

    confirmDeleteCard() {
        this.cardsFormArray.removeAt(this.cardDeleteIndex);
    }


    scrollToBottomCard(): void {
        if (this.scrollCardContainer) {
            const container = this.scrollCardContainer.nativeElement;
            container.scrollTop = container.scrollHeight;
        }
    }

    markAllAsTouched(form: FormGroup) {
        Object.keys(form.controls).forEach((field) => {
            const control = form.get(field);
            control?.markAsTouched({ onlySelf: true });
        });
    }
}
