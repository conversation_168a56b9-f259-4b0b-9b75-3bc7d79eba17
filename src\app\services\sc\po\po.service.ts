import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { InformationRetrieval, Po, PoReportReq } from '../../../models/interface/sc';
import { ApiResponse, GeneralEntity } from 'src/app/models/interface';
import { ParamsTable } from 'src/app/shared/table-module/table.common.service';
import { Observable } from 'rxjs';

@Injectable()
export class PoService extends BaseService<Po> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/po');
    }

    importBOQ(file: File, id: number) {
        const formData: FormData = new FormData();
        formData.append('file', file);
        if (id) {
            formData.append('poId', id.toString());
        }

        return this.http.post<ApiResponse>('/sc/api/po/import-boq', formData);
    }

    exportExcel(cols: string[], params: ParamsTable) {
        if (!params.rsql || params.rsql.trim().length === 0) {
            params.rsql = 'state=in=(0,1,2,3)';
        } else {
            params.rsql += ';state=in=(0,1,2,3)';
        }
        return this.http.post(`/sc/api/po/export-excel?query=${params.rsql}`, cols, { responseType: 'blob' });
    }

    getPoStatus(id: number) {
        return this.http.get<number>(`${this.baseUrl}/get-po-status/` + id, {
            observe: 'response',
        });
    }

    override getPageTableCustom({ native = '', pageable = '&page=0&size=10', rsql = '' }: ParamsTable) {
        if (!rsql || rsql.trim().length === 0) {
            rsql = 'state=in=(0,1,2,3)';
        } else {
            rsql += ';state=in=(0,1,2,3)';
        }
        return this.http.get<Po[]>(`${this.baseUrl}/search?query=${rsql}${native}${pageable}`, {
            observe: 'response',
        });
    }

    getPageInformationRetrieval({ pageable = '&page=0&size=10' }: ParamsTable, body: unknown) {
        return this.http.post<InformationRetrieval[]>(`${this.baseUrl}/information-retrieval?${pageable}`, body, {
            observe: 'response',
        });
    }

    getMaterialTableData({ native = '', pageable = '&page=0&size=10', rsql = '' }: ParamsTable, body: PoReportReq) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        return this.http.post<any>(`${this.baseUrl}/report-po?query=${rsql}${native}${pageable}`, body, {
            observe: 'response',
        });
    }

    exportInformationRetrieval(body: unknown): Observable<Blob> {
        return this.http.post(`${this.baseUrl}/export-information-retrieval`, body, {
            responseType: 'blob',
        });
    }

    exportReportPo(body: unknown) {
        return this.http.post<GeneralEntity>(`${this.baseUrl}/export-report-po`, body);
    }

    getTotalAmount(rsql, pageable) {
        if (!rsql || rsql.trim().length === 0) {
            rsql = 'state=in=(0,1,2,3)';
        } else {
            rsql += ';state=in=(0,1,2,3)';
        }
        return this.http.get(`${this.baseUrl}/total-amount?query=${rsql}${pageable}`);
    }

    updateState(body: { poId: number; state: number }) {
        return this.http.post<void>(`/sc/api/po/update-state`, body);
    }
}
