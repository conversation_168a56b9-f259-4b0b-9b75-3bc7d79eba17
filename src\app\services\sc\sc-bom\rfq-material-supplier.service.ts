import { Injectable } from '@angular/core';
import {HttpClient, HttpResponse} from '@angular/common/http';
import { BaseService } from '../../base.service';
import {MaterialSupplier} from 'src/app/models/interface/sc';
import {GeneralEntity} from "../../../models/interface";
import {Observable} from "rxjs";

@Injectable()
export class RfqMaterialSupplierService extends BaseService<MaterialSupplier> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/material-supplier');
    }

    // export = (rfqId: number, supplierIds: number[]) => {
    //     return this.http.post(`/sc/api/material-supplier/export?rfqId=` + rfqId, supplierIds, { responseType: 'blob' });
    // };

export = (rfqId: number, supplierIds: number[]): Observable<HttpResponse<Blob>> => {
    return this.http.post<Blob>(`/sc/api/material-supplier/export?rfqId=${rfqId}`, supplierIds, {
        responseType: 'blob' as 'json',
        observe: 'response' as const
    });
};

}
