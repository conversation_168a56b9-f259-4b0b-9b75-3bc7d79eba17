import { Component, EventEmitter, Input, OnChanges, OnInit, Output, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PanelModule } from 'primeng/panel';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { TableModule } from 'primeng/table';
import { FormCustomModule } from '../../../../../../shared/form-module/form.custom.module';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { InputNumberModule } from 'primeng/inputnumber';
import { CalendarModule } from 'primeng/calendar';
import { DropdownModule } from 'primeng/dropdown';
import { CheckboxModule } from 'primeng/checkbox';
import { ButtonModule } from 'primeng/button';
import { LogisticsAgent, Lot } from '../../../../../../models/interface/sc';

import { DialogModule } from 'primeng/dialog';
import { ButtonGroupFileComponent } from '../../../../../../shared/components/button-group-file/button-group-file.component';
import { ApiResponse, Column, User } from 'src/app/models/interface';
import { FormArray, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { LotService } from 'src/app/services/sc/lot/lot.service';
import { AbstractControlCustom, FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { ShippingMethodService } from 'src/app/services/sc/bo/shipping-method.service';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { cloneDeep, debounce } from 'lodash';
import { LogisticsAgentService } from 'src/app/services/sc/logistics/logistics-agent.service';
import { ConfirmationService } from 'primeng/api';
import { FormArrayCustom } from 'src/app/shared/form-module/from-array.custom';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';

@Component({
    selector: 'app-lot-state-one',
    standalone: true,
    templateUrl: './state-one.component.html',
    styleUrls: ['./state-one.component.scss'],
    imports: [
        CommonModule,
        PanelModule,
        InputTextModule,
        InputTextareaModule,
        TableModule,
        TableCommonModule,
        FormCustomModule,
        ReactiveFormsModule,
        OverlayPanelModule,
        InputNumberModule,
        CalendarModule,
        FormCustomModule,
        DropdownModule,
        CheckboxModule,
        ButtonModule,
        DialogModule,
        ButtonGroupFileComponent,
        HasAnyAuthorityDirective,
    ],
    providers: [LotService, ShippingMethodService, LogisticsAgentService],
})
export class StateOneComponent implements OnInit, OnChanges {
    @Input() lot: Lot;
    @Input() receivers: User[];
    formGroup: FormGroup;
    formGroupAgents: FormGroup;
    formGroupSubmit: FormGroup;
    mapTypeShippingMethod = {};
    visible: boolean = false;
    @ViewChild('formSubmit', { static: false }) formSubmit: FormComponent;

    visibleSubmit: boolean = false;
    // choose column
    @Output('onComplete') onComplete: EventEmitter<Lot> = new EventEmitter();

    // agent
    isAddingAgent: boolean = false;
    isEditingAgent: boolean = false;
    backUpAgent: LogisticsAgent;
    optionAgent: LogisticsAgent[] = [];
    constructor(
        private lotService: LotService,
        private fb: FormBuilder,
        private shippingMethodService: ShippingMethodService,
        private loadingService: LoadingService,
        private alertService: AlertService,
        private logisticsAgentService: LogisticsAgentService,
        private confirmationService: ConfirmationService,
    ) {}
    ngOnInit(): void {
        this.fetchShippingMethod();
        this.logisticsAgentService.getPage(`query=logisticId==${this.lot?.logisticForwarderId}&page=0&size=1000&sort=id,desc`).subscribe({
            next: (res) => {
                this.optionAgent = res.body;
            },
        });
    }

    fetchShippingMethod() {
        this.shippingMethodService.getPage('query=&page=0&size=100&sort=id,desc').subscribe({
            next: (res) => {
                res.body.forEach((item) => {
                    this.mapTypeShippingMethod[item.id] = item.name;
                });
            },
        });
    }

    ngOnChanges(): void {
        this.initForm();
    }

    initForm() {
        this.formGroup = new FormGroupCustom(this.fb, {
            salesAgentId: [this.lot?.salesAgentId, Validators.required],
            note: [this.lot?.note],
            lotAttachmentIds: [this.lot?.lotAttachmentIds],
            lotAttachments: [this.lot?.lotAttachments],
            noteLot: [this.lot?.noteLot],
        });
        this.initFormAgentFunc();
        if (this.lot?.estimatedTransportCost) {
            const shippingChoice = this.lot.boShippingMethods.find(
                (shipping) =>
                    shipping.shippingMethodId === this.lot?.estimatedTransportCost.shippingMethodId &&
                    shipping.logisticFwdId === this.lot?.estimatedTransportCost.logisticId,
            );

            this.lot.estimatedTransportCost['logisticShortName'] = shippingChoice?.logisticsFwd?.shortName;
            this.lot.estimatedTransportCost['roadNote'] = shippingChoice?.roadNote;

            if (this.lot?.estimatedSchedule) {
                this.lot.estimatedSchedule['logisticShortName'] = shippingChoice.logisticsFwd?.shortName;
                this.lot.estimatedSchedule['roadNote'] = shippingChoice?.roadNote;
            }
        }

        if (this.lot?.estimatedInsurance) {
            const shippingChoice = this.lot.boShippingMethods.find(
                (shipping) =>
                    shipping.shippingMethodId === this.lot?.estimatedInsurance.shippingMethodId &&
                    shipping.logisticInsuranceId === this.lot?.estimatedInsurance.logisticId,
            );

            this.lot.estimatedInsurance['logisticShortName'] = shippingChoice?.logisticsInsurance?.shortName;
            this.lot.estimatedInsurance['roadNote'] = shippingChoice?.roadNote;
        }

        this.formGroupSubmit = new FormGroupCustom(this.fb, {
            receiverIds: [null, Validators.required],
            content: [null],
        });

        this.formGroup.valueChanges.subscribe(() => {
            this.debouncedUpdateStateOne();
        });
    }

    initFormAgentFunc() {
        this.formGroupAgents = new FormGroupCustom(this.fb, {
            logisticsAgents: new FormArrayCustom(this.initFormAgent([])),
        });
    }
    handleUploadFile(files: File[]) {
        this.loadingService.show();
        this.lotService.importFile(files).subscribe({
            next: (res: ApiResponse) => {
                const itemValue = this.formGroup.getRawValue() as Lot;
                const attachmentIds = itemValue?.lotAttachmentIds ?? [];
                const attachments = itemValue?.lotAttachments ?? [];
                if (res.code === 1) {
                    // Nối mảng cũ và mảng mới
                    const newAttachmentIds = [...attachmentIds, ...(res.data['attachmentIds'] || [])];
                    const newAttachments = [...attachments, ...(res.data['attachments'] || [])];

                    this.formGroup.patchValue({
                        lotAttachmentIds: newAttachmentIds,
                        lotAttachments: newAttachments,
                    });
                } else {
                    this.alertService.error('Có lỗi xảy ra khi lưu file');
                    this.formGroup.patchValue({ lotAttachmentIds: attachmentIds, lotAttachments: attachments });
                }
                this.loadingService.hide();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    showPopupSubmit() {
        if (this.formGroup.invalid) return;
        this.visibleSubmit = true;
    }

    handleChangeReceivers(event) {
        if (event.value !== null && event.objects !== null && event.objects.length > 0) {
            const ids = event.objects.map((obj) => obj.id);
            this.formGroupSubmit.patchValue({
                receiverIds: ids,
            });
        } else {
            this.formGroupSubmit.patchValue({
                receiverIds: null,
            });
        }
    }

    debouncedUpdateStateOne = debounce(() => {
        this.onSubmitStateOne();
    }, 500);

    onSubmitStateOne() {
        this.lotService.stateOneUpdate({ ...this.lot, ...this.formGroup.getRawValue() }).subscribe({
            next: (res) => {
                this.onComplete.emit(res);
            },
            error: () => {},
        });
    }

    sendNotification(value: { receiverIds: number[]; content: string }) {
        this.loadingService.show();
        this.lotService.sendNotification(this.lot.id, value).subscribe({
            next: () => {
                this.visibleSubmit = false;
                this.loadingService.hide();
                this.alertService.success('Thành công');
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    initFormAgent(items: LogisticsAgent[]): FormGroup[] {
        return items.map(
            (item) =>
                new FormGroupCustom(this.fb, {
                    id: [item?.id],
                    logisticId: [this.lot.logisticForwarderId],
                    created: [item?.created],
                    updated: [item?.updated],
                    createdBy: [item?.createdBy],
                    updatedBy: [item?.updatedBy],
                    tenantId: [item?.tenantId],
                    active: [item?.active],
                    name: [item?.name, Validators.required],
                    address: [item?.address, Validators.required],
                    isEdit: [false],
                }),
        );
    }

    get agents(): FormArray {
        return this.formGroupAgents.get('logisticsAgents') as FormArray;
    }

    addItem(): void {
        const newItem = new FormGroupCustom(this.fb, {
            id: [null],
            created: [null],
            updated: [null],
            createdBy: [null],
            updatedBy: [null],
            tenantId: [null],
            active: [null],
            logisticId: [this.lot.logisticForwarderId],
            name: [null, Validators.required],
            address: [null, Validators.required],
            isEdit: [true],
        });
        this.agents.push(newItem);
        this.isAddingAgent = true;
    }

    editItem(index: number): void {
        const item = this.agents.at(index);
        this.backUpAgent = item ? cloneDeep(item.getRawValue()) : null;
        item?.patchValue({ isEdit: true });
        this.isEditingAgent = true;
    }
    saveItem(index: number): void {
        const item = this.agents.at(index) as AbstractControlCustom;
        const itemValue = item.getRawValue() as LogisticsAgent;
        item.isSubmited = true;
        if (item.invalid) return;

        this.loadingService.show();
        this.logisticsAgentService.create(itemValue).subscribe({
            next: (res) => {
                this.loadingService.hide();
                item.patchValue({ ...res.body, isEdit: false });
                this.alertService.success('Thành công');
                this.isEditingAgent = false;
                this.isAddingAgent = false;
                this.optionAgent = [res.body, ...this.optionAgent];
                this.formGroup.patchValue({ salesAgentId: res.body.id });
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    cancelCreate(index: number): void {
        const item = this.agents.at(index);
        if (this.isAddingAgent) {
            this.agents.removeAt(index);
        } else if (this.isEditingAgent) {
            item?.patchValue(this.backUpAgent);
        }
    }

    deleteItem(index: number): void {
        const item = this.agents.at(index);
        const itemValue = item.getRawValue();
        console.log('delete: ', itemValue);

        if (item) {
            this.confirmationService.confirm({
                key: 'app-confirm',
                header: 'Xác nhận xóa',
                message: 'Bạn có chắc chắn muốn xóa?',
                icon: 'pi pi-exclamation-triangle',
                accept: () => {
                    this.loadingService.show();
                    this.logisticsAgentService.delete(itemValue.id).subscribe({
                        next: () => {
                            if (itemValue.id === this.formGroup.get('salesAgentId')) {
                                this.formGroup.patchValue({ salesAgentId: itemValue.id });
                            }
                            this.loadingService.hide();
                            this.agents.removeAt(index);
                            this.alertService.success('Xóa thành công');
                        },
                        error: () => {
                            this.loadingService.hide();
                        },
                    });
                },
            });
        }
    }

    columns: Column[] = [
        {
            header: 'NCC dịch vụ',
            field: 'logisticShortName',
            default: true,
            hide: false,
        },
        {
            header: 'Phương thức vận chuyển',
            field: 'shippingMethodId',
            default: true,
            hide: false,
        },
        {
            header: 'Tỷ giá VND/USD',
            field: 'exchangeRate', // Đổi từ 'manufacturer' thành 'exchangeRate'
            hide: false,
        },
        {
            header: 'Phí chứng từ đầu xuất',
            field: 'documentFee',
            hide: false,
        },
        {
            header: 'Phí hải quan xuất khẩu',
            field: 'exportCustomsFee',
            hide: false,
        },
        {
            header: 'Phí bến bãi hàng truck qua cửa khẩu',
            field: 'borderTruckYardFee',
            hide: false,
        },
        {
            header: 'Phí lưu ca xe đầu xuất',
            field: 'exportTruckStorageFee',
            hide: false,
        },
        {
            header: 'Phí EXW đầu xuất',
            field: 'exwFee',
            hide: false,
        },
        {
            header: 'Phí FCA đầu xuất',
            field: 'fcaFee',
            hide: false,
        },
        {
            header: 'Phí local charge đầu xuất',
            field: 'exportLocalCharge',
            hide: false,
        },
        {
            header: 'Phí giấy phép xuất khẩu',
            field: 'exportLicenseFee',
            hide: false,
        },
        {
            header: 'Phí kiểm tra từ tính',
            field: 'magneticInspectionFee',
            hide: false,
        },
        {
            header: 'Phí đầu xuất phát sinh khác (Nếu có)',
            field: 'otherExportFees',
            hide: false,
        },
        {
            header: 'Phí trucking từ kho nhà máy đến cửa khẩu/cảng biển/cảng hàng không',
            field: 'factoryToPortFee',
            hide: false,
        },
        {
            header: 'Phí nâng hạ bốc xếp tại cảng nhập (Chi hộ)',
            field: 'importPortHandlingFee',
            hide: false,
        },
        {
            header: 'Cước hàng không/biển/bộ chuyển phát nhanh',
            field: 'mainTransportCost',
            hide: false,
        },
        {
            header: 'Phí local charge tại cảng nhập',
            field: 'importLocalCharge',
            hide: false,
        },
        {
            header: 'Phí bến bãi biên phòng kiểm dịch',
            field: 'borderInspectionFee',
            hide: false,
        },
        {
            header: 'Phí cầu cảng',
            field: 'wharfFee',
            hide: false,
        },
        {
            header: 'Phí hải quan giám sát tại cửa khẩu',
            field: 'supervisionFee',
            hide: false,
        },
        {
            header: 'Phí dịch vụ hải quan tại cảng nhập',
            field: 'serviceFee',
            hide: false,
        },
        {
            header: 'Phí vận chuyển nội địa từ cảng nhập đến địa điểm nhận hàng cuối cùng',
            field: 'domesticTransportFee',
            hide: false,
        },
        {
            header: 'Phí phát sinh khác tại cảng nhập (Nếu có)',
            field: 'otherImportFees',
            hide: false,
        },
        {
            header: 'Phí bốc xếp tại kho (Nếu có)',
            field: 'warehouseHandlingFee',
            hide: false,
        },
        {
            header: 'Tổng chi phí vận chuyển dự kiến',
            field: 'totalEstimatedTransportCost',
            hide: false,
        },
        {
            header: 'Tổng chi phí vận chuyển dự kiến sau khi trừ phân phí NCC chịu (Nếu có)',
            field: 'totalCostAfterSupplierDeduction',
            hide: false,
        },
        {
            header: 'Đơn giá tính quốc tế',
            field: 'internationalPrice',
            hide: false,
        },
        {
            header: 'Đơn vị tính cước quốc tế (CBM/CONT/KG)',
            field: 'internationalRateUnitId',
            hide: false,
        },
    ];
    columnChoose = [];
    setColumnSelection(selectedColumns: Column[]) {
        if (selectedColumns.length === 0) {
            this.columns.forEach((c) => {
                if (!c.default) {
                    c.hide = true;
                }
            });
            return;
        } else {
            this.columns.forEach((c) => {
                if (!c.default) {
                    if (selectedColumns.some((s) => c.field === s.field)) {
                        c.hide = false; // Show the column
                    } else {
                        c.hide = true; // Hide the column
                    }
                }
            });
        }
    }

    closePopupAgents() {
        this.visible = false;
        this.isAddingAgent = false;
        this.formGroupAgents = new FormGroupCustom(this.fb, {
            logisticsAgents: new FormArrayCustom(this.initFormAgent([])),
        });
    }
}
