<ng-container *ngIf="product">
    <div #container>
        <div class="tw-flex tw-justify-end mb-3">
            <button pButton type="button" class="p-button-outlined p-button-sm toggle-btn pi pi-arrows-alt" appFullscreenToggle [target]="container"></button>
        </div>

        <app-form [formGroup]="generalInfoForm" formId="generalInfo" (onSubmit)="onSubmitGeneralInfo()">
            <p-panel header="Thông tin chung" [toggleable]="true">
                <div class="tw-grid tw-grid-cols-3 tw-gap-6 tw-p-4">
                    <!-- Cột 1: Tên SOP / Ngày áp dụng -->
                    <div class="tw-flex tw-flex-col tw-space-y-4">
                        <app-form-item label="Tên SOP" noGrid>
                            <span class="tw-text-gray-700">{{ sopName }}</span>
                        </app-form-item>

                        <app-form-item label="Ngày áp dụng" noGrid>
                            <span class="tw-text-gray-700">{{ applyDate }}</span>
                        </app-form-item>
                    </div>

                    <!-- Cột 2: Trạng thái soát xét / Ngày soát xét / Người soát xét -->
                    <div class="tw-flex tw-flex-col tw-space-y-4">
                        <app-form-item label="Trạng thái soát xét" noGrid>
                            <p-tag value="Draft" severity="secondary"></p-tag>
                        </app-form-item>

                        <app-form-item label="Ngày soát xét" noGrid>
                            <span class="tw-text-gray-700">
                                {{ generalInfoForm.get('reviewDate')!.value | date: 'HH:mm dd/MM/yyyy' }}
                            </span>
                        </app-form-item>

                        <app-form-item label="Người soát xét" noGrid>
                            <p-multiSelect
                                formControlName="reviewers"
                                [options]="accountsCanReview"
                                optionLabel="label"
                                optionValue="id"
                                placeholder="Chọn account"
                            >
                            </p-multiSelect>
                        </app-form-item>
                    </div>

                    <!-- Cột 3: Trạng thái phê duyệt / Ngày phê duyệt / Người phê duyệt -->
                    <div class="tw-flex tw-flex-col tw-space-y-4">
                        <app-form-item label="Trạng thái phê duyệt" noGrid>
                            <p-tag value="Draft" severity="secondary"></p-tag>
                        </app-form-item>

                        <app-form-item label="Ngày phê duyệt" noGrid>
                            <span class="tw-text-gray-700">
                                {{ generalInfoForm.get('approveDate')!.value | date: 'HH:mm dd/MM/yyyy' }}
                            </span>
                        </app-form-item>

                        <app-form-item label="Người phê duyệt" noGrid>
                            <p-dropdown
                                formControlName="approver"
                                [options]="accountsCanReview"
                                optionLabel="label"
                                optionValue="id"
                                placeholder="Chọn account"
                            >
                            </p-dropdown>
                        </app-form-item>
                    </div>
                </div>
            </p-panel>
        </app-form>
        <hr class="my-6" />
        <app-form [formGroup]="sectionsForm" formId="sectionsForm" (onSubmit)="onSubmitSections()">
            <div formArrayName="sections" class="tw-mt-4">
                <ng-container *ngFor="let secCtrl of sections.controls; let i = index">
                    <div [formGroupName]="i">
                        <p-panel header="{{ secCtrl.get('code')?.value }}" [toggleable]="true" class="tw-mb-4">
                            <ng-template pTemplate="icons">
                                <button pButton type="button" icon="pi pi-file-excel" label="Xuất Excel" class="p-button-sm p-button-outlined tw-mr-2"></button>
                                <button pButton type="button" icon="pi pi-upload" label="Upload file" class="p-button-sm p-button-outlined"></button>
                            </ng-template>
                            <div formArrayName="entries">
                                <p-table [value]="secCtrl.get('entries').controls" styleClass="p-datatable-gridlines">
                                    <ng-template pTemplate="header">
                                        <tr>
                                            <th>STT</th>
                                            <th>OC</th>
                                            <th>Khối thao tác chính</th>
                                            <th>Operation Description</th>
                                            <th>Mức tập trung</th>
                                            <th>Không gian thao tác</th>
                                            <th>Tên thiết bị</th>
                                            <th>Qty</th>
                                            <th>Mã vật tư</th>
                                            <th>Tên vật tư</th>
                                            <th>Qty</th>
                                            <th>Chú ý tại trạm làm việc</th>
                                            <th>Chi tiết</th>
                                            <th>Thời gian thao tác cơ bản</th>
                                        </tr>
                                    </ng-template>
                                    <ng-template pTemplate="body" let-row let-rowIndex="rowIndex">
                                        <tr [formGroupName]="rowIndex">
                                            <td>{{ row.get('sequenceNumber')?.value }}</td>
                                            <td>{{ row.get('operationCode')?.value }}</td>
                                            <td>
                                                <app-form-item label="" validateTrigger="touched">
                                                    <input pInputText formControlName="mainOperationBlock" class="tw-w-full" maxlength="10" />
                                                </app-form-item>
                                            </td>
                                            <td>{{ row.get('operationDescription')?.value }}</td>
                                            <td>
                                                <app-form-item label="" validateTrigger="touched">
                                                    <input pInputText formControlName="focusLevel" class="tw-w-full" maxlength="3" />
                                                </app-form-item>
                                            </td>
                                            <td>
                                                <app-form-item label="" validateTrigger="touched">
                                                    <input pInputText formControlName="workArea" class="tw-w-full" maxlength="10" />
                                                </app-form-item>
                                            </td>
                                            <td>{{ row.get('equipmentName')?.value }}</td>
                                            <td>{{ row.get('equipmentQty')?.value }}</td>
                                            <td>{{ row.get('materialCode')?.value }}</td>
                                            <td>{{ row.get('materialName')?.value }}</td>
                                            <td>{{ row.get('materialQty')?.value }}</td>
                                            <td>
                                                <app-form-item label="" validateTrigger="touched">
                                                    <input pInputText formControlName="workStationNotes" class="tw-w-full" maxlength="500" />
                                                </app-form-item>
                                            </td>
                                            <td>
                                                <app-form-item label="">
                                                    <app-upload-custom
                                                        #RowUploader
                                                        [loading]="loadingRows.get(row.get('id')?.value)"
                                                        [filePath]="row.get('details')?.value"
                                                        [limit]="1"
                                                        (onChange)="onFileSelected($event, row.get('id')?.value)"
                                                        (onClear)="onClearFile(row.get('id')?.value)"
                                                    ></app-upload-custom>
                                                </app-form-item>
                                            </td>
                                            <td>{{ row.get('basicOperationTime')?.value }}</td>
                                        </tr>
                                    </ng-template>
                                </p-table>
                            </div>
                        </p-panel>
                    </div>
                </ng-container>
            </div>
            <app-tab-action-buttons
                [form]="sectionsForm"
                [mode]="mode"
                [status]="version.status"
                [canSubmitStatuses]="[1, 4]"
                [commonDisableSubmitStatuses]="[2, 3]"
                [canApproveStatuses]="[2, 8]"
                [isSaving]="isBusy$"
            ></app-tab-action-buttons>
        </app-form>
    </div>
</ng-container>
