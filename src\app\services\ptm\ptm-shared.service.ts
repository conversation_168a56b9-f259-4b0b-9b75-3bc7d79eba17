import { inject, Injectable } from '@angular/core';
import { catchError, Observable, throwError } from 'rxjs';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { SendApprovalRequest, ApprovalRequest } from 'src/app/models/interface/ptm';

@Injectable({
    providedIn: 'root',
})
export class PtmSharedService {
    path = '/pr/api/production-instruction';
    #http = inject(HttpClient);

    getTrackChanges(idInstruction: number, param: any): Observable<any> {
        const url = `/pr/api/history/production-instruction/${idInstruction}`;
        return this.#http.get<any>(url, { params: param });
    }

    preview(formData: any, id: number) {
        const url = `/pr/api/approval/production-instruction/${id}`;
        return this.#http.post(url, formData).pipe(catchError(this.handleError));
    }

    confirm(formData: any, id: number) {
        const url = `/pr/api/approval/production-instruction/${id}/confirm`;
        return this.#http.post(url, formData).pipe(catchError(this.handleError));
    }

    getListOptionEquip(): Observable<any> {
        const url = `/pr/api/equipment/active`;
        return this.#http.get<any>(url);
    }

    getListOptionConsumable(): Observable<any> {
        const url = `/pr/api/sys_part`;
        return this.#http.get<any>(url);
    }

    sendApprovalRequest(payload: SendApprovalRequest): Observable<any> {
        return this.#http.post<any>(`/pr/api/approval/product-instruction`, payload);
    }
    confirmApprovalRequest(payload: ApprovalRequest): Observable<any> {
        return this.#http.post<any>(`/pr/api/approval/product-instruction/confirm`, payload);
    }

    private handleError(error: HttpErrorResponse) {
        // có thể tuỳ chỉnh thông báo, logging, …
        console.error('API error', error);
        return throwError(() => error);
    }
}
