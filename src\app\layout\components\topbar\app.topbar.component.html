<div
    class="tw-flex tw-w-full tw-justify-between tw-items-center tw-px-8"
    style="
        width: 100%;
        height: 3.5rem;
        background-color: #ffffff;
        box-shadow:
            0 3px 6px rgba(0, 0, 0, 0.16),
            0 3px 6px rgba(0, 0, 0, 0.23);
    "
>
    <div class="md:tw-hidden tw-flex tw-items-center tw-gap-4 md:tw-w-32">
        <button #menubutton class="p-link" (click)="layoutService.changeSideBarState()">
            <i class="pi pi-bars" style="color: var(--primary-color-text)"></i>
        </button>
        <button class="text-white tw-inline-block md:tw-hidden p-link" (click)="platformTemplate.toggle($event)">
            <i class="material-icons md-24" style="color: var(--primary-color-text)">&#xE8F0;</i>
        </button>
    </div>
    <a class="" routerLink="/">
        <img src="assets/images/logo/vnpt-logo.png" alt="logo" width="156" height="30" />
    </a>
    <div class="md:tw-hidden tw-flex tw-gap-4 tw-items-center">
        <ng-container *ngTemplateOutlet="containerNotification"></ng-container>
        <button #topbarmenubutton class="p-link" (click)="op.toggle($event)">
            <i class="pi pi-ellipsis-v" style="color: var(--primary-color-text)"></i>
        </button>
    </div>

    <div #topbarmenu class="tw-gap-4 tw-hidden md:tw-flex">
        <button class="text-white md:tw-inline-block tw-hidden p-link" (click)="platformTemplate.toggle($event)">
            <i class="material-icons md-24" style="color: #366ba1">&#xE8F0;</i>
        </button>

        <ng-container *ngTemplateOutlet="containerNotification"></ng-container>

        <button class="p-link" (click)="op.toggle($event)" style="color: #366ba1">
            <!--            <span>{{ user?.email }}</span>-->
            <!--            <i class="pi pi-angle-down" [title]="user?.email"></i>-->
            <span class="material-icons">account_circle</span>
        </button>
        <div class="tw-inline-block md:tw-hidden tw-w-full">
            <ng-container *ngTemplateOutlet="popupProfile"></ng-container>
        </div>
    </div>
</div>

<ng-template #containerNotification>
    <div *ngIf="isActiveNotification" (click)="notificationTemplate.toggle($event); fetchNotificationDataOnClick()" class="tw-cursor-pointer">
        <i class="pi pi-bell p-text-secondary" style="padding-top: 2px; font-size: 20px; color: #366ba1">
            <ng-container *ngIf="notificationNewNumber > 0">
                <span pBadge [value]="String(notificationNewNumber)" severity="danger"></span>
            </ng-container>
        </i>
        <!-- <a class="tw-hidden md:tw-inline text-cyan-50">Thông báo</a> -->
    </div>
</ng-template>

<ng-template #popupProfile>
    <div class="flex flex-column gap-3 tw-w-full">
        <div class="logout-btn tw-whitespace-nowrap tw-font-bold">
            {{ this.user?.email }}
        </div>

        <div class="tw-inline-flex tw-justify-start tw-items-center">
            <button class="p-link tw-flex tw-space-x-2 tw-items-center tw-font-bold logout-btn tw-w-full" (click)="logout()" title="Thoát">
                <span>Đăng xuất</span>
                <i class="pi pi-sign-out"></i>
            </button>
        </div>
    </div>
</ng-template>

<p-overlayPanel #op>
    <div class="flex flex-column gap-3 tw-w-full">
        <div class="tw-whitespace-nowrap tw-font-bold">
            {{ this.user?.email }}
        </div>

        <div class="tw-inline-flex tw-justify-start tw-items-center">
            <button class="p-link tw-flex tw-space-x-2 tw-items-center tw-font-bold tw-w-full" (click)="logout()" title="Thoát">
                <span>Đăng xuất</span>
                <i class="pi pi-sign-out"></i>
            </button>
        </div>
    </div>
</p-overlayPanel>

<p-overlayPanel #notificationTemplate>
    <div class="tw-flex tw-justify-between tw-items-center tw-mb-3">
        <div class="tw-text-2xl tw-font-bold">Thông báo</div>
        <!--<div (click)="markAsReadAll()" class="tw-text-blue-500 tw-cursor-pointer">Đánh dấu tất cả đã đọc</div>-->
    </div>
    <div class="tw-flex tw-flex-col tw-space-y-1 notification">
        <div
            (click)="navigateToApprove(notification); notificationTemplate.hide()"
            style="border-radius: 5px; padding: 10px; cursor: pointer"
            *ngFor="let notification of notifications"
            [ngClass]="{
                'notification-item un-read': notification?.isRead === 0,
                'read notification-item': notification?.isRead === 1,
            }"
        >
            <div class="tw-text-lg">{{ notification?.contentWeb }}</div>
            <div class="tw-mt-2 tw-italic tw-text-sm">{{ Common.formatDate(notification?.created) }}</div>
        </div>
    </div>
    <div style="margin-top: 15px" (click)="navigateToNotification()" class="tw-p-2 tw-flex tw-justify-center tw-text-blue-600 tw-cursor-pointer">
        Xem tất cả
    </div>
</p-overlayPanel>

<p-overlayPanel #platformTemplate>
    <div class="md:tw-mt-5 tw-grid tw-gap-6 tw-p-3" style="max-height: calc(100vh - 5rem); overflow: auto">
        <!-- Card 1: Quản lý, điều hành -->
        <p-card
            *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'ROLE_DAC', 'ROLE_RND', 'ROLE_RND_PROJECT', 'ROLE_OKR', 'ROLE_INITIATIVE']"
            subheader="Quản lý, điều hành"
            styleClass="tw-row-span-1 tw-h-full"
        >
            <div>
                <div class="tw-grid tw-grid-cols-4 tw-text-center tw-gap-4 tw-mt-4" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'ROLE_DAC']">
                    <div>
                        <a class="tw-block" target="_blank" [href]="environmentState.HOST_BOS + 'dac'">
                            <img src="/assets/images/ui/dac_icon.png" class="tw-w-20 tw-h-20 tw-mx-auto hover:tw-scale-125 tw-transition tw-duration-300" />
                            <span class="tw-block tw-mt-2">Điều hành kinh doanh</span>
                        </a>
                    </div>
                    <div *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'ROLE_RND']">
                        <a class="tw-block" target="_blank" [href]="environmentState.HOST_BOS + 'management'">
                            <img src="/assets/images/ui/project2.png" class="tw-w-20 tw-h-20 tw-mx-auto hover:tw-scale-125 tw-transition tw-duration-300" />
                            <span class="tw-block tw-mt-2">Quản lý dự án <br />khoa học công nghệ</span>
                        </a>
                    </div>
                    <div *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'ROLE_RND_PROJECT']">
                        <a class="tw-block" target="_blank" [href]="environmentState.HOST_BOS + 'rnd'">
                            <img src="/assets/images/ui/rnd.png" class="tw-w-20 tw-h-20 tw-mx-auto hover:tw-scale-125 tw-transition tw-duration-300" />
                            <span class="tw-block tw-mt-2">Quản lý dự án <br />nghiên cứu phát triển</span>
                        </a>
                    </div>
                    <div *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'ROLE_OKR']">
                        <a class="tw-block" target="_blank" [href]="environmentState.HOST_BOS + 'objects'">
                            <img src="/assets/images/ui/okr.png" class="tw-w-20 tw-h-20 tw-mx-auto hover:tw-scale-125 tw-transition tw-duration-300" />
                            <span class="tw-block tw-mt-2">Quản trị mục tiêu kết quả</span>
                        </a>
                    </div>
                    <div *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'ROLE_INITIATIVE']">
                        <a class="tw-block" target="_blank" [href]="environmentState.HOST_BOS + 'initiative/home'">
                            <img
                                src="/assets/images/ui/initiativelogo.png"
                                class="tw-w-20 tw-h-20 tw-mx-auto hover:tw-scale-125 tw-transition tw-duration-300"
                            />
                            <span class="tw-block tw-mt-2">Sáng kiến sáng tạo</span>
                        </a>
                    </div>
                </div>
            </div>
        </p-card>

        <!-- Card 2: Nghiệp vụ (chiếm 2 hàng) -->
        <p-card
            *appHasAnyAuthority="[
                'ROLE_SYSTEM_ADMIN',
                'ROLE_MANUFACTURING',
                'ROLE_WARRANTY',
                'ROLE_SMARTQC',
                'ROLE_INVENTORY',
                'ROLE_MANUFACTURER_PLAN',
                'ROLE_SUPPLIER_CHAIN',
                'ROLE_PRODUCT_DOCUMENTATION',
            ]"
            subheader="Nghiệp vụ"
            styleClass="tw-row-span-2"
        >
            <div class="tw-grid tw-grid-cols-4 tw-text-center tw-gap-4 tw-mt-4">
                <div *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'ROLE_MANUFACTURER_PLAN']">
                    <a class="tw-block" target="_blank" [href]="environmentState.HOST_BOS + 'manufacturer-plan'">
                        <img src="/assets/images/ui/plan3.png" class="tw-w-20 tw-h-20 tw-mx-auto hover:tw-scale-125 tw-transition tw-duration-300" />
                        <span class="tw-block tw-mt-2">Quản lý KH sản xuất</span>
                    </a>
                </div>
                <div *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'ROLE_MANUFACTURING']">
                    <a class="tw-block" target="_blank" [href]="environmentState.HOST_BOS + 'manufacturing'">
                        <img src="/assets/images/ui/man_icon-2.png" class="tw-w-20 tw-h-20 tw-mx-auto hover:tw-scale-125 tw-transition tw-duration-300" />
                        <span class="tw-block tw-mt-2">Quản lý dữ liệu SX</span>
                    </a>
                </div>
                <div class="" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'ROLE_WARRANTY']">
                    <a class="tw-block" target="_blank" [href]="environmentState.HOST_BOS + 'warranty'">
                        <img src="/assets/images/ui/warranty-icon.png" class="tw-w-20 tw-h-20 tw-mx-auto hover:tw-scale-125 tw-transition tw-duration-300" />
                        <span class="tw-block tw-mt-2">Quản lý bảo hành</span>
                    </a>
                </div>
                <div class="" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'ROLE_INVENTORY']">
                    <a class="tw-block" target="_blank" [href]="environmentState.HOST_INVENTORY">
                        <img src="/assets/images/ui/inv.png" class="tw-w-20 tw-h-20 tw-mx-auto hover:tw-scale-125 tw-transition tw-duration-300" />
                        <span class="tw-block tw-mt-2">Quản lý kho</span>
                    </a>
                </div>
                <div class="" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'ROLE_SMARTQC']">
                    <a class="tw-block" routerLink="/sqc/dashboard">
                        <img src="/assets/images/ui/SmartQC2.png" class="tw-w-20 tw-h-20 tw-mx-auto hover:tw-scale-125 tw-transition tw-duration-300" />
                        <span class="tw-block tw-mt-2">Quản lý chất lượng thi công</span>
                    </a>
                </div>
                <div *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'ROLE_SUPPLIER_CHAIN']">
                    <a class="tw-block" routerLink="/sc/dashboard">
                        <img src="/assets/images/ui/supplyChain.png" class="tw-w-20 tw-h-20 tw-mx-auto hover:tw-scale-125 tw-transition tw-duration-300" />
                        <span class="tw-block tw-mt-2">Quản lý cung ứng</span>
                    </a>
                </div>
                <div *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'ROLE_PRODUCT_DOCUMENTATION']">
                    <a class="tw-block" routerLink="/pms/dashboard">
                        <img src="/assets/images/ui/documenticon.png" class="tw-w-20 tw-h-20 tw-mx-auto hover:tw-scale-125 tw-transition tw-duration-300" />
                        <span class="tw-block tw-mt-2">Quản lý hồ sơ sản phẩm</span>
                    </a>
                </div>
                <div>
                    <a class="tw-block" routerLink="/ptm/project-plans">
                        <img src="/assets/images/ui/documenticon.png" class="tw-w-20 tw-h-20 tw-mx-auto hover:tw-scale-125 tw-transition tw-duration-300" />
                        <span class="tw-block tw-mt-2">Quản lý CNSX</span>
                    </a>
                </div>
            </div>
        </p-card>
        <!-- Card 3: Hệ thống -->
        <p-card subheader="Hệ thống" styleClass="tw-row-span-1 tw-h-full">
            <div>
                <div class="tw-grid tw-grid-cols-4 tw-text-center tw-gap-4 tw-mt-4">
                    <div
                        *appHasAnyAuthority="[
                            'ROLE_SYSTEM_ADMIN',
                            'ROLE_SUB_ADMIN',
                            'user_view',
                            'user_create',
                            'user_delete',
                            'user_edit',
                            'role_view',
                            'role_create',
                            'role_delete',
                            'role_edit',
                        ]"
                    >
                        <a class="tw-block" href="/administration/dashboard">
                            <img src="/assets/images/ui/admin.png" class="tw-w-20 tw-h-20 tw-mx-auto hover:tw-scale-125 tw-transition tw-duration-300" />
                            <span class="tw-block tw-mt-2">Quản trị hệ thống</span>
                        </a>
                    </div>
                    <div>
                        <a class="tw-block" routerLink="/guide-document">
                            <img src="/assets/images/ui/guide-book.png" class="tw-w-20 tw-h-20 tw-mx-auto hover:tw-scale-125 tw-transition tw-duration-300" />
                            <span class="tw-block tw-mt-2">Hướng dẫn sử dụng</span>
                        </a>
                    </div>
                </div>
            </div>
        </p-card>
    </div>
</p-overlayPanel>
