import { HttpClient, HttpErrorResponse, HttpParams, HttpResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { catchError, Observable, throwError } from 'rxjs';
import { Routing } from 'src/app/models/interface/ptm/routing';

@Injectable({ providedIn: 'root' })
export class RoutingService {
    path = '/pr/api/production-instruction';
    #http = inject(HttpClient);
    constructor(private http: HttpClient) {}

    getRouting(id: string): Observable<Routing[]> {
        const url = `${this.path}/${id}/routing`;
        return this.#http.get<Routing[]>(url);
    }

    create(formData: any, instructionId: string): Observable<any> {
        const url = `${this.path}/${instructionId}/routing`;
        return this.http.post(url, formData).pipe(catchError(this.handleError));
    }

    updateRouting(routing: Routing) {
        const url = `${this.path}/routing/${routing.id}`;
        return this.#http.put<Routing>(url, routing).pipe(catchError(this.handleError));
    }

    deleteRouting(id: number) {
        const url = `${this.path}/routing/${id}`;
        return this.#http.delete(url).pipe(catchError(this.handleError));
    }

    uploadFile(file: File) {
        const formData: FormData = new FormData();
        formData.append('file', file);
        const url = `/pr/api/files/upload`;
        return this.http.post(url, formData).pipe(catchError(this.handleError));
    }

    /** Xử lý chung lỗi HTTP */
    private handleError(error: HttpErrorResponse) {
        // có thể tuỳ chỉnh thông báo, logging, …
        console.error('API error', error);
        return throwError(() => error);
    }
}
