import { canAuthorize } from '../../../core/auth/auth.guard';

export const ScBomRouting = {
    path: 'bom',
    title: 'Quản lý SC BOM',
    canActivate: [canAuthorize],
    data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_bom_view'] },
    children: [
        {
            path: '',
            title: 'Danh sách SC BOM',
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_bom_view', 'sc_bom_edit'] },
            loadComponent: () => import('./list/list.component').then((c) => c.ListComponent),
        },
        {
            path: ':id',
            title: 'Chi tiết SC BOM',
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_bom_view', 'sc_bom_edit'] },
            loadComponent: () => import('./detail/sc-bom.detail.component').then((c) => c.ScBomDetailComponent),
        },
        {
            path: 'create',
            title: 'Tạo mới SC BOM',
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_bom_edit'] },
            loadComponent: () => import('./detail/sc-bom.detail.component').then((c) => c.ScBomDetailComponent),
        }
    ],
};

export const PoDraftRouting = {
    path: 'po-draft',
    title: 'Quản lý SC BOM',
    canActivate: [canAuthorize],
    data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_bom_view'] },
    children: [
        {
            path: ':id',
            title: 'Quản lý đơn nháp',
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_bom_view', 'sc_bom_edit'] },
            loadComponent: () => import('./po-draft/po-draft.detail.component').then((c) => c.PoDraftDetailComponent),
        }
    ],
};
