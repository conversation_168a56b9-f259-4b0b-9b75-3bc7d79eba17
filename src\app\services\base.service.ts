import { HttpClient } from '@angular/common/http';
import { ParamsTable } from '../shared/table-module/table.common.service';
import { BaseEntity } from '../models/BaseEntity';

export class BaseService<T extends BaseEntity> {
    constructor(
        protected http: HttpClient,
        protected baseUrl: string,
    ) {}

    getPage(params: string) {
        return this.http.get<T[]>(`${this.baseUrl}/search?` + params, {
            observe: 'response',
        });
    }
    create<T>(template: T) {
        return this.http.post<T>(`${this.baseUrl}`, template, {
            observe: 'response',
        });
    }

    update(entity: T) {
        return this.http.put<T>(`${this.baseUrl}/` + entity.id, entity, {
            observe: 'response',
        });
    }

    getOne(id: number) {
        return this.http.get<T>(`${this.baseUrl}/` + id, {
            observe: 'response',
        });
    }

    getPageTableCustom({ native = '', pageable = '&page=0&size=10', rsql = '' }: ParamsTable) {
        return this.http.get<T[]>(`${this.baseUrl}/search?query=${rsql}${native}${pageable}`, {
            observe: 'response',
        });
    }

    getPageTablePost({ native = '', pageable = '&page=0&size=10', rsql = '' }: ParamsTable, body: unknown) {
        return this.http.post<T[]>(`${this.baseUrl}/search?query=${rsql}${native}${pageable}`, body, {
            observe: 'response',
        });
    }

    delete(id: number) {
        return this.http.delete<number>(`${this.baseUrl}/${id}`, {});
    }

    batchDelete(ids: number[]) {
        return this.http.post<number[]>(`${this.baseUrl}/batch-delete`, ids);
    }

    advancedGroup({ native = '', pageable = '&page=0&size=100000', rsql = '' }: ParamsTable) {
        return this.http.get<T[]>(`${this.baseUrl}/advanced-group?query${rsql}${native}${pageable}`, {
            observe: 'response',
        });
    }
}
