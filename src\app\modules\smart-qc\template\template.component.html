<app-sub-header [items]="itemsHeader" [action]="actionHeader"></app-sub-header>

<ng-template #actionHeader>
    <p-button *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM']" routerLink="create" label="Thêm mới" size="small" severity="success" />
</ng-template>

<div style="padding: 1rem 1rem 0 1rem">
    <app-table-common
        [tableId]="tableId"
        [columns]="columns"
        [data]="state.data"
        [loading]="state.isFetching"
        [filterTemplate]="filterTemplate"
        [funcDelete]="deleteSelectedTemplate"
        [rowSelectable]="isRowSelectable"
        [selectionMode]="authService.isAdminOrPM() ? 'multiple' : null"
        name="Danh sách mẫu kiểm tra"
    >
        <ng-template #filterTemplate>
            <tr>
                <th *ngIf="authService.isAdminOrPM()"></th>
                <th>
                    <app-filter-table
                        [tableId]="tableId"
                        field="id"
                        rsql="true"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            param: 'name',
                            url: '/smart-qc/api/template/search',
                        }"
                        placeholder="Tên mẫu kiểm tra"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'code']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="code"
                        rsql="true"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'code',
                            fieldLabel: 'code',
                            param: 'code',
                            url: '/smart-qc/api/template/search',
                        }"
                        placeholder="Mã mẫu kiểm tra"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'workType']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="workType"
                        rsql="true"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'label',
                            options: workTypes,
                        }"
                        placeholder="Công việc"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'action']" *ngIf="authService.isAdminOrPM()">
                    <ng-template #actionTemplate let-rowData>
                        <p-button icon="pi pi-copy" (click)="copyTemplate(rowData.id)" label="" pTooltip="Sao chép" tooltipPosition="right" title=""></p-button>
                    </ng-template>
                </th>
            </tr>
        </ng-template>
    </app-table-common>
</div>
<ng-template #templateName let-rowData>
    <a [routerLink]="rowData.id + '/edit'">{{ rowData.name }}</a>
</ng-template>
<ng-template #templateWorkType let-rowData>
    <div>{{ getWorkTypeByValue(rowData.workType) }}</div>
</ng-template>
<ng-template #hello>
    <button>Edit</button>
</ng-template>
