import { AfterViewInit, Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { ButtonModule } from 'primeng/button';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { CalendarModule } from 'primeng/calendar';
import { TableModule } from 'primeng/table';
import { PopupComponent } from 'src/app/shared/components/popup/popup.component';
import { FormArray, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { AbstractControlCustom, FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { InputTextModule } from 'primeng/inputtext';
import { PoService } from 'src/app/services/sc/po/po.service';
import { Column, EventChangeFilter, EventPopupSubmit } from 'src/app/models/interface';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { TABLE_KEY } from 'src/app/models/constant';
import { InformationRetrieval, Supplier, SupplierMaterial } from 'src/app/models/interface/sc';
import { TableCommonService } from 'src/app/shared/table-module/table.common.service';
import { DialogModule } from 'primeng/dialog';
import { SupplierMaterialService } from 'src/app/services/sc/supplier/suppiler-material.service';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { FormArrayCustom } from 'src/app/shared/form-module/from-array.custom';
import { AlertService } from 'src/app/shared/services/alert.service';
import { FileService } from 'src/app/shared/services/file.service';
import { ButtonGroupFileComponent } from 'src/app/shared/components/button-group-file/button-group-file.component';
import * as XLSX from 'xlsx';
import { ConfirmationService } from 'primeng/api';
@Component({
    selector: 'app-information-retrieval',
    standalone: true,
    imports: [
        CommonModule,
        SubHeaderComponent,
        ButtonModule,
        TableCommonModule,
        FormCustomModule,
        CalendarModule,
        TableModule,
        PopupComponent,
        ReactiveFormsModule,
        InputTextModule,
        DialogModule,
        ButtonGroupFileComponent,
    ],
    templateUrl: './information-retrieval.component.html',
    styleUrls: ['./information-retrieval.component.scss'],
    providers: [PoService, SupplierMaterialService],
})
export class InformationRetrievalComponent implements OnInit, AfterViewInit {
    @ViewChild('templateAction') templateAction: TemplateRef<Element>;
    visible: boolean = false;
    itemPopup: InformationRetrieval;
    formGroup: FormGroup;
    formGroupMaterial: FormGroup;
    columns: Column[] = [];
    tableId: string = TABLE_KEY.InformationRetrieval;
    state: QueryObserverBaseResult<InformationRetrieval[]>;

    materialHaveTransfer: SupplierMaterial[] = [];
    materialNotTransfer: SupplierMaterial[] = [];
    backUpMaterial: SupplierMaterial;
    isAddingMaterial: boolean = false;
    constructor(
        private fb: FormBuilder,
        private poService: PoService,
        private supplierMaterialService: SupplierMaterialService,
        private tableCommonService: TableCommonService,
        private loadingService: LoadingService,
        private alertService: AlertService,
        private fileService: FileService,
        private confirmationService: ConfirmationService,
    ) {
        this.initForm();
        this.initFormMaterial([]);
    }

    ngOnInit(): void {
        this.tableCommonService
            .init<InformationRetrieval>({
                tableId: this.tableId,
                queryFn: (filter) => this.poService.getPageInformationRetrieval(filter, this.getCleanedFormValue()),
                defaultParams: {
                    sort: 'internal_reference',
                },
            })
            .subscribe({
                next: (res) => {
                    this.state = res;
                },
            });
    }

    getCleanedFormValue() {
        const raw = this.formGroup.getRawValue();
        const cleaned = {};

        Object.keys(raw).forEach((key) => {
            const value = raw[key];

            if (typeof value === 'string') {
                const trimmed = value.trim();
                cleaned[key] = trimmed === '' ? null : trimmed;
            } else {
                cleaned[key] = value;
            }
        });

        return cleaned;
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.columns = [
                {
                    header: 'VNPT PN',
                    field: 'vnptPn',
                },
                {
                    field: 'vnptManPn',
                    header: 'VNPT Man PN',
                },
                { field: 'manPn', header: 'Mã nhà sản xuất' },

                { field: 'description', header: 'Mô tả' },
                { field: 'supplierShortNameList', header: 'Danh sách nhà cung cấp (viết tắt)' },
                { field: 'supplierFullNameList', header: 'Danh sách nhà cung cấp (đầy đủ)' },
                { header: 'Thao tác', body: this.templateAction },
            ];
        });
    }
    initForm() {
        this.formGroup = new FormGroupCustom(this.fb, {
            vnptPn: [null],
            vnptManPn: [null],
            manPn: [null],
            description: [null],
            supplierId: [null],
            manPns: [['-1']],
            vnptManPns: [['-1']],
        });
    }

    initFormMaterial(data: SupplierMaterial[]) {
        this.formGroupMaterial = new FormGroupCustom(this.fb, {
            materials: new FormArrayCustom(
                data?.map(
                    (item) =>
                        new FormGroupCustom(this.fb, {
                            id: [item?.id],
                            created: [item?.created],
                            updated: [item?.updated],
                            createdBy: [item?.createdBy],
                            updatedBy: [item?.updatedBy],
                            tenantId: [item?.tenantId],
                            active: [item?.active],
                            supplierId: [item?.supplierId],
                            supplierShortName: [item?.supplierShortName],
                            supplierName: [item?.supplierName],
                            haveTransfer: [item?.haveTransfer ?? 0],
                            manPn: [item?.manPn],
                            manufacturerId: [item?.manufacturerId],
                            manufacturerName: [item?.manufacturerName],
                            type: [item.type ?? 0],
                            description: [item.description],
                            internalReference: [item.internalReference],
                            isEdit: [false],
                        }),
                ),
            ),
        });
    }

    get materials(): FormArray {
        return this.formGroupMaterial?.get('materials') as FormArray;
    }

    search() {
        this.state.refetch();
    }

    showDialog(item: InformationRetrieval) {
        this.itemPopup = item;
        this.loadingService.show();
        this.supplierMaterialService.getPage(`query=internalReference=='${item.vnptManPn}';type==0&page=0&size=10000`).subscribe({
            next: (res) => {
                this.visible = true;
                this.materialNotTransfer = res.body.filter((item) => item.haveTransfer === 0);
                this.materialHaveTransfer = res.body.filter((item) => item.haveTransfer === 1);
                this.initFormMaterial(this.materialNotTransfer);
            },
            complete: () => {
                this.loadingService.hide();
            },
        });
    }

    closeDialog() {
        this.visible = false;
        this.itemPopup = null;
        this.materialNotTransfer = [];
        this.materialHaveTransfer = [];
        this.isAddingMaterial = false;
    }

    addItem(): void {
        this.isAddingMaterial = true;
        const newItem = new FormGroupCustom(this.fb, {
            id: [null],
            created: [null],
            updated: [null],
            createdBy: [null],
            updatedBy: [null],
            tenantId: [null],
            active: [1],
            supplierId: [null, Validators.required],
            supplierShortName: [null],
            supplierName: [null],
            manPn: [this.itemPopup?.manPn],
            manufacturerId: [this.itemPopup?.manufacturerId],
            manufacturerName: [this.itemPopup?.manufacturerName],
            description: [this.itemPopup?.description],
            internalReference: [this.itemPopup?.vnptManPn],
            type: [0],
            haveTransfer: [0],
            isEdit: [true],
        });
        this.materials.push(newItem);
    }
    saveItem(index: number) {
        const item = this.materials.at(index) as AbstractControlCustom;
        item.isSubmited = true;

        if (item && item.valid) {
            const itemValue = item.getRawValue();
            this.loadingService.show();
            const saveObservable = itemValue.id ? this.supplierMaterialService.update(itemValue) : this.supplierMaterialService.create(itemValue);
            saveObservable.subscribe({
                next: (res) => {
                    this.loadingService.hide();
                    item.patchValue({ ...res.body, isEdit: false });
                    this.alertService.success('Thành công');
                    this.isAddingMaterial = false;
                },
                error: () => {
                    this.loadingService.hide();
                },
            });
        }
    }
    cancelCreate(index: number) {
        if (this.isAddingMaterial) {
            this.materials.removeAt(index);
        }
        this.isAddingMaterial = false;
    }
    deleteItem(index: number) {
        const item = this.materials.at(index);
        const itemValue = item.getRawValue();

        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận xóa',
            message: 'Bạn có chắc chắn muốn xóa?',
            icon: 'pi pi-exclamation-triangle',
            accept: () => {
                this.loadingService.show();
                this.supplierMaterialService.delete(itemValue.id).subscribe({
                    next: () => {
                        this.loadingService.hide();
                        this.materials.removeAt(index);
                    },
                    error: () => {
                        this.loadingService.hide();
                    },
                });
            },
        });
    }

    selectSupplier(event: EventChangeFilter, index: number) {
        if (event.objects && event.objects.length > 0) {
            const supplier = event.objects[0] as Supplier;

            this.materials.at(index).patchValue({
                supplierShortName: supplier.shortName,
                supplierName: supplier.name,
            });
        } else {
            this.materials.at(index).patchValue({
                supplierShortName: null,
                supplierName: null,
            });
        }
    }

    handleUploadFile(file: File) {
        const reader = new FileReader();

        reader.onload = (e: ProgressEvent<FileReader>) => {
            // Đọc dữ liệu file
            const data = new Uint8Array(e.target?.result as ArrayBuffer);
            const workbook = XLSX.read(data, { type: 'array' });

            // Lấy sheet đầu tiên
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];

            // Chuyển sheet thành mảng JSON
            const jsonData = XLSX.utils.sheet_to_json(worksheet, {
                header: 1, // Lấy dữ liệu dạng mảng 2D
                range: 1, // Bắt đầu từ hàng thứ 2 (bỏ qua hàng đầu tiên)
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
            }) as any[][];

            // Reset mảng trước khi lưu dữ liệu mới
            const columnA = [];
            const columnB = [];

            // Lấy dữ liệu từ cột A (index 0) và cột B (index 1)
            jsonData.forEach((row) => {
                columnA.push(row[0] !== undefined && row[0] !== null && row[0].toString().trim().length > 0 ? row[0] : null); // Cột A
                columnB.push(row[1] !== undefined && row[1] !== null && row[1].toString().trim().length > 0 ? row[1] : null); // Cột B
            });
            if (columnA.length > 0) {
                this.formGroup.patchValue({
                    manPns: columnA,
                });
            } else {
                this.formGroup.patchValue({
                    manPns: ['-1'],
                });
            }

            if (columnB.length > 0) {
                this.formGroup.patchValue({
                    vnptManPns: columnB,
                });
            } else {
                this.formGroup.patchValue({
                    vnptManPns: ['-1'],
                });
            }

            this.search();
        };

        reader.onerror = () => {
            this.alertService.error('Lỗi khi đọc file');
        };

        // Đọc file dưới dạng ArrayBuffer
        reader.readAsArrayBuffer(file);
    }

    handleExportReport(event: EventPopupSubmit<unknown>) {
        this.loadingService.show();

        this.poService.exportInformationRetrieval(this.formGroup.getRawValue()).subscribe({
            next: (res: Blob) => {
                this.fileService.downloadBlob(res, 'report.xlsx');
                this.loadingService.hide();
                event.close();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    handleClearFile() {
        this.formGroup.patchValue({
            manPns: ['-1'],
            vnptManPns: ['-1'],
        });
    }
}
