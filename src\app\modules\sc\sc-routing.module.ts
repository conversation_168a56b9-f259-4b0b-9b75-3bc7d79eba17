import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { DashBoardSCCRouting } from './dashboard/dashboard.routing';
import { SupplierRouting } from './supplier/supplier.routing';
import { PORouting } from './po/order.routing';
import { LogisticsRouting } from './logistics/logistics.routing';
import { RfqRouting } from './ask-price/rfq.routing';
import { PoDraftRouting, ScBomRouting } from './sc-bom/sc-bom.routing';
import { RequestShippingRouting } from './request-shipping/request-shipping.routing';
import { POReportRouting } from './po-report/po-report.routing';

@NgModule({
    imports: [
        RouterModule.forChild([
            {
                path: '',
                redirectTo: 'dashboard',
                pathMatch: 'full',
            },

            DashBoardSCCRouting,
            SupplierRouting,
            PORouting,
            LogisticsRouting,
            RfqRouting,
            ScBomRouting,
            PoDraftRouting,
            RequestShippingRouting,
            POReportRouting,
        ]),
    ],
    exports: [RouterModule],
})
export class SCRoutingModule {}
