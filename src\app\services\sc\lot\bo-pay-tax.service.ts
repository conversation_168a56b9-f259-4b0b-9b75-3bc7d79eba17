import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { BoPayTax } from '../../../models/interface/sc';
import { ApiResponse } from 'src/app/models/interface';

@Injectable()
export class BoPayTaxService extends BaseService<BoPayTax> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/bo-pay-tax');
    }

    importFile(files: File[]) {
        const formData: FormData = new FormData();
        files.forEach((file) => formData.append('files', file));

        return this.http.post<ApiResponse>('/sc/api/bo-pay-tax/import-file', formData);
    }

    importFileCustoms(file: File) {
        const formData: FormData = new FormData();
        formData.append('file', file);

        return this.http.post<ApiResponse>('/sc/api/bo-pay-tax/import-file-customs', formData);
    }

    getByLot(lotId: number) {
        return this.http.get<BoPayTax>('/sc/api/bo-pay-tax/get-info', {
            params: { lotId },
        });
    }
}
