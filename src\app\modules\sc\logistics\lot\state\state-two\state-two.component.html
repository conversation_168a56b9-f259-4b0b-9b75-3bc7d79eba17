<p-panel
    [header]="lot?.type === 0 ? 'Giai đoạn 2: Thông tin lịch trình vận chuyển' : 'Giai đoạn 2: <PERSON><PERSON><PERSON> nhận hàng vận chuyển khỏi kho'"
    [toggleable]="true"
    *ngIf="lot"
>
    <ng-template pTemplate="icons">
        <p-button label="Lịch sử thay đổi" severity="secondary" size="small" class="tw-mr-4" (click)="getTotalHistory()"></p-button>
        <p-button label="Cập nhật lịch trình" severity="primary" size="small" (click)="showPopupCreateHis()"></p-button>
    </ng-template>
    <div class="tw-grid tw-grid-cols-2 tw-gap-4">
        <div class="tw-space-y-2">
            <div class="font-bold">Ngày giao hàng dự kiến</div>
            <div>{{ lastLotHistory?.expectedDeliveryDate | date: 'dd/MM/yyyy' }}</div>
            <div class="text-red-600" *ngIf="lastLotHistory?.delayDeliveryDate">Thời gian delay {{ lastLotHistory.delayDeliveryDate }} ngày</div>
        </div>

        <div class="tw-space-y-2">
            <div class="font-bold">Thời gian khởi hành dự kiến</div>
            <div>{{ lastLotHistory?.expectedLeaveDate | date: 'dd/MM/yyyy' }}</div>
            <div class="text-red-600" *ngIf="lastLotHistory?.delayLeaveDate">Thời gian delay {{ lastLotHistory.delayLeaveDate }} ngày</div>
        </div>

        <div class="tw-space-y-2">
            <div class="font-bold">Thời gian dự kiến đến nơi</div>
            <div>{{ lastLotHistory?.expectedPortDate | date: 'dd/MM/yyyy' }}</div>
            <div class="text-red-600" *ngIf="lastLotHistory?.delayPortDate">Thời gian delay {{ lastLotHistory.delayPortDate }} ngày</div>
        </div>

        <div class="tw-space-y-2">
            <div class="font-bold">Thời gian dự kiến về kho</div>
            <div>{{ lastLotHistory?.expectedWarehouseDate | date: 'dd/MM/yyyy' }}</div>
            <div class="text-red-600" *ngIf="lastLotHistory?.delayWarehouseDate">Thời gian delay {{ lastLotHistory.delayWarehouseDate }} ngày</div>
        </div>
        <div class="tw-space-y-2 tw-col-span-2">
            <div class="font-bold">Ghi chú</div>
            <div>{{ lastLotHistory?.note }}</div>
        </div>
        <div class="tw-space-y-2 tw-col-span-2">
            <div class="font-bold">Đính kèm</div>
            <div class="tw-flex tw-flex-col tw-gap-4" *ngIf="lastLotHistory">
                <ng-container *ngFor="let att of lastLotHistory.attachments">
                    <app-attachment [attachment]="att"></app-attachment>
                </ng-container>
            </div>
        </div>
    </div>
    <br />
    <ng-container *ngIf="lot.type === 1">
        <hr class="tw-w-full" />
        <p>Khai báo hải quan</p>
        <app-form #formFour [formGroup]="formGroupFour" layout="vertical">
            <div class="tw-grid tw-grid-cols-2 tw-gap-4">
                <app-form-item label="Tờ khai hải quan">
                    <app-button-group-file
                        simpleUpload=""
                        (onFileSelected)="handleUploadFileCustoms($event)"
                        [attachment]="formGroupFour.getRawValue().attachmentCustom"
                        formControlName="attachmentCustomId"
                        [types]="['excel']"
                    ></app-button-group-file>
                </app-form-item>
                <app-form-item label="Thuế GTGT">
                    <app-inputNumber mode="decimal" formControlName="vatTax"> </app-inputNumber>
                </app-form-item>
                <app-form-item label="Số tờ khai báo hải quan">
                    <input type="text" pInputText class="tw-w-full" formControlName="customsDocumentNumber" />
                </app-form-item>
                <app-form-item label="Thuế nhập khẩu">
                    <app-inputNumber mode="decimal" formControlName="taxImport"> </app-inputNumber>
                </app-form-item>
                <app-form-item label="Ngày khai báo hải quan">
                    <p-calendar
                        [showButtonBar]="true"
                        placeholder="dd/MM/yyyy"
                        dateFormat="dd/mm/yy"
                        appendTo="body"
                        formControlName="customsDeclareDateCustom"
                    ></p-calendar>
                </app-form-item>
                <app-form-item label="Tổng số thuế phải nhập">
                    <app-inputNumber mode="decimal" formControlName="totalTax"> </app-inputNumber>
                </app-form-item>
                <hr style="margin: 0" class="tw-col-span-2" />
                <app-form-item label="Số vận đơn">
                    <input type="text" pInputText class="tw-w-full" formControlName="orderCode" />
                </app-form-item>
                <app-form-item label="Ngày chuyển bộ thuế lên kế toán">
                    <p-calendar
                        [showButtonBar]="true"
                        placeholder="dd/MM/yyyy"
                        dateFormat="dd/mm/yy"
                        appendTo="body"
                        formControlName="submitDateCustom"
                    ></p-calendar>
                </app-form-item>
                <hr style="margin: 0" class="tw-col-span-2" />
                <app-form-item label="Chi phí khác">
                    <app-inputNumber mode="decimal" formControlName="otherFee"> </app-inputNumber>
                </app-form-item>
                <div></div>
                <hr style="margin: 0" class="tw-col-span-2" />
                <app-form-item label="Ghi chú" class="tw-col-span-2">
                    <textarea rows="5" formControlName="note" pInputTextarea class="tw-w-full"></textarea>
                </app-form-item>
                <hr style="margin: 0" class="tw-col-span-2" />
                <app-form-item label="Đính kèm">
                    <app-button-group-file
                        simpleUpload=""
                        (onFileSelected)="handleUploadFileLotCustoms($event)"
                        [attachments]="formGroupFour.getRawValue().attachments"
                        formControlName="attachmentIds"
                        [multiple]="true"
                    ></app-button-group-file>

                    <app-button-group-file
                        *ngIf="formGroupFour.getRawValue().attachments && formGroupFour.getRawValue().attachments.length > 0"
                        class="tw-col-span-2"
                        (onFileSelected)="handleUploadFileLotCustoms($event)"
                        [multiple]="true"
                        simpleUpload=""
                        formControlName="attachmentIds"
                    ></app-button-group-file>
                </app-form-item>
            </div>
        </app-form>
    </ng-container>
    <div class="tw-flex tw-justify-end tw-space-x-3">
        <p-button
            label="Xuất tài liệu"
            severity="success"
            size="small"
            type="button"
            title="Xuất bảng kê và Yêu cầu bảo hiểm"
            (click)="showPopupExport()"
        ></p-button>
        <p-button label="Thông báo cho người tiếp nhận" type="button" severity="primary" size="small" (click)="visibleSubmit = true"></p-button>
    </div>
</p-panel>

<p-dialog header="Cập nhật lịch trình" [(visible)]="visibleNew" [style]="{ width: '80vw' }" [breakpoints]="{ '1199px': '80vw', '575px': '100vw' }">
    <hr style="margin: 0" />
    <br />
    <app-form #form *ngIf="formGroup" [formGroup]="formGroup" layout="vertical" (onSubmit)="onSubmitHis($event)">
        <div class="tw-grid tw-grid-cols-2 tw-gap-4">
            <app-form-item label="Ngày giao hàng dự kiến">
                <p-calendar
                    [showButtonBar]="true"
                    placeholder="dd/MM/yyyy"
                    dateFormat="dd/mm/yy"
                    appendTo="body"
                    formControlName="expectedDeliveryDateCustom"
                ></p-calendar>
            </app-form-item>
            <app-form-item label="Thời gian khởi hành dự kiến">
                <p-calendar
                    [showButtonBar]="true"
                    placeholder="dd/MM/yyyy"
                    dateFormat="dd/mm/yy"
                    appendTo="body"
                    formControlName="expectedLeaveDateCustom"
                ></p-calendar>
            </app-form-item>
            <app-form-item label="Thời gian dự kiến đến nơi">
                <p-calendar
                    [showButtonBar]="true"
                    placeholder="dd/MM/yyyy"
                    dateFormat="dd/mm/yy"
                    appendTo="body"
                    formControlName="expectedPortDateCustom"
                ></p-calendar>
            </app-form-item>
            <app-form-item label="Thời gian dự kiến về kho">
                <p-calendar
                    [showButtonBar]="true"
                    placeholder="dd/MM/yyyy"
                    dateFormat="dd/mm/yy"
                    appendTo="body"
                    formControlName="expectedWarehouseDateCustom"
                ></p-calendar>
            </app-form-item>

            <app-form-item label="Ghi chú" class="tw-col-span-2">
                <textarea rows="5" formControlName="note" pInputTextarea class="tw-w-full"></textarea>
            </app-form-item>

            <app-form-item label="Đính kèm">
                <app-button-group-file
                    simpleUpload=""
                    (onFileSelected)="handleUploadFile($event)"
                    [attachments]="formGroup.getRawValue().attachments"
                    formControlName="attachmentIds"
                    [multiple]="true"
                ></app-button-group-file>

                <app-button-group-file
                    *ngIf="formGroup.getRawValue().attachments && formGroup.getRawValue().attachments.length > 0"
                    class="tw-col-span-2"
                    (onFileSelected)="handleUploadFile($event)"
                    [multiple]="true"
                    simpleUpload=""
                    formControlName="attachmentIds"
                ></app-button-group-file>
            </app-form-item>
        </div>
    </app-form>
    <ng-template pTemplate="footer">
        <div>
            <p-button severity="primary" type="button" size="small" (click)="form.handleSubmit()" label="Lưu"></p-button>
            <p-button label="Hủy" type="button" [text]="true" [raised]="true" size="small" severity="secondary" (click)="visibleNew = false"></p-button>
        </div>
    </ng-template>
</p-dialog>

<p-dialog header="Cập nhật lịch trình" [(visible)]="visibleHistory" [style]="{ width: '80vw' }" [breakpoints]="{ '1199px': '80vw', '575px': '100vw' }">
    <hr style="margin: 0" />
    <br />
    <p-table styleClass="p-datatable-gridlines" [value]="lotHistoryList" [scrollable]="true" [loading]="isLoadingList">
        <ng-template pTemplate="header">
            <tr>
                <th>Ngày cập nhật</th>
                <th>Người cập nhật</th>
                <th style="min-width: 10rem">Ngày giao hàng dự kiến</th>
                <th style="min-width: 10rem">Thời gian dự kiến đến nơi</th>
                <th style="min-width: 10rem">Thời gian khởi hành dự kiến</th>
                <th style="min-width: 10rem">Thời gian dự kiến về kho</th>
                <th style="min-width: 10rem">Ghi chú</th>
                <th style="min-width: 10rem">Đính kèm</th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
            <tr>
                <td>
                    {{ item.created | date: 'dd/MM/yyyy HH:mm:ss' }}
                </td>
                <td>
                    {{ item.createdBy }}
                </td>
                <td>
                    {{ item.expectedDeliveryDate | date: 'dd/MM/yyyy' }}
                </td>
                <td>
                    {{ item.expectedPortDate | date: 'dd/MM/yyyy' }}
                </td>
                <td>
                    {{ item.expectedLeaveDate | date: 'dd/MM/yyyy' }}
                </td>
                <td>
                    {{ item.expectedWarehouseDate | date: 'dd/MM/yyyy' }}
                </td>
                <td>{{ item.note }}</td>

                <td>
                    <div class="tw-flex tw-flex-row tw-gap-4" *ngIf="item.attachments">
                        <ng-container *ngFor="let att of item.attachments">
                            <app-attachment [attachment]="att"></app-attachment>
                        </ng-container>
                    </div>
                </td>
            </tr>
        </ng-template>
    </p-table>
</p-dialog>

<p-dialog
    header="Thông báo cho người tiếp nhận"
    [(visible)]="visibleSubmit"
    [style]="{ width: '80vw' }"
    [breakpoints]="{ '1199px': '80vw', '575px': '100vw' }"
    (onHide)="formGroupSubmit.reset()"
>
    <hr style="margin: 0" />
    <br />
    <app-form #formSubmit *ngIf="visibleSubmit" [formGroup]="formGroupSubmit" layout="vertical" (onSubmit)="sendNotification($event)">
        <app-form-item label="Nội dung">
            <textarea rows="5" formControlName="content" pInputTextarea class="tw-w-full"></textarea>
        </app-form-item>
        <app-form-item label="Người tiếp nhận" [isRequired]="true">
            <app-filter-table
                type="select"
                [configSelect]="{
                    fieldValue: 'id',
                    fieldLabel: 'email',
                    options: receivers,
                    filterLocal: true,
                }"
                (onChange)="handleChangeReceivers($event)"
            ></app-filter-table>
        </app-form-item>
    </app-form>
    <ng-template pTemplate="footer">
        <div>
            <p-button
                type="button"
                severity="primary"
                size="small"
                (click)="formSubmit.handleSubmit()"
                label="Xác nhận gửi"
                [disabled]="formGroupSubmit.invalid"
                *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_lot_edit']"
            ></p-button>
            <p-button
                type="button"
                label="Hủy"
                [text]="true"
                [raised]="true"
                size="small"
                severity="secondary"
                (click)="visibleSubmit = false; formGroupSubmit.reset()"
            ></p-button>
        </div>
    </ng-template>
</p-dialog>

<p-dialog
    header="Xuất yêu cầu bảo hiểm và bảng kê"
    [(visible)]="visibleExport"
    [style]="{ width: '80vw' }"
    [breakpoints]="{ '1199px': '80vw', '575px': '100vw' }"
>
    <hr style="margin: 0" />
    <br />
    <p-tabView (onChange)="getLotHistoryExport($event)">
        <p-tabPanel header="Bảng kê">
            <app-form #formGroupExport [formGroup]="formGroupBoDto" layout="vertical" (onSubmit)="onExport($event, 1)">
                <div class="tw-w-80">
                    <app-form-item label="Ngày" layout="horizontal">
                        <p-calendar
                            [showButtonBar]="true"
                            [showIcon]="true"
                            placeholder="dd/MM/yyyy"
                            dateFormat="dd/mm/yy"
                            appendTo="body"
                            formControlName="exportDateCustom"
                        ></p-calendar>
                    </app-form-item>
                </div>
                <br />
                <div class="tw-grid tw-grid-cols-2">
                    <p class="border-top-1 border-left-1 tw-border-gray-500 !tw-m-0 tw-p-2">Tên hàng</p>
                    <p class="border-top-1 border-left-1 tw-border-gray-500 !tw-m-0 tw-p-2 border-right-1">{{ boDto?.goodsName }}</p>

                    <p class="border-top-1 border-left-1 tw-border-gray-500 !tw-m-0 tw-p-2">Tên người giao hàng</p>
                    <p class="border-top-1 border-left-1 tw-border-gray-500 !tw-m-0 tw-p-2 border-right-1">{{ boDto?.supplierName }}</p>

                    <p class="border-top-1 border-left-1 tw-border-gray-500 !tw-m-0 tw-p-2">Địa chỉ giao hàng</p>
                    <p class="border-top-1 border-left-1 tw-border-gray-500 !tw-m-0 tw-p-2 border-right-1">{{ boDto?.supplierAddress }}</p>

                    <p class="border-top-1 border-left-1 tw-border-gray-500 !tw-m-0 tw-p-2">Khối lượng tổng lô hàng(KG)</p>
                    <p class="border-top-1 border-left-1 tw-border-gray-500 !tw-m-0 tw-p-2 border-right-1">
                        <app-inputNumber class="tw-w-full" formControlName="totalWeight" mode="decimal"></app-inputNumber>
                    </p>

                    <p class="border-top-1 border-left-1 tw-border-gray-500 !tw-m-0 tw-p-2">Số kiện của lô hàng(carton(s), pallet(s), container(s))</p>
                    <div class="border-top-1 border-left-1 tw-border-gray-500 !tw-m-0 tw-p-2 border-right-1">
                        <div class="p-inputgroup">
                            <app-inputNumber class="tw-w-full" formControlName="packageNumber" mode="decimal" maxLength="20"></app-inputNumber>

                            <span class="p-inputgroup-addon">{{ boDto?.unitLabel }}</span>
                        </div>
                    </div>

                    <p class="border-top-1 border-left-1 tw-border-gray-500 !tw-m-0 tw-p-2">Điều kiện giao hàng(theo Incoterms 2010)</p>
                    <p class="border-top-1 border-left-1 tw-border-gray-500 !tw-m-0 tw-p-2 border-right-1">{{ boDto?.deliveryCondition }}</p>

                    <p class="border-top-1 border-left-1 tw-border-gray-500 !tw-m-0 tw-p-2">Tổng giá trị shipment dự kiến(USD)</p>
                    <p class="border-top-1 border-left-1 tw-border-gray-500 !tw-m-0 tw-p-2 border-right-1">
                        <app-inputNumber class="tw-w-full" formControlName="shipmentValue" mode="decimal"></app-inputNumber>
                    </p>

                    <p class="border-top-1 border-left-1 tw-border-gray-500 !tw-m-0 tw-p-2">Ngày khởi hành dự kiến</p>
                    <p class="border-top-1 border-left-1 tw-border-gray-500 !tw-m-0 tw-p-2 border-right-1">
                        <p-calendar
                            [showButtonBar]="true"
                            placeholder="dd/MM/yyyy"
                            [showIcon]="true"
                            dateFormat="dd/mm/yy"
                            class="tw-w-full"
                            formControlName="expectedLeaveDateCustom"
                            appendTo="body"
                        ></p-calendar>
                    </p>

                    <p class="border-top-1 border-left-1 tw-border-gray-500 !tw-m-0 tw-p-2">Điều kiện thanh toán</p>
                    <p class="border-top-1 border-left-1 tw-border-gray-500 !tw-m-0 tw-p-2 border-right-1">{{ boDto?.paymentCondition }}</p>

                    <p class="border-top-1 border-left-1 tw-border-gray-500 !tw-m-0 tw-p-2">Số đơn hàng/Số hợp đồng</p>
                    <p class="border-top-1 border-left-1 tw-border-gray-500 !tw-m-0 tw-p-2 border-right-1">{{ boDto?.boCode }}</p>

                    <p class="border-top-1 border-left-1 tw-border-gray-500 !tw-m-0 tw-p-2">Mã kế toán/mã vụ việc</p>
                    <p class="border-top-1 border-left-1 tw-border-gray-500 !tw-m-0 tw-p-2 border-right-1">{{ boDto?.accountingCode }}</p>

                    <p class="border-top-1 border-left-1 border-bottom-1 tw-border-gray-500 !tw-m-0 tw-p-2">
                        Tỷ giá bán VNĐ/USD Vietcombank ngày {{ formGroupBoDto.get('exportDateCustom')?.value | date: 'dd/MM/yyyy' }}
                    </p>
                    <p class="border-top-1 border-left-1 border-bottom-1 tw-border-gray-500 !tw-m-0 tw-p-2 border-right-1">
                        <app-inputNumber class="tw-w-full" formControlName="rateMoney" mode="decimal"></app-inputNumber>
                    </p>
                </div>

                <br />

                <div
                    [style.display]="'grid'"
                    [style.grid-template-columns]="
                        'repeat(' + (boDto?.boInsuranceDtos?.length > 0 ? boDto.boInsuranceDtos.length + 1 : 1) + ', minmax(0, 1fr))'
                    "
                >
                    <ng-container
                        *ngFor="
                            let header of [
                                'Nhà cung cấp bảo hiểm',
                                'Tỷ lệ chi phí chấp nhận',
                                'Phí bảo hiểm(VNĐ)',
                                'Nhà cung cấp bảo hiểm được chọn',
                                'Ghi chú',
                            ];
                            let rowIndex = index
                        "
                    >
                        <p class="border-top-1 border-left-1 tw-border-gray-500 !tw-m-0 tw-p-2" [ngClass]="{ 'border-bottom-1': rowIndex === 4 }">
                            {{ header }}
                        </p>
                        <ng-container *ngFor="let item of boDto?.boInsuranceDtos; let i = index" formGroupName="boInsuranceDtos">
                            <p
                                class="border-top-1 border-left-1 tw-border-gray-500 !tw-m-0 tw-p-2 tw-text-center"
                                [ngClass]="{
                                    'border-right-1': i === boDto.boInsuranceDtos.length - 1,
                                    'border-bottom-1': rowIndex === 4,
                                }"
                            >
                                <ng-container [ngSwitch]="rowIndex">
                                    <ng-container *ngSwitchCase="0">{{ item.name }}</ng-container>
                                    <ng-container *ngSwitchCase="1">{{ item.insuranceRate }} %</ng-container>
                                    <ng-container *ngSwitchCase="2">{{ item.insuranceFee | number }}</ng-container>
                                    <ng-container *ngSwitchCase="3">{{ item.isChoice ? 'X' : '' }}</ng-container>
                                    <ng-container *ngSwitchCase="4" [formArrayName]="i">
                                        <textarea rows="3" pInputTextarea formControlName="note" class="tw-w-full"></textarea>
                                    </ng-container>
                                </ng-container>
                            </p>
                        </ng-container>
                    </ng-container>
                </div>

                <br />
                <app-form-item label="Người lập bảng" layout="horizontal">
                    <input type="text" pInputText formControlName="tabulator" class="tw-w-full" />
                </app-form-item>
            </app-form>

            <div class="tw-flex tw-gap-4 tw-justify-end tw-mt-4">
                <p-button severity="primary" type="button" size="small" label="Xuất YCBH" (click)="onExport(boDto, 0)"></p-button>
                <p-button severity="primary" type="button" size="small" label="Xuất YCBH sửa đổi" (click)="formGroupExport.handleSubmit()"></p-button>
            </div>
        </p-tabPanel>
        <p-tabPanel header="Lịch sử">
            <p-table styleClass="p-datatable-gridlines" [value]="listExportHistory" [scrollable]="true">
                <ng-template pTemplate="header">
                    <tr>
                        <th>Ngày xuất</th>
                        <th>Người xuất</th>
                        <th>Loại</th>
                        <th>Tài liệu</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                    <tr>
                        <td>
                            {{ item.created | date: 'dd/MM/yyyy HH:mm:ss' }}
                        </td>
                        <td>
                            {{ item.createdBy }}
                        </td>
                        <td>
                            {{ item.type === 0 ? 'YCBH' : 'YCBH sửa đổi' }}
                        </td>
                        <td>
                            <ng-container *ngIf="item?.attachment">
                                <app-attachment [attachment]="item?.attachment"></app-attachment>
                            </ng-container>
                        </td>
                    </tr>
                </ng-template>
            </p-table>
        </p-tabPanel>
    </p-tabView>
</p-dialog>
