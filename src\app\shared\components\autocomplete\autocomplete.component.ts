import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild, ElementRef, HostListener, SimpleChanges, EventEmitter, Input, Output, OnChanges, forwardRef } from '@angular/core';
import { FormGroup, FormBuilder, ReactiveFormsModule, NG_VALUE_ACCESSOR } from '@angular/forms';
import { find, filter, includes, isObject, get, isArray } from 'lodash';
import { ConfirmationService } from 'primeng/api';
import { OverlayPanelModule } from 'primeng/overlaypanel';

@Component({
    selector: 'app-autocomplete',
    standalone: true,
    imports: [CommonModule, ReactiveFormsModule, OverlayPanelModule],
    templateUrl: './autocomplete.component.html',
    styleUrls: ['./autocomplete.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => AutocompleteComponent),
            multi: true,
        },
    ],
})
export class AutocompleteComponent implements OnInit, OnChanges {
    @ViewChild('tagInput') tagInputRef: ElementRef;
    @ViewChild('autocomplete') autocompleteRef: ElementRef;

    // Input for external options
    @Input() options: unknown[] = [];
    @Input() optionValue: string = '';
    @Input() optionLabel: string = '';
    @Input() optionDelete: string;
    @Input() placeholder: string = '';
    @Input() allowCustomTags: boolean = true;
    @Input() mode: 'single' | 'multi' = 'single';
    // Cập nhật onAdd để emit một object chứa value và callback
    @Output() onAdd = new EventEmitter<{ value: unknown; callback: (result: unknown) => void }>();
    @Output() onDelete = new EventEmitter<{ value: unknown; callback: (result: unknown) => void }>();
    @Input() rowDelete: (rowData: unknown) => boolean;

    tags: unknown[] = [];
    filteredOptions: unknown[] = [];
    filterValue: string;
    form: FormGroup;
    showDropdown = false;
    activeIndex = -1;
    isComposing = false; // Track IME composition state
    dropdownPosition = { top: '0px', left: '0px', width: '0px' };
    isFull: boolean = false;
    // For ControlValueAccessor
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    private onChange: any = () => {};
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    private onTouched: any = () => {};
    private backupValue: unknown[] | unknown = null; // Lưu giá trị gốc từ form
    constructor(
        private fb: FormBuilder,
        private elementRef: ElementRef,
        private confirmationService: ConfirmationService,
    ) {}

    ngOnInit() {
        this.form = this.fb.group({
            tag: [''],
        });
        if (!this.rowDelete) {
            this.rowDelete = () => true;
        }

        // Subscribe to value changes on the tag input to filter options
        this.form.get('tag').valueChanges.subscribe((value) => {
            this.filterOptions(value);
        });
    }

    ngOnChanges(changes: SimpleChanges) {
        if (changes['options']) {
            // Khi options thay đổi, đồng bộ lại tags dựa trên giá trị hiện tại
            this.syncTagsWithOptions();
            this.filterOptions(this.form?.get('tag')?.value || '');
        }
    }

    // Methods for ControlValueAccessor interface
    writeValue(value: unknown[] | unknown): void {
        this.backupValue = value; // Lưu giá trị gốc từ form

        if (!value) {
            this.tags = [];
            this.isFull = false;
            this.onChange(null);
            return;
        }

        // Chuyển value thành mảng nếu nó là giá trị đơn
        const inputValues = isArray(value) ? value : [value];

        if (this.optionValue && this.options.length > 0) {
            // Nếu có optionValue và options đã sẵn sàng, tìm các đối tượng khớp
            const matchedTags = inputValues
                .map((val) => {
                    return find(this.options, (option) => {
                        const optionVal = get(option, this.optionValue);
                        return optionVal === val || String(optionVal).toLowerCase() === String(val).toLowerCase();
                    });
                })
                .filter((tag) => tag !== undefined);

            if (this.mode === 'single') {
                this.tags = matchedTags.length > 0 ? [matchedTags[0]] : [];
                this.isFull = this.tags.length > 0;
            } else {
                this.tags = matchedTags;
                this.isFull = false;
            }
        } else {
            // Nếu không có optionValue hoặc options chưa sẵn sàng, dùng giá trị trực tiếp
            if (this.mode === 'single') {
                this.tags = inputValues.length > 0 ? [inputValues[0]] : [];
                this.isFull = this.tags.length > 0;
            } else {
                this.tags = inputValues;
                this.isFull = false;
            }
        }
    }

    private syncTagsWithOptions(): void {
        // Nếu tags chưa được đồng bộ đúng (rỗng) và có backupValue, dùng backupValue để đồng bộ
        const valuesToSync =
            this.tags.length === 0 && this.backupValue
                ? isArray(this.backupValue)
                    ? this.backupValue
                    : [this.backupValue]
                : this.mode === 'single'
                  ? [this.getStoredValue(this.tags[0])]
                  : this.tags.map((tag) => this.getStoredValue(tag));

        if (this.optionValue && this.options.length > 0) {
            const matchedTags = valuesToSync
                .map((val) => {
                    return find(this.options, (option) => {
                        const optionVal = get(option, this.optionValue);
                        return optionVal === val || String(optionVal).toLowerCase() === String(val).toLowerCase();
                    });
                })
                .filter((tag) => tag !== undefined);

            if (this.mode === 'single') {
                this.tags = matchedTags.length > 0 ? [matchedTags[0]] : [];
                this.isFull = this.tags.length > 0;
            } else {
                this.tags = matchedTags;
                this.isFull = false;
            }
        }
    }

    registerOnChange(fn: unknown): void {
        this.onChange = fn;
    }

    registerOnTouched(fn: unknown): void {
        this.onTouched = fn;
    }

    setDisabledState(isDisabled: boolean): void {
        if (isDisabled) {
            this.form.get('tag').disable();
        } else {
            this.form.get('tag').enable();
        }
    }

    // Composition event handlers for IME input (Vietnamese, Chinese, Japanese, etc.)
    onCompositionStart(): void {
        this.isComposing = true;
    }

    onCompositionEnd(): void {
        this.isComposing = false;
    }

    // Get display value based on configuration
    getDisplayValue(item: unknown): string {
        if (isObject(item)) {
            if (this.optionLabel) {
                return get(item, this.optionLabel, '');
            } else if (this.optionValue) {
                return get(item, this.optionValue, '');
            }
        }
        return String(item);
    }

    // Get stored value based on configuration
    getStoredValue(item: unknown): unknown {
        if (isObject(item) && this.optionValue) {
            return get(item, this.optionValue);
        }
        return item;
    }

    // Create object from string input if needed
    createItemFromInput(input: string): unknown {
        if (!input) return null;

        if (this.optionValue && this.optionLabel) {
            const newItem = {};
            newItem[this.optionValue] = input;
            newItem[this.optionLabel] = input;
            return newItem;
        }

        return input;
    }

    filterOptions(value: string): void {
        this.filterValue = value;
        if (!value) {
            this.filteredOptions = [...this.options];
        } else {
            const searchText = value.toLowerCase();
            this.filteredOptions = filter(this.options, (option) => {
                const label = this.getDisplayValue(option).toLowerCase();
                return includes(label, searchText);
            });
        }

        // Reset active selection
        this.updateDropdownPosition();
        this.activeIndex = -1;
    }

    focusTagInput(): void {
        this.tagInputRef.nativeElement.focus();
        this.showDropdown = true;
        this.filteredOptions = [...this.options];
        this.updateDropdownPosition();
    }

    blurTagInput(): void {
        // Use setTimeout to allow click events on dropdown items to execute first
        setTimeout(() => {
            this.onTouched();
        }, 150);
    }

    onKeyDown(event: KeyboardEvent): void {
        // Skip processing during IME composition
        if (this.isComposing) {
            return;
        }

        const inputValue: string = this.form.get('tag').value;

        // Handle backspace only when input is empty
        if (event.key === 'Backspace' && !inputValue) {
            this.removeTag();
            return;
        }

        // Handle navigation keys
        switch (event.key) {
            case 'ArrowDown':
                this.activeIndex = Math.min(this.activeIndex + 1, this.filteredOptions.length - 1);
                event.preventDefault();
                break;
            case 'ArrowUp':
                this.activeIndex = Math.max(this.activeIndex - 1, -1);
                event.preventDefault();
                break;
            case 'Enter':
                if (this.activeIndex >= 0 && this.activeIndex < this.filteredOptions.length) {
                    // Select the highlighted suggestion
                    this.addTag(this.filteredOptions[this.activeIndex]);
                } else if (inputValue && this.allowCustomTags) {
                    // Add custom tag if allowed
                    this.addTagFromInput(inputValue);
                }
                this.form.get('tag').setValue('');
                event.preventDefault();
                break;
        }
    }

    // Processing regular key inputs
    onKeyUp(event: KeyboardEvent): void {
        // Skip special keys - they're handled in onKeyDown , 'Escape', ',', ' '
        if (['Backspace', 'Enter', 'ArrowDown', 'ArrowUp'].includes(event.key)) {
            return;
        }

        // Show dropdown for general typing
        this.showDropdown = true;
    }

    // Add tag from input text
    async addTagFromInput(input: string) {
        if (!input) return;

        // Remove trailing comma or space if present
        if (input[input.length - 1] === ',' || input[input.length - 1] === ' ') {
            input = input.slice(0, -1);
        }

        if (input.length > 0 && this.allowCustomTags) {
            let newItem = this.createItemFromInput(input);

            if (this.onAdd.observers.length > 0) {
                // Nếu có onAdd subscriber, emit và chờ result
                const result = await this.emitOnAdd(newItem);
                if (result) {
                    // Kiểm tra result có hợp lệ và không trùng trong options
                    if (!this.findInOptions(result)) {
                        this.options = [...this.options, result];
                    }
                    newItem = result; // Sử dụng result làm newItem để thêm vào tags
                }
            } else if (!this.findInOptions(newItem)) {
                // Nếu không có onAdd, thêm newItem vào options nếu chưa tồn tại
                this.options = [...this.options, newItem];
            }

            // Thêm tag mới vào tags
            this.addTag(newItem);
        }
    }

    private emitOnAdd(value: unknown): Promise<unknown> {
        return new Promise((resolve) => {
            this.onAdd.emit({
                value,
                callback: resolve, // Gửi callback để cha gọi khi xử lý xong
            });
        });
    }

    // Check if tag value already exists in tags
    tagExists(tagValue: unknown): boolean {
        const valueToCheck = this.getStoredValue(tagValue);

        return this.tags.some((tag) => {
            const existingValue = this.getStoredValue(tag);
            return String(existingValue).toLowerCase() === String(valueToCheck).toLowerCase();
        });
    }

    // Find item in options
    findInOptions(item: unknown): unknown {
        const valueToFind = this.getStoredValue(item);

        return find(this.options, (option) => {
            const optionValue = this.getStoredValue(option);
            return String(optionValue).toLowerCase() === String(valueToFind).toLowerCase();
        });
    }

    addTag(tag: unknown): void {
        this.filterValue = null;
        if (!tag) return;

        if (this.mode === 'single') {
            // Single mode: Thay thế tag cũ bằng tag mới
            if (!this.tagExists(tag)) {
                this.tags = [tag]; // Chỉ giữ một tag
                this.showDropdown = false; // Ẩn dropdown sau khi chọn
                this.isFull = true;
            }
        } else {
            // Multi mode: Thêm tag nếu chưa tồn tại
            if (!this.tagExists(tag)) {
                this.tags = [...this.tags, tag];
            }
            this.showDropdown = true; // Giữ dropdown mở để tiếp tục chọn
        }

        // Tính toán valueForm dựa trên mode
        let valueForm = null;
        if (this.tags.length >= 1) {
            if (this.mode === 'single') {
                // Single mode: Lấy giá trị của tag duy nhất
                valueForm = this.optionValue ? get(this.tags[0], this.optionValue) : this.tags[0];
            } else {
                // Multi mode: Trả về toàn bộ mảng tags hoặc mảng giá trị optionValue
                valueForm = this.optionValue ? this.tags.map((tag) => get(tag, this.optionValue)) : this.tags;
            }
        }

        // Emit changes and update form control
        this.onChange(valueForm);
        this.updateDropdownPosition();
    }

    removeTag(tag?: unknown): void {
        if (tag) {
            this.tags = this.tags.filter((t) => this.getStoredValue(t) !== this.getStoredValue(tag));
        } else {
            this.tags = this.tags.slice(0, -1);
        }
        this.isFull = false;

        // Emit changes and update form control
        this.onChange(this.tags);
        this.updateDropdownPosition();
    }

    selectOption(options: unknown): void {
        this.addTag(options);
        this.form.get('tag').setValue('');
        this.tagInputRef.nativeElement.focus();
    }

    @HostListener('document:click', ['$event'])
    clickOutside(event: MouseEvent) {
        if (this.isFull) {
            this.showDropdown = false;
            return;
        }
        // Kiểm tra xem click có nằm trong tagInput hoặc dropdown hay không
        const dropdownElement = this.elementRef.nativeElement.querySelector('.tag-dropdown');

        if (!this.tagInputRef.nativeElement.contains(event.target) && !(dropdownElement && dropdownElement.contains(event.target))) {
            this.showDropdown = false;
        }
    }
    // Lắng nghe sự kiện scroll trên window
    @HostListener('window:scroll', ['$event'])
    onScroll() {
        this.updateDropdownPosition(); // Cập nhật lại vị trí khi scroll
    }
    getDropdownPosition() {
        if (!this.autocompleteRef) return { top: '0px', left: '0px', width: '200px' };
        const inputRect = this.autocompleteRef.nativeElement.getBoundingClientRect();
        const windowHeight = window.innerHeight; // Chiều cao viewport
        const isInputInBottomHalf = inputRect.top > windowHeight / 2; // Input ở nửa dưới màn hình
        const spaceBelow = windowHeight - inputRect.bottom;
        let top: string;
        let dropdownHeight = 200;

        if (this.filteredOptions.length < 6 && this.filteredOptions.length > 0) {
            dropdownHeight = 33 * this.filteredOptions.length;
        } else if (this.filteredOptions.length === 0 && this.filterValue && this.filterValue.trim().length > 0) {
            dropdownHeight = 33;
        }

        if (isInputInBottomHalf && spaceBelow) {
            // Nếu input ở nửa dưới và không đủ chỗ cho dropdown ở dưới, đặt dropdown phía trên
            top = `${inputRect.top - dropdownHeight - 10}px`;
        } else {
            // Nếu input ở nửa trên hoặc đủ chỗ ở dưới, đặt dropdown phía dưới
            top = `${inputRect.bottom + 10}px`;
        }

        return {
            top,
            left: `${inputRect.left}px`,
            width: `${inputRect.width + 10}px`,
        };
    }

    updateDropdownPosition() {
        if (this.isFull) return;
        this.dropdownPosition = this.getDropdownPosition();
    }

    handleDelete(event, index: number, options: unknown): void {
        event.stopPropagation(); // Ngăn chặn sự kiện click lan truyền
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận xóa',
            message: 'Xác nhận xóa lựa chọn này?',
            icon: 'pi pi-exclamation-triangle',
            accept: async () => {
                if (this.onDelete.observers.length > 0) {
                    // Kiểm tra nếu có subscriber
                    const ok = await this.emitOnDelete(options);
                    if (ok) {
                        this.removeOption(options);
                        this.removeTag(options);
                    }
                } else {
                    // Nếu không có onDelete, xóa trực tiếp
                    this.removeOption(options);
                    this.removeTag(options); // Đồng bộ xóa trong tags nếu cần
                }
            },
            reject: () => {
                console.log('Hủy xóa');
            },
        });
    }

    private removeOption(options: unknown): void {
        if (this.optionValue) {
            this.options = this.options.filter((item) => get(item, this.optionValue) !== get(options, this.optionValue));
        } else {
            this.options = this.options.filter((item) => item !== options);
        }
        this.filterOptions(this.form.get('tag')?.value || ''); // Cập nhật lại filteredOptions
    }
    private emitOnDelete(value: unknown): Promise<unknown> {
        return new Promise((resolve) => {
            this.onDelete.emit({
                value,
                callback: resolve, // Gửi callback để cha gọi khi xử lý xong
            });
        });
    }
}
