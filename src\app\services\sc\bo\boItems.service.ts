import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { BoItem, PoInfoDTO } from '../../../models/interface/sc';
import { ApiResponse, GeneralEntity } from 'src/app/models/interface';

@Injectable()
export class BoItemService extends BaseService<BoItem> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/bo-items');
    }

    importFile(file: File, boId: number, poIds?: number[]) {
        const formData: FormData = new FormData();
        formData.append('file', file);
        if (poIds) {
            formData.append('poIds', poIds.toString());
        }
        if (boId) {
            formData.append('boId', boId.toString());
        }
        return this.http.post<ApiResponse>('/sc/api/bo-items/import', formData);
    }

    getByPo(poIds: number[]) {
        return this.http.post<PoInfoDTO[]>(`/sc/api/bo-items/get-by-po`, poIds);
    }

    getTempalate(poIds: number[]) {
        return this.http.post<GeneralEntity>(`/sc/api/bo-items/export-template`, poIds);
    }
}
