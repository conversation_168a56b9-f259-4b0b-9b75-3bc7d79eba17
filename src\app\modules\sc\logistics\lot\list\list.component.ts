import { AfterViewInit, Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { RouterLink } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { Column } from 'src/app/models/interface';
import { TABLE_KEY } from 'src/app/models/constant';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { Lot, LotFilter } from 'src/app/models/interface/sc';
import { AlertService } from 'src/app/shared/services/alert.service';
import { TableCommonService } from 'src/app/shared/table-module/table.common.service';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { MAP_TYPE_SHIPPING, TYPE_SHIPPING } from 'src/app/models/constant/sc';
import { SCDepartmentService } from 'src/app/services/sc/bo/department.service';
import { TabViewModule } from 'primeng/tabview';
import { LotService } from '../../../../../services/sc/lot/lot.service';

@Component({
    selector: 'app-list-lot',
    standalone: true,
    imports: [CommonModule, SubHeaderComponent, RouterLink, ButtonModule, TableCommonModule, TabViewModule],
    templateUrl: './list.component.html',
    styleUrls: ['./list.component.scss'],
    providers: [SCDepartmentService, LotService],
})
export class ListComponent implements OnInit, AfterViewInit {
    @ViewChild('templateType') templateType: TemplateRef<Element>;
    @ViewChild('templateDepartment') templateDepartment: TemplateRef<Element>;
    columns: Column[] = [];
    tableIdDoing: string = TABLE_KEY.BO_TRACKING_DOING;
    stateDoing: QueryObserverBaseResult<Lot[]>;

    tableIdSuccess: string = TABLE_KEY.BO_TRACKING_SUCCESS;
    stateSuccess: QueryObserverBaseResult<Lot[]>;

    //option
    TYPE_SHIPPING = TYPE_SHIPPING;
    MAP_TYPE_SHIPPING = MAP_TYPE_SHIPPING;
    mapDepartment: Record<number, string> = {};

    constructor(
        private alertService: AlertService,
        private tableCommonService: TableCommonService,
        private sCDepartmentService: SCDepartmentService,
        private lotService: LotService,
    ) {}

    ngOnInit(): void {
        this.sCDepartmentService.getPage('query=&page=0&size=100&sort=id,desc').subscribe({
            next: (res) => {
                res.body.forEach((item) => {
                    this.mapDepartment[item.id] = item.name;
                });
            },
        });
        this.tableCommonService
            .init<Lot>({
                tableId: this.tableIdDoing,
                queryFn: (filter, body: unknown) => this.lotService.getPageTableNative(filter, body as LotFilter),
                configFilter: [
                    'ids',
                    'boIds',
                    'poNumber',
                    'lotCode',
                    'declarationCode',
                    'types',
                    'indexShipment',
                    'totalWeight',
                    'packageNumber',
                    'departmentIds',
                    'accountingCode',
                    'note',
                    'requiredArrivedDateStart&requiredArrivedDateEnd',
                    'readyDateStart&readyDateEnd',
                    'status',
                    'supplierName',
                ],
                defaultParams: {
                    status: 0,
                },
            })
            .subscribe({
                next: (res) => {
                    this.stateDoing = res;
                },
            });

        this.tableCommonService
            .init<Lot>({
                tableId: this.tableIdSuccess,
                queryFn: (filter, body: unknown) => this.lotService.getPageTableNative(filter, body as LotFilter),
                configFilter: [
                    'ids',
                    'boIds',
                    'poNumber',
                    'lotCode',
                    'declarationCode',
                    'types',
                    'indexShipment',
                    'totalWeight',
                    'packageNumber',
                    'departmentIds',
                    'accountingCode',
                    'note',
                    'requiredArrivedDateStart&requiredArrivedDateEnd',
                    'readyDateStart&readyDateEnd',
                    'status',
                    'supplierName',
                ],
                defaultParams: {
                    status: 1,
                },
            })
            .subscribe({
                next: (res) => {
                    this.stateSuccess = res;
                },
            });
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.columns = [
                {
                    header: 'Số BO',
                    field: 'boCode',
                    type: 'link',
                    url: './{id}',
                    default: true,
                },
                {
                    field: 'supplierName',
                    header: 'Tên NCC',
                    default: true,
                },
                {
                    field: 'poNumber',
                    header: 'Số PO',
                    default: true,
                },
                {
                    field: 'lotCode',
                    header: 'Số vận đơn',
                    default: true,
                },
                {
                    field: 'declarationCode',
                    header: 'Số tờ khai',
                    default: true,
                },
                { field: 'type', header: 'Phân loại', style: { 'max-width': '8rem' }, body: this.templateType },
                { field: 'indexShipment', header: 'STT shipment Po', type: 'number' },
                { field: 'accountingCode', header: 'Mã kế toán/mã vụ việc' },
                {
                    field: 'readyDate',
                    header: 'Thời gian hàng hóa ready',
                    type: 'date',

                    format: 'dd/MM/yyyy',
                },
                {
                    field: 'requiredArrivedDate',
                    header: 'Thời gian yêu cầu hàng về',
                    type: 'date',

                    format: 'dd/MM/yyyy',
                },
                { field: 'totalWeight', header: 'Khối lượng', type: 'number' },
                { field: 'packageNumber', header: 'Số kiện', type: 'number' },
                { field: 'department', header: 'Phòng ban' },
                { field: 'note', header: 'Ghi chú' },
            ];
        });
    }
    deleteSelected = (ids: number[]) => {
        return this.lotService.batchDelete(ids);
    };
}
