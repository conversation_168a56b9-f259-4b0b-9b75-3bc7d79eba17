import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { InventoryProduct } from '../../../models/interface/sc';

@Injectable()
export class InventoryProductService extends BaseService<InventoryProduct> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/inventory-product');
    }
}
