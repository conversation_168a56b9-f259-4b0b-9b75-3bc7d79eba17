<app-sub-header [items]="itemsHeader" [action]="actionHeader"></app-sub-header>

<ng-template #actionHeader>
    <p-button
        (click)="goToCreatePage()"
        label="Thêm mới"
        size="small"
        severity="success"
    />
</ng-template>

<div style="padding: 1rem 1rem 0 1rem">
    <app-table-common
        [tableId]="tableId"
        [columns]="columns"
        [data]="state.data"
        [loading]="state.isFetching"
        [filterTemplate]="filterAcceptanceDocument"
        [funcDelete]="deleteSelectedTemplate"
        title="<PERSON>h sách hồ sơ nghiệm thu"
    >
        <ng-template #filterAcceptanceDocument>
            <tr>
                <th></th>
                <th [appFilter]="[tableId, 'contractName']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="contractId"
                        [rsql]="true"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            param: 'contractName',
                            rsql: false,
                            url: '/smart-qc/api/acceptance-docs/cbb-contract',
                        }"
                        placeholder="Dự án"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'actionName']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="actionId"
                        [rsql]="true"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            param: 'actionName',
                            rsql: false,
                            url: '/smart-qc/api/acceptance-docs/cbb-action',
                        }"
                        placeholder="Công việc"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'createdBy']"></th>
                <th [appFilter]="[tableId, 'created']"></th>
                <th [appFilter]="[tableId, 'action']">
                    <ng-template #actionAcceptanceDocument let-rowData>
                        <p-button
                            icon="pi pi-arrow-right"
                            (click)="gotoDetailPage(rowData.id)"
                            label=""
                            pTooltip="Chi tiết"
                            tooltipPosition="right"
                            title=""
                        ></p-button>
                    </ng-template>
                </th>
            </tr>
        </ng-template>
    </app-table-common>
</div>
<!--<ng-template #templateName let-rowData>
    <a [routerLink]="rowData.id + '/edit'">{{ rowData.name }}</a>
</ng-template>
<ng-template #templateWorkType let-rowData>
    <div>{{ getWorkTypeByValue(rowData.workType) }}</div>
</ng-template>
<ng-template #hello>
    <button>Edit</button>
</ng-template>-->
