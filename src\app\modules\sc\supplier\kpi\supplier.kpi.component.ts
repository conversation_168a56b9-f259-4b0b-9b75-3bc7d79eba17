import { Component, inject, OnInit } from '@angular/core';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { Column, ValidateMessage } from '../../../../models/interface';
import { TABLE_KEY, TIME_TYPE_SUPPLIER_KPI } from '../../../../models/constant';
import { ButtonModule } from 'primeng/button';
import { HasAnyAuthorityDirective } from '../../../../shared/directives/has-any-authority.directive';
import { Router, RouterLink } from '@angular/router';
import { AuthService } from '../../../../core/auth/auth.service';
import { NgForOf, NgIf } from '@angular/common';
import { TooltipModule } from 'primeng/tooltip';
import { ConfirmationService, MessageService } from 'primeng/api';
import { LoadingService } from '../../../../shared/services/loading.service';
import { AlertService } from '../../../../shared/services/alert.service';
import { CdkDrag, CdkDropList } from '@angular/cdk/drag-drop';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { InputValidationComponent } from '../../../../shared/components/input-validation/input.validation.component';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import Common from '../../../../utils/common';
import { CalendarModule } from 'primeng/calendar';
import CommonValidator from '../../../../utils/common.validator';
import { SupplierKpiService } from 'src/app/services/sc/supplier/supplier.kpi.service';
import { SupplierKpi } from '../../../../models/interface/sc';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';

@Component({
    selector: 'app-sc-score-rule',
    templateUrl: './supplier.kpi.component.html',
    styleUrls: ['supplier.kpi.component.scss'],
    standalone: true,
    imports: [
        TableCommonModule,
        ButtonModule,
        HasAnyAuthorityDirective,
        RouterLink,
        NgIf,
        TooltipModule,
        CdkDrag,
        CdkDropList,
        DropdownModule,
        InputTextModule,
        InputTextareaModule,
        InputValidationComponent,
        NgForOf,
        ReactiveFormsModule,
        CalendarModule,
        SubHeaderComponent,
    ],
    providers: [SupplierKpiService],
})
export class SupplierKpiComponent implements OnInit {
    confirmationService = inject(ConfirmationService);
    loadingService = inject(LoadingService);
    alertService = inject(AlertService);

    state: QueryObserverBaseResult<SupplierKpi[]>;
    columns: Column[] = [];
    rowSelects: SupplierKpi[] = [];
    tableId: string = TABLE_KEY.PO;
    itemsHeader = [{ label: 'Quản lý nhà cung cấp' }, { label: 'Cấu hình quy tắc tính điểm' }];

    deliveryProgressForm: FormGroup;
    priceChangeForm: FormGroup;
    evaluationTimeForm: FormGroup;
    weightForm: FormGroup;

    supplierKpis: SupplierKpi[];

    supplierKpiName = {
        deliverProgress: 'Quy tắc tính điểm tiến độ giao hàng',
        priceChange: 'Quy tắc tính điểm biến động giá',
        evaluationTime: 'Cấu hình thời gian đánh giá',
        weight: 'Cấu hình trọng số',
    };

    timeTypesOption: { label: string; value: number }[] = [
        { label: 'Năm', value: 0 },
        { label: 'Tháng', value: 1 },
    ];

    yearsOption: { label: string; value: number }[] = [];

    // Validate message
    validateMessageTargetDeliveryProgress: ValidateMessage[] = [
        {
            type: 'naturalNumber',
            message: 'Trường số ngày chậm phải là số nguyên dương, tối đa 3 ký tự',
        },
    ];

    validateMessageResultDeliveryProgress: ValidateMessage[] = [
        {
            type: 'decimal',
            message: 'Trường số điểm trừ là số, tối đa một chữ số thập phân sau dấu ,',
        },
    ];

    validateMessageStartPoint: ValidateMessage[] = [
        {
            type: 'positiveNumberSmallerOrEqual100',
            message: 'Trường điểm xuất phát là số nguyên dương, từ 0-100',
        },
    ];

    validateMessageTargetPriceChange: ValidateMessage[] = [
        {
            type: 'decimal',
            message: 'Trường biến động giá là số thập phân, tối đa 2 chữ số sau dấu ,',
        },
    ];

    validateMessageResultPriceChange: ValidateMessage[] = [
        {
            type: 'decimal',
            message: 'Trường số điểm trừ là số thập phân, tối đa 1 chữ số sau dấu ,',
        },
    ];

    validateMessageQuantityWeight: ValidateMessage[] = [
        {
            type: 'naturalNumber2Character',
            message: 'Trường chất lượng là số nguyên dương, tối đa 2 ký tự',
        },
    ];

    validateMessagePriceChangeWeight: ValidateMessage[] = [
        {
            type: 'naturalNumber2Character',
            message: 'Trường biến động giá là số nguyên dương, tối đa 2 ký tự',
        },
    ];

    validateMessageDeliveryProgressWeight: ValidateMessage[] = [
        {
            type: 'naturalNumber2Character',
            message: 'Trường tiến độ giao hàng là số nguyên dương, tối đa 2 ký tự',
        },
    ];

    validateMessageTimeEvaluation: ValidateMessage[] = [
        {
            type: 'endBeforeStart',
            message: 'Thời gian kết thúc phải lớn hơn thời gian bắt đầu',
        },
        {
            type: 'rangeExceedsLimit',
            message: 'Khoảng thời gian tối đa là 36 tháng',
        },
    ];

    constructor(
        private supplierKpiService: SupplierKpiService,
        private router: Router,
        private messageService: MessageService,
        protected authService: AuthService,
        private formBuilder: FormBuilder,
    ) {
        this.deliveryProgressForm = this.formBuilder.group({
            id: ['', [Validators.required]],
            name: [''],
            target: [
                { value: '', disabled: true },
                [Validators.required, CommonValidator.naturalNumberValidator, Validators.maxLength(3)],
            ],
            result: [{ value: '', disabled: true }, [Validators.required, CommonValidator.decimalValidator(1)]],
            startPoint: [
                { value: '', disabled: true },
                [Validators.required, CommonValidator.positiveNumberSmallerOrEqual100Validator],
            ],
        });
        this.priceChangeForm = this.formBuilder.group({
            id: ['', [Validators.required]],
            name: [''],
            target: ['', [Validators.required, CommonValidator.decimalValidator(2)]],
            result: ['', [Validators.required, CommonValidator.decimalValidator(1)]],
            startPoint: ['', [Validators.required, CommonValidator.positiveNumberSmallerOrEqual100Validator]],
        });
        this.evaluationTimeForm = this.formBuilder.group(
            {
                id: ['', [Validators.required]],
                name: [''],
                timeType: [0, [Validators.required]],
                year: ['', [Validators.required]],
                startTime: [null, [Validators.required]],
                endTime: [null, [Validators.required]],
            },
            { validator: CommonValidator.dateRangeValidator(30) },
        );

        this.weightForm = this.formBuilder.group({
            id: ['', [Validators.required]],
            name: [''],
            quantity: ['', [Validators.required, Common.naturalNumber2CharacterValidator]],
            priceChange: ['', [Validators.required, Common.naturalNumber2CharacterValidator]],
            deliveryQuantity: ['', [Validators.required, Common.naturalNumber2CharacterValidator]],
        });
    }

    ngOnInit() {
        // Gen years option
        const currentYear = new Date().toLocaleString('en-US', { year: 'numeric', timeZone: 'Asia/Ho_Chi_Minh' });
        for (let year = Number(currentYear); year >= 2000; year--) {
            this.yearsOption.push({ label: year.toString(), value: year });
        }
        //
        this.loadingService.show();
        this.supplierKpiService.getPage('query=&page=0&size=10&sort=id').subscribe({
            next: (res) => {
                this.supplierKpis = res.body;
                res.body.forEach((item: SupplierKpi) => {
                    if (item.code === 'DELIVERY') {
                        this.deliveryProgressForm.patchValue({
                            id: item.id,
                            name: item.name,
                            target: Common.convertDotToComma(item.target),
                            result: Common.convertDotToComma(item.result),
                            startPoint: item.startPoint,
                        });
                    } else if (item.code === 'PRICE') {
                        this.priceChangeForm.patchValue({
                            id: item.id,
                            name: item.name,
                            target: Common.convertDotToComma(item.target),
                            result: Common.convertDotToComma(item.result),
                            startPoint: item.startPoint,
                        });
                    } else if (item.code === 'TIME') {
                        this.updateValidatorsTimeType(item.timeType);
                        this.evaluationTimeForm.patchValue({
                            id: item.id,
                            name: item.name,
                            timeType: item.timeType,
                            year: item.timeType === TIME_TYPE_SUPPLIER_KPI.YEAR ? item.year : null,
                            startTime:
                                item.timeType === TIME_TYPE_SUPPLIER_KPI.MONTH
                                    ? Common.getDateFromHCMTimeZone(item.startTime)
                                    : null,
                            endTime:
                                item.timeType === TIME_TYPE_SUPPLIER_KPI.MONTH
                                    ? Common.getDateFromHCMTimeZone(item.endTime)
                                    : null,
                        });
                        this.evaluationTimeForm.get('timeType').valueChanges.subscribe((value) => {
                            this.updateValidatorsTimeType(value);
                        });
                    } else if (item.code === 'WEIGHT') {
                        this.weightForm.patchValue({
                            id: item.id,
                            name: item.name,
                            quantity: item.quantity,
                            priceChange: item.priceChange,
                            deliveryQuantity: item.deliveryQuantity,
                        });
                    }
                });
                this.loadingService.hide();
            },
            error: () => {
                this.loadingService.hide();
            },
            complete: () => {},
        });
    }

    updateValidatorsTimeType(timeType: number) {
        const startTimeField = this.evaluationTimeForm.get('startTime');
        const endTimeField = this.evaluationTimeForm.get('endTime');
        const yearField = this.evaluationTimeForm.get('year');

        startTimeField?.clearValidators();
        endTimeField?.clearValidators();
        yearField?.clearValidators();

        if (timeType === TIME_TYPE_SUPPLIER_KPI.YEAR) {
            yearField?.setValidators([Validators.required]);
            // Set year default
            if (!this.evaluationTimeForm.get('year').value) {
                const currentYear = new Date().toLocaleString('en-US', {
                    year: 'numeric',
                    timeZone: 'Asia/Ho_Chi_Minh',
                });
                this.evaluationTimeForm.patchValue({
                    year: Number(currentYear) - 1,
                });
            }

            this.evaluationTimeForm.patchValue({
                startTime: null,
                endTime: null,
            });
        } else if (timeType === TIME_TYPE_SUPPLIER_KPI.MONTH) {
            startTimeField?.setValidators([Validators.required]);
            endTimeField?.setValidators([Validators.required]);

            this.evaluationTimeForm.patchValue({
                year: null,
            });
        }
    }

    saveSupplierKpi() {
        // Common.trimValueInForm(this.subHeaderForm, ['name', 'description']);
        // Check valid
        if (
            this.deliveryProgressForm.valid &&
            this.priceChangeForm.valid &&
            this.evaluationTimeForm.valid &&
            this.weightForm.valid
        ) {
            const updatedSupplierKpis = this.supplierKpis.map((kpi: SupplierKpi) => {
                if (kpi.id === this.deliveryProgressForm.value.id) {
                    return {
                        ...kpi,
                        ...this.deliveryProgressForm.value,
                        target: Common.convertCommaToDot(this.deliveryProgressForm.getRawValue().target),
                        result: Common.convertCommaToDot(this.deliveryProgressForm.getRawValue().result),
                    };
                } else if (kpi.id === this.priceChangeForm.value.id) {
                    return {
                        ...kpi,
                        ...this.priceChangeForm.value,
                        target: Common.convertCommaToDot(this.priceChangeForm.value.target),
                        result: Common.convertCommaToDot(this.priceChangeForm.value.result),
                    };
                } else if (kpi.id === this.evaluationTimeForm.value.id) {
                    return {
                        ...kpi,
                        ...this.evaluationTimeForm.value,
                        startTime:
                            this.evaluationTimeForm.value.timeType === TIME_TYPE_SUPPLIER_KPI.YEAR
                                ? null
                                : Common.getTimeFromHCMTimeZone(this.evaluationTimeForm.value.startTime),
                        endTime:
                            this.evaluationTimeForm.value.timeType === TIME_TYPE_SUPPLIER_KPI.YEAR
                                ? null
                                : Common.getTimeFromHCMTimeZone(this.evaluationTimeForm.value.endTime),
                        year:
                            this.evaluationTimeForm.value.timeType === TIME_TYPE_SUPPLIER_KPI.MONTH
                                ? null
                                : this.evaluationTimeForm.value.year,
                    };
                } else if (kpi.id === this.weightForm.value.id) {
                    return { ...kpi, ...this.weightForm.value };
                }
                return kpi;
            });

            this.loadingService.show();
            this.supplierKpiService.batchUpdate(updatedSupplierKpis).subscribe({
                next: () => {
                    this.loadingService.hide();
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'success',
                        summary: 'Thành công',
                        detail: 'Cập nhật cấu hình quy tắc tính điểm thành công',
                    });
                },
                error: () => {
                    this.loadingService.hide();
                },
                complete: () => {},
            });
        } else {
            Common.markAllAsTouchedForm(this.deliveryProgressForm);
            Common.markAllAsTouchedForm(this.priceChangeForm);
            Common.markAllAsTouchedForm(this.evaluationTimeForm);
            Common.markAllAsTouchedForm(this.weightForm);
        }
    }
}
