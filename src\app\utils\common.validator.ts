import { FormControl, ValidationErrors } from '@angular/forms';

export default class CommonValidator {
    static naturalNumberValidator(control: FormControl) {
        const value = control.value;
        if (!value) {
            return null;
        }
        const isInteger = Number.isInteger(Number(value));
        const isNature = Number(value) > 0;

        if (!isInteger || !isNature) {
            return { naturalNumber: true };
        }
        return null;
    }

    static decimalValidator(decimalPlaces: number) {
        return (control: FormControl) => {
            const value = control.value;
            if (!value) {
                return null;
            }

            const decimalPattern = new RegExp(`^[0-9]+(,[0-9]{1,${decimalPlaces}})?$`);
            if (!decimalPattern.test(value)) {
                return { decimal: true };
            }

            return null;
        };
    }

    static decimalOnlyValidator(decimalPlaces: number) {
        return (control: FormControl) => {
            const value = control.value;
            if (!value) {
                return null;
            }

            const decimalPattern = new RegExp(`^[0-9]+(,[0-9]{1,${decimalPlaces}})$`);

            if (!decimalPattern.test(value)) {
                return { decimalOnly: true };
            }

            return null;
        };
    }

    static positiveNumberSmallerOrEqual100Validator(control: FormControl) {
        const value = control.value;
        const isInteger = Number.isInteger(Number(value));
        const isPositive = Number(value) >= 0;
        const isSmallerOrEqual100 = Number(value) <= 100;

        if (!isInteger || !isPositive || !isSmallerOrEqual100) {
            return { positiveNumberSmallerOrEqual100: true };
        }
        return null;
    }

    static dateRangeValidator(maxMonths: number) {
        return (group: FormControl): ValidationErrors | null => {
            const startTime = group.get('startTime')?.value;
            const endTime = group.get('endTime')?.value;

            if (!startTime || !endTime) {
                return null; // Không cần kiểm tra nếu một trong hai giá trị là null
            }

            const startDate = new Date(startTime);
            const endDate = new Date(endTime);

            // Kiểm tra endTime phải lớn hơn startTime
            if (endDate <= startDate) {
                return { endBeforeStart: true };
            }

            // Kiểm tra khoảng cách không quá 30 tháng
            const diffInMonths =
                (endDate.getFullYear() - startDate.getFullYear()) * 12 + (endDate.getMonth() - startDate.getMonth());
            if (diffInMonths > maxMonths) {
                return { rangeExceedsLimit: true };
            }

            return null;
        };
    }
}
