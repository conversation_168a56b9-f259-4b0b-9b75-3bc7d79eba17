<style>
    :host::ng-deep {
        .add-checklist-header-button {
            display: flex;
            justify-content: flex-end;
        }
    }
</style>
<app-sub-header [items]="itemsHeader" [action]="dcm"></app-sub-header>

<ng-template #dcm>
    <p-button *ngIf="authService.isAdminOrPM()" label="Lưu" (click)="saveTemplate()" severity="success" size="small" />
    <p-button label="Đóng" (click)="gotoListPage()" severity="secondary" size="small" />
</ng-template>
<div class="tw-p-5">
    <form [formGroup]="templateForm" class="tw-bg-white tw-p-5 tw-rounded-xl tw-mb-5">
        <div class="tw-grid lg:tw-grid-cols-3 md:tw-grid-cols-1 sm:tw-grid-cols-1 tw-gap-4 tw-mb-3">
            <div class="tw-flex tw-flex-col tw-space-y-3">
                <div class="tw-font-bold">Công việc <span class="tw-text-red-600">(*)</span>:</div>
                <div>
                    <p-dropdown
                        [style]="{ width: '100%' }"
                        id="workTypeTemplate"
                        [options]="workTypes"
                        formControlName="workType"
                        [disabled]="template.used === 1 || isViewOnly"
                        placeholder="Chọn công việc"
                    >
                    </p-dropdown>
                </div>
                <app-input-validate [control]="templateForm.get('workType')" fieldName="loại công việc" />
            </div>
            <div class="tw-flex tw-flex-col tw-space-y-3">
                <div class="tw-font-bold">Mã mẫu kiểm tra:</div>
                <div>
                    <input
                        [value]="template.code ? template.code : 'Hệ thống tự sinh'"
                        [readOnly]="true"
                        style="width: 100%"
                        type="text"
                        pInputText
                    />
                </div>
            </div>
            <div class="tw-flex tw-flex-col tw-space-y-3">
                <div class="tw-font-bold">Tên mẫu kiểm tra <span class="tw-text-red-600">(*)</span>:</div>
                <div>
                    <input
                        style="width: 100%"
                        placeholder="Nhập tên mẫu kiểm tra"
                        type="text"
                        pInputText
                        formControlName="name"
                        [readOnly]="isViewOnly"
                    />
                </div>
                <app-input-validate [control]="templateForm.get('name')" fieldName="tên mẫu kiểm tra" />
            </div>
        </div>
        <div class="tw-flex tw-flex-col tw-space-y-3">
            <div class="tw-font-bold">Mô tả :</div>
            <div class="tw-w-full">
                <textarea
                    [readOnly]="isViewOnly"
                    style="width: 100%; max-width: 100%"
                    rows="5"
                    pInputTextarea
                    formControlName="description"
                >
                </textarea>
            </div>
        </div>
    </form>
    <div class="tw-grid lg:tw-grid-cols-3 tw-grid-cols-1 tw-gap-4">
        <div class="tw-bg-white tw-rounded-xl tw-p-4 example-container" style="height: 600px">
            <div class="tw-p-2" style="border-bottom: 2px #e0e0e0 solid">
                <div class="tw-font-bold">Danh mục</div>
                <div *ngIf="authService.isAdminOrPM()" class="add-checklist-header-button tw-mb-3">
                    <p-button
                        (click)="openModalAddChecklistHeader()"
                        class="tw-mr-2"
                        severity="success"
                        size="small"
                        label="Thêm danh mục"
                        [raised]="true"
                        icon="pi pi-plus"
                        [disabled]="isViewOnly"
                    />
                </div>
            </div>
            <div
                style="height: 500px; overflow-y: auto"
                cdkDropList
                #todoList="cdkDropList"
                [cdkDropListData]="selectedChecklistHeader"
                class="drag-custom-list"
                (cdkDropListDropped)="dropChecklistHeader($event)"
            >
                <div
                    cdkDrag
                    (click)="selectChecklistHeader(i)"
                    [ngClass]="{
                        '  tw-bg-blue-100': selectedChecklistHeaderIndex === i,
                        'border-1 border-gray-200 ': selectedChecklistHeaderIndex !== i,
                    }"
                    class="drag-custom-box tw-p-3 tw-rounded-xl tw-mt-4 tw-flex tw-justify-between tw-cursor-pointer"
                    style="border: 1px #eeeeee solid; min-height: 70px"
                    *ngFor="let checklistHeader of template.checklists; let i = index"
                >
                    <div class="text-overflow-ellipsis" style="max-width: calc(100% - 100px)">
                        {{ i + 1 }}. {{ checklistHeader.name }}
                    </div>
                    <div class="tw-flex tw-items-center tw-gap-x-2 tw-flex-row">
                        <p-button
                            *ngIf="!isViewOnly"
                            icon="pi pi-pencil"
                            severity="info"
                            size="small"
                            (click)="openModalEditHeader(i)"
                        />
                        <p-button
                            *ngIf="template.used !== 1 && !isViewOnly"
                            icon="pi pi-trash"
                            severity="danger"
                            size="small"
                            (click)="deleteChecklistHeader(i)"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="tw-bg-white tw-rounded-xl tw-p-4 example-container" style="height: 600px">
            <div class="tw-p-2" style="border-bottom: 2px #e0e0e0 solid">
                <div class="tw-font-bold">Thành phần</div>
                <div *ngIf="authService.isAdminOrPM()" class="add-checklist-header-button tw-mb-3">
                    <p-button
                        [disabled]="
                            selectedChecklistHeaderIndex === null ||
                            selectedChecklistHeaderIndex === undefined ||
                            selectedChecklistHeader?.nonSubHeader === 1 ||
                            isViewOnly
                        "
                        (click)="openAddSubHeaderModal()"
                        class=""
                        severity="success"
                        size="small"
                        label="Thêm thành phần"
                        [raised]="true"
                        icon="pi pi-plus"
                    />
                </div>
            </div>
            <div
                class="tw-p-3 tw-rounded-xl tw-mt-4"
                style="border: 1px #eeeeee solid"
                *ngIf="selectedChecklistHeader?.checklists?.length === 0"
            >
                Hiện tại không có bản ghi nào
            </div>
            <div
                style="height: 500px; overflow-y: auto"
                [cdkDropListDisabled]="template.used === 1 || isViewOnly"
                *ngIf="selectedChecklistHeader?.nonSubHeader === 0"
                cdkDropList
                #todoList="cdkDropList"
                [cdkDropListData]="selectedChecklistHeader.checklists"
                class="drag-custom-list"
                (cdkDropListDropped)="dropSubHeader($event)"
            >
                <div
                    cdkDrag
                    (click)="selectSubHeader(i)"
                    [ngClass]="{
                        '  tw-bg-blue-100': selectedSubHeaderIndex === i,
                        'border-1 border-gray-200 ': selectedSubHeaderIndex !== i,
                    }"
                    class="drag-custom-box tw-p-3 tw-rounded-xl tw-mt-4 tw-flex tw-justify-between tw-cursor-pointer"
                    style="border: 1px #eeeeee solid; min-height: 70px"
                    *ngFor="let subHeader of selectedChecklistHeader.checklists; let i = index"
                >
                    <div class="text-overflow-ellipsis" style="max-width: calc(100% - 100px)">
                        {{ i + 1 }}. {{ subHeader.name }}
                    </div>
                    <div class="tw-flex tw-items-center tw-gap-x-2 tw-flex-row">
                        <p-button
                            *ngIf="!isViewOnly"
                            class="tw-mr-2"
                            icon="pi pi-pencil"
                            severity="info"
                            size="small"
                            (click)="openModalEditSubHeader(i)"
                        />
                        <p-button
                            *ngIf="template.used !== 1 && !isViewOnly"
                            icon="pi pi-trash"
                            severity="danger"
                            size="small"
                            (click)="deleteSubHeader(i)"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="tw-bg-white tw-rounded-xl tw-p-4 example-container" style="height: 600px">
            <div class="tw-p-2" style="border-bottom: 2px #e0e0e0 solid">
                <div class="tw-font-bold">Chi tiết</div>
                <div *ngIf="authService.isAdminOrPM()" class="add-checklist-header-button tw-mb-3">
                    <p-button
                        [disabled]="selectedChecklistHeader === null || selectedChecklistHeader === undefined || isViewOnly"
                        (click)="openAddCheckDetailModal()"
                        class=""
                        severity="success"
                        size="small"
                        label="Thêm chi tiết"
                        [raised]="true"
                        icon="pi pi-plus"
                    />
                </div>
            </div>
            <div
                class="tw-p-3 tw-rounded-xl tw-mt-4"
                style="border: 1px #eeeeee solid"
                *ngIf="selectedChecklistHeader?.checklists?.length === 0"
            >
                Hiện tại không có bản ghi nào
            </div>
            <div
                style="height: 500px; overflow-y: auto"
                [cdkDropListDisabled]="template.used === 1 || isViewOnly"
                *ngIf="selectedChecklistHeader?.nonSubHeader === 0"
                cdkDropList
                #todoList="cdkDropList"
                [cdkDropListData]="selectedSubHeader?.checklists"
                class="drag-custom-list"
                (cdkDropListDropped)="dropChecklistDetail($event)"
            >
                <div
                    cdkDrag
                    (click)="selectChecklistDetail(i)"
                    [ngClass]="{
                        '  tw-bg-blue-100': selectedChecklistDetailIndex === i,
                        'border-1 border-gray-200 ': selectedChecklistDetailIndex !== i,
                    }"
                    class="drag-custom-box tw-p-3 tw-rounded-xl tw-mt-4 tw-flex tw-justify-between tw-cursor-pointer"
                    style="border: 1px #eeeeee solid; min-height: 70px"
                    *ngFor="let checklistDetail of selectedSubHeader?.checklists; let i = index"
                >
                    <div class="text-overflow-ellipsis" style="max-width: calc(100% - 100px)">
                        {{ i + 1 }}. {{ checklistDetail.name }}
                    </div>
                    <div style="margin-top: 10px">
                        <p-button
                            *ngIf="!isViewOnly"
                            class="tw-mr-2"
                            icon="pi pi-pencil"
                            severity="info"
                            size="small"
                            (click)="openModalEditChecklistDetail(i, checklistDetail)"
                        />
                        <p-button
                            *ngIf="template.used !== 1 && !isViewOnly"
                            icon="pi pi-trash"
                            severity="danger"
                            size="small"
                            (click)="deleteChecklistDetail(i, checklistDetail)"
                        />
                    </div>
                </div>
            </div>
            <div
                style="height: 500px; overflow-y: auto"
                [cdkDropListDisabled]="template.used === 1 || isViewOnly"
                *ngIf="selectedChecklistHeader?.nonSubHeader === 1"
                cdkDropList
                #todoList="cdkDropList"
                [cdkDropListData]="selectedChecklistHeader?.checklists"
                class="drag-custom-list"
                (cdkDropListDropped)="dropChecklistDetail($event)"
            >
                <div
                    cdkDrag
                    (click)="selectChecklistDetail(i)"
                    [ngClass]="{
                        '  tw-bg-blue-100': selectedChecklistDetailIndex === i,
                        'border-1 border-gray-200 ': selectedChecklistDetailIndex !== i,
                    }"
                    class="drag-custom-box tw-p-3 tw-rounded-xl tw-mt-4 tw-flex tw-justify-between tw-cursor-pointer"
                    style="border: 1px #eeeeee solid; min-height: 70px"
                    *ngFor="let checklistDetail of selectedChecklistHeader?.checklists; let i = index"
                >
                    <div class="tw-text-vertical-center">{{ i + 1 }}. {{ checklistDetail.name }}</div>
                    <div style="margin-top: 10px">
                        <p-button
                            *ngIf="!isViewOnly"
                            class="tw-mr-2"
                            icon="pi pi-pencil"
                            severity="info"
                            size="small"
                            (click)="openModalEditChecklistDetail(i, checklistDetail)"
                        />
                        <p-button
                            *ngIf="template.used !== 1 && !isViewOnly"
                            icon="pi pi-trash"
                            severity="danger"
                            size="small"
                            (click)="deleteChecklistDetail(i, checklistDetail)"
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<p-dialog
    [header]="isEditHeader ? 'Sửa danh mục' : 'Thêm danh mục'"
    [modal]="true"
    [(visible)]="isOpenModalAddCheckHeader"
    [style]="{ width: '45rem', top: '' }"
>
    <form [formGroup]="checklistHeaderForm" (ngSubmit)="isEditHeader ? saveChecklistHeader() : addChecklistHeader()">
        <div class="tw-mb-5">
            <div class="tw-mb-1">Tiêu đề <span class="tw-text-red-600">(*)</span>:</div>
            <input
                placeholder="Nhâp tiêu đề"
                formControlName="name"
                style="width: 100%"
                pInputText
                id="name"
                class="flex-auto"
                autocomplete="off"
            />
            <app-input-validate [control]="checklistHeaderForm.get('name')" fieldName="tiêu đề" />
        </div>
        <div class="tw-mb-5">
            <div class="tw-mb-1">Mô tả :</div>
            <input
                placeholder="Nhập mô tả"
                formControlName="description"
                style="width: 100%"
                pInputText
                id="description"
                class="flex-auto"
                autocomplete="off"
            />
            <app-input-validate [control]="checklistHeaderForm.get('description')" fieldName="mô tả" />
        </div>
        <div class="flex justify-content-end gap-2">
            <p-button label="Lưu" severity="success" type="submit" />
            <p-button label="Đóng" severity="secondary" (click)="isOpenModalAddCheckHeader = false" />
        </div>
    </form>
</p-dialog>

<p-dialog
    [header]="isEditSubHeader ? 'Sửa thành phần' : 'Thêm thành phần'"
    [modal]="true"
    [(visible)]="isOpenModalAddSubHeader"
    [style]="{ width: '45rem', top: '' }"
>
    <form [formGroup]="subHeaderForm" (ngSubmit)="isEditSubHeader ? saveSubHeader() : addSubHeader()">
        <div class="tw-mb-5">
            <div class="tw-mb-1">Tiêu đề <span class="tw-text-red-600">(*)</span>:</div>
            <input
                placeholder="Nhâp tiêu đề"
                formControlName="name"
                style="width: 100%"
                pInputText
                id="name"
                class="flex-auto"
                autocomplete="off"
            />
            <app-input-validate [control]="subHeaderForm.get('name')" fieldName="tiêu đề" />
        </div>
        <div class="tw-mb-5">
            <div class="tw-mb-1">Mô tả :</div>
            <input
                placeholder="Nhập mô tả"
                formControlName="description"
                style="width: 100%"
                pInputText
                id="description"
                class="flex-auto"
                autocomplete="off"
            />
            <app-input-validate [control]="subHeaderForm.get('description')" fieldName="mô tả" />
        </div>
        <div class="flex justify-content-end gap-2">
            <p-button label="Lưu" severity="success" type="submit" />
            <p-button label="Đóng" severity="secondary" (click)="isOpenModalAddSubHeader = false" />
        </div>
    </form>
</p-dialog>

<p-dialog
    [header]="isEditChecklistDetail ? 'Sửa chi tiết' : 'Thêm chi tiết'"
    [modal]="true"
    [(visible)]="isOpenModalAddCheckDetail"
    [style]="{ width: '45rem', top: '' }"
>
    <form
        [formGroup]="checklistDetailForm"
        (ngSubmit)="isEditChecklistDetail ? saveChecklistDetail() : addChecklistDetail()"
    >
        <div class="tw-mb-5">
            <div class="tw-mb-1">Tiêu đề <span class="tw-text-red-600">(*)</span>:</div>
            <input
                placeholder="Nhâp tiêu đề"
                formControlName="name"
                style="width: 100%"
                pInputText
                id="header"
                class="flex-auto"
                autocomplete="off"
            />
            <app-input-validate [control]="checklistDetailForm.get('name')" fieldName="tiêu đề" />
        </div>
        <div class="tw-mb-5">
            <div class="tw-mb-1">Kiểu dữ liệu <span class="tw-text-red-600">(*)</span>:</div>
            <p-dropdown
                [style]="{ width: '100%' }"
                id="dropdownField"
                [options]="dataTypeOptions"
                formControlName="type"
                [disabled]="template.used === 1 && isEditChecklistDetail"
                placeholder="Chọn kiểu dữ liệu"
            >
            </p-dropdown>
            <app-input-validate [control]="checklistDetailForm.get('type')" fieldName="kiểu dữ liệu" />
        </div>
        <div *ngIf="checklistDetailForm.get('type').value === CheckListType.IMAGE" class="tw-mb-5">
            <div class="tw-mb-1">Số lượng hình ảnh <span class="tw-text-red-600">(*)</span>:</div>
            <input
                placeholder="Nhập số lượng ảnh"
                type="number"
                formControlName="numberImage"
                style="width: 100%"
                pInputText
                id="numberImage"
                class="flex-auto"
                autocomplete="off"
            />
            <app-input-validate
                [control]="checklistDetailForm.get('numberImage')"
                fieldName="số lượng hình ảnh"
                [validateMessage]="validateMessageNumberImage"
            />
        </div>
        <div
            *ngIf="
                checklistDetailForm.get('type').value === CheckListType.OPTION ||
                checklistDetailForm.get('type').value === CheckListType.YES_NO
            "
            class="tw-mb-5"
        >
            <div class="tw-mb-1">Giá trị <span class="tw-text-red-600">(*)</span>:</div>
            <input
                placeholder="Các giá trị cách nhau bởi dấu (,). VD: abc, def"
                type="text"
                formControlName="value"
                style="width: 100%"
                pInputText
                id="value"
                class="flex-auto"
                autocomplete="off"
            />
            <app-input-validate
                [control]="checklistDetailForm.get('value')"
                fieldName="giá trị"
                [validateMessage]="validateMessageValue"
            />
        </div>
        <div class="tw-mb-5">
            <div class="tw-mb-1">Nội dung hướng dẫn:</div>
            <textarea
                placeholder="Nhập mô tả"
                style="width: 100%; height: 100px"
                id="description"
                formControlName="description"
            ></textarea>
            <app-input-validate [control]="checklistDetailForm.get('description')" fieldName="mô tả" />
        </div>
        <div class="flex justify-content-end gap-2">
            <p-button label="Lưu" severity="success" type="submit" />
            <p-button label="Đóng" severity="secondary" (click)="isOpenModalAddCheckDetail = false" />
        </div>
    </form>
</p-dialog>
