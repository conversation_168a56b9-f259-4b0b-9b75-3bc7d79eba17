<app-sub-header [items]="itemsHeader" [action]="actionHeader"></app-sub-header>
<ng-template #actionHeader>
    <p-button label="Tạo mới" routerLink="create" severity="success" />
</ng-template>
<div style="padding: 1rem 1rem 0 1rem">
    <app-table-common
        [tableId]="tableId"
        [columns]="columns"
        [data]="state.data"
        [loading]="state.isFetching"
        [filterTemplate]="filterTemplate"
        [funcDelete]="deleteSelectedProcess"
        [rowSelectable]="rowSelectable"
        name="Danh sách tiến trình"
    >
        <ng-template #filterTemplate>
            <tr>
                <th></th>
                <th [appFilter]="[tableId, 'name']">
                    <app-filter-table [tableId]="tableId" field="name"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'description']">
                    <app-filter-table [tableId]="tableId" field="description"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'keyEntity']">
                    <app-filter-table [tableId]="tableId" field="keyEntity"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'arrange']"></th>
                <th [appFilter]="[tableId, 'active']"></th>
                <th [appFilter]="[tableId, 'created']"></th>
            </tr>
        </ng-template>

        <ng-template #templateActive let-rowData>
            <p-inputSwitch [(ngModel)]="rowData.active" readonly="true" (click)="handleActive(rowData)" />
        </ng-template>
        <ng-template #templateArrange let-rowData>
            <p-inputSwitch [(ngModel)]="rowData.arrange" readonly="true" />
        </ng-template>
    </app-table-common>
</div>
