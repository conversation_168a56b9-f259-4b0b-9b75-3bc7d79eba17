<p-button [label]="label" (onClick)="openDialog()" [severity]="severity" [class]="classButton"></p-button>

<p-dialog
    [header]="header"
    [(visible)]="isVisible"
    [modal]="true"
    [breakpoints]="{ '1199px': '40vw', '575px': '30vw' }"
    [style]="{ width: '40vw' }"
    (onHide)="closeDialog()"
    *ngIf="formGroup"
>
    <hr style="margin: 0" />
    <br />
    <app-form [formGroup]="formGroup">
        <app-button-group-file
            [simpleUpload]="null"
            [types]="types"
            [disabled]="disabled"
            (onFileSelected)="handleSelectFile($event)"
            (onClearFile)="handleClearFile()"
            (onClickDowload)="handleClickDownload()"
            [file]="formGroup.get('file').value"
            [urlError]="urlError"
            [urlTemplate]="urlTemplate"
            [service]="service"
        ></app-button-group-file>
        <i *ngIf="description" class="tw-text-gray-500 tw-mt-2 tw-text-sm">{{ description }}</i>
        <br />

        <ng-container
            [ngTemplateOutlet]="formTemplate"
            [ngTemplateOutletContext]="{
                $implicit: formGroup,
            }"
        ></ng-container>
        <br />
    </app-form>

    <ng-template pTemplate="footer">
        <div>
            <p-button
                severity="primary"
                type="submit"
                [disabled]="formGroup.invalid"
                size="small"
                (click)="handleUpload()"
                >Xác nhận</p-button
            >
            <p-button
                label="Hủy"
                [text]="true"
                [raised]="true"
                size="small"
                severity="secondary"
                (click)="closeDialog()"
            ></p-button>
        </div>
    </ng-template>
</p-dialog>
