<ng-container *ngFor="let section of sections; let idx = index">
    <div class="section-wrapper">
        <p-panel [header]="idx + 1 + '. ' + section.title" [toggleable]="true">
            <!-- RDBOM: chia 2 cột riêng -->
            <ng-container *ngIf="section.title === 'RD BOM'; else defaultSection">
                <div class="rdbom-wrapper">
                    <!-- cột trái: bảng -->
                    <div class="rdbom-left">
                        <app-edit-table-section
                            [columns]="section.columns"
                            [initialRows]="section.rows"
                            [isHideAction]="true"
                            (rowsChange)="section.rows = $event"
                        >
                        </app-edit-table-section>
                    </div>
                    <!-- cột phải: nút + link -->
                    <div class="rdbom-right">
                        <div class="rdbom-actions">
                            <p-button label="Check RD BOM và CAD File" />
                            <a class="tw-text-right mt-4" href="#" (click)="onDownloadResult(section)">T<PERSON><PERSON> kết qu<PERSON></a>
                        </div>
                    </div>
                </div>
            </ng-container>

            <!-- Mặc định cho các section khác -->
            <ng-template #defaultSection>
                <app-edit-table-section
                    [columns]="section.columns"
                    [initialRows]="section.rows"
                    (rowsChange)="onSectionRowsChange(section, $event)"
                    (saveRowEvent)="onSaveSectionRow(section, $event)"
                    (deleteRowEvent)="onDeleteRow($event)"
                    [mode]="mode"
                    [reloadTrigger]="reloadTrigger"
                >
                </app-edit-table-section>
            </ng-template>
        </p-panel>
    </div>
</ng-container>
<!-- <div class="tw-flex tw-gap-4 tw-justify-center"> -->
<!-- <p-button label="Lưu" size="small" severity="success" (click)="handleSave()"></p-button> -->

<!-- <p-button
        *ngIf="!isEditMode; else deleteBtn"
        label="Hủy"
        size="small"
        styleClass="p-button-danger"
        (click)="handleCancel()"
    ></p-button> -->

<!-- <ng-template #deleteBtn>
        <p-button label="Xóa" size="small" severity="danger" (click)="handleDeleteVersion()"></p-button>
    </ng-template> -->
<!-- </div> -->
