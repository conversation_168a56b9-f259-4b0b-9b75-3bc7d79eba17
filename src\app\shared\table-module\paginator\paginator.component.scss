.paginator-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 3.25rem;
    padding: 0;
    font-size: 14px;
    gap: 1rem;
    background-color: #F5F7FA;
}

.paginator-info {
    font-size: 14px;
}

.paginator-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
}

.paginator-controls > .paginator-controls-item {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    align-items: center;
    gap: 1.5rem;
    padding: 0 0.75rem;
    border-right: 1px solid #ebeff5;
}

.paginator-reload {
    margin: 0 0 0 1rem;
}

:host ::ng-deep .p-inputnumber .p-inputtext {
    width: 48px;
    height: 32px;
    font-size: 14px;
}
:host ::ng-deep #choose-size > .p-dropdown {
    height: 32px;
    width: 64px;
}
:host ::ng-deep #choose-size > .p-dropdown-trigger {
    width: 20px;
    font-size: 14px;
}

:host ::ng-deep .p-button-secondary {
    border: 1px solid var(--surface-200);
    border-radius: 6px;
}

.paginator-goto-page {
    display: none !important;
}

@media (min-width: 992px) {
    .paginator-goto-page {
        display: flex !important;
    }

    .paginator-controls > .paginator-controls-item {
        gap: 1rem;
    }
}

@media (min-width: 768px) {
    .paginator-container {
        padding: 0 1rem;
    }
    .paginator-controls {
        justify-content: flex-end;
    }
}
