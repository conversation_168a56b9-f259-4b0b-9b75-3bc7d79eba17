import { canAuthorize } from '../../../core/auth/auth.guard';

export const PSRouting = {
    path: 'production-software',
    title: '<PERSON>u<PERSON>n lý phầm mềm hỗ trợ sản xuất',

    children: [
        {
            path: '',
            title: '<PERSON><PERSON> sách <PERSON>',
            canActivate: [canAuthorize],
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'software_view'] },
            loadComponent: () => import('./list/list.component').then((c) => c.ProductionSoftwareListComponent),
        },
    ],
};
