// src/app/modules/pms/shared/services/product.service.ts
import { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { catchError, throwError, Observable } from 'rxjs';
import { ParamsTable } from 'src/app/shared/table-module/table.common.service';
import { Product } from 'src/app/models/product.interface';

@Injectable({ providedIn: 'root' })
export class ProductService {
    // sử dụng injection mới của Angular
    #http = inject(HttpClient);

    /**
     * Phân trang + lọc
     * @param paramsTable chứa pageable, rsql
     * @param filter chứa các trường cần lọc (ví dụ: lineId, categoryId,…)
     */
    getPage({ pageable, rsql }: ParamsTable, filter: Partial<Product>): Observable<Product[]> {
        let params = new HttpParams()
            .set('service', 'PmsService') // nếu cần
            .appendAll(pageable);

        // build query string từ filter
        Object.entries(filter).forEach(([key, value]) => {
            if (value != null) {
                params = params.append(key, value.toString());
            }
        });

        // api giả định: /api/products/search
        return this.#http
            .get<Product[]>('/api/products/search', { params, observe: 'response' })
            .pipe(catchError(this.handleError));
    }

    /** Lấy tất cả sản phẩm (không phân trang) */
    getAll(): Observable<Product[]> {
        return this.#http.get<Product[]>('/api/products').pipe(catchError(this.handleError));
    }

    /** Lấy chi tiết 1 product theo ID */
    getOne(id: number): Observable<Product> {
        return this.#http.get<Product>(`/api/products/${id}`).pipe(catchError(this.handleError));
    }

    /** Tạo mới */
    create(product: Product): Observable<Product> {
        return this.#http.post<Product>('/api/products', product).pipe(catchError(this.handleError));
    }

    /** Cập nhật */
    update(product: Product): Observable<Product> {
        return this.#http.put<Product>(`/api/products/${product.id}`, product).pipe(catchError(this.handleError));
    }

    /** Xóa 1 bản ghi */
    delete(id: number): Observable<void> {
        return this.#http.delete<void>(`/api/products/${id}`).pipe(catchError(this.handleError));
    }

    /** Xóa nhiều bản ghi cùng lúc */
    deleteMany(ids: number[]): Observable<number[]> {
        return this.#http.post<number[]>('/api/products/batch-delete', ids).pipe(catchError(this.handleError));
    }

    /** Xử lý chung lỗi HTTP */
    private handleError(error: HttpErrorResponse) {
        // có thể tuỳ chỉnh thông báo, logging, …
        console.error('API error', error);
        return throwError(() => error);
    }
}
