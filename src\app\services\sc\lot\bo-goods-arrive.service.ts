import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { BoGoodsArrive } from '../../../models/interface/sc';
import { ApiResponse } from 'src/app/models/interface';

@Injectable()
export class BoGoodsArriveService extends BaseService<BoGoodsArrive> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/bo-goods-arrive');
    }

    importFile(files: File[]) {
        const formData: FormData = new FormData();
        files.forEach((file) => formData.append('files', file));

        return this.http.post<ApiResponse>('/sc/api/bo-goods-arrive/import-file', formData);
    }

    getByLot(lotId: number) {
        return this.http.get<BoGoodsArrive>('/sc/api/bo-goods-arrive/get-info', {
            params: { lotId },
        });
    }
}
