import { FormGroup, AbstractControl, ValidatorFn, AbstractControlOptions, AsyncValidatorFn, FormArray, FormBuilder } from '@angular/forms';

// Định nghĩa kiểu generic cho controlsConfig
type ControlsConfig<T> = {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    [K in keyof T]: any | [any, ValidatorFn | ValidatorFn[] | null, AsyncValidatorFn | AsyncValidatorFn[] | null];
};

export interface AbstractControlCustom extends AbstractControl {
    isSubmited: boolean;
    countSubmit: number;
    lastSubmitTime: number;
    markAsSubmit: () => void;
}

// Kế thừa trực tiếp từ FormGroup
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export class FormGroupCustom<T extends object = any> extends FormGroup {
    constructor(formBuilder: FormBuilder, controlsConfig: ControlsConfig<T>, options?: AbstractControlOptions) {
        super(formBuilder.group(controlsConfig, options).controls); // Sử dụng FormBuilder để tạo FormGroup
        // Khởi tạo trạng thái submit cho các control con
        this.initializeSubmitState(this);
    }

    // Getter để truy cập chính instance này như một FormGroup
    get form(): FormGroup {
        return this;
    }

    // Khởi tạo trạng thái submit cho tất cả các control
    private initializeSubmitState(control: AbstractControl): void {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (control as any).isSubmited = false;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (control as any).countSubmit = 0;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (control as any).lastSubmitTime = null;

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (control as any).markAsSubmit = () => this.markAsSubmit(control); // Gắn phương thức submit

        if (control instanceof FormGroup || control instanceof FormArray) {
            Object.values(control.controls).forEach((child) => this.initializeSubmitState(child));
        }
    }

    // Đánh dấu một control và các control con của nó là đã submit
    private markAsSubmit(control: AbstractControl, isProcessing: Set<AbstractControl> = new Set()): void {
        if (isProcessing.has(control)) return; // Ngăn chặn đệ quy vô hạn
        isProcessing.add(control);
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (control as any).isSubmited = true;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (control as any).countSubmit = ((control as any).countSubmit || 0) + 1;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (control as any).lastSubmitTime = new Date();
        isProcessing.delete(control);

        return;
        // control.markAllAsTouched();
        // control.updateValueAndValidity();

        // // if (control instanceof FormGroup || control instanceof FormArray) {
        // //     Object.values(control.controls).forEach((child) => this.markControlAsSubmited(child));
        // // }
    }

    // Reset trạng thái form và các control con
    resetForm(): void {
        this.resetControls(this);
        super.reset(); // Gọi reset của FormGroup
    }

    // Reset trạng thái submit của control và các control con
    private resetControls(control: AbstractControl): void {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (control as any).isSubmited = false;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (control as any).countSubmit = 0;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (control as any).lastSubmitTime = null;

        if (control instanceof FormGroup || control instanceof FormArray) {
            Object.values(control.controls).forEach((child) => this.resetControls(child));
        }
    }
}
