import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ImageUpload } from '../models/interface/smart-qc';

@Injectable({
    providedIn: 'root',
})
export class MinioService {
    private apiUrl = '/smart-qc/api/minio/upload';

    constructor(private http: HttpClient) {}
    uploadOne(file: File): Observable<string[]> {
        const formData = new FormData();
        formData.append('files', file);

        return this.http.post<string[]>(this.apiUrl, formData);
    }

    uploadMany(files: File[], contractId: number, taskId: number): Observable<ImageUpload[]> {
        const formData = new FormData();
        files.forEach((file) => {
            formData.append('files', file);
        });
        formData.append('contractId', contractId.toString());
        formData.append('taskId', taskId.toString());

        return this.http.post<ImageUpload[]>(this.apiUrl, formData);
    }
}
