import { Component, ElementRef, EventEmitter, Input, OnInit, Output, SimpleChanges, ViewChild, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FileUpload, FileUploadModule } from 'primeng/fileupload';
import { ButtonModule } from 'primeng/button';
import { AlertService } from '../../services/alert.service';
import { environment } from 'src/environments/environment';
import { TooltipModule } from 'primeng/tooltip';

/**
 * Custom upload component using PrimeNG FileUpload
 */
@Component({
    standalone: true,
    selector: 'app-upload-custom',
    imports: [CommonModule, FileUploadModule, ButtonModule, TooltipModule],
    templateUrl: './upload-custom.component.html',
    styleUrls: ['./upload-custom.component.scss'],
})
export class UploadCustomComponent implements OnInit {
    /** Chuỗi accept giống attribute HTML: ví dụ ".pdf,.xlsx,image/*" */
    @Input() accept: string = '';
    /** Số file tối đa */
    @Input() limit: number = 1;
    /** Disable control */
    @Input() disabled: boolean = false;
    @Input() fileNamePattern?: RegExp;
    @Input() fileNamePatternMessage?: string;
    /** nếu true: sai format sẽ error + clear + return; nếu false: chỉ warning và vẫn emit tiếp */
    @Input() strictFileNamePattern = true;
    /** ← Thêm cái này để nhận tên file khởi tạo */
    @Input() initialFileName: string | null = null;
    @Input() loading = false;
    @Input()
    set filePath(path: string) {
        this._filePath = path;
    }
    get filePath(): string {
        return this._filePath;
    }
    /** Emit mảng File đã upload thành công */
    @Output() onChange = new EventEmitter<File[]>();
    /** Emit khi clear file */
    @Output() onClear = new EventEmitter<void>();
    @ViewChild('uploader', { read: ElementRef }) uploader!: ElementRef;
    @ViewChild('uploader') fileUploadCmp!: FileUpload;
    private _filePath = '';
    private alertService = inject(AlertService);
    downloadBaseUrl: string = environment.STORAGE_BASE_URL;
    multiple = false;
    uploadedFileName: string | null = null;
    private patterns: string[] = [];
    get downloadUrl(): string {
        const path = this.filePath;
        return `${this.downloadBaseUrl}/${path}`;
    }

    ngOnInit() {
        this.multiple = this.limit > 1;
        // Chuẩn hoá accept thành mảng pattern
        this.patterns = this.accept
            .split(',')
            .map((p) => p.trim().toLowerCase())
            .filter((p) => !!p);
        // Nếu đã có initialFileName, gán ngay
        if (this.initialFileName) {
            this.uploadedFileName = this.initialFileName;
        }
    }
    ngOnChanges(changes: SimpleChanges) {
        if (changes['initialFileName'] && !changes['initialFileName'].firstChange) {
            this.uploadedFileName = changes['initialFileName'].currentValue;
        }
    }
    /** Chạy khi user chọn file + validate */
    handleSelect(event: any, uploader: FileUpload) {
        const files = Array.isArray(event.files) ? event.files : Array.from(event.files as FileList);

        // Validate số lượng
        if (files.length > this.limit) {
            this.alertService.error('Lỗi', `Tải lên tối đa ${this.limit} file.`);
            uploader.clear();
            return;
        }

        // Validate từng file theo patterns
        const invalid = files.filter((f) => !this.matchesAccept(f));
        if (invalid.length) {
            this.alertService.error('Lỗi', `Trường này phải có định dạng:  ${this.accept}`);
            uploader.clear();
            return;
        }

        // Kiểm tra format tên file ---
        if (this.fileNamePattern) {
            const wrongFormat = files.some((f) => {
                // Loại bỏ extension để lấy base name
                const baseName = f.name.replace(/\.[^/.]+$/, '');
                return !this.fileNamePattern!.test(baseName);
            });
            if (wrongFormat) {
                if (this.strictFileNamePattern) {
                    this.alertService.error('Lỗi', this.fileNamePatternMessage);
                    uploader.clear();
                    return;
                }
                this.alertService.warning('Cảnh báo', this.fileNamePatternMessage);
            }
        }

        uploader.clear();
        this.uploadedFileName = files.map((f) => f.name).join(', ');
        this.onChange.emit(files);
    }

    /** Xoá file và emit clear */
    clearUpload(uploader: FileUpload) {
        this.uploadedFileName = null;
        uploader.clear();
        this.onClear.emit();
    }
    public clearAll(): void {
        // xóa tên file
        this.uploadedFileName = null;
        // clear file trong PrimeNG
        this.fileUploadCmp.clear();
        // emit sự kiện clear
        this.onClear.emit();
    }
    openFileDialog() {
        const input: HTMLInputElement | null = this.uploader.nativeElement.querySelector('input[type=file]');
        if (input) {
            input.click();
        }
    }
    /** Check xem file có match một trong các accept patterns không */
    private matchesAccept(file: File): boolean {
        if (!this.patterns.length) return true; // nếu không cấu hình accept
        const lowerMime = file.type.toLowerCase();
        const lowerName = file.name.toLowerCase();
        return this.patterns.some((p) => {
            if (p.startsWith('.')) {
                // extension match
                return lowerName.endsWith(p);
            }
            if (p.endsWith('/*')) {
                // wildcard mime match, ví dụ image/*
                return lowerMime.startsWith(p.slice(0, -1));
            }
            // exact mime match
            return lowerMime === p;
        });
    }
}
