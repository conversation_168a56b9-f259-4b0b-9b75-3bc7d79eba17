<app-sub-header [items]="[{ label: 'Quản lý nhà cung cấp' }, { label: 'Tổng hợp chi phí' }]" [action]="actionHeader"></app-sub-header>

<ng-template #actionHeader>
    <p-button
        (click)="isOpenAddModal = true; initAddForm()"
        label="Nhập tổng hợp chi phí"
        severity="success"
        *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_monthly_expenses_edit']"
    />
</ng-template>
<div style="padding: 1rem 1rem 0 1rem">
    <p-tabView [(activeIndex)]="selectedTab" (activeIndexChange)="handleTabChange($event)">
        <p-tabPanel header="Forwarder">
            <app-table-common
                [tableId]="tableIdForwarder"
                [columns]="columns"
                [data]="stateForwarder.data"
                [loading]="stateForwarder.isFetching"
                [filterTemplate]="filterTemplateForwarder"
                name="Danh sách Forwarder"
                [funcDelete]="deleteSelected"
            >
                <ng-template #filterTemplateForwarder>
                    <tr>
                        <th></th>
                        <th [appFilter]="[tableIdForwarder, 'fileName']">
                            <app-filter-table [rsql]="true" [tableId]="tableIdForwarder" field="fileName"></app-filter-table>
                        </th>
                        <th [appFilter]="[tableIdForwarder, 'logisticId']">
                            <app-filter-table
                                [rsql]="true"
                                [tableId]="tableIdForwarder"
                                field="logisticId"
                                type="select"
                                [configSelect]="{
                                    fieldValue: 'id',
                                    fieldLabel: 'shortName',
                                    url: '/sc/api/logistics/search',
                                    rsql: true,
                                    param: 'shortName',
                                    paramForm: 'id',
                                    body: {
                                        type: 0,
                                    },
                                }"
                            ></app-filter-table>
                        </th>

                        <th [appFilter]="[tableIdForwarder, 'date']">
                            <app-filter-table [rsql]="true" [tableId]="tableIdForwarder" field="date" type="month"></app-filter-table>
                        </th>
                        <th [appFilter]="[tableIdForwarder, 'created']">
                            <app-filter-table [rsql]="true" [tableId]="tableIdForwarder" field="created" type="date-range"></app-filter-table>
                        </th>
                        <th [appFilter]="[tableIdForwarder, 'createdBy']">
                            <app-filter-table
                                [tableId]="tableIdForwarder"
                                field="createdBy"
                                type="select-one"
                                [rsql]="true"
                                [configSelect]="{
                                    fieldValue: 'email',
                                    fieldLabel: 'fullName',
                                    options: userOptions,
                                }"
                            ></app-filter-table>
                        </th>
                    </tr>
                </ng-template>
                <ng-template #templateFileName let-rowData>
                    <a target="_blank" class="tw-cursor-pointer" rel="noopener noreferrer" (click)="handleDownloadForwarderFileName(rowData.attachment?.url)">
                        {{ rowData.fileName }}
                    </a>
                </ng-template>
                <ng-template #templateLogistics let-rowData>
                    {{ mapLogistics[rowData.logisticId] }}
                </ng-template>
            </app-table-common>
        </p-tabPanel>
        <p-tabPanel header="Bảo hiểm">
            <app-table-common
                [tableId]="tableIdInsurance"
                [columns]="columns"
                [data]="stateInsurance.data"
                [loading]="stateInsurance.isFetching"
                [filterTemplate]="filterTemplateInsurance"
                name="Danh sách Bảo hiểm"
                [funcDelete]="deleteSelected"
            >
                <ng-template #filterTemplateInsurance>
                    <tr>
                        <th></th>
                        <th [appFilter]="[tableIdInsurance, 'fileName']">
                            <app-filter-table [rsql]="true" [tableId]="tableIdInsurance" field="fileName"></app-filter-table>
                        </th>
                        <th [appFilter]="[tableIdInsurance, 'logisticId']">
                            <app-filter-table
                                [rsql]="true"
                                [tableId]="tableIdInsurance"
                                field="logisticId"
                                type="select"
                                [configSelect]="{
                                    fieldValue: 'id',
                                    fieldLabel: 'shortName',
                                    url: '/sc/api/logistics/search',
                                    rsql: true,
                                    param: 'shortName',
                                    paramForm: 'id',
                                    body: {
                                        type: 1,
                                    },
                                }"
                            ></app-filter-table>
                        </th>

                        <th [appFilter]="[tableIdInsurance, 'date']">
                            <app-filter-table [rsql]="true" [tableId]="tableIdInsurance" field="date" type="month"></app-filter-table>
                        </th>
                        <th [appFilter]="[tableIdInsurance, 'created']">
                            <app-filter-table [rsql]="true" [tableId]="tableIdInsurance" field="created" type="date-range"></app-filter-table>
                        </th>
                        <th [appFilter]="[tableIdInsurance, 'createdBy']">
                            <app-filter-table
                                [tableId]="tableIdInsurance"
                                field="createdBy"
                                type="select-one"
                                [rsql]="true"
                                [configSelect]="{
                                    fieldValue: 'email',
                                    fieldLabel: 'fullName',
                                    options: userOptions,
                                }"
                            ></app-filter-table>
                        </th>
                        <!--<th [appFilter]="[tableIdInsurance, 'createdBy']">
                            <app-filter-table [rsql]="true" [tableId]="tableIdInsurance" field="createdBy"></app-filter-table>
                        </th>-->
                    </tr>
                </ng-template>
                <ng-template #templateFileName let-rowData>
                    <a target="_blank" class="tw-cursor-pointer" rel="noopener noreferrer" (click)="handleDownloadForwarderFileName(rowData.fileName)">
                        {{ rowData.fileName }}
                    </a>
                </ng-template>
                <ng-template #templateLogistics let-rowData>
                    {{ mapLogistics[rowData.logisticId] }}
                </ng-template>
            </app-table-common>
        </p-tabPanel>
        <p-tabPanel header="Chuyển phát nhanh">
            <app-table-common
                [tableId]="tableIdExpressDelivery"
                [columns]="columns"
                [data]="stateExpressDelivery.data"
                [loading]="stateExpressDelivery.isFetching"
                [filterTemplate]="filterTemplateExpressDelivery"
                name="Danh sách Chuyển phát nhanh"
                [funcDelete]="deleteSelected"
            >
                <ng-template #filterTemplateExpressDelivery>
                    <tr>
                        <th></th>
                        <th [appFilter]="[tableIdExpressDelivery, 'fileName']">
                            <app-filter-table [rsql]="true" [tableId]="tableIdExpressDelivery" field="fileName"></app-filter-table>
                        </th>
                        <th [appFilter]="[tableIdExpressDelivery, 'logisticId']">
                            <app-filter-table
                                [rsql]="true"
                                [tableId]="tableIdExpressDelivery"
                                field="logisticId"
                                type="select"
                                [configSelect]="{
                                    fieldValue: 'id',
                                    fieldLabel: 'shortName',
                                    url: '/sc/api/logistics/search',
                                    rsql: true,
                                    param: 'shortName',
                                    paramForm: 'id',
                                    body: {
                                        type: 2,
                                    },
                                }"
                            ></app-filter-table>
                        </th>

                        <th [appFilter]="[tableIdExpressDelivery, 'date']">
                            <app-filter-table [rsql]="true" [tableId]="tableIdExpressDelivery" field="date" type="month"></app-filter-table>
                        </th>
                        <th [appFilter]="[tableIdExpressDelivery, 'created']">
                            <app-filter-table [rsql]="true" [tableId]="tableIdExpressDelivery" field="created" type="date-range"></app-filter-table>
                        </th>
                        <th [appFilter]="[tableIdExpressDelivery, 'createdBy']">
                            <app-filter-table
                                [tableId]="tableIdExpressDelivery"
                                field="createdBy"
                                type="select-one"
                                [rsql]="true"
                                [configSelect]="{
                                    fieldValue: 'email',
                                    fieldLabel: 'fullName',
                                    options: userOptions,
                                }"
                            ></app-filter-table>
                        </th>
                        <!--<th [appFilter]="[tableIdExpressDelivery, 'createdBy']">
                            <app-filter-table [rsql]="true" [tableId]="tableIdExpressDelivery" field="createdBy"></app-filter-table>
                        </th>-->
                    </tr>
                </ng-template>
                <ng-template #templateFileName let-rowData>
                    <a target="_blank" class="tw-cursor-pointer" rel="noopener noreferrer" (click)="handleDownloadForwarderFileName(rowData.fileName)">
                        {{ rowData.fileName }}
                    </a>
                </ng-template>
                <ng-template #templateLogistics let-rowData>
                    {{ mapLogistics[rowData.logisticId] }}
                </ng-template>
            </app-table-common>
        </p-tabPanel>
    </p-tabView>
</div>

<p-dialog [style]="{ minWidth: '500px' }" [(visible)]="isOpenAddModal" [modal]="true" [closable]="true" [header]="tabHeaders[selectedTab]">
    <div class="tw-mt-1">
        <app-form #form *ngIf="addFormGroup" [formGroup]="addFormGroup" layout="horizontal" (onSubmit)="onSubmitCreate($event)">
            <app-form-item *ngIf="isOpenAddModal" label="Nhà cung cấp" [isRequired]="true">
                <app-filter-table
                    type="select-one"
                    [configSelect]="{
                        fieldValue: 'id',
                        fieldLabel: 'shortName',
                        rsql: true,
                        url: '/sc/api/logistics/search',
                        paramForm: 'id',
                        body: {
                            type: selectedTab,
                        },
                    }"
                    (onChange)="changeLogistics($event)"
                ></app-filter-table>
            </app-form-item>
            <br />
            <app-form-item label="Tháng">
                <p-calendar
                    [showButtonBar]="true"
                    placeholder="MM/yyyy"
                    [showIcon]="true"
                    dateFormat="mm/yy"
                    view="month"
                    formControlName="date"
                    class="tw-w-full"
                    appendTo="body"
                ></p-calendar>
            </app-form-item>
            <br />
            <app-form-item label="Đính kèm">
                <app-button-group-file
                    (onFileSelected)="handleUploadFile($event)"
                    [attachment]="addFormGroup.getRawValue().attachmentFile"
                    formControlName="attachmentFile"
                    urlTemplate="{{
                        selectedTab === 0
                            ? 'monthly_expenses_fwd.xlsx'
                            : selectedTab === 1
                              ? 'monthly_expenses_insurance.xlsx'
                              : 'monthly_expenses_delivery.xlsx'
                    }}"
                    [urlError]="urlError"
                    service="/sc/api"
                    (onClearFile)="handleClearFile()"
                    [types]="['excel']"
                ></app-button-group-file>
            </app-form-item>
        </app-form>
        <div class="tw-flex tw-justify-end tw-mt-4 tw-space-x-3">
            <p-button label="Đóng" (click)="isOpenAddModal = false" severity="secondary" size="small"></p-button>
            <p-button label="Lưu" [disabled]="addFormGroup.invalid" (click)="formComponent?.handleSubmit()" severity="primary" size="small"></p-button>
        </div>
    </div>
</p-dialog>
