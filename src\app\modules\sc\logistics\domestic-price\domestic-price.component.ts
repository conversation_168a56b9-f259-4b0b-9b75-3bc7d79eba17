import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { TabViewModule } from 'primeng/tabview';
import { TagModule } from 'primeng/tag';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { CalendarModule } from 'primeng/calendar';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { TableModule, TableRowCollapseEvent, TableRowExpandEvent } from 'primeng/table';
import { DialogModule } from 'primeng/dialog';
import { ButtonGroupFileComponent } from 'src/app/shared/components/button-group-file/button-group-file.component';
import { PopupComponent } from 'src/app/shared/components/popup/popup.component';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { MonthlyQuoteService } from 'src/app/services/sc/logistics/monthly-quote.service';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { ApiResponse, Column, EventPopupSubmit } from 'src/app/models/interface';
import { MonthlyQuote, Route } from 'src/app/models/interface/sc';
import { ActivatedRoute, Router } from '@angular/router';
import { TableCommonService } from 'src/app/shared/table-module/table.common.service';
import { TABLE_KEY } from 'src/app/models/constant';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { RouteService } from 'src/app/services/sc/logistics/route.service';
import { FileService } from 'src/app/shared/services/file.service';

@Component({
    selector: 'app-sc-domestic-price',
    templateUrl: './domestic-price.component.html',
    styleUrls: ['./domestic-price.component.scss'],
    standalone: true,
    imports: [
        TableCommonModule,
        FormCustomModule,
        ButtonModule,
        CommonModule,
        TagModule,
        SubHeaderComponent,
        TabViewModule,
        CalendarModule,
        ReactiveFormsModule,
        TableModule,
        DialogModule,
        ButtonGroupFileComponent,
        PopupComponent,
        HasAnyAuthorityDirective,
        FormsModule,
    ],
    providers: [MonthlyQuoteService, RouteService],
})
export class DomesticPriceComponent implements OnInit, AfterViewInit {
    //tabview
    activeIndex: number = 0;

    // Add data
    formGroup: FormGroup;
    urlErrorData: string;
    urlErrorAnother: string;
    // tab 1
    stateRoute: Route[];
    loading: boolean;
    expandedRows = {};
    // tab 2
    columns: Column[] = [];
    tableId: string = TABLE_KEY.DOMESTIC_PRICE;
    state: QueryObserverBaseResult<MonthlyQuote[]>;
    @ViewChild('templateUrl') templateUrl: TemplateRef<Element>;
    @ViewChild('templateAdditionUrl') templateAdditionUrl: TemplateRef<Element>;

    objectMin: Record<string, boolean> = {};
    arrDateReport: Date[] = [];
    initDateSerach: Date;
    constructor(
        private monthlyQuoteService: MonthlyQuoteService,
        private loadingService: LoadingService,
        private fb: FormBuilder,
        private alertService: AlertService,
        private routeService: RouteService,
        private activatedRoute: ActivatedRoute,
        private router: Router,
        private tableCommonService: TableCommonService,
        private fileService: FileService,
    ) {
        this.initForm();
    }

    ngOnInit(): void {
        if (this.activatedRoute.snapshot.queryParams['activeIndex']) {
            this.activeIndex = Number(this.activatedRoute.snapshot.queryParams['activeIndex']);
        }
        this.updateParam();
        this.tableCommonService
            .init<MonthlyQuote>({
                tableId: this.tableId,
                queryFn: (filter) => this.monthlyQuoteService.getPageTableCustom(filter),
                configFilterRSQL: {
                    date: 'Month',
                    created: 'DateRange',
                    updated: 'DateRange',
                    attachmentUrl: 'Text',
                    additionAttachmentUrl: 'Text',
                },
                defaultParams: {
                    sort: 'date,desc',
                },
            })
            .subscribe({
                next: (res) => {
                    this.state = res;
                    if (res.data.length > 0) {
                        // Tìm item có updated lớn nhất
                        const latestItem = res.data.reduce((max, current) => (new Date(current.updated) > new Date(max.updated) ? current : max));

                        // Lưu mảng updated thành Date
                        this.arrDateReport = res.data.map((item) => new Date(item.updated));

                        // Gán date tương ứng với updated mới nhất
                        this.initDateSerach = new Date(latestItem.date);
                        this.handleChangeDate(this.initDateSerach);
                    }
                },
                error: (error) => {
                    this.alertService.handleError(error);
                },
            });
        if (this.initDateSerach) {
            this.callApiTableRoute(null);
        }
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.columns = [
                {
                    field: 'date',
                    header: 'Thời gian báo giá',
                    type: 'date',
                    format: 'MM/yyyy',
                    default: true,
                },
                {
                    field: 'attachmentUrl',
                    header: 'Tài liệu Excel',
                    body: this.templateUrl,
                },
                {
                    field: 'additionAttachmentUrl',
                    header: 'Tài liệu khác',
                    body: this.templateAdditionUrl,
                },

                {
                    field: 'updated',
                    header: 'Thời gian cập nhật',
                    type: 'date',
                    format: 'dd/MM/yyyy HH:mm:ss',
                },
                {
                    field: 'updatedBy',
                    header: 'Người cập nhật',
                },
            ];
        });
    }

    updateParam() {
        this.router.navigate([], {
            queryParams: {
                activeIndex: this.activeIndex,
            },
            queryParamsHandling: '',
        });

        if (this.activeIndex === 1) {
            setTimeout(() => {
                this.columns = [
                    {
                        field: 'date',
                        header: 'Thời gian báo giá',
                        type: 'date',
                        format: 'MM/yyyy',
                        default: true,
                    },
                    {
                        field: 'attachmentUrl',
                        header: 'Tài liệu Excel',
                        body: this.templateUrl,
                    },
                    {
                        field: 'additionAttachmentUrl',
                        header: 'Tài liệu khác',
                        body: this.templateAdditionUrl,
                    },

                    {
                        field: 'updated',
                        header: 'Thời gian cập nhật',
                        type: 'date',
                        format: 'dd/MM/yyyy HH:mm:ss',
                    },
                    {
                        field: 'updatedBy',
                        header: 'Người cập nhật',
                    },
                ];
            });
        }
    }

    callApiTableRoute(date: number | null) {
        this.loading = true;
        this.routeService.groupByDate(date).subscribe({
            next: (res) => {
                this.stateRoute = res;
                this.loading = false;
                this.expandAll();
                this.findMin(this.stateRoute);
            },
        });
    }

    initForm() {
        this.formGroup = this.fb.group({
            dateCustom: [null, Validators.required],
            attachmentUrl: [null, Validators.required],
            attachmentUrlFile: [null],
            additionAttachmentUrl: [null],
            additionAttachmentUrlFile: [null],
            routes: [null, Validators.required],
        });
    }

    expandAll() {
        this.stateRoute.forEach((data) => {
            this.expandedRows[data.id] = true;
        });
    }
    onRowExpand(event: TableRowExpandEvent) {
        console.log('row expanded', event);
    }
    onRowCollapse(event: TableRowCollapseEvent) {
        console.log('row collapsed', event);
    }

    handleClearFile(type: 'DOCUMENT_DATA' | 'DOCUMENT_ANOTHER') {
        switch (type) {
            case 'DOCUMENT_DATA':
                this.formGroup.patchValue({ attachmentUrl: null, routes: [] });
                break;
            case 'DOCUMENT_ANOTHER':
                this.formGroup.patchValue({ additionAttachmentUrl: null });

                break;
        }
    }

    handleUploadFileDocument(file: File, type: 'DOCUMENT_DATA' | 'DOCUMENT_ANOTHER') {
        this.loadingService.show();
        this.monthlyQuoteService.importFile(file, type).subscribe({
            next: (res: ApiResponse) => {
                if (res.code === 1) {
                    const data = res.data as Route[];
                    switch (type) {
                        case 'DOCUMENT_DATA':
                            this.formGroup.patchValue({ attachmentUrl: res.attachment.url, attachmentUrlFile: res.attachment, routes: data });
                            this.urlErrorData = null;
                            break;
                        case 'DOCUMENT_ANOTHER':
                            this.formGroup.patchValue({ additionAttachmentUrl: res.attachment.url, additionAttachmentUrlFile: res.attachment });
                            this.urlErrorAnother = null;
                            break;
                    }
                } else {
                    this.alertService.error('Lỗi! Vui lòng kiểm tra lại file');
                    switch (type) {
                        case 'DOCUMENT_DATA':
                            this.urlErrorData = res.message as string;
                            this.formGroup.patchValue({ attachmentUrl: null, attachmentUrlFile: null, routes: [] });

                            break;
                        case 'DOCUMENT_ANOTHER':
                            this.urlErrorAnother = res.message as string;
                            this.formGroup.patchValue({ additionAttachmentUrl: null, additionAttachmentUrlFile: null });

                            break;
                    }
                    this.handleClearFile(type);
                }
                this.loadingService.hide();
            },
            error: () => {
                switch (type) {
                    case 'DOCUMENT_DATA':
                        this.urlErrorData = null;
                        this.formGroup.patchValue({ attachmentUrl: null, attachmentUrlFile: null, routes: [] });

                        break;
                    case 'DOCUMENT_ANOTHER':
                        this.urlErrorAnother = null;
                        this.formGroup.patchValue({ additionAttachmentUrl: null, additionAttachmentUrlFile: null });
                        break;
                }
                this.loadingService.hide();
                this.handleClearFile(type);
            },
        });
    }

    handleSubmit(event: EventPopupSubmit<MonthlyQuote>) {
        this.loadingService.show();
        const date = event.value.dateCustom.getTime();

        this.monthlyQuoteService.create({ ...event.value, date }).subscribe({
            next: () => {
                this.loadingService.hide();
                event.close();
                this.formGroup.reset();
                this.alertService.success('Thành công');
                this.state.refetch();
                this.callApiTableRoute(null);
                this.initDateSerach = event.value.dateCustom;
                this.handleChangeDate(this.initDateSerach);
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    getLastPart(item: MonthlyQuote, field: 'attachmentUrl' | 'additionAttachmentUrl') {
        if (!item) return '';

        switch (field) {
            case 'attachmentUrl':
                return item.attachmentUrl?.split('/').pop();
            case 'additionAttachmentUrl':
                return item.additionAttachmentUrl?.split('/').pop();
        }
    }

    deleteSelected = (ids: number[]) => {
        return this.monthlyQuoteService.batchDelete(ids);
    };

    getAllKey(route: Route): string[] {
        return route.logisticsDetails ? Object.keys(route.logisticsDetails).sort() : [];
    }

    handleChangeDate(date: Date) {
        this.callApiTableRoute(date ? date.getTime() : null);
    }

    handleDownload(url: string) {
        this.fileService.downLoadFileByService(url, '/sc/api');
    }

    findMin(routes: Route[]) {
        this.objectMin = {};
        if (routes && routes.length > 0) {
            for (const route of routes) {
                if (route.logisticsDetails) {
                    const arrLogisticPrice = [];
                    const detailItems = []; // Lưu trữ các MonthlyQuoteDetail tương ứng

                    // Tạo mảng giá và lưu thông tin item
                    for (const logistic in route.logisticsDetails) {
                        const items = route.logisticsDetails[logistic];
                        arrLogisticPrice.push(items.map((item) => item.price));
                        detailItems.push(items); // Lưu các MonthlyQuoteDetail
                    }

                    if (arrLogisticPrice.length > 0 && arrLogisticPrice[0].length > 0) {
                        const numRows = arrLogisticPrice.length;
                        const numCols = arrLogisticPrice[0].length;

                        // Tìm min theo từng cột và lưu index của giá trị min
                        for (let col = 0; col < numCols; col++) {
                            let minPrice = Infinity;

                            // Tìm giá trị min trong cột
                            for (let row = 0; row < numRows; row++) {
                                if (arrLogisticPrice[row][col] < minPrice) {
                                    minPrice = arrLogisticPrice[row][col];
                                }
                            }

                            // Duyệt lại để thêm tất cả các giá trị min vào objectMin
                            for (let row = 0; row < numRows; row++) {
                                if (arrLogisticPrice[row][col] === minPrice) {
                                    const detail = detailItems[row][col];
                                    if (detail && detail.id) {
                                        this.objectMin[detail.id] = true;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    handleClose() {
        this.formGroup.reset();
        this.urlErrorData = null;
        this.urlErrorAnother = null;
    }
}
