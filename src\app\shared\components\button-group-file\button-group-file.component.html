<!-- Tr<PERSON><PERSON><PERSON> hợp chọn một file (!multiple) -->
<div class="border-1 border-gray-300 tw-h-12 tw-flex tw-flex-row tw-items-center tw-w-fit tw-rounded" *ngIf="!multiple">
    <span class="tw-px-4 tw-flex tw-flex-row tw-h-full tw-items-center" [class.disabled]="disabled" style="position: relative">
        <ng-container *ngIf="fileSelected?.length > 0 || attachment; else templateInputUpload">
            <span *ngIf="fileSelected[0] && !attachment" class="tw-max-w-52 tw-inline-block tw-text-nowrap tw-overflow-hidden tw-text-ellipsis align-middle">{{
                fileSelected[0].name
            }}</span>
            <a
                *ngIf="attachment"
                class="tw-max-w-52 tw-inline-block tw-text-ellipsis tw-overflow-hidden tw-cursor-pointer tw-text-nowrap align-middle"
                (click)="downloadAttachment(attachment)"
                >{{ attachment.name }}</a
            >
            <i class="pi pi-times tw-text-red-500 tw-cursor-default hover:tw-bg-red-200 tw-rounded-full tw-p-2" (click)="removeFile(-1, fileInput)"></i>
        </ng-container>
        <ng-template #templateInputUpload>
            <a class="tw-cursor-pointer" style="display: flex; align-items: center">
                <span class="pi pi-cloud-upload" style="color: #3490dc; margin-right: 5px; font-size: x-large"></span>
                <span>Chọn file</span>
            </a>
            <input
                type="file"
                #fileInput
                [accept]="acceptTypes"
                style="opacity: 0; position: absolute; top: 0; left: 0; width: 100%; height: 100%; cursor: pointer"
                (change)="onSelectFile($event)"
            />
        </ng-template>
    </span>
    <ng-container *ngIf="!urlError && simpleUpload !== ''; else templateUrlError">
        <span [class.disabled]="disabled" class="border-left-1 tw-h-full border-gray-300 tw-flex tw-items-center tw-px-4 tw-cursor-pointer">
            <ng-container *ngIf="!simpleUpload">
                <i class="pi pi-cloud-download" style="color: #3490dc; margin-right: 5px; font-size: x-large"></i>
                <a *ngIf="urlTemplate" [href]="urlTemplate" download class="tw-flex tw-items-center tw-h-full tw-whitespace-nowrap">
                    <span>File mẫu</span>
                </a>
                <a *ngIf="!urlTemplate" (click)="downloadTemplate()" class="tw-flex tw-items-center tw-h-full tw-whitespace-nowrap">
                    <span>File mẫu</span>
                </a>
            </ng-container>
            <ng-container *ngIf="simpleUpload && simpleUpload !== ''">
                <i class="tw-flex tw-items-center tw-h-full">{{ simpleUpload }}</i>
            </ng-container>
        </span>
    </ng-container>
    <ng-template #templateUrlError>
        <span
            [class.disabled]="disabled"
            *ngIf="urlError || blodResponseError"
            class="border-left-1 tw-h-full border-gray-300 tw-flex tw-items-center tw-px-4 tw-cursor-pointer"
        >
            <p class="tw-h-full tw-whitespace-nowrap" *ngIf="urlError">
                <a class="tw-flex tw-h-full tw-flex-nowrap tw-items-center" [href]="urlError" download>
                    Tải file lỗi <span class="text-red-400 ml-2">tại đây</span>
                </a>
            </p>
            <p class="tw-h-full tw-whitespace-nowrap" *ngIf="blodResponseError && !urlError">
                Tải file lỗi
                <a class="tw-h-full tw-flex tw-items-center tw-flex-nowrap" (click)="downloadFileError()">
                    <span class="text-red-400 ml-2">tại đây</span>
                </a>
            </p>
        </span>
    </ng-template>
</div>

<!-- Trường hợp chọn nhiều file (multiple) -->
<ng-container *ngIf="multiple">
    <ng-container *ngIf="fileSelected?.length > 0 || attachments?.length > 0; else templateInputUploadMultiple">
        <ng-container *ngIf="fileSelected?.length > 0 && (attachments?.length === 0 || attachments)">
            <ng-container *ngFor="let file of fileSelected; let i = index">
                <div class="border-1 border-gray-300 tw-h-12 tw-flex tw-flex-row tw-items-center tw-w-fit tw-rounded tw-mb-2">
                    <!-- Hiển thị danh sách file đã chọn -->
                    <div class="tw-flex tw-flex-row tw-items-center tw-gap-2 tw-px-4">
                        <!-- Hiển thị các file đã chọn (fileSelected) -->
                        <span class="tw-max-w-52 tw-inline-block tw-text-nowrap tw-overflow-hidden tw-text-ellipsis align-middle">{{ file.name }}</span>
                        <i class="pi pi-times tw-text-red-500 tw-cursor-pointer hover:tw-bg-red-200 tw-rounded-full tw-p-1" (click)="removeFile(i)"></i>
                    </div>
                </div>
            </ng-container>
        </ng-container>
        <!-- Hiển thị các attachment từ server -->

        <ng-container *ngIf="attachments?.length > 0">
            <ng-container *ngFor="let att of attachments; let i = index">
                <div class="border-1 border-gray-300 tw-h-12 tw-flex tw-flex-row tw-items-center tw-w-fit tw-rounded tw-mb-2">
                    <div class="tw-flex tw-flex-row tw-items-center tw-gap-2 tw-px-4">
                        <a
                            class="tw-max-w-52 tw-inline-block tw-text-ellipsis tw-overflow-hidden tw-cursor-pointer tw-text-nowrap align-middle"
                            (click)="downloadAttachment(att)"
                            >{{ att.name }}</a
                        >
                        <i class="pi pi-times tw-text-red-500 tw-cursor-pointer hover:tw-bg-red-200 tw-rounded-full tw-p-1" (click)="removeFile(i)"></i>
                    </div>
                </div>
            </ng-container>
        </ng-container>
    </ng-container>
    <!-- Input để chọn thêm file -->
    <ng-template #templateInputUploadMultiple>
        <div class="border-1 border-gray-300 tw-h-12 tw-flex tw-flex-row tw-items-center tw-w-fit tw-rounded">
            <span class="tw-px-4 tw-flex tw-flex-row tw-h-full tw-items-center" [class.disabled]="disabled" style="position: relative">
                <a class="tw-cursor-pointer" style="display: flex; align-items: center">
                    <span class="pi pi-cloud-upload" style="color: #3490dc; margin-right: 5px; font-size: x-large"></span>
                    <span>Chọn file</span>
                </a>
                <input
                    type="file"
                    #fileInputMultiple
                    [accept]="acceptTypes"
                    multiple
                    style="opacity: 0; position: absolute; top: 0; left: 0; width: 100%; height: 100%; cursor: pointer"
                    (change)="onSelectFile($event)"
                />
            </span>
        </div>
    </ng-template>
</ng-container>
