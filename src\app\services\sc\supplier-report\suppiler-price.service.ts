import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { LineSupplierPrice, SupplierPriceChange } from '../../../models/interface/sc';

@Injectable()
export class SupplierPriceService extends BaseService<SupplierPriceChange> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/supplier-price-change');
    }

    lineSupplierPrice(id: number, startTime: number, endTime: number) {
        return this.http.get<LineSupplierPrice>(
            `/sc/api/supplier-price-change/line-supplier?supplierId=${id}&startTime=${startTime}&endTime=${endTime}`,
        );
    }
}
