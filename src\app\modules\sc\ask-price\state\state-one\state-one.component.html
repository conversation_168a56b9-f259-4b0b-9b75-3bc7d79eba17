<div>
    <p-panel header="Thông tin chung" [toggleable]="true">
        <div [formGroup]="rfqForm">
            <div class="tw-grid tw-grid-cols-2 tw-gap-4">
                <div>
                    <div class="tw-font-bold tw-mb-3">Mã</div>
                    <input
                        type="text"
                        placeholder="Hệ thống tự sinh"
                        class="tw-w-full"
                        pInputText
                        formControlName="code"
                    />
                </div>
                <div>
                    <div class="tw-font-bold tw-mb-3">Tên đợt hỏi giá</div>
                    <app-editable-input
                        [control]="rfqForm.get('name')"
                        type="input"
                        placeholder="Tên đợt hỏi giá"
                        [trim]="true"
                        (save)="saveGeneralInfo()"
                        fieldName="tên đợt hỏi giá"
                    >
                    </app-editable-input>
                </div>
            </div>
            <br />
            <div class="tw-grid tw-grid-cols-2 tw-gap-4">
                <div>
                    <div class="tw-font-bold tw-mb-3">Ghi chú</div>
                    <app-editable-input
                        [control]="rfqForm.get('note')"
                        type="textarea"
                        placeholder="Ghi chú"
                        [trim]="true"
                        (save)="saveGeneralInfo()"
                        fieldName="ghi chú"
                    >
                    </app-editable-input>
                </div>
                <div>

                </div>
            </div>
        </div>
    </p-panel>
    <br />
    <p-panel header="Thông tin dự án" [toggleable]="true">
        <div>
            <div class="flex items-center gap-1">
                <span>Dự án</span>
                <span class="text-red-600">*</span>
            </div>
            <br />
            <app-button-group-file
                (onFileSelected)="handleUploadItemsFile($event)"
                (onClearFile)="handleClearFile()"
                [attachment]="rfqOld?.itemAttachment"
                [types]="['excel']"
                urlTemplate="mau_du_an_import.xlsx"
                [urlError]="urlError"
            ></app-button-group-file>
        </div>
        <br />
        <div>
            <p-table
                [value]="rfqItemsDisplay"
                styleClass="p-datatable-gridlines"
                [scrollable]="true"
                scrollHeight="700px"
                [resizableColumns]="true"
            >
                <ng-template pTemplate="header">
                    <tr>
                        <th rowspan="2" style="">STT</th>
                        <th rowspan="2" style="">Dự án</th>
                        <th rowspan="2" style="">Mã kế toán</th>
                        <th rowspan="2" style="">Sản phẩm</th>
                        <th rowspan="2" style="">Tên sản phẩm viết tắt</th>
                        <th rowspan="2" style="">Số lượng</th>
                        <th [attr.colspan]="sortedUniqueTimes.length" style="text-align: center;">
                            Thời gian yêu cầu
                        </th>
                        <th rowspan="2" style="">Version BOM</th>
                    </tr>
                    <tr>
                        <th *ngFor="let time of sortedUniqueTimes">
                            {{ time | date:'dd/MM/yyyy' }}
                        </th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                    <tr>
                        <td style="">{{ rowIndex + 1 }}</td>
                        <td style="">{{ item.project }}</td>
                        <td style="">{{ item.accountingCode }}</td>
                        <td style="">{{ item.productName }}</td>
                        <td style="">{{ item.productShortName }}</td>
                        <td style="">{{ item.quantity }}</td>
                        <td *ngFor="let time of sortedUniqueTimes">
                            {{ item?.type === RFQ_ITEM_TYPE.SC_BOM? getQuantity(item, time) : '' }}
                        </td>
                        <td style="">{{ item.bomVersion }}</td>
                    </tr>
                </ng-template>
            </p-table>
        </div>
    </p-panel>
    <br />
    <p-panel header="Báo cáo tồn active" [toggleable]="true">
        <div>
            <div class="flex items-center gap-1">
                <span>Báo cáo tồn active</span>
                <span class="text-red-600">*</span>
            </div>
            <br />
            <app-button-group-file
                (onFileSelected)="handleUploadInventoryFile($event)"
                (onClearFile)="handleClearFile()"
                [attachment]="rfqOld?.inventoryAttachment"
                [types]="['excel']"
                urlTemplate="mau_bao_cao_ton_active_import.xlsx"
                [urlError]="urlErrorInventory"
            ></app-button-group-file>
        </div>
        <br />
        <div>
            <p-table
                [value]="rfqOld?.inventories"
                styleClass="p-datatable-gridlines"
                [scrollable]="true"
                scrollHeight="700px"
                [resizableColumns]="true"
            >
                <ng-template pTemplate="header">
                    <tr>
                        <th style="">STT</th>
                        <th style="">MAN PN</th>
                        <th style="">Mã VNPT MAN PN</th>
                        <th style="">Mô tả</th>
                        <th style="">Nhà sản xuất</th>
                        <th style="">Số lượng</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-inventory let-rowIndex="rowIndex">
                    <tr>
                        <td style="">{{ rowIndex + 1 }}</td>
                        <td style="">{{ inventory.manPn }}</td>
                        <td style="">{{ inventory.vnptPn }}</td>
                        <td style="">{{ inventory.description }}</td>
                        <td style="">{{ inventory.manufacturer }}</td>
                        <td style="">{{ inventory.quantity }}</td>
                    </tr>
                </ng-template>
            </p-table>
        </div>
    </p-panel>
</div>
