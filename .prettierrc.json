{"printWidth": 160, "tabWidth": 4, "useTabs": false, "semi": true, "singleQuote": true, "quoteProps": "as-needed", "jsxSingleQuote": false, "trailingComma": "all", "bracketSpacing": true, "jsxBracketSameLine": false, "arrowParens": "always", "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "overrides": [{"files": "*.html", "options": {"parser": "angular"}}, {"files": "*.json", "options": {"printWidth": 100}}]}