import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { LineSupplierQuality, SupplierQuality } from '../../../models/interface/sc';

@Injectable()
export class SupplierQualityService extends BaseService<SupplierQuality> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/supplier-quality');
    }

    lineSupplierQuality(id: number, startTime: number, endTime: number) {
        return this.http.get<LineSupplierQuality>(
            `/sc/api/supplier-quality/line-supplier?supplierId=${id}&startTime=${startTime}&endTime=${endTime}`,
        );
    }
}
