import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { LotCustom } from '../../../models/interface/sc';
import { ApiResponse } from 'src/app/models/interface';

@Injectable()
export class LotCustomService extends BaseService<LotCustom> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/lot-custom');
    }

    importFile(files: File[]) {
        const formData: FormData = new FormData();
        files.forEach((file) => formData.append('files', file));

        return this.http.post<ApiResponse>('/sc/api/lot-custom/import-file', formData);
    }

    importFileCustoms(file: File) {
        const formData: FormData = new FormData();
        formData.append('file', file);

        return this.http.post<ApiResponse>('/sc/api/lot-custom/import-file-customs', formData);
    }

    getByLot(lotId: number) {
        return this.http.get<LotCustom>('/sc/api/lot-custom/get-info', {
            params: { lotId },
        });
    }
}
