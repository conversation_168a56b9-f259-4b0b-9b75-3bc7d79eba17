import { Component, inject, OnInit } from '@angular/core';
import { TableCommonModule } from '../../../../shared/table-module/table.common.module';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';
import { ChipModule } from 'primeng/chip';
import { DialogModule } from 'primeng/dialog';
import { FormCustomModule } from '../../../../shared/form-module/form.custom.module';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { PaginatorModule } from 'primeng/paginator';
import { FormArray, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { PanelModule } from 'primeng/panel';
import { TableModule } from 'primeng/table';
import { ApproveService } from '../../../../services/smart-qc/masterdata/approve.service';
import { TableCommonService } from '../../../../shared/table-module/table.common.service';
import { PoService } from '../../../../services/sc/po/po.service';
import { RfqService } from '../../../../services/sc/sc-bom/rfq.service';
import { SubHeaderComponent } from '../../../../shared/components/sub-header/sub-header.component';
import { Po, PoDraft } from '../../../../models/interface/sc';
import { PO_DRAFT_STATUS_CONSTANT, PO_STATE_CONSTANT, PoStates, RFQ_ITEM_TYPE } from '../../../../models/constant/sc';
import { PoDraftService } from '../../../../services/sc/sc-bom/po-draft.service';
import { LoadingService } from '../../../../shared/services/loading.service';
import { AlertService } from '../../../../shared/services/alert.service';
import { HasAnyAuthorityDirective } from '../../../../shared/directives/has-any-authority.directive';
import { FileService } from 'src/app/shared/services/file.service';
import { EditableInputComponent } from '../../../../shared/edit/editable-input.component';
import { AttachmentComponent } from '../../../../shared/components/attachment/attachment.component';
import { ComboboxComponent } from 'src/app/shared/components/combobox/combobox.component';

@Component({
    selector: 'app-sc-rfq',
    standalone: true,
    imports: [
        TableCommonModule,
        CommonModule,
        RouterModule,
        ButtonModule,
        TagModule,
        TooltipModule,
        ChipModule,
        DialogModule,
        FormCustomModule,
        InputTextModule,
        InputTextareaModule,
        PaginatorModule,
        ReactiveFormsModule,
        PanelModule,
        TableModule,
        SubHeaderComponent,
        HasAnyAuthorityDirective,
        EditableInputComponent,
        AttachmentComponent,
        ComboboxComponent,
    ],
    templateUrl: './po-draft.detail.component.html',
    styleUrls: ['./po-draft.detail.component.scss'],
    providers: [ApproveService, TableCommonService, PoService, RfqService, PoDraftService],
})
export class PoDraftDetailComponent implements OnInit {
    // Inject service
    poDraftService = inject(PoDraftService);
    loadingService = inject(LoadingService);
    alertService = inject(AlertService);
    fileService = inject(FileService);
    // End inject service

    // Declare form
    poDraftId: number;
    poDraftOld: PoDraft;
    poDraftForm: FormGroup;
    isLoading: boolean = true;
    selectedPos: FormGroup[] = [];
    isEnableConfirmButton: boolean = false;
    scBomIdsOld: number[];
    isHasBomIds: boolean = false;
    // End declare form

    // Warning modal
    isOpenWarningModal = false;
    warnings;
    // End warning modal

    constructor(
        private fb: FormBuilder,
        private router: Router,
        private route: ActivatedRoute,
        private activatedRoute: ActivatedRoute,
    ) {
        this.poDraftForm = this.fb.group({
            id: [{ value: this.poDraftOld?.id ?? null, disabled: false }, []],
            code: [{ value: this.poDraftOld?.code ?? null, disabled: false }, []],
            note: [{ value: this.poDraftOld?.note ?? null, disabled: false }, []],
            scBomIds: [{ value: this.poDraftOld?.scBomIds ?? null, disabled: false }, [Validators.required]],
        });
    }

    ngOnInit(): void {
        this.loadData();
    }

    loadData() {
        const idString = this.activatedRoute.snapshot.paramMap.get('id');
        if (idString) {
            this.poDraftId = Number(idString);
        }

        if (this.poDraftId) {
            this.loadingService.show();
            this.poDraftService.getOne(this.poDraftId).subscribe({
                next: (res) => {
                    this.loadingService.hide();
                    this.poDraftOld = res.body;
                    this.isLoading = false;
                    this.initForm();
                },
                error: () => {
                    this.loadingService.hide();
                    this.alertService.error('Lỗi', 'Không thể truy cập thông tin');
                    this.router.navigate(['/sc/bom']);
                    this.isLoading = false;
                },
            });
        } else {
            this.isLoading = false;
            this.initForm();
        }
    }

    initForm() {
        // Check disbale cbb
        const isDisableCbb = this.poDraftOld?.poItems?.some((item) => item.state !== PO_STATE_CONSTANT.DRAFT);
        this.poDraftForm = this.fb.group({
            id: [{ value: this.poDraftOld?.id ?? null, disabled: false }, []],
            code: [{ value: this.poDraftOld?.code ?? null, disabled: false }, []],
            note: [{ value: this.poDraftOld?.note ?? null, disabled: false }, []],
            scBomIds: [{ value: this.poDraftOld?.scBomIds ?? null, disabled: isDisableCbb }, [Validators.required]],
            poItems: this.fb.array(this.initPoItemsForm()),
        });
        this.scBomIdsOld = this.poDraftOld?.scBomIds;
        this.isHasBomIds = this.scBomIdsOld !== null && this.scBomIdsOld.length > 0;

        this.poDraftForm.get('scBomIds')?.valueChanges.subscribe(() => {
            this.isEnableConfirmButton = !this.checkChangeBomIds(this.poDraftForm.get('scBomIds')?.value, this.scBomIdsOld);
        });
    }

    checkChangeBomIds(oldValue: number[], newValue: number[]): boolean {
        if (oldValue.length !== newValue.length) return false;
        const sorted1 = [...oldValue].sort();
        const sorted2 = [...newValue].sort();
        return sorted1.every((val, i) => val === sorted2[i]);
    }

    initPoItemsForm(): FormGroup[] {
        return this.poDraftOld.poItems.map((poItem) => this.createPoItemFormGroup(poItem));
    }

    createPoItemFormGroup(po: Po): FormGroup {
        return this.fb.group({
            id: [po?.id ?? null],
            orderNo: [po?.orderNo ?? null],
            supplierName: [po?.supplierName ?? null],
            totalValue: [po?.totalValue ?? null],
            boqAttachment: [po?.boqAttachment ?? null],
            unitPrice: [po?.unitPrice ?? null],
            state: [po?.state ?? null],
        });
    }

    get poItems(): FormArray {
        return this.poDraftForm.get('poItems') as FormArray;
    }

    getStatusText(status: number): string {
        switch (status) {
            case -1:
                return 'Nháp';
            case 0:
                return 'Mới';
            case 1:
                return 'Đang xử lý';
            case 2:
                return 'Đang giao hàng';
            default:
                return 'Hoàn thành';
        }
    }

    getStatusSeverity(status: number): string {
        switch (status) {
            case -1:
                return 'secondary'; // Vàng
            case 0:
                return 'secondary'; // Vàng
            case 1:
                return 'warning'; // Vàng
            case 2:
                return 'success'; // Xanh
            case 3:
                return 'danger'; // Đỏ
            default:
                return 'info'; // Xanh dương
        }
    }

    confirmPoDraft() {
        this.loadingService.show();
        this.poDraftService.confirm(this.poDraftOld.id, this.poDraftForm.get('scBomIds').getRawValue()).subscribe({
            next: (res) => {
                if (res.code === 0) {
                    const data: Record<string, string[]> = res.data as Record<string, string[]>;

                    this.warnings = Object.entries(data).flatMap(([key, errors]) => {
                        const [internalReferences, po] = key.split('*');
                        return errors.map((error) => ({
                            internalReferences,
                            po,
                            error,
                        }));
                    });
                    this.isOpenWarningModal = true;
                } else {
                    this.alertService.success('Thành công');
                    this.loadData();
                }
                this.loadingService.hide();
                /*if (res.data.warnings != null) {
                    this.loadingService.hide();
                    this.warnings = res.data.warnings.map((item: string) => {
                        const [po, internalReferences, error] = item.split('*');
                        return { po, internalReferences, error };
                    });
                    this.isOpenWarningModal = true;
                } else {
                    this.alertService.success('Thành công');
                    this.loadData();
                }*/
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    approvePoDraft() {
        const selectedIds: number[] = this.selectedPos.map((po) => po.get('id')?.value).filter((id) => id !== null && id !== undefined);
        if (selectedIds !== null && selectedIds.length > 0) {
            this.loadingService.show();
            this.poDraftService.approvePo(this.poDraftOld.id, selectedIds).subscribe({
                next: () => {
                    this.alertService.success('Thành công');
                    this.loadingService.hide();
                    this.loadData();
                },
                error: () => {
                    this.loadingService.hide();
                },
            });
        } else {
            this.alertService.error('Chọn ít nhất một PO để phê duyệt');
        }
    }

    downBoq(id: number) {
        this.poDraftService.getBoq(id).subscribe({
            next: (res) => {
                this.fileService.downLoadFileByService(res.url, '/sc/api');
            },
        });
    }

    saveNote() {
        this.loadingService.show();
        const data = this.poDraftForm.getRawValue();
        this.poDraftService.update(data).subscribe({
            next: () => {
                this.alertService.success('Thành công');
                this.loadingService.hide();
                this.loadData();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    get hasDraftPo(): boolean {
        return this.poItems?.controls?.some((po) => po.get('state')?.value === PO_STATE_CONSTANT.DRAFT);
    }

    protected readonly RFQ_ITEM_TYPE = RFQ_ITEM_TYPE;
    protected readonly poStates = PoStates;
    protected readonly PO_DRAFT_STATUS = PO_DRAFT_STATUS_CONSTANT;
    protected readonly PO_STATE_CONSTANT = PO_STATE_CONSTANT;
}
