import { canAuthorize } from '../../../core/auth/auth.guard';

export const ApproveRouting = {
    path: 'approve',
    title: '<PERSON><PERSON> duyệt',
    children: [
        {
            path: '',
            canActivate: [canAuthorize],
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'ROLE_QC_SUBPM', 'ROLE_QC_CUSTOMER'] },
            loadComponent: () => import('./approve.component').then((c) => c.ApproveComponent),
        },
        // ⚠ Đặt các route tĩnh trước route động!
        {
            path: 'customer/:id',
            canActivate: [canAuthorize],
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_CUSTOMER'] },
            loadComponent: () =>
                import('./approve-customer.detail.component').then((c) => c.ApproveCustomerDetailComponent),
        },
        {
            path: 'sub/:id',
            canActivate: [canAuthorize],
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_SUBPM'] },
            loadComponent: () => import('./approve-sub.detail.component').then((c) => c.ApproveSubDetailComponent),
        },
        // ⚠ Đặt route động sau cùng
        {
            path: ':id/:contractId',
            canActivate: [canAuthorize],
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM'] },
            children: [
                {
                    path: '',
                    canActivate: [canAuthorize],
                    data: { authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM'] },
                    loadComponent: () => import('./approve.detail.component').then((c) => c.ApproveDetailComponent),
                },
                {
                    path: 'history',
                    canActivate: [canAuthorize],
                    data: { authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM'] },
                    loadComponent: () => import('./approve.log.component').then((c) => c.ApproveLogComponent),
                },
            ],
        },
    ],
};
