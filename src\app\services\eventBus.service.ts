import { Injectable } from '@angular/core';
import { Subject, Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';

interface BusEvent {
    name: string;
    payload?: any;
}

@Injectable({ providedIn: 'root' })
export class EventBusService {
    private bus = new Subject<BusEvent>();

    /** Gửi một event với tên và payload tuỳ ý */
    emit(name: string, payload?: any): void {
        this.bus.next({ name, payload });
    }

    /** Lắng nghe event theo tên, chỉ nhận payload */
    on<T = any>(name: string): Observable<T> {
        return this.bus.pipe(
            filter((e) => e.name === name),
            map((e) => e.payload as T),
        );
    }
}
