import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { LineSupplierDelivery, SupplierDeliveryQuality } from '../../../models/interface/sc';
import { ApiResponse } from 'src/app/models/interface';

@Injectable()
export class SupplierDeliveryQualityService extends BaseService<SupplierDeliveryQuality> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/supplier-delivery-quality');
    }

    lineSupplierDeliveryQuality(id: number, startTime: number, endTime: number) {
        return this.http.get<LineSupplierDelivery>(
            `/sc/api/supplier-delivery-quality/line-supplier?supplierId=${id}&startTime=${startTime}&endTime=${endTime}`,
        );
    }

    importCreate(file: File) {
        const formData: FormData = new FormData();
        formData.append('file', file);

        return this.http.post<ApiResponse>('/sc/api/supplier-delivery-quality/import-create', formData);
    }
}
