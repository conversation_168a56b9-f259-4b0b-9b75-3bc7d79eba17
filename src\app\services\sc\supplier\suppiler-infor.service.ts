import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { ApiResponse } from 'src/app/models/interface';
import { Supplier, SupplierDocumentRes } from '../../../models/interface/sc';
import { ParamsTable } from 'src/app/shared/table-module/table.common.service';

@Injectable({
    providedIn: 'root',
})
export class SupplierInforService extends BaseService<Supplier> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/supplier');
    }

    importFileQuality(file: File, supplierId?: number) {
        const formData: FormData = new FormData();
        formData.append('file', file);

        if (supplierId) {
            formData.append('supplierId', supplierId.toString());
        }

        return this.http.post<ApiResponse>('/sc/api/supplier/import-file-quality', formData);
    }

    exportExcel(cols: string[], params: ParamsTable) {
        return this.http.post(`/sc/api/supplier/export-excel?query=${params.rsql}`, cols, { responseType: 'blob' });
    }

    exportExcelDetail(id: number) {
        return this.http.get(`/sc/api/supplier/export-detail/${id}`, { responseType: 'blob' });
    }

    importReportQuality(file: File, date: number) {
        const formData: FormData = new FormData();
        formData.append('file', file);
        formData.append('date', date.toString());

        return this.http.post<ApiResponse>('/sc/api/supplier/import-report-quality', formData);
    }

    importCreate(file: File) {
        const formData: FormData = new FormData();
        formData.append('file', file);

        return this.http.post<ApiResponse>('/sc/api/supplier/import-create', formData);
    }

    checkQualityAgree(url: string) {
        return this.http.post<SupplierDocumentRes>(`/sc/api/supplier/check-quality-agree`, url);
    }

    exportReport(value: { startTime: number; endTime: number; type: string }) {
        return this.http.get(
            `/sc/api/supplier/export-report?type=${value.type}&startTime=${value.startTime}&endTime=${value.endTime}`,
            { responseType: 'blob' },
        );
    }

    restore(ids: number[]) {
        return this.http.put<SupplierDocumentRes>(`/sc/api/supplier/restore`, ids);
    }

    tempDelete(ids: number[]) {
        return this.http.post<number[]>(`/sc/api/supplier/temp-delete`, ids);
    }
}
