<app-sub-header [items]="[{ label: '<PERSON> dõi yêu cầu vận chuyển' }, { label: '<PERSON><PERSON> sách' }]"></app-sub-header>

<div style="padding: 1rem 1rem 0 1rem">
    <app-table-multi
        [tableIds]="[tableIdDoing, tableIdSuccess]"
        [configMulti]="{
            BO_TRACKING_DOING: {
                name: '<PERSON><PERSON> thực hiện',
                columns: columns,
                selectionMode: null,
                loading: stateDoing && stateDoing?.isFetching,
                funcDelete: null,
                data: stateDoing && stateDoing?.data,
                filterTemplate: filterTemplateDoing,
                authoritiesDelete: ['ROLE_SYSTEM_ADMIN', 'sc_lot_delete'],
            },
            BO_TRACKING_SUCCESS: {
                name: 'Đ<PERSON> hoàn thành',
                columns: columns,
                selectionMode: null,
                funcDelete: null,
                loading: stateSuccess && stateSuccess?.isFetching,
                data: stateSuccess && stateSuccess?.data,
                filterTemplate: filterTemplateSuccess,
                authoritiesDelete: ['ROLE_SYSTEM_ADMIN', 'sc_lot_delete'],
            },
        }"
    >
        <ng-template #filterTemplateDoing>
            <tr>
                <th [appFilter]="[tableIdDoing, 'boCode']">
                    <app-filter-table
                        [tableId]="tableIdDoing"
                        field="boIds"
                        type="select"
                        [rsql]="false"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'code',
                            rsql: true,
                            param: 'code',
                            paramForm: 'id',
                            url: '/sc/api/bo/search',
                        }"
                        placeholder="Số bo"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableIdDoing, 'supplierName']">
                    <app-filter-table [rsql]="false" [tableId]="tableIdDoing" field="supplierName"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdDoing, 'poNumber']">
                    <app-filter-table placeholder="Số po" [rsql]="false" [tableId]="tableIdDoing" field="poNumber"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdDoing, 'lotCode']">
                    <app-filter-table placeholder="Số vận đơn" [rsql]="false" [tableId]="tableIdDoing" field="lotCode"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdDoing, 'declarationCode']">
                    <app-filter-table placeholder="Số tờ khai" [rsql]="false" [tableId]="tableIdDoing" field="declarationCode"></app-filter-table>
                </th>

                <th [appFilter]="[tableIdDoing, 'type']">
                    <app-filter-table
                        [rsql]="false"
                        [tableId]="tableIdDoing"
                        field="types"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'label',
                            filterLocal: true,
                            options: TYPE_SHIPPING,
                        }"
                        placeholder="Phân loại"
                    ></app-filter-table>
                </th>

                <th [appFilter]="[tableIdDoing, 'indexShipment']">
                    <app-filter-table
                        placeholder="Stt shipment Po"
                        type="number"
                        [rsql]="false"
                        [tableId]="tableIdDoing"
                        field="indexShipment"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableIdDoing, 'accountingCode']">
                    <app-filter-table placeholder="Mã kết toán/mã vụ việc" [rsql]="false" [tableId]="tableIdDoing" field="accountingCode"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdDoing, 'readyDate']">
                    <app-filter-table
                        placeholder="Thời gian hàng hóa ready"
                        [rsql]="false"
                        [tableId]="tableIdDoing"
                        field="readyDateStart&readyDateEnd"
                        type="date-range"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableIdDoing, 'requiredArrivedDate']">
                    <app-filter-table
                        placeholder="Thời gian yêu cầu hàng về"
                        [rsql]="false"
                        [tableId]="tableIdDoing"
                        field="requiredArrivedDateStart&requiredArrivedDateEnd"
                        type="date-range"
                    ></app-filter-table>
                </th>

                <th [appFilter]="[tableIdDoing, 'totalWeight']">
                    <app-filter-table placeholder="Khối lượng" [rsql]="false" [tableId]="tableIdDoing" field="totalWeight"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdDoing, 'packageNumber']">
                    <app-filter-table placeholder="Số kiện" [rsql]="false" [tableId]="tableIdDoing" field="packageNumber"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdDoing, 'department']">
                    <app-filter-table
                        [rsql]="false"
                        [tableId]="tableIdDoing"
                        field="departmentIds"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            url: '/sc/api/department/search',
                            rsql: true,
                            param: 'name',
                            paramForm: 'id',
                        }"
                        placeholder="Phòng ban"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableIdDoing, 'note']">
                    <app-filter-table placeholder="Ghi chú" [rsql]="false" [tableId]="tableIdDoing" field="note"></app-filter-table>
                </th>
            </tr>
        </ng-template>

        <ng-template #filterTemplateSuccess>
            <tr>
                <th [appFilter]="[tableIdSuccess, 'boCode']">
                    <app-filter-table
                        [tableId]="tableIdSuccess"
                        field="boIds"
                        type="select"
                        [rsql]="false"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'code',
                            rsql: true,
                            param: 'code',
                            paramForm: 'id',
                            url: '/sc/api/bo/search',
                        }"
                        placeholder="Số bo"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableIdSuccess, 'supplierName']">
                    <app-filter-table [rsql]="false" [tableId]="tableIdSuccess" field="supplierName"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdSuccess, 'poNumber']">
                    <app-filter-table placeholder="Số po" [rsql]="false" [tableId]="tableIdSuccess" field="poNumber"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdSuccess, 'lotCode']">
                    <app-filter-table placeholder="Số vận đơn" [rsql]="false" [tableId]="tableIdSuccess" field="lotCode"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdSuccess, 'declarationCode']">
                    <app-filter-table placeholder="Số tờ khai" [rsql]="false" [tableId]="tableIdSuccess" field="declarationCode"></app-filter-table>
                </th>

                <th [appFilter]="[tableIdSuccess, 'type']">
                    <app-filter-table
                        [rsql]="false"
                        [tableId]="tableIdSuccess"
                        field="types"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'label',
                            filterLocal: true,
                            options: TYPE_SHIPPING,
                        }"
                        placeholder="Phân loại"
                    ></app-filter-table>
                </th>

                <th [appFilter]="[tableIdSuccess, 'indexShipment']">
                    <app-filter-table
                        placeholder="Stt shipment Po"
                        type="number"
                        [rsql]="false"
                        [tableId]="tableIdSuccess"
                        field="indexShipment"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableIdSuccess, 'accountingCode']">
                    <app-filter-table placeholder="Mã kết toán/mã vụ việc" [rsql]="false" [tableId]="tableIdSuccess" field="accountingCode"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdSuccess, 'readyDate']">
                    <app-filter-table
                        placeholder="Thời gian hàng hóa ready"
                        [rsql]="false"
                        [tableId]="tableIdSuccess"
                        field="readyDateStart&readyDateEnd"
                        type="date-range"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableIdSuccess, 'requiredArrivedDate']">
                    <app-filter-table
                        placeholder="Thời gian yêu cầu hàng về"
                        [rsql]="false"
                        [tableId]="tableIdSuccess"
                        field="requiredArrivedDateStart&requiredArrivedDateEnd"
                        type="date-range"
                    ></app-filter-table>
                </th>

                <th [appFilter]="[tableIdSuccess, 'totalWeight']">
                    <app-filter-table placeholder="Khối lượng" [rsql]="false" [tableId]="tableIdSuccess" field="totalWeight"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdSuccess, 'packageNumber']">
                    <app-filter-table placeholder="Số kiện" [rsql]="false" [tableId]="tableIdSuccess" field="packageNumber"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdSuccess, 'department']">
                    <app-filter-table
                        [rsql]="false"
                        [tableId]="tableIdSuccess"
                        field="departmentIds"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            url: '/sc/api/department/search',
                            rsql: true,
                            param: 'name',
                            paramForm: 'id',
                        }"
                        placeholder="Phòng ban"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableIdSuccess, 'note']">
                    <app-filter-table placeholder="Ghi chú" [rsql]="false" [tableId]="tableIdSuccess" field="note"></app-filter-table>
                </th>
            </tr>
        </ng-template>

        <ng-template #templateType let-rowData>
            {{ MAP_TYPE_SHIPPING[rowData.type] }}
        </ng-template>

        <ng-template #templateDepartment let-rowData>
            {{ mapDepartment[rowData.departmentId] }}
        </ng-template>
    </app-table-multi>
</div>
