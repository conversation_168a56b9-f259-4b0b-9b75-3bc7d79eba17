<p-table
    [columns]="columns"
    [value]="data"
    [paginator]="data?.length > 0"
    [rows]="rows"
    [responsiveLayout]="'scroll'"
    [style]="{ 'margin-bottom': '1rem' }"
>
    <!-- HEADER -->
    <ng-template pTemplate="header" let-cols>
        <tr>
            <th *ngFor="let col of cols" [style.width]="col.width || null">
                {{ col.header }}
            </th>
        </tr>
    </ng-template>

    <!-- BODY -->
    <ng-template pTemplate="body" let-row let-cols="columns">
        <tr>
            <td *ngFor="let col of cols">
                <!-- nếu có template custom cho col.field thì render nó -->
                <ng-container
                    *ngTemplateOutlet="
                        cellTemplateMap.get(col.field) || defaultCell;
                        context: { $implicit: row, col: col }
                    "
                ></ng-container>
            </td>
        </tr>
    </ng-template>
    <!-- EMPTY MESSAGE -->
    <ng-template pTemplate="emptymessage">
        <tr>
            <!-- colspan bằng số cột để nội dung căn giữa -->
            <td [attr.colspan]="columns.length" class="text-center tw-text-gray-500 tw-italic">Chưa có hồ sơ</td>
        </tr>
    </ng-template>
</p-table>

<!-- defaultCell: fallback khi parent không custom -->
<ng-template #defaultCell let-row let-col="col">
    {{ row[col.field] }}
</ng-template>
