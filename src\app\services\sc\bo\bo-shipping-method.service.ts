import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { BoShippingMethod } from '../../../models/interface/sc';

@Injectable()
export class BoShippingMethodService extends BaseService<BoShippingMethod> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/bo-shipping-method');
    }
}
