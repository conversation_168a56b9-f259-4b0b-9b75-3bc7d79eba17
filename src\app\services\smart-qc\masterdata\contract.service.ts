import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Contract, ContractFilter, ContractMaintenance } from 'src/app/models/interface/smart-qc';
import { ParamsTable } from 'src/app/shared/table-module/table.common.service';
import { ApiResponse } from '../../../models/interface';

@Injectable({
    providedIn: 'root',
})
export class ContractService {
    constructor(private http: HttpClient) {}

    getPage(params) {
        return this.http.get<Contract[]>('/smart-qc/api/contract/search?' + params);
    }

    getPageTable({ native = '', pageable = '&page=0&size=10', rsql = '' }: ParamsTable) {
        return this.http.get<Contract[]>(`/smart-qc/api/contract/search?query=${rsql}${native}${pageable}`, {
            observe: 'response',
        });
    }
    getPageTableCustom({ pageable = '&page=0&size=10' }: ParamsTable, body: ContractFilter) {
        return this.http.post<Contract[]>(`/smart-qc/api/contract/search-native?${pageable}`, body, {
            observe: 'response',
        });
    }

    getOne(id) {
        return this.http.get<Contract>('/smart-qc/api/contract/' + id);
    }

    getContractMaintenance(stationId: number) {
        return this.http.get<ContractMaintenance>(`/smart-qc/api/contract/get-contract-maintenance?stationId=${stationId}`);
    }

    createOne(body) {
        const headers = new HttpHeaders().set('show-loading', 'true');
        return this.http.post<Contract>('/smart-qc/api/contract/', body, { headers });
    }

    updateOne(body) {
        const headers = new HttpHeaders().set('show-loading', 'true');
        return this.http.put<Contract>('/smart-qc/api/contract/' + body.id, body, { headers });
    }

    batchDelete(ids) {
        return this.http.post<number[]>('/smart-qc/api/contract/batch-delete', ids);
    }
    simpleUpdateOne(body) {
        const headers = new HttpHeaders().set('show-loading', 'true');
        return this.http.put<Contract>('/smart-qc/api/contract/simple-update/' + body.id, body, { headers });
    }

    importError(file: File, contractId: number) {
        const formData: FormData = new FormData();
        formData.append('file', file);
        if (contractId) {
            formData.append('contractId', contractId.toString());
        }

        return this.http.post<ApiResponse>('/smart-qc/api/contract/import-file-error', formData);
    }
}
