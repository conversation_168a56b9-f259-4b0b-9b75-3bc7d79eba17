import { AfterViewInit, Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { RouterLink } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { Column } from 'src/app/models/interface';
import { TABLE_KEY } from 'src/app/models/constant';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { Bo } from 'src/app/models/interface/sc';
import { BoService } from 'src/app/services/sc/bo/bo.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { TableCommonService } from 'src/app/shared/table-module/table.common.service';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { MAP_TYPE_SHIPPING, TYPE_SHIPPING } from 'src/app/models/constant/sc';
import { SCDepartmentService } from 'src/app/services/sc/bo/department.service';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { forkJoin } from 'rxjs';
import { PackageTypeService } from 'src/app/services/sc/bo/package-type.service';

@Component({
    selector: 'app-bo-list',
    standalone: true,
    imports: [CommonModule, SubHeaderComponent, RouterLink, ButtonModule, TableCommonModule, HasAnyAuthorityDirective],
    templateUrl: './list.component.html',
    styleUrls: ['./list.component.scss'],
    providers: [BoService, SCDepartmentService, PackageTypeService],
})
export class ListComponent implements OnInit, AfterViewInit {
    @ViewChild('templateType') templateType: TemplateRef<Element>;
    @ViewChild('templateDepartment') templateDepartment: TemplateRef<Element>;
    columns: Column[] = [];
    tableId: string = TABLE_KEY.BO;
    state: QueryObserverBaseResult<Bo[]>;

    //option
    TYPE_SHIPPING = TYPE_SHIPPING;
    MAP_TYPE_SHIPPING = MAP_TYPE_SHIPPING;
    mapDepartment: Record<number, string> = {};
    mapPackageTypes: Record<number, string> = {};

    constructor(
        private alertService: AlertService,
        private tableCommonService: TableCommonService,
        private boService: BoService,
        private sCDepartmentService: SCDepartmentService,
        private packageTypeService: PackageTypeService,
    ) {}

    ngOnInit(): void {
        forkJoin({
            departments: this.sCDepartmentService.getPage('query=&page=0&size=100&sort=id,desc'),
            packageTypes: this.packageTypeService.getPage('query=&page=0&size=100&sort=name,asc'),
        }).subscribe({
            next: ({ departments, packageTypes }) => {
                // Xử lý departments
                departments.body.forEach((item) => {
                    this.mapDepartment[item.id] = item.name;
                });

                // Gán packageTypes
                packageTypes.body.forEach((item) => {
                    this.mapPackageTypes[item.id] = item.name;
                });
            },
            error: (err) => {
                console.error('Error loading data:', err);
            },
        });
        this.tableCommonService
            .init<Bo>({
                tableId: this.tableId,
                queryFn: (filter) => this.boService.getPageTableCustom(filter),
                configFilterRSQL: {
                    type: 'Number',
                    id: 'Number',
                    poNumber: 'Text',
                    indexShipment: 'Number',
                    accountingCode: 'Text',
                    readyDate: 'DateRange',
                    requiredArrivedDate: 'DateRange',
                    totalWeight: 'Text',
                    packageNumber: 'Text',
                    note: 'Text',
                    departmentId: 'Number',
                    status: 'NotEqual',
                    supplierName: 'Text',
                },
                defaultParamsRSQL: {
                    status: -1,
                },
            })
            .subscribe({
                next: (res) => {
                    this.state = res;
                },
                error: (error) => {
                    this.alertService.handleError(error);
                },
            });
    }
    ngAfterViewInit(): void {
        setTimeout(() => {
            this.columns = [
                {
                    header: 'Số BO',
                    field: 'code',
                    type: 'link',
                    url: './{id}',
                    default: true,
                },
                {
                    field: 'supplierName',
                    header: 'Tên NCC',
                    default: true,
                },
                {
                    field: 'poNumber',
                    header: 'Số PO',
                    default: true,
                },
                { field: 'type', header: 'Phân loại', style: { 'max-width': '8rem' }, body: this.templateType },
                { field: 'indexShipment', header: 'STT shipment Po', type: 'number' },
                { field: 'accountingCode', header: 'Mã kế toán/mã vụ việc' },
                {
                    field: 'readyDate',
                    header: 'Thời gian hàng hóa ready',
                    type: 'date',
                    format: 'dd/MM/yyyy',
                },
                {
                    field: 'requiredArrivedDate',
                    header: 'Thời gian yêu cầu hàng về',
                    type: 'date',
                    format: 'dd/MM/yyyy',
                },
                { field: 'totalWeight', header: 'Khối lượng', type: 'number' },
                { field: 'packageNumber', header: 'Số kiện', type: 'number' },
                { field: 'departmentId', header: 'Phòng ban', body: this.templateDepartment },
                { field: 'note', header: 'Ghi chú' },
            ];
        });
    }
    deleteSelected = (ids: number[]) => {
        return this.boService.batchDelete(ids);
    };
}
