import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import Common from 'src/app/utils/common';

@Injectable()
export class InfinitescrollService {
    component: unknown[];
    private http = inject(HttpClient);

    fetchMore(term, datas, component: unknown) {
        // let api : string = this.genQuery(term);
        this.http.get<unknown[]>(component['api'], { observe: 'response' }).subscribe((res) => {
            component['options'] = Common.mergeArray(component['options'], res.body, (a, b) => a.id === b.id);
            component['loading'] = false;

            if (term === '' || term === undefined) {
                component['originPage'] = ++component['page'];
            }
        });
    }

    // onSearch(e) {
    //     this.page = (e.term == "" || e.term == undefined)
    //                 ? this.originPage : 0;
    //     this.fetchMore(e.term);
    // }

    // onRemove(e) {
    //     this.page = this.originPage;
    //     this.fetchMore("")
    //     this.selected = this.selected.filter(obj => obj.id != e.id);
    //     this.handleChangeSelected.emit([this.selected]);
    // }

    // onClear(e) {
    //     this.page = this.originPage;
    //     this.fetchMore("");
    // }

    // onAdd(e) {
    //     let selected = this.options.filter(obj => obj.id == e.id);
    //     this.selected.push(...selected)
    //     this.handleChangeSelected.emit([this.selected]);
    // }

    // genQuery(param) {
    //     let query = this.url;
    //     if (param != "" && param != undefined) {
    //         query += `${this.itemLable}=='*${param}*'`
    //     }
    //     return `${query}&page=${this.page}&size=10&sort=id,desc`
    // }

    // onChange(e) {
    //     this.callback(e);
    // }
}
