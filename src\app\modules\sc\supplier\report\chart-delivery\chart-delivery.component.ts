import { AfterViewInit, Component, ElementRef, Input, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { LineSupplierDelivery } from 'src/app/models/interface/sc';
import { ChartConfiguration } from 'chart.js';
import Chart from 'chart.js/auto';
import { TableModule } from 'primeng/table';
import { SupplierDeliveryQualityService } from 'src/app/services/sc/supplier-report/suppiler-delivery-quality.service';
import { SkeletonLoadingComponent } from 'src/app/shared/components/skeleton-loading/skeleton-loading.component';
import { CommonModule } from '@angular/common';
import { catchError, Observable, tap, throwError } from 'rxjs';
@Component({
    selector: 'app-supplier-chart-delivery',
    templateUrl: './chart-delivery.component.html',
    styleUrls: ['./chart-delivery.component.scss'],
    standalone: true,
    imports: [CommonModule, TableModule, SkeletonLoadingComponent],
    providers: [SupplierDeliveryQualityService],
})
export class ChartDeliveryComponent implements AfterViewInit {
    @Input() formReport: FormGroup;
    @Input() supplierId: number;

    lineSupplierDeliveries: LineSupplierDelivery;
    @ViewChild('templateChartLine') templateChartLine: ElementRef<HTMLCanvasElement>;
    isLoading = true;
    chartLineSupplierPrice: Chart;

    constructor(private supplierDeliveryQualityService: SupplierDeliveryQualityService) {}

    ngAfterViewInit() {
        if (!this.isLoading) {
            this.initializeChart();
        }
    }

    initializeChart() {
        if (this.chartLineSupplierPrice) {
            this.chartLineSupplierPrice.destroy();
        }

        const ctx: HTMLCanvasElement = this.templateChartLine.nativeElement;

        const config: ChartConfiguration<'line', number[], string> = {
            type: 'line',
            data: {
                labels: this.lineSupplierDeliveries.labels,
                datasets: [
                    {
                        label: 'Điểm',
                        data: this.lineSupplierDeliveries.points,
                    },
                ],
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    filler: {
                        propagate: false,
                    },
                    title: {
                        display: true,
                        text: 'Tiến độ gia hàng',
                    },
                },
                interaction: {
                    intersect: false,
                },
            },
        };

        this.chartLineSupplierPrice = new Chart(ctx, config);
    }

    callApiReportDelivery(): Observable<LineSupplierDelivery> {
        let startTime = new Date();
        let endTime = new Date();
        this.isLoading = true;

        if (this.formReport.value['type'] === 'year') {
            startTime.setFullYear(this.formReport.value['year'], 0, 1);
            endTime.setFullYear(this.formReport.value['year'], 11, 1);
        } else {
            startTime = this.formReport.value['startTime'];
            endTime = this.formReport.value['endTime'];
        }
        return this.supplierDeliveryQualityService
            .lineSupplierDeliveryQuality(this.supplierId, startTime.getTime(), endTime.getTime())
            .pipe(
                tap((res: LineSupplierDelivery) => {
                    this.lineSupplierDeliveries = res;
                    this.isLoading = false;

                    // If the chart template exists, initialize the chart
                    if (this.templateChartLine) {
                        this.initializeChart();
                    }
                }),
                catchError((error) => {
                    this.isLoading = false;
                    return throwError(error);
                }),
            );
    }
}
