<app-sub-header [items]="itemsHeader" [action]="actionHeader"> </app-sub-header>
<ng-template #actionHeader>
    <p-button label="Lưu" severity="success" (click)="submitForm('formPL')" [disabled]="isViewOnly" />
    <p-button routerLink="/ptm/project-plans" label="Hủy" severity="secondary" />
</ng-template>
<div style="padding: 1rem 1rem 0 1rem">
    <app-form formId="formPL" [formGroup]="formGroup">
        <p-panel>
            <ng-template pTemplate="header">
                <div class="flex justify-between w-full tw-items-center">
                    <span class="tw-font-bold">Thông tin chung</span>
                    <span class="ml-auto tw-mr-5">
                        <app-wizard [currentState]="activeIndex" [states]="itemsStep"> </app-wizard>
                    </span>
                </div>
            </ng-template>
            <div class="tw-grid lg:tw-grid-cols-2 tw-grid-cols-1 tw-gap-4">
                <app-custom-form-item label="Tên dự án" [control]="formGroup.get('projectName')">
                    <input type="text" class="tw-w-full" pInputText formControlName="projectName" maxlength="50" />
                </app-custom-form-item>
                <app-custom-form-item label="Tên sản phẩm" [control]="formGroup.get('productName')">
                    <app-combobox-nonRSQL
                        #projectNameFilter
                        [fetchOnInit]="false"
                        type="select-one"
                        formControlName="productName"
                        fieldValue="id"
                        fieldLabel="name"
                        url="/pr/api/product"
                        param="name"
                        placeholder=""
                        [additionalParams]="{ size: 100 }"
                        (onChange)="onProductSelect($event)"
                    ></app-combobox-nonRSQL>
                </app-custom-form-item>
                <app-custom-form-item label="Start date" [control]="formGroup.get('startDate')">
                    <p-calendar
                        [showButtonBar]="true"
                        placeholder="dd/mm/yyyy"
                        [showIcon]="true"
                        dateFormat="dd/mm/yy"
                        class="tw-w-full"
                        formControlName="startDate"
                        [maxDate]="formGroup.get('endDate')?.value"
                    >
                    </p-calendar>
                </app-custom-form-item>
                <app-custom-form-item label="VNPT Man P/N" [control]="formGroup.get('vnptManPn')">
                    <app-combobox-nonRSQL
                        #customerFilter
                        [fetchOnInit]="false"
                        type="select-one"
                        formControlName="vnptManPn"
                        fieldValue="id"
                        (panelShow)="handlePanelShow(customerFilter)"
                        fieldLabel="vnptManPn"
                        url="/pr/api/product"
                        param="vnptManPn"
                        placeholder=""
                        [additionalParams]="{ size: 100 }"
                        (onChange)="onProductSelect($event)"
                    >
                    </app-combobox-nonRSQL>
                    <!-- <input type="text" class="tw-w-full" pInputText formControlName="vnptManPN" /> -->
                </app-custom-form-item>
                <app-custom-form-item label="End date" [control]="formGroup.get('endDate')">
                    <p-calendar
                        [showButtonBar]="true"
                        placeholder="dd/mm/yyyy"
                        [showIcon]="true"
                        dateFormat="dd/mm/yy"
                        class="tw-w-full"
                        formControlName="endDate"
                        [minDate]="formGroup.get('startDate')?.value"
                    >
                    </p-calendar>
                </app-custom-form-item>
                <app-custom-form-item label="Tỷ lệ hoàn thành">
                    <p-tag severity="info" class="tw-text-base tw-px-3 tw-py-2" [value]="Info + ' task hoàn thành'"></p-tag>
                    <!-- <input type="text" class="tw-w-full" pInputText formControlName="completionRate" /> -->
                </app-custom-form-item>
                <app-custom-form-item label="PIC dự án/ PM" [control]="formGroup.get('picOrPm')">
                    <!-- <input type="text" class="tw-w-full" pInputText formControlName="picOrPm" /> -->
                    <app-combobox-nonRSQL
                        #picIdFilter
                        (panelShow)="handlePanelShow(picIdFilter)"
                        [fetchOnInit]="false"
                        type="select-one"
                        formControlName="picOrPm"
                        fieldValue="id"
                        fieldLabel="fullName"
                        url="/pr/api/user/list"
                        param="query"
                        placeholder=""
                        [additionalParams]="{ page: 0, size: 100 }"
                    ></app-combobox-nonRSQL>
                </app-custom-form-item>
            </div>
        </p-panel>

        <br />
        <ng-container *ngIf="mode !== 'create'">
            <app-pro-detail
                [formArray]="formGroup.get('planDetails')"
                [projectId]="projectId"
                [isViewOnly]="isViewOnly"
                (workingDayUpdated)="onWorkingDayUpdated()"
            ></app-pro-detail>
        </ng-container>
    </app-form>
</div>
