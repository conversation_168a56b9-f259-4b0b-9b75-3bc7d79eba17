{
    "root": true,
    "ignorePatterns": ["projects/**/*"],
    "overrides": [
        {
            "files": ["*.ts"],
            "parserOptions": {
                "project": ["tsconfig.json"],
                "createDefaultProgram": true
            },
            "extends": [
                "plugin:@angular-eslint/recommended",
                "plugin:@angular-eslint/template/process-inline-templates",
                "plugin:@typescript-eslint/recommended"
            ],
            "rules": {
                "@angular-eslint/directive-selector": [
                    "error",
                    {
                        "type": "attribute",
                        "prefix": "app",
                        "style": "camelCase"
                    }
                ],
                "@angular-eslint/component-selector": [
                    "error",
                    {
                        "type": "element",
                        "prefix": "app",
                        "style": "kebab-case"
                    }
                ],

                "@typescript-eslint/no-unused-vars": [
                    "error",
                    { "vars": "all", "args": "after-used", "ignoreRestSiblings": false }
                ],
                "unused-imports/no-unused-imports": "error",
                "unused-imports/no-unused-vars": [
                    "warn",
                    {
                        "vars": "all",
                        "varsIgnorePattern": "^_",
                        "args": "after-used",
                        "argsIgnorePattern": "^_"
                    }
                ],
                "eqeqeq": ["error", "always"],
                "@angular-eslint/no-output-rename": "off",
                "@angular-eslint/no-output-on-prefix": "off",
                "@angular-eslint/no-input-rename": "off"
            },
            "plugins": ["unused-imports"]
        },
        {
            "files": ["*.html"],
            "extends": ["plugin:@angular-eslint/template/recommended"],
            "rules": {
                "max-len": ["error", { "code": 160 }], // Giới hạn độ dài dòng trong template
                "@angular-eslint/template/eqeqeq": "error" // Bắt buộc sử dụng === thay vì ==
            }
        }
    ]
}
