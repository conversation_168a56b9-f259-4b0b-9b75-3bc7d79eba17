<ng-container>
    <div class="p-fluid tw-space-y-2">
        <app-info-share
            [title]="title"
            [mode]="mode"
            [detailInfo]="detailManBom"
            [name]="name"
            (changeValueApprover)="handleApproverChange($event)"
        ></app-info-share>
    </div>
    <div class="tw-flex tw-items-center tw-justify-between tw-gap-4 tw-flex-wrap tw-mb-[10px]">
        <label class="tw-font-semibold tw-text-base label">Chi tiết MANBOM</label>

        <div class="tw-flex tw-gap-2">
            <button
                (click)="exportManBomErp()"
                pButton
                type="button"
                label="Xuất ERP"
                class="p-button-sm p-button-primary tw-h-10 tw-whitespace-nowrap"
            ></button>
            <button
                (click)="openTrackDialog(tabType)"
                pButton
                type="button"
                label="Track Changes"
                class="p-button-sm p-button-primary tw-h-10 tw-whitespace-nowrap"
            ></button>
            <button
                (click)="exportManBom()"
                pButton
                type="button"
                label="Xuất excel"
                class="p-button-sm p-button-secondary tw-h-10 tw-whitespace-nowrap"
            ></button>
            <button pButton type="button" label="Import TLTH" class="p-button-sm p-button-secondary tw-h-10 tw-whitespace-nowrap"></button>
        </div>
    </div>
    <app-form-item label="">
        <app-form #form [formGroup]="formGroup" layout="vertical" (onSubmit)="onSubmit()">
            <div formArrayName="listManBom">
                <p-panel [toggleable]="true">
                    <p-table styleClass="p-datatable-gridlines" [value]="manBom.controls">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="min-width: 5rem">STT</th>
                                <th style="min-width: 9rem">Description <span class="tw-text-red-500">*</span></th>
                                <th style="min-width: 9rem">Đơn vị <span class="tw-text-red-500">*</span></th>
                                <th style="min-width: 9rem">Công đoạn <span class="tw-text-red-500">*</span></th>
                                <th style="min-width: 5rem">Số lượng/Công đoạn <span class="tw-text-red-500">*</span></th>
                                <th style="min-width: 9rem">Reference</th>
                                <th style="min-width: 9rem">Loại vật tư <span class="tw-text-red-500">*</span></th>
                                <th style="min-width: 7rem">Tỷ lệ tiêu hao</th>
                                <th style="min-width: 9rem">Ghi chú</th>
                                <th style="max-width: 5rem">Thao tác</th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                            <tr [formGroupName]="rowIndex" *ngIf="item.get('actionPayload')?.value !== 3">
                                <!-- STT -->
                                <td>
                                    <app-form-item label=""> {{ rowIndex + 1 }}</app-form-item>
                                </td>
                                <!-- Description -->
                                <td>
                                    <app-form-item label="" [disableAutoErrorMessage]="true">
                                        <p-dropdown
                                            [options]="listDes"
                                            formControlName="description"
                                            optionLabel="vnptPn"
                                            optionValue="vnptPn"
                                            class="tw-w-full"
                                            [showClear]="true"
                                            appendTo="body"
                                            [filter]="true"
                                            [disabled]="mode === 'view'"
                                            (onChange)="handleChangeValue(rowIndex)"
                                        />
                                        <div *ngIf="item.get('description')?.touched && item.get('description')?.invalid" class="text-red-400">
                                            <ng-container *ngIf="item.get('description')?.errors?.['required']"> Trường này là bắt buộc </ng-container>
                                        </div>
                                    </app-form-item>
                                </td>
                                <!-- Unit -->
                                <td>
                                    <app-form-item label="" [disableAutoErrorMessage]="true">
                                        <input
                                            pInputText
                                            class="tw-w-full"
                                            formControlName="unit"
                                            maxlength="10"
                                            [disabled]="mode === 'view'"
                                            (input)="handleChangeValue(rowIndex)"
                                        />
                                        <div *ngIf="item.get('unit')?.touched && item.get('unit')?.invalid" class="text-red-400">
                                            <ng-container *ngIf="item.get('unit')?.errors?.['required']; else maxLengthError">
                                                Trường này là bắt buộc
                                            </ng-container>

                                            <ng-template #maxLengthError>
                                                <ng-container *ngIf="item.get('unit')?.errors?.['maxlength']">
                                                    Trường này không được phép quá 10 ký tự
                                                </ng-container>
                                            </ng-template>
                                        </div>
                                    </app-form-item>
                                </td>
                                <!-- Process -->
                                <td>
                                    <app-form-item label="" [disableAutoErrorMessage]="true">
                                        <p-dropdown
                                            [options]="processType"
                                            formControlName="section"
                                            optionLabel="label"
                                            optionValue="value"
                                            class="tw-w-full"
                                            [showClear]="true"
                                            appendTo="body"
                                            [filter]="true"
                                            [disabled]="mode === 'view'"
                                            (onChange)="handleChangeValue(rowIndex)"
                                        />
                                    </app-form-item>
                                </td>
                                <!-- Quantity -->
                                <td>
                                    <app-form-item label="" [disableAutoErrorMessage]="true">
                                        <input
                                            pInputText
                                            class="tw-w-full"
                                            formControlName="quantity"
                                            maxlength="10"
                                            [disabled]="mode === 'view'"
                                            (input)="handleChangeValue(rowIndex)"
                                        />
                                        <div *ngIf="item.get('quantity')?.touched && item.get('quantity')?.invalid" class="text-red-400">
                                            <ng-container *ngIf="item.get('quantity')?.errors?.['required']; else maxLengthError">
                                                Trường này là bắt buộc
                                            </ng-container>
                                            <ng-template #maxLengthError>
                                                <ng-container *ngIf="item.get('quantity')?.errors?.['maxlength']; else patternError">
                                                    Trường này không được phép quá 10 ký tự
                                                </ng-container>
                                            </ng-template>
                                            <ng-template #patternError>
                                                <ng-container *ngIf="item.get('quantity')?.hasError('invalidCharacters')">
                                                    Trường này phải có định dạng số, dấu phẩy và dấu chấm
                                                </ng-container>
                                            </ng-template>
                                        </div>
                                    </app-form-item>
                                </td>
                                <!-- Reference -->
                                <td>
                                    <app-form-item label="" [disableAutoErrorMessage]="true">
                                        <input
                                            pInputText
                                            class="tw-w-full"
                                            formControlName="reference"
                                            maxlength="50"
                                            [disabled]="mode === 'view'"
                                            (input)="handleChangeValue(rowIndex)"
                                        />
                                    </app-form-item>
                                </td>
                                <!-- Material Type -->
                                <td>
                                    <app-form-item label="" [disableAutoErrorMessage]="true">
                                        <p-dropdown
                                            [options]="materialType"
                                            formControlName="materialType"
                                            optionLabel="label"
                                            optionValue="value"
                                            class="tw-w-full"
                                            [showClear]="true"
                                            appendTo="body"
                                            [disabled]="mode === 'view'"
                                            (onChange)="handleChangeValue(rowIndex)"
                                        />
                                        <div *ngIf="item.get('materialType')?.touched && item.get('materialType')?.invalid" class="text-red-400">
                                            <ng-container *ngIf="item.get('materialType')?.errors?.['required']"> Trường này là bắt buộc </ng-container>
                                        </div>
                                    </app-form-item>
                                </td>
                                <!-- Attrition Rate -->
                                <td>
                                    <app-form-item label="" [disableAutoErrorMessage]="true">
                                        <input
                                            pInputText
                                            class="tw-w-full"
                                            formControlName="consumptionRate"
                                            maxlength="10"
                                            [disabled]="mode === 'view'"
                                            (input)="handleChangeValue(rowIndex)"
                                        />
                                        <div *ngIf="item.get('consumptionRate')?.touched && item.get('consumptionRate')?.invalid" class="text-red-400">
                                            <ng-container *ngIf="item.get('consumptionRate')?.errors?.['maxlength']; else patternError">
                                                Trường này không được phép quá 10 ký tự
                                            </ng-container>
                                            <ng-template #patternError>
                                                <ng-container *ngIf="item.get('consumptionRate')?.hasError('invalidCharacters')">
                                                    Trường này phải có định dạng số, dấu phẩy và dấu chấm
                                                </ng-container>
                                            </ng-template>
                                        </div>
                                    </app-form-item>
                                </td>
                                <!-- Note -->
                                <td>
                                    <app-form-item label="" [disableAutoErrorMessage]="true">
                                        <input
                                            pInputText
                                            class="tw-w-full"
                                            formControlName="note"
                                            maxlength="50"
                                            [disabled]="mode === 'view'"
                                            (input)="handleChangeValue(rowIndex)"
                                        />
                                    </app-form-item>
                                </td>
                                <!-- Thao tác -->
                                <td>
                                    <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                        <button
                                            class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                            pTooltip="Hủy"
                                            tooltipPosition="top"
                                            type="button"
                                            (click)="removeItem(rowIndex)"
                                            [disabled]="mode === 'view'"
                                        >
                                            <span class="pi pi-times"></span>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <div class="tw-mt-3" *ngIf="mode !== 'view'">
                        <p-button label="Thêm dòng" icon="pi pi-plus" severity="info" size="small" (click)="addItem()"></p-button>
                    </div>
                </p-panel>
            </div>

            <div class="tw-flex tw-gap-4 tw-justify-center tw-mt-4">
                <p-button
                    *ngIf="
                        (detailManBom?.instructionInfo?.status === 1 ||
                            detailManBom?.instructionInfo?.status === 4 ||
                            this.instructionId === 0 ||
                            isCreatingInstruction ||
                            !detailManBom?.instructionInfo?.status) &&
                        mode !== 'view'
                    "
                    label="Lưu"
                    type="submit"
                    [loading]="isSaving | async"
                    (onClick)="handleSubmit($event)"
                    loadingIcon="pi pi-spinner pi-spin"
                    [disabled]="processFlow?.controls.length === 0"
                >
                </p-button>
                <button
                    *ngIf="
                        detailManBom?.instructionInfo?.status === 1 ||
                        detailManBom?.instructionInfo?.status === 4 ||
                        this.instructionId === 0 ||
                        isCreatingInstruction ||
                        !detailManBom?.instructionInfo?.status
                    "
                    label="Gửi review"
                    pButton
                    type="button"
                    [disabled]="(isApproving | async) || processFlow?.controls.length === 0"
                    loadingIcon="pi pi-spinner pi-spin"
                    class="p-button-secondary"
                    (click)="handlePreview()"
                ></button>
                <p-button
                    *ngIf="
                        !isCreatingInstruction &&
                        detailManBom?.instructionInfo?.status !== 1 &&
                        detailManBom?.instructionInfo?.status !== 4 &&
                        this.instructionId !== 0 &&
                        detailManBom?.instructionInfo?.status
                    "
                    label="Phê duyệt"
                    type="button"
                    [disabled]="isApproving | async"
                    [loading]="isApproving | async"
                    loadingIcon="pi pi-spinner pi-spin"
                    (click)="handleComplete()"
                >
                </p-button>
                <button
                    *ngIf="
                        !isCreatingInstruction &&
                        detailManBom?.instructionInfo?.status !== 1 &&
                        detailManBom?.instructionInfo?.status !== 4 &&
                        this.instructionId !== 0 &&
                        detailManBom?.instructionInfo?.status
                    "
                    label="Từ chối"
                    pButton
                    type="button"
                    [disabled]="isApproving | async"
                    loadingIcon="pi pi-spinner pi-spin"
                    class="p-button-danger"
                    (click)="handleReject()"
                ></button>
            </div>
        </app-form>
    </app-form-item>
    <ng-container *ngIf="showTrackDialog">
        <app-track-changes
            [visible]="showTrackDialog"
            [idInstruction]="instructionId"
            [recordId]="selectedTab"
            (onClose)="showTrackDialog = false"
        ></app-track-changes>
    </ng-container>
</ng-container>
