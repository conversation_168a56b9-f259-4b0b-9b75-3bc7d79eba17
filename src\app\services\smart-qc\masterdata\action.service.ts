import { HttpClient, HttpErrorResponse, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { catchError, throwError } from 'rxjs';
import { Action, Station, Task } from 'src/app/models/interface/smart-qc';
import {Area, District, GeneralEntity} from "../../../models/interface";

@Injectable()
export class ActionService {
    constructor(private http: HttpClient) {}

    getTaskInAction(actionId, pagination) {
        return this.http.get<unknown>('/smart-qc/api/task/search?query=actionId==' + actionId + '&' + pagination, {
            observe: 'response',
        });
    }

    searchStationByCode(code) {
        const headers = new HttpHeaders().set('show-loading', 'true');
        return this.http.get<Station>('/smart-qc/api/station/get-by-code?code=' + code, { headers });
    }

    searchTask(actionId, condition, pagable) {
        return this.http.get<Task>(`/smart-qc/api/task/search-advanced?actionId=${actionId}${condition}${pagable}`);
    }

    updateAction(action) {
        const headers = new HttpHeaders().set('show-loading', 'true');
        return this.http.put<Action>('/smart-qc/api/action/' + action.id, action, { headers });
    }

    createAction(action) {
        const headers = new HttpHeaders().set('show-loading', 'true');
        return this.http.post<Action>('/smart-qc/api/action', action, { headers });
    }

    deleteAction(id) {
        const headers = new HttpHeaders().set('show-loading', 'true');
        return this.http.delete<void>('/smart-qc/api/action/' + id, { headers });
    }

    updateTask(task) {
        const headers = new HttpHeaders().set('show-loading', 'true');
        return this.http.put<Task>('/smart-qc/api/task/update-one/' + task.id, task, { headers });
    }

    createTask(task) {
        const headers = new HttpHeaders().set('show-loading', 'true');
        return this.http.post<unknown>('/smart-qc/api/task/create-one', task, { headers });
    }

    deleteTask(id) {
        const headers = new HttpHeaders().set('show-loading', 'true');
        return this.http.delete<unknown>('/smart-qc/api/task/' + id, { headers });
    }

    getDistrictOptions(actionId, areaId) {
        const url = '/smart-qc/api/task/combobox/district?actionId=' + actionId + (areaId ? `areaId=${areaId}` : '');
        return this.http.get<District[]>(url);
    }

    getAreaOptions(actionId) {
        return this.http.get<Area[]>('/smart-qc/api/task/combobox/area?actionId=' + actionId);
    }

    importTask(file, contractId, actionId, templateId, isPMImport) {
        const fd = new FormData();
        fd.append('file', file);
        if (contractId !== undefined && actionId !== undefined) {
            fd.append('contractId', contractId);
            fd.append('actionId', actionId);
            fd.append('templateId', templateId);
        }
        fd.append('isPMImport', isPMImport);

        const headers = new HttpHeaders().set('show-loading', 'true');
        headers.set('Content-Type', undefined);
        return this.http.post<unknown>('/smart-qc/api/task/import-create-task', fd, { headers }).pipe(
            catchError((error: HttpErrorResponse) => {
                return throwError(() => error);
            }),
        );
    }

    importDeleteTask(file, contractId, actionId, templateId) {
        const fd = new FormData();
        fd.append('file', file);
        if (contractId !== undefined && actionId !== undefined) {
            fd.append('contractId', contractId);
            fd.append('actionId', actionId);
            fd.append('templateId', templateId);
        }

        const headers = new HttpHeaders().set('show-loading', 'true');
        headers.set('Content-Type', undefined);
        return this.http.post<unknown>('/smart-qc/api/task/import-delete-task', fd, { headers });
    }

    exportTask(actionId) {
        const headers = new HttpHeaders().set('show-loading', 'true');
        return this.http.post<GeneralEntity>('/smart-qc/api/task/export-task?query=actionId==' + actionId, { headers });
    }

    batchDelete(ids) {
        return this.http.post<unknown[]>(`/smart-qc/api/task/batch-delete`, ids);
    }

    updatePosition(action) {
        return this.http.post<unknown[]>(`/smart-qc/api/action/update-position`, action);
    }
    getFileImport(contractId: string | null, actionId: string | null) {
        const headers = new HttpHeaders().set('show-loading', 'true');

        // Khởi tạo HttpParams trống
        let params = new HttpParams();

        // Thêm các tham số vào HttpParams nếu chúng không phải là null
        if (contractId !== null && contractId !== undefined) {
            params = params.set('contractId', contractId);
        }
        if (actionId !== null && actionId !== undefined) {
            params = params.set('actionId', actionId);
        }

        return this.http.get<GeneralEntity>('/smart-qc/api/task/file-import', {
            headers,
            params,
        });
    }
}
