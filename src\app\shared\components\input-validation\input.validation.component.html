<div [ngClass]="class" [ngStyle]="style">
    <ng-content></ng-content>
    <ng-container *ngIf="control?.errors && control?.touched">
        <p *ngIf="control?.errors['pattern']" class="text-red-400">
            {{ "Trường " + fieldName + " phải có định dạng " + pattern }}
        </p>
        <p *ngIf="control?.errors['required']" class="text-red-400">
            {{ "Trường " + fieldName + " là bắt buộc" }}
        </p>
        <p *ngIf="control?.errors['min']" class="text-red-400">
            {{ "Trường " + fieldName + " tối thiểu " + control?.errors['min'].min + (type === 'text'? " ký tự" : "") }}
        </p>
        <p *ngIf="control?.errors['maxlength']" class="text-red-400">
            {{ "Trường " + fieldName + " không v<PERSON><PERSON><PERSON> qu<PERSON> " + control?.errors['maxlength'].requiredLength + (type === 'text'? " ký tự" : "") }}
        </p>
        <ng-container *ngFor="let validateMessage of validateMessages">
            <p *ngIf="control?.errors[validateMessage.type] || control?.hasError(validateMessage.type)" class="text-red-400">
                {{ validateMessage.message }}
            </p>
        </ng-container>
    </ng-container>
    <ng-container *ngIf="control?.invalid && control?.touched">
        <ng-container *ngFor="let validateMessage of validateMessages">
            <p *ngIf="control?.hasError(validateMessage.type)" class="text-red-400">
                {{ validateMessage.message }}
            </p>
        </ng-container>
    </ng-container>
</div>
