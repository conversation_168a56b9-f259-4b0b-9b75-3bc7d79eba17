# # Stage 1: Build Angular app
# FROM node:18-alpine AS builder

# WORKDIR /app

# RUN npm install -g @angular/cli@16

# COPY package.json yarn.lock ./
# RUN yarn install

# COPY . .

# ARG CONFIG=vivas
# RUN ng build --configuration=$CONFIG

# Stage 2: Serve with NGINX
FROM nginx:1.27.5-alpine

# Copy built Angular dist
# COPY --from=builder /app/dist/new-webapp /usr/share/nginx/html
COPY new-webapp /usr/share/nginx/html

# Copy custom nginx config
COPY nginx.conf /etc/nginx/conf.d/default.conf


EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
