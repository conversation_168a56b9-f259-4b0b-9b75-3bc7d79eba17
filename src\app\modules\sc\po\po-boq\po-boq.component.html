<div class="tw-mb-4" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_po_import']">
    <app-button-group-file
        urlTemplate="BOQ_Template.xlsx"
        [urlError]="urlError"
        service="/sc/api"
        (onFileSelected)="handleSelectFileBOQ($event)"
        (onClearFile)="handleClearFile()"
        [attachment]="po.boqAttachment"
        [disabled]="po.state === PO_STATE_CONSTANT.COMPLETED"
    >
    </app-button-group-file>
</div>

<p-table
    [value]="poBoqQuantityLst.controls"
    styleClass="p-datatable-gridlines"
    [scrollable]="true" scrollHeight="700px"
    [resizableColumns]="true"
>
    <ng-template pTemplate="header">
        <tr>
            <th rowspan="2" class="p-th-sticky" style="left: 0px; min-width: 4rem">STT</th>
            <th rowspan="2" class="p-th-sticky" style="left: 4rem; min-width: 15rem">Mã VNPT</th>
            <th rowspan="2" style="width: 5rem">Mã nhà sản xuất</th>
            <th rowspan="2" style="width: 10rem">Mô tả</th>
            <th rowspan="2" style="width: 5rem">Nhà sản xuất</th>
            <th rowspan="2" style="width: 5rem">
                Đơn giá dự toán
                <span style="display: block; text-align: center">({{ poBoqQuantityFirst?.unitPrice }})</span>
            </th>
            <th style="width: 5rem">Tổng số lượng yêu cầu</th>
            <th rowspan="2">Tồn khả dụng</th>
            <th rowspan="2" style="width: 5rem">SPQ</th>
            <th rowspan="2" style="width: 5rem">MOQ</th>
            <th style="width: 5rem">Số lượng đặt hàng</th>
            <th [attr.colspan]="poBoqQuantityFirst?.projectQuantities?.length || 1">Số lượng theo dự án</th>
            <th style="width: 5rem">Đơn giá đặt hàng</th>
            <th [attr.colspan]="poBoqQuantityFirst?.projectValue?.length || 1" style="width: 5rem">
                Giá trị dự án ({{ poBoqQuantityFirst?.unitPrice }})
            </th>
            <th rowspan="2" style="width: 5rem">Số lượng mua thừa</th>
            <th rowspan="2" style="width: 5rem">Số tiền mua thừa</th>
            <th style="width: 5rem">Thời gian giao hàng</th>
            <th [attr.colspan]="poBoqQuantityFirst?.projectDeliver?.length || 1" style="width: 5rem">Lịch giao hàng dự kiến</th>
            <th style="width: 5rem">Tổng giá trị</th>
            <th rowspan="2" style="width: 10rem">Ghi chú</th>
        </tr>
        <tr>
            <th class="text-center">{{ poBoqQuantityFirst?.unitQuantity }}</th>
            <th>{{ poBoqQuantityFirst?.unitQuantity }}</th>
            <th *ngFor="let item of poBoqQuantityFirst?.projectQuantities || [{ name: '' }]">
                {{ item.name }}
            </th>
            <th class="text-center">{{ poBoqQuantityFirst?.unitPrice }}</th>
            <th *ngFor="let item of poBoqQuantityFirst?.projectValue || [{ name: '' }]">
                {{ item.name }}
            </th>
            <th class="text-center">{{ poBoqQuantityFirst?.unitDeliveryTime === 0 ? 'Ngày' : 'Tuần' }}</th>
            <th *ngFor="let item of poBoqQuantityFirst?.projectDeliver || [{ date: '' }]">
                {{ item.date | date: 'dd/MM/yyyy' }}
            </th>
            <th class="text-center">{{ poBoqQuantityFirst?.unitPrice }}</th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
        <tr>
            <td class="p-td-sticky" [attr.rowspan]="item.get('poBoqList')?.length || 1" style="left: 0px; min-width: 4rem">
                {{ item.get('indexLevel')?.value }}
            </td>
            <td class="p-td-sticky" style="left: 4rem; min-width: 15rem">
                {{ item.get('poBoqList')?.controls[0]?.get('productName')?.value }}
            </td>
            <td style="min-width: 20rem">
                {{ item.get('poBoqList')?.controls[0]?.get('manufacturerCode')?.value }}
            </td>
            <td style="min-width: 20rem">
                {{ item.get('poBoqList')?.controls[0]?.get('description')?.value }}
            </td>
            <td style="min-width: 15rem">
                {{ item.get('poBoqList')?.controls[0]?.get('manufacturer')?.value }}
            </td>
            <ng-container *ngIf="hasViewPriceRole; else noRoleViewPrice">
                <td class="text-center" [attr.rowspan]="item.get('poBoqList')?.length || 1">
                    {{
                        item.get('estimatedPrice')?.value
                            | currency: (item.get('unitPrice')?.value === 'USD' ? 'USD' : 'VND') : 'symbol' : (item.get('unitPrice')?.value === 'USD' ? '1.6-6' : '1.0-0')
                    }}
                </td>
            </ng-container>
            <ng-template #noRoleViewPrice>
                <td class="text-center">****</td>
            </ng-template>
            <td class="text-center" [attr.rowspan]="item.get('poBoqList')?.length">{{ item.get('requestQuantity')?.value | number }}</td>
            <td class="text-center" [attr.rowspan]="item.get('poBoqList')?.length">{{ item.get('availableQuantity')?.value | number }}</td>
            <td class="text-center" [attr.rowspan]="item.get('poBoqList')?.length">{{ item.get('spq')?.value | number }}</td>
            <td class="text-center" [attr.rowspan]="item.get('poBoqList')?.length">{{ item.get('moq')?.value | number }}</td>
            <td [attr.rowspan]="item.get('poBoqList')?.length">
                <app-editable-input
                    [control]="item.get('orderQuantity')"
                    type="input-number"
                    placeholder="Số lượng đặt hàng"
                    (save)="saveOrderQuantityData(item)"
                    fieldName="số lượng đặt hàng"
                >
                </app-editable-input>
            </td>
            <td style="min-width: 150px" [attr.rowspan]="item.get('poBoqList')?.length || 1" *ngFor="let project of getProjectQuantities(item).controls">
                <app-editable-input
                    [control]="project.get('quantity')"
                    type="input-number"
                    placeholder="Số lượng theo dự án"
                    (save)="saveQuantityData(project)"
                    fieldName="số lượng theo dự án"
                >
                </app-editable-input>
            </td>
            <ng-container *ngIf="hasViewPriceRole; else noRoleViewPriceOrder">
                <td class="text-center" [attr.rowspan]="item.get('poBoqList')?.length || 1">
                    {{ item.get('orderPrice')?.value | currency: (item.get('unitPrice')?.value === 'USD' ? 'USD' : 'VND') : 'symbol' : (item.get('unitPrice')?.value === 'USD' ? '1.6-6' : '1.0-0') }}
                </td>
            </ng-container>
            <ng-template #noRoleViewPriceOrder>
                <td class="text-center" [attr.rowspan]="item.get('poBoqList')?.length || 1">
                    ****
                </td>
            </ng-template>
            <ng-container *ngIf="item.get('projectValue')?.length > 0; else emptyDataProjectValue">
                <td class="text-center" [attr.rowspan]="item.get('poBoqList')?.length || 1" *ngFor="let project of getProjectValue(item).controls">
                    <ng-container *ngIf="hasViewPriceRole; else noRoleViewPriceProject">
                        {{ project.get('amount')?.value | currency: (item.get('unitPrice')?.value === 'USD' ? 'USD' : 'VND') : 'symbol' : (item.get('unitPrice')?.value === 'USD' ? '1.6-6' : '1.0-0') }}
                    </ng-container>
                </td>
                <ng-template #noRoleViewPriceProject>
                    ****
                </ng-template>
            </ng-container>
            <ng-template #emptyDataProjectValue>
                <td></td>
            </ng-template>
            <td class="text-center" [attr.rowspan]="item.get('poBoqList')?.length || 1">{{ item.get('excessQuantity')?.value | number }}</td>
            <td class="text-center" [attr.rowspan]="item.get('poBoqList')?.length || 1">
                {{ item.get('excessMoney')?.value | currency: (item.get('unitPrice')?.value === 'USD' ? 'USD' : 'VND') : 'symbol' : (item.get('unitPrice')?.value === 'USD' ? '1.2-2' : '1.0-0') }}
            </td>
            <td class="text-center" [attr.rowspan]="item.get('poBoqList')?.length || 1">
                {{ item.get('deliveryTime')?.value }}
            </td>
            <ng-container *ngIf="item.get('projectDeliver')?.length > 0; else emptyDataDeliver">
                <td class="text-center" [attr.rowspan]="item.get('poBoqList')?.length || 1" *ngFor="let project of getProjectDeliver(item).controls">
                    {{ project.get('quantity')?.value | number }}
                </td>
            </ng-container>
            <ng-template #emptyDataDeliver>
                <td class="text-center" [attr.rowspan]="item.get('poBoqList')?.length || 1"></td>
            </ng-template>
            <ng-container *ngIf="hasViewPriceRole; else noRoleViewPriceTotal">
                <td class="text-center" [attr.rowspan]="item.get('poBoqList')?.length || 1">
                    {{ item.get('totalValue')?.value | currency: (item.get('unitPrice')?.value === 'USD' ? 'USD' : 'VND') : 'symbol' : (item.get('unitPrice')?.value === 'USD' ? '1.2-2' : '1.0-0') }}
                </td>
            </ng-container>
            <ng-template #noRoleViewPriceTotal>
                <td class="text-center" [attr.rowspan]="item.get('poBoqList')?.length || 1">
                    ****
                </td>
            </ng-template>
            <td class="" [attr.rowspan]="item.get('poBoqList')?.length || 1">{{ item.get('note')?.value }}</td>
        </tr>

        <tr *ngFor="let item of item.get('poBoqList')?.value?.slice(1)">
            <td class="p-td-sticky" style="left: 4rem; min-width: 15rem">
                {{ item.productName }}
            </td>
            <td style="min-width: 20rem">
                {{ item.manufacturerCode }}
            </td>
            <td style="min-width: 20rem">
                {{ item.description }}
            </td>
            <td style="min-width: 15rem">
                {{ item.manufacturer }}
            </td>
        </tr>
    </ng-template>
</p-table>
