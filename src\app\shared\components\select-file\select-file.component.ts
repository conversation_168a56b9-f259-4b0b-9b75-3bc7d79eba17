import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, inject } from '@angular/core';
import { AlertService } from '../../services/alert.service';
import { FileService } from '../../services/file.service';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { CommonModule } from '@angular/common';
import {Attachment} from "../../../models/interface";

@Component({
    selector: 'app-button-select-file',
    templateUrl: './select-file.component.html',
    styleUrls: ['./select-file.component.scss'],
    standalone: true,
    imports: [ButtonModule, DialogModule, CommonModule],
    providers: [AlertService, FileService],
})
export class SelectFileComponent implements OnInit, OnChanges {
    // Service
    alertService = inject(AlertService);
    fileService = inject(FileService);
    // End service

    @Input() typesAccept: string[];
    @Output() onFileSelected = new EventEmitter<File>(); // Sự kiện truyền file ra ngoài
    @Input() attachment: Attachment;
    fileSelected: File;

    ngOnChanges(changes: SimpleChanges): void {
    }

    ngOnInit(): void {
    }

    onSelectFile(event: Event) {
        const input = event.target as HTMLInputElement;
        const files: FileList = input.files;

        if (files && files.length > 0) {
            const file = files[0];

            // If type is null or not specified, skip validation
            if (this.typesAccept && !this.isValidType(file)) {
                this.alertService.error('Lỗi', `Định dạng file không hợp lệ`);
                this.clearFiles(input);
                return;
            }

            this.fileSelected = file; // Set the selected file

            if (this.onFileSelected) {
                this.onFileSelected.emit(this.fileSelected);
            }
        }
    }

    clearFiles(fileInput: HTMLInputElement) {
        this.fileSelected = null;
        if (fileInput) {
            fileInput.value = ''; // Clear the file input value
        }
    }

    downloadAttachment() {

    }

    private isValidType(file: File): boolean {
        const mimeTypes = this.getMimeTypes(this.typesAccept);
        return mimeTypes.includes(file.type);
    }

    checkFileType(): string {
        if (this.fileSelected) {
            const fileType = this.fileSelected.type;

            switch (fileType) {
                case "application/pdf":
                    return "PDF";
                case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
                case "application/msword":
                    return "Word";
                case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
                case "application/vnd.ms-excel":
                    return "Excel";
                case "application/vnd.openxmlformats-officedocument.presentationml.presentation":
                case "application/vnd.ms-powerpoint":
                    return "PowerPoint";
                default:
                    return "Unknown";
            }
        } else {
            return "No File Selected";
        }
    }

    getFileColor(): string {
        if (this.fileSelected) {
            const fileType = this.fileSelected.type;

            switch (fileType) {
                case "application/pdf":
                    return "tw-text-red-500";
                case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
                case "application/msword":
                    return "tw-text-blue-500";
                case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
                case "application/vnd.ms-excel":
                    return "tw-text-green-500";
                case "application/vnd.openxmlformats-officedocument.presentationml.presentation":
                case "application/vnd.ms-powerpoint":
                    return "tw-text-orange-500";
                default:
                    return "tw-text-gray-500";
            }
        }
        return "tw-text-gray-500";
    }

    private getMimeTypes(types: string[] | null): string[] {
        if (!types || types.length === 0) {
            return [];
        }
        const mimeTypes: string[] = [];
        types.forEach((type) => {
            switch (type) {
                case 'image':
                    mimeTypes.push('image/jpeg', 'image/png', 'image/gif', 'image/bmp');
                    break;
                case 'pdf':
                    mimeTypes.push('application/pdf');
                    break;
                case 'excel':
                    mimeTypes.push(
                        'application/vnd.ms-excel',
                        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    );
                    break;
                case 'word':
                    mimeTypes.push(
                        'application/msword',
                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    );
                    break;
                case 'ppt':
                    mimeTypes.push('application/vnd.openxmlformats-officedocument.presentationml.presentation');
                    break;
                default:
                    break;
            }
        });
        return mimeTypes;
    }

}
