import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { BoDto, LotHistoryExport } from 'src/app/models/interface/sc';
import { GeneralEntity } from 'src/app/models/interface';
import Common from 'src/app/utils/common';

@Injectable()
export class LotHistoryExportService extends BaseService<LotHistoryExport> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/lot-history-export');
    }

    export(data: BoDto, lotId: number, lotHistoryId: number, type: number) {
        return this.http.post<GeneralEntity>('/sc/api/lot-history-export/export', data, {
            params: Common.cleanObject({
                lotId,
                type,
                lotHistoryId,
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
            }) as any,
        });
    }
}
