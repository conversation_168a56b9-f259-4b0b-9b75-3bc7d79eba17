import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ProductionInstruction } from 'src/app/models/interface/ptm';

@Injectable({ providedIn: 'root' })
export class MfgTechRecordService {
    path = '/pr/api/production-instruction';
    #http = inject(HttpClient);

    getVersionOptions(productId: number): Observable<any[]> {
        const params = new HttpParams().set('productId', productId);
        return this.#http.get<any[]>('/pr/api/product-version', { params });
    }
    /** L<PERSON>y danh sách ProductionInstruction theo versionId */
    getProductionInstructionsByVersion(versionId: number): Observable<ProductionInstruction[]> {
        const url = `${this.path}/by-version/${versionId}`;
        return this.#http.get<ProductionInstruction[]>(url);
    }
    deleteVersion(versionId: number): Observable<any> {
        return this.#http.delete<any>(`${this.path}/${versionId}`);
    }
    getDetailProductInstruction(id: number): Observable<any> {
        return this.#http.get<any>(`${this.path}/${id}`);
    }
}
