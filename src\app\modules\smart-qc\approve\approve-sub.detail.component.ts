import { CommonModule } from '@angular/common';
import {Component, ElementRef, HostListener, OnInit, ViewChild, inject, AfterViewInit} from '@angular/core';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { TreeModule, TreeNodeSelectEvent } from 'primeng/tree';
import { AuthService } from 'src/app/core/auth/auth.service';
import {
    ApproveDetailDTO,
    ImageUpload,
    ResponseApprove,
    TaskDetail,
    TreeCheckListDTO,
} from 'src/app/models/interface/smart-qc';
import { ApproveService } from 'src/app/services/smart-qc/masterdata/approve.service';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { TableCommonService } from 'src/app/shared/table-module/table.common.service';
import { ImageModule } from 'primeng/image';
import { AccordionModule } from 'primeng/accordion';
import { PanelModule } from 'primeng/panel';
import { ConfirmationService, TreeNode } from 'primeng/api';
import { DividerModule } from 'primeng/divider';
import { AlertService } from 'src/app/shared/services/alert.service';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { CheckboxModule } from 'primeng/checkbox';
import { InputTextModule } from 'primeng/inputtext';
import { isArray, isEmpty, isNull, isUndefined } from 'lodash';
import { FormsModule } from '@angular/forms';
import { FileUploadModule } from 'primeng/fileupload';
import { MinioService } from 'src/app/services/minio.service';
import { DropdownModule } from 'primeng/dropdown';
import { InputNumberModule } from 'primeng/inputnumber';
import { RadioButtonModule } from 'primeng/radiobutton';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { catchError, of } from 'rxjs';
import { TagModule } from 'primeng/tag';
import { ApproveStatus, CheckListType, listApproveStatus } from 'src/app/models/constant/smart-qc';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { ImageMagnifierComponent } from 'src/app/shared/components/magnifier/image.magnifier.component';
import { JsonParsePipe } from 'src/app/shared/pipes/jsonParse.pipe';
import {UploadComponent} from "../../../shared/components/upload/upload.component";
@Component({
    selector: 'app-approve-detail',
    standalone: true,
    imports: [
        CommonModule,
        RouterModule,
        ButtonModule,
        TreeModule,
        ImageModule,
        AccordionModule,
        PanelModule,
        DividerModule,
        CheckboxModule,
        InputTextModule,
        FormsModule,
        FileUploadModule,
        DropdownModule,
        InputNumberModule,
        RadioButtonModule,
        InputTextareaModule,
        TagModule,
        HasAnyAuthorityDirective,
        SubHeaderComponent,
        ImageMagnifierComponent,
        JsonParsePipe,
        UploadComponent
    ],
    templateUrl: './approve-sub.detail.component.html',
    styleUrls: ['./approve.component.scss'],
    providers: [ApproveService, JsonParsePipe],
})
export class ApproveSubDetailComponent implements OnInit, AfterViewInit {
    @ViewChild('treeDom') treeDom: ElementRef;
    @ViewChild('approveChecklistDetail') approveChecklistDetail!: ElementRef;

    CheckListType = CheckListType;
    taskId: number;
    contractId: number;
    optionStatus = listApproveStatus;
    ApproveStatus = ApproveStatus;
    approveService = inject(ApproveService);
    minioService = inject(MinioService);
    tableCommonService = inject(TableCommonService);
    authService = inject(AuthService);
    loadingService = inject(LoadingService);
    route = inject(ActivatedRoute);
    router = inject(Router);
    alertService = inject(AlertService);
    confirmationService = inject(ConfirmationService);
    itemsHeader = [{ label: 'Quản lý phê duyệt', url: '/sqc/approve' }, { label: 'Chi tiết' }];
    approveDetail: ApproveDetailDTO;
    selectedCheckList: TreeNode = null;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    selectedNode: any = {
        data: [],
        response: {},
    };
    note: string = '';

    isAllGood: boolean = false;
    isAnyBad: boolean = false;

    /**
     * Tạo model để lưu lại thông tin chỉnh sửa
     */

    objectTaskDetail: Record<string, TaskDetail> = {};

    /**
     * Tạo model để lưu lại thông tin chỉnh sửa
     */

    objectTaskResponse: Record<string, ResponseApprove> = {};
    collapsedInfor: boolean = false;
    heightPanelChecklist: number = 600;
    ngOnInit() {
        this.route.paramMap.subscribe((params) => {
            this.taskId = params.get('id') ? Number(params.get('id')) : null;
            this.loadingService.show();
            this.approveService.getOne(this.taskId).subscribe({
                next: (value) => {
                    if (value.note) this.note = value.note;
                    this.approveDetail = value;
                    this.contractId = this.approveDetail.contractId;

                    /**
                     * Focus vào checklist đầu tiên
                     */
                    if (isArray(this.approveDetail.treeCheckList) && this.approveDetail.treeCheckList.length > 0) {
                        this.expandRecursive(this.approveDetail.treeCheckList[0], true);
                        let node;
                        if (this.approveDetail.treeCheckList[0].levelChild === 3) {
                            node = this.approveDetail.treeCheckList[0];
                        } else if (
                            this.approveDetail.treeCheckList[0].children.length > 0 &&
                            this.approveDetail.treeCheckList[0].levelChild === 2
                        ) {
                            node = this.approveDetail.treeCheckList[0].children[0];
                        }
                        this.selectedNode = node;
                        this.selectedCheckList = node;
                        if (node && isArray(node.data)) {
                            for (const item of node.data) {
                                const taskDetail = item as TaskDetail;
                                if (!this.objectTaskDetail[taskDetail.id]) {
                                    this.objectTaskDetail[taskDetail.id] = taskDetail;
                                }
                            }
                            if (!this.objectTaskResponse[node.key]) {
                                this.objectTaskResponse[node.key] = node.response;
                            }
                        }
                    }
                    this.initIconLevel1();
                },
                error: () => {
                    this.loadingService.hide();
                    this.router.navigate(['/sqc/approve']);
                },
                complete: () => {
                    this.loadingService.hide();
                },
            });
        });
    }

    ngAfterViewInit() {
        // Có thể gọi setEqualHeights tại đây nếu cần lần đầu
        this.setEqualHeights();
    }

    setEqualHeights() {
        // Chọn tất cả các phần tử có lớp 'item-container'
        /*const items = document.querySelectorAll('.item-container-set-height');
        if (items.length > 0) {
            let maxHeight = 0;

            // Tính toán chiều cao tối đa
            items.forEach((item: HTMLElement) => {
                const height = item.clientHeight;
                if (height > maxHeight) {
                    maxHeight = height;
                }
            });

            items.forEach((item: HTMLElement) => {
                item.style.height = `${maxHeight}px`;
            });
        }*/
    }

    autoResize(event: Event): void {
        const textarea = event.target as HTMLTextAreaElement;
        textarea.style.height = 'auto';
        //const newHeight = Math.min(textarea.scrollHeight, 60);
        const newHeight = textarea.scrollHeight;
        textarea.style.height = `${newHeight}px`;
    }

    nodeSelect(event: TreeNodeSelectEvent) {
        if (
            this.objectTaskResponse[this.selectedNode.key]?.rate === 0 &&
            (!this.objectTaskResponse[this.selectedNode.key]?.subPmToEmployee ||
                this.objectTaskResponse[this.selectedNode.key]?.subPmToEmployee.trim().length === 0)
        ) {
            this.alertService.warning('Cảnh báo', 'Bạn cần nhập đủ thông tin phản hồi SubPm -> đội thi công');
            return;
        }

        this.approveChecklistDetail.nativeElement.scrollTo({
            top: 0,
            behavior: 'smooth',
        });

        const node: TreeCheckListDTO = event.node as unknown as TreeCheckListDTO;
        if (node.level === 3 || !node?.levelChild || node?.levelChild === 2) return;
        const shouldStop = this.selectedNode.data.some((taskDetail: TaskDetail) => {
            if (
                !taskDetail.ignored &&
                taskDetail.type !== CheckListType.IMAGE &&
                !taskDetail.value &&
                (!isNull(this.objectTaskResponse[this.selectedNode.key]?.rate) ||
                    !isUndefined(this.objectTaskResponse[this.selectedNode.key]?.rate))
            ) {
                this.alertService.warning('Cảnh báo', 'Bạn cần nhập đầy đủ giá trị của chi tiết danh mục');
                return true; // Trả về true để dừng vòng lặp
            }
            return false;
        });
        if (shouldStop) return;

        this.selectedNode = node;

        if (node && isArray(node.data)) {
            for (const item of node.data) {
                const taskDetail = item as TaskDetail;
                if (!this.objectTaskDetail[taskDetail.id]) {
                    this.objectTaskDetail[taskDetail.id] = taskDetail;
                }
            }
            if (!this.objectTaskResponse[node.key]) {
                this.objectTaskResponse[node.key] = node.response;
            }
        }

        setTimeout(() => {
            this.setEqualHeights();
        }, 100);
    }

    private expandRecursive(node: TreeNode, isExpand: boolean) {
        node.expanded = isExpand;
        if (node.children) {
            node.children.forEach((childNode) => {
                this.expandRecursive(childNode, isExpand);
            });
        }
    }

    onUpload(files: File[], taskDetail: TaskDetail) {
        this.loadingService.show();
        this.minioService.uploadMany(files, this.contractId, this.taskId).subscribe({
            next: (objs: ImageUpload[]) => {
                this.loadingService.hide();
                if (taskDetail.id) {
                    for (const obj of objs) {
                        this.objectTaskDetail[taskDetail.id].imageTaskList.push({
                            taskDetailId: taskDetail.id,
                            checkListId: taskDetail.checkListDetailId,
                            taskId: taskDetail.taskId,
                            url: obj.url,
                            uploadBy: 0,
                        });
                    }
                }
                this.loadingService.hide();
            },
            error: () => {
                this.loadingService.hide();
            },
            complete: () => {
                this.loadingService.hide();
            },
        });
    }

    onRemove(e: { value: string; key: TaskDetail }) {
        const images = this.objectTaskDetail[`${e.key.id}`].imageTaskList;
        for (let index = 0; index < images.length; index++) {
            if (images[index].url === e.value) {
                this.objectTaskDetail[`${e.key.id}`].imageTaskList.splice(index, 1);
            }
        }
    }

    rate(response: ResponseApprove) {
        let canBreakLevel2 = false;

        for (let index = 0; index < this.approveDetail.treeCheckList.length; index++) {
            if (canBreakLevel2) break;

            if (
                this.approveDetail.treeCheckList[index].response.checkListHeaderId === response.checkListHeaderId &&
                this.approveDetail.treeCheckList[index].response.checkListSubId === response.checkListSubId
            ) {
                this.approveDetail.treeCheckList[index].icon =
                    response.rate === 0 ? 'pi pi-times text-red-500' : 'pi pi-check text-teal-500';
                this.approveDetail.treeCheckList[index].response.rate = response.rate;

                break;
            }
            if (this.approveDetail.treeCheckList[index].levelChild !== 3) {
                for (let jndex = 0; jndex < this.approveDetail.treeCheckList[index].children.length; jndex++) {
                    if (
                        this.approveDetail.treeCheckList[index].children[jndex].response.checkListHeaderId ===
                            response.checkListHeaderId &&
                        this.approveDetail.treeCheckList[index].children[jndex].response.checkListSubId ===
                            response.checkListSubId
                    ) {
                        this.approveDetail.treeCheckList[index].children[jndex].icon =
                            response.rate === 0 ? 'pi pi-times text-red-500' : 'pi pi-check text-teal-500';

                        this.approveDetail.treeCheckList[index].children[jndex].response.rate = response.rate;
                        canBreakLevel2 = true;
                        break;
                    }
                }
            }
        }

        this.initIconLevel1();
    }

    initIconLevel1() {
        if (!this.approveDetail.treeCheckList || this.approveDetail.treeCheckList.length === 0) return;
        let countGood = 0;
        let countBad = 0;
        for (let index = 0; index < this.approveDetail.treeCheckList.length; index++) {
            let allGood = 1;
            if (this.approveDetail.treeCheckList[index].levelChild === 3) {
                if (this.approveDetail.treeCheckList[index].response.rate === 1) {
                    this.approveDetail.treeCheckList[index].icon = 'pi pi-check text-teal-500';
                    countGood++;
                } else if (this.approveDetail.treeCheckList[index].response.rate === 0) {
                    this.approveDetail.treeCheckList[index].icon = 'pi pi-times text-red-500';
                    countBad++;
                }
                continue;
            }

            for (let jndex = 0; jndex < this.approveDetail.treeCheckList[index].children.length; jndex++) {
                if (this.approveDetail.treeCheckList[index].children[jndex].response === null) {
                    this.approveDetail.treeCheckList[index].children[jndex].response.taskId = this.taskId;
                }

                if (this.approveDetail.treeCheckList[index].children[jndex].response.rate === 0) {
                    allGood = 0;
                    break;
                }
                if (this.approveDetail.treeCheckList[index].children[jndex].response.rate === 1) {
                    allGood = 1;
                }
                if (!this.approveDetail.treeCheckList[index].children[jndex].response.rate) {
                    allGood = -1;
                    break;
                }
            }
            if (allGood === 1) {
                this.approveDetail.treeCheckList[index].icon = 'pi pi-check text-teal-500';
                countGood++;
            }
            if (allGood === 0) {
                this.approveDetail.treeCheckList[index].icon = 'pi pi-times text-red-500';
                countBad++;
            }
        }

        if (countGood === this.approveDetail.treeCheckList.length) {
            this.isAllGood = true;
        }
        if (countBad > 0) {
            this.isAllGood = false;
            this.isAnyBad = true;
        } else {
            this.isAnyBad = false;
        }
    }

    getSeverity(state: number): string {
        switch (state) {
            case 4:
                return 'success';
            case 3:
                return 'danger';
            case 2:
                return 'primary';
            case 1:
                return 'danger';
            case 0:
                return 'warning';
            default:
                return 'info';
        }
    }

    getStateText(state: number): string {
        switch (state) {
            case 4:
                return 'Hoàn thành';
            case 3:
                return 'PM Từ chối';
            case 2:
                return 'Chờ PM duyệt';
            case 1:
                return 'Từ chối';
            case 0:
                return 'Chờ duyệt';
            default:
                return '';
        }
    }

    approve(type: string) {
        if (
            this.objectTaskResponse[this.selectedNode.key]?.rate === 0 &&
            (!this.objectTaskResponse[this.selectedNode.key]?.subPmToEmployee ||
                this.objectTaskResponse[this.selectedNode.key]?.subPmToEmployee.trim().length === 0)
        ) {
            this.alertService.warning('Cảnh báo', 'Bạn cần nhập đủ thông tin phản hồi SubPm -> đội thi công');
            return;
        }
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message:
                type === 'accept'
                    ? 'Bạn có chắc chắn chấp nhận kết quả của trạm này không'
                    : 'Bạn có chắc chắn muốn từ chối kết quả này không',
            icon: 'pi pi-exclamation-triangle',
            acceptIcon: 'none',
            rejectIcon: 'none',
            rejectButtonStyleClass: 'p-button-text',
            acceptButtonStyleClass: 'p-button-outlined',
            acceptLabel: 'Đồng ý',
            rejectLabel: 'Hủy',
            accept: () => {
                // Lấy danh sách giá trị từ objectTaskDetail
                const taskDetails: TaskDetail[] = Object.values(this.objectTaskDetail);

                // Lấy danh sách giá trị từ objectTaskResponse
                const responseApproves: ResponseApprove[] = Object.values(this.objectTaskResponse);

                this.loadingService.show();
                this.approveService
                    .approveBySubPm(this.taskId, {
                        taskDetails,
                        responseApproves,
                        isAccept: type === 'accept',
                        note: isEmpty(this.note) ? null : this.note,
                    })
                    .pipe(
                        catchError(() => {
                            this.loadingService.hide();
                            return of(null);
                        }),
                    )
                    .subscribe({
                        next: (res) => {
                            this.alertService.success(
                                'Thành công',
                                type === 'accept' ? 'Phê duyệt công việc thành công' : 'Từ chối công việc thành công',
                            );
                            this.approveDetail.state = res['data']['task']['state'];
                            this.loadingService.hide();
                        },
                        error: () => {
                            this.loadingService.hide();
                        },
                    });
            },
        });
    }

    setValueTaskDetail() {}

    isSticky = false;

    @HostListener('window:scroll', ['$event'])
    checkScroll() {
        const divOffset = this.treeDom.nativeElement.getBoundingClientRect().top;

        if (divOffset <= 50) {
            this.isSticky = true;
        } else {
            this.isSticky = false;
        }
    }

    collapsedChange(collaped: boolean) {
        this.collapsedInfor = collaped;
        this.caculateHeightPanel();
    }

    caculateHeightPanel() {
        if (!this.collapsedInfor) {
            this.heightPanelChecklist = 600;
            return;
        }

        const documentHeight = document.documentElement.clientHeight;
        const remToPx = 16; // 1 rem = 16px
        const remToSubtract = 10;

        const newHeight = documentHeight - remToSubtract * remToPx;
        if (newHeight < 600) {
            this.heightPanelChecklist = 600;
        } else {
            this.heightPanelChecklist = newHeight;
        }
    }
}
