<div [formGroup]="form" class="editor-wrapper" #autocomplete>
    <div class="editor-tag" (click)="focusTagInput()" *ngIf="!isFull">
        <div class="tw-flex tw-gap-2 tw-flex-wrap">
            <span class="tag-item tw-h-full" *ngFor="let tag of tags">
                {{ getDisplayValue(tag) }}
                <span class="pi pi-times-circle tw-mx-2 tw-cursor-pointer" (click)="removeTag(tag)"></span>
            </span>
        </div>
        <input
            [placeholder]="tags && tags.length > 0 ? '' : placeholder"
            #tagInput
            type="text"
            (compositionstart)="onCompositionStart()"
            (compositionend)="onCompositionEnd()"
            (keydown)="onKeyDown($event)"
            (keyup)="onKeyUp($event)"
            (focus)="focusTagInput()"
            (blur)="blurTagInput()"
            formControlName="tag"
        />
    </div>
    <div *ngIf="isFull" class="tw-flex tw-flex-row tw-justify-between tw-p-3 tw-rounded-md border-1 tw-border-gray-300">
        <span>
            {{ tags.length > 0 ? getDisplayValue(tags[0]) : '' }}
        </span>
        <span class="pi pi-times tw-mx-2 tw-cursor-pointer" (click)="removeTag(tags[0])"></span>
    </div>

    <div
        class="tag-dropdown"
        *ngIf="showDropdown && !isFull"
        [style.top]="dropdownPosition.top"
        [style.left]="dropdownPosition.left"
        [style.width]="dropdownPosition.width"
    >
        <div class="dropdown-content">
            <div *ngIf="filteredOptions.length === 0" class="no-suggestions">
                <ng-container *ngIf="allowCustomTags">Enter để thêm</ng-container>
                <ng-container *ngIf="!allowCustomTags">No option</ng-container>
            </div>
            <div
                *ngFor="let option of filteredOptions; let i = index"
                class="dropdown-item tw-relative"
                [class.active]="i === activeIndex"
                (mouseenter)="activeIndex = i"
                (click)="selectOption(option)"
            >
                <div class="tw-w-full tw-h-full">
                    {{ getDisplayValue(option) }}
                </div>
                <span
                    class="pi pi-trash tw-absolute tw-top-0 tw-mx-2 tw-cursor-pointer tw-rounded-full tw-p-2 hover:tw-bg-red-400 hover:tw-text-white tw-right-0"
                    (click)="handleDelete($event, index, option)"
                    *ngIf="rowDelete(option)"
                ></span>
            </div>
        </div>
    </div>
</div>
