import { Component, Input, OnInit } from '@angular/core';
import { CommonModule, formatDate } from '@angular/common';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { CalendarModule } from 'primeng/calendar';
import { MultiSelectModule } from 'primeng/multiselect';
import { DropdownModule } from 'primeng/dropdown';
import { TagModule } from 'primeng/tag';

interface AccountOption {
    id: string;
    label: string;
}

/**
 * Khối "Thông tin chung" cho SOPBASE
 */
@Component({
    selector: 'app-general-info',
    standalone: true,
    imports: [CommonModule, ReactiveFormsModule, InputTextModule, CalendarModule, MultiSelectModule, DropdownModule, TagModule],
    template: `
        <div [formGroup]="form" class="p-fluid p-formgrid p-grid">
            <!-- Hàng 1 -->
            <div class="p-field p-col-6">
                <label for="sopName">Tên SOPBASE</label>
                <input id="sopName" type="text" pInputText [value]="sopBaseName" readonly />
            </div>

            <div class="p-field p-col-3">
                <label>Trạng thái soát xét</label>
                <p-tag value="Draft" severity="secondary"></p-tag>
            </div>

            <div class="p-field p-col-3">
                <label>Trạng thái phê duyệt</label>
                <p-tag value="Draft" severity="secondary"></p-tag>
            </div>

            <!-- Hàng 2 -->
            <div class="p-field p-col-4">
                <label for="applyDate">Ngày áp dụng</label>
                <input id="applyDate" type="text" pInputText [value]="applyDate" readonly />
            </div>

            <div class="p-field p-col-4">
                <label for="reviewDate">Ngày soát xét</label>
                <p-calendar inputId="reviewDate" formControlName="reviewDate" dateFormat="dd/mm/yy" showTime="true" hourFormat="24"></p-calendar>
            </div>

            <div class="p-field p-col-4">
                <label for="approveDate">Ngày phê duyệt</label>
                <p-calendar inputId="approveDate" formControlName="approveDate" dateFormat="dd/mm/yy" showTime="true" hourFormat="24"></p-calendar>
            </div>

            <!-- Hàng 3 -->
            <div class="p-field p-col-6">
                <label for="reviewers">Người soát xét</label>
                <p-multiSelect
                    inputId="reviewers"
                    placeholder="Chọn account"
                    formControlName="reviewers"
                    [options]="reviewerOptions"
                    optionLabel="label"
                    optionValue="id"
                    display="comma"
                ></p-multiSelect>
            </div>

            <div class="p-field p-col-6">
                <label for="approver">Người phê duyệt</label>
                <p-dropdown
                    inputId="approver"
                    placeholder="Chọn account"
                    formControlName="approver"
                    [options]="reviewerOptions"
                    optionLabel="label"
                    optionValue="id"
                ></p-dropdown>
            </div>
        </div>
    `,
})
export class GeneralInfoComponent implements OnInit {
    /** FormGroup do parent cung cấp */
    @Input() form!: FormGroup;

    /** Tên thương mại (Tenthuongmai) & mã VNPT MAN PN */
    @Input() productTradeName!: string;
    @Input() vnptManPn!: string;

    /** Danh sách account được phân quyền soát xét/phê duyệt */
    @Input() reviewerOptions: AccountOption[] = [];

    applyDate = '';
    sopBaseName = '';

    ngOnInit(): void {
        const now = new Date();
        this.applyDate = formatDate(now, 'HH:mm dd/MM/yyyy', 'vi-VN');
        this.sopBaseName = `SOPBASE-${this.productTradeName}-${this.vnptManPn}`;
    }
}
