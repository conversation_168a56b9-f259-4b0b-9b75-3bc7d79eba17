import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { PoTransferItem } from '../../../models/interface/sc';

@Injectable()
export class PoTransferItemService extends BaseService<PoTransferItem> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/po-transfer-item');
    }

    getAll(params: string) {
        return this.http.get<PoTransferItem[]>(`${this.baseUrl}/get-all?` + params, {
            observe: 'response',
        });
    }
}
