import {AfterViewInit, Component, inject, OnInit} from "@angular/core";
import {TableCommonModule} from "../../../../shared/table-module/table.common.module";
import {CommonModule} from "@angular/common";
import {ActivatedRoute, Router, RouterModule} from "@angular/router";
import {ButtonModule} from "primeng/button";
import {TagModule} from "primeng/tag";
import {TooltipModule} from "primeng/tooltip";
import {ChipModule} from "primeng/chip";
import {SubHeaderComponent} from "../../../../shared/components/sub-header/sub-header.component";
import {DialogModule} from "primeng/dialog";
import {FormCustomModule} from "../../../../shared/form-module/form.custom.module";
import {InputTextModule} from "primeng/inputtext";
import {InputTextareaModule} from "primeng/inputtextarea";
import {PaginatorModule} from "primeng/paginator";
import {FormB<PERSON>er, FormGroup, ReactiveFormsModule, Validators} from "@angular/forms";
import {ApproveService} from "../../../../services/smart-qc/masterdata/approve.service";
import {TableCommonService} from "../../../../shared/table-module/table.common.service";
import {PoService} from "../../../../services/sc/po/po.service";
import {DateUtils} from "../../../../utils/date-utils";
import {PanelModule} from "primeng/panel";
import {TableModule} from "primeng/table";
import {StateOneComponent} from "../state/state-one/state-one.component";
import {StateTwoComponent} from "../state/state-two/state-two.component";
import {WizardDoubleRowComponent} from "../../../../shared/components/wizard-double-row/wizard-double-row.component";
import {StateWizard} from "../../../../models/interface";
import {LoadingService} from "../../../../shared/services/loading.service";
import {ConfirmationService} from "primeng/api";
import {AlertService} from "../../../../shared/services/alert.service";
import {AuthService} from "../../../../core/auth/auth.service";
import {RfqService} from "../../../../services/sc/sc-bom/rfq.service";
import {Rfq} from "../../../../models/interface/sc";
import {RFQ_STATE} from "../../../../models/constant/sc";

@Component({
    selector: 'app-sc-rfq',
    standalone: true,
    imports: [
        TableCommonModule,
        CommonModule,
        RouterModule,
        ButtonModule,
        TagModule,
        TooltipModule,
        ChipModule,
        SubHeaderComponent,
        DialogModule,
        FormCustomModule,
        InputTextModule,
        InputTextareaModule,
        PaginatorModule,
        ReactiveFormsModule,
        PanelModule,
        TableModule,
        StateOneComponent,
        StateTwoComponent,
        WizardDoubleRowComponent
    ],
    templateUrl: './detail.component.html',
    providers: [ApproveService, TableCommonService, PoService, RfqService],
})
export class DetailComponent implements OnInit, AfterViewInit {
    // Declare
    rfqForm: FormGroup;

    isLoading: boolean = false;
    rfqId: number;

    // End Declare

    // State
    versionStates: StateWizard[] = [
        { id: 0, name: 'Giai đoạn 1', description: 'Khởi tạo' },
        { id: 1, name: 'Giai đoạn 2', description: 'Hỏi giá' }
    ];

    stateIdView;
    rfqOld: Rfq;
    // End state

    // Inject service
    loadingService = inject(LoadingService);
    confirmationService = inject(ConfirmationService);
    alertService = inject(AlertService);
    authService = inject(AuthService);
    rfqService = inject(RfqService);
    // End inject service

    constructor(private formBuilder: FormBuilder, private route: ActivatedRoute,
                private activatedRoute: ActivatedRoute, private fb: FormBuilder,
                private router: Router,) {
    }

    ngOnInit() {
        this.route.paramMap.subscribe(() => {
            this.loadData();
        });
    }

    loadData() {
        const idString = this.activatedRoute.snapshot.paramMap.get('id');
        if (idString) {
            this.rfqId = Number(idString);
        }

        if (this.rfqId) {
            this.loadingService.show();
            this.rfqService.getOne(this.rfqId).subscribe({
                next: (res) => {
                    this.loadingService.hide();
                    this.rfqOld = res.body;
                    this.stateIdView = this.rfqOld.state;
                    this.isLoading = true;
                    this.initForm(res.body);
                },
                error: () => {
                    this.loadingService.hide();
                    this.alertService.error('Lỗi', 'Không thể truy cập thông tin ');
                    this.router.navigate(['/sc/rfq']);
                    this.isLoading = true;
                },
            });
        }
    }

    ngAfterViewInit() {

    }

    initForm(oldPriceData: Rfq): void {
        this.rfqForm = this.formBuilder.group({
            code: [{ value: oldPriceData?.code, disabled: true }],
            name: [oldPriceData?.name, [Validators.maxLength(255)]],
            note: [oldPriceData?.note, []]
        });
    }

    handleSelectState(stateId: number) {
        /*this.stateIdSelected = stateId;
        console.log('Selected State:', stateId);*/
        if (this.rfqOld.items == null || this.rfqOld.items.length === 0) {
            this.alertService.error("Chưa có thông tin dự án");
            return;
        }
        if (this.rfqOld.inventories == null || this.rfqOld.inventories.length === 0) {
            this.alertService.error("Chưa có thông tin báo cáo tồn active");
            return;
        }
        if (stateId !== this.stateIdView) {
            this.confirmationService.confirm({
                key: 'app-confirm',
                header: `Xác nhận chuyển trạng thái`,
                message: stateId === RFQ_STATE.STATE_ONE? 'Xác nhận quay lại trạng thái giai đoạn 1?' : 'Xác nhận chuyển trạng thái tới giai đoạn 2?',
                icon: 'pi pi-exclamation-triangle',
                accept: () => {
                    this.loadingService.show();
                    if (stateId === RFQ_STATE.STATE_ONE) {
                        this.rfqService.update({...this.rfqOld, state: RFQ_STATE.STATE_ONE}).subscribe({
                            next: (res) => {
                                this.loadData()
                                this.alertService.success('Chuyển trạng thái thành công');
                                this.loadingService.hide();
                                this.stateIdView = stateId;
                            },
                            error: (res) => {
                                this.alertService.handleError(res);
                                this.loadingService.hide();
                            },
                        });
                    } else {
                        this.rfqService.requestForQuotation(this.rfqOld.id).subscribe({
                            next: (res) => {
                                this.loadData()
                                this.alertService.success('Chuyển trạng thái thành công');
                                this.loadingService.hide();
                                this.stateIdView = stateId;
                            },
                            error: (res) => {
                                this.alertService.handleError(res);
                                this.loadingService.hide();
                            },
                        });
                    }
                },
            });
        } else {
            this.stateIdView = stateId;
        }
    }

    protected readonly DateUtils = DateUtils;
}
