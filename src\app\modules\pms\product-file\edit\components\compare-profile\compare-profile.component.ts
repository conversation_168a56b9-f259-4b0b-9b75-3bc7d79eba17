// src/app/shared/components/history-dialog/history-dialog.component.ts
import { Component, inject, Input, Type, OnDestroy, ViewChild } from '@angular/core';
import { DialogModule } from 'primeng/dialog';
import { StepsModule } from 'primeng/steps';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { CommonModule } from '@angular/common';
import { forkJoin, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { InputSwitchModule } from 'primeng/inputswitch';
import { FormsModule } from '@angular/forms';
import { CheckboxModule } from 'primeng/checkbox';

import { ProductFileService } from 'src/app/services/pms/product-file/product-file.service';
import { TabViewComponent } from 'src/app/shared/components/tab-view/tab-view.component';
import { DesignProfileComponent } from 'src/app/modules/pms/product-file/edit/components/compare-profile/design-profile.component';
import { ProductionProfileComponent } from 'src/app/modules/pms/product-file/edit/components/compare-profile/production-profile.component';
import { QuantityProfileComponent } from 'src/app/modules/pms/product-file/edit/components/compare-profile/quantity-profile.component';
import { TabItem } from 'src/app/models/interface/pms';
interface StepItem {
    label: string;
}
interface Column {
    field: string;
    header: string;
}
interface SectionRow {
    section?: string;
    name: string;
    note1?: string;
    note2?: string;
}

@Component({
    selector: 'app-compare-profile',
    standalone: true,
    imports: [DialogModule, StepsModule, TableModule, ButtonModule, CommonModule, TabViewComponent, InputSwitchModule, CheckboxModule, FormsModule],
    templateUrl: './compare-profile.component.html',
    styleUrls: ['./compare-profile.component.scss'],
    providers: [ProductFileService],
})
export class CompareProfileComponent implements OnDestroy {
    @Input() selectedVersions: any[] = [];
    @Input() versions: any[] = [];
    @ViewChild(TabViewComponent) tabViewComp?: TabViewComponent;
    private destroy$ = new Subject<void>();
    visible = false;
    productFileService = inject(ProductFileService);
    allTabData: {
        [versionId: number]: {
            [tabType: number]: any;
        };
    } = {};
    isInitialized = false;
    hideNote1 = false;
    hideNote2 = false;
    onlyShowDifferences = false;
    currentActiveTabType = 1;
    // ví dụ 2 bước Version
    steps: StepItem[] = [{ label: 'Version 1.0 MP' }, { label: 'Version 1.1 MP' }];
    activeStep = 0;
    get version1(): string {
        return this.versions[0] ? `Version ${this.versions[0].version}` : '';
    }

    get version2(): string {
        return this.versions[1] ? `Version ${this.versions[1].version}` : '';
    }
    itemsTab: TabItem[] = [
        { header: 'Hồ sơ thiết kế', type: 1, component: DesignProfileComponent },
        { header: 'Hồ sơ sản xuất', type: 2, component: ProductionProfileComponent },
        { header: 'Hồ sơ chất lượng', type: 4, component: QuantityProfileComponent },
    ];
    columns: Column[] = [
        { field: 'name', header: 'Thông tin' },
        { field: 'note1', header: 'Ghi chú' },
        { field: 'note2', header: 'Ghi chú' },
        { field: 'action', header: 'Hành động' },
    ];

    rows: SectionRow[] = [
        { section: 'Hồ sơ thiết kế', name: 'Thông tin sản phẩm', note1: '', note2: '' },
        { section: '', name: 'Thông số kỹ thuật', note1: '', note2: '' },
        { section: '', name: 'DFMEA Sản phẩm', note1: '', note2: '' },
        { section: 'Thiết kế HW', name: 'Thiết kế Schematic', note1: '', note2: '' },
        { section: '', name: 'Thiết kế Layout PCB', note1: '', note2: '' },
        { section: '', name: 'DFM checklist', note1: '', note2: '' },
        // … thêm các dòng khác …
    ];
    // ngOnInit() {
    //     this.loadAllTabsData();
    // }
    initializeData() {
        if (!this.isInitialized || this.versions?.length) {
            setTimeout(() => {
                this.tabViewComp!.activeIndex = 0;
                this.tabViewComp!.onTabChange({ index: 0 }); // cập nhật currentActiveTabType và data tab
            });
            this.loadAllTabsData();
            this.isInitialized = true;
        }
    }
    ngOnDestroy() {
        this.destroy$.next();
        this.destroy$.complete();
    }
    onTabChange(type: number) {
        this.currentActiveTabType = type;
        this.updateActiveTabData();
    }

    private loadAllTabsData() {
        const version1Id = this.versions[0]?.id;
        const version2Id = this.versions[1]?.id;
        // Gọi API một lần cho mỗi version với types=7
        forkJoin([
            this.productFileService.getProductDoc({
                versionId: version1Id,
                types: 7, // Lấy tất cả dữ liệu 3 tab
            }),
            this.productFileService.getProductDoc({
                versionId: version2Id,
                types: 7,
            }),
        ])
            .pipe(takeUntil(this.destroy$))
            .subscribe({
                next: ([res1, res2]) => {
                    // Lưu dữ liệu theo cấu trúc: allTabData[versionId][tabType]
                    this.allTabData[version1Id] = {
                        1: res1['1'],
                        2: res1['2'],
                        4: res1['4'],
                    };
                    this.allTabData[version2Id] = {
                        1: res2['1'],
                        2: res2['2'],
                        4: res2['4'],
                    };

                    // Cập nhật tab đầu tiên
                    this.updateActiveTabData();
                },
                error: (err) => {
                    console.error('Error loading all tabs data:', err);
                },
            });
    }

    onToggleDifferences() {
        this.updateActiveTabData();
    }
    onToggleNoteHidden() {
        this.updateActiveTabData();
    }

    private updateActiveTabData() {
        const version1Id = this.versions[0]?.id;
        const version2Id = this.versions[1]?.id;
        const tabType = this.currentActiveTabType;

        if (!version1Id || !version2Id || !this.allTabData[version1Id] || !this.allTabData[version2Id]) {
            return;
        }

        const tabIndex = this.itemsTab.findIndex((tab) => tab.type === tabType);
        if (tabIndex !== -1) {
            this.itemsTab[tabIndex].inputs = {
                docRes1: this.allTabData[version1Id][tabType],
                docRes2: this.allTabData[version2Id][tabType],
                onlyShowDifferences: this.onlyShowDifferences,
                hideNote1: this.hideNote1,
                hideNote2: this.hideNote2,
            };
        }
    }
    resetState() {
        this.onlyShowDifferences = false;
        this.hideNote1 = false;
        this.hideNote2 = false;
        this.updateActiveTabData(); // cập nhật lại UI
    }
    // hideNotesTarget: 'note1' | 'note2' | '' = '';

    toggleNoteVisibility(target: string) {
        this.hideNote1 = target === 'note1';
        this.hideNote2 = target === 'note2';
        this.updateActiveTabData();
    }

    getLifecycleStage(lifecycleStage: number | string): string {
        switch (lifecycleStage) {
            case 1:
                return 'Prototype';
            case 2:
                return 'Pilot';
            case 4:
                return 'MP';
            default:
                return '';
        }
    }
    /** Mở dialog khi cần */
    show() {
        this.visible = true;
    }

    /** Ví dụ hành động Mở hồ sơ */
    openDocument(row: SectionRow) {
        // TODO: logic mở file hoặc chuyển trang
    }
}
