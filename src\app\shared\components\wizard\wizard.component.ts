import { Component, Input, Output, OnChanges, SimpleChanges, EventEmitter } from '@angular/core';
import {StateWizard} from "../../../models/interface";
import {NgClass, NgForOf} from "@angular/common";

@Component({
    selector: 'app-wizard',
    templateUrl: './wizard.component.html',
    styleUrls: ['./wizard.component.scss'],
    imports: [
        NgClass,
        NgForOf
    ],
    standalone: true
})
export class WizardComponent implements OnChanges {
    @Input() currentState: any;
    @Input() canChangeState: boolean = false;
    @Input() states: StateWizard[];
    @Output() stateChanged = new EventEmitter<number>();

    ngOnInit() {
        // console.log('ngOnInit:', this.states); // Có thể undefined do chưa nhận dữ liệu
    }

    ngOnChanges(changes: SimpleChanges) {
        if (changes['states']) {
            // console.log('ngOnChanges:', changes['states'].currentValue); // Nhận dữ liệu sau khi cập nhật
        }
    }

    changeState(stateId: number) {
        /*console.log(this.currentState)
        console.log('Change state click, stateId:', stateId);*/
        if (this.canChangeState) {
            this.stateChanged.emit(stateId);
        }
    }
}
