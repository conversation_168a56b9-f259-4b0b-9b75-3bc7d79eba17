import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { environment } from '../../../../../../../environments/environment';

interface RowData {
    section: string;
    name: string;
    note1: string;
    note2: string;
    note3?: string;
    note4?: string;
    path1?: string;
    path3?: string;
}

@Component({
    selector: 'app-design-profile',
    standalone: true,
    imports: [CommonModule, TableModule, ButtonModule],
    templateUrl: './design-profile.component.html',
    styles: [
        `
            .section-table ::ng-deep table {
                table-layout: fixed;
            }
        `,
        `
            .section-table ::ng-deep .p-table-thead > tr > th {
                background: #e0e0e0;
                font-weight: bold;
                text-align: left;
            }
        `,
        `
            .mb-4 {
                margin-bottom: 1rem;
            }
        `,
        `
            .footer {
                text-align: right;
                margin-top: 1rem;
            }
        `,
        `
            .ml-auto {
                margin-left: auto;
            }
        `,
    ],
})
export class DesignProfileComponent implements OnChanges {
    @Input() docRes1: any;
    @Input() docRes2: any;
    @Input() hideNote1 = false;
    @Input() hideNote2 = false;
    @Input() onlyShowDifferences = false;

    groupedRows: { name: string; items: RowData[] }[] = [];
    public STORAGE_BASE_URL = environment.STORAGE_BASE_URL;
    ngOnChanges(changes: SimpleChanges) {
        if (changes['docRes1'] || changes['docRes2'] || changes['onlyShowDifferences']) {
            this.updateGroupedRows();
        }
    }
    private updateGroupedRows() {
        // Kiểm tra dữ liệu đầu vào
        if (!this.docRes1 || !this.docRes2) {
            console.warn('Missing input data');
            return;
        }

        const sectionMap: { [key: number]: string } = {
            0: 'Thông tin sản phẩm',
            1: 'RD BOM',
            2: 'Thiết kế HW',
            3: 'Thiết kế ID/ MD',
            4: 'Thiết kế Accessories và Packaging',
            5: 'Bootloader/Firmware sản phẩm',
        };

        const data1 = this.docRes1 ?? {};
        const data2 = this.docRes2 ?? {};

        const allSectionKeys = new Set([...Object.keys(data1), ...Object.keys(data2)]);
        const map = new Map<string, RowData[]>();

        for (const sectionKey of allSectionKeys) {
            const sectionNum = Number(sectionKey);
            const sectionName = sectionMap[sectionNum] || 'Khác';

            const list1 = Array.isArray(data1[sectionKey]) ? data1[sectionKey] : [];
            const list2 = Array.isArray(data2[sectionKey]) ? data2[sectionKey] : [];

            // Tạo một Set để lưu trữ các description đã xử lý
            const processedDescriptions = new Set<string>();
            const rows: RowData[] = [];

            // Xử lý tất cả các mục từ cả hai phiên bản
            const allItems = [...list1, ...list2];

            for (const item of allItems) {
                const description = item.description || '';

                // Nếu description chưa được xử lý
                if (description && !processedDescriptions.has(description)) {
                    processedDescriptions.add(description);

                    // Tìm item tương ứng trong cả hai phiên bản
                    const item1 = list1.find((i) => i.description === description) || {};
                    const item2 = list2.find((i) => i.description === description) || {};

                    const fileName1 = item1.fileName || '';
                    const fileName2 = item2.fileName || '';
                    const md5_1 = item1.md5 || '';
                    const md5_2 = item2.md5 || '';

                    const isDifferent = fileName1 !== fileName2 || (fileName1 === fileName2 && md5_1 && md5_2 && md5_1 !== md5_2);

                    if (!this.onlyShowDifferences || isDifferent) {
                        const showMd5Diff = fileName1 === fileName2 && md5_1 !== md5_2;
                        rows.push({
                            section: sectionName,
                            name: description,
                            note1: fileName1 + (showMd5Diff && md5_1 ? ` (MD5: ${md5_1})` : ''),
                            note2: item1.note || '',
                            note3: fileName2 + (showMd5Diff && md5_2 ? ` (MD5: ${md5_2})` : ''),
                            note4: item2.note || '',
                            path1: item1.filePath || '',
                            path3: item2.filePath || '',
                        });
                    }
                }
            }

            if (rows.length > 0) {
                map.set(sectionName, rows);
            }
        }

        this.groupedRows = Array.from(map, ([name, items]) => ({ name, items }));
    }
    downloadFile(url: string, namePath: string) {
        const a = document.createElement('a');
        a.href = this.STORAGE_BASE_URL + '/' + url;
        a.download = `${namePath}`;
        a.click();
        a.remove();
    }
}
