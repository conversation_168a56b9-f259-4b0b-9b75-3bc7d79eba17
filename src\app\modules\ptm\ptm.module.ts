import { NgModule } from '@angular/core';
import { FileService } from 'src/app/shared/services/file.service';

import { RouterModule } from '@angular/router';
import { BaseUserService } from 'src/app/services/administration/admin/user.service';
import { TableCommonService } from 'src/app/shared/table-module/table.common.service';
import { PTMRoutingModule } from './ptm-routing.module';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { CheckboxModule } from 'primeng/checkbox';
import { DropdownModule } from 'primeng/dropdown';
import { FormCustomModule } from '../../shared/form-module/form.custom.module';
import { InfoShareComponent } from './manufacturing-technology-records/components/info-share/info-share.component';
import { InputTextModule } from 'primeng/inputtext';
import { NgForOf } from '@angular/common';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from 'primeng/api';
import { TableModule } from 'primeng/table';

@NgModule({
    imports: [
        PTMRoutingModule,
        ButtonModule,
        CalendarModule,
        CheckboxModule,
        DropdownModule,
        FormCustomModule,
        InfoShareComponent,
        InputTextModule,
        NgForOf,
        OverlayPanelModule,
        ReactiveFormsModule,
        SharedModule,
        TableModule,
    ],
    providers: [FileService, BaseUserService, TableCommonService],
    exports: [RouterModule],
    declarations: [],
})
export class PTMModule {}
