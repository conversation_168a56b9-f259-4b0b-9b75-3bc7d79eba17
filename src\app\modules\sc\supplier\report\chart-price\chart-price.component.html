<app-skeleton-loading [isLoading]="isLoading">
    <p><b>Trung bình : </b> {{ lineSupplierPrices?.averagePoint ?? 'Không có thông tin' }}</p>
    <p><b>Phân hạng : </b> {{ lineSupplierPrices?.averageRate ?? 'Không có thông tin' }}</p>

    <p-table styleClass="p-datatable-gridlines" [value]="[lineSupplierPrices]">
        <ng-template pTemplate="header">
            <tr>
                <th>Thời gian</th>
                <th *ngFor="let label of lineSupplierPrices?.labels">{{ label }}</th>
            </tr>
        </ng-template>

        <ng-template pTemplate="body" let-lineSupplierPrice>
            <tr>
                <td>Phần trăm</td>
                <td *ngFor="let percent of lineSupplierPrice?.percents">{{ percent }}%</td>
            </tr>
            <tr>
                <td><PERSON><PERSON><PERSON><PERSON> số</td>
                <td *ngFor="let point of lineSupplierPrice?.points">{{ point }}</td>
            </tr>
        </ng-template>
    </p-table>

    <div class="tw-mt-8">
        <canvas #templateChartLine style="height: 500px"></canvas>
    </div>
</app-skeleton-loading>
