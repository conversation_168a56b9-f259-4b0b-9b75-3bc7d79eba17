import { Attachment } from 'src/app/models/interface';
import { BaseEntity } from 'src/app/models/BaseEntity';

export interface Supplier extends BaseEntity {
    name: string;
    shortName: string;
    code?: string;
    supplierTypeId: string;
    state?: number;
    profileLink?: string;
    profileStatus?: number;
    point?: number;
    rate?: string;
    productSupplied?: string;
    address?: string;
    country?: string;
    website?: string;
    contact?: string;
    position?: string;
    email: string;
    phone?: string;
    yearBegin?: number;
    note?: string;
    qualityAgree?: string;
    dateAgree?: number;
    dateAgreeCustom?: Date;
    urlAgree?: string;
    criteriaList?: SupplierCriteria[];
    criteriaListView?: SupplierCriteria[];
    saveCriteria?: boolean;
    saveQuality?: boolean;
    criteriaAttachment?: Attachment;
    agreeQualityAttachment?: Attachment;
    removeAttachments?: number[];
    supplierDocumentRes?: SupplierDocumentRes;
    deletedAt?: number;
    deletedBy?: string;
    provideItems?: SupplierItem[];
    agreeItems?: SupplierItem[];
    itemPoDetailDTO?: ItemPoDetailDTO[];
}

export interface ItemPoDetailDTO {
    getInternalReference: string;
    getManPn: string;
    getDescription: string;
    getPrice: number;
    getAmount: number;
    getUnitPrice: string;
}

export interface SupplierCriteria extends BaseEntity {
    criteriaId: number; // ID tiêu chí
    name: string; // Tên tiêu chí
    dataType: number; // Kiểu dữ liệu
    unit: string; // Đơn vị
    note: string; // Ghi chú
    operator: number; // Toán tử
    value: string; // Giá trị
    result: boolean; // Kết quả
    valueStr?: string; // Chuỗi giá trị (có thể không có, nên dùng dấu hỏi ?)
    type?: number; // 0: New , 1: Buy
}

export interface PackageType extends BaseEntity {
    name: string;
}

export interface SupplierDocumentRes {
    errorCode: number;
    errorMessage: string;
    documentInfos: DocumentInfo[];
}

export interface DocumentInfo {
    name: string;
    status: number;
    statusMessage: string;
}

export interface LineSupplierQuality {
    labels: string[];
    points: number[];
    rates: string[];
    averagePoint: number;
    averageRate: string;
    supplierQualities: SupplierQuality[];
}

export interface LineSupplierDelivery {
    labels: string[];
    points: number[];
    listDelay: number[];
    rates: string[];
    averagePoint: number;
    averageRate: string;
    averageDelay: number;
}

export interface LineSupplierPrice {
    labels: string[];
    percents: number[];
    averagePoint: number;
    averagePercent: number;
    averageRate: string;
}

export interface SupplierQuality extends BaseEntity {
    supplierId: number;
    date: number;
    point: number;
    rate: string;
    code?: string;
}

export interface SupplierDeliveryQuality extends BaseEntity {
    supplierId: number;
    date: number;
    point: number;
    delay: string;
}

export interface SupplierType extends BaseEntity {
    name?: string;
    priority?: number;
    note?: string;
    criteria?: SupplierCriteria[];
    displayName?: string;
    createCriteria?: SupplierCriteria[];
    removeCriteria?: SupplierCriteria[];
}

export interface CriteriaBuy extends BaseEntity {
    code?: string;
    createBy?: string;
    createDate?: number;
    note?: string;
    criteriaBuySuppliers?: CriteriaBuySupplier[];
}

export interface CriteriaBuySupplier extends BaseEntity {
    criteriaBuyId?: number;
    supplierId?: number;
    supplierName?: string;
    supplierShortName?: string;
    supplierCode?: string;
    supplierTypeId?: number;
    note?: string;
    evaluationDoc?: Attachment;
    result?: boolean;
    evaluateDate: number;
    criteriaBuySupplierDetails?: CriteriaBuySupplierDetail[];
}

export interface CriteriaBuySupplierDetail extends BaseEntity {
    criteriaBuySupplierId?: number;
    criteriaId?: number;
    name?: string;
    dataType?: number;
    unit?: string;
    operator?: number;
    value?: string;
    valueCompare?: string;
    result?: boolean;
    note?: string;
}

export interface SupplierKpi extends BaseEntity {
    code?: 'DELIVERY' | 'PRICE' | 'WEIGHT' | 'TIME';

    name?: string;

    target?: number;

    result?: number;

    timeType?: number;

    year?: number;

    startTime?: number;

    endTime?: number;

    startPoint?: number;

    quantity?: number;

    priceChange?: number;

    deliveryQuantity?: number;
}

export interface Po extends BaseEntity {
    orderNo: string;
    orderDate: number;
    orderDateCustom: Date;
    supplierId: number;
    supplierName: string;
    imputation: string;
    requestNo: string;
    supplierNo: string;
    poBoqList: PoBoq[];
    updateBoq: boolean;
    boqAttachment: Attachment;
    unitPrice: string;
    removeAttachments: number[];
    poDetails: PoDetail[];
    state: number;
    totalValue: number;
    invNumberTransferByDate: { [key: number]: string };
    accountingCode: string;
    poBoqQuantityLst?: PoBoQuantity[];

    poDetailDays?: PoDetailDay[];

    detailDaysMap?: Map<string, PoDetailDay>;

    internalReferenceByLevelMap?: unknown;

    deliveredQuantityMap?: any;
}

export interface PoBoQuantity extends BaseEntity {
    poId?: number;

    supplierId?: number; // Id NCC

    indexLevel?: number; // Level

    estimatedPrice?: number; //đơn giá dự toán

    requestQuantity?: number; //tổng số lương yêu cầu

    availableQuantity?: number; // Tồn khả dụng

    spq?: number;

    moq?: number;

    unitQuantity?: number; //đơn vị của tổng số lượng đặt hàng

    orderQuantity?: number; //tổng số lượng đặt hàng

    orderPrice?: number; //đơn giá đặt hàng

    unitPrice?: string; //đơn vị của đơn giá đặt hàng

    excessQuantity?: number; //số lượng mua thừa

    excessMoney?: number; //số tiền mua thừa

    deliveryTime?: number; //thời gian giao hàng

    unitDeliveryTime?: number; //đơn vị thời gian giao hàng (0: ngày, 1: tuần)

    totalValue?: number; // tổng giá trị

    note?: string; //ghi chú

    orderDate?: number;

    poBoqList?: PoBoq[];

    projectQuantities: PoBoqProjectQuantity[]; // List of PoBoqProjectQuantity

    projectValue: PoBoqProjectValue[];

    projectDeliver: PoBoqDeliver[];
}

export interface PoBoq extends BaseEntity {
    poId: number; // id đơn hàng

    productName: string; // Mã VNPT

    indexLevel: string; // Index level

    manufacturerCode: string; // mã nhà sản xuất

    description: string; // mô tả

    manufacturer: string; // tên nhà sản xuất

    estimatedPrice: number; // đơn giá dự toán (BigDecimal equivalent in TS is number)

    requestQuantity: number; // tổng số lương yêu cầu

    spq: number; // spq

    moq: number; // moq

    unitQuantity: string;

    orderQuantity: number; // tổng số lượng đặt

    orderPrice: number; // đơn giá đặt hàng

    unitPrice: string;

    excessQuantity: number; // số lượng mua thừa

    excessMoney: number; // số tiền mua thừa

    deliveryTime: number; // thời gian giao hàng

    totalValue: number; // tổng giá trị

    note?: string; // ghi chú, optional if null is allowed

    projectQuantities: PoBoqProjectQuantity[]; // List of PoBoqProjectQuantity

    projectValue: PoBoqProjectValue[];

    projectDeliver: PoBoqDeliver[];
}

export interface PoBoqProjectQuantity extends BaseEntity {
    poBoqId?: number;

    name?: string;

    quantity?: number;
}

export interface PoBoqProjectValue extends BaseEntity {
    poBoqId?: number;

    name?: string;

    amount?: number;
}

export interface PoBoqDeliver extends BaseEntity {
    poBoqId?: number;

    date?: number;

    quantity?: number;
}

export interface PoDetail extends BaseEntity {
    poId?: number; // PO reference

    indexLevel?: number;

    accountingCode?: string; // Mã kế toán

    internalReference?: string; // Mã VNPT

    display?: string; // Mã VNPT

    manufacturer?: string; // Tên nhà sản xuất

    manPn?: string; // Mã nhà sản xuất

    description?: string; // Mô tả

    unit?: string; // Đơn vị

    unitPrice?: string;

    quantity?: number; // Số lượng

    price?: number; // Đơn giá

    amount?: number; // Thành tiền

    deliveredQuantity?: number; // Số lượng hàng đã về

    deliveredAmount?: number; // Tổng thành tiền hàng đã về

    remainingQuantity?: number; // Số lượng hàng chưa về

    remainingAmount?: number; // Tổng thành tiền còn lại

    poDetailDays?: PoDetailDay[]; // List of PoDetailDay objects
}

export interface PoDetailDTO {
    invNumberTransferByDate?: { [key: number]: string };

    poDetails?: PoDetail[];

    poDetailDays?: PoDetailDay[];

    detailDaysMap?: Map<string, PoDetailDay>;
}

export interface CanImportDTO {
    indexLevel?: number;

    internalReference?: string;

    display?: string;

    quantity?: number;

    focQuantity?: number;

    price?: number;

    amount?: number;

    description?: string;
}

export interface CanExportDTO {
    indexLevel?: number;

    internalReference?: string;

    display?: string;

    quantity?: number;

    focQuantity?: number;

    price?: number;

    amount?: number;

    description?: string;
}

export interface PoDetailDay extends BaseEntity {
    poId?: number; // PO reference

    poDetailId?: number; // PoDetail reference

    date?: number; // Date represented as a timestamp (or use a string/Date depending on actual type used)

    invTransferNumber?: string; // Inv transfer number

    internalReference?: string; // Mã VNPT

    manPn?: string; // Mã nhà sản xuất

    quantity?: number; // Số lượng

    price?: number; // Đơn giá

    amount?: number; // Thanh tien

    quantityHistory?: string; // Lich su cap nhat so luong
}

export interface PoTransfer extends BaseEntity {
    poId: number;
    contract: string; // Du an
    accountingCode: string; // Ma ke toan
    accountingCodeCustom: string[]; // Mã kế toán
    lstAccountingCode: string[]; // // Mã kế toán
    orderNo: string;
    supplierName: string;
    code: string;
    sourceDocument: string;
    invTransferNumber: string; // Ma giao dich Inventory
    invTransferId: number;
    date: number; // Ngay (timestamp)
    dateCustom: Date; // Ngay (timestamp)
    state: number; // Trang thai phieu
    warehouse: string; // Kho nhan
    payment: number;
    poTransferItems?: PoTransferItem[]; // List of PoTransferItem
    poTransferItemsDelete?: number[];
    type: 0 | 1;
    isUpdatePoDetail?: boolean;
    createdUser?: string;
    updateItemsFromSc?: boolean;
}

export interface PoTransferItem extends BaseEntity {
    transferId: number;
    indexLevel?: number; // Index level in boq
    internalReference: string;
    description: string;
    quantity: number;
    focQuantity: number;
    doneQuantity: number;
    price: number;
    amount: number;
    note: string;
    scrapQuantity: string;
    transferType: 0 | 1;
    isEdit?: boolean;
    unitPrice?: number;
    manufacturerCode?: string;
    dateCode?: number;
}

export interface SupplierMaterialChange extends BaseEntity {
    supplierId: number;
    poId: number;
    internalReference: string;
    description: string;
    date: number;
    price: number;
    unit: string;
}

export interface SupplierMaterial extends BaseEntity {
    supplierId: number;
    manufacturerId: number;
    manPn: string;
    manufacturerName: string;
    poId: number;
    internalReference: string;
    description: string;
    date: number;
    type: number;
    haveTransfer: number;
    price: number;
    unit: string;
    total?: number;
    erpManufacturer: ErpManufacturer;
    supplierShortName: string;
    supplierName: string;
}

export interface SupplierPriceChange extends BaseEntity {
    supplierId: number;
    poId: number;
    internalReference: string;
    date: number;
    price: number;
    point: number;
    percent: number;
}

export interface SummaryPo {
    quantityNew: number;
    quantityProcessing: number;
    quantityDelivering: number;
    quantityCompleted: number;
}

export interface SummarySupplier {
    quantityEvaluating: number;
    quantityLimited: number;
    quantityTemporary: number;
    quantityStop: number;
    quantityNotTraded: number;
    quantityTrading: number;
    quantityTraded: number;
}

export interface SummarySCC {
    po: SummaryPo;
    supplier: SummarySupplier;
}

export interface SupplierItem extends BaseEntity {
    supplierId: number; //Id nhà cung cấp

    manufacturerId: number; //Id manufacture erp

    manPn: string; //mã ManPn

    internalReference: string; //mã 16 sản phẩm

    productDescription: string; //mô tả sản phẩm

    erpManufacturer: ErpManufacturer;
}

export interface ErpManufacturer extends BaseEntity {
    name: string;

    code: string;

    alias: string;

    address: string;

    email: string;

    phone: string;

    note: string;
}

export interface Logistics extends BaseEntity {
    type: number; // 0: Forwarder, 1: Bảo hiểm, 2: Chuyển phát nhanh, 3: Khác
    fullName: string;
    shortName: string;
    serviceProvided: string;
    address: string;
    national: string;
    website: string;
    contactPerson: string;
    position: string;
    email: string;
    phone: string;
    tradingYear: number;
    status: number; // 0: Đang hợp tác, 1: Dừng hợp tác
    note: string;
    contractStartTime: number;
    contractEndTime: number;
    logisticsContracts: LogisticsContract[];
    logisticsDocuments: LogisticsDocument[];
    logisticsAgents: LogisticsAgent[];
    logisticsEvaluates: LogisticsEvaluate[];
    logisticsFee: LogisticsFee;

    expensesFwdLst?: LogisticExpensesFwdDTO[]; //lịch sử thông tin chi phí tháng các đơn hàng
    expensesFwdOtherLst?: LogisticExpensesFwdDTO[]; //lịch sử thông tin chi phí tháng các chi phí khác
    expensesInsuranceLst?: LogisticExpensesInsuranceDTO[]; //lịch sử thông tin chi phí tháng các đơn hàng
    expensesDeliveryLst?: LogisticExpensesDeliveryDTO[]; //lịch sử thông tin chi phí tháng các đơn hàng
}

export interface LogisticsAgent extends BaseEntity {
    logisticId: number;
    name: string;
    address: string;
}

export interface LogisticsContract extends BaseEntity {
    logisticId: number;
    startTime: number;
    endTime: number;
    startTimeCustom?: Date;
    endTimeCustom?: Date;
    attachmentIds: number[];
    attachments?: Attachment[];
}

export interface LogisticsDocument extends BaseEntity {
    logisticId: number;
    name: string;
    attachmentIds?: number[];
    attachments?: Attachment[];
}

export interface LogisticsEvaluate extends BaseEntity {
    logisticId: number;
    year: number;
    result: string;
    rank: string;
    note: string;
    attachmentId?: number;
    attachment?: Attachment;
}

export interface LogisticsFee extends BaseEntity {
    logisticId?: number;
    currency?: 'PERCENT' | 'USD' | 'VND';
    seaRoad: number;
    airRoad: number;
    lcAirSea: number;
    internalRoad: number;
    internationalRoad: number;
    minFee?: number;
}

// LogisticExpensesFwdDTO
export interface LogisticExpensesFwdDTO {
    boId: number;
    boCode: string;
    poNumber: string;
    completeDate: number;
    shipperName: string;
    trackingNumber: string;
    declarationNumber: string;
    totalWeightKgPacking: number;
    totalWeightKgOrder: number;
    chargeableWeight: string;
    chargeableWeightUnit: string;
    deliveryMethod: string;
    deliveryProvider: string;
    deliveryConditions: string;
    departurePort: string;
    importPort: string;
    finalDeliveryLocation: string;
    exchangeRateUsdToVnd: number;
    crossBorderFreightCost: number;
    customsServiceFee: number;
    importPortFee: number;
    inlandTransportFee: number;
    vatTax: number;
    infrastructureFee: number;
    storageFee: number;
    containerFee: number;
    otherFees: number;
    importPortVat: number;
    totalLogisticsCost: number;
    feeName: string;
    fee: number;
    date: number;
}

// LogisticExpensesInsuranceDTO
export interface LogisticExpensesInsuranceDTO {
    boId: number;
    boCode: string;
    billNumber: string;
    issueDate: number;
    orderNumber: string;
    deliveryMethod: string;
    feeRate: number;
    totalWeight: number;
    shippingFrom: string;
    shippingTo: string;
    salesContract: string;
    poNumber: string;
    technicalCode: string;
    department: string;
    insuranceValue: string;
    insuranceFee: number;
    note: string;
}

// LogisticExpensesDeliveryDTO
export interface LogisticExpensesDeliveryDTO {
    boId: number;
    boCode: string;
    awb: string;
    deliveryDate: number;
    fullName: string;
    po: string;
    projectCode: string;
    managingDepartment: string;
    exportCountry: string;
    expressServiceType: string;
    weight: number;
    deliveryFee: number;
    otherCosts: number;
    fuelFee: number;
    totalAmount: number;
}

export interface Bo extends BaseEntity {
    code: string;
    shipmentValue: number;
    type: number;
    paymentCondition: string;
    goodsName: string;
    supplierName: string;
    poNumber: string;
    supplierAddress: string;
    indexShipment: number;
    supplierInfo: string;
    accountingCode: string;
    totalWeight: number;
    unit: number;
    readyDate: number;
    readyDateCustom: Date;
    packageNumber: number;
    requiredArrivedDate: number;
    requiredArrivedDateCustom: Date;
    deliveryCondition: string;
    departmentId: number;
    note: string;
    noteNegotiate: string;
    attachmentIds: number[];
    attachments: Attachment[];
    finalDeliveryAddress: string;
    consignee: string;
    attachmentItemId: number;
    attachmentItem: Attachment;
    status: number; // 0 , 1, 2
    boItems?: BoItem[];
    boShippingMethods?: BoShippingMethod[];
    shipmentInfo?: ShipmentInfo;
    estimatedTransportCosts?: EstimatedTransportCost[];
    estimatedSchedules?: EstimatedSchedule[];
    estimatedInsurances?: EstimatedInsurance[];

    shippingMethodId?: number; // được chọn
    logisticForwarderId?: number; // nhà cung cấp logistic fwd
    logisticInsuranceId?: number; // nhà cung cấp logistic bảo hiểm
    poIds: number[];
    typeModule: 0 | 1; // 0: Cho Logistic, 1: Cho Po
    state: number; // 0, 1, 2, 3, 4

    poList: Po[];
    isNegotiate?: boolean;
}

export interface MonthlyQuote extends BaseEntity {
    date: number;
    dateCustom: Date;
    attachmentUrl?: string;
    additionAttachmentUrl?: string;
    routes: Route[];
}

export interface MonthlyQuoteDetail extends BaseEntity {
    type: number; // Loại xe, mức cân
    price: number; // Giá vận chuyển
    routeId: number; // ID tuyến đường
    logisticId: number; // ID logistic
}

export interface Route extends BaseEntity {
    name: string; // Tên tuyến đường
    code: string; // Code tuyến đường
    monthlyQuoteDetails?: MonthlyQuoteDetail[]; // Danh sách chi tiết báo giá theo tháng
    logisticsDetails?: Record<string, MonthlyQuoteDetail[]>;
}

export interface BoItem extends BaseEntity {
    id: number;
    boId: number;
    poId: number;
    internalReference: string;
    manufacturerId: number;
    productId: number;
    manPn: string;
    productDescription: string;
    quantity: number;
    foc: number;
    indexLevel: number;
    price: number;
    unit: number; // 0-USD, 1-VND
    note?: string;
    orderNo?: string;
}

export interface ShippingMethod extends BaseEntity {
    name: string;
}

export interface AdditionalFee extends BaseEntity {
    name: string;
    value: number;
    poId: number;
    transportCostId: number;
    position: number;
}

export interface InternationalRateUnit extends BaseEntity {
    name: string;
    shippingMethodId: number;
}

export interface BoShippingMethod extends BaseEntity {
    id: number;
    boId: number;
    shippingMethodId: number;
    logisticFwdId: number; // NCC Fwd
    roadNote: string;
    logisticsFwd?: Logistics; // NCC Fwd
    logisticInsuranceId?: number; // NCC bảo hiểm
    logisticInsuranceIds?: number[];
    logisticsInsurance?: Logistics; // NCC bảo hiểm
    isEdit: boolean;
    shippingMethod: ShippingMethod;
}

export interface BaseDepartment extends BaseEntity {
    id: number;
    code: string;
    value: number;
    name: string;
}

export interface ShipmentInfo extends BaseEntity {
    boId: number; // ID đơn hàng gốc
    requestDateOrder: string; // Ngày yêu cầu - số thứ tự
    sender: string; // Người gửi hàng
    receiver: string; // Người nhận hàng
    itemName: string; // Tên hàng
    deliveryCondition: string; // Điều kiện giao hàng
    estimatedWeightCw: number; // Khối lượng dự kiến (KG) - CW
    estimatedVolumeCbm: string; // Thể tích dự kiến (CBM)
    deliveryLocation: string; // Địa điểm giao hàng
    exportPort: string; // Cảng xuất
    importPort: string; // Cảng nhập
    finalDestination: string; // Địa điểm nhận hàng cuối cùng
    boCode: string; // Số PO/HĐ/DA
    accountingCode: string; // Mã kế toán
    indexShipment: number; // Số thứ tự shipment theo PO/HĐ/DA
    shippedValue: number; // Giá trị các lô hàng đã vận chuyển
    currentShipmentValue: number; // Giá trị lô hàng đang đề xuất phương án vận chuyển
    pendingShipmentValue: number; // Giá trị các lô hàng chưa gửi yêu cầu
    totalPoValue: number; // Tổng giá trị PO/HĐ/DA
    totalEstimatedTransportCost: number; // Tổng chi phí vận chuyển dự kiến
    transportCostRatio: number; // Tỷ lệ chi phí vận chuyển so với giá trị PO/HĐ/DA
}

export interface EstimatedTransportCost extends BaseEntity {
    boId: number; // ID đơn hàng gốc
    isChoice: boolean; // Lựa chọn
    logisticId: number; // Nhà cung cấp dịch vụ
    shippingMethodId: number; // Phương thức vận chuyển
    exchangeRate: number; // Tỷ giá VND/USD
    documentFee: number; // Phí chứng từ đầu xuất
    exportCustomsFee: number; // Phí hải quan xuất khẩu
    borderTruckYardFee: number; // Phí bến bãi hàng truck qua cửa khẩu
    exportTruckStorageFee: number; // Phí lưu ca xe đầu xuất
    exwFee: number; // Phí EXW đầu xuất
    fcaFee: number; // Phí FCA đầu xuất
    exportLocalCharge: number; // Phí local charge đầu xuất
    exportLicenseFee: number; // Phí giấy phép xuất khẩu
    magneticInspectionFee: number; // Phí kiểm tra từ tính
    otherExportFees: number; // Phí đầu xuất phát sinh khác
    factoryToPortFee: number; // Phí vận chuyển từ kho nhà máy đến cảng xuất
    importPortHandlingFee: number; // Phí nâng hạ bốc xếp tại cảng nhập
    mainTransportCost: number; // Cước phí vận chuyển chính
    importLocalCharge: number; // Phí local charge tại cảng nhập
    borderInspectionFee: number; // Phí bến bãi biên phòng kiểm dịch
    wharfFee: number; // Phí cầu cảng
    supervisionFee: number; // Phí hải quan giám sát
    serviceFee: number; // Phí dịch vụ hải quan
    domesticTransportFee: number; // Phí vận chuyển nội địa
    otherImportFees: number; // Phí phát sinh khác tại cảng nhập
    warehouseHandlingFee: number; // Phí bốc xếp tại kho
    totalEstimatedTransportCost: number; // Tổng chi phí vận chuyển dự kiến
    totalCostAfterSupplierDeduction: number; // Tổng chi phí vận chuyển sau khi trừ phân phí NCC
    internationalPrice: number; // Đơn giá vận chuyển quốc tế
    internationalRateUnitId: number; // Đơn vị tính cước quốc tế
    logisticShortName?: string; // Tên nhà cung cấp vận chuyển
    additionalFees?: AdditionalFee[];
}

export interface EstimatedSchedule extends BaseEntity {
    boId: number;
    transportMethodNote?: string;
    logisticId: number; // Nhà cung cấp dịch vụ
    shippingMethodId: number; // Phương thức vận chuyển
    readyDate: number; // Ngày hàng sẵn sàng tại địa điểm giao hàng
    readyDateCustom?: Date; // Ngày hàng sẵn sàng tại địa điểm giao hàng
    finalDeliveryRequestDate: number; // Ngày yêu cầu hàng về địa điểm nhận hàng cuối cùng
    finalDeliveryRequestDateCustom?: Date; // Ngày yêu cầu hàng về địa điểm nhận hàng cuối cùng
    exportPortDeliveryDate: number; // Ngày giao hàng tại cảng xuất
    exportPortDeliveryDateCustom?: Date; // Ngày giao hàng tại cảng xuất
    departureDate: number; // Ngày tàu chạy từ cảng xuất
    departureDateCustom?: Date; // Ngày tàu chạy từ cảng xuất
    arrivalDate: number; // Ngày tàu về cảng nhập
    arrivalDateCustom?: Date; // Ngày tàu về cảng nhập
    clearanceTime: string; // Thời gian khai nộp thuế, làm thủ tục hải quan tại cảng nhập
    finalDeliveryDate: number; // Ngày giao hàng tại địa điểm cuối cùng
    finalDeliveryDateCustom?: Date; // Ngày giao hàng tại địa điểm cuối cùng
    note: string; // Ghi chú
    roadNote: string;

    logisticShortName?: string;
}

export interface EstimatedInsurance extends BaseEntity {
    isChoice: boolean; // Lựa chọn
    boId: number;
    logisticId: number; // Nhà cung cấp dịch vụ bảo hiểm
    shippingMethodId: number; // Phương thức vận chuyển
    insuranceRate: number; // Tỷ lệ phí bảo hiểm
    exchangeRate: number; // Tỷ giá VNĐ/USD
    totalInsuranceValue: number; // Tổng số tiền bảo hiểm
    insuranceFee: number; // Phí bảo hiểm
    roadNote: string;
    minFee: number;
    logisticShortName?: string;
    currencyFee?: 'USD' | 'VND';
}

export interface InventoryProduct extends BaseEntity {
    manufacturerId: number;

    productId: number;

    productDescription: string;

    internalReference: string;

    supplierShortCode: string;
}

export interface ProductManPN extends BaseEntity {
    manufacturerId: number;

    productId: number;

    manufacturerPn: string;
}

export interface BoNegotiate extends BaseEntity {
    shipmentInfo: ShipmentInfo;

    estimatedInsurances: EstimatedInsurance[];

    estimatedSchedules: EstimatedSchedule[];

    xestimatedTransportCosts: EstimatedTransportCost[];

    noteNegotiate: string;
    isNegotiate: boolean;
}

export interface Lot extends Bo {
    boId?: number; // Id đơn hàng gốc
    salesAgentId?: number; //Id đại lý
    estimatedTransportCost?: EstimatedTransportCost;
    estimatedInsurance?: EstimatedInsurance;
    estimatedSchedule?: EstimatedSchedule;

    lotAttachmentIds: number[];
    lotAttachments: Attachment[];
    noteLot: string;
    state: number;
    lotCustom: LotCustom;
}

export interface LotHistory extends BaseEntity {
    lotId?: number; // Id lô vận chuyển
    boId?: number; // Id yêu cầu chuyển hàng
    logisticId?: number; // Id logistic
    expectedDeliveryDate?: number; // Ngày giao hàng dự kiến
    delayDeliveryDate?: number; // Thời gian delay dự kiến
    expectedLeaveDate?: number; // Ngày khởi hành dự kiến
    delayLeaveDate?: number; // Thời gian delay khởi hành
    expectedPortDate?: number; // Ngày dự kiến đến nơi
    expectedWarehouseDate?: number; // Ngày dự kiến về kho
    note?: string; // Ghi chú
    type?: number; // 0: Lịch trình vận chuyển giai đoạn 2, 1: lịch trình vận chuyển giai đoạn 3
    attachmentIds?: number[]; // Id tài liệu đính kèm
    attachments?: Attachment[]; // Id tài liệu đính kèm
    delayWarehouseDate: number; //Ngày dự kiến về kho
    delayPortDate: number; //Thời gian delay dự kiến đến nơi

    expectedDeliveryDateCustom?: Date; // Ngày giao hàng dự kiến
    expectedLeaveDateCustom?: Date; // Ngày khởi hành dự kiến
    expectedPortDateCustom?: Date; // Ngày dự kiến đến nơi
    expectedWarehouseDateCustom?: Date; // Ngày dự kiến về kho
}

// Định nghĩa interface LotCustom
export interface LotCustom extends BaseEntity {
    lotId: number; // Id lô vận chuyển
    boId: number; // Id bo
    customsDocumentNumber: string; // Số tờ khai báo hải quan
    customsDeclareDate: number; // Ngày khai báo hải quan
    customsDeclareDateCustom: Date; // Ngày khai báo hải quan
    vatTax: number; // Thuế giá trị gia tăng
    taxImport: number; // Thuế nhập khẩu
    totalTax: number; // Tổng thuế phải nộp
    orderCode: string; // Số vận đơn
    submitDate: number; // Ngày chuyển bộ thuế lên kế toán
    submitDateCustom: Date; // Ngày chuyển bộ thuế lên kế toán
    otherFee: number; // Chi phí khác
    note: string; // Ghi chú
    attachmentCustomId: number; // Id tài liệu hải quan
    attachmentCustom: Attachment[]; // Id tài liệu hải quan
    attachmentIds: number[]; // Id tài liệu đính kèm
    attachments: Attachment[]; // Id tài liệu đính kèm
}

export interface BoPayTax extends BaseEntity {
    lotId: number; // Id lô vận chuyển
    boId: number; // Id bo
    clearanceDate: number; // Ngày thông quan
    clearanceDateCustom: Date; // Ngày thông quan
    note: string; // Ghi chú
    attachmentIds: number[]; // Tập hợp Id tài liệu đính kèm
    attachments: Attachment[]; // Danh sách tài liệu đính kèm
    clearanceAttachment: Attachment;
    clearanceAttachmentId: number;
    receiverIds?: number[]; // Tập hợp Id người nhận
    content: string; // Nội dung
}

export interface BoGoodsArrive extends BaseEntity {
    lotId: number; // Id lô vận chuyển
    boId: number; // Id bo
    dateArriveCustom: Date; // Ngày hàng về
    dateArrive: number; // Ngày hàng về
    note: string; // Ghi chú
    attachmentIds: number[]; // Tập hợp Id tài liệu đính kèm
    attachments: Attachment[]; // Danh sách tài liệu đính kèm
    receiverIds: number[]; // Tập hợp Id người nhận
    content: string; // Nội dung
}

export interface LotFilter {
    ids?: number[];

    boIds?: number[];

    poNumber?: string;

    orderCode?: string;

    customsDocumentNumber?: string;

    type?: number;

    status?: number;

    indexShipment?: number;

    totalWeight?: number;

    packageNumber?: number;

    departmentId?: number;

    accountingCode?: string;

    note?: string;

    requiredArrivedDateStart?: number;

    requiredArrivedDateEnd?: number;

    readyDateStart?: number;

    readyDateEnd?: number;

    logisticId?: number;
}

export interface PaymentLot extends BaseEntity {
    lotId: number; // Id lô vận chuyển
    boId: number; // Id bo
    completeDate: number; // Ngày hoàn thành
    completeDateCustom: Date; // Ngày hoàn thành
    receiver: string; // Người nhận/người gửi
    lotCode: string; // Số vận đơn
    declarationCode: string; // Số tờ khai
    declarationCodeImport: string; // Số tờ khai import từ file tổng hợp chi phí
    totalWeightPck: number; // Tổng khối lượng trên packing list
    totalWeightLot: number; // Tổng khối lượng trên vận đơn
    weightFee: string; // Khối lượng tính phí
    weightFeeUnit: string; // Đơn vị tính khối lượng tính phí
    shippingMethod: string; // Phương thức vận chuyển
    shippingBrand: string; // Hãng vận chuyển
    deliveryCondition: string; // Điều kiện giao hàng
    deliveryAddress: string; // Địa chỉ giao hàng
    exportPort: string; // Cảng xuất
    importPort: string; // Cảng nhập
    deliveryAddressFinal: string; // Địa điểm nhận hàng cuối cùng
    da: string; // Số hợp đồng/DA/PO
    accountingCode: string; // Mã kế toán
    totalAmount: number; // Tổng giá trị lô hàng
    rateMoney: number; // Tỷ giá tiền (USD/VNĐ)
    totalShippingFee: number; // Tổng tiền phí vận chuyển
    monthlyExpensesId: number; // Id chi phí hàng tháng
}
export interface PaymentLotDeliverPort extends BaseEntity {
    lotId: number; // Id lô vận chuyển
    boId: number; // Id bo
    type: number; // Loại: 0 - cảng xuất, 1 - cảng nhập
    exportLicenseFee: number; // Phí giấy phép xuất khẩu
    coFee: number; // Phí C/O
    toExportFee: number; // Phí chuyển hàng từ điểm giao tới cảng xuất
    customsFee: number; // Phí dịch vụ hải quan
    exportFee: number; // Phí cảng xuất
    vatTax: number; // Thuế GTGT
    toImportFee: number; // Cước từ cảng xuất đến cảng nhập
    monthlyExpensesId: number; // Id chi phí hàng tháng
}

export interface PaymentLotDelivery extends BaseEntity {
    lotId: number; // Id lô vận chuyển
    boId: number; // Id bo
    deliveryFee: number; // Chi phí vận chuyển

    fuelFee: number; // chi phí xăng dầu

    otherFee: number; // chi phí khác

    monthlyExpensesId: number;
}
export interface PaymentLotImportPort extends BaseEntity {
    lotId: number; // Id lô vận chuyển
    boId: number; // Id bo
    infrastructureFee: number; // Phí hạ tầng
    storageFee: number; // Phí lưu kho
    cleanFee: number; // Phí nâng hạ, dọn dẹp container
    otherFee: number; // Phí khác
    vatTax: number; // Thuế GTGT
    monthlyExpensesId: number; // Id chi phí hàng tháng
}

export interface PaymentLotLocal extends BaseEntity {
    lotId: number; // Id lô vận chuyển
    boId: number; // Id bo
    insuranceFee: number; // Chi phí bảo hiểm
    localFee: number; // Chi phí local
    description: string; // Diễn giải
    monthlyExpensesId: number; // Id chi phí hàng tháng
    attachmentIds: number[];
    attachments: Attachment[];
}
export interface PaymentLotTax extends BaseEntity {
    lotId: number; // Id lô vận chuyển
    boId: number; // Id bo
    vatTax: number; // Thuế GTGT
    importTax: number; // Thuế nhập khẩu
    monthlyExpensesId: number; // Id chi phí hàng tháng
}

export interface PaymentDTO {
    paymentLot: PaymentLot;
    paymentLotTax: PaymentLotTax;
    paymentLotLocal: PaymentLotLocal;
    paymentLotImportPort: PaymentLotImportPort;
    paymentLotDeliverPortExport: PaymentLotDeliverPort;
    paymentLotDeliverPortImport: PaymentLotDeliverPort;
    paymentLotDelivery: PaymentLotDelivery;
    totalFeeAfterTax: number;
    totalFeeDifference: number;
    totalFeePreTax: number;
}

export interface BoDto {
    boId: number;
    goodsName: string; // Tên hàng hóa
    supplierName: string; // Tên nhà cung cấp
    supplierAddress: string; // Địa chỉ người giao hàng
    totalWeight: number; // Tổng khối lượng lô hàng
    packageNumber: number; // Số kiện lô hàng
    deliveryCondition: string; // Điều kiện giao hàng
    shipmentValue: number; // Tổng giá trị shipment dự kiến
    departDate: number; // Ngày khởi hành
    paymentCondition: string; // Điều kiện thanh toán
    boCode: string; // Mã bo
    accountingCode: string; // Mã kế toán
    rateMoney: number; // Tỷ giá VNĐ/USD
    boInsuranceDtos: BoInsuranceDto[]; // Danh sách BoInsuranceDto
    expectedLeaveDate: number;
    expectedLeaveDateCustom?: Date; // Ngày khởi hành dự kiến
    unitLabel: string;
    exportDate?: number;
    exportDateCustom?: Date;
    tabulator?: string;
}

export interface BoInsuranceDto {
    name: string; // Tên công ty bảo hiểm
    isChoice: boolean; // Được lựa chọn
    insuranceRate: number; // Tỷ lệ chi phí chấp nhận
    insuranceFee: number; // Phí bảo hiểm
    note: string; // Ghi chú
}

export interface LotHistoryExport extends BaseEntity {
    lotId: number;
    lotHistoryId: number;
    attachmentId: number;
    attachment: Attachment;
    type: number;
}

export interface MonthlyExpenses extends BaseEntity {
    fileName?: string;

    logisticId?: number;

    date?: number;

    type?: number;

    attachmentId?: number;
}

export interface Rfq extends BaseEntity {
    code?: string;

    name?: string;

    note?: string;

    attachment?: Attachment;

    items?: RfqItem[];

    inventories?: RfqInventory[];

    state?: number;

    itemAttachment: Attachment;

    inventoryAttachment: Attachment;

    materials: RfqMaterial[];

    rfqMaterialProviders: RfqMaterialProvider[];

    levelsErr?: string[];
}

export interface RfqInventory extends BaseEntity {
    rfqId?: number;

    manPn?: string;

    vnptPn?: string;

    description?: string;

    manufacturer?: string;

    quantity?: number;
}

export interface RfqItem extends BaseEntity {
    type?: number; // 0 - R&D BOM, 1 - Bán lẻ

    rfqId?: string;

    project?: string;

    accountingCode?: string;

    productName?: string;

    productShortName?: string;

    manufacturerCode?: string;

    quantity?: number;

    bomId?: number;

    bomVersion?: string;

    vnptPn?: string;

    manPn?: string;

    description?: string;

    rfqItemDates?: RfqItemDate[]; // Lưu danh sách JSON
}

export interface RfqItemDate extends BaseEntity {
    itemId?: number;

    date?: number;

    quantity?: number;
}

export interface RfqMaterial extends BaseEntity {
    rfqId: number;

    level: string;

    levels: string[];

    manPn: string;

    description: string;

    section: string;

    manufacturerId: number;

    manufacturerName?: string;

    requestQuantity: number;

    availableQuantity: number;

    orderQuantity: number;

    note: string;

    dateCode?: string;

    providers?: string;

    providerIds?: number[];

    rfqMaterialDates?: RfqMaterialDate[];

    checkNcc?: boolean;
}

export interface RfqMaterialDate extends BaseEntity {
    materialId?: number;

    rfqId?: number;

    date?: number;

    quantity?: number;
}

export interface RfqMaterialProvider extends BaseEntity {
    materialId?: number;

    providerId?: number;
}

export interface Bom extends BaseEntity {
    code?: string;

    rdBom?: string;

    rdBomId?: number;

    note?: string;

    status?: number;

    accountingCode?: string;

    rd_bom?: string;

    attachmentId?: number;

    attachment?: Attachment;

    usdValue?: number;

    vndValue?: number;

    excessValue?: number;

    valueDifference?: number;

    unitDeliveryTime?: number;

    exchangeRate?: number;

    bomItems?: BomItem[];

    updateItem?: boolean;
    bomCost: number;
    eBom: number;
    mBom: number;
    pBom: number;
    logLevels: LogLevel[];
    poGroups?: { [state: number]: Po[] };
}

export interface BomItem extends BaseEntity {
    key?: number;

    bomId: number; // ID của BOM

    level: string; // Cấp độ

    qtyPerProduct: number; // Định mức

    attritionRate: number; // Tỷ lệ tiêu hao

    requiredQuantity: bigint; // Tổng số lượng yêu cầu

    type: string; // Loại BOM

    availableQuantity: bigint; // Tồn khả dụng

    orderedQuantity: bigint; // Số lượng cần đặt hàng

    internalReference: string; // Mã VNPT

    manPn: string; // Mã nhà sản xuất

    productDescription: string; // Mô tả

    manufacturerId: number; // Nhà sản xuất

    estimatedPrice: number; // Đơn giá dự toán
    usdAskPrice: number;
    vndAskPrice: number;
    usdPrice: number; // Đơn giá thực tế (USD)

    vndPrice: number; // Đơn giá thực tế (VND)
    priceDifferenceAsk: number;
    priceDifference: number; // Tăng/giảm đơn giá (%)

    spq: bigint; // SPQ

    moq: bigint; // MOQ

    deliveryTime: number; // Thời gian giao hàng

    finalOrderedQuantity: bigint; // Tổng số lượng đặt hàng

    foc: bigint; // FOC

    usdValue: number; // Tổng giá trị đặt hàng (USD)

    vndValue: number; // Tổng giá trị đặt hàng (VND)

    excessQuantity: bigint; // Số lượng đặt hàng thừa

    excessValue: number; // Tổng giá trị đặt hàng thừa

    valueDifference: number; // Tổng giá trị thực tế chênh lệch

    deliveryCondition: string; // Điều kiện giao hàng

    supplierId: number; // ID nhà cung cấp

    supplierShortName: string;

    supplierCode: string; // Tên viết tắt nhà cung cấp

    contractNumber: string; // Số hợp đồng/đơn hàng

    note: string; // Ghi chú

    manufacturer?: string; // Nhà sản xuất (Transient)

    bomItemDays?: BomItemDay[]; // Danh sách ngày BOM (Transient)

    statusInternalReference: number; //0: NON_DRAFT; 1: CREATED_DRAFT, 2: APPROVE_DRAFT
}

export interface LogLevel extends BaseEntity {
    bomId: number; // ID của BOM
    level: string; // Cấp độ
    vnptManPn: string;
}

export interface BomItemDay extends BaseEntity {
    bomId: number; // ID của BOM

    bomItemId: number; // ID của BOM Item

    date: number; // Ngày (Epoch timestamp hoặc định dạng số nguyên)

    quantity: number; // Số lượng
}

export interface PoDraft extends BaseEntity {
    code?: string;

    note?: string;

    scBomIds: number[];

    poItems: Po[];
}

export interface MaterialSupplier extends BaseEntity {
    rfqId?: number;

    fullName?: string;

    shortName?: string;

    note?: string;

    country?: string;
}

export interface InformationRetrieval {
    id: number;
    vnptPn: string;
    vnptManPn: string;
    manufacturerName: string;
    manPn: string;
    manufacturerId: number;
    description: string;
    supplierIdList: string;
    supplierShortName: string;
    supplierShortNameList: string;
    supplierFullNameList: string;
}

export interface PoReportReq {
    startTime: number;
    endTime: number;
    manufactureId: number;
    supplierId: number;
    vnptMan: string;
    manufacturerCode: string;
    poId: number;
    accountingCode: string;
}
export interface PoDetailDayDTO {
    poId: number;

    indexLevel: number;

    date: number;

    accountingCode: string;

    internalReference: string;

    quantity: number;
}
export interface PoDetailInfoDTO {
    poId: number;

    indexLevel: number;

    accountingCode: string;

    productName: string;

    manufacturerCode: string;

    manufacturer: string;

    description: string;

    orderNo: string;

    shortName: string;

    orderDate: number;

    orderQuantity: number;

    availableQuantity: number;

    orderPrice: number;

    detailDayDTOList: PoDetailDayDTO[];
}
export interface BoChangeStateDTO {
    status?: number;

    receiverId?: number[];

    content?: string;
}

export interface PoInfoDTO {
    indexLevel?: number;

    poId?: number;

    internalReference?: string;

    manPn?: string;

    price?: number;

    unitPrice?: number;

    description?: string;

    unit?: number;

    remainingQuantity?: number;

    transferQuantity?: number;

    availableQuantity?: number;

    remainingFocQuantity?: number;
}

export interface BoResponseITF {
    id: number;
    boCode: string;
    poNumber: string;
    indexShipment: number;
    accountingCode: string;
    readyDate: number;
    requiredArrivedDate: number;
    totalWeight: number;
    packageNumber: number;
    shipmentValue: number;
    dateArrive: number;
    completeDate: number;
    note: string;
    state: number;
}

export interface BoRequestDTO {
    boCode: string | null;
    boIds: Array<number> | null;
    poNumber: string | null;
    indexShipment: number | null;
    accountingCode: string | null;
    startReadyDate: number | null;
    endReadyDate: number | null;
    startRequiredArrived: number | null;
    endRequiredArrived: number | null;
    totalWeight: number | null;
    packageNumber: number | null;
    shipmentValue: number | null;
    startArrivedDate: number | null;
    endArrivedDate: number | null;
    startCompleteDate: number | null;
    endCompleteDate: number | null;
    note: string | null;
    state: number | null;
}
