import { RouterModule } from '@angular/router';
import { NgModule } from '@angular/core';
import {
    canAuthenticate,
    canAuthenticateChild,
    canAuthorize,
    canRedirectDashBoardSCC,
    canRedirectDashBoardSQC,
    canRedirectDashBoardPMS,
    canRedirectLogin,
} from './core/auth/auth.guard';
import { MODULE } from './models/constant';
import { SIDE_BAR_SMART_QC } from './layout/components/sidebar/data/smart-qc';
import { SIDE_BAR_SC } from './layout/components/sidebar/data/sc';
import { SIDE_BAR_ADMINISTRATION } from './layout/components/sidebar/data/administration';
// Vivas ---------------
import { SIDE_BAR_PMS } from './layout/components/sidebar/data/pms';
import { SIDE_BAR_PTM } from './layout/components/sidebar/data/ptm';
// Vivas ---------------
@NgModule({
    imports: [
        RouterModule.forRoot(
            [
                {
                    path: '',
                    title: 'BOS',
                    loadComponent: () => import('./layout/main-layout/app.layout.component').then((c) => c.AppLayoutComponent),
                    canActivate: [canAuthenticate],
                    canActivateChild: [canAuthenticateChild],
                    data: {
                        module: MODULE.BOS,
                        sidebar: [],
                    },
                    children: [
                        {
                            path: 'masterdata',
                            redirectTo: '/sqc/dashboard',
                        },
                        {
                            path: '',
                            data: {
                                authorize: [
                                    'ROLE_SYSTEM_ADMIN',
                                    'ROLE_QC_PM',
                                    'ROLE_QC_SUBPM',
                                    'ROLE_QC_CUSTOMER',
                                    'ROLE_SUB_ADMIN',
                                    'ROLE_DAC',
                                    'ROLE_MANUFACTURING',
                                    'ROLE_WARRANTY',
                                    'ROLE_SUPPLIER_CHAIN',
                                    'ROLE_PRODUCT_DOCUMENTATION',
                                ],
                            },
                            canActivate: [canAuthorize],
                            pathMatch: 'full',
                            loadComponent: () => import('src/app/modules/dashboard/dashboard.component').then((m) => m.DashboardComponent),
                        },
                        {
                            path: 'sqc',
                            title: 'SmartQC',
                            data: {
                                authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'ROLE_QC_SUBPM', 'ROLE_QC_CUSTOMER'],
                                sidebar: SIDE_BAR_SMART_QC,
                                module: MODULE.SMART_QC,
                            },
                            canActivate: [canAuthorize, canRedirectDashBoardSQC],
                            loadChildren: () => import('src/app/modules/smart-qc/smart-qc.module').then((m) => m.SmartQcModule),
                        },
                        {
                            path: 'administration',
                            title: 'Quản trị hệ thống',
                            data: {
                                authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_SUB_ADMIN'],
                                sidebar: SIDE_BAR_ADMINISTRATION,
                                module: MODULE.ADMINISTRATION,
                            },
                            canActivate: [canAuthorize],
                            loadChildren: () => import('src/app/modules/administration/administration.module').then((m) => m.AdministrationModule),
                        },
                        {
                            path: 'sc',
                            data: {
                                authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_SUPPLIER_CHAIN'],
                                sidebar: SIDE_BAR_SC,
                                module: MODULE.SUPPLIER_CHAIN,
                            },
                            canActivate: [canAuthorize, canRedirectDashBoardSCC],
                            loadChildren: () => import('src/app/modules/sc/sc.module').then((m) => m.SCModule),
                        },

                        // Vivas ---------------
                        {
                            path: 'pms',
                            data: {
                                authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_PRODUCT_DOCUMENTATION'],
                                sidebar: SIDE_BAR_PMS,
                                module: MODULE.PMS,
                            },
                            canActivate: [canAuthorize, canRedirectDashBoardPMS],
                            loadChildren: () => import('src/app/modules/pms/pms.module').then((m) => m.PMSModule),
                        },
                        {
                            path: 'ptm',
                            data: {
                                // authorize: [],
                                sidebar: SIDE_BAR_PTM,
                                module: MODULE.PTM,
                            },
                            canActivate: [canAuthorize],
                            loadChildren: () => import('src/app/modules/ptm/ptm.module').then((m) => m.PTMModule),
                        },
                        // Vivas ---------------
                    ],
                },

                {
                    path: 'login',
                    title: 'Đăng nhập',
                    canActivate: [canRedirectLogin],
                    loadComponent: () => import('./pages/account/login.component').then((c) => c.LoginComponent),
                },
                {
                    path: 'reset-password',
                    title: 'Đổi mật khẩu',
                    loadComponent: () => import('./pages/password/password.component').then((c) => c.PasswordComponent),
                },
                {
                    path: '',
                    title: 'BOS',
                    loadComponent: () => import('./layout/simple-layout/simple-layout.component').then((c) => c.AppSimpleLayoutComponent),
                    canActivate: [canAuthenticate],
                    canActivateChild: [canAuthenticateChild],
                    data: {
                        module: null,
                    },
                    children: [
                        {
                            path: 'error',
                            title: 'Lỗi',
                            canActivate: [canAuthenticate],
                            loadComponent: () => import('./pages/error/error.component').then((c) => c.ErrorComponent),
                        },
                        {
                            path: 'guide-document',
                            title: 'HDSD',
                            loadComponent: () => import('src/app/modules/administration/guide/guide.component').then((m) => m.GuideComponent),
                        },
                        {
                            path: 'access',
                            title: 'Từ chối truy cập',
                            canActivate: [canAuthenticate],
                            loadComponent: () => import('./pages/access/access.component').then((c) => c.AccessComponent),
                        },
                        {
                            path: 'notfound',
                            title: 'Trang không tồn tại',
                            canActivate: [canAuthenticate],
                            loadComponent: () => import('./pages/notfound/notfound.component').then((c) => c.NotfoundComponent),
                        },
                    ],
                },
                { path: '**', redirectTo: '/notfound' }, // Wildcard route for handling 404s
            ],
            { scrollPositionRestoration: 'enabled', anchorScrolling: 'enabled', onSameUrlNavigation: 'reload' },
        ),
    ],
    exports: [RouterModule],
})
export class AppRoutingModule {}
