import { MenuItem } from 'primeng/api';
import { BaseEntity } from '../BaseEntity';
import { Observable } from 'rxjs';
import { Type } from '@angular/core';

export interface ApiVersionDto {
    id: number;
    productId: number;
    versionName: string;
    versionEnum: number;
    status: number;
    lifecycleStage: number;
    cloneFromVersionId: number | null;
    cloneFromVersionName: string | null;
    note: string | null;
    isCurrent: boolean | null;
    created: number;
    updated: number;
    createdBy: string;
    updatedBy: string;
    hardwareVersion: string | null;
}

export interface ProductSoftware extends BaseEntity {
    name?: string;
    /** Đường dẫn file */
    path?: string;
    /** MD5 checksum của file */
    md5?: string;
    /** Tên version hiển thị */
    versionName?: string;
    /** Mã enum version */
    versionEnum?: number;
    /** Thời gian build (timestamp) */
    buildTime?: number;
    /** Link hoặc nội dung hướng dẫn sử dụng */
    userGuide?: string;
    /** <PERSON> phép xóa hay không */
    allowDelete?: boolean;
    /** ID gốc */
    rootId?: number;
    /** ID tham chiếu */
    refId?: number;
    instructionName?: string;
    productLineName?: string;
    productModelName?: string;
    productName?: string;
    existedWithThisName?: boolean;
    softwareName?: string;
    productIds?: number[];
    productLines?: number[];
}
export interface ProfileSoftware {
    productId: number;
    productName: string;
    productVersionId: number;
    productVersionName: string;
    status: number | null;
    softwareResourceId: number;
    vnptManPn: string;
    lifecycleStage?: number;
}

export interface ProductLine extends BaseEntity {
    id: number;
    name: string;
    description: string | null;
    productModels: [] | null;
    createdAt: number | null;
    modifiedAt: number | null;
    modifiedBy: number | null;
}
export interface ProductVersion extends BaseEntity {
    id?: number;
    productId?: number;
    versionName?: string | null;
    versionEnum?: number | null;
    status?: number | null;
    lifecycleStage?: number | null;
    cloneFromVersionId?: number | null;
    cloneFromVersionName?: string | null;
    note?: string | null;
    isCurrent?: boolean | null;
    approvalTime?: number | null;
    compareUrl: string | null;
    compared: number | null;
}
export interface ProductRecordVersion {
    id?: number;
    version: string;
    lastUpdate: Date;
    statusName: 'Approved' | 'Rejected' | 'Draft' | string;
    selected?: boolean;
    note?: string;
    lifecycleStage?: number;
    status?: number;
    isCurrent?: unknown;
}
export interface ProductDetail extends BaseEntity {
    modelId?: number | null;
    modelName?: string | null;
    lineId?: number;
    lineName?: string | null;
    name?: string;
    vnptManPn?: string;
    tradeName?: string;
    tradeCode?: string;
    description?: string | null;
    generation?: string | null;
    customerIds?: number[];
    firmwareVersion?: string | null;
    imageName?: string | null;
    rdbom?: string | null;
    lifecycleStage?: number;
    version?: string | null;
    imageUrl?: string | null;
    hardwareVersion?: string | null;
    stage?: string;
    productVersions?: ProductVersion[];
    softwareResourceDtos?: ProductSoftware[];
}

export interface AnalyzeResp {
    softwareName?: string;
    version?: string;
    buildTime?: string;
    error?: string | null;
    presignedUrl?: string;
    objectPath?: string;
    md5CheckMatch?: boolean | null;
    fileName?: string | null;
}

export interface CreateProductDocumentDto extends BaseEntity {
    versionId: number;
    description: string;
    type: number;
    category: number;
    documentType: number;
    fileName: string;
    filePath: string;
    md5: string;
    buildTime: number | string;
    versionName: string;
    note: string;
}

export interface ProductDocumentInput extends BaseEntity {
    versionId: number;
    type: number;
    category: number;
    isDefault: number;
    documentType?: number;
    description: string;
    fileName?: string;
    filePath?: string | null;
    md5?: string | null;
    buildTime?: string | null;
    versionName?: string | null;
    note?: string | null;
    fileCode?: string | null;
    documentName?: string | null;
}

export interface SendApprovalRequest {
    productVersionId: number;
    email: string;
    note: string;
    ccEmail: string[];
}
export interface ApprovalRequest {
    productVersionId: number;
    type: number;
    note: string;
}
export interface FilterProductDocumentRequest {
    versionId: number;
    types: number;
}

export interface ColumnTableSection {
    header: string;
    field: string;
    type: string;
    // cho select-one:
    options?: { label: string; value: any }[];
    totalRecords?: number;
    optionLabel?: string;
    optionValue?: string;
    placeholder?: string;
    lazyLoadFn?: (row: any, query: string) => Observable<any[]>;
    loading?: boolean;
    minLength?: number;
    minWidth?: string;
}
export interface RdBom {
    id: number;
    name: string;
    // … các trường khác nếu có
}
export interface UpdateVersionDto {
    productVersionId: number;
    softwareResourceId: number;
    lifeCycleStage: number;
    lifeCycleStageSelect?: number;
}
export interface ProductLine extends BaseEntity {
    name: string;
    description: string | null;
    productModels: [] | null;
    createdAt: number | null;
    modifiedAt: number | null;
    modifiedBy: number | null;
}
export interface SecuredMenuItem extends MenuItem {
    authority?: string[]; // mảng các quyền cần thiết để xem item này
}
export interface ChangeHistoryRecord {
    stt: number;
    action: string;
    detail: string;
    beforeFile?: string;
    beforeVersion?: string;
    beforeBuildtime?: string;
    afterFile?: string;
    afterVersion?: string;
    afterBuildtime?: string;
    note?: string;
    user?: string;
    timestamp: string;
}
export interface TabItem {
    header: string;
    type: number; // 1: thiết kế, 2: sản xuất, 4: chất lượng
    component: Type<any>;
    inputs?: Record<string, any>;
}
export interface SectionConfig {
    category: string;
    title: string;
    columns: Array<ColumnTableSection>;
    rows: any[];
}
export interface DropdownOption {
    label: string;
    value: number;
}

export interface CloneItem {
    label: string;
    selected: boolean;
    value: number;
}

export interface VersionRecord {
    id: number;
    date: string;
    version: string;
    author: string;
    updateContent: string;
    cloneVersion?: string;
}
export interface ProductRecordVersion {
    id?: number;
    version: string | null;
    lastUpdate: Date | null;
    statusName: 'Approved' | 'Rejected' | 'Draft' | string | null;
    selected?: boolean | null;
    note?: string | null;
    lifecycleStage?: number | null;
    status?: number | null;
    isCurrent?: unknown;
    created?: Date | null;
    approvalTime?: Date | null;
    compareUrl?: string | null;
    compared?: number | null;
}
