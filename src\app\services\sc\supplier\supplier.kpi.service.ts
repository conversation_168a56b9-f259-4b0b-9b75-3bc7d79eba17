import {Injectable} from "@angular/core";
import {BaseService} from "../../base.service";
import {HttpClient} from "@angular/common/http";
import {SupplierKpi} from "../../../models/interface/sc";

@Injectable()
export class SupplierKpiService extends BaseService<SupplierKpi> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/supplier-kpi');
    }

    batchUpdate(supplierKpis: SupplierKpi[]) {
        return this.http.put<void>('/sc/api/supplier-kpi/batch-update', supplierKpis);
    }
}
