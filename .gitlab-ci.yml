stages:
    - install
    - build
    - dockerize
    - push
    - deploy

## may need setup test or lint stage before build stage
variables:
    BUILD_TAG: '${CI_PIPELINE_ID}-${CI_COMMIT_SHA}'
    DOCKER_DRIVER: overlay2
    REGISTRY_URL: 'registry.vivas.vn/dac/smartqc-web'


# Step 1: Install dependencies
install-deps:
    stage: install
    image: node:18-alpine
    script:
        - npm install
    cache:
        key: npm-${CI_COMMIT_REF_SLUG}
        paths:
            - node_modules/
    artifacts:
        paths:
            - node_modules/
        expire_in: 1h
    only:
        - release

# Step 2: Build Angular app
build-angular:
    stage: build
    image: node:18-alpine
    dependencies:
        - install-deps
    script:
        - npx ng build --configuration=vivas
    artifacts:
        paths:
            - dist/new-webapp
        expire_in: 1h
    only:
        - release

# Step 3: Build Docker image (uses dist from build)
dockerize-angular:
    stage: dockerize
    image: docker:20.10
    services:
        - docker:dind
    dependencies:
        - build-angular
    before_script:
        - ls -lah dist/new-webapp
        - cp -r dist/new-webapp .
        - echo "$REGISTRY_PASS" | docker login registry.vivas.vn -u "robot\$dac+unauthenticate" --password-stdin
    script:
        - docker build -t $REGISTRY_URL:$BUILD_TAG -f Dockerfile .
    only:
        - release

# Step 4: Push Docker image
push-angular:
    stage: push
    image: docker:20.10
    services:
        - docker:dind
    before_script:
        - echo "$REGISTRY_PASS" | docker login registry.vivas.vn -u "robot\$dac+unauthenticate" --password-stdin
    script:
        - docker push $REGISTRY_URL:$BUILD_TAG
    only:
        - release

# Step 5: Deploy to staging
deploy-staging:
    stage: deploy
    image: alpine:latest
    only:
        - release

    before_script:
        - apk add --no-cache openssh
        - mkdir -p ~/.ssh
        - echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
        - chmod 600 ~/.ssh/id_rsa
        - ssh-keyscan -H "$DEPLOY_HOST" >> ~/.ssh/known_hosts
    script:
        - |
            ssh $DEPLOY_USER@$DEPLOY_HOST "
            sed -i 's/^FE_IMAGE_TAG=.*/FE_IMAGE_TAG=$BUILD_TAG/' /opt/dac/.env && \
            cd /opt/dac && \
            docker compose pull && \
            docker compose up -d
            "
    environment:
        name: staging
