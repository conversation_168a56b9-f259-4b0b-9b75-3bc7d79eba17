import { Directive, ElementRef, Input, OnInit } from '@angular/core';
import { isArray } from 'lodash';
import { AuthService } from 'src/app/core/auth/auth.service';

@Directive({
    selector: '[appAuthorities]',
    standalone: true,
})
export class AttributeAuthorityDirective implements OnInit {
    @Input('appAuthorities') authorities: string[];

    constructor(
        private authService: AuthService,
        private el: ElementRef,
    ) {}

    ngOnInit(): void {
        if (isArray(this.authorities) && !this.authService.hasAuthority(this.authorities)) {
            this.el.nativeElement.style.display = 'none';
        }
    }
}
