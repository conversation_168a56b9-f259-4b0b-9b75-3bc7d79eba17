<p-table
    [value]="poTransfers"
    styleClass="p-datatable-gridlines "
    [scrollable]="true"
    [loading]="loading"
    scrollHeight="700px"
>
    <ng-template pTemplate="header">
        <tr>
            <th>STT</th>
            <th style="max-width: 30rem">Mã kế toán</th>
            <th>Mã giao dịch</th>
            <th><PERSON><PERSON><PERSON> lập</th>
            <th>Trạng thái</th>
            <th>Thao tác</th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-transfer let-rowIndex="rowIndex">
        <tr>
            <td>{{ rowIndex + 1 }}</td>
            <td style="text-overflow: ellipsis; text-wrap: nowrap; max-width: 30rem; overflow: hidden">
                {{ transfer.accountingCode?.replaceAll('*', ', ') }}
            </td>
            <td>
                <a
                    [href]="environment.HOST_INVENTORY + 'transfers/' + transfer.invTransferId + '/details'"
                    target="_blank"
                    >{{ transfer.invTransferNumber }}</a
                >
            </td>
            <td>{{ transfer.date | date: 'dd/MM/yyyy' }}</td>
            <td>
                <p-tag [severity]="getSeverity(transfer)" [value]="getValueTagState(transfer)" />
            </td>

            <td>
                <div class="tw-flex tw-flex-nowrap tw-gap-3">
                    <p-button
                        severity="secondary"
                        icon="pi pi-info-circle tw-text-blue-400"
                        pTooltip="Chi tiết"
                        tooltipPosition="top"
                        (click)="viewDetail(transfer)"
                    >
                    </p-button>
                    <p-button
                        *ngIf="transfer.state === 0 || transfer.state === 3"
                        severity="secondary"
                        (click)="confirmDelete(transfer, rowIndex)"
                        icon="pi pi-trash tw-text-red-400"
                        pTooltip="Xóa"
                        tooltipPosition="top"
                        [appAuthorities]="['ROLE_SYSTEM_ADMIN', 'sc_transfer_import_delete']"
                    >
                    </p-button>

                    <p-button
                        content
                        severity="secondary"
                        icon="pi pi-arrow-circle-down"
                        pTooltip="Tải xuống"
                        tooltipPosition="top"
                        (click)="export(transfer.id)"
                        *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_transfer_import_excel']"
                    ></p-button>

                    <p-button
                        *ngIf="transfer.invTransferId == null || transfer.invTransferNumber == null"
                        content
                        severity="success"
                        icon="pi pi-sync"
                        pTooltip="Đồng bộ lại kho"
                        tooltipPosition="top"
                        (click)="reSyncInv(transfer.id)"
                        [appAuthorities]="['ROLE_SYSTEM_ADMIN']"
                    ></p-button>
                </div>
            </td>
        </tr>
    </ng-template>
</p-table>

<div class="tw-flex tw-justify-end tw-mt-4">
    <p-button
        [disabled]="po.state === PO_STATE_CONSTANT.COMPLETED"
        label="Thêm PCG"
        size="small"
        (click)="addTransfer()"
        *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_transfer_import_edit']"
    ></p-button>
    <app-edit-po-transfer
        *ngIf="isEdit"
        [visible]="isEdit"
        [po]="po"
        (updatePoDetails)="updateDetails($event)"
        [poTransfer]="poTransferView"
        (onClose)="handleClose()"
        (onSuccess)="getListTransfer()"
    ></app-edit-po-transfer>
</div>
