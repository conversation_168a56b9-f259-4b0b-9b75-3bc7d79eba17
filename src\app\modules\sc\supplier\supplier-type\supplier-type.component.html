<app-sub-header [items]="[{ label: 'Quản lý nhà cung cấp' }, { label: 'Quản lý loại nhà cung cấp' }]" [action]="actionHeader"></app-sub-header>

<ng-template #actionHeader>
    <!--<p-button routerLink="create" label="Tạo mới" severity="success" />-->
</ng-template>
<div style="padding: 1rem 1rem 0 1rem">
    <app-table-common
        [tableId]="tableId"
        [columns]="columns"
        [data]="state.data"
        [loading]="state.isFetching"
        [filterTemplate]="filterTemplate"
        name="Danh sách loại nhà cung cấp"
        [selectionMode]="null"
        [funcDelete]="delete"
    >
        <ng-template #filterTemplate>
            <tr>
                <th [appFilter]="[tableId, 'name']">
                    <app-filter-table [tableId]="tableId" field="name"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'priority']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="priority"
                        type="select-one"
                        [rsql]="true"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'value',
                            options: priority,
                        }"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'note']">
                    <app-filter-table [tableId]="tableId" field="note"></app-filter-table>
                </th>
                <th></th>
            </tr>
        </ng-template>

        <ng-template #templateStatus let-rowData>
            <p-tag [style]="{ width: '100%' }" *ngIf="rowData.priority === 0" severity="info" value="Minor"></p-tag>

            <p-tag [style]="{ width: '100%' }" *ngIf="rowData.priority === 1" severity="warning" value="Major"></p-tag>
        </ng-template>

        <ng-template #templateAction let-rowData>
            <a class="tw-py-1 tw-px-2 bg-blue-400 text-white tw-rounded" [routerLink]="rowData.id">-> </a>
        </ng-template>
    </app-table-common>
</div>
