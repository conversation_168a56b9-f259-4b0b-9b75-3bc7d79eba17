import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { Bom } from 'src/app/models/interface/sc';
import { ApiResponse } from '../../../models/interface';
import { ParamsTable } from 'src/app/shared/table-module/table.common.service';

@Injectable()
export class BomService extends BaseService<Bom> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/bom');
    }

    import(file: File, rdBomId: number) {
        const formData: FormData = new FormData();
        formData.append('file', file);
        if (rdBomId) {
            formData.append('rdBomId', rdBomId.toString());
        }

        return this.http.post<ApiResponse>('/sc/api/bom/import', formData);
    }

    getPageTableNeedPo({ native = '', pageable = '&page=0&size=10', rsql = '' }: ParamsTable) {
        return this.http.get<Bom[]>(`${this.baseUrl}/search-bom?needPo=true&query=${rsql}${native}${pageable}`, {
            observe: 'response',
        });
    }
}
