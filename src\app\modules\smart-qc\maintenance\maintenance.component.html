<app-sub-header [items]="itemsHeader" [action]="actionHeader">
    <ng-template #actionHeader>
        <p-button
            label="Thêm mới"
            severity="success"
            (click)="handleShowPopup()"
            *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'sqc_maintenance_edit']"
        />
        <app-popup-upload
            header="Nhập thông tin xử lý lỗi"
            label="Nhập Excel"
            [types]="['excel']"
            (onUpload)="handleUploadImportCreate($event)"
            [urlError]="urlErrorCreate"
            [formItems]="itemsFormPopup"
            [formTemplate]="formTemplate"
            service="/smart-qc/api"
            [disabled]="!contractIdFile"
            (onClickDowload)="handleClickDownFile()"
            *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'sqc_maintenance_import']"
        >
            <ng-template #formTemplate let-formGroup>
                <div class="tw-flex tw-flex-col tw-gap-4">
                    <app-form-item label="Dự án" layout="vertical">
                        <app-filter-table
                            type="select-one"
                            formControlName="contractId"
                            [configSelect]="{
                                fieldValue: 'id',
                                fieldLabel: 'name',
                                rsql: true,
                                url: '/smart-qc/api/contract/search',
                            }"
                            placeholder="Dự án"
                            (onChange)="changeContractId($event)"
                        ></app-filter-table>
                    </app-form-item>
                    <i *ngIf="!contractIdFile" class="tw-text-red-400 tw-col-span-8">Vui lòng chọn dự án </i>
                </div>
            </ng-template>
        </app-popup-upload>
        <app-popup
            header="Xuất danh sách "
            severity="success"
            label="Xuất Excel"
            *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'sqc_maintenance_export']"
            (onSubmit)="exportExcel($event)"
            typePopup="download"
        ></app-popup>
    </ng-template>
</app-sub-header>
<div style="padding: 1rem 1rem 0 1rem">
    <app-table-common
        [tableId]="tableId"
        [columns]="columns"
        [data]="state.data"
        [loading]="state.isFetching"
        [funcDelete]="deleteSelected"
        name="Danh sách trạm lỗi"
        [filterTemplate]="filterTemplate"
    >
        <ng-template #filterTemplate>
            <tr>
                <th></th>
                <th></th>
                <th [appFilter]="[tableId, 'id']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="contractId"
                        [rsql]="false"
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            rsql: false,
                            url: '/smart-qc/api/contract/combobox',
                            paramForm: 'id',
                        }"
                        placeholder="Tên dự án"
                        (onChange)="changeFilter($event, 'contractId')"
                    ></app-filter-table>
                </th>

                <th [appFilter]="[tableId, 'areaName']" style="max-width: 8rem">
                    <app-filter-table
                        [tableId]="tableId"
                        field="areaId"
                        [rsql]="false"
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            filterLocal: true,
                            paramForm: 'id',
                            url: '/smart-qc/api/area/search',
                        }"
                        placeholder="Tỉnh/Tp"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'stationName']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="stationName"
                        [rsql]="false"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'name',
                            fieldLabel: 'name',
                            rsql: false,
                            param: 'name',
                            url: '/smart-qc/api/station/combobox',
                            body: {
                                contractIds: bodyFilter.contractId ? [bodyFilter.contractId] : null,
                            },
                        }"
                        placeholder="Trạm"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'stationName']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="stationCode"
                        [rsql]="false"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'name',
                            fieldLabel: 'name',
                            rsql: false,
                            param: 'name',
                            url: '/smart-qc/api/station/combobox',
                            body: {
                                contractIds: bodyFilter.contractId ? [bodyFilter.contractId] : null,
                            },
                        }"
                        placeholder="Trạm"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'subPm']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="subPmId"
                        [rsql]="false"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'fullNameAndEmail',
                            rsql: false,
                            url: '/smart-qc/api/combobox/user',
                            body: {
                                privileges: ['ROLE_QC_SUBPM'],
                                contractId: bodyFilter.contractId,
                            },
                        }"
                        placeholder="SubPM"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'hw']" style="width: 8rem">
                    <app-filter-table [tableId]="tableId" [rsql]="false" field="hw"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'createDate']" style="max-width: 8rem">
                    <app-filter-table
                        [tableId]="tableId"
                        [rsql]="false"
                        field="createDateStart&createDateEnd"
                        type="date-range"
                        placeholder="Ngày tạo"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'doneDate']" style="max-width: 8rem">
                    <app-filter-table
                        [tableId]="tableId"
                        [rsql]="false"
                        field="doneDateStart&doneDateEnd"
                        type="date-range"
                        placeholder="Ngày xử lý xong"
                    ></app-filter-table>
                </th>

                <th [appFilter]="[tableId, 'backTimes']" style="width: 8rem"></th>
                <th [appFilter]="[tableId, 'processDetail']" style="width: 8rem">
                    <app-filter-table [tableId]="tableId" [rsql]="false" field="processDetail"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'note']" style="width: 8rem">
                    <app-filter-table [tableId]="tableId" [rsql]="false" field="note"></app-filter-table>
                </th>
            </tr>
        </ng-template>
        <ng-template #templateAction let-rowData>
            <span class="tw-py-1 tw-px-2 bg-blue-400 text-white tw-rounded cursor-pointer" (click)="handleShowPopup(rowData)">
                <i class="pi pi-pen-to-square" style="font-size: 1rem"></i>
            </span>
        </ng-template>
    </app-table-common>
</div>

<app-maintenance-edit
    *ngIf="isVisible"
    [isVisible]="isVisible"
    [oldId]="oldId"
    [stationId]="stationId"
    (onClose)="handleClosePopup()"
    (onSuccess)="state.refetch()"
></app-maintenance-edit>
