import { APP_INITIALIZER, NgModule } from '@angular/core';
import { AppComponent } from './app.component';
import { AppRoutingModule } from './app-routing.module';
import { CoreModule } from './core/core.module';
import { NgxLoadingModule, ngxLoadingAnimationTypes } from 'ngx-loading';
import { LoadingBarRouterModule } from '@ngx-loading-bar/router';
import { environment } from '../environments/environment';
import { AngularFireModule } from '@angular/fire/compat';
import { AuthService } from './core/auth/auth.service';
import { ToastModule } from 'primeng/toast';
import { ConfirmDialogModule } from 'primeng/confirmdialog';

export function initializeApp(authService: AuthService): () => Promise<void> {
    return () =>
        authService
            .setCurrentUser()
            .toPromise()
            .then(() => undefined);
}

@NgModule({
    declarations: [AppComponent],
    imports: [
        AppRoutingModule,
        CoreModule,
        NgxLoadingModule.forRoot({
            animationType: ngxLoadingAnimationTypes.circle,
            backdropBackgroundColour: 'rgba(0,0,0,0.5)',
            primaryColour: '#ffffff',
            secondaryColour: '#ccc',
            tertiaryColour: '#ffffff',
            fullScreenBackdrop: true,
        }),
        LoadingBarRouterModule,
        ToastModule,
        ConfirmDialogModule,
        AngularFireModule.initializeApp(environment.firebaseConfig),
    ],
    bootstrap: [AppComponent],
    providers: [
        {
            provide: APP_INITIALIZER,
            useFactory: initializeApp,
            deps: [AuthService],
            multi: true,
        },
    ],
})
export class AppModule {}
