import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { ShippingMethod } from '../../../models/interface/sc';

@Injectable()
export class ShippingMethodService extends BaseService<ShippingMethod> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/shipping-method');
    }
}
