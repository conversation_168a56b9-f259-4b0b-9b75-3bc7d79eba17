import { canAuthorize } from '../../../core/auth/auth.guard';

export const POReportRouting = {
    path: 'po-report',
    title: 'Báo cáo thông tin mua hàng',
    canActivate: [canAuthorize],
    data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_report_po_view'] },
    children: [
        {
            path: 'summary',
            title: 'Báo cáo tổng hợp ',
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_report_po_view'] },
            canActivate: [canAuthorize],
            loadComponent: () => import('./summary-report/summary-report.component').then((c) => c.SummaryReportComponent),
        },
        {
            path: 'information-retrieval',
            title: 'T<PERSON>y xuất thông tin mua hàng',
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_report_po_view'] },
            canActivate: [canAuthorize],
            loadComponent: () => import('./information-retrieval/information-retrieval.component').then((c) => c.InformationRetrievalComponent),
        },
    ],
};
