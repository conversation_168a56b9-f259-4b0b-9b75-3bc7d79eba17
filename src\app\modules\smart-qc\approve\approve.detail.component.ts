import { CommonModule } from '@angular/common';
import { Component, ElementRef, HostListener, OnInit, ViewChild, inject } from '@angular/core';
import { ActivatedRoute, Router, RouterLink, RouterModule } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { TreeModule, TreeNodeSelectEvent } from 'primeng/tree';
import { AuthService } from 'src/app/core/auth/auth.service';
import {
    ApproveDetailDTO,
    ApproveLog,
    ErrorSmartQC,
    ResponseApprove,
    TaskDetail,
    TreeCheckListDTO,
} from 'src/app/models/interface/smart-qc';
import { ApproveStatus, CheckListType, listApproveStatus } from 'src/app/models/constant/smart-qc';
import { ApproveService } from 'src/app/services/smart-qc/masterdata/approve.service';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { TableCommonService } from 'src/app/shared/table-module/table.common.service';
import { ImageModule } from 'primeng/image';
import { AccordionModule } from 'primeng/accordion';
import { PanelModule } from 'primeng/panel';
import { ConfirmationService, TreeNode } from 'primeng/api';
import { DividerModule } from 'primeng/divider';
import { AlertService } from 'src/app/shared/services/alert.service';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { CheckboxModule } from 'primeng/checkbox';
import { InputTextModule } from 'primeng/inputtext';
import { FormsModule } from '@angular/forms';
import { FileUploadModule } from 'primeng/fileupload';
import { DropdownModule } from 'primeng/dropdown';
import { InputNumberModule } from 'primeng/inputnumber';
import { RadioButtonModule } from 'primeng/radiobutton';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { forkJoin } from 'rxjs';
import { isArray, isEmpty } from 'lodash';
import { TagModule } from 'primeng/tag';
import { ImageMagnifierComponent } from 'src/app/shared/components/magnifier/image.magnifier.component';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { JsonParsePipe } from 'src/app/shared/pipes/jsonParse.pipe';
import { MultiSelectChangeEvent, MultiSelectModule } from 'primeng/multiselect';
import { ErrorSmartQCService } from 'src/app/services/smart-qc/masterdata/error.service';
import { ApproveLogService } from 'src/app/services/smart-qc/masterdata/approve-log.service';

@Component({
    selector: 'app-approve-detail',
    standalone: true,
    imports: [
        CommonModule,
        RouterModule,
        RouterLink,
        ButtonModule,
        TreeModule,
        ImageModule,
        AccordionModule,
        PanelModule,
        DividerModule,
        CheckboxModule,
        InputTextModule,
        FormsModule,
        FileUploadModule,
        DropdownModule,
        InputNumberModule,
        RadioButtonModule,
        InputTextareaModule,
        TagModule,
        HasAnyAuthorityDirective,
        ImageMagnifierComponent,
        SubHeaderComponent,
        JsonParsePipe,
        MultiSelectModule,
    ],
    templateUrl: './approve.detail.component.html',
    styleUrls: ['./approve.component.scss'],
    providers: [ApproveService, JsonParsePipe, ApproveLogService],
})
export class ApproveDetailComponent implements OnInit {
    @ViewChild('treeDom') treeDom: ElementRef;
    @ViewChild('approveChecklistDetail') approveChecklistDetail!: ElementRef;

    CheckListType = CheckListType;
    taskId: number;
    contractId: number;
    optionStatus = listApproveStatus;
    ApproveStatus = ApproveStatus;
    approveService = inject(ApproveService);
    tableCommonService = inject(TableCommonService);
    authService = inject(AuthService);
    loadingService = inject(LoadingService);
    route = inject(ActivatedRoute);
    router = inject(Router);
    alertService = inject(AlertService);
    confirmationService = inject(ConfirmationService);
    errorSmartQCService = inject(ErrorSmartQCService);
    approveLogService = inject(ApproveLogService);

    itemsHeader = [{ label: 'Quản lý phê duyệt', url: '/sqc/approve' }, { label: 'Chi tiết' }];
    approveDetail: ApproveDetailDTO;
    selectedCheckList: TreeNode = null;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    selectedNode: any = {
        data: [],
        response: {},
    };
    note: string = '';

    isAllGood: boolean = false;
    isAnyBad: boolean = false;

    /**
     * Tạo model để lưu lại thông tin chỉnh sửa
     */

    objectTaskDetail: Record<string, TaskDetail> = {};

    /**
     * Tạo model để lưu lại thông tin chỉnh sửa
     */

    objectTaskResponse: Record<string, ResponseApprove> = {};

    collapsedInfor: boolean = false;
    heightPanelChecklist: number = 600;

    optionsError: ErrorSmartQC[] = [];

    errorIdsSelect: number[] = [];

    // lịch sử
    arrayApproveLog: ApproveLog[] = [];

    ngOnInit() {
        this.route.paramMap.subscribe((params) => {
            this.taskId = params.get('id') ? Number(params.get('id')) : null;
            this.contractId = params.get('contractId') ? Number(params.get('contractId')) : null;
            this.loadingService.show();
            forkJoin([
                this.approveService.getOne(this.taskId),
                this.errorSmartQCService.getPage(`query=contractId==${this.contractId}&size=1000&page=0`),
            ]).subscribe({
                next: ([approveResponse, errorResponse]) => this.handleResponses(approveResponse, errorResponse.body),
                error: () => {
                    this.loadingService.hide();
                    this.router.navigate(['/sqc/approve']);
                },
                complete: () => {
                    this.loadingService.hide();
                },
            });

            this.approveLogService.getPage(`query=taskId==${this.taskId}&page=0&size=100&order=id,desc`).subscribe({
                next: (res) => {
                    this.arrayApproveLog = res.body;
                },
            });
        });
    }

    private handleResponses(approveResponse: ApproveDetailDTO, errorResponse: ErrorSmartQC[]): void {
        if (!approveResponse) return;

        this.approveDetail = approveResponse;
        this.note = approveResponse.note || null;

        if (isArray(this.approveDetail.treeCheckList) && this.approveDetail.treeCheckList.length > 0) {
            const firstNode = this.approveDetail.treeCheckList[0];
            this.expandRecursive(firstNode, true);
            this.setSelectedNode(firstNode);
        }

        this.optionsError = this.mapErrorOptions(errorResponse);
        this.initIconLevel1();
    }

    private setSelectedNode(node: TreeCheckListDTO): void {
        let selectedNode = null;

        if (node.levelChild === 3) {
            selectedNode = node;
        } else if (node.levelChild === 2 && node.children?.length > 0) {
            selectedNode = node.children[0];
        }

        this.selectedNode = selectedNode;
        this.selectedCheckList = selectedNode;

        if (selectedNode?.data) {
            this.populateTaskDetails(selectedNode.data);
            this.populateTaskResponses(selectedNode.key, selectedNode.response);
        }
    }

    private populateTaskDetails(data: TaskDetail[]): void {
        for (const item of data) {
            const taskDetail = item;
            if (!this.objectTaskDetail[taskDetail.id]) {
                this.objectTaskDetail[taskDetail.id] = taskDetail;
            }
        }
    }

    private populateTaskResponses(key: string, response: ResponseApprove): void {
        if (!this.objectTaskResponse[key]) {
            this.objectTaskResponse[key] = response;
        }

        this.errorIdsSelect = isArray(response?.responseErrors)
            ? response.responseErrors.map((item) => item.errorId)
            : [];
    }

    private mapErrorOptions(errors: ErrorSmartQC[]) {
        return errors.map((item) => ({
            ...item,
            displayName: `${item.name || 'Unknown'} (${item.level || 'Unknown'})`,
        }));
    }
    autoResize(event: Event): void {
        const textarea = event.target as HTMLTextAreaElement;
        textarea.style.height = 'auto';
        //const newHeight = Math.min(textarea.scrollHeight, 60);
        const newHeight = textarea.scrollHeight + 1000;
        textarea.style.height = `${newHeight}px`;
    }

    private expandRecursive(node: TreeNode, isExpand: boolean) {
        node.expanded = isExpand;
        if (node.children) {
            node.children.forEach((childNode) => {
                this.expandRecursive(childNode, isExpand);
            });
        }
    }

    nodeSelect(event: TreeNodeSelectEvent) {
        if (
            this.objectTaskResponse[this.selectedNode.key]?.rate === 0 &&
            (!this.objectTaskResponse[this.selectedNode.key]?.pmToSubPm ||
                this.objectTaskResponse[this.selectedNode.key]?.pmToSubPm.trim().length === 0)
        ) {
            this.alertService.warning('Cảnh báo', 'Bạn cần nhập đủ thông tin phản hồi PM -> SubPM');
            return;
        }

        //document.getElementById('scroll-focus-point').scrollIntoView();
        this.approveChecklistDetail.nativeElement.scrollTo({
            top: 0,
            behavior: 'smooth',
        });

        const node: TreeCheckListDTO = event.node as TreeCheckListDTO;
        if (node.level === 3 || !node?.levelChild || node?.levelChild === 2) return;
        this.selectedNode = node;

        if (node && isArray(node.data)) {
            for (const item of node.data) {
                const taskDetail = item as TaskDetail;
                if (!this.objectTaskDetail[taskDetail.id]) {
                    this.objectTaskDetail[taskDetail.id] = taskDetail;
                }
            }
            if (!this.objectTaskResponse[node.key]) {
                this.objectTaskResponse[node.key] = node.response;
            }
            this.errorIdsSelect = isArray(this.objectTaskResponse[node.key]?.responseErrors)
                ? this.objectTaskResponse[node.key].responseErrors.map((item) => item.errorId)
                : [];
        }
    }

    rate(response: ResponseApprove) {
        let canBreakLevel2 = false;

        for (let index = 0; index < this.approveDetail.treeCheckList.length; index++) {
            if (canBreakLevel2) break;

            if (
                this.approveDetail.treeCheckList[index].response.checkListHeaderId === response.checkListHeaderId &&
                this.approveDetail.treeCheckList[index].response.checkListSubId === response.checkListSubId
            ) {
                this.approveDetail.treeCheckList[index].icon =
                    response.rate === 0 ? 'pi pi-times text-red-500' : 'pi pi-check text-teal-500';
                this.approveDetail.treeCheckList[index].response.rate = response.rate;

                break;
            }
            if (this.approveDetail.treeCheckList[index].levelChild !== 3) {
                for (let jndex = 0; jndex < this.approveDetail.treeCheckList[index].children.length; jndex++) {
                    if (
                        this.approveDetail.treeCheckList[index].children[jndex].response.checkListHeaderId ===
                            response.checkListHeaderId &&
                        this.approveDetail.treeCheckList[index].children[jndex].response.checkListSubId ===
                            response.checkListSubId
                    ) {
                        this.approveDetail.treeCheckList[index].children[jndex].icon =
                            response.rate === 0 ? 'pi pi-times text-red-500' : 'pi pi-check text-teal-500';

                        this.approveDetail.treeCheckList[index].children[jndex].response.rate = response.rate;
                        canBreakLevel2 = true;
                        break;
                    }
                }
            }
        }

        this.initIconLevel1();
    }

    initIconLevel1() {
        if (!this.approveDetail.treeCheckList || this.approveDetail.treeCheckList.length === 0) return;
        let countGood = 0;
        let countBad = 0;
        for (let index = 0; index < this.approveDetail.treeCheckList.length; index++) {
            let allGood = 1;
            if (this.approveDetail.treeCheckList[index].levelChild === 3) {
                if (this.approveDetail.treeCheckList[index].response.rate === 1) {
                    this.approveDetail.treeCheckList[index].icon = 'pi pi-check text-teal-500';
                    countGood++;
                } else if (this.approveDetail.treeCheckList[index].response.rate === 0) {
                    this.approveDetail.treeCheckList[index].icon = 'pi pi-times text-red-500';
                    countBad++;
                }

                continue;
            }

            for (let jndex = 0; jndex < this.approveDetail.treeCheckList[index].children.length; jndex++) {
                if (this.approveDetail.treeCheckList[index].children[jndex].response === null) {
                    this.approveDetail.treeCheckList[index].children[jndex].response.taskId = this.taskId;
                }

                if (this.approveDetail.treeCheckList[index].children[jndex].response.rate === 0) {
                    allGood = 0;
                    break;
                }
                if (this.approveDetail.treeCheckList[index].children[jndex].response.rate === 1) {
                    allGood = 1;
                }
                if (!this.approveDetail.treeCheckList[index].children[jndex].response.rate) {
                    allGood = -1;
                    break;
                }
            }
            if (allGood === 1) {
                this.approveDetail.treeCheckList[index].icon = 'pi pi-check text-teal-500';
                countGood++;
            }
            if (allGood === 0) {
                this.approveDetail.treeCheckList[index].icon = 'pi pi-times text-red-500';
                countBad++;
            }
        }

        if (countGood === this.approveDetail.treeCheckList.length) {
            this.isAllGood = true;
        }
        if (countBad > 0) {
            this.isAllGood = false;
            this.isAnyBad = true;
        } else {
            this.isAnyBad = false;
        }
    }

    getSeverity(state: number): string {
        switch (state) {
            case 4:
                return 'success';
            case 3:
                return 'danger';
            case 2:
                return 'primary';
            case 1:
                return 'danger';
            case 0:
                return 'warning';
            default:
                return 'info';
        }
    }

    getStateText(state: number): string {
        switch (state) {
            case 4:
                return 'Hoàn thành';
            case 3:
                return 'Từ chối';
            case 2:
                return 'Chờ duyệt';
            default:
                return '';
        }
    }

    approve(type: string) {
        if (
            this.objectTaskResponse[this.selectedNode.key]?.rate === 0 &&
            (!this.objectTaskResponse[this.selectedNode.key]?.pmToSubPm ||
                this.objectTaskResponse[this.selectedNode.key]?.pmToSubPm.trim().length === 0)
        ) {
            this.alertService.warning('Cảnh báo', 'Bạn cần nhập đủ thông tin phản hồi PM -> SubPM');
            return;
        }

        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message:
                type === 'accept'
                    ? 'Bạn có chắc chắn chấp nhận kết quả của trạm này không'
                    : 'Bạn có chắc chắn muốn từ chối kết quả này không',
            icon: 'pi pi-exclamation-triangle',
            acceptIcon: 'none',
            rejectIcon: 'none',
            rejectButtonStyleClass: 'p-button-text',
            acceptButtonStyleClass: 'p-button-outlined',
            acceptLabel: 'Đồng ý',
            rejectLabel: 'Hủy',
            accept: () => {
                // Lấy danh sách giá trị từ objectTaskDetail
                const taskDetails: TaskDetail[] = Object.values(this.objectTaskDetail);

                // Lấy danh sách giá trị từ objectTaskResponse
                const responseApproves: ResponseApprove[] = Object.values(this.objectTaskResponse);

                this.loadingService.show();
                this.approveService
                    .approveByPm(this.taskId, {
                        taskDetails,
                        responseApproves,
                        isAccept: type === 'accept',
                        note: isEmpty(this.note) ? null : this.note,
                    })
                    .subscribe({
                        next: (res) => {
                            this.alertService.success(
                                'Thành công',
                                type === 'accept' ? 'Phê duyệt công việc thành công' : 'Từ chối công việc thành công',
                            );
                            this.approveDetail.state = res['data']['task']['state'];
                            this.loadingService.hide();
                        },
                        error: (e) => {
                            this.loadingService.hide();
                            this.alertService.handleError(e);
                        },
                    });
            },
        });
    }

    isSticky = false;

    @HostListener('window:scroll', ['$event'])
    checkScroll() {
        const divOffset = this.treeDom.nativeElement.getBoundingClientRect().top;
        if (divOffset <= 50) {
            this.isSticky = true;
        } else {
            this.isSticky = false;
        }
    }

    isEmptyResponse(value) {
        return isEmpty(value);
    }

    collapsedChange(collaped: boolean) {
        this.collapsedInfor = collaped;
        this.caculateHeightPanel();
    }

    caculateHeightPanel() {
        if (!this.collapsedInfor) {
            this.heightPanelChecklist = 600;
            return;
        }

        const documentHeight = document.documentElement.clientHeight;
        const remToPx = 16; // 1 rem = 16px
        const remToSubtract = 10;

        const newHeight = documentHeight - remToSubtract * remToPx;
        if (newHeight < 600) {
            this.heightPanelChecklist = 600;
        } else {
            this.heightPanelChecklist = newHeight;
        }
    }

    onSelectErrorChange(event: MultiSelectChangeEvent): void {
        const selectedNodeKey = this.selectedNode?.key;

        if (!selectedNodeKey) {
            console.warn('Selected node key is undefined.');
            return;
        }

        // Clear responseErrors if no value is selected
        if (!event.value || (isArray(event.value) && event.value.length === 0)) {
            this.objectTaskResponse[selectedNodeKey].responseErrors = [];
            return;
        }

        // Handle adding a new responseError when an item is deselected
        if (isArray(event.value) && event.value.length > 0) {
            const { originalEvent } = event;
            const isDeselected = !originalEvent?.['selected'];
            const option = originalEvent?.['option'] as ErrorSmartQC;

            if (isDeselected) {
                const existingResponseErrors = isArray(this.objectTaskResponse[selectedNodeKey].responseErrors)
                    ? this.objectTaskResponse[selectedNodeKey].responseErrors
                    : [];

                const newResponseError = {
                    taskId: this.taskId,
                    errorId: option['id'],
                };

                this.objectTaskResponse[selectedNodeKey].responseErrors = [...existingResponseErrors, newResponseError];
            } else {
                this.objectTaskResponse[selectedNodeKey].responseErrors = this.objectTaskResponse[
                    selectedNodeKey
                ].responseErrors.filter((item) => item.errorId !== option.id);
            }
        }
    }

    getDisplayError(id: number) {
        return this.optionsError.find((item) => item.id === id)['displayName'];
    }
}
