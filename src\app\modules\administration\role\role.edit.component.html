<style>
    :host ::ng-deep .btn-action > .p-button {
        width: 100%;
    }
</style>

<app-sub-header
    [items]="[{ label: 'Quản trị hệ thống' }, { label: '<PERSON>ai trò', url: '/administration/role' }, { label: role?.name ?? 'Tạo mới' }]"
    [action]="actionHeader"
></app-sub-header>

<ng-template #actionHeader>
    <p-button label="Lưu" severity="success" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN']" (click)="onSave()" />
    <p-button routerLink="/administration/role" label="Hủy" severity="secondary" />
</ng-template>
<div style="padding: 1rem 1rem 0 1rem">
    <app-form #formCreate [formGroup]="formGroup" layout="vertical" (onSubmit)="onSubmit($event)">
        <p-panel header="Thông tin chung" [toggleable]="true">
            <div class="tw-grid lg:tw-grid-cols-2 tw-grid-cols-1 tw-gap-4">
                <app-form-item label="Tên">
                    <input type="text" class="tw-w-full" pInputText formControlName="name" />
                </app-form-item>

                <app-form-item label="Mô tả">
                    <input type="text" class="tw-w-full" pInputText formControlName="description" />
                </app-form-item>
                <ng-container *ngIf="roleId">
                    <app-form-item label="Ngày tạo">
                        <input type="text" class="tw-w-full" pInputText formControlName="createdText" />
                    </app-form-item>

                    <app-form-item label="Ngày cập nhật">
                        <input type="text" class="tw-w-full" pInputText formControlName="updatedText" />
                    </app-form-item>
                    <app-form-item label="Người tạo">
                        <input type="text" class="tw-w-full" pInputText formControlName="createdBy" />
                    </app-form-item>

                    <app-form-item label="Người cập nhật">
                        <input type="text" class="tw-w-full" pInputText formControlName="updatedBy" />
                    </app-form-item>
                </ng-container>
            </div>
        </p-panel>
        <br />
        <div class="tw-flex tw-flex-row tw-gap-8">
            <div class="tw-flex-grow bg-white tw-rounded-md tw-p-4">
                <h5>Các quyền có sẵn</h5>
                <div class="tw-flex tw-flex-row tw-gap-4">
                    <input
                        type="text"
                        class="tw-w-full"
                        placeholder="Lọc kết quả"
                        pInputText
                        (input)="onSearchLeftChange($event.target.value)"
                        formControlName="searchLeft"
                    />
                    <p-button icon="pi pi-filter-slash" severity="secondary" (click)="onClear('left')"></p-button>
                </div>
                <br />
                <p-tree
                    [value]="treeLeft"
                    selectionMode="checkbox"
                    [(selection)]="selectedLeftNode"
                    (selectionChange)="handleSelectNode($event, 'left')"
                    scrollHeight="600px"
                ></p-tree>
            </div>
            <div class="tw-flex tw-flex-col tw-gap-5 tw-p-8 tw-bg-white tw-rounded-md tw-items-center tw-h-fit" style="width: 250px">
                <p-button label=">" class="btn-action tw-w-28" (click)="addPriv()" [disabled]="selectedLeftNode.length === 0"></p-button>
                <p-button label=">>" class="btn-action tw-w-28" (click)="addAllPriv()"></p-button>
                <p-button class="btn-action tw-w-28" label="<" severity="danger" (click)="removePriv()" [disabled]="selectedRightNode.length === 0"></p-button>
                <p-button class="btn-action tw-w-28" label="<<" severity="danger" (click)="removeAllPriv()"></p-button>
            </div>
            <div class="tw-flex-grow bg-white tw-rounded-md tw-p-4">
                <h5>Các quyền đã chọn</h5>
                <div class="tw-flex tw-flex-row tw-gap-4">
                    <input
                        type="text"
                        class="tw-w-full"
                        pInputText
                        placeholder="Lọc kết quả"
                        (input)="onSearchRightChange($event.target.value)"
                        formControlName="searchRight"
                    />
                    <p-button icon="pi pi-filter-slash" severity="secondary" (click)="onClear('right')"></p-button>
                </div>
                <br />

                <p-tree
                    [value]="treeRight"
                    [(selection)]="selectedRightNode"
                    (selectionChange)="handleSelectNode($event, 'right')"
                    selectionMode="checkbox"
                    scrollHeight="600px"
                ></p-tree>
            </div>
        </div>
    </app-form>
</div>
