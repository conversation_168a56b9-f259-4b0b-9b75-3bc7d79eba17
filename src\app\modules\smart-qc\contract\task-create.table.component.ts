/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { CommonModule } from '@angular/common';
import { Component, Input, OnChanges, SimpleChanges, ViewChild, inject } from '@angular/core';
import { NgSelectComponent, NgSelectModule } from '@ng-select/ng-select';
import { CalendarModule } from 'primeng/calendar';
import { PaginatorModule } from 'primeng/paginator';
import { TableModule } from 'primeng/table';
import { CheckboxModule } from 'primeng/checkbox';
import { InputCBBComponent } from 'src/app/shared/components/form-input/input.cbb.component';
import { AuthService } from 'src/app/core/auth/auth.service';
import { ActionService } from 'src/app/services/smart-qc/masterdata/action.service';
import { ConfirmationService, MessageService } from 'primeng/api';
import { DialogModule } from 'primeng/dialog';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { FileService } from 'src/app/shared/services/file.service';
import { environment } from 'src/environments/environment';
import { ButtonModule } from 'primeng/button';
import { AlertService } from 'src/app/shared/services/alert.service';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { isDateTypeValidator, timeSpamValidator, trimValidator } from 'src/app/shared/directives/time.span.validator';
import Common from 'src/app/utils/common';

@Component({
    selector: 'app-task-table',
    standalone: true,
    templateUrl: './task.table.component.html',
    imports: [
        CommonModule,
        TableModule,
        PaginatorModule,
        CalendarModule,
        NgSelectModule,
        CheckboxModule,
        InputCBBComponent,
        DialogModule,
        ReactiveFormsModule,
        ButtonModule,
        InputTextModule,
    ],
    providers: [FileService],
    host: { 'binding-value': 'value' },
})
export class TaskCreateTableComponent implements OnChanges {
    templatePM = environment.HOST_GW + '/smart-qc/api/sample?fileName=Import-DanhSachTram-PM.xlsx';
    templateDelete = environment.HOST_GW + '/smart-qc/api/sample?fileName=Template_Delete_Station.xlsx';
    @Input('areaList') areaList = [];
    @Input('districtList') districtList = [];
    @Input('userList') userList = [];
    @Input('tasks') tasks = [];
    @Input('canOpenEditForm') canOpenEditForm: boolean;
    // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
    @Input('setTask') setTask: Function;
    @Input('contractId') contractId;
    @Input('actionId') actionId;
    @Input('templateId') templateId;

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    selectedTasks: any[] = [];
    displayTasks = [];
    comboboxArea = [];
    comboboxDistrict = [];
    searchObject: {
        areaId: number;
        districtId: number;
        subPMId: number;
        stationName: string;
        stationCode: string;
    } = {
        areaId: null,
        districtId: null,
        subPMId: null,
        stationName: null,
        stationCode: null,
    };
    pageable = {
        size: 5,
        page: 0,
        first: 0,
    };
    taskForm;
    visiblemFileForm;
    visibleTaskForm;
    visibleImportForm;
    visbleFileDelete;
    isNewTask;
    selectedTask;
    tableLoading = false;
    totalTask: number = 0;
    isPMOrAdmin = false;
    isPM = false;
    isSubPM = false;
    @ViewChild('areaCbb') areaCbb: NgSelectComponent;
    @ViewChild('districtCbb') districtCbb: NgSelectComponent;
    @ViewChild('stationNameInput') stationNameInput: Input;
    @ViewChild('stationCodeInput') stationCodeInput: Input;

    constructor(
        public authService: AuthService,
        private actionService: ActionService,
        private messageService: MessageService,
        private formBuilder: FormBuilder,
        private confirmationService: ConfirmationService,
        private alertService: AlertService,
        private loadingService: LoadingService,
    ) {
        this.isPMOrAdmin = this.authService.isAdminOrPM();
        this.isPM = this.authService.isPM();
        this.isSubPM = this.authService.isSubPM();

        this.taskForm = this.formBuilder.group(
            {
                stationId: [null],
                name: ['', [trimValidator(), Validators.required, Validators.maxLength(255)]],
                code: ['', [trimValidator(), Validators.required, Validators.maxLength(255)]],
                areaId: ['', Validators.required],
                districtId: [null, Validators.required],
                latitude: [null, [trimValidator(), Validators.required, Validators.maxLength(255)]],
                longitude: [null, [trimValidator(), Validators.required, Validators.maxLength(255)]],
                config: [''],
                address: [''],
                manage: [''],
                phone: ['', [trimValidator(), Validators.pattern('^[0-9]*$')]],
                subPMId: [null, Validators.required],
                email: ['', [Validators.maxLength(255), Validators.required]],
                startTime: ['', [isDateTypeValidator()]],
                endTime: ['', [isDateTypeValidator()]],
                realStartTime: { value: '', disabled: true },
                realEndTime: { value: '', disabled: true },
                employeeId: [{ value: '', disabled: !this.authService.isAdminOrPM() }],
                employeeEmail: [{ value: '', disabled: !this.authService.isAdminOrPM() }],
            },
            {
                validators: timeSpamValidator('startTime', 'endTime'),
            },
        );

        this.taskForm.get('areaId').valueChanges.subscribe((value) => {
            this.taskForm.get('districtId').setValue(null);
            if (value) {
                this.taskForm.get('districtId').enable();
            } else {
                this.taskForm.get('districtId').disable();
            }
        });
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['tasks']) {
            this.filter();
            this.totalTask = this.tasks.length;
            const areaId = this.tasks.filter((t) => t.station).map((t) => t.station.areaId);
            const districtIds = this.tasks.filter((t) => t.station).map((t) => t.station.districtId);
            this.comboboxArea = this.areaList.filter((a) => areaId.includes(a.id));
            this.comboboxDistrict = this.districtList.filter((d) => districtIds.includes(d.id));
            this.clearFilter();
        }
    }

    clearFilter() {
        if (this.areaCbb) this.areaCbb.clearModel();
        if (this.districtCbb) this.districtCbb.clearModel();
        this.searchObject = {
            areaId: null,
            districtId: null,
            subPMId: null,
            stationName: null,
            stationCode: null,
        };
    }

    filterArea(station) {
        const area = this.areaList.find((a) => a.id === station?.areaId);
        return area?.name;
    }

    filterDistrict(station) {
        const district = this.districtList.find((d) => d.id === station?.districtId);
        return district?.name;
    }

    filterSubPm(task) {
        const user = this.userList.find((d) => d.id === task?.subPMId);
        return user?.fullName + ' - ' + user?.email;
    }

    public loadTask() {
        this.pageable = {
            size: 5,
            page: 0,
            first: 0,
        };
        this.filter();
    }

    filter() {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        let stationFilterByName: any[] = [-1];
        let stationFilterByCode: any[] = [-1];
        if (this.searchObject.stationName && this.searchObject.stationName !== '') {
            stationFilterByName = this.tasks
                .filter((t) => t.station.name.includes(this.searchObject.stationName))
                .map((t) => t.station);
        }

        if (this.searchObject.stationCode && this.searchObject.stationCode !== '') {
            stationFilterByCode = this.tasks
                .filter((t) => t.station.code.includes(this.searchObject.stationCode))
                .map((t) => t.station);
        }

        this.displayTasks = this.tasks.filter(
            (t) =>
                (!this.searchObject.districtId || t.station.districtId === this.searchObject.districtId) &&
                (!this.searchObject.areaId || t.station.areaId === this.searchObject.areaId) &&
                (!this.searchObject.subPMId || t.subPMId === this.searchObject.subPMId) &&
                (stationFilterByName.includes(-1) || stationFilterByName.some((f) => t.station.code === f.code)) &&
                (stationFilterByCode.includes(-1) || stationFilterByCode.some((f) => t.station.code === f.code)),
        );
    }

    districtListFilterForm;
    setAreaByDistrictForm = (district) => {
        if (district?.areaId) {
            this.districtListFilterForm = this.districtList.filter((d) => d.areaId === district.areaId);

            this.taskForm.patchValue({ areaId: district.areaId });
        }
    };

    filterDistrictByAreaForm = (area) => {
        this.districtListFilterForm = this.districtList.filter((d) => !area || d.areaId === area.id);
    };

    openEditTask = (task, isNewTask) => {
        if (task?.station?.areaId) {
            this.filterDistrictByAreaForm({ id: task.station.areaId });
        } else {
            this.districtListFilterForm = [...this.districtList];
        }

        this.taskForm.reset();

        if (!isNewTask) {
            this.taskForm.patchValue({
                ...task,
                ...task.station,
                startTime: task.startTime ? new Date(task.startTime) : '',
                endTime: task.endTime ? new Date(task.endTime) : '',
                realStartTime: task.realStartTime ? new Date(task.realStartTime) : '',
                realEndTime: task.realEndTime ? new Date(task.realEndTime) : '',
                email: task.subPmEmail,
            });
        }
        this.selectedTask = task;
        this.visibleTaskForm = true;
        this.isNewTask = isNewTask;
    };

    setAssigneeCallBack = (e) => {
        this.taskForm.patchValue({ subPMId: e?.id, email: e?.email });
    };

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    searchStation(e) {
        this.actionService.searchStationByCode(this.taskForm.getRawValue().code).subscribe((data) => {
            if (data) {
                this.taskForm.patchValue({ ...data, stationId: data.id });
                this.filterDistrictByAreaForm({ id: data.areaId });
            } else {
                this.taskForm.patchValue({ stationId: null });
                const station = this.sationList.find((s) => s.code === this.taskForm.getRawValue().code);
                if (station) {
                    this.taskForm.patchValue({ ...station });
                }
            }
        });
    }

    fileUpload;
    errorFileUrl: string;

    onSelectFile = (e) => {
        const file = e.target.files[0];
        e.target.value = null;
        if (!file.type?.includes('vnd.ms-excel') && !file.type?.includes('spreadsheetml')) {
            this.alertService.error('Lỗi import', 'File import sai định dạng, vui lòng thử lại với file excel');
            this.fileUpload = null;
        } else {
            this.fileUpload = file;
        }
    };

    onUpload() {
        if (!this.fileUpload) {
            this.alertService.error('Lỗi import', 'Vui lòng chọn file cần import');
            return;
        }

        if (!this.fileUpload.type?.includes('vnd.ms-excel') && !this.fileUpload.type?.includes('spreadsheetml')) {
            this.alertService.error('Lỗi import', 'File import sai định dạng, vui lòng thử lại với file excel');
            return;
        }

        if (this.visibleImportForm) {
            this.actionService
                .importTask(this.fileUpload, null, null, this.templateId, this.authService.isAdminOrPM())
                .subscribe({
                    next: (res) => {
                        const data = res['data'];
                        this.tasks = [
                            ...this.tasks.filter((t) => !data.some((d) => d.station.code === t.station.code)),
                            ...data,
                        ];
                        this.setTask(this.tasks);

                        if (res['code'] === 0) {
                            if (res['message'] === 'Failed') {
                                this.alertService.error('Lỗi!', 'File import không đúng mẫu, vui lòng kiểm tra lại');
                                return;
                            }
                            this.alertService.error('Lỗi!', 'Dữ liệu trong file không hợp lệ');
                            this.errorFileUrl =
                                environment.HOST_GW + '/smart-qc/api/download?filePath=' + res['message'];
                        } else {
                            this.alertService.success('Thành công', 'Import danh sách trạm xong');
                            this.errorFileUrl = null;
                            this.visibleImportForm = false;
                            this.visiblemFileForm = false;
                            this.visiblemFileForm = false;
                            this.fileUpload = null;
                        }
                    },
                });
        }
    }
    private fileService = inject(FileService);

    downLoadTemplate = () => {
        this.fileService.downLoadFile({ url: '' });
    };

    deleteTask = (task) => {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận xóa trạm',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.tasks = this.tasks.filter((t, i) => t !== task);
                this.setTask(this.tasks);
                this.alertService.success('Thành công', 'Xóa trạm thành công');
            },
        });
    };

    onSelectionChange(value = []) {
        this.selectedTasks = [...value];
    }

    deleteMany() {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận xóa trạm',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.tasks = this.tasks.filter((t) => !this.selectedTasks.includes(t));
                this.setTask(this.tasks);
                this.alertService.success('Thành công', 'Xóa trạm thành công');
            },
        });
    }

    sationList: any[] = [];

    saveTask() {
        if (!this.taskForm.valid) return;

        const formValue = this.taskForm.getRawValue();
        if (this.tasks.some((t) => t !== this.selectedTask && t.station?.code === formValue.code)) {
            this.messageService.add({
                key: 'app-alert',
                severity: 'error',
                summary: 'Lỗi thêm trạm',
                detail: 'Trạm đã tạo trong công việc hiện tại',
            });
            return;
        }
        if (formValue.stationId) {
            this.messageService.add({
                key: 'app-alert',
                severity: 'error',
                summary: 'Có lỗi xảy ra',
                detail: 'Mã trạm đã tồn tại ở dự án khác',
            });
            return;
        }

        const task = {
            ...this.selectedTask,
            stationId: formValue.stationId,
            station: {
                id: formValue.stationId,
                name: formValue.name,
                code: formValue.code,
                latitude: formValue.latitude,
                longitude: formValue.longitude,
                config: formValue.config,
                address: formValue.address,
                areaId: formValue.areaId,
                districtId: formValue.districtId,
                areaName: this.filterArea(formValue),
                districtName: this.filterDistrict(formValue),
            },
            templateId: this.templateId,
            manage: formValue.manage,
            phone: formValue.phone,
            subPMId: formValue.subPMId,
            subPm: this.filterSubPm(formValue),
            subPmEmail: formValue.email,
            startTime: formValue.startTime,
            endTime: formValue.endTime,
            realStartTime: formValue.realStartTime instanceof Date ? formValue.startTime.getTime() : '',
            realEndTime: formValue.realEndTime instanceof Date ? formValue.endTime.getTime() : '',
        };

        this.sationList = Common.mergeArray([task.station], this.sationList, (a, b) => a.code === b.code);

        setTimeout(() => {
            if (this.isNewTask) {
                this.tasks = [...this.tasks, task];
                this.displayTasks = [...this.tasks];
            } else {
                this.tasks = this.tasks.map((tsk) => (this.selectedTask === tsk ? task : tsk));
            }

            this.setTask(this.tasks);
        });

        this.visibleTaskForm = false;
    }

    logEvent(e) {}

    downLoadTemplateImport() {
        if (!this.visibleImportForm) {
            this.fileService.downLoadFile({ url: this.templateDelete, fileName: '' });
            return;
        }

        this.actionService.getFileImport(this.contractId, this.actionId).subscribe({
            next: (res) => {
                this.fileService.downLoadFile(res);
            },
        });
    }
}
