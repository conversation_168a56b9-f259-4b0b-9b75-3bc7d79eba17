import { CommonModule } from '@angular/common';
import {
    AfterViewInit,
    Component,
    ElementRef,
    HostListener,
    OnInit,
    QueryList,
    TemplateRef,
    ViewChild,
    ViewChildren,
} from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import {ActivatedRoute, Router, RouterLink} from '@angular/router';
import { ConfirmationService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { TableModule } from 'primeng/table';
import { AlertService } from 'src/app/shared/services/alert.service';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { FileService } from 'src/app/shared/services/file.service';
import { SupplierTypeService } from 'src/app/services/sc/supplier/suppiler-type.service';

import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { PanelModule } from 'primeng/panel';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { DialogModule } from 'primeng/dialog';
import { QueryObserverBaseResult } from '@tanstack/query-core';

import { TooltipModule } from 'primeng/tooltip';
import { CalendarModule } from 'primeng/calendar';
import { Calendar } from 'primeng/calendar';
import { ButtonGroupFileComponent } from 'src/app/shared/components/button-group-file/button-group-file.component';
import { CriteriaSupplierService } from 'src/app/services/sc/supplier/criteria-supplier.service';
import { CriteriaBuy, CriteriaBuySupplier, CriteriaBuySupplierDetail, Supplier } from 'src/app/models/interface/sc';
import { Operators } from 'src/app/models/constant/sc';
import { CriteriaBuyService } from 'src/app/services/sc/evaluate/criteria-buy.service';
import { CriteriaBuySupplierService } from 'src/app/services/sc/evaluate/criteria-buy-supplier.service';
import { TableCommonService } from 'src/app/shared/table-module/table.common.service';
import { SupplierInforService } from 'src/app/services/sc/supplier/suppiler-infor.service';
import { DateUtils } from 'src/app/utils/date-utils';

@Component({
    selector: 'app-supplier-evaluate-edit',
    templateUrl: './criteria-buy.edit.component.html',
    styleUrls: ['./criteria-buy.component.scss'],
    standalone: true,
    imports: [
        CommonModule,
        PanelModule,
        ReactiveFormsModule,
        ButtonModule,
        InputTextModule,
        InputTextareaModule,
        DropdownModule,
        SubHeaderComponent,
        TableModule,
        TableCommonModule,
        FormCustomModule,
        DialogModule,
        ButtonGroupFileComponent,
        FormsModule,
        TooltipModule,
        CalendarModule,
        RouterLink,
    ],
    providers: [SupplierTypeService, CriteriaSupplierService],
    styles: [
        `
            :host ::ng-deep .p-dropdown {
                width: 100%;
            }
        `,
    ],
})
export class CriteriaBuyEditComponent implements OnInit, AfterViewInit {
    @ViewChild('templateSupplierType') templateSupplierType: TemplateRef<Element>;

    mapSupplierType: { [key: number]: string } = {};
    isCreateEditing: boolean = false;
    isCreateAdding: boolean = false;
    isCreate: boolean = false;
    isLoadingFirstData = true;

    // Criteria buy
    criteriaBuyForm: FormGroup;
    criteriaBuy: CriteriaBuy;

    // Supplier
    urlError: string;
    urlErrorSupplier: Map<number, string> = new Map<number, string>();

    // Popup add supplier
    stateSupplier: QueryObserverBaseResult<Supplier[]>;
    tableIdSupplier: string = 'tableSelectSupplierId';
    columnsSupplierImportTable = [];
    isOpenModalAddSupplier: boolean = false;
    numberSuppliersSelected: number = 0;
    selectedSuppliers: Supplier[] = [];
    selectedSuppliersTemp: Supplier[] = [];
    typesAccept = ['excel'];
    // End popup

    // Note criteria
    @ViewChild('criteriaNoteTextarea', { static: false }) criteriaNoteTextarea!: ElementRef;
    isEditingCriteriaNote: boolean = false;
    criteriaOriginNote: string;
    // End note criteria

    // Note
    @ViewChildren('noteInput') noteInputs!: QueryList<ElementRef>;
    editingNoteItemId: number | null = null;
    originalNote: Record<number, string> = {}; // Lưu giá trị gốc trước khi chỉnh sửa
    // End note

    // Date
    @ViewChildren(Calendar) calendars!: QueryList<Calendar>;
    @ViewChildren('dateInput') dateInputs!: QueryList<ElementRef>;
    editingDateItemId: number | null = null;
    originalDateTimestamp: number | null = null;
    itemEditing: CriteriaBuySupplier;
    editingDate: Date | null = null;
    // End date

    // Result detail
    isOpenModalViewResult: boolean = false;
    resultsDetail: CriteriaBuySupplierDetail[];
    // Edn result detail

    operators = Operators;
    DateUtils = DateUtils;
    constructor(
        private route: ActivatedRoute,
        private router: Router,
        private loadingService: LoadingService,
        private formBuilder: FormBuilder,
        private alertService: AlertService,
        private confirmationService: ConfirmationService,
        private supplierTypeService: SupplierTypeService,
        private criteriaBuyService: CriteriaBuyService,
        private criteriaBuySupplierService: CriteriaBuySupplierService,
        private criteriaSupplierService: CriteriaSupplierService,
        private fileService: FileService,
        private tableCommonService: TableCommonService,
        private supplierService: SupplierInforService,
    ) {
        this.initForm();
    }

    ngAfterViewInit(): void {
        this.columnsSupplierImportTable = [
            { field: 'name', default: true, header: 'Tên đầy đủ' },
            { field: 'shortName', header: 'Tên viết tắt' },
            { field: 'code', header: 'ID' },
            { field: 'supplierTypeId', header: 'Loại hình NCC', body: this.templateSupplierType },
            {
                header: 'Hàng hóa cung cấp',
                field: 'productSupplied',
            },
        ];
    }
    ngOnInit(): void {
        this.loadData();
        this.loadSupplierTable();
    }

    initForm(): void {
        this.criteriaBuyForm = this.formBuilder.group({
            code: [{ value: null, disabled: true }],
            createBy: [null, []],
            createDate: [null, []],
            note: [null, []],
        });
    }

    loadData = () => {
        if (this.route.snapshot.paramMap.get('id') !== null) {
            // Edit
            this.loadDataCriteriaBuy();
        } else {
            // New
            this.isCreate = true;
            this.isLoadingFirstData = false;
        }
    };

    loadDataCriteriaBuy() {
        this.loadingService.show();
        const id = Number(this.route.snapshot.paramMap.get('id'));
        this.isCreate = false;
        this.criteriaBuyService.getOne(id).subscribe({
            next: (res) => {
                this.loadingService.hide();
                this.criteriaBuy = res.body;
                // Check result
                const suppliers = [];
                this.criteriaBuy.criteriaBuySuppliers.forEach((criteriaBuySupplier) => {
                    criteriaBuySupplier.result = !criteriaBuySupplier.criteriaBuySupplierDetails?.some(
                        (detail) => detail.result === false,
                    );
                    suppliers.push({ id: criteriaBuySupplier.supplierId });
                });
                this.selectedSuppliers = suppliers;
                this.updateFilter();
                this.loadingService.hide();
                this.isLoadingFirstData = false;
            },
            error: () => {
                this.loadingService.hide();
                this.alertService.error('Lỗi', 'Không thể truy cập thông tin đánh giá trước mua');
                this.router.navigate(['./..']);
                this.loadingService.hide();
                this.isLoadingFirstData = false;
            },
        });
    }

    removeSupplier(index: number): void {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                if (this.isCreate) {
                    this.selectedSuppliers.splice(index, 1);
                    this.updateFilter();
                } else {
                    this.deleteCriteriaSupplier(this.criteriaBuy.criteriaBuySuppliers[index]);
                }
            },
        });
    }

    deleteCriteriaSupplier(criteriaBuySupplier: CriteriaBuySupplier) {
        this.loadingService.show();
        this.criteriaBuySupplierService.delete(criteriaBuySupplier.id).subscribe({
            next: () => {
                this.alertService.success('Thành công', 'Xóa nhà cung cấp thành công');
                this.loadingService.hide();
                this.loadDataCriteriaBuy();
            },
            error: () => {
                this.alertService.error('Lỗi', 'Có lỗi xảy ra');
                this.loadingService.hide();
            },
        });
    }

    loadSupplierTable() {
        this.tableCommonService
            .init<Supplier>({
                tableId: this.tableIdSupplier,
                queryFn: (filter) => this.supplierService.getPageTableCustom(filter),
                configFilterRSQL: {
                    name: 'Text',
                    shortName: 'Text',
                    code: 'Text',
                    supplierTypeId: 'SetLong',
                    id: 'SetLongNotIn',
                    deletedAt: 'IsNull',
                },
                defaultParamsRSQL: {
                    deletedAt: true,
                },
            })
            .subscribe({
                next: (res) => {
                    this.stateSupplier = res;
                    // this.loadingService.hide();
                },
                error: (error) => {
                    this.alertService.handleError(error);
                },
            });

        this.tableCommonService.getRowSelect(this.tableIdSupplier).subscribe((res: Supplier[]) => {
            this.selectedSuppliersTemp = res;
            this.numberSuppliersSelected = res.length;
        });

        this.supplierTypeService.advancedGroup({}).subscribe({
            next: (response) => {
                response.body.filter((item) => {
                    this.mapSupplierType[item.id] = item.displayName;
                });
            },
            error: (error) => {
                this.alertService.handleError(error);
            },
        });
    }

    selectSupplier() {
        this.isOpenModalAddSupplier = false;
        if (this.isCreate) {
            this.selectedSuppliers = [...this.selectedSuppliers, ...this.selectedSuppliersTemp];
            //this.mappingSupplierToCriteriaBuySupplier(this.selectedSuppliersTemp);
            this.updateFilter();
        } else {
            // Add supplier buy to supplier buy
            this.loadingService.show();
            const supplierIds = this.selectedSuppliersTemp.map((supplier) => supplier.id);
            this.criteriaBuySupplierService.createSimple(supplierIds, this.criteriaBuy.id).subscribe({
                next: () => {
                    this.loadDataCriteriaBuy();
                    this.loadingService.hide();
                    this.alertService.success('Thành công', 'Thêm nhà cung cấp thành công');
                },
                error: () => {
                    this.loadingService.hide();
                    this.alertService.error('Lỗi', 'Có lỗi xảy ra');
                },
            });
        }
    }

    deleteSupplier = (ids: number[]) => {
        return this.supplierService.batchDelete(ids);
    };

    getSupplierType(supplierTypeId: number) {
        return this.mapSupplierType[supplierTypeId];
    }

    updateFilter() {
        const supplierIdsRemove = this.selectedSuppliers?.map((supplier) => supplier.id) || [];
        this.tableCommonService.updateFilterRSQL(this.tableIdSupplier, {
            id: supplierIdsRemove,
        });
        // Update selected
        this.tableCommonService.updateRowSelect(this.tableIdSupplier, []);
    }

    showDetailResult(criteriaBuySupplier: CriteriaBuySupplier) {
        this.isOpenModalViewResult = true;
        this.resultsDetail = criteriaBuySupplier.criteriaBuySupplierDetails;
    }

    handleSelectFile(file: File, criteriaBuySupplier: CriteriaBuySupplier) {
        this.loadingService.show();
        this.criteriaBuySupplierService.importCriteriaBuy(file, criteriaBuySupplier.id).subscribe({
            next: (res) => {
                if (res.code === 1) {
                    //criteriaBuySupplier.evaluationDoc = res.attachment;
                    this.alertService.success('Import phiếu đánh giá thành công');
                    /*// Check result
                    const criteriaBuySupplierDetails = res.data as unknown as CriteriaBuySupplierDetail[];
                    criteriaBuySupplier.criteriaBuySupplierDetails = criteriaBuySupplierDetails;
                    criteriaBuySupplier.result = !criteriaBuySupplierDetails.some(detail => detail.result === false);*/

                    // Call api get
                    this.loadDataCriteriaBuy();
                } else {
                    this.urlError = res.message;
                    this.urlErrorSupplier.set(criteriaBuySupplier.id, res.message);
                    this.alertService.error('Import phiếu đánh giá thất bại');
                }
                this.loadingService.hide();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    handleClearFile(criteriaBuySupplier: CriteriaBuySupplier) {
        criteriaBuySupplier.evaluationDoc = null;
    }

    handleDownloadFile(supplierTypeId: number) {
        this.criteriaBuySupplierService.exportTemplateCriteria(supplierTypeId).subscribe({
            next: (res) => {
                if (res.url !== null) {
                    this.fileService.downLoadFileByService(res.url, '/sc/api');
                }
            },
            error: () => {},
        });
    }

    close() {
        this.router.navigate(['../'], { relativeTo: this.route });
    }

    createCriteriaBuy() {
        if (!this.criteriaBuyForm.valid) return;
        if (this.selectedSuppliers.length <= 0) {
            this.alertService.error('Lỗi', 'Vui lòng chọn ít nhất một NCC');
            return;
        }

        const criteriaBuyData = this.criteriaBuyForm.getRawValue();
        criteriaBuyData.supplierList = this.selectedSuppliers;
        // Call api create
        this.loadingService.show();
        this.criteriaBuyService.create(criteriaBuyData).subscribe({
            next: (res) => {
                this.alertService.success('Thành công', 'Tạo đánh giá trước mua thành công');
                this.loadingService.hide();
                const criteriaBuySupplier: CriteriaBuy = res.body as unknown as CriteriaBuySupplier;
                this.router.navigate(['/sc/criteria-buy/' + criteriaBuySupplier.id]);
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    updateNote(itemId: number, event: Event) {
        const inputElement = event.target as HTMLInputElement;
        const item = this.criteriaBuy.criteriaBuySuppliers.find((i) => i.id === itemId);
        if (item) {
            item.note = inputElement.value;
        }
    }

    saveNote(criteriaBuySupplier: CriteriaBuySupplier) {
        if (!criteriaBuySupplier.note) {
            criteriaBuySupplier.note = '';
        }
        if (criteriaBuySupplier.id !== null && criteriaBuySupplier.note !== this.originalNote[criteriaBuySupplier.id]) {
            this.criteriaBuySupplierService.update(criteriaBuySupplier).subscribe({
                next: () => {
                    this.alertService.success('Thành công', 'Lưu ghi chú thành công');
                    this.editingNoteItemId = null;
                },
                error: () => {
                    this.alertService.error('Lỗi', 'Có lỗi xảy ra');
                    this.editingNoteItemId = null;
                },
            });
        } else {
            this.editingNoteItemId = null;
        }
    }
    startEditing(item) {
        this.editingNoteItemId = item.id;
        this.originalNote[item.id] = item.note ?? '';
        setTimeout(() => {
            const input = document.getElementById(`note-${item.id}`) as HTMLInputElement;
            input?.focus();
        }, 0);
    }
    isSelectingDate: boolean = false;

    startEditingDate(item: CriteriaBuySupplier) {
        this.editingDateItemId = item.id;
        this.isSelectingDate = false;
        this.originalDateTimestamp = item.evaluateDate ? item.evaluateDate : null;
        this.itemEditing = item;
        this.editingDate = item.evaluateDate ? new Date(item.evaluateDate) : null;
        setTimeout(() => this.focusDateInput(item.id), 0);
    }

    focusDateInput(itemId: number) {
        const dateInput = this.dateInputs.find((input) => input.nativeElement?.id === `date-${itemId}`);
        if (dateInput) {
            dateInput.nativeElement.focus();
        }
    }

    onSelectDate(date: Date, item) {
        this.isSelectingDate = true;
        item.evaluateDate = DateUtils.convertToTimestampHCM(date);
        setTimeout(() => {
            this.isSelectingDate = false;
        }, 200);
    }

    @HostListener('document:click', ['$event'])
    handleClickOutside(event: Event) {
        if (this.isSelectingDate) return;
        const isClickInside = this.calendars.some((c) => c.el.nativeElement.contains(event.target as Node));
        if (!isClickInside) {
            this.editingDateItemId = null;
            // Save date
            if (this.itemEditing && this.itemEditing.evaluateDate !== this.originalDateTimestamp) {
                this.saveDate(this.itemEditing);
            }
        }
    }

    saveDate(item) {
        this.loadingService.show();
        this.criteriaBuySupplierService.update(item).subscribe({
            next: () => {
                this.alertService.success('Thành công', 'Lưu ngày đánh giá thành công');
                this.loadingService.hide();
                this.itemEditing = null;
            },
            error: () => {
                this.alertService.error('Lỗi', 'Có lỗi xảy ra');
                this.loadingService.hide();
                this.itemEditing = null;
            },
        });
    }

    startEditCriteriaNote() {
        this.isEditingCriteriaNote = true;
        this.criteriaOriginNote = this.criteriaBuy.note;
    }

    onBlurCriteriaNote() {
        this.isEditingCriteriaNote = false;
        if (this.criteriaBuy.note !== this.criteriaOriginNote) {
            this.saveCriteriaNote();
        }
    }

    saveCriteriaNote() {
        this.loadingService.show();
        this.criteriaBuyService.update(this.criteriaBuy).subscribe({
            next: () => {
                this.alertService.success('Thành công', 'Lưu ghi chú thành công');
                this.isEditingCriteriaNote = false;
                this.loadingService.hide();
            },
            error: () => {
                this.alertService.error('Lỗi', 'Có lỗi xảy ra');
                this.isEditingCriteriaNote = false;
                this.loadingService.hide();
            },
        });
    }
}
