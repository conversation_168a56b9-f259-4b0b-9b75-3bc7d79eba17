<app-skeleton-loading [isLoading]="isLoading">
    <p><b>Trung bình : </b> {{ lineSupplierQuality?.averagePoint ?? 'Không có thông tin' }}</p>
    <p><b>Phân hạng : </b> {{ lineSupplierQuality?.averageRate ?? 'Không có thông tin' }}</p>

    <p-table styleClass="p-datatable-gridlines" [value]="[lineSupplierQuality]">
        <ng-template pTemplate="header">
            <tr>
                <th>Tháng</th>
                <th *ngFor="let label of lineSupplierQuality?.labels">{{ label }}</th>
            </tr>
        </ng-template>

        <ng-template pTemplate="body" let-supplierQuality>
            <tr>
                <td><PERSON>i<PERSON><PERSON> số</td>
                <td *ngFor="let point of lineSupplierQuality?.points">{{ point }}</td>
            </tr>
            <tr>
                <td><PERSON><PERSON> hạng</td>
                <td *ngFor="let rate of lineSupplierQuality?.rates">{{ rate }}</td>
            </tr>
        </ng-template>
    </p-table>

    <div class="tw-mt-8">
        <canvas #templateChartLine style="height: 500px"></canvas>
    </div>
</app-skeleton-loading>
