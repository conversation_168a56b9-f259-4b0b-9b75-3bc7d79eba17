// file-upload-manager.service.ts
import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

interface UploadEntry {
    cancel$: () => void;
}

@Injectable({ providedIn: 'root' })
export class FileUploadManagerService {
    private uploads = new Map<string, UploadEntry>();
    private _isUploading$ = new BehaviorSubject<boolean>(false);
    isUploading$ = this._isUploading$.asObservable();

    start(uploadId: string, cancelFn: () => void) {
        this.uploads.set(uploadId, { cancel$: cancelFn });
        this.updateStatus();
    }

    finish(uploadId: string) {
        if (!this.uploads.has(uploadId)) return;
        this.uploads.delete(uploadId);
        this.updateStatus();
    }

    cancel(uploadId: string) {
        const entry = this.uploads.get(uploadId);
        if (!entry) return;
        entry.cancel$();
        this.uploads.delete(uploadId);
        this.updateStatus();
    }

    private updateStatus() {
        this._isUploading$.next(this.uploads.size > 0);
    }
}
