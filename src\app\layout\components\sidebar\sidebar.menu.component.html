<aside
    *ngIf="sidebar && sidebar.items.length > 0"
    class="tw-h-full border-right-1 tw-border-slate-200 tw-flex tw-flex-col"
    style="background-color: var(--secondary-color)"
>
    <div
        class="tw-flex tw-flex-nowrap tw-items-center border-bottom-1 tw-border-slate-200"
        [ngClass]="{
            'tw-justify-between': sidebar.state === 'expand',
            'tw-justify-center': sidebar.state === 'collapse',
        }"
        style="height: 48px; padding: 12px"
    >
        <div
            [ngClass]="{ 'tw-hidden': sidebar.state === 'collapse' }"
            class="tw-text-base tw-font-semibold tw-overflow-hidden tw-text-ellipsis tw-text-nowrap"
            routerLink="/dashboard"
        >
            {{ module.title }}
        </div>
        <button
            class="tw-flex tw-items-center tw-justify-center tw-rounded-full tw-border-0 tw-outline-none tw-cursor-pointer"
            style="width: 22px; height: 22px; background-color: var(--background-color)"
            [ngClass]="{
                'rotate-180': sidebar.state === 'collapse',
            }"
            (click)="onButtonClick($event)"
        >
            <i class="pi pi-angle-left tw-cursor-pointer"></i>
        </button>
    </div>
    <ul class="menu_item-level-1 tw-flex-1 tw-overflow-y-auto">
        <ng-container *ngFor="let item of sidebar.items">
            <li>
                <app-menuitem app-menuitem [item]="item" [level]="1"></app-menuitem>
            </li>
        </ng-container>
    </ul>
</aside>
