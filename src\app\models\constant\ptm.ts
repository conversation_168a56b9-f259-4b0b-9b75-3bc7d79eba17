export const SYMB<PERSON>_TYPE = [
    { value: 1, label: 'Operation' },
    { value: 2, label: 'Inspection' },
    { value: 3, label: 'Transportation' },
    { value: 4, label: 'Delay' },
    { value: 5, label: 'Storage' },
    { value: 6, label: 'Rework' },
];

export const PRODUCT_PROCESS_TYPE = [
    { value: 1, label: 'Man' },
    { value: 2, label: 'Machine' },
    { value: 3, label: 'Man/Machine' },
];

export const MANBOM_PROCESS_TYPE = [
    { value: 1, label: 'SMTTOP' },
    { value: 2, label: 'SMTBOT' },
    { value: 3, label: 'DIP' },
    { value: 4, label: 'PCBA' },
    { value: 5, label: 'ASM' },
    { value: 6, label: 'TEST' },
    { value: 7, label: 'PKG' },
];

export const LINE_PFMEA = [
    { value: 0, label: 'SMTTOP' },
    { value: 1, label: 'SMTBOT' },
    { value: 2, label: 'DIP' },
    { value: 3, label: 'PCBA' },
    { value: 4, label: 'ASM' },
    { value: 5, label: 'FINAL TEST' },
    { value: 6, label: 'PKG' },
    { value: 7, label: 'SD' },
    { value: 8, label: 'FA' },
];

export const MANBOM_MATERIAL_TYPE = [
    { value: 1, label: 'Vật tư chính' },
    { value: 2, label: 'Vật tư phụ' },
    { value: 3, label: 'Vật tư tiêu hao' },
];

export const SECTION_BOR = [
    { value: 1, label: 'SMTTOP' },
    { value: 2, label: 'SMTBOT' },
    { value: 3, label: 'DIP' },
    { value: 4, label: 'PCBA' },
    { value: 5, label: 'ASM' },
    { value: 6, label: 'FINAL' },
    { value: 7, label: 'TEST' },
    { value: 8, label: 'PKG' },
    { value: 9, label: 'SD' },
    { value: 10, label: 'FA' },
];

export const UNIT_BOR = [
    { value: 1, label: 'Machine.h/ SP' },
    { value: 2, label: 'Line.h/ SP' },
];

export const TAB_TYPE = {
    processFlow: 0,
    routing: 1,
    pfmea: 2,
    workStandard: 3,
    tcc: 4,
    manbom: 5,
    bor: 6,
    sopBase: 7,
    sopApplication: 8,
    CLDocs: 9,
    otherDocs: 10,
};

export const STATUS_TRANSFER_MAP: Record<number, string> = {
    1: 'Draft',
    2: 'Waiting confirm',
    4: 'Denied',
    8: 'Confirmed',
    16: 'Waiting confirm',
};
