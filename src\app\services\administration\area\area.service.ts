import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ParamsTable } from '../../../shared/table-module/table.common.service';
import { Contract } from '../../../models/interface/smart-qc';
import { Area } from '../../../models/interface';
import {BaseService} from "../../base.service";

@Injectable()
export class AreaService extends BaseService<Area>{
    constructor(protected override http: HttpClient) {
        super(http, '/smart-qc/api/area');
    }

    getAll() {
        return this.http.get<Area[]>('/smart-qc/api/area/search?query=&page=0&size=1000');
    }
}
