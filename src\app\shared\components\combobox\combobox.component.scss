.combobox-container {
  position: relative;
  display: inline-block;
  width: 100%;
  border: 1px solid #ccc;
  border-radius: 4px;
  background: #fff;

  &.disabled {
    background: #f4f4f4;
    cursor: not-allowed;
  }

  &.selected {
    .combobox-input.hidden {
      visibility: hidden;
    }
  }

  .combobox-input {
    width: 100%;
    border: none;
    outline: none;
    padding: 8px;
    font-size: 14px;
    background: transparent;
  }

  .selected-label {
    position: absolute;
    top: 0;
    left: 8px;
    right: 30px; /* Space for clear button */
    padding: 8px;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .clear-button {
    position: absolute;
    top: 50%;
    right: 8px;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    font-size: 14px;
    color: #666;

    &:hover {
      color: #000;
    }

    .pi-times {
      font-size: 12px;
    }
  }

  .dropdown {
    position: fixed;
    background: #fff;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;

    .dropdown-search {
      padding: 8px;
      border-bottom: 1px solid #eee;

      .dropdown-search-input {
        width: 100%;
        border: none;
        outline: none;
        padding: 4px;
        font-size: 14px;
        background: #f9f9f9;
        border-radius: 2px;
      }
    }

    ul {
      list-style: none;
      margin: 0;
      padding: 0;

      li {
        padding: 8px;
        cursor: pointer;

        &.active,
        &:hover {
          background: #f0f0f0;
        }

        &.selected {
          background: #e0e0e0;
          font-weight: bold;
        }
      }
    }
  }
}