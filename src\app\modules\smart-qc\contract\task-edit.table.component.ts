import { CommonModule } from '@angular/common';
import { Component, Input, OnChanges, SimpleChanges, ViewChild } from '@angular/core';
import { NgSelectComponent, NgSelectModule } from '@ng-select/ng-select';
import { CalendarModule } from 'primeng/calendar';
import { PaginatorModule } from 'primeng/paginator';
import { TableModule } from 'primeng/table';
import { CheckboxModule } from 'primeng/checkbox';
import { InputCBBComponent } from 'src/app/shared/components/form-input/input.cbb.component';
import { AuthService } from 'src/app/core/auth/auth.service';
import { ActionService } from 'src/app/services/smart-qc/masterdata/action.service';
import { ConfirmationService, MessageService } from 'primeng/api';
import { DialogModule } from 'primeng/dialog';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { FileService } from 'src/app/shared/services/file.service';
import { environment } from 'src/environments/environment';
import { ButtonModule } from 'primeng/button';
import { AlertService } from 'src/app/shared/services/alert.service';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { isDateTypeValidator, timeSpamValidator, trimValidator } from 'src/app/shared/directives/time.span.validator';
import { Task } from 'src/app/models/interface/smart-qc';
import {Area, District} from "../../../models/interface";


@Component({
    selector: 'app-task-table',
    standalone: true,
    templateUrl: './task.table.component.html',
    imports: [
        CommonModule,
        TableModule,
        PaginatorModule,
        CalendarModule,
        NgSelectModule,
        CheckboxModule,
        InputCBBComponent,
        DialogModule,
        ReactiveFormsModule,
        ButtonModule,
        InputTextModule,
    ],
    providers: [FileService],
    host: { 'some-binding': 'some-value' },
})
export class TaskEditTableComponent implements OnChanges {
    templatePM = environment.HOST_GW + '/smart-qc/api/sample?fileName=Import-DanhSachTram-PM.xlsx';

    templateDelete = environment.HOST_GW + '/smart-qc/api/sample?fileName=Template_Delete_Station.xlsx';
    @Input('areaList') areaList = [];
    @Input('districtList') districtList = [];
    @Input('userList') userList = [];
    @Input('tasks') tasks = [];
    @Input('canOpenEditForm') canOpenEditForm: boolean;
    // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
    @Input('setTask') setTask: Function;
    @Input('contractId') contractId;
    @Input('actionId') actionId;
    @Input('templateId') templateId;

    selectedTasks: Task[] = [];
    displayTasks = [];
    comboboxArea: Area[] = [];
    comboboxDistrict: District[] = [];
    searchObject: {
        areaId: number;
        districtId: number;
        employeeId: number;
        subPMId: number;
        stationName: string;
        stationCode: string;
    } = {
        areaId: null,
        districtId: null,
        employeeId: null,
        subPMId: null,
        stationName: null,
        stationCode: null,
    };
    pageable = {
        size: 5,
        page: 0,
        first: 0,
    };
    taskForm;
    visiblemFileForm;
    visibleTaskForm;
    visibleImportForm;
    visbleFileDelete;
    isNewTask;
    selectedTask;
    tableLoading = false;
    totalTask: number = 0;
    isPMOrAdmin = false;
    isPM = false;
    isSubPM = false;
    @ViewChild('areaCbb') areaCbb: NgSelectComponent;
    @ViewChild('districtCbb') districtCbb: NgSelectComponent;
    @ViewChild('stationNameInput') stationNameInput: Input;
    @ViewChild('stationCodeInput') stationCodeInput: Input;

    constructor(
        public authService: AuthService,
        private actionService: ActionService,
        private messageService: MessageService,
        private formBuilder: FormBuilder,
        private confirmationService: ConfirmationService,
        private alertService: AlertService,
        private loadingService: LoadingService,
        private fileService: FileService,
    ) {
        this.isPMOrAdmin = this.authService.isAdminOrPM();
        this.isPM = this.authService.isPM();
        this.isSubPM = this.authService.isSubPM();
        this.searchObject.subPMId = this.isSubPM ? this.authService.getPrinciple().id : null;

        this.taskForm = this.formBuilder.group(
            {
                id: [null],
                stationId: [null],
                name: [
                    { value: '', disabled: !this.isPMOrAdmin },
                    [trimValidator(), Validators.required, Validators.maxLength(255)],
                ],
                code: [
                    { value: '', disabled: !this.isPMOrAdmin },
                    [trimValidator(), Validators.required, Validators.maxLength(255)],
                ],
                areaId: [{ value: '', disabled: !this.isPMOrAdmin }, Validators.required],
                districtId: [{ value: '', disabled: !this.isPMOrAdmin }, Validators.required],
                state: [null],
                latitude: [
                    { value: '', disabled: !this.isPMOrAdmin },
                    [trimValidator(), Validators.required, Validators.maxLength(255)],
                ],
                longitude: [
                    { value: '', disabled: !this.isPMOrAdmin },
                    [trimValidator(), Validators.required, Validators.maxLength(255)],
                ],
                config: [{ value: '', disabled: !this.isPMOrAdmin }],
                address: [{ value: '', disabled: !this.isPMOrAdmin }],
                manage: [{ value: '', disabled: !this.isPMOrAdmin }],
                phone: [{ value: '', disabled: !this.isPMOrAdmin }, Validators.pattern('^[0-9]*$')],
                subPMId: [{ value: '', disabled: !this.isPMOrAdmin }, Validators.required],
                email: [null, [Validators.maxLength(255), Validators.required]],
                startTime: ['', [isDateTypeValidator()]],
                endTime: ['', [isDateTypeValidator()]],
                realStartTime: { value: '', disabled: true },
                realEndTime: { value: '', disabled: true },
                employeeId: [{ value: '', disabled: this.isPMOrAdmin }, Validators.required],
            },
            {
                validators: timeSpamValidator('startTime', 'endTime'),
            },
        );

        if (this.isPMOrAdmin) {
            this.taskForm.get('areaId').valueChanges.subscribe((value) => {
                this.taskForm.get('districtId').setValue(null);
                if (value) {
                    this.taskForm.get('districtId').enable();
                } else {
                    this.taskForm.get('districtId').disable();
                }
            });
            this.taskForm.get('id').valueChanges.subscribe((value) => {
                if (value) {
                    this.taskForm.get('code').disable();
                } else {
                    this.taskForm.get('code').enable();
                }
            });
            this.taskForm.get('state').valueChanges.subscribe((value) => {
                if (value != null || value != undefined) {
                    this.taskForm.get('subPMId').disable();
                    this.taskForm.get('email').disable();
                } else {
                    this.taskForm.get('subPMId').enable();
                    this.taskForm.get('email').enable();
                }
            });
        }
        if (authService.isSubPM()) {
            this.taskForm.get('state').valueChanges.subscribe((value) => {
                if (value != null || value != undefined) {
                    this.taskForm.get('employeeId').disable();
                    this.taskForm.get('email').disable();
                } else {
                    this.taskForm.get('employeeId').enable();
                    this.taskForm.get('email').enable();
                }
            });
        }
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['actionId']) {
            this.loadTask();
            if (this.actionId) {
                this.actionService.getAreaOptions(this.actionId).subscribe((data) => {
                    this.comboboxArea = data;
                });
                this.actionService.getDistrictOptions(this.actionId, null).subscribe((data) => {
                    this.comboboxDistrict = data;
                });
            }
        }
    }

    public loadTask() {
        if (this.actionId) {
            this.tableLoading = true;
            this.pageable = {
                size: 5,
                page: 0,
                first: 0,
            };
            this.filter();
        }
    }

    clearFilter() {
        if (this.areaCbb) this.areaCbb.clearModel();
        if (this.districtCbb) this.districtCbb.clearModel();
        this.searchObject = {
            areaId: null,
            districtId: null,
            employeeId: null,
            subPMId: null,
            stationName: null,
            stationCode: null,
        };
    }

    filter() {
        const condition =
            (this.searchObject.areaId ? `&areaId=${this.searchObject.areaId}` : '') +
            (this.searchObject.districtId ? `&districtId=${this.searchObject.districtId}` : '') +
            (this.searchObject.stationCode ? `&stationCode=${this.searchObject.stationCode}` : '') +
            (this.searchObject.stationName ? `&stationName=${this.searchObject.stationName}` : '') +
            (this.searchObject.employeeId ? `&employeeId=${this.searchObject.employeeId}` : '') +
            (this.searchObject.subPMId ? `&subPMId=${this.searchObject.subPMId}` : '');

        // return;
        const page = `&page=${this.pageable.page}&size=${this.pageable.size}`;
        this.actionService.searchTask(this.actionId, condition, page).subscribe({
            next: (page) => {
                this.totalTask = page['totalElements'];
                this.displayTasks = page['content'];
                this.tasks = page['content'];
                this.tableLoading = false;
            },
            error: () => {
                this.tableLoading = false;
            },
        });
    }

    districtListFilterForm;
    setAreaByDistrictForm = (district) => {
        if (district?.areaId) {
            this.districtListFilterForm = this.districtList.filter((d) => d.areaId === district.areaId);

            this.taskForm.patchValue({ areaId: district.areaId });
        }
    };

    filterDistrictByAreaForm = (area) => {
        this.districtListFilterForm = this.districtList.filter((d) => !area || d.areaId === area.id);
    };

    openEditTask = (task, isNewTask) => {
        if (task?.station?.areaId) {
            this.filterDistrictByAreaForm({ id: task.station.areaId });
        } else {
            this.districtListFilterForm = [...this.districtList];
        }

        this.taskForm.reset();

        if (!isNewTask) {
            this.taskForm.patchValue({
                ...task,
                ...task.station,
                id: task.id,
                stationId: task.station.id,
                email: this.isPMOrAdmin ? task.subPmEmail : task.employeeEmail,
                startTime: task.startTime ? new Date(task.startTime) : '',
                endTime: task.endTime ? new Date(task.endTime) : '',
                realStartTime: task.realStartTime ? new Date(task.realStartTime) : '',
                realEndTime: task.realEndTime ? new Date(task.realEndTime) : '',
            });
        }
        this.selectedTask = task;
        this.visibleTaskForm = true;
        this.isNewTask = isNewTask;
    };

    setAssigneeCallBack = (e) => {
        if (this.isPMOrAdmin) {
            this.taskForm.patchValue({ subPMId: e?.id, email: e?.email });
        } else {
            this.taskForm.patchValue({ employeeId: e?.id, email: e?.email });
        }
    };

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    searchStation(e) {
        this.actionService.searchStationByCode(this.taskForm.getRawValue().code).subscribe((data) => {
            if (data) {
                this.taskForm.patchValue({ ...data, id: this.taskForm.getRawValue().id, stationId: data.id });
                this.filterDistrictByAreaForm({ id: data.areaId });
            } else {
                this.taskForm.patchValue({ stationId: null });
            }
        });
    }

    fileUpload;
    errorFileUrl: string;

    onSelectFile = (e) => {
        const file = e.target.files[0];
        e.target.value = null;
        if (!file.type?.includes('vnd.ms-excel') && !file.type?.includes('spreadsheetml')) {
            this.alertService.error('Lỗi import', 'File import sai định dạng, vui lòng thử lại với file excel');
            this.fileUpload = null;
        } else {
            this.fileUpload = file;
        }
    };

    onUpload() {
        if (!this.fileUpload) {
            this.alertService.error('Lỗi import', 'Vui lòng chọn file cần import');
            return;
        }

        if (!this.fileUpload.type?.includes('vnd.ms-excel') && !this.fileUpload.type?.includes('spreadsheetml')) {
            this.alertService.error('Lỗi import', 'File import sai định dạng, vui lòng thử lại với file excel');
            return;
        }

        if (this.visibleImportForm) {
            setTimeout(() => {
                this.loadingService.show();
            });
            this.actionService
                .importTask(this.fileUpload, this.contractId, this.actionId, this.templateId, this.isPMOrAdmin)
                .subscribe({
                    next: (res) => {
                        this.loadTask();
                        if (res['code'] === 0) {
                            this.alertService.error('Lỗi!', 'Dữ liệu trong file không hợp lệ');
                            if (res['message'] === 'Failed') {
                                return;
                            }
                            this.errorFileUrl =
                                environment.HOST_GW + '/smart-qc/api/download?filePath=' + res['message'];
                        } else {
                            this.alertService.success('Thành công', 'Import danh sách trạm xong');
                            this.errorFileUrl = null;
                            this.visibleImportForm = false;
                            this.visiblemFileForm = false;
                            this.fileUpload = null;
                        }
                    },
                    error: () => {
                        this.loadTask();
                    },
                });
        } else {
            this.actionService
                .importDeleteTask(this.fileUpload, this.contractId, this.actionId, this.templateId)
                .subscribe({
                    next: () => {
                        this.loadTask();
                        this.visibleImportForm = false;
                        this.visiblemFileForm = false;
                        this.fileUpload = null;
                        this.alertService.success('Thành công', 'Xóa trạm thành công');
                    },
                    error: () => {
                        this.loadTask();
                        this.alertService.error('Thất bại', 'Xóa trạm thất bại');
                    },
                    complete: () => {},
                });
        }
    }

    downLoadTemplate = () => {
        this.actionService.exportTask(this.actionId).subscribe((res) => {
            this.fileService.downLoadFile(res);
        });
    };

    deleteTask = (task) => {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận xóa trạm',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.actionService.deleteTask(task.id).subscribe({
                    next: () => {
                        this.filter();
                        this.alertService.success('Thành công', 'Xóa trạm thành công');
                    },
                    error: () => {
                        this.filter();
                        this.alertService.error('Thất bại', 'Xóa trạm thất bại');
                    },
                });
            },
        });
    };

    onSelectionChange(value = []) {
        this.selectedTasks = [...value];
    }

    deleteMany() {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận xóa trạm',
            message: 'Bạn có chắc chắn muốn xóa' + this.selectedTasks.length + ' trạm',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.actionService.batchDelete(this.selectedTasks.map((t) => t.id)).subscribe({
                    next: () => {
                        this.filter();
                        this.alertService.success('Thành công', 'Xóa trạm thành công');
                    },
                    error: () => {
                        this.filter();
                        this.alertService.error('Thất bại', 'Xóa trạm thất bại');
                    },
                });
            },
        });
    }

    saveTask() {
        if (!this.taskForm.valid) return;

        const formValue = this.taskForm.getRawValue();
        if (this.tasks.some((t) => t !== this.selectedTask && t.station?.code === formValue.code)) {
            this.messageService.add({
                key: 'app-alert',
                severity: 'error',
                summary: 'Lỗi thêm trạm',
                detail: 'Trạm đã tạo trong công việc hiện tại',
            });
            return;
        }

        const task = {
            ...this.selectedTask,
            stationId: formValue.stationId,
            actionId: this.actionId,
            contractId: this.contractId,
            station: {
                id: formValue.stationId,
                name: formValue.name,
                code: formValue.code,
                latitude: formValue.latitude,
                longitude: formValue.longitude,
                config: formValue.config,
                address: formValue.address,
                areaId: formValue.areaId,
                districtId: formValue.districtId,
            },
            templateId: this.templateId,
            manage: formValue.manage,
            phone: formValue.phone,
            subPMId: formValue.subPMId,
            employeeId: formValue.employeeId,
            email: formValue.email,
            startTime: formValue.startTime,
            endTime: formValue.endTime,
        };

        if (this.isNewTask) {
            this.actionService
                .createTask({
                    ...task,
                    startTime: task.startTime instanceof Date ? task.startTime.getTime() : '',
                    endTime: task.endTime instanceof Date ? task.endTime.getTime() : '',
                })
                .subscribe({
                    next: () => {
                        this.loadTask();
                        this.visibleTaskForm = false;
                        this.alertService.success('Thành công', 'Tạo trạm thành công');
                    },
                    error: () => {
                        this.filter();
                    },
                });
        } else {
            this.actionService
                .updateTask({
                    ...task,
                    startTime: task.startTime instanceof Date ? task.startTime.getTime() : '',
                    endTime: task.endTime instanceof Date ? task.endTime.getTime() : '',
                })
                .subscribe({
                    next: () => {
                        this.filter();
                        this.visibleTaskForm = false;
                        this.alertService.success('Thành công', 'Cập nhật trạm thành công');
                    },
                    error: () => {
                        this.filter();
                    },
                });
        }
    }

    logEvent(e) {
        this.pageable.page = e.first / e.rows;
        this.tableLoading = true;
        this.filter();
    }

    downLoadTemplateImport() {
        if (!this.visibleImportForm) {
            this.fileService.downLoadFile({ url: this.templateDelete, fileName: '' });
            return;
        }

        this.actionService.getFileImport(this.contractId, this.actionId).subscribe({
            next: (res) => {
                this.fileService.downLoadFile(res);
            },
        });
    }
}
