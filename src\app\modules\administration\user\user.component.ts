import { After<PERSON><PERSON>w<PERSON>nit, Component, OnInit, TemplateRef, ViewChild, inject } from '@angular/core';
import { BaseUserService } from 'src/app/services/administration/admin/user.service';
import { ButtonModule } from 'primeng/button';
import { TableCommonService } from 'src/app/shared/table-module/table.common.service';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { AuthService } from 'src/app/core/auth/auth.service';
import { TABLE_KEY } from 'src/app/models/constant';
import { Column, Role, User } from 'src/app/models/interface';
import { TagModule } from 'primeng/tag';
import { CommonModule } from '@angular/common';
import { RoleService } from 'src/app/services/administration/admin/role.service';
import { RouterLink } from '@angular/router';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { DialogModule } from 'primeng/dialog';
import { FileService } from 'src/app/shared/services/file.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { environment } from 'src/environments/environment';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { ConfirmationService } from 'primeng/api';
import { isArray } from 'lodash';

@Component({
    selector: 'app-user',
    templateUrl: './user.component.html',
    standalone: true,
    imports: [TableCommonModule, ButtonModule, TagModule, CommonModule, RouterLink, DialogModule, SubHeaderComponent],
    // styleUrl: './user.component.scss'
    providers: [BaseUserService, RoleService],
})
export class UserComponent implements OnInit, AfterViewInit {
    @ViewChild('templateActive') templateActive: TemplateRef<Element>;
    @ViewChild('templateDeparment') templateDeparment: TemplateRef<Element>;
    @ViewChild('templateArea') templateArea: TemplateRef<Element>;
    @ViewChild('templateRole') templateRole: TemplateRef<Element>;
    @ViewChild('templateEmail') templateEmail: TemplateRef<Element>;
    userService = inject(BaseUserService);
    roleService = inject(RoleService);
    loadingService = inject(LoadingService);
    tableCommonService = inject(TableCommonService);
    authService = inject(AuthService);
    fileService = inject(FileService);
    alertService = inject(AlertService);
    state: QueryObserverBaseResult<User[]>;
    confirmationService = inject(ConfirmationService);
    tableId: string = TABLE_KEY.USER;
    itemsHeader = [{ label: 'Quản lý người dùng' }, { label: 'Danh sách người dùng' }];
    columns: Column[] = [];
    roleOption: Role[];
    errorFileUrl: string;
    fileUpload: File;
    visible: boolean = false;
    haveDeletePriv = this.authService.isAdmin();
    template =
        environment.HOST_GW +
        (this.authService.isAdminOrPM()
            ? '/auth/api/template?fileName=TemplateCreateUserPM.xlsx'
            : '/auth/api/template?fileName=TemplateCreateUserSubPM.xlsx');

    idSelects: number[] = [];
    ngOnInit() {
        this.tableCommonService
            .init<User>({
                tableId: this.tableId,
                queryFn: (filter, body) => this.userService.getPage(filter, body),
                defaultParamsRSQL: {
                    tenantId: [0, null],
                    id: this.authService.getPrinciple().id,
                },
                configFilter: ['roleId', 'departmentId', 'areaId'],
                configFilterRSQL: {
                    email: 'Text',
                    fullName: 'Text',
                    phone: 'Text',
                    active: 'TextExact',
                    tenantId: 'NumberRange',
                    id: 'NotEqual',
                },
            })
            .subscribe((state) => {
                this.state = state;
            });
        // if (this.authService.isAdminOrPM) {
        //     this.roleService.getPage('query=qcLevel=in=(1,2,3,4)').subscribe((res) => {
        //         this.roleOption = res.body;
        //     });
        // } else {
        this.roleService.getRoleByUser().subscribe((data) => {
            this.roleOption = data.filter((d) => d.qcLevel);
        });
        // }

        this.tableCommonService.getRowSelect(this.tableId).subscribe((state: User[]) => {
            if (isArray(state)) {
                this.idSelects = state.map((item) => item.id);
            }
        });
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.columns = [
                { field: 'email', header: 'Tài khoản/Email', body: this.templateEmail, default: true },
                { field: 'fullName', header: 'Họ tên' },
                { field: 'phone', header: 'Điện thoại' },
                { field: 'role', header: 'Vai trò', body: this.templateRole },
                {
                    field: 'active',
                    header: 'Tình trạng',
                    body: this.templateActive,
                },
            ];
        }, 0);
    }

    getSeverity(state: boolean): string {
        switch (state) {
            case true:
                return 'success';
            case false:
                return 'danger';
            default:
                return 'warning';
        }
    }

    getStateText(state: boolean): string {
        switch (state) {
            case true:
                return 'Hoạt động';
            case false:
                return 'Ngừng hoạt động';
            default:
                return 'Đăng kí';
        }
    }

    deleteSelectedUser = (ids: number[]) => {
        return this.userService.deleteMany(ids);
    };

    downLoadTemplate() {
        this.loadingService.show();
        this.userService.downloadTemplate().subscribe((res) => {
            this.fileService.downLoadFileByService(res['data'], '/auth/api');
            this.loadingService.hide();
        });
    }

    onSelectFile = (e) => {
        const file = e.target.files[0];
        e.target.value = null;
        if (!file.type?.includes('vnd.ms-excel') && !file.type?.includes('spreadsheetml')) {
            this.alertService.error('Lỗi import', 'File import sai định dạng, vui lòng thử lại với file excel');
            this.fileUpload = null;
        } else {
            this.fileUpload = file;
        }
    };

    clearFileInput(fileInput: HTMLInputElement) {
        this.fileUpload = null;
        fileInput.value = '';
    }

    onUpload(fileInput: HTMLInputElement) {
        if (!this.fileUpload) {
            this.alertService.error('Lỗi import', 'Vui lòng chọn file cần import');
            return;
        }

        if (!this.fileUpload.type?.includes('vnd.ms-excel') && !this.fileUpload.type?.includes('spreadsheetml')) {
            this.alertService.error('Lỗi import', 'File import sai định dạng, vui lòng thử lại với file excel');
            return;
        }

        this.loadingService.show();
        this.userService.importUser(this.fileUpload, this.authService.isAdminOrPM() ? 1 : 2).subscribe({
            next: (res) => {
                if (res['code'] === 0) {
                    this.alertService.error('Lỗi!', 'Dữ liệu trong file không hợp lệ');
                    this.errorFileUrl = environment.HOST_GW + '/auth/api/download?filePath=' + res['data'];
                } else {
                    this.alertService.success('Thành công', 'Import danh sách người dùng xong');
                    this.errorFileUrl = null;
                    this.visible = false;
                    this.clearFileInput(fileInput);
                }
                this.state.refetch();
            },
            complete: () => {
                this.loadingService.hide();
            },
        });
    }

    funcDeactiveUser = () => {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Xác nhận khóa các tài khoản',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.loadingService.show();
                this.userService.deactiveUser(this.idSelects).subscribe({
                    next: () => {
                        this.loadingService.hide();
                        this.state.refetch();
                        this.alertService.success('Thành công', 'Khóa tài khoản thành công');
                        this.tableCommonService.updateRowSelect(this.tableId, []);
                    },
                    error: (e) => {
                        this.loadingService.hide();
                        this.alertService.handleError(e);
                    },
                });
            },
        });
    };
}
