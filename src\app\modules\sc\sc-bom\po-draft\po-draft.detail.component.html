<app-sub-header
    [items]="[
        { label: 'Quản lý thông tin mua hàng', url: '/sc/bom' },
        { label: 'Quản lý đơn nháp', url: '/sc/bom' },
        { label: poDraftOld?.code ? poDraftOld?.code : 'Thêm mới' },
    ]"
    [action]="action"
></app-sub-header>

<ng-template #action>
    <!--<p-button label="Lưu" (click)="null" severity="success" size="small" />-->
    <p-button label="Đóng" routerLink="/sc/bom" [queryParams]="{ tabIndex: 1 }" severity="secondary" size="small" />
</ng-template>

<div class="tw-p-4">
    <p-panel header="Thông tin SC BOM" [toggleable]="true">
        <app-form #form [formGroup]="poDraftForm" layout="vertical" (onSubmit)="confirmPoDraft()">
            <div class="tw-grid lg:tw-grid-cols-2 tw-grid-cols-1 tw-gap-4">
                <app-form-item label="Chọn danh sách SCBOM để tạo PO">
                    <app-combobox
                        fieldValue="id"
                        fieldLabel="code"
                        rsql="true"
                        url="/sc/api/bom/search"
                        formControlName="scBomIds"
                        additionalCondition="status==0"
                    >
                    </app-combobox>
                </app-form-item>

                <div>
                    <label style="line-height: 2rem">
                        <b>Ghi chú</b>
                    </label>
                    <app-editable-input
                        [control]="poDraftForm.get('note')"
                        type="input"
                        placeholder="Ghi chú"
                        [trim]="true"
                        (save)="saveNote()"
                        fieldName="ghi chú"
                    >
                    </app-editable-input>
                </div>
            </div>
        </app-form>
        <br />
        <div class="tw-flex tw-justify-end tw-space-x-3">
            <p-button
                [disabled]="!isEnableConfirmButton"
                label="Xác nhận"
                severity="primary"
                (click)="form.handleSubmit()"
                size="small"
                *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_bom_edit']"
            >
            </p-button>
        </div>
    </p-panel>
    <br />
    <p-panel [toggleable]="true">
        <ng-template pTemplate="header">
            <div class="tw-flex tw-justify-between tw-items-center tw-w-full">
                <span class="tw-font-bold">Danh sách đơn mua</span>
                <span class="tw-ml-auto tw-mr-5">
                    <p-button
                        label="Phê duyệt"
                        [disabled]="!(selectedPos?.length > 0)"
                        severity="primary"
                        (click)="approvePoDraft()"
                        size="small"
                        *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_bom_edit']"
                    >
                    </p-button>
                </span>
            </div>
        </ng-template>
        <div>
            <p-table
                [value]="poItems?.controls"
                styleClass="p-datatable-gridlines"
                [scrollable]="true"
                scrollHeight="700px"
                [resizableColumns]="true"
                [(selection)]="selectedPos"
            >
                <ng-template pTemplate="header">
                    <tr>
                        <th>
                            <p-tableHeaderCheckbox *ngIf="hasDraftPo"></p-tableHeaderCheckbox>
                        </th>
                        <th>Số đơn hàng</th>
                        <th>Nhà cung cấp dự kiến</th>
                        <th>Tổng giá trị đơn hàng</th>
                        <th>Đồng tiền (USD/VNĐ)</th>
                        <th>BOQ</th>
                        <th>Trạng thái</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-po>
                    <tr>
                        <td>
                            <p-tableCheckbox [value]="po" *ngIf="po.get('state')?.value === PO_STATE_CONSTANT.DRAFT"></p-tableCheckbox>
                        </td>
                        <td>
                            <ng-container *ngIf="po.get('state')?.value === PO_STATE_CONSTANT.DRAFT; else linkTpl">
                                {{ po.get('orderNo')?.value }}
                            </ng-container>

                            <ng-template #linkTpl>
                                <a [routerLink]="['/sc/po', po.get('id')?.value]" target="_blank">
                                    {{ po.get('orderNo')?.value }}
                                </a>
                            </ng-template>
                        </td>
                        <td>{{ po.get('supplierName')?.value }}</td>
                        <td>{{ po.get('totalValue')?.value | currency: po.unitPrice }}</td>
                        <td>{{ po.get('unitPrice')?.value }}</td>
                        <td>
                            <app-attachment [attachment]="po.get('boqAttachment')?.value"></app-attachment>
                        </td>
                        <td>
                            <p-tag [value]="getStatusText(po.get('state')?.value)" [severity]="getStatusSeverity(po.get('state')?.value)"> </p-tag>
                        </td>
                    </tr>
                </ng-template>
            </p-table>
        </div>
    </p-panel>
</div>

<p-dialog [style]="{ minWidth: '500px', maxWidth: '900px' }" [(visible)]="isOpenWarningModal" [modal]="true" [closable]="true" [header]="'Cảnh báo'">
    <div class="tw-mt-1">
        <p-table [value]="warnings" styleClass="p-datatable-gridlines" [scrollable]="true" scrollHeight="500px" [resizableColumns]="true">
            <ng-template pTemplate="header">
                <tr>
                    <th>PO</th>
                    <th>Mã VNPT theo mã NSX</th>
                    <th>Lỗi</th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-row>
                <tr>
                    <td>{{ row.po }}</td>
                    <td>{{ row.internalReferences }}</td>
                    <td>{{ row.error }}</td>
                </tr>
            </ng-template>
        </p-table>
        <div class="tw-flex tw-justify-end tw-mt-4 tw-space-x-3">
            <p-button label="Đóng" (click)="isOpenWarningModal = false" severity="secondary" size="small"></p-button>
        </div>
    </div>
</p-dialog>
