import { Component, TemplateRef, ViewChild, OnInit, AfterViewInit, Inject } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { NotificationService } from '../../../services/smart-qc/notification/notification.service';
import { NotificationDTO } from '../../../models/notification/NotificationDTO';
import { Column, User } from '../../../models/interface';
import { TableCommonService } from '../../../shared/table-module/table.common.service';
import { AuthService } from '../../../core/auth/auth.service';
import { ButtonModule } from 'primeng/button';
import Common from '../../../utils/common';
import { TooltipModule } from 'primeng/tooltip';
import { TABLE_KEY } from 'src/app/models/constant';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';

@Component({
    selector: 'app-notification',
    templateUrl: './notification.component.html',
    styleUrls: ['./notification.component.scss'],
    standalone: true,
    imports: [CommonModule, TableCommonModule, ButtonModule, TooltipModule, SubHeaderComponent],
})
export class NotificationComponent implements OnInit, AfterViewInit {
    @ViewChild('notificationTime') notificationTime: TemplateRef<Element>;
    @ViewChild('notificationContent') notificationContent: TemplateRef<Element>;

    user: User;
    state: QueryObserverBaseResult<NotificationDTO[]>;
    columns: Column[] = [];
    tableId: string = TABLE_KEY.NOTIFICATION;
    itemsHeader = [{ label: 'Thông báo' }, { label: 'Danh sách thông báo', url: '' }];

    constructor(
        @Inject(TableCommonService) private tableCommonService: TableCommonService,
        private router: Router,
        private notificationService: NotificationService,
        private authService: AuthService,
    ) {
        this.authService.userObserver.subscribe((data) => {
            this.user = data;
        });
    }

    ngOnInit() {
        this.tableCommonService
            .init<NotificationDTO>({
                tableId: this.tableId,
                defaultParamsRSQL: { receiverId: this.user.id },
                queryFn: (filter, body) => this.notificationService.getPageTableCustomPost(filter, body),
                configFilter: ['contentWeb', 'startTime&endTime'],
            })
            .subscribe((state) => {
                this.state = state;
            });
    }
    ngAfterViewInit(): void {
        setTimeout(() => {
            this.columns = [
                { header: 'Nội dung', body: this.notificationContent, default: true },
                { header: 'Thời gian', body: this.notificationTime, field: 'time' },
            ];
        });
    }

    protected readonly Common = Common;

    isRowSelectable = () => {
        return true;
    };

    deleteSelectedNotification = (ids: number[]) => {
        return this.notificationService.batchDelete(ids);
    };

    deleteAllNotification = () => {
        return this.notificationService.deleteAll();
    };

    navigateToApprove(row: unknown) {
        if (row['objectModel'] && row['objectId']) {
            if (
                row['objectModel'].includes('EmployeeSubmit') ||
                row['objectModel'].includes('SubPMApprove') ||
                row['objectModel'].includes('PMApprove')
            ) {
                // Chuyển tới màn hình chi tiết phê duyệt
                if (this.authService.isAdminOrPM()) {
                    this.router.navigate(['/sqc/approve/' + row['objectId']]);
                } else {
                    this.router.navigate(['/sqc/approve/sub/' + row['objectId']]);
                }
                // Call API mask as Read
                if (row['isRead'] === 0) {
                    const markAsReadDTO = {
                        ids: [row['id']],
                        read: true,
                    };
                    this.notificationService.markAsRead(markAsReadDTO).subscribe(() => {
                        // Set read status
                        row['isRead'] = 1;
                    });
                }
            }
        }
    }
}
