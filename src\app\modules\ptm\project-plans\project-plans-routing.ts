import { canAuthorize } from '../../../core/auth/auth.guard';

export const PLRouting = {
    path: 'project-plans',
    title: '<PERSON>ế hoạch dự án',
    canActivate: [canAuthorize],
    data: { authorize: ['ROLE_SYSTEM_ADMIN'] },
    children: [
        {
            path: '',
            title: '<PERSON><PERSON> sách kế hoạch dự án',
            data: { authorize: ['ROLE_SYSTEM_ADMIN'] },
            loadComponent: () => import('./list/list.component').then((c) => c.ProjectPlanListComponent),
        },
        {
            path: 'create',
            title: 'Tạo mới kế hoạch dự án',
            data: { authorize: ['ROLE_SYSTEM_ADMIN'] },
            loadComponent: () => import('./detail/detail.component').then((c) => c.ProjectPlanDetailComponent),
        },
        {
            path: 'edit/:productId',
            title: '<PERSON><PERSON><PERSON> nhật kế hoạch dự án',
            data: { authorize: ['ROLE_SYSTEM_ADMIN'] },
            loadComponent: () => import('./detail/detail.component').then((c) => c.ProjectPlanDetailComponent),
        },
        {
            path: 'view/:productId',
            title: 'Cập nhật kế hoạch dự án',
            data: { authorize: ['ROLE_SYSTEM_ADMIN'] },
            loadComponent: () => import('./detail/detail.component').then((c) => c.ProjectPlanDetailComponent),
        },
        // {
        //     path: 'view/:productId/:versionId',
        //     title: 'Xem chi tiết hồ sơ sản phẩm',
        //     data: { authorize: ['ROLE_SYSTEM_ADMIN'] },
        //     loadComponent: () => import('./edit/product-file.edit.component').then((c) => c.ProductFileEditComponent),
        // },
    ],
};
