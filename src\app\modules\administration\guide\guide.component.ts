import { Component } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { environment } from 'src/environments/environment';

@Component({
    selector: 'app-guide',
    templateUrl: './guide.component.html',
    standalone: true,
})
export class GuideComponent {
    urlDoc: SafeResourceUrl;

    constructor(private sanitizer: DomSanitizer) {
        this.urlDoc = this.sanitizer.bypassSecurityTrustResourceUrl(environment.HOST_DOC);
    }
}
