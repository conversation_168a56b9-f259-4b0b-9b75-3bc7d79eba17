import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { FormComponent } from './form-base/form.component';
import { FormItemComponent } from './form-item/form-Item.component';
import { CustomFormItemComponent } from './form-item/custom-form-Item.component';

@NgModule({
    declarations: [FormComponent, FormItemComponent, CustomFormItemComponent],
    imports: [CommonModule, FormsModule, ReactiveFormsModule, FormsModule],
    exports: [FormComponent, ReactiveFormsModule, FormItemComponent, CustomFormItemComponent],
})
export class FormCustomModule {}
