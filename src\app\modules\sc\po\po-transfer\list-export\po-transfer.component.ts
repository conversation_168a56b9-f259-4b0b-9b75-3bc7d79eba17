import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { TableModule } from 'primeng/table';
import { Po, PoDetailDTO, PoTransfer } from 'src/app/models/interface/sc';
import { TooltipModule } from 'primeng/tooltip';
import { ConfirmationService } from 'primeng/api';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { MAP_TRANSFER_STATE } from 'src/app/models/constant/sc';
import { TagModule } from 'primeng/tag';
import { EditPoTransferExportComponent } from '../popup-edit-export/edit.po-export.component';
import { PoTransferService } from 'src/app/services/sc/po/po-transfer.service';
import { forkJoin } from 'rxjs';
import { environment } from 'src/environments/environment';
import { PoDetailService } from '../../../../../services/sc/po/po-detail.service';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import {AttributeAuthorityDirective} from "../../../../../shared/directives/attribute-authority.directive";

@Component({
    selector: 'app-list-po-transfer-export',
    templateUrl: './po-transfer.component.html',
    styleUrls: ['./po-transfer.component.scss'],
    standalone: true,
    imports: [
        TableModule,
        InputTextModule,
        DropdownModule,
        CalendarModule,
        ButtonModule,
        CommonModule,
        EditPoTransferExportComponent,
        TooltipModule,
        TagModule,
        HasAnyAuthorityDirective,
        AttributeAuthorityDirective,
    ],
    providers: [PoTransferService, PoDetailService],
})
export class ListPoTransferExportComponent implements OnChanges {
    @Input() po: Po;
    @Output() updatePoDetails = new EventEmitter<PoDetailDTO>();
    @Input() activeIndex: number;

    poTransfers: PoTransfer[] = [];
    poTransferView: PoTransfer;
    loading: boolean = false;
    isDisabled: boolean = true;
    isEdit: boolean = false;
    mapStateTransfer = MAP_TRANSFER_STATE;
    environment = environment;

    constructor(
        private poDetailService: PoDetailService,
        private poTransferExportService: PoTransferService,
        private confirmationService: ConfirmationService,
        private alertService: AlertService,
        private loadingService: LoadingService,
        private poTransferService: PoTransferService,
    ) {}

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['activeIndex'] && changes['activeIndex'].currentValue === 3) {
            this.getListTransfer();
        }
    }

    getListTransfer() {
        forkJoin([
            this.poTransferExportService.getPage(`query=poId==${this.po.id};type==1&page=0&size=100&sort=id,desc`),
            this.poTransferExportService.getPage(`query=poId==${this.po.id};type==0&page=0&size=100&sort=id,desc`),
        ]).subscribe({
            next: ([response1, response2]) => {
                this.poTransfers = response1.body;
                this.isDisabled = response2.body.length === 0;
                this.loading = false;
            },
        });
    }

    confirmDelete(poTransfer: PoTransfer, index: number) {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.loadingService.show();
                this.poTransferExportService.delete(poTransfer.id).subscribe({
                    next: () => {
                        // Call API lay bang ke
                        this.poDetailService.getPoDetailByPo(poTransfer.poId).subscribe({
                            next: (res) => {
                                this.updatePoDetails.emit(res as PoDetailDTO);
                                this.alertService.success('Thành công');
                                this.loadingService.hide();
                                this.poTransfers.splice(index, 1);
                            },
                            error: () => {
                                this.loadingService.hide();
                            },
                        });
                    },
                    error: () => {
                        this.loadingService.hide();
                    },
                });
            },
        });
    }
    handleClose() {
        this.isEdit = false;
        this.getListTransfer();
    }

    addTransfer() {
        this.poTransferView = null;
        this.isEdit = true;
    }
    viewDetail(poTransfer: PoTransfer) {
        this.poTransferView = poTransfer;
        this.isEdit = true;
    }

    getSeverity(poTransfer: PoTransfer) {
        switch (poTransfer.state) {
            case 3:
                return 'danger';
            case 0:
                return 'primary';
            case 1:
                return 'primary';
            case 2:
                return 'success';
            default:
                return 'primary';
        }
    }

    getValueTagState(poTransfer: PoTransfer) {
        return this.mapStateTransfer[poTransfer.state];
    }

    updateDetails(poDetailDTO: PoDetailDTO) {
        this.updatePoDetails.emit(poDetailDTO);
    }

    reSyncInv(id: number) {
        this.loadingService.show();
        this.poTransferService.reSyncInv(id).subscribe({
            next: (res) => {
                this.loadingService.hide();
                this.alertService.success('Thành công');
                this.getListTransfer();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }
}
