<app-skeleton-loading [isLoading]="isLoading">
    <p><b>Trung bình : </b> {{ lineSupplierDeliveries?.averagePoint ?? 'Không có thông tin' }}</p>
    <p><b>Phân hạng : </b> {{ lineSupplierDeliveries?.averageRate ?? 'Không có thông tin' }}</p>

    <p-table styleClass="p-datatable-gridlines" [value]="[lineSupplierDeliveries]">
        <ng-template pTemplate="header">
            <tr>
                <th>Thời gian</th>
                <th *ngFor="let label of lineSupplierDeliveries?.labels">{{ label }}</th>
            </tr>
        </ng-template>

        <ng-template pTemplate="body" let-lineSupplierDelivery>
            <tr>
                <td><PERSON><PERSON><PERSON><PERSON> số</td>
                <td *ngFor="let point of lineSupplierDelivery?.points">{{ point }}</td>
            </tr>
        </ng-template>
    </p-table>

    <div class="tw-mt-8">
        <canvas #templateChartLine style="height: 500px"></canvas>
    </div>
</app-skeleton-loading>
