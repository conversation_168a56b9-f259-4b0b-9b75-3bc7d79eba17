import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { FormArray, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { PanelModule } from 'primeng/panel';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { CalendarModule } from 'primeng/calendar';
import { TableModule } from 'primeng/table';
import { TooltipModule } from 'primeng/tooltip';
import { ButtonGroupFileComponent } from 'src/app/shared/components/button-group-file/button-group-file.component';
import { BoItem, Bo, BoShippingMethod, InventoryProduct, Logistics, ShippingMethod, ProductManPN, PackageType } from 'src/app/models/interface/sc';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { ConfirmationService } from 'primeng/api';
import { AlertService } from 'src/app/shared/services/alert.service';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { MAP_TYPE_MONEY, TYPE_BO_SHIPPING_FORWARDER, TYPE_BO_SHIPPING_INSURANCE, TYPE_SHIPPING, UnitPriceArr } from 'src/app/models/constant/sc';
import { ApiResponse } from 'src/app/models/interface';
import { BoItemService } from 'src/app/services/sc/bo/boItems.service';
import { TimeLineComponent, TimelineItem } from 'src/app/shared/components/time-line/time-line.component';
import { TabViewModule } from 'primeng/tabview';
import { BoNegotiateComponent } from '../negotiate/negotiate.component';
import { BoService } from 'src/app/services/sc/bo/bo.service';
import { ErpManufacturerService } from 'src/app/services/sc/bo/erp-manufacturer.service';
import { InventoryProductService } from 'src/app/services/sc/bo/inventory-product.service';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { InputNumberModule } from 'primeng/inputnumber';
import { FilterChangeEvent } from 'src/app/shared/table-module/filter-table/filter-table.component';
import { cloneDeep, isArray } from 'lodash';
import { BoShippingMethodService } from 'src/app/services/sc/bo/bo-shipping-method.service';
import { ShippingMethodService } from 'src/app/services/sc/bo/shipping-method.service';
import { AutocompleteComponent } from 'src/app/shared/components/autocomplete/autocomplete.component';
import { AbstractControlCustom, FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { FormArrayCustom } from 'src/app/shared/form-module/from-array.custom';
import { LotService } from '../../../../../services/sc/lot/lot.service';
import { InputNumberComponent } from 'src/app/shared/components/inputnumber/inputnumber.component';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { nonEmptyArrayValidator } from 'src/app/utils/validator';
import { ComboboxComponent } from 'src/app/shared/components/combobox/combobox.component';
import { forkJoin, of } from 'rxjs';
import { PackageTypeService } from 'src/app/services/sc/bo/package-type.service';

@Component({
    selector: 'app-bo-detail',
    standalone: true,
    imports: [
        CommonModule,
        SubHeaderComponent,
        RouterLink,
        ButtonModule,
        ReactiveFormsModule,
        FormCustomModule,
        PanelModule,
        InputTextModule,
        InputTextareaModule,
        DropdownModule,
        CalendarModule,
        TableModule,
        TooltipModule,
        ButtonGroupFileComponent,
        TimeLineComponent,
        TabViewModule,
        TableModule,
        DropdownModule,
        BoNegotiateComponent,
        TableCommonModule,
        InputNumberModule,
        AutocompleteComponent,
        ComboboxComponent,
        InputNumberComponent,
        HasAnyAuthorityDirective,
    ],
    templateUrl: './detail.component.html',
    styleUrls: ['./detail.component.scss'],
    providers: [
        BoService,
        BoItemService,
        ErpManufacturerService,
        InventoryProductService,
        BoShippingMethodService,
        ShippingMethodService,
        LotService,
        PackageTypeService,
    ],
})
export class DetailComponent implements OnInit {
    oldbo: Bo;
    boId: number;
    formGroup: FormGroupCustom<Bo>;
    formShippingMethodGroup: FormGroup;
    formShippingMethodInsurancesGroup: FormGroup;
    @ViewChild('form') form: TemplateRef<FormComponent>;
    @ViewChild('boNegotiateComponent') boNegotiateComponent!: BoNegotiateComponent;

    //option
    TYPE_SHIPPING = TYPE_SHIPPING;
    TYPE_SHIPPING_METHOD: ShippingMethod[] = [];
    UnitPriceArr = UnitPriceArr;
    packageTypes: PackageType[] = [];
    MAP_TYPE_MONEY = MAP_TYPE_MONEY;
    // BoItem
    isAddingBoItem: boolean = false;
    isEditingBoItem: boolean = false;
    backUpBoItem: BoItem;

    // Bo SHhipping
    isEditingBoShipping: boolean = false;
    isAddingBoShipping: boolean = false;
    backUpBoShipping: BoItem;

    MAP_TYPE_SHIPPING_METHOD: Record<number, string> = {};

    // file
    urlErrorBoItems: string;
    // status

    // Set
    isSetReferenceFromManPn: boolean = false;
    isChangeReference: boolean = false;

    timelineItems: TimelineItem[] = [
        { name: 'Chọn NCC dịch vụ Logistics', value: 0 },
        { name: 'Thương lượng', value: 1 },
        { name: 'Phê duyệt', value: 2 },
    ];

    activeTabStatus: number;

    // idShipmentMethodNotIn = [];
    isShowEstimatedInsurances: boolean = true;
    constructor(
        private activatedRoute: ActivatedRoute,
        private router: Router,
        private loadingService: LoadingService,
        private fb: FormBuilder,
        private alertService: AlertService,
        private confirmationService: ConfirmationService,
        private boService: BoService,
        private boItemService: BoItemService,
        private boShippingMethodService: BoShippingMethodService,
        private shippingMethodService: ShippingMethodService,
        private route: ActivatedRoute,
        private lotService: LotService,
        private inventoryProductService: InventoryProductService,
        private packageTypeService: PackageTypeService,
    ) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        this.initForm(null);
    }
    ngOnInit(): void {
        this.route.paramMap.subscribe(() => {
            this.loadData();
        });
        this.fetchPackageTypes();
    }

    onStatusSelected(item: TimelineItem) {
        const status = Number(item.value);

        if (status < this.activeTabStatus || (this.oldbo && status <= this.oldbo.status)) {
            this.activeTabStatus = status;
            return;
        }

        if (status === 2) return;
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: `Xác nhận chuyển trạng thái ${item.name}`,
            message: 'Xác nhận chuyển trạng thái?',
            icon: 'pi pi-exclamation-triangle',
            accept: () => {
                this.loadingService.show();
                this.boService.toNegotiate(this.boId).subscribe({
                    next: (res) => {
                        this.oldbo = res;
                        this.activeTabStatus = status > 2 ? 2 : status;
                        this.alertService.success('Thành công');
                        this.loadingService.hide();
                    },
                    error: () => {
                        this.loadingService.hide();
                    },
                });
            },
        });
    }
    initForm(data: Bo | null) {
        this.formGroup = new FormGroupCustom(this.fb, {
            code: [{ value: data?.code, disabled: true }],
            shipmentValue: [data?.shipmentValue],
            type: [{ value: data?.type, disabled: !!data?.id }, Validators.required],
            paymentCondition: [data?.paymentCondition],
            supplierAddress: [data?.supplierAddress],
            supplierName: [data?.supplierName],
            goodsName: [data?.goodsName],
            poNumber: [data?.poNumber],
            indexShipment: [data?.indexShipment],
            supplierInfo: [data?.supplierInfo],
            accountingCode: [data?.accountingCode],
            totalWeight: [data?.totalWeight],
            unit: [data?.unit || 1],
            readyDateCustom: [data && data.readyDate ? new Date(data.readyDate) : null],
            packageNumber: [data?.packageNumber],
            requiredArrivedDateCustom: [data && data.requiredArrivedDate ? new Date(data.requiredArrivedDate) : null],
            deliveryCondition: [data?.deliveryCondition],
            departmentId: [data?.departmentId, Validators.required],
            note: [data?.note],
            typeModule: [data ? data.typeModule : 0], // 0 Bo cho module logistic
            status: [data ? data.status : 0],
            attachmentIds: [data?.attachmentIds],
            attachments: [data?.attachments],
            attachmentItemId: [data?.attachmentItemId],
            attachmentItem: [data?.attachmentItem],
            consignee: [data?.consignee],
            finalDeliveryAddress: [data?.finalDeliveryAddress],
            boItems: this.initBoItems(data?.boItems || []),
        });

        this.formGroup.valueChanges.subscribe(() => {
            this.boNegotiateComponent?.updateFromBoChange(this.formGroup.getRawValue());
        });
    }

    initFormShippingMethod(data: Bo | null) {
        //tab 1
        this.formShippingMethodGroup = this.fb.group({
            boShippingMethods: this.initBoShippingMethods(data?.boShippingMethods || []),
        });

        this.formShippingMethodGroup.get('boShippingMethods').valueChanges.subscribe((newValue: BoShippingMethod[]) => {
            this.timelineItems[1].disabled = !this.checkActiceNegotiate(newValue);
            // this.idShipmentMethodNotIn =
            //     this.formShippingMethodGroup
            //         .get('boShippingMethods')
            //         ?.value.filter((item: BoShippingMethod) => !item.isEdit) // Lọc ra các item có isEdit = false
            //         .map((item: BoShippingMethod) => item.logisticFwdId) || [];
        });
    }

    initFormShippingMethodInsurances() {
        const uniqueShippingMethodIds = [...new Set(this.oldbo.boShippingMethods.map((item) => item.shippingMethodId).filter((item) => !!item))];
        // tab 2
        this.formShippingMethodInsurancesGroup = this.fb.group({
            boShippingInsurances: this.initBoShippingInsurances(uniqueShippingMethodIds),
        });
    }

    checkActiceNegotiate(value: BoShippingMethod[]): boolean {
        if (!value || value.length === 0) {
            return false;
        } else {
            const setTypeShippingMethod = new Set();
            const setTypeShippingMethodInSurance = new Set();
            for (const boShipping of value) {
                if (boShipping['isEdit']) {
                    return false;
                }
                if (boShipping.logisticFwdId) {
                    setTypeShippingMethod.add(boShipping.logisticFwdId);
                }
                if (boShipping.logisticInsuranceId) {
                    setTypeShippingMethodInSurance.add(boShipping.logisticInsuranceId);
                }
            }
            this.isShowEstimatedInsurances = setTypeShippingMethodInSurance.size >= 1;

            if (setTypeShippingMethod.size < 1) {
                return false;
            } else {
                return true;
            }
        }
    }

    loadData = () => {
        const idString = this.activatedRoute.snapshot.paramMap.get('id');
        if (idString) {
            this.boId = Number(idString);
        }

        if (this.boId) {
            this.loadingService.show();
            this.boService.getOne(this.boId).subscribe({
                next: (res) => {
                    this.loadingService.hide();
                    this.oldbo = res.body;
                    this.initForm(this.oldbo);
                    this.initFormShippingMethod(this.oldbo);
                    this.initFormShippingMethodInsurances();

                    this.timelineItems = [
                        { name: 'Chọn NCC dịch vụ Logistics', value: 0 },
                        { name: 'Thương lượng', value: 1 },
                        { name: 'Phê duyệt', value: 2, disabled: this.oldbo.status === 0 },
                    ];
                    this.timelineItems[1].disabled = !this.checkActiceNegotiate(this.oldbo.boShippingMethods);

                    this.activeTabStatus = this.oldbo.status > 2 ? 2 : this.oldbo.status;
                },
                error: () => {
                    this.loadingService.hide();
                    this.alertService.error('Lỗi', 'Không thể truy cập thông tin ');
                    this.router.navigate(['/sc/bo']);
                },
            });
        }

        this.fetchShippingMethod();
    };

    fetchShippingMethod() {
        this.shippingMethodService.getPage('query=&page=0&size=100&sort=id,desc').subscribe({
            next: (res) => {
                this.TYPE_SHIPPING_METHOD = res.body;
                this.TYPE_SHIPPING_METHOD.forEach((item) => {
                    this.MAP_TYPE_SHIPPING_METHOD[item.id] = item.name;
                });
            },
        });
    }

    fetchPackageTypes() {
        this.packageTypeService.getPage('query=&page=0&size=100&sort=name,asc').subscribe({
            next: (res) => {
                this.packageTypes = res.body;
            },
        });
    }

    handleClearFile(type: 'boItems' | 'bo') {
        switch (type) {
            case 'boItems':
                this.formGroup.patchValue({
                    boItems: this.initBoItems([]),
                    attachmentItem: null,
                    attachmentItemId: null,
                });
                break;
            case 'bo':
                this.formGroup.patchValue({
                    attachment: null,
                    attachmentId: null,
                });
                break;
        }
    }

    handleUploadFile(file: File | File[], type: 'boItems' | 'bo') {
        this.loadingService.show();
        switch (type) {
            case 'boItems':
                this.loadingService.show();
                this.boItemService.importFile(file as File, this.boId).subscribe({
                    next: (res: ApiResponse) => {
                        if (res.code === 1) {
                            const data = res.data as BoItem[];
                            data.forEach((item) => {
                                item.boId = this.boId;
                            });
                            const boItemsFormArray = this.initBoItems(data); // Hàm trả về FormArray
                            boItemsFormArray.controls.forEach((item) => {
                                this.disableExceptQuantity(item);
                            });
                            this.formGroup.setControl('boItems', boItemsFormArray);
                            this.urlErrorBoItems = null;
                            this.alertService.success('Thành công');
                        } else {
                            this.urlErrorBoItems = res.message;
                            this.formGroup.patchValue({
                                boItems: [],
                                attachmentItemId: null,
                                attachmentItem: null,
                            });
                            this.alertService.error('Có lỗi xảy ra khi đọc file');
                        }
                        this.loadingService.hide();
                    },
                    error: () => {
                        this.formGroup.patchValue({
                            boItems: [],
                            attachmentItemId: null,
                            attachmentItem: null,
                        });
                        this.loadingService.hide();
                    },
                });
                break;
            case 'bo':
                this.loadingService.show();
                this.boService.importFile(file as File[]).subscribe({
                    next: (res: ApiResponse) => {
                        const itemValue = this.formGroup.getRawValue() as Bo;
                        const attachmentIds = itemValue?.attachmentIds ?? [];
                        const attachments = itemValue?.attachments ?? [];
                        if (res.code === 1) {
                            const newAttachmentIds = [...attachmentIds, ...(res.data['attachmentIds'] || [])];
                            const newAttachments = [...attachments, ...(res.data['attachments'] || [])];

                            this.formGroup.patchValue({
                                attachments: newAttachments,
                                attachmentIds: newAttachmentIds,
                            });
                        } else {
                            this.formGroup.patchValue({
                                attachments: attachments,
                                attachmentIds: attachmentIds,
                            });
                            this.alertService.error('Có lỗi xảy ra khi đọc file');
                        }
                        this.loadingService.hide();
                    },
                    error: () => {
                        this.loadingService.hide();
                    },
                });
        }
    }

    initBoItems(items: BoItem[]): FormArray {
        return new FormArrayCustom(
            items.map(
                (item) =>
                    new FormGroupCustom(this.fb, {
                        id: [item?.id],
                        boId: [item?.boId],
                        created: [item?.created],
                        updated: [item?.updated],
                        createdBy: [item?.createdBy],
                        updatedBy: [item?.updatedBy],
                        tenantId: [item?.tenantId],
                        active: [item?.active],
                        internalReference: [item?.internalReference, Validators.required],
                        manufacturerId: [item?.manufacturerId],
                        productId: [item?.productId],
                        manPn: [item?.manPn],
                        quantity: [item?.quantity, [Validators.required, Validators.min(1)]],
                        price: [item?.price, [Validators.required, Validators.min(0)]],
                        productDescription: [{ value: item?.productDescription, disabled: true }],
                        note: [item?.note],
                        unit: [item?.unit, Validators.required],
                        isEdit: [false],
                    }),
            ),
        );
    }

    initBoShippingMethods(items: BoShippingMethod[]): FormArray {
        return this.fb.array(
            items
                .filter((item) => item.logisticFwdId)
                .map((item) =>
                    this.fb.group({
                        id: [item?.id],
                        boId: [item?.boId],
                        created: [item?.created],
                        updated: [item?.updated],
                        createdBy: [item?.createdBy],
                        updatedBy: [item?.updatedBy],
                        tenantId: [item?.tenantId],
                        active: [item?.active],
                        logisticFwdId: [item?.logisticFwdId],
                        logisticsFwd: [item?.logisticsFwd],
                        shippingMethodId: [item?.shippingMethodId, [Validators.required]],
                        roadNote: [item?.roadNote],
                        isEdit: [false],
                    }),
                ),
        );
    }

    initBoShippingInsurances(uniqueShippingMethodIds: number[]): FormArray {
        // Nhóm logisticInsuranceIds theo shippingMethodId
        const insuranceMap = new Map<number, number[]>();
        this.oldbo.boShippingMethods.forEach((item) => {
            if (item.logisticInsuranceId) {
                if (insuranceMap.has(item.shippingMethodId)) {
                    insuranceMap.get(item.shippingMethodId).push(item.logisticInsuranceId);
                } else {
                    insuranceMap.set(item.shippingMethodId, [item.logisticInsuranceId]);
                }
            }
        });

        // Tạo danh sách bản ghi bảo hiểm
        const insurances = uniqueShippingMethodIds.map((shippingMethodId) => ({
            shippingMethodId,
            logisticInsuranceIds: insuranceMap.get(shippingMethodId) || [],
            isEdit: false,
        }));

        // Khởi tạo FormArray
        return this.fb.array(
            insurances.map((insurance) =>
                this.fb.group({
                    shippingMethodId: [insurance.shippingMethodId, Validators.required],
                    logisticInsuranceIds: [insurance.logisticInsuranceIds, nonEmptyArrayValidator('NCC bảo hiểm yêu cầu dữ liệu')],
                    isEdit: [insurance.isEdit],
                }),
            ),
        );
    }

    get boItems(): FormArray {
        return this.formGroup.get('boItems') as FormArray;
    }

    addBoItem(): void {
        const newItem = this.fb.group({
            id: [null],
            created: [null],
            updated: [null],
            createdBy: [null],
            updatedBy: [null],
            tenantId: [null],
            active: [null],
            boId: [this.boId],
            internalReference: [null, Validators.required],
            manufacturerId: [null],
            productId: [null],
            manPn: [null],
            quantity: [null, [Validators.required, Validators.min(1)]],
            price: [null, [Validators.required, Validators.min(0)]],
            productDescription: [{ value: null, disabled: true }],
            note: [null],
            unit: [null, Validators.required],
            isEdit: [true],
        });
        this.boItems.push(newItem);
        this.isAddingBoItem = true;
    }

    editBoItem(index: number): void {
        const item = this.boItems.at(index);
        this.backUpBoItem = cloneDeep(item.getRawValue());
        item.patchValue({ isEdit: true });
        this.isEditingBoItem = true;
    }

    saveBoItem(index: number): void {
        const item: AbstractControlCustom = this.boItems.at(index) as AbstractControlCustom;

        const itemValue = item.getRawValue();
        item.isSubmited = true;
        if (item.valid) {
            if (this.boId) {
                this.loadingService.show();
                if (item.get('id').getRawValue() === null) {
                    this.boItemService.create(item.getRawValue()).subscribe({
                        next: (res) => {
                            this.loadingService.hide();
                            item.patchValue({ ...res.body, isEdit: false });
                            this.alertService.success('Thành công');
                            // Đặt trạng thái isEditing thành false
                            this.isEditingBoItem = false;
                            this.isAddingBoItem = false;
                            this.disableExceptQuantity(item);

                            // Call api save
                        },
                        error: () => {
                            this.loadingService.hide();
                        },
                    });
                } else {
                    this.boItemService.update(item.getRawValue()).subscribe({
                        next: (res) => {
                            this.loadingService.hide();
                            item.patchValue({ ...res.body, isEdit: false });
                            this.alertService.success('Thành công');
                            // Đặt trạng thái isEditing thành false
                            this.isEditingBoItem = false;
                            this.isAddingBoItem = false;
                            this.disableExceptQuantity(item);

                            // Call api save
                        },
                        error: () => {
                            this.loadingService.hide();
                        },
                    });
                }
            } else {
                item.patchValue({ ...itemValue, isEdit: false });
                // Đặt trạng thái isEditing thành false
                this.isEditingBoItem = false;
                this.isAddingBoItem = false;
                this.disableExceptQuantity(item);
            }
        }
    }

    private disableExceptQuantity(item) {
        item.disable(); // Disable toàn bộ
        item.get('quantity')?.enable(); // Bật lại quantity
        item.get('isEdit')?.enable();
    }

    cancelCreate(index: number): void {
        if (this.isAddingBoItem) {
            this.boItems.removeAt(index);
        } else if (this.isEditingBoItem) {
            this.boItems.at(index).enable();
            this.boItems.at(index).patchValue(this.backUpBoItem);
            this.disableExceptQuantity(this.boItems.at(index));
        }
        this.isAddingBoItem = false;
        this.isEditingBoItem = false;
    }

    removeBoItem(index: number): void {
        const item = this.boItems.at(index);

        const itemValue = item.getRawValue() as BoItem;
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận xóa',
            message: 'Bạn có chắc chắn muốn xóa?',
            icon: 'pi pi-exclamation-triangle',
            accept: () => {
                if (itemValue?.id) {
                    this.loadingService.show();
                    this.boItemService.delete(itemValue.id).subscribe({
                        next: () => {
                            this.isEditingBoItem = false;
                            this.boItems.removeAt(index);
                            this.loadingService.hide();
                            this.alertService.success('Thành công');
                        },
                        error: (res) => {
                            this.alertService.handleError(res);
                            this.loadingService.hide();
                        },
                    });
                } else {
                    this.isEditingBoItem = false;
                    this.boItems.removeAt(index);
                }
            },
        });
    }

    get shippingMethods(): FormArray {
        return this.formShippingMethodGroup?.get('boShippingMethods') as FormArray;
    }

    get shippingMethodInsurances(): FormArray {
        return this.formShippingMethodInsurancesGroup?.get('boShippingInsurances') as FormArray;
    }

    addBoShipping(type: 'forwarder' | 'insurance'): void {
        const newItem = this.fb.group({
            id: [null],
            created: [null],
            updated: [null],
            createdBy: [null],
            updatedBy: [null],
            tenantId: [null],
            active: [null],
            boId: [this.boId],
            logisticFwdId: [null, Validators.required],
            shippingMethodId: [null, [Validators.required]],
            roadNote: [null],
            logisticsFwd: [null],
            logisticInsuranceId: [null],
            logisticsInsurance: [null],
            isEdit: [true],
            type: [type === 'forwarder' ? TYPE_BO_SHIPPING_FORWARDER : TYPE_BO_SHIPPING_INSURANCE],
        });
        this.shippingMethods.push(newItem);
        this.isAddingBoShipping = true;
    }

    editItemBoShipping(index: number): void {
        const item = this.shippingMethods.at(index);
        this.backUpBoShipping = cloneDeep(item.getRawValue());
        item.patchValue({ isEdit: true });
        this.isEditingBoShipping = true;
    }

    editItemBoShippingInsurances(index: number): void {
        const item = this.shippingMethodInsurances.at(index);
        this.backUpBoShipping = cloneDeep(item.getRawValue());
        item.patchValue({ isEdit: true });
        this.isEditingBoShipping = true;
    }

    saveItemBoShipping(index: number): void {
        const item = this.shippingMethods.at(index) as AbstractControlCustom;
        const itemValue = item.getRawValue() as BoShippingMethod;

        item.isSubmited = true;
        if (item.valid) {
            item.enable();

            this.loadingService.show();
            if (itemValue?.id) {
                this.boShippingMethodService.update(itemValue).subscribe({
                    next: (res) => {
                        // Cập nhật isEdit thành false
                        this.loadingService.hide();

                        item.patchValue({ ...res.body, isEdit: false });
                        this.alertService.success('Thành công');
                        // Đặt trạng thái isEditing thành false
                        this.isEditingBoShipping = false;
                        this.isAddingBoShipping = false;

                        const oldIndex = this.oldbo.boShippingMethods.findIndex((x) => x.id === itemValue?.id);
                        this.oldbo.boShippingMethods[oldIndex] = res.body;
                        this.initFormShippingMethodInsurances();
                    },
                    error: (res) => {
                        this.alertService.handleError(res);
                        this.loadingService.hide();
                    },
                });
            } else {
                this.boShippingMethodService.create(itemValue).subscribe({
                    next: (res) => {
                        // Cập nhật isEdit thành false
                        this.loadingService.hide();
                        item.patchValue({ ...res.body, isEdit: false });
                        this.alertService.success('Thành công');
                        this.oldbo.boShippingMethods.push(res.body);

                        // Đặt trạng thái isEditing thành false
                        this.isEditingBoShipping = false;
                        this.isAddingBoShipping = false;
                        this.initFormShippingMethodInsurances();
                    },
                    error: (res) => {
                        this.alertService.handleError(res);
                        this.loadingService.hide();
                    },
                });
            }
        }
    }

    saveItemBoShippingInsurances(index: number): void {
        const item = this.shippingMethodInsurances.at(index) as AbstractControlCustom;
        const itemValue = item.getRawValue() as BoShippingMethod;

        item.isSubmited = true;
        if (item.valid) {
            // lọc ra item bảo hiểm cũ
            const boShippingInsurances = this.oldbo.boShippingMethods.filter(
                (x) => x.shippingMethodId === itemValue.shippingMethodId && x.logisticInsuranceId && !x.logisticFwdId,
            );
            // Tạo tập hợp (Set) các composite keys từ logisticInsuranceIds và shippingMethodId của itemValue
            const setTypeShippingMethod = new Set(itemValue.logisticInsuranceIds.map((id) => `${id}-${itemValue.shippingMethodId}`));
            // Tạo tập hợp các composite keys hiện có trong mảng boShippingMethods
            const currentIds = new Set(boShippingInsurances.map((x) => `${x.logisticInsuranceId}-${x.shippingMethodId}`));

            // Tìm các composite keys cần tạo mới (có trong setTypeShippingMethod nhưng không có trong currentIds)
            const itemsToCreate = [...setTypeShippingMethod]
                .filter((key) => !currentIds.has(key))
                .map((key) => {
                    const [logisticInsuranceId] = key.split('-');
                    return logisticInsuranceId;
                });
            // Tìm các composite keys cần xóa (có trong currentIds nhưng không có trong setTypeShippingMethod)
            const itemsToDelete = [...currentIds].filter((key) => !setTypeShippingMethod.has(key));
            // Lấy danh sách ID của các model cần xóa (dựa trên composite keys cần xóa)
            const idModelToDelete = boShippingInsurances
                .filter((x) => itemsToDelete.includes(`${x.logisticInsuranceId}-${x.shippingMethodId}`))
                .map((x) => x.id);

            // Tạo danh sách observable để gọi API tạo mới các mục bảo hiểm vận chuyển
            const createObservables = itemsToCreate.map((id) =>
                this.boShippingMethodService.create({
                    ...itemValue,
                    logisticInsuranceId: id,
                    shippingMethodId: itemValue.shippingMethodId,
                    boId: this.boId,
                }),
            );
            // Tạo observable để xóa các mục, nếu không có gì để xóa thì trả về of(null)
            const deleteObservable = idModelToDelete.length > 0 ? this.boShippingMethodService.batchDelete(idModelToDelete) : of(null);

            // Hiển thị loading indicator
            this.loadingService.show();

            // Thực hiện đồng thời các thao tác tạo và xóa bằng forkJoin
            forkJoin([...createObservables, deleteObservable]).subscribe({
                next: (results) => {
                    // Tách kết quả thành kết quả tạo và xóa
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    const createResults = results.slice(0, createObservables.length) as any as { body: BoShippingMethod }[];

                    // Xóa các mục đã bị xóa khỏi mảng boShippingMethods
                    this.oldbo.boShippingMethods = this.oldbo.boShippingMethods.filter((x) => !idModelToDelete.includes(x.id));

                    // Thêm các mục mới được tạo vào mảng boShippingMethods
                    const createdItems = createResults
                        .filter((res) => res?.body) // Đảm bảo body tồn tại
                        .map((res) => res.body);
                    this.oldbo.boShippingMethods.push(...createdItems);

                    // Cập nhật control với giá trị mới và đặt isEdit thành false
                    item.patchValue({ ...itemValue, isEdit: false });

                    this.loadingService.hide();
                    this.alertService.success('Thành công');
                    this.isEditingBoShipping = false;
                    this.isAddingBoShipping = false;
                },
                error: () => {
                    this.loadingService.hide();
                },
            });
        }
    }

    cancelCreateBoShippingInsurances(index: number): void {
        if (this.isAddingBoShipping) {
            this.shippingMethodInsurances.removeAt(index);
        } else if (this.isEditingBoShipping) {
            this.shippingMethodInsurances.at(index).enable();
            this.shippingMethodInsurances.at(index).patchValue({ ...this.backUpBoShipping, isEdit: false });
        }
        this.isAddingBoShipping = false;
        this.isEditingBoShipping = false;
    }

    cancelCreateBoShipping(index: number): void {
        if (this.isAddingBoShipping) {
            this.shippingMethods.removeAt(index);
        } else if (this.isEditingBoShipping) {
            this.shippingMethods.at(index).enable();
            this.shippingMethods.at(index).patchValue({ ...this.backUpBoShipping, isEdit: false });
        }
        this.isAddingBoShipping = false;
        this.isEditingBoShipping = false;
    }

    deleteItemBoShipping(index: number): void {
        const item = this.shippingMethods.at(index);

        const itemValue = item.getRawValue() as BoShippingMethod;
        if (itemValue?.id) {
            this.confirmationService.confirm({
                key: 'app-confirm',
                header: 'Xác nhận xóa',
                message: 'Bạn có chắc chắn muốn xóa?',
                icon: 'pi pi-exclamation-triangle',
                accept: () => {
                    this.loadingService.show();
                    const idDeletes = this.oldbo.boShippingMethods
                        .filter((x) => x.shippingMethodId === itemValue.shippingMethodId && x.logisticInsuranceId && !x.logisticFwdId)
                        .map((x) => x.id);
                    idDeletes.push(itemValue.id);
                    this.boShippingMethodService.batchDelete(idDeletes).subscribe({
                        next: () => {
                            this.isEditingBoShipping = false;
                            this.shippingMethods.removeAt(index);
                            this.loadingService.hide();
                            this.oldbo.boShippingMethods = this.oldbo.boShippingMethods.filter((item) => !idDeletes.includes(item.id));

                            this.initFormShippingMethodInsurances();

                            this.alertService.success('Thành công');
                        },
                        error: (res) => {
                            this.alertService.handleError(res);
                            this.loadingService.hide();
                        },
                    });
                },
            });
        } else {
            console.log('delete no action');

            this.isEditingBoShipping = false;
            this.shippingMethods.removeAt(index);
        }
    }

    handleChangeManufacture(e: FilterChangeEvent, index: number) {
        const objects = e.objects as unknown as ProductManPN[];
        const productMan = isArray(objects) && objects.length > 0 ? objects[0] : (null as ProductManPN);
        if (e?.value !== null && productMan !== null) {
            this.boItems.controls[index].patchValue({
                manPn: productMan?.manufacturerPn,
                manufacturerId: productMan?.manufacturerId,
                productId: productMan?.productId,
            });
            const query = `query=manufacturerId==${productMan?.manufacturerId};productId==${productMan?.productId}`;
            this.inventoryProductService.getPage(query).subscribe({
                next: (res) => {
                    this.loadingService.hide();
                    const data = res.body as unknown as InventoryProduct[];
                    if (isArray(data) && data.length > 0) {
                        this.boItems.controls[index].patchValue({
                            internalReference: data[0].internalReference,
                        });
                    }
                },
                error: () => {
                    this.loadingService.hide();
                },
            });
        } else {
            this.boItems.controls[index].patchValue({
                manPn: null,
            });
        }
    }

    handleChangeInternalReference(e: FilterChangeEvent, index: number) {
        const objects = e.objects as unknown as ProductManPN[];
        const inventoryProduct = isArray(objects) && objects.length > 0 ? objects[0] : (null as InventoryProduct);
        if (e?.value !== null && inventoryProduct !== null) {
            this.boItems.controls[index].patchValue({
                manufacturerId: inventoryProduct?.manufacturerId,
                productId: inventoryProduct?.productId,
            });
        } else {
            this.boItems.controls[index].patchValue({
                internalReference: null,
                manufacturerId: null,
                productId: null,
            });
        }
    }

    handleChangeLogistic(e: FilterChangeEvent, index: number) {
        const objects = e.objects as unknown as Logistics[];
        const logistic = isArray(objects) && objects.length > 0 ? objects[0] : (null as Logistics);
        this.shippingMethods.controls[index].patchValue({
            logisticsFwd: logistic,
        });
    }

    handleChangeLogisticInsurance(e: FilterChangeEvent, index: number) {
        const objects = e.objects as unknown as Logistics[];
        const logistic = isArray(objects) && objects.length > 0 ? objects[0] : (null as Logistics);
        this.shippingMethods.controls[index].patchValue({
            logisticsInsurance: logistic,
        });
    }

    onSubmit(value: Bo): void {
        value.readyDate = value.readyDateCustom ? value.readyDateCustom.getTime() : null;
        value.requiredArrivedDate = value.requiredArrivedDateCustom ? value.requiredArrivedDateCustom.getTime() : null;
        this.loadingService.show();
        if (this.boId) {
            this.boService.update({ ...this.oldbo, ...value }).subscribe({
                next: (res) => {
                    this.loadingService.hide();
                    this.oldbo = res.body;
                    this.alertService.success('Thành công');
                },
                error: () => {
                    this.loadingService.hide();
                },
            });
        } else {
            this.boService.create(value).subscribe({
                next: (res) => {
                    this.loadingService.hide();
                    this.alertService.success('Thành công');
                    this.router.navigate([`/sc/bo/${res.body.id}`]);
                },
                error: () => {
                    this.loadingService.hide();
                },
            });
        }
    }

    onNegotiate(bo: Bo) {
        if (bo.isNegotiate) {
            this.activeTabStatus = 2; // phê duyệt
        }
        const newDataPo = {
            ...this.oldbo,
            status: bo.status,
            shipmentInfo: bo.shipmentInfo,
            logisticForwarderId: bo.logisticForwarderId,
            logisticInsuranceId: bo.logisticInsuranceId,
            shippingMethodId: bo.shippingMethodId,
            estimatedInsurances: bo.estimatedInsurances,
            estimatedSchedules: bo.estimatedSchedules,
            estimatedTransportCosts: bo.estimatedTransportCosts,
            noteNegotiate: bo.noteNegotiate,
        };
        this.oldbo = newDataPo;
    }

    handleAddMethod(event: { value: ShippingMethod; callback: (result: ShippingMethod) => void }) {
        this.loadingService.show();
        this.shippingMethodService.create({ ...event.value, id: null }).subscribe({
            next: (res) => {
                event.callback(res.body);
                this.loadingService.hide();
                this.TYPE_SHIPPING_METHOD.push(res.body);
                this.TYPE_SHIPPING_METHOD.forEach((item) => {
                    this.MAP_TYPE_SHIPPING_METHOD[item.id] = item.name;
                });
            },
            error: () => {
                this.loadingService.hide();
                event.callback({ ...event.value, id: null });
            },
        });
    }

    handleDeleteMethod(event: { value: ShippingMethod; callback: (result: boolean) => void }) {
        this.loadingService.show();

        this.shippingMethodService.delete(event.value.id).subscribe({
            next: () => {
                event.callback(true);
                this.loadingService.hide();
                this.fetchShippingMethod();
            },
            error: () => {
                this.loadingService.hide();
                event.callback(false);
            },
        });
    }

    handleAddPackagegType(event: { value: PackageType; callback: (result: PackageType) => void }) {
        this.loadingService.show();
        this.packageTypeService.create({ ...event.value, id: null }).subscribe({
            next: (res) => {
                event.callback(res.body);
                this.loadingService.hide();
                this.packageTypes.push(res.body);
            },
            error: () => {
                this.loadingService.hide();
                event.callback({ ...event.value, id: null });
            },
        });
    }

    handleDeletePackagegType(event: { value: PackageType; callback: (result: boolean) => void }) {
        this.loadingService.show();

        this.packageTypeService.delete(event.value.id).subscribe({
            next: () => {
                event.callback(true);
                this.loadingService.hide();
                this.fetchPackageTypes();
            },
            error: () => {
                this.loadingService.hide();
                event.callback(false);
            },
        });
    }

    rowDelete = (rowData: ShippingMethod) => {
        return rowData.createdBy !== 'system';
    };

    goToLotPage() {
        this.loadingService.show();
        this.lotService.getPage(`query=boId==${this.oldbo.id}`).subscribe({
            next: (res) => {
                this.loadingService.hide();
                if (isArray(res.body) && res.body[0] !== null) {
                    this.router.navigate([`/sc/lot/${res.body[0].id}`]);
                } else {
                    this.alertService.error('Theo dõi yêu cầu vận chuyển không tồn tại');
                }
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }
}
