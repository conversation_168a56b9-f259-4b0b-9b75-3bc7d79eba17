import { Component, Input, Optional, Host, AfterViewInit, ElementRef, OnInit } from '@angular/core';
import { AbstractControl, ControlContainer, FormArray, FormControl, FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { isBoolean, isObject } from 'lodash';
import { FormContextService } from '../form-context.service';
import { AbstractControlCustom } from '../from-group.custom';

@Component({
    selector: 'app-form-item',
    template: `
        <div [ngClass]="itemClass">
            <label [ngClass]="labelClass" *ngIf="label" style="line-height: 2rem;">
                <b>{{ label }}</b>
                <span class="text-red-400" *ngIf="isRequired">(*)</span>
            </label>
            <div [ngClass]="wrapperClass">
                <ng-content></ng-content>
                <div *ngIf="showError && !disableAutoErrorMessage" class="text-red-400 tw-whitespace-nowrap">
                    {{ errorMessage }}
                </div>
            </div>
        </div>
    `,
})
export class FormItemComponent implements OnInit, AfterViewInit {
    @Input() label: string;
    @Input() formControlName: string;
    @Input() labelCol: string = 'tw-col-span-4';
    @Input() wrapperCol: string = 'tw-col-span-8';
    @Input() validateTrigger: 'change' | 'touched' | 'submit';
    @Input() layout: 'horizontal' | 'vertical';
    /** Nếu true → không thêm bất cứ class grid nào */
    @Input() noGrid = false;
    @Input() disableAutoErrorMessage: boolean = false;
    private observer: MutationObserver;

    control: AbstractControl | null = null;
    @Input() isRequired: boolean = false;

    constructor(
        @Optional() @Host() private controlContainer: ControlContainer,
        @Host() @Optional() private formContext: FormContextService,
        private translateService: TranslateService,
        private elementRef: ElementRef,
    ) {}

    ngOnInit(): void {
        // Sử dụng giá trị từ formContext nếu không có giá trị input
        this.labelCol = this.labelCol || this.formContext?.labelCol;
        this.wrapperCol = this.wrapperCol || this.formContext?.wrapperCol;
        this.validateTrigger = this.validateTrigger || this.formContext?.validateTrigger;
        this.layout = this.layout || this.formContext?.layout;

        if (!this.layout) {
            this.layout = 'horizontal';
        }
        if (!this.wrapperCol) {
            this.wrapperCol = 'tw-col-span-8';
        }
        if (!this.labelCol) {
            this.labelCol = 'tw-col-span-4';
        }
        if (!this.validateTrigger) {
            this.validateTrigger = 'submit';
        }
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.detectFormControlName();
            this.checkRequiredValidator();
        });

        setTimeout(() => {
            this.detectFormControlName();
        }, 500);

        this.observer = new MutationObserver((mutations) => {
            this.detectFormControlName(); // gọi lại khi DOM có gì đó mới gắn vào
        });

        this.observer.observe(this.elementRef.nativeElement, {
            childList: true,
            subtree: true,
        });
    }

    private detectFormControlName(): void {
        const element = this.elementRef.nativeElement;
        const inputElements = element.querySelectorAll('[formControlName]');
        const arrayElements = element.querySelectorAll('[formArrayName]');

        if (inputElements.length > 0) {
            this.formControlName = inputElements[0].getAttribute('formControlName');
        } else if (arrayElements.length > 0) {
            this.formControlName = arrayElements[0].getAttribute('formArrayName');
        }

        if (this.formControlName && this.controlContainer?.control) {
            if (this.controlContainer.control instanceof FormGroup) {
                this.control = this.controlContainer.control.get(this.formControlName);
            } else if (this.controlContainer.control instanceof FormArray) {
                this.control = this.controlContainer.control.get(this.formControlName);
            }
        }
    }

    private checkRequiredValidator(): void {
        if (this.control?.validator && !this.isRequired) {
            const validatorResult = this.control.validator({} as FormControl);
            this.isRequired = validatorResult && validatorResult['required'];
        }
    }

    get itemClass() {
        if (this.noGrid) {
            return {}; // không có class nào
        }
        return {
            'tw-grid': true,
            'tw-grid-cols-12': this.layout === 'horizontal',
        };
    }

    get labelClass(): string {
        return this.layout === 'vertical' ? 'tw-col-span-12' : this.labelCol;
    }

    get wrapperClass(): string {
        return this.layout === 'vertical' || !this.label ? 'tw-col-span-12' : this.wrapperCol;
    }

    get showError(): boolean {
        if (!this.control) return false;

        if (this.formContext?.isSubmited) {
            return this.control.invalid;
        }

        // Lấy parent FormGroupCustom gần nhất để kiểm tra isSubmited
        const parent: AbstractControlCustom = this.control.parent as unknown as AbstractControlCustom;
        const isSubmited = parent?.isSubmited ?? false;

        if (this.validateTrigger === 'submit') {
            return isSubmited && this.control.invalid;
        }

        return (
            (this.validateTrigger === 'touched' && this.control.touched && this.control.invalid) ||
            (this.validateTrigger === 'change' && this.control.dirty && this.control.invalid)
        );
    }

    get errorMessage(): string {
        if (!this.control || !this.control.errors) return '';

        const errorMessages: { [key: string]: string } = {
            required: 'validate.required',
            min: 'validate.min',
            max: 'validate.max',
            email: 'validate.email',
            minlength: 'validate.minlength',
            maxlength: 'validate.maxlength',
            phone: 'validate.phone',
            url: 'validate.url',
            maxDateRange: 'validate.maxdaterange',
            duplicateValue: 'validate.duplicateValue',
            emptyArray: 'validate.emptyArray',
            number: '',
            invalidSharePointUrl: '',
            mismatch: '',
        };

        for (const errorKey in this.control.errors) {
            if (errorMessages[errorKey]) {
                const errorValue = this.control.errors[errorKey];
                if (isBoolean(errorValue)) {
                    return this.translateService.instant(errorMessages[errorKey]);
                } else if (isObject(errorValue)) {
                    const params = {
                        [errorKey]:
                            errorValue['requiredLength'] ?? errorValue['max'] ?? errorValue['min'] ?? errorValue['duplicateValue'] ?? errorValue['emptyArray'],
                        type: errorValue['type'] ?? '',
                    };
                    return this.translateService.instant(errorMessages[errorKey], params);
                } else {
                    return errorValue;
                }
            } else {
                return this.control.errors[errorKey];
            }
        }

        return '';
    }
}
