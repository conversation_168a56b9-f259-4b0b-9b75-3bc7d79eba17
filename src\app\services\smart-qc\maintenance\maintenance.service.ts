import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '../../base.service';
import { Maintenance, MaintenanceDTO, MaintenanceFilter } from '../../../models/interface/smart-qc';
import { ParamsTable } from 'src/app/shared/table-module/table.common.service';
import { ApiResponse, GeneralEntity } from 'src/app/models/interface';

@Injectable()
export class MaintenanceService extends BaseService<Maintenance> {
    constructor(protected override http: HttpClient) {
        super(http, '/smart-qc/api/maintenance');
    }

    getPageCustom({ pageable = '&page=0&size=10' }: ParamsTable, body: Partial<MaintenanceFilter>) {
        return this.http.post<MaintenanceDTO[]>(`/smart-qc/api/maintenance/search-custom?${pageable}`, body, {
            observe: 'response',
        });
    }

    getFileImport(contractId: number) {
        return this.http.get<GeneralEntity>(`/smart-qc/api/maintenance/file-import?contractId=${contractId}`);
    }

    importCreate(file: File, contractId: number) {
        const formData: FormData = new FormData();
        formData.append('file', file);
        formData.append('contractId', contractId.toString());

        return this.http.post<ApiResponse>('/smart-qc/api/maintenance/import', formData);
    }

    exportListExcel(body: Partial<MaintenanceFilter>) {
        return this.http.post<GeneralEntity>(`/smart-qc/api/maintenance/export`, body);
    }
}
