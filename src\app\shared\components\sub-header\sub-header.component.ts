import { <PERSON><PERSON><PERSON>, NgIf, NgTemplateOutlet } from '@angular/common';
import { Component, Input, TemplateRef } from '@angular/core';
import { MenuItem } from 'primeng/api';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { ButtonModule } from 'primeng/button';
import { OverlayPanelModule } from 'primeng/overlaypanel';

@Component({
    selector: 'app-sub-header',
    templateUrl: './sub-header.component.html',
    styleUrls: ['./sub-header.component.scss'],
    standalone: true,
    imports: [BreadcrumbModule, ButtonModule, OverlayPanelModule, NgClass, NgIf, NgTemplateOutlet],
})
export class SubHeaderComponent {
    @Input() items: MenuItem[] | undefined;
    @Input() home: MenuItem | undefined = {
        icon: 'pi pi-home',
        routerLink: '/',
    };
    @Input() action: TemplateRef<Element>;
}
