import { TranslateService } from '@ngx-translate/core';
import { isBoolean, isObject } from 'lodash';
import { FormContextService } from 'src/app/shared/form-module/form-context.service';
import { Component, Input, OnInit, Host, OnChanges, SimpleChanges, Optional } from '@angular/core';
import { FormControl } from '@angular/forms';

@Component({
    selector: 'app-custom-form-item',
    template: `
        <div [ngClass]="itemClass">
            <label [ngClass]="labelClass" *ngIf="label" style="line-height: 2rem;">
                <b>{{ label }}</b>
                <span class="text-red-400" *ngIf="isRequired">(*)</span>
            </label>
            <div [ngClass]="wrapperClass">
                <ng-content></ng-content>
                <div *ngIf="showError" class="text-red-400">
                    {{ errorMessage }}
                </div>
            </div>
        </div>
    `,
})
export class CustomFormItemComponent implements OnInit, OnChanges {
    @Input() label: string;
    @Input() control: FormControl;
    @Input() labelCol: string;
    @Input() wrapperCol: string;
    @Input() validateTrigger: 'change' | 'touched' | 'submit';
    @Input() layout: 'horizontal' | 'vertical';
    /** Nếu true → không thêm bất cứ class grid nào */
    @Input() noGrid = false;

    isRequired: boolean = false;

    constructor(
        @Host() @Optional() private formContext: FormContextService,
        private translateService: TranslateService,
    ) {}
    ngOnChanges(changes: SimpleChanges): void {
        if (changes['labelCol'] && changes['labelCol'].currentValue !== this.labelCol) {
            this.labelCol = changes['labelCol'].currentValue;
        }
        if (changes['wrapperCol'] && changes['wrapperCol'].currentValue !== this.wrapperCol) {
            this.wrapperCol = changes['wrapperCol'].currentValue;
        }
        if (changes['validateTrigger'] && changes['validateTrigger'].currentValue !== this.validateTrigger) {
            this.validateTrigger = changes['validateTrigger'].currentValue;
        }
        if (changes['layout'] && changes['layout'].currentValue !== this.layout) {
            this.layout = changes['layout'].currentValue;
        }
    }

    ngOnInit(): void {
        // Kiểm tra xem control có validator required không
        if (this.control?.validator) {
            const validator = this.control.validator({} as FormControl);
            this.isRequired = validator && validator['required'];
        }

        // Sử dụng giá trị từ formContext nếu không có giá trị input
        this.labelCol = this.labelCol || this.formContext?.labelCol;
        this.wrapperCol = this.wrapperCol || this.formContext?.wrapperCol;
        this.validateTrigger = this.validateTrigger || this.formContext?.validateTrigger;
        this.layout = this.layout || this.formContext?.layout;
        if (!this.layout) {
            this.layout = 'horizontal';
        }
        if (!this.wrapperCol) {
            this.wrapperCol = 'tw-col-span-8';
        }
        if (!this.labelCol) {
            this.labelCol = 'tw-col-span-4';
        }
        if (!this.validateTrigger) {
            this.validateTrigger = 'submit';
        }
    }

    // Class cho item dựa trên layout
    get itemClass() {
        if (this.noGrid) {
            return {}; // không có class nào
        }
        return {
            'tw-grid': true,
            'tw-grid-cols-12': this.layout === 'horizontal',
        };
    }

    get labelClass(): string {
        return this.layout === 'vertical' ? 'tw-col-span-12' : this.labelCol;
    }

    get wrapperClass(): string {
        return this.layout === 'vertical' || !this.label ? 'tw-col-span-12' : this.wrapperCol;
    }

    // Kiểm tra điều kiện để hiển thị lỗi
    get shouldShowError(): boolean {
        if (!this.control) return false;

        if (this.validateTrigger === 'submit') {
            return this.formContext?.isSubmited && this.control.invalid;
        }

        return (
            (this.validateTrigger === 'touched' && this.control.touched && this.control.invalid) ||
            (this.validateTrigger === 'change' && this.control.dirty && this.control.invalid)
        );
    }

    // Thông báo lỗi tùy theo các loại lỗi khác nhau
    get errorMessage(): string {
        const errorMessages: { [key: string]: string } = {
            required: 'validate.required',
            min: 'validate.min',
            max: 'validate.max',
            email: 'validate.email',
            minlength: 'validate.minlength',
            maxlength: 'validate.maxlength',
            phone: 'validate.phone',
            url: 'validate.url',
            maxDateRange: 'validate.maxdaterange',
            duplicateValue: 'validate.duplicateValue',
        };

        for (const errorKey in this.control.errors) {
            if (errorMessages[errorKey]) {
                const errorValue = this.control.errors[errorKey];
                if (isBoolean(errorValue)) {
                    return this.translateService.instant(errorMessages[errorKey]);
                } else if (isObject(errorValue)) {
                    const params = {
                        [errorKey]:
                            errorValue['requiredLength'] ??
                            errorValue['max'] ??
                            errorValue['min'] ??
                            errorValue['duplicateValue'],
                        type: errorValue['type'] ?? '',
                    };

                    return this.translateService.instant(errorMessages[errorKey], params);
                } else {
                    return errorValue; // Custom error message
                }
            }
        }

        return '';
    }

    get showError(): boolean {
        return this.shouldShowError;
    }
}
