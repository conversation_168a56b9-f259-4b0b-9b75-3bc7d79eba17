import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ParamsTable } from '../../../shared/table-module/table.common.service';
import { Contract } from '../../../models/interface/smart-qc';
import { District } from '../../../models/interface';

@Injectable()
export class DistrictService {
    constructor(private http: HttpClient) {}

    getPage(params) {
        return this.http.get<unknown[]>('/smart-qc/api/district/search?' + params, {
            observe: 'response',
        });
    }

    getAll() {
        return this.http.get<unknown[]>('/smart-qc/api/district/search?query=&page=0&size=1000');
    }

    addDistrict(district) {
        return this.http.post<unknown[]>('/auth/api/district', district, {
            observe: 'response',
        });
    }

    update(district: District) {
        return this.http.put<unknown[]>('/auth/api/district/' + district.id, district, {
            observe: 'response',
        });
    }

    deleteDistricts(ids: number[]): Observable<unknown> {
        return this.http.post<unknown[]>('/auth/api/district/batch-delete', ids, {
            observe: 'response',
        });
    }

    getPageTableCustom({ native = '', pageable = '&page=0&size=10', rsql = '' }: ParamsTable) {
        return this.http.get<Contract[]>(`/smart-qc/api/district/search?query=${rsql}${native}${pageable}`, {
            observe: 'response',
        });
    }

    batchDelete(ids: number[]) {
        return this.http.post<unknown[]>(`/auth/api/district/batch-delete`, ids);
    }
}
