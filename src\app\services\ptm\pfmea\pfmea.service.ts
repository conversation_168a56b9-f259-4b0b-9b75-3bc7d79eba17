import { inject, Injectable } from '@angular/core';
import { catchError, Observable, throwError } from 'rxjs';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Pfmea, PfmeaUpdate } from '../../../models/interface/ptm/pfmea';

@Injectable({
    providedIn: 'root',
})
export class PfmeaService {
    path = '/pr/api/production-instruction';
    #http = inject(HttpClient);

    getPfmea(id: string): Observable<any> {
        const url = `${this.path}/${id}/pfmea`;
        return this.#http.get<any>(url);
    }

    updatePfmea(id, data: any) {
        const url = `${this.path}/${id}/pfmea`;
        return this.#http.post<any>(url, data).pipe(catchError(this.handleError));
    }

    exportPfmea(id: number) {
        const url = `${this.path}/${id}/pfmea/export`;
        return this.#http.get(url, { responseType: 'text' }).pipe(catchError(this.handleError));
    }

    previewPfmea(formData: any, id: number) {
        const url = `/pr/api/approval/production-instruction/${id}`;
        return this.#http.post(url, formData).pipe(catchError(this.handleError));
    }

    comfirmPfmea(formData: any, id: number) {
        const url = `/pr/api/approval/production-instruction/${id}/confirm`;
        return this.#http.post(url, formData).pipe(catchError(this.handleError));
    }

    private handleError(error: HttpErrorResponse) {
        // có thể tuỳ chỉnh thông báo, logging, …
        console.error('API error', error);
        return throwError(() => error);
    }
}
