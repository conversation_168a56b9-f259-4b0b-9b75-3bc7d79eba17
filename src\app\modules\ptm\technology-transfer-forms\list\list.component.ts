import { AfterViewInit, Component, OnInit, TemplateRef, ViewChild, inject, ChangeDetectorRef } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { ParamsTable, TableCommonService } from 'src/app/shared/table-module/table.common.service';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { TABLE_KEY } from 'src/app/models/constant';
import { Column, EventPopupSubmit } from 'src/app/models/interface';
import { TagModule } from 'primeng/tag';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { DialogModule } from 'primeng/dialog';
import { AlertService } from 'src/app/shared/services/alert.service';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { ConfirmationService } from 'primeng/api';
import { DropdownModule } from 'primeng/dropdown';
import { FormBuilder, FormGroup, FormsModule, Validators } from '@angular/forms';
import { Menu, MenuModule } from 'primeng/menu';
import { MenuItem } from 'primeng/api';
import { PopupComponent } from 'src/app/shared/components/popup/popup.component';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { TableCustomComponent, TrackColumn } from 'src/app/shared/components/table-custom/table-custom.component';
import { TableCellDirective } from 'src/app/shared/directives/table-cell.directive';
import { UploadCustomComponent } from 'src/app/shared/components/upload-custom/upload-custom.component';
import { UploadService } from 'src/app/services/upload/upload.service';
import { md5File } from 'src/app/utils/hash-md5';
import { finalize, Observable, Subject, Subscription, switchMap, takeUntil, tap } from 'rxjs';
import { AnalyzeResp, DropdownOption, ProductDetail, ProductSoftware, UpdateVersionDto } from 'src/app/models/interface/pms';
import { TechnologyTransferService } from 'src/app/services/ptm/technology-transfer/technology-transfer.service';
import { environment } from 'src/environments/environment';
import { v4 as uuid } from 'uuid';
import { FilterChangeEvent, FilterTableComponent } from 'src/app/shared/table-module/filter-table/filter-table.component';
import Common from 'src/app/utils/common';
import { FILE_ACCEPT_STRING, LIFECYCLE_STAGE_DOC_MAP, STATUS_MAP } from 'src/app/models/constant/pms';
import { TableModule } from 'primeng/table';
import { SafeApiCallerService } from '../../../../services/SafeApiCaller.service';
import { ProductFileService } from 'src/app/services/pms/product-file/product-file.service';
import { FileUploadManagerService } from 'src/app/services/upload/file-upload-manager.service';
import { HasAnyAuthorityDirective } from '../../../../shared/directives/has-any-authority.directive';
import { InputTextModule } from 'primeng/inputtext';
import { ComboboxNonRSQLComponent } from 'src/app/shared/components/combobox-nonRSQL/combobox-nonRSQL.component';
import { TransferDetail } from 'src/app/models/interface/ptm/technology-transfer';
import { STATUS_TRANSFER_MAP } from 'src/app/models/constant/ptm';

@Component({
    selector: 'app-list',
    templateUrl: './list.component.html',
    standalone: true,
    imports: [
        TableCommonModule,
        ButtonModule,
        TagModule,
        CommonModule,
        RouterLink,
        DialogModule,
        SubHeaderComponent,
        DropdownModule,
        FormsModule,
        MenuModule,
        PopupComponent,
        FormCustomModule,
        TableCustomComponent,
        TableCellDirective,
        UploadCustomComponent,
        TableModule,
        HasAnyAuthorityDirective,
        InputTextModule,
        ComboboxNonRSQLComponent,
    ],
    styleUrls: ['./list.component.scss'],
    providers: [ProductFileService, TechnologyTransferService],
})
export class TechnologyTransferFormsListComponent implements OnInit, AfterViewInit {
    public STORAGE_BASE_URL = environment.STORAGE_BASE_URL;
    @ViewChild('SoftwareUploader') softwareUploader!: UploadCustomComponent;
    @ViewChild('DocUploader') docUploader!: UploadCustomComponent;
    @ViewChild('HistoryPopup', { static: true }) private historyPopup!: PopupComponent;
    @ViewChild('ListProfilePopup', { static: true }) private listProfilePopup!: PopupComponent;
    @ViewChild('EditPopup', { static: true }) editPopup!: PopupComponent;
    @ViewChild('SubmitChangeVersion', { static: true }) sChangeVersionPopup!: PopupComponent;
    @ViewChild('actionsTpl', { static: true }) actionsTpl!: TemplateRef<any>;
    @ViewChild('ActionMenu') actionMenu!: Menu;
    @ViewChild('pnFilter') pnFilter!: FilterTableComponent;
    @ViewChild('requestTimeValue') requestTimeValue: TemplateRef<Element>;
    @ViewChild('createdValue') createdValue: TemplateRef<Element>;
    @ViewChild('statusValue') statusValue: TemplateRef<Element>;
    // @ViewChild('TableCustom', { static: true }) private popup!: PopupComponent;
    @ViewChild('productLinesPopup', { static: false })
    productLinesPopup!: ComboboxNonRSQLComponent;
    @ViewChild('productIdsPopup', { static: false })
    productIdsPopup!: ComboboxNonRSQLComponent;
    @ViewChild('productSelect', { static: false })
    productSelect!: ComboboxNonRSQLComponent;
    @ViewChild('lineSelect', { static: false })
    lineSelect!: ComboboxNonRSQLComponent;
    @ViewChild('modelSelectFilter', { static: false })
    modelSelectFilter!: ComboboxNonRSQLComponent;
    @ViewChild('modelSelectPopup', { static: false })
    modelSelectPopup!: ComboboxNonRSQLComponent;
    // Injected services
    uploadService = inject(UploadService);
    fileUploadManager = inject(FileUploadManagerService);
    technologyTransferService = inject(TechnologyTransferService);
    safeCaller = inject(SafeApiCallerService);
    loadingService = inject(LoadingService);
    tableCommonService = inject(TableCommonService);
    alertService = inject(AlertService);
    fb = inject(FormBuilder);
    cd = inject(ChangeDetectorRef);
    productFileService = inject(ProductFileService);
    confirmationService = inject(ConfirmationService);
    tableVersionCols: Column[] = [
        { field: 'productName', header: 'Sản phẩm' },
        { field: 'vnptManPn', header: 'VNPT Man P/N' },
        { field: 'lifecycleStage', header: 'Giai đoạn sản phẩm' },
        { field: 'productVersionName', header: 'Version hồ sơ' },
        { field: 'lifeCycleStageSelect', header: 'Chọn giai đoạn tạo Draft' },
    ];
    dataTableVersionCols: any[] = [];
    stageKeys = Object.keys(LIFECYCLE_STAGE_DOC_MAP).map((k: number | string) => {
        return { name: LIFECYCLE_STAGE_DOC_MAP[k], id: Number(k) };
    });
    // Table state
    progress = -1;
    public accept = FILE_ACCEPT_STRING;
    fileNamePattern = /^[^_]+_[^_]+_[^_]+_[^_]+$/;
    state!: QueryObserverBaseResult<TransferDetail[]>;
    tableId: string = TABLE_KEY.SOFTWARE_RESOURCE;
    itemsHeader = [{ label: 'Phiếu CG CNSX' }, { label: 'Danh sách yêu cầu CG CNSX' }];
    columns: Column[] = [];
    fileUpload: File;
    visible: boolean = false;
    isButtonVisible: boolean = false;
    loadingSoftware: boolean = false;
    loadingInstruction: boolean = false;
    softwareResourceId: number;
    private uploadEntries = new Map<string, { sub: Subscription; uploadId: string }>();
    title: string = 'Thêm mới phần mềm hỗ trợ sản xuất';
    // Menu items for actions
    itemsAction: MenuItem[] = [];
    // Form for edit popup initialized at class instantiation
    formEditPopup: FormGroup;
    rootIdEdit: number;
    originalFormValue: ProductSoftware;
    filterForm!: FormGroup;
    // Danh sách option (sản phẩm) cho MultiSelect
    productsOptions: ProductDetail[] = [];
    // Danh sách sản phẩm (đối tượng đầy đủ) đang được chọn
    selectedProducts: ProductDetail[] = [];
    // Chỉ giữ mảng các mã VNPT để hiển thị ở khung bên cạnh
    selectedCodes: string[] = [];
    productOptions$: Observable<DropdownOption[]>;
    // Dropdown filters
    softwareOptions: DropdownOption[] = [];
    productOptions: DropdownOption[] = [];
    pnOptions: DropdownOption[] = [];
    trackCols: TrackColumn[] = [
        { field: 'time', header: 'Thời điểm thực hiện', width: '165px' },
        { field: 'versionName', header: 'Version', width: '80px' },
        { field: 'buildTime', header: 'Build time', width: '120px' },
        { field: 'action', header: 'Thao tác thay đổi' },
        { field: 'file', header: 'File' },
        { field: 'user', header: 'Người thực hiện' },
        { field: 'userGuide', header: 'Hướng dẫn vận hành' },
        { field: 'productLine', header: 'Dòng sản phẩm' },
        { field: 'model', header: 'Model' },
        { field: 'details', header: 'Hồ sơ áp dụng' },
    ];

    trackData = [];
    profileCols: TrackColumn[] = [
        { field: 'product', header: 'Sản phẩm' },
        { field: 'vnptManPn', header: 'VNPT Man P/N' },
        { field: 'versionName', header: 'Version hồ sơ' },
        { field: 'status', header: 'Trạng thái' },
        { field: 'action', header: 'Hành động' },
    ];
    selectedRowsToSubmit: UpdateVersionDto[] = [];
    profileData: any[] = [];
    selectedSoftware: any = null;
    selectedProduct: any = null;
    selectedPN: any = null;
    newFileName: string;
    firstTimeFlags = new WeakMap();
    selectedLineId: number | null = null;
    STATUS_TRANSFER_MAP = STATUS_TRANSFER_MAP;
    private sub: Subscription;
    private destroy$ = new Subject<void>();

    onSearch() {
        // Gọi API hoặc filter dữ liệu dựa trên các giá trị đã chọn
        this.tableCommonService.updateFilter(this.tableId, {
            productIds: this.filterForm.get('productId')?.value,
            lineIDs: this.filterForm.get('productLine')?.value,
            modelIDs: this.filterForm.get('model')?.value,
        });
    }

    ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }

    get productParams() {
        return {
            size: 100,
            // chỉ thêm lineId khi đã chọn (non-null và >0)
            modelId: this.filterForm.get('model')?.value || null,
            lineId: this.filterForm.get('productLine')?.value || null,
        };
    }

    ngOnInit() {
        this.initFormPopup();

        this.tableCommonService
            .init<TransferDetail>({
                tableId: this.tableId,
                queryFn: (filter) => this.technologyTransferService.getPageOverwrite(filter),
            })
            .pipe(takeUntil(this.destroy$))
            .subscribe((state) => {
                const transformedData = state.data.map((item, index) => ({
                    stt: index + 1 + (state.pagination.page - 1) * 10,
                    ...item,
                }));
                this.state = {
                    ...state,
                    data: transformedData,
                };
            });
        this.tableCommonService.updateFilterPageable(this.tableId, {
            sort: undefined,
        });
        // 2. Lắng nghe mỗi khi giá trị chọn ở MultiSelect thay đổi
        this.sub = this.filterForm
            .get('products')!
            .valueChanges.pipe(takeUntil(this.destroy$))
            .subscribe((products: ProductDetail[]) => {
                this.selectedProducts = products || [];
                // Cập nhật mảng mã VNPT tương ứng
                this.selectedCodes = this.selectedProducts.map((p) => p.vnptManPn);
            });
        this.fileUploadManager.isUploading$.pipe(takeUntil(this.destroy$)).subscribe((isUploading) => {
            if (isUploading) {
                this.formEditPopup.setErrors({ uploading: true });
            } else {
                this.formEditPopup.setErrors(null);
            }
        });
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.columns = [
                { field: 'stt', header: 'STT' },
                { field: 'requestTime', body: this.requestTimeValue, header: 'Ngày yêu cầu' },
                { field: 'vnptManPn', header: 'VNPT Man P/N', default: true },
                { field: 'workOrderCode', header: 'Mã lệnh sản xuất' },
                { field: 'instructionNameVersion', header: 'Version CNSX' },
                { field: 'created', header: 'Ngày tạo phiếu', body: this.createdValue, default: true },
                { field: 'creator', header: 'Người tạo phiếu', default: true },
                { field: 'receiver', header: 'Người nhận phiếu', default: true },
                { field: 'status', header: 'Trạng thái phiếu', body: this.statusValue, default: true },
                {
                    field: 'action',
                    header: 'Thao tác',
                    style: { width: '8rem', textAlign: 'center', whiteSpace: 'nowrap' },
                    bodyWrapper: this.actionsTpl,
                },
            ];
        }, 0);
    }

    initFormPopup() {
        this.filterForm = this.fb.group({
            productName: [null],
            products: [null],
            productLine: [null],
            model: [null],
            productId: [null],
        });
        this.formEditPopup = this.fb.group({
            id: [null],
            workOrderCode: [null],
            vnptManPn: [null],
            versionId: [null, Validators.required],
            receiver: [null, Validators.required],
            note: [null],
            requestTime: [null],
            nameProduct: [null],
            instructionNameVersion: [null],
            creator: [null],
            created: [null],
        });
    }

    onProductLineSelect(event: FilterChangeEvent) {
        this.selectedLineId = event.value as number;
        this.filterForm.get('productId')!.reset();
        this.filterForm.get('model')!.reset();
        // this.modelSelectFilter.additionalParams = {
        //     ...this.modelSelectFilter.additionalParams,
        //     size: 100,
        //     page: 0,
        //     unpaged: false,
        //     productLineId: this.filterForm.get('productLine').value,
        // };
        // this.modelSelectFilter.fetchOptions(null);
        this.filterForm.get('productId')!.reset(null, { emitEvent: false });
        this.filterForm.get('model')!.reset(null, { emitEvent: false });
        this.modelSelectFilter.additionalParams = {
            ...this.modelSelectFilter.additionalParams,
            size: 100,
            page: 0,
            unpaged: false,
            productLineId: this.filterForm.get('productLine').value,
        };
        this.modelSelectFilter.fetchOptions(null);
    }

    onChangeModel(event: any) {
        this.filterForm.get('productId')!.reset();
        this.productSelect.additionalParams = {
            ...this.productSelect.additionalParams,
            size: 100,
            page: 0,
            unpaged: false,
            modelId: this.filterForm.get('model')?.value || null,
            lineId: this.filterForm.get('productLine')?.value || null,
        };
        // this.productSelect.fetchOptions(null);
    }

    handlePanelShow(ref: any) {
        if (!this.firstTimeFlags.has(ref)) {
            this.firstTimeFlags.set(ref, true);
        }

        ref.additionalParams = {
            ...ref.additionalParams,
            size: 100,
            page: 0,
            unpaged: false,
        };

        ref.fetchOptions(null);
    }

    deleteSingle = (row) => {
        if (!row.allowDelete) {
            this.alertService.error('Không được phép', 'Phần mềm không thể xóa vì đang được sử dụng trong hồ sơ sản phẩm');
            return;
        }
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.technologyTransferService.delete(row.id).subscribe({
                    next: () => {
                        this.alertService.success('Thành công', 'Xóa phần mềm thành công');
                        // reload dữ liệu
                        this.state.refetch();
                    },
                    error: (err) => {
                        console.error('Delete error:', err);
                    },
                });
            },
        });
        return;
    };

    onSelectFile = (e) => {
        const file = e.target.files[0];
        e.target.value = null;
        if (!file.type?.includes('vnd.ms-excel') && !file.type?.includes('spreadsheetml')) {
            this.alertService.error('Lỗi import', 'File import sai định dạng, vui lòng thử lại với file excel');
            this.fileUpload = null;
        } else {
            this.fileUpload = file;
        }
    };

    openEditDialog(row: any, type: string) {
        if (type === 'edit') {
            this.title = 'Cập nhật phiếu';
            this.formEditPopup.patchValue({
                id: row.id,
                workOrderCode: row.workOrderCode,
                vnptManPn: row.vnptManPn,
                versionId: [null, Validators.required],
                receiver: [null, Validators.required],
                note: [null],
                requestTime: row.requestTime,
                nameProduct: [null],
                instructionNameVersion: row.instructionNameVersion,
                creator: row.creator,
                created: row.created,
            });
            this.originalFormValue = this.formEditPopup.getRawValue();
        } else {
            this.formEditPopup.patchValue({
                id: row.id,
                workOrderCode: row.workOrderCode,
                vnptManPn: row.vnptManPn,
                versionId: [null, Validators.required],
                receiver: [null, Validators.required],
                note: [null],
                requestTime: row.requestTime,
                nameProduct: [null],
                instructionNameVersion: row.instructionNameVersion,
                creator: row.creator,
                created: row.created,
            });
            this.title = 'Tạo phiếu';
        }

        this.editPopup.openDialog(row);
    }

    viewHistory(row: any) {
        this.technologyTransferService.getVersionsByRootId(row.rootId).subscribe({
            next: (res) => {
                this.trackData = res.map((v) => ({
                    id: v.id,
                    time: v.created != null ? Common.formatAmPmDate(v.created) : '',
                    versionName: v.versionName ?? '',
                    buildTime: v.buildTime ?? '',
                    action: v.id === v.rootId ? 'Tạo mới' : 'Chỉnh sửa',
                    file: v.name,
                    user: v.id === v.rootId ? v.createdBy : v.updatedBy,
                    productLine: v.productLineName,
                    model: v.productModelName,
                    details: 'Xem danh sách',
                    userGuide: v.userGuide,
                    instructionName: v.instructionName,
                }));
            },
            error: (err) => {
                console.error('Lỗi ', err);
            },
        });

        this.historyPopup.openDialog(row);

        // fetch và hiển thị lịch sử…
    }

    onMenuClick(event: MouseEvent, row: any) {
        this.itemsAction = [
            {
                label: 'Tạo phiếu',
                icon: 'pi pi-plus',
                command: () => this.openEditDialog(row, 'create'),
                authorities: ['ROLE_SYSTEM_ADMIN', 'software_delete'],
            },
            {
                label: 'Xem chi tiết/Chỉnh sửa',
                icon: 'pi pi-pencil',
                command: () => this.openEditDialog(row, 'edit'),
                authorities: ['ROLE_SYSTEM_ADMIN', 'software_update'],
            },
            {
                label: 'Gửi xác nhận',
                icon: 'pi pi-check',
                command: () => this.viewHistory(row),
                authorities: ['ROLE_SYSTEM_ADMIN', 'software_history'],
            },
        ];
        this.actionMenu.toggle(event);
    }

    submitFormEdit(event: EventPopupSubmit<ProductSoftware>) {
        if (this.formEditPopup.get('name')?.value.trim() === '') {
            this.alertService.error('Lỗi', 'Tên phần mềm không được để trống');
            return;
        }

        if (this.formEditPopup.get('buildTime')?.value.trim() === '') {
            this.alertService.error('Lỗi', 'Build time không được để trống');
            return;
        }

        if (this.formEditPopup.get('versionName')?.value.trim() === '') {
            this.alertService.error('Lỗi', 'Version không được để trống');
            return;
        }

        // 1. Lấy dữ liệu từ event.data và/hoặc form controls
        const fileName = this.formEditPopup.get('file')?.value;
        const name = this.formEditPopup.get('name')?.value.trim();
        const versionName = this.formEditPopup.get('versionName')?.value.trim();
        const buildTime = this.formEditPopup.get('buildTime')?.value.trim();
        const md5 = this.formEditPopup.get('md5')?.value;
        const path = this.formEditPopup.get('path')?.value;
        const userGuide = this.formEditPopup.get('userGuide')?.value;
        const instructionName = this.formEditPopup.get('instructionName')?.value;
        const productIds = this.formEditPopup.get('productIds')?.value;
        const productLines = this.formEditPopup.get('productLines')?.value;

        // 2. Xây payload theo đúng interface ProductSoftware
        const payload: ProductSoftware = {
            name: fileName,
            versionName,
            buildTime,
            md5,
            path,
            userGuide,
            instructionName,
            softwareName: name,
            productIds,
            productLines,
        };

        // 3. Gọi API create và subscribe kết quả
        let isExistId = this.formEditPopup.get('id')?.value;
        this.safeCaller.call({
            apiCall: () => (isExistId ? this.technologyTransferService.update(isExistId, payload) : this.technologyTransferService.create(payload)),
            onSuccess: (res) => {
                if (isExistId) {
                    this.softwareResourceId = res.id;
                    this.alertService.success('Thành công', 'Cập nhật phần mềm thành công');

                    this.tableCommonService.updateFilter(this.tableId, {
                        productIds: null,
                        lineIDs: null,
                        modelIDs: null,
                    });
                    this.filterForm.get('productId')!.reset();
                    this.filterForm.get('model')!.reset();
                    this.filterForm.get('productLine')!.reset();
                    // document v.1.1.1- them buoc check thay doi version

                    if (this.hasChangeVersion(this.originalFormValue, this.formEditPopup.getRawValue())) {
                        this.handlerAfterChangeVer(this.rootIdEdit);
                    }
                    this.editPopup.closeDialog();
                } else {
                    if (res.existedWithThisName) {
                        this.alertService.warning('Lưu ý: Tên phần mềm đã tồn tại!');
                    } else {
                        this.alertService.success('Thành công', 'Thêm mới phần mềm thành công');
                    }
                    this.editPopup.closeDialog();
                    this.tableCommonService.updateFilter(this.tableId, {
                        productIds: null,
                        lineIDs: null,
                        modelIDs: null,
                    });
                    this.filterForm.get('productId')!.reset();
                    this.filterForm.get('model')!.reset();
                    this.filterForm.get('productLine')!.reset();
                }
            },
            onError: (err) => {
                console.error('Lỗi khi cập nhật:', err);
            },
        });
    }

    openProfileList(row: any) {
        // TODO: load data từ API theo row.id

        this.technologyTransferService.getProfiles(row.id).subscribe({
            next: (res) => {
                this.profileData = res.map((v) => ({
                    productVersion: v.productVersionId || 0,
                    productId: v.productId || 0,
                    product: v.productName || '',
                    vnptManPn: v.vnptManPn || '',
                    versionName: v.productVersionName || '',
                    status: STATUS_MAP[v.status] || 'Draft',
                }));
            },
            error: (err) => {
                console.error('Lỗi ', err);
            },
        });
        this.listProfilePopup.openDialog();
    }

    async handleChangeFile(files: File[], type: string) {
        if (!files?.length) return;
        const uploadId = `${type}-${uuid()}`;
        // Đăng ký start

        this.fileUploadManager.start(uploadId, () => {
            // callback khi cancel() được gọi
            const entry = this.uploadEntries.get(type);
            entry?.sub.unsubscribe();
            this.uploadEntries.delete(type);
        });

        const file = files[0];
        const md5 = await md5File(file);
        // const fullName = file.name.replace(/\.[^/.]+$/, ''); // bỏ đuôi
        // const parts = fullName.split('_');

        // Nếu là SOFTWARE_RESOURCE thì patch thêm vào form
        if (type === 'SOFTWARE_RESOURCE') {
            // const [name, versionName, buildTime] = parts;
            // if (![name, versionName, buildTime].every(Boolean)) {
            //     this.alertService.error('Tên file không hợp lệ:', file.name);
            //     this.fileUploadManager.finish(uploadId);
            //     return;
            // }
            // Kiểm tra buildTime chỉ là chữ số
            // if (!/^\d+$/.test(buildTime)) {
            //     this.alertService.error('Buildtime chỉ được chứa kí tự số:', buildTime);
            //     this.softwareUploader.clearAll();
            //     this.fileUploadManager.finish(uploadId);
            //     return;
            // }
            this.loadingSoftware = true;
            this.formEditPopup.patchValue({ file: file.name, md5 });
        } else {
            this.loadingInstruction = true;
        }
        // đánh dấu form đang lỗi (invalid)
        this.formEditPopup.setErrors({ uploading: true });
        // Phần upload chung cho cả hai type
        // --- start upload chain ---
        const obs$ = this.uploadService.analyzeFile(file.name, type).pipe(
            tap((meta) => {
                if (type === 'SOFTWARE_RESOURCE') {
                    this.formEditPopup.patchValue({
                        name: meta.softwareName,
                        buildTime: meta.buildTime,
                        versionName: meta.version,
                        path: meta.objectPath,
                    });
                } else if (type === 'SOFTWARE_INSTRUCTION') {
                    this.formEditPopup.patchValue({ userGuide: meta.objectPath, instructionName: meta.fileName });
                }
                this.formEditPopup.setErrors({ uploading: true });
            }),
            switchMap((meta: AnalyzeResp) => this.uploadService.uploadToPresignedUrl(file, meta.presignedUrl)),
            finalize(() => {
                this.fileUploadManager.finish(uploadId);
            }),
        );
        const sub = obs$.subscribe({
            next: (p) => (this.progress = p),
            error: (e) => this.cleanupAfterFinish(type),
            complete: () => {
                this.cleanupAfterFinish(type);
                if (type === 'SOFTWARE_RESOURCE') {
                    this.loadingSoftware = false;
                } else if (type === 'SOFTWARE_INSTRUCTION') {
                    this.loadingInstruction = false;
                }
            },
        });

        this.uploadEntries.set(type, { sub, uploadId });
    }

    handleClearFile(type: string) {
        const entry = this.uploadEntries.get(type);
        if (entry) {
            // đây mới là uploadId chính xác
            this.fileUploadManager.cancel(entry.uploadId);
            // subscription cũng sẽ được unsubscribe trong callback start()
            this.uploadEntries.delete(type);
        }
        if (type === 'SOFTWARE_RESOURCE') {
            this.formEditPopup.patchValue({
                file: null,
                name: null,
                versionName: null,
                buildTime: null,
                path: null,
                md5: null,
            });
        } else if (type === 'SOFTWARE_INSTRUCTION') {
            this.formEditPopup.patchValue({
                userGuide: null,
                instructionName: null,
            });
        }

        // và reset progress nếu muốn

        this.progress = -1;
        if (this.uploadEntries.size === 0) {
            this.formEditPopup.setErrors(null);
        } else {
            this.formEditPopup.setErrors({ uploading: true });
        }
    }

    private cleanupAfterFinish(type: string) {
        if (this.uploadEntries.has(type)) {
            this.uploadEntries.delete(type);
        }
        if (this.uploadEntries.size === 0) {
            this.formEditPopup.setErrors(null);
        }
    }

    // handler cho nút Hủy của popup
    handlePopupClose() {
        // 1. reset toàn bộ form
        this.formEditPopup.reset();
        // 2. clear file trên từng upload-custom
        this.softwareUploader.clearAll();
        this.docUploader.clearAll();
    }

    handlerAfterChangeVer(id: number) {
        this.technologyTransferService.getApprovedDocuments(id).subscribe({
            next: (res) => {
                if (res.length > 0) {
                    // phan nay máp lai vi thang BE tra fields luc nay, luc kia
                    this.dataTableVersionCols = res.map((v) => ({
                        ...v,
                        lifeCycleStage: v.lifecycleStage,
                        lifeCycleStageSelect: v.lifecycleStage,
                    }));
                    this.sChangeVersionPopup.openDialog();
                }
            },
            error: (err) => {
                this.alertService.error('Lỗi', err.message);
            },
        });
    }

    mapLifecycle(lifecycleId: number) {
        return this.stageKeys.find((s) => s.id === lifecycleId)?.name;
    }

    submitChangeVersion() {
        const params = this.selectedRowsToSubmit.map((r) => {
            return {
                productVersionId: r.productVersionId,
                softwareResourceId: this.softwareResourceId,
                lifeCycleStage: r.lifeCycleStageSelect,
            };
        });
        this.safeCaller.call({
            apiCall: () => this.technologyTransferService.changeVersion(params),
            onSuccess: () => {
                this.alertService.success('Thành công', 'Chuyển version thành công');
                this.sChangeVersionPopup.closeDialog();
            },
            onError: (err) => {
                this.alertService.error('Lỗi', err.message);
            },
        });
    }

    hasChangeVersion(original: ProductSoftware, currentForm: ProductSoftware) {
        return original.path !== currentForm.path;
    }

    closeChangeVersionPopup() {
        this.selectedRowsToSubmit = []; // resetTableSelectedRows
    }

    downloadFile(userGuide: any) {
        console.log(userGuide);
    }
}
