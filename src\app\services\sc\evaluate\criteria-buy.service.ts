import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import {CriteriaBuy} from '../../../models/interface/sc';

@Injectable({
    providedIn: 'root',
})
export class CriteriaBuyService extends BaseService<CriteriaBuy> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/criteria-buy');
    }
}
