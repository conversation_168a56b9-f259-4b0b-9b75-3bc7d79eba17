import { canAuthorize } from '../../../core/auth/auth.guard';

export const RequestShippingRouting = {
    path: 'request-shipping',
    title: 'Quản lý Yêu cầu vận chuyển',
    canActivate: [canAuthorize],
    data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_request_shipping_view', 'sc_request_shipping_edit'] },
    children: [
        {
            path: '',
            title: '<PERSON>h sách yêu cầu vận chuyển',
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_request_shipping_view', 'sc_request_shipping_edit'] },
            loadComponent: () => import('./list/list.component').then((c) => c.ListComponent),
        },
        {
            path: ':id',
            title: 'Chi tiết yêu cầu vận chuyển',
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_request_shipping_view', 'sc_request_shipping_edit'] },
            loadComponent: () => import('./detail/detail.component').then((c) => c.DetailComponent),
        },
        {
            path: 'create',
            title: 'Tạo mới yêu cầu vận chuyển',
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_request_shipping_view', 'sc_request_shipping_edit'] },
            loadComponent: () => import('./detail/detail.component').then((c) => c.DetailComponent),
        },
    ],
};
