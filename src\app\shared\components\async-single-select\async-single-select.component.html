<p-autoComplete
    [(ngModel)]="selected"
    [suggestions]="options"
    [field]="displayField"
    [placeholder]="placeholder"
    [minLength]="minLength"
    [delay]="delay"
    [dropdown]="true"
    [showClear]="true"
    [virtualScroll]="virtualScroll"
    [lazy]="lazy"
    [virtualScrollItemSize]="virtualScrollItemSize"
    (completeMethod)="onSearch($event)"
    (onLazyLoad)="onLazy($event)"
    (onSelect)="onSelect($event)"
    (onClear)="onClear()"
    appendTo="body"
    [disabled]="disabled"
    [showEmptyMessage]="true"
>
    <ng-template pTemplate="empty">
        <div class="p-autocomplete-empty-message tw-text-center"><PERSON>h<PERSON>ng tìm thấy kết quả</div>
    </ng-template>
</p-autoComplete>
