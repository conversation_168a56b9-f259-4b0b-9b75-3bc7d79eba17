import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import {CanExportDTO, CanImportDTO, PoDetail, PoDetailDTO} from 'src/app/models/interface/sc';
import { GeneralEntity } from 'src/app/models/interface';

@Injectable()
export class PoDetailService extends BaseService<PoDetail> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/po-detail');
    }

    getCanImport(body: { poId: number; accountingCodes?: string[] }) {
        return this.http.post<CanImportDTO[]>(`/sc/api/po-detail/can-import`, body);
    }

    getCanExport(body: { poId: number; accountingCodes?: string[] }) {
        return this.http.post<CanExportDTO[]>(`/sc/api/po-detail/can-export`, body);
    }

    exportDetailOfPo(poId: number) {
        return this.http.get<GeneralEntity>(`/sc/api/po-detail/export-po-detail?poId=${poId}`);
    }

    getPoDetailByPo(poId: number) {
        return this.http.get<PoDetailDTO>(`/sc/api/po-detail/get-po-detail-by-po?poId=${poId}`);
    }
}
