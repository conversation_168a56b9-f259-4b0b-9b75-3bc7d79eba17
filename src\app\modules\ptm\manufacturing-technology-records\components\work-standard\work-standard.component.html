<ng-container>
    <div class="p-fluid tw-space-y-2">
        <app-info-share
            [title]="title"
            (changeValueApprover)="handleApproverChange($event)"
            [name]="nameTab"
            [detailInfo]="detailWorkStandard"
        ></app-info-share>
    </div>
    <div class="p-fluid tw-space-y-2">
        <div class="tw-flex tw-justify-between tw-border-b-2">
            <label class="label-tab-cnsx">Chi tiết Work Standard</label>
            <div class="tw-flex tw-space-x-2">
                <p-button label="Track Changes" size="small" (click)="openTrackDialog()"></p-button>
                <p-button label="Xuất excel" size="small" (click)="exportWorkStandard()"></p-button>
            </div>
        </div>
    </div>
    <div #container>
        <p-panel [toggleable]="true">
            <ng-template pTemplate="icons">
                <button
                    pButton
                    type="button"
                    class="p-button-outlined p-button-sm toggle-btn pi pi-arrows-alt"
                    appFullscreenToggle
                    [target]="container"
                ></button>
            </ng-template>

            <div class="tw-mb-6" *ngFor="let group of groupedWorkStandards ; let groupIndex = index">
                <div class="tw-font-bold "> {{ group.lineName }}</div>
                <div
                    class="tw-flex tw-flex-col tw-p-4 tw-justify-center tw-border-[1px] tw-border-solid tw-border-gray-300 tw-rounded-md">
                    <app-form-item label="" class="tw-w-full">
                        <app-form #form [formGroup]="formGroup" layout="vertical">
                            <div formArrayName="workStandards">
                                <p-table styleClass="p-datatable-gridlines" [value]="group.items.controls">
                                    <ng-template pTemplate="header">
                                        <tr>
                                            <th style="min-width: 9rem ;text-align: center ">STT</th>
                                            <th *ngFor="let col of columns"
                                                [style.minWidth]="col.minWidth"
                                                [style.text-align]="'center'"
                                                [style.maxWidth]="col.maxWidth">
                                                {{ col.header }}
                                            </th>
                                            <th style=" text-align: center">Thao tác</th>
                                        </tr>
                                    </ng-template>
                                    <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                                        <tr [formGroupName]="rowIndex">
                                            <td>
                                                <app-form-item label=""> {{ rowIndex + 1 }}</app-form-item>
                                            </td>

                                            <td>
                                                <app-form-item label="">
                                                    {{ item.get('oc').value }}
                                                </app-form-item>
                                            </td>
                                            <td>
                                                <app-form-item label="">
                                                    {{ item.get('nextOc').value }}
                                                </app-form-item>
                                            </td>
                                            <td>
                                                <app-form-item label="">
                                                    {{ item.get('od').value }}
                                                </app-form-item>
                                            </td>
                                            <td>
                                                <app-form-item label="">
                                                    {{ getLineLabel(item.get('line')?.value) }}
                                                </app-form-item>
                                            </td>

                                            <td class="tw-flex tw-justify-center">
                                                <button pButton type="button"
                                                        (click)="openPanel(groupIndex, rowIndex, op, $event)"
                                                        label="Thêm/sửa chi tiết"></button>

                                                <!-- Overlay Panel gắn vào đây -->
                                                <p-overlayPanel #op [dismissable]="false" [showCloseIcon]="true"
                                                                [style]="{ width: '90vw' }" appendTo="body">
                                                    <form [formGroup]="item">
                                                        <div formArrayName="workStandardDetails">
                                                            <p-table styleClass="p-datatable-gridlines"
                                                                     [value]="getVisibleDetails(item.get('workStandardDetails'))">
                                                                <!-- HEADER -->
                                                                <ng-template pTemplate="header">
                                                                    <tr>
                                                                        <th rowspan="2">STT</th>

                                                                        <!-- Hàng 1: Nhóm cột -->
                                                                        <ng-container
                                                                            *ngFor="let group of getColumnGroups()">
                                                                            <th style="text-align: center"
                                                                                [attr.colspan]="group.colSpan"
                                                                                *ngIf="group.name">
                                                                                {{ group.name }}
                                                                            </th>
                                                                            <th style="text-align: center"
                                                                                *ngIf="!group.name"
                                                                                rowspan="2">
                                                                                {{ group.headers[0].header }}
                                                                            </th>
                                                                        </ng-container>
                                                                        <th style="text-align: center" rowspan="2">
                                                                            <button pButton icon="pi pi-plus"
                                                                                    (click)="addRow(groupIndex,rowIndex)"
                                                                                    class="p-button-text"></button>
                                                                        </th>
                                                                    </tr>

                                                                    <tr>
                                                                        <!-- Hàng 2: Header con nếu có nhóm -->
                                                                        <ng-container
                                                                            *ngFor="let group of getColumnGroups()">
                                                                            <ng-container *ngIf="group.name">
                                                                                <th style="text-align: center"
                                                                                    *ngFor="let col of group.headers">{{ col.header }}
                                                                                </th>
                                                                            </ng-container>
                                                                        </ng-container>
                                                                    </tr>
                                                                </ng-template>

                                                                <!-- BODY -->
                                                                <ng-template pTemplate="body" let-row let-i="rowIndex">
                                                                    <tr [formGroupName]="i"
                                                                        *ngIf="row.get('action')?.value !== 3">
                                                                        <td>
                                                                            {{ rowIndex + 1 }}.{{ i + 1 }}
                                                                        </td>
                                                                        <td>
                                                                            <app-form-item label="">
                                                                                <input pInputText
                                                                                       formControlName="od"
                                                                                       maxlength="200"
                                                                                />
                                                                            </app-form-item>
                                                                        </td>

                                                                        <td>
                                                                            <app-form-item label=""
                                                                                           validateTrigger="touched">
                                                                                <input
                                                                                    pInputText
                                                                                    formControlName="inTime"
                                                                                    maxlength="10"

                                                                                />
                                                                            </app-form-item>
                                                                        </td>

                                                                        <td>
                                                                            <app-form-item label=""
                                                                                           validateTrigger="touched">
                                                                                <input
                                                                                    pInputText
                                                                                    formControlName="laborTime"
                                                                                    maxlength="10"
                                                                                />
                                                                            </app-form-item>
                                                                        </td>

                                                                        <td>
                                                                            <app-form-item label=""
                                                                                           validateTrigger="touched">
                                                                                <input pInputText
                                                                                       formControlName="machineTime"
                                                                                       maxlength="10"
                                                                                />
                                                                            </app-form-item>
                                                                        </td>

                                                                        <td>
                                                                            <app-form-item label=""
                                                                                           validateTrigger="touched">
                                                                                <input pInputText
                                                                                       formControlName="outTime"
                                                                                       maxlength="10"
                                                                                />
                                                                            </app-form-item>
                                                                        </td>

                                                                        <td>
                                                                            <app-form-item label="">
                                                                                <p-dropdown
                                                                                    [options]="listOptionEquip"
                                                                                    formControlName="equipId"
                                                                                    optionLabel="machineName"
                                                                                    optionValue="id"
                                                                                    appendTo="body"
                                                                                />
                                                                            </app-form-item>
                                                                        </td>

                                                                        <td>
                                                                            <app-form-item label=""
                                                                                           validateTrigger="touched">
                                                                                <input pInputText
                                                                                       formControlName="equipQty"
                                                                                       maxlength="10"
                                                                                />
                                                                            </app-form-item>
                                                                        </td>

                                                                        <td>
                                                                            <app-form-item label="">
                                                                                <p-dropdown
                                                                                    [options]="listOptionConsumable"
                                                                                    formControlName="consumableMaterialPnId"
                                                                                    optionLabel="vnptPn"
                                                                                    optionValue="id"
                                                                                    appendTo="body"
                                                                                    filter="true"
                                                                                    (onChange)="handleChangeConsumable($event, rowIndex,row )"
                                                                                />
                                                                            </app-form-item>
                                                                        </td>

                                                                        <td>
                                                                            <app-form-item label="">
                                                                                {{ row.get('consumableDescription')?.value || '' }}
                                                                            </app-form-item>
                                                                        </td>

                                                                        <td>
                                                                            <app-form-item label=""
                                                                                           validateTrigger="touched">
                                                                                <input pInputText
                                                                                       formControlName="qty"
                                                                                       maxlength="10"
                                                                                />
                                                                            </app-form-item>
                                                                        </td>

                                                                        <td>
                                                                            <app-form-item label="">
                                                                                <input pInputText
                                                                                       formControlName="unit" />
                                                                            </app-form-item>
                                                                        </td>

                                                                        <td *ngIf="i === 0"
                                                                            [attr.rowspan]="9999">
                                                                            <app-form-item label=""
                                                                                           validateTrigger="touched">
                                                                                <input pInputText
                                                                                       [formControl]="item.get('laborAllowance')"
                                                                                       maxlength="5"
                                                                                />
                                                                            </app-form-item>
                                                                        </td>

                                                                        <td *ngIf="i === 0"
                                                                            [attr.rowspan]="9999">
                                                                            <app-form-item label=""
                                                                                           validateTrigger="touched">
                                                                                <input pInputText
                                                                                       [formControl]="item.get('laborUtilization')"
                                                                                       maxlength="5" />
                                                                            </app-form-item>
                                                                        </td>

                                                                        <td *ngIf="i === 0"
                                                                            [attr.rowspan]="9999">
                                                                            <app-form-item label=""
                                                                                           validateTrigger="touched">
                                                                                <input pInputText
                                                                                       [formControl]="item.get('machineUtilization')"
                                                                                       maxlength="5" />
                                                                            </app-form-item>
                                                                        </td>
                                                                        <td *ngIf="i === 0"
                                                                            [attr.rowspan]="9999">
                                                                            <app-form-item label=""
                                                                                           validateTrigger="touched">
                                                                                <input pInputText
                                                                                       [formControl]="item.get('btp')"
                                                                                       maxlength="5"
                                                                                />
                                                                            </app-form-item>
                                                                        </td>
                                                                        <td *ngIf="i === 0"
                                                                            [attr.rowspan]="9999">
                                                                            <app-form-item label="">
                                                                                {{ item.get('laborTime').value }}
                                                                            </app-form-item>
                                                                        </td>
                                                                        <td *ngIf="i === 0"
                                                                            [attr.rowspan]="9999">
                                                                            <app-form-item label="">
                                                                                {{ item.get('machine').value }}
                                                                            </app-form-item>
                                                                        </td>
                                                                        <td>
                                                                            <button pButton icon="pi pi-minus"
                                                                                    (click)="removeRow(groupIndex,rowIndex,i)"
                                                                                    class="p-button-text text-red-500"></button>
                                                                        </td>
                                                                    </tr>
                                                                </ng-template>
                                                            </p-table>
                                                        </div>
                                                    </form>
                                                    <div class="tw-flex tw-justify-center tw-mt-2">
                                                        <button pButton label="Lưu" class="tw-mr-2"
                                                                (click)="savePanel(groupIndex, rowIndex, op)"></button>
                                                        <button pButton label="Đóng" class="tw-mr-2"
                                                                (click)="cancelPanel(groupIndex, rowIndex, op)"></button>
                                                    </div>
                                                </p-overlayPanel>
                                            </td>

                                        </tr>
                                    </ng-template>
                                </p-table>
                            </div>
                        </app-form>
                    </app-form-item>
                    <div class="tw-flex tw-justify-between tw-border-b-2 tw-mt-4 ">
                        <div>
                            <p-button label="Xuất excel" size="small"
                                      (click)="exportWorkStandardLine(group.line, group.lineName)"></p-button>
                        </div>
                        <div class="tw-flex tw-flex-col tw-space-y-4 tw-w-[300px]">
                            <div>
                                Total Labor Time(s):{{ calculateWorkStandardGroupTotals(groupIndex).totalLabor }}
                            </div>
                            <div>
                                Total Machine Time(s): {{ calculateWorkStandardGroupTotals(groupIndex).totalMachine }}
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            <div class="tw-flex tw-gap-4 tw-justify-center tw-mt-4">
                <p-button
                    *ngIf="detailWorkStandard?.instructionInfo?.status === 1 ||
             detailWorkStandard?.instructionInfo?.status === 4 ||
             this.productInstructionId === 0"
                    label="Lưu"
                    type="submit"
                    [loading]="isSaving | async"
                    (onClick)="handleSubmit()"
                    loadingIcon="pi pi-spinner pi-spin"
                >
                </p-button>
                <button
                    *ngIf="
                            detailWorkStandard?.instructionInfo?.status === 1 || detailWorkStandard?.instructionInfo?.status === 4 || this.productInstructionId === 0
                        "
                    label="Gửi Preview"
                    pButton
                    type="button"
                    [disabled]="isApproving | async"
                    loadingIcon="pi pi-spinner pi-spin"
                    class="p-button-secondary"
                    (click)="handlePreview()"
                ></button>
                <p-button
                    *ngIf="
                            detailWorkStandard?.instructionInfo?.status !== 1 && detailWorkStandard?.instructionInfo?.status !== 4 && this.productInstructionId === 0
                        "
                    label="Phê duyệt"
                    type="button"
                    [disabled]="isApproving | async"
                    [loading]="isApproving | async"
                    loadingIcon="pi pi-spinner pi-spin"
                    (click)="handleComplete()"
                >
                </p-button>
                <button
                    *ngIf="
                            detailWorkStandard?.instructionInfo?.status !== 1 && detailWorkStandard?.instructionInfo?.status !== 4 && this.productInstructionId === 0
                        "
                    label="Từ chối"
                    pButton
                    type="button"
                    [disabled]="isApproving | async"
                    loadingIcon="pi pi-spinner pi-spin"
                    class="p-button-danger"
                    (click)="handleReject()"
                ></button>
            </div>
        </p-panel>
    </div>
    <ng-container *ngIf="showTrackDialog">
        <app-track-changes
            [visible]="showTrackDialog"
            [recordId]="selectedTab"
            [idInstruction]="productInstructionId"
            (closed)="handleCloseTrackDialog()"
        ></app-track-changes>
    </ng-container>

</ng-container>

