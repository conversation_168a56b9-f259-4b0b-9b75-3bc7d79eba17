import { Injectable } from '@angular/core';
import { HttpRequest, HttpHandler, HttpEvent, HttpInterceptor } from '@angular/common/http';

import { AuthService } from 'src/app/core/auth/auth.service';
import { Observable } from 'rxjs';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
    constructor(private authService: AuthService) {}

    intercept(req: HttpRequest<unknown>, next: <PERSON>ttpHandler): Observable<HttpEvent<unknown>> {
        if (req.url.includes('auth/token')) return next.handle(req);
        const token = this.authService.getToken();

        if (token) {
            req = req.clone({
                headers: req.headers.append('Authorization', `Bearer ${token}`),
            });
        }

        return next.handle(req);
    }
}
