import { CdkDragDrop } from '@angular/cdk/drag-drop';
import { Injectable } from '@angular/core';

@Injectable()
export class DragDropService {
    public dropElement(event: CdkDragDrop<string[]>, dataList: unknown[], callback: () => void) {
        if (event.previousContainer === event.container) {
            const newIndex = event.currentIndex;
            // Swap
            const element = dataList.splice(event.previousIndex, 1)[0];
            dataList.splice(newIndex, 0, element);

            callback();
        }
    }
}
