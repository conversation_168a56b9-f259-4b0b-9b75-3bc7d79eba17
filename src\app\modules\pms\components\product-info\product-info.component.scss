/* product-info.component.scss */

.product-panel {
    .p-panel-content {
        padding: 0; /* Dùng padding ở info-multicolumn */
    }
}

.info-multicolumn {
    padding: 1rem;
    border: 1px solid #ccc;
    border-radius: 6px;

    dl {
        /* <PERSON><PERSON> 2 cột, k<PERSON><PERSON><PERSON> c<PERSON>ch gi<PERSON>a 2 cột là 2rem */
        column-count: 2;
        column-gap: 2rem;
    }

    dt {
        font-weight: 600;
        margin: 0.25rem 0;
    }

    dd {
        margin: 0 0 0.75rem 0;
        word-break: break-word;
    }

    a {
        color: var(--primary-color);
        text-decoration: underline;
    }
}
:host ::ng-deep {
    /* tag custom */
    .p-tag-cutom .p-tag {
        background-color: var(--surface-100);
        color: #1c1a1a;
        border: 0.005px solid #bbabab;
        margin-left: 2px;
    }

    /* dropdown / multiselect / password đều full-width */
    .p-multiselect,
    .p-dropdown,
    .p-password {
        width: 100%;
    }
}
