<style>
    :host ::ng-deep .p-inputnumber {
        width: 100% !important;
    }
    :host ::ng-deep .p-dropdown {
        width: 100% !important;
    }
    th {
        text-wrap: nowrap;
    }

    :host ::ng-deep .p-multiselect {
        width: 100%;
    }
</style>

<p-dialog
    [header]="poTransfer?.id ? 'Chi tiết phiếu xuất kho' : 'Thêm mới phiếu xuất kho'"
    [modal]="true"
    [(visible)]="visible"
    [style]="{ width: '80vw' }"
    [breakpoints]="{ '1199px': '80vw', '575px': '100vw' }"
    (onHide)="closeDialog()"
>
    <hr style="margin: 0" />
    <br />
    <app-form formId="poTransferExport" [formGroup]="formGroup" (onSubmit)="handleSubmit()" layout="vertical" *ngIf="formGroup">
        <p-panel header="Thông tin phiếu">
            <ng-template pTemplate="icons">
                <p-tag *ngIf="poTransfer?.id" [severity]="getSeverity()" [value]="getValueTagState()" />
            </ng-template>
            <div class="tw-grid tw-grid-cols-2 tw-gap-4">
                <app-form-item label="Dự án">
                    <input pInputText class="tw-w-full" formControlName="contract" />
                </app-form-item>
                <app-form-item label="Mã giao dịch">
                    <input pInputText class="tw-w-full" formControlName="invTransferNumber" />
                </app-form-item>
                <app-form-item label="Thông tin gốc">
                    <input class="tw-w-full" pInputText formControlName="sourceDocument" />
                </app-form-item>
                <app-form-item label="Ngày">
                    <p-calendar
                        [showButtonBar]="true"
                        placeholder="dd/MM/yyyy"
                        [showIcon]="true"
                        dateFormat="dd/mm/yy"
                        class="tw-w-full"
                        formControlName="dateCustom"
                        appendTo="body"
                    ></p-calendar>
                </app-form-item>
                <app-form-item label="Số đơn hàng">
                    <input class="tw-w-full" pInputText [disabled]="true" formControlName="orderNo" />
                </app-form-item>

                <app-form-item label="Tên nhà cung cấp">
                    <input class="tw-w-full" pInputText [disabled]="true" formControlName="supplierName" />
                </app-form-item>
            </div>
        </p-panel>
        <br />
        <ng-container formArrayName="poTransferItems">
            <p-panel header="Thông tin vật tư">
                <ng-template pTemplate="icons">
                    <app-button-group-file
                        [urlError]="urlError"
                        service="/sc/api"
                        [disabled]="isDisabled"
                        (onFileSelected)="handleSelectFile($event)"
                        (onClearFile)="handleClearFile()"
                        urlTemplate="template_export_transfer.xlsx"
                    >
                    </app-button-group-file>
                </ng-template>
                <p-table [value]="poTransferItems.controls" styleClass="p-datatable-gridlines">
                    <ng-template pTemplate="header">
                        <tr>
                            <th></th>
                            <th>VNPT PN</th>
                            <th>MAN PN</th>
                            <th>Tên hàng/Mô tả hàng hóa</th>
                            <th>Tổng số lượng</th>
                            <th>FOC</th>
                            <th>Đơn giá</th>
                            <th>Thành tiền</th>
                            <th>Ghi chú</th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                        <tr *ngIf="item.value.isEdit" [formGroupName]="rowIndex">
                            <td>
                                <div class="tw-flex tw-flex-nowrap tw-gap-3 tw-h-full tw-mb-3">
                                    <button
                                        class="p-link tw-p-2 bg-green-300 hover:tw-bg-green-400 tw-text-white"
                                        (click)="saveTransferItem(rowIndex)"
                                        pTooltip="Lưu"
                                        tooltipPosition="top"
                                    >
                                        <span class="pi pi-save"></span>
                                    </button>
                                    <button
                                        class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                        (click)="cancelCreate(rowIndex)"
                                        pTooltip="Hủy"
                                        tooltipPosition="top"
                                    >
                                        <span class="pi pi-times"></span>
                                    </button>
                                </div>
                                <div class="tw-min-h-[1.25rem]"></div>
                            </td>
                            <td style="min-width: 15rem">
                                <div class="tw-grid tw-grid-rows-[3fr_1fr] tw-h-full">
                                    <app-form-item label="">
                                        <p-dropdown
                                            [formControl]="poTransferItems.controls[rowIndex].get('internalReference')"
                                            formControlName="internalReference"
                                            [options]="optionPoDetail"
                                            optionLabel="display"
                                            optionValue="internalReference"
                                            (onChange)="handleChangeInternal($event, rowIndex)"
                                            appendTo="body"
                                            filter="true"
                                            placeholder="VNPT/PN"
                                        />
                                    </app-form-item>
                                    <div class="tw-min-h-[1.25rem]"></div>
                                </div>
                            </td>
                            <td style="min-width: 15rem">
                                <div class="tw-grid tw-grid-rows-[3fr_1fr] tw-h-full">
                                    <app-form-item label="">
                                        <input pInputText class="tw-w-full" formControlName="manufacturerCode" placeholder="MAN PN" />
                                    </app-form-item>
                                    <div class="tw-min-h-[1.25rem]"></div>
                                </div>
                            </td>
                            <td style="min-width: 15rem">
                                <div class="tw-grid tw-grid-rows-[3fr_1fr] tw-h-full">
                                    <app-form-item label="">
                                        <input pInputText class="tw-w-full" formControlName="description" placeholder="Mô tả" />
                                    </app-form-item>
                                    <div class="tw-min-h-[1.25rem]"></div>
                                </div>
                            </td>
                            <td style="min-width: 15rem">
                                <div class="tw-grid tw-grid-rows-[3fr_1fr] tw-h-full">
                                    <app-form-item label="">
                                        <p-inputNumber class="tw-w-full" formControlName="quantity" placeholder="Tổng số lượng" />
                                    </app-form-item>
                                    <div class="tw-min-h-[1.25rem]"></div>
                                </div>
                            </td>
                            <td style="min-width: 15rem">
                                <div class="tw-grid tw-grid-rows-[3fr_1fr] tw-h-full">
                                    <app-form-item label="">
                                        <p-inputNumber class="tw-w-full" formControlName="focQuantity" placeholder="FOC" />
                                    </app-form-item>
                                    <div class="tw-text-gray-500 tw-text-sm tw-flex tw-items-end tw-min-h-[1.25rem]">
                                        FOC =
                                        {{ getFocQuantityByReference(poTransferItems.controls[rowIndex].get('internalReference')?.value) }}
                                    </div>
                                </div>
                            </td>
                            <td style="min-width: 15rem">
                                <div class="tw-grid tw-grid-rows-[3fr_1fr] tw-h-full">
                                    <app-form-item label="">
                                        <p-inputNumber
                                            class="tw-w-full"
                                            formControlName="price"
                                            placeholder="giá thành"
                                            [minFractionDigits]="unitPriceString === 'USD' ? 6 : 0"
                                            [maxFractionDigits]="unitPriceString === 'USD' ? 6 : 0"
                                        />
                                    </app-form-item>
                                    <div class="tw-min-h-[1.25rem]"></div>
                                </div>
                            </td>
                            <td style="min-width: 15rem">
                                <div class="tw-grid tw-grid-rows-[3fr_1fr] tw-h-full">
                                    <app-form-item label="">
                                        <p-inputNumber
                                            class="tw-w-full"
                                            formControlName="amount"
                                            placeholder="thành tiền"
                                            [minFractionDigits]="unitPriceString === 'USD' ? 2 : 0"
                                            [maxFractionDigits]="unitPriceString === 'USD' ? 2 : 0"
                                        />
                                    </app-form-item>
                                    <div class="tw-min-h-[1.25rem]"></div>
                                </div>
                            </td>
                            <td style="min-width: 15rem">
                                <div class="tw-grid tw-grid-rows-[3fr_1fr] tw-h-full">
                                    <app-form-item label="">
                                        <input pInputText class="tw-w-full" formControlName="note" placeholder="ghi chú" />
                                    </app-form-item>
                                    <div class="tw-min-h-[1.25rem]"></div>
                                </div>
                            </td>
                        </tr>

                        <tr *ngIf="!item.value.isEdit">
                            <td>
                                <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                    <button
                                        class="p-link p-link tw-p-2 bg-green-300 hover:tw-bg-green-400 tw-text-white"
                                        (click)="editTransferItem(rowIndex)"
                                        pTooltip="Sửa"
                                        tooltipPosition="top"
                                        *ngIf="!isDisabled && !isAdding && !isEditing"
                                    >
                                        <span class="pi pi-pencil"></span>
                                    </button>

                                    <button
                                        class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                        (click)="removeTransferItem(rowIndex)"
                                        pTooltip="Xóa"
                                        tooltipPosition="top"
                                        *ngIf="!isDisabled && !isAdding && !isEditing"
                                    >
                                        <span class="pi pi-trash"></span>
                                    </button>
                                </div>
                            </td>
                            <td>
                                {{ item.getRawValue()?.internalReference }}
                            </td>
                            <td>
                                {{ item.getRawValue()?.manufacturerCode }}
                            </td>
                            <td>
                                {{ item.getRawValue()?.description }}
                            </td>
                            <td>
                                {{ item.getRawValue()?.quantity | number }}
                            </td>
                            <td>
                                {{ item.getRawValue()?.focQuantity | number }}
                            </td>
                            <td>
                                {{
                                    item.getRawValue()?.price
                                        | currency: (unitPriceString === 'USD' ? 'USD' : 'VND') : 'symbol' : (unitPriceString === 'USD' ? '1.6-6' : '1.0-0')
                                }}
                            </td>
                            <td>
                                {{
                                    item.getRawValue()?.amount
                                        | currency: (unitPriceString === 'USD' ? 'USD' : 'VND') : 'symbol' : (unitPriceString === 'USD' ? '1.2-2' : '1.0-0')
                                }}
                            </td>
                            <td>
                                {{ item.getRawValue()?.note }}
                            </td>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="footer">
                        <tr>
                            <td></td>
                            <td colspan="6" class="tw-text-left tw-font-bold">Tổng cộng:</td>
                            <td>
                                {{
                                    getTotalAmount()
                                        | currency: (unitPriceString === 'USD' ? 'USD' : 'VND') : 'symbol' : (unitPriceString === 'USD' ? '1.2-2' : '1.0-0')
                                }}
                            </td>
                            <td></td>
                        </tr>
                    </ng-template>
                </p-table>
                <div class="tw-mt-4" *ngIf="!isDisabled">
                    <p-button size="small" [disabled]="isEditing || isAdding" label="Thêm mới" (onClick)="addPoTransferItem()"> </p-button>
                </div>
            </p-panel>
        </ng-container>
    </app-form>
    <ng-template pTemplate="footer">
        <div>
            <p-button
                *ngIf="!isDisabled"
                severity="primary"
                type="submit"
                [disabled]="formGroup.invalid || isEditing || isAdding || isDisabled"
                size="small"
                (click)="handleSubmit()"
                [label]="poTransfer?.id ? 'Lưu' : 'Lưu và xác nhận'"
                [appAuthorities]="['ROLE_SYSTEM_ADMIN', 'sc_transfer_export_edit']"
            ></p-button>
            <p-button
                *ngIf="isCanCancel"
                label="Hủy phiếu"
                [text]="true"
                [raised]="true"
                size="small"
                severity="danger"
                (click)="confirmCancel()"
                [disabled]="formGroup.invalid || isEditing || isAdding"
                [appAuthorities]="['ROLE_SYSTEM_ADMIN', 'sc_transfer_export_cancel']"
            ></p-button>
            <p-button label="Đóng" [text]="true" [raised]="true" size="small" severity="secondary" (click)="closeDialog()"></p-button>
        </div>
    </ng-template>
</p-dialog>
