// src/app/shared/directives/fullscreen-toggle.directive.ts
import { Directive, Input, HostListener, Renderer2, ElementRef } from '@angular/core';

@Directive({
    selector: '[appFullscreenToggle]',
    standalone: true, //
})
export class FullscreenToggleDirective {
    @Input('target') target!: HTMLElement | ElementRef<HTMLElement>;
    constructor(
        private renderer: Renderer2,
        private host: ElementRef<HTMLElement>,
    ) {}

    @HostListener('click')
    toggle() {
        // lấy node thật
        const elem = this.target instanceof ElementRef ? this.target.nativeElement : this.target;

        const isFull = elem.classList.toggle('fullscreen');
        this.swapIcon(isFull ? 'pi-arrows-alt' : 'pi-window-minimize', isFull ? 'pi-window-minimize' : 'pi-arrows-alt');
        document.body.classList.toggle('fullscreen', isFull);
    }

    private swapIcon(remove: string, add: string) {
        const btn = this.host.nativeElement;
        this.renderer.removeClass(btn, remove);
        this.renderer.addClass(btn, add);
    }
}
