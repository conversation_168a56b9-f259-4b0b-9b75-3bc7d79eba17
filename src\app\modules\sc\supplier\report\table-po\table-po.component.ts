import { CommonModule } from '@angular/common';
import { HttpResponse } from '@angular/common/http';
import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { injectQuery, QueryObserverResult } from '@ngneat/query';
import { PanelModule } from 'primeng/panel';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { MAP_PO_STATE } from 'src/app/models/constant/sc';
import { Po, SupplierItem, SupplierMaterial, SupplierMaterialChange } from 'src/app/models/interface/sc';
import { PoService } from 'src/app/services/sc/po/po.service';
import { SupplierMaterialChangeService } from 'src/app/services/sc/supplier-report/suppiler-material-change.service';
import { SupplierMaterialService } from 'src/app/services/sc/supplier-report/suppiler-material.service';
import { SkeletonLoadingComponent } from 'src/app/shared/components/skeleton-loading/skeleton-loading.component';
import { ChartConfiguration } from 'chart.js';
import Chart from 'chart.js/auto';
import { format } from 'date-fns';
import { DialogModule } from 'primeng/dialog';
import { RouterLink } from '@angular/router';
import {TableCommonModule} from "../../../../../shared/table-module/table.common.module";
import {CalendarModule} from "primeng/calendar";
import {FormCustomModule} from "../../../../../shared/form-module/form.custom.module";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {DateUtils} from "../../../../../utils/date-utils";
@Component({
    selector: 'app-suppplier-table-po',
    templateUrl: './table-po.component.html',
    styleUrls: ['./table-po.component.scss'],
    standalone: true,
    imports: [TableModule, TagModule, CommonModule, PanelModule, SkeletonLoadingComponent, DialogModule, RouterLink, TableCommonModule, CalendarModule, FormCustomModule, ReactiveFormsModule, FormsModule],
    providers: [PoService, SupplierMaterialChangeService, SupplierMaterialService],
})
export class TablePoComponent implements OnInit {
    @Input() supplierId: number;
    @Input() hasViewPriceRole: boolean;
    @Input() provideItems: SupplierItem;
    //
    #query = injectQuery();

    stateListPo: QueryObserverResult<HttpResponse<Po[]>, Error>;
    stateListMaterial: QueryObserverResult<SupplierMaterial[], Error>;
    stateListMaterialChange: QueryObserverResult<HttpResponse<SupplierMaterialChange[]>, Error>;

    totalValue: number = 0;
    @ViewChild('templateChartLine') templateChartLine: ElementRef<HTMLCanvasElement>;
    chartLineMaterialHistory: Chart;
    visible: boolean = false;
    internalReference: string;

    // Filter po
    orderIdFilter: number;
    orderDateFilter: number;
    selectedDateFilter;
    orderNoFilter: string;
    unitPrice: string;
    // ENd filter po

    constructor(
        private poService: PoService,
        private supplierMaterialService: SupplierMaterialService,
        private supplierMaterialChangeService: SupplierMaterialChangeService,
    ) {}

    ngOnInit(): void {
        this.loadPoList();

        this.#query({
            queryKey: ['materialSupplier', this.supplierId],
            queryFn: () => this.supplierMaterialService.getSupplierMaterials(this.supplierId),
        }).result$.subscribe({
            next: (res) => {
                this.stateListMaterial = res;
                console.log(this.stateListMaterial.data)
                if (this.stateListMaterial.data && this.stateListMaterial.data.length > 0) {
                    this.unitPrice = this.stateListMaterial.data[0].unit;
                }
            },
        });
    }

    loadPoList() {
        this.#query({
            queryKey: ['poSupplier', this.supplierId, this.orderIdFilter, this.orderDateFilter], // Luôn cập nhật khi filter thay đổi
            queryFn: () => {
                let query = `query=supplierId==${this.supplierId}`;
                if (this.orderIdFilter) {
                    query += `;id==${this.orderIdFilter}`;
                }
                if (this.orderDateFilter) {
                    query += `;orderDate==${this.orderDateFilter}`;
                }
                query += `&page=0&size=10000&sort=orderDate,asc`;

                // console.log('Fetching with query:', query); // Debug query
                return this.poService.getPage(query);
            },
        }).result$.subscribe({
            next: (res) => {
                this.stateListPo = res;
                if (res.isSuccess) {
                    this.totalValue = this.stateListPo.data?.body.reduce((sum, item) => sum + item.totalValue, 0);
                }
            },
        });
    }

    getSeverity(po: Po) {
        switch (po.state) {
            case 0:
                return 'primary';
            case 1:
                return 'primary';
            case 2:
                return 'warning';
            case 3:
                return 'success';
            default:
                return 'primary';
        }
    }

    getValueTagState(po: Po) {
        return MAP_PO_STATE[po.state];
    }

    viewChart(item: SupplierMaterial) {
        this.visible = true;
        this.internalReference = item.internalReference;

        this.#query({
            queryKey: ['materialChangeSupplier', this.supplierId, item.internalReference],
            queryFn: () =>
                this.supplierMaterialChangeService.getPage(
                    `query=supplierId==${this.supplierId};internalReference==${item.internalReference}&page=0&size=1000&sort=date,asc`,
                ),
            staleTime: 30000,
            refetchOnWindowFocus: true,
        }).result$.subscribe({
            next: (res) => {
                this.stateListMaterialChange = res;
                if (res.isSuccess) {
                    this.initializeChart(item.internalReference);
                }
            },
        });
    }

    initializeChart(internalReference: string) {
        if (this.chartLineMaterialHistory) {
            this.chartLineMaterialHistory.destroy();
        }

        const ctx: HTMLCanvasElement = this.templateChartLine.nativeElement;

        const config: ChartConfiguration<'line', number[], string> = {
            type: 'line',
            data: {
                labels: this.stateListMaterialChange.data?.body.map((item) => format(item.date, 'dd/MM/yyyy')) || [],
                datasets: [
                    {
                        label:
                            this.stateListMaterialChange.data?.body && this.stateListMaterialChange.data.body.length > 0
                                ? this.stateListMaterialChange.data.body[0]?.unit
                                : '',
                        data: this.stateListMaterialChange.data.body.map((item) => item.price),
                    },
                ],
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    filler: {
                        propagate: false,
                    },
                    title: {
                        display: true,
                        text: `Biến động giá ${internalReference}`,
                    },
                },
                interaction: {
                    intersect: false,
                },
            },
        };

        this.chartLineMaterialHistory = new Chart(ctx, config);
    }

    selectOrderDate(event: Date) {
        if (event) {
            this.orderDateFilter = DateUtils.convertToTimestampHCM(event);
        } else {
            this.orderDateFilter = null;
        }
        this.loadPoList();
    }

    selectOrderCode(data) {
        this.orderIdFilter = data.value;
        this.loadPoList();
    }
}
