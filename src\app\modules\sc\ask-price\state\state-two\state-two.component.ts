import {
    AfterViewInit,
    Component,
    Input,
    inject,
    OnInit,
    SimpleChanges,
    OnChanges,
    ViewChild,
    TemplateRef
} from "@angular/core";
import {TableCommonModule} from "../../../../../shared/table-module/table.common.module";
import {CommonModule} from "@angular/common";
import {RouterModule} from "@angular/router";
import {ButtonModule} from "primeng/button";
import {TagModule} from "primeng/tag";
import {TooltipModule} from "primeng/tooltip";
import {ChipModule} from "primeng/chip";
import {HasAnyAuthorityDirective} from "../../../../../shared/directives/has-any-authority.directive";
import {PopupComponent} from "../../../../../shared/components/popup/popup.component";
import {DialogModule} from "primeng/dialog";
import {FormCustomModule} from "../../../../../shared/form-module/form.custom.module";
import {InputTextModule} from "primeng/inputtext";
import {InputTextareaModule} from "primeng/inputtextarea";
import {PaginatorModule} from "primeng/paginator";
import {
    FormBuilder,
    FormGroup,
    ReactiveFormsModule,
    FormArray,
    FormControl,
    Validators,
    FormsModule
} from "@angular/forms";
import {PanelModule} from "primeng/panel";
import {TableModule} from "primeng/table";
import {ApproveService} from "../../../../../services/smart-qc/masterdata/approve.service";
import {TableCommonService} from "../../../../../shared/table-module/table.common.service";
import {PoService} from "../../../../../services/sc/po/po.service";
import { TabViewModule } from 'primeng/tabview';
import {CheckboxModule} from "primeng/checkbox";
import {EditableInputComponent} from "../../../../../shared/edit/editable-input.component";
import {RfqService} from "../../../../../services/sc/sc-bom/rfq.service";
import {MaterialSupplier, Po, Rfq, RfqItem, RfqMaterial, Supplier} from "../../../../../models/interface/sc";
import {LoadingService} from "../../../../../shared/services/loading.service";
import {AlertService} from "../../../../../shared/services/alert.service";
import {RFQ_ITEM_TYPE} from "../../../../../models/constant/sc";
import {Column, EventPopupSubmit} from "../../../../../models/interface";
import { RfqMaterialSupplierService } from "src/app/services/sc/sc-bom/rfq-material-supplier.service";
import {FileService} from "../../../../../shared/services/file.service";
import {isArray} from "lodash";
import {QueryObserverBaseResult} from "@tanstack/query-core";
import {TABLE_KEY} from "../../../../../models/constant";
import {RfqMaterialService} from "../../../../../services/sc/sc-bom/rfq-material.service";
import {SupplierInforService} from "../../../../../services/sc/supplier/suppiler-infor.service";

@Component({
    selector: 'app-ask-price-state-two',
    standalone: true,
    imports: [
        TableCommonModule,
        CommonModule,
        RouterModule,
        ButtonModule,
        TagModule,
        TooltipModule,
        ChipModule,
        DialogModule,
        FormCustomModule,
        InputTextModule,
        InputTextareaModule,
        PaginatorModule,
        ReactiveFormsModule,
        PanelModule,
        TabViewModule,
        TableModule,
        CheckboxModule,
        HasAnyAuthorityDirective,
        PopupComponent,
        EditableInputComponent,
        FormsModule
    ],
    templateUrl: './state-two.component.html',
    providers: [ApproveService, TableCommonService, PoService, RfqService, RfqMaterialSupplierService, FileService, RfqMaterialService, SupplierInforService],
})
export class StateTwoComponent implements OnInit, AfterViewInit, OnChanges {
    // Declare
    @Input() rfqOld: Rfq;
    rfqItemsDisplay?: RfqItem[];
    rfqForm: FormGroup;
    uniqueTimes = new Set<number>();
    sortedUniqueTimes: number[] = [];
    itemQuantityMap = new Map<string, number>();

    uniqueTimesMaterials = new Set<number>();
    sortedUniqueTimesMaterials: number[] = [];
    itemQuantityMapMaterials = new Map<string, number>();

    // End Declare

    // Inject service
    rfqMaterialSupplierService = inject(RfqMaterialSupplierService);
    supplierInforService = inject(SupplierInforService);
    rfqMaterialService = inject(RfqMaterialService);
    fileService = inject(FileService);
    rfqService = inject(RfqService);
    loadingService = inject(LoadingService);
    alertService = inject(AlertService);
    // End inject service

    // Table
    state: QueryObserverBaseResult<MaterialSupplier[]>;
    columns: Column[] = [];
    tableId: string = TABLE_KEY.MATERIAL_SUPPLIER_STATE_TWO;
    tableCommonService = inject(TableCommonService);
    rowSupplierSelects: MaterialSupplier[] = [];
    uniqueProviderIds: number[] = [];
    @ViewChild('templateNote') templateNote: TemplateRef<Element>;
    @ViewChild('templateTotalRevenue') templateTotalRevenue: TemplateRef<Element>;
    // End table

    // Material
    showOnlyCheckNccTrue: boolean = false;
    filteredMaterialArray: FormArray;
    // End material

    constructor(private formBuilder: FormBuilder,) {
    }

    ngOnInit() {
        this.initForm();
        this.genItemsData();
        this.genRfqMaterialsData();

        this.tableCommonService
            .init<Supplier>({
                tableId: this.tableId,
                queryFn: (filter) => this.supplierInforService.getPageTableCustom(filter),
                configFilterRSQL: {
                    id: 'SetLong',
                    code: 'Text',
                    shortName: 'Text',
                    name: 'Text',
                    note: 'Text',
                    rate: 'Text',
                    totalRevenue: 'Number',
                    created: 'DateRange',
                    createdBy: 'Text'
                },
                defaultParamsRSQL: {
                    id: this.uniqueProviderIds
                },
                defaultParams: {
                    sort: 'name,asc'
                }
            })
            .subscribe((state) => {
                this.state = state;
            });

        this.tableCommonService.getRowSelect(this.tableId).subscribe((state) => {
            if (isArray(state)) {
                this.rowSupplierSelects = state;
            }
        });
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['rfqOld']) {
            this.initForm();
            this.genItemsData();
            this.genRfqMaterialsData();
        }
    }

    ngAfterViewInit() {
        setTimeout(() => {
            this.columns = [
                {
                    header: 'Tên đầy đủ',
                    field: 'name',
                    type: 'link',
                    url: '/sc/supplier-infor/{id}',
                    default: true,
                },
                {
                    field: 'shortName',
                    header: 'Tên viết tắt',
                    style: { 'max-width': '8rem' },
                },
                {
                    field: 'rate',
                    header: 'Xếp hạng',
                    style: { 'max-width': '8rem' },
                },
                {
                    field: 'totalRevenue',
                    header: 'Doanh thu NCC',
                    body: this.templateTotalRevenue,
                },
                {
                    field: 'note',
                    header: 'Ghi chú',
                    body: this.templateNote,
                    style: { 'max-width': '8rem' },
                }
            ];
        }, 0);
    }

    initForm(): void {
        // Sort materials by levels
        this.sortMaterialsByLevel();

        this.rfqForm = this.formBuilder.group({
            id: [{ value: this.rfqOld?.id, disabled: false }, []],
            code: [{ value: this.rfqOld?.code, disabled: true }],
            name: [{ value: this.rfqOld?.name, disabled: false }, [Validators.maxLength(255)]],
            note: [{ value: this.rfqOld?.note, disabled: false }, []],
            materials: this.formBuilder.array(this.initMaterialsForm()) // Fake data cho FormArray
        });

        this.uniqueProviderIds = Array.from(
            new Set(
                this.rfqOld.materials
                    .flatMap(material => material.providerIds || [])
            )
        );

        /*this.rfqOld.materials.forEach(material => {
            if (material.levels.length > 1) {
                console.log(material.levels);
            }
        })*/
    }

    sortMaterialsByLevel(): void {
        this.rfqOld?.materials.sort((a, b) => {
            const levelA = a.levels?.[0] || '';
            const levelB = b.levels?.[0] || '';
            return levelA.localeCompare(levelB);
        });
    }

    initMaterialsForm(): FormGroup[] {
        return this.rfqOld.materials.map(material => this.createMaterialFormGroup(material));
    }

    createMaterialFormGroup(material: RfqMaterial): FormGroup {
        return this.formBuilder.group({
            id: [material?.id ?? null],
            rfqId: [material?.rfqId ?? null],
            level: [material?.level ?? null],
            levels: [material?.levels ?? null],
            manPn: [material?.manPn ?? null],
            description: [material?.description ?? null],
            section: [material?.section ?? null, [Validators.maxLength(255)]],
            manufacturerId: [material?.manufacturerId ?? null],
            manufacturerName: [material?.manufacturerName ?? null],
            requestQuantity: [material?.requestQuantity ?? null],
            availableQuantity: [material?.availableQuantity ?? null],
            orderQuantity: [material?.orderQuantity ?? null],
            providers: [material?.providers ?? null],
            checkNcc: [material?.checkNcc ?? null],
            dateCode: [material?.dateCode ?? null, [Validators.maxLength(255)]],
            note: [material?.note ?? null, [Validators.maxLength(1000)]],
        });
    }

    get materials(): FormArray {
        return this.rfqForm.get('materials') as FormArray;
    }

    genRfqItemsDisplay(rfqItems: RfqItem[]) {
        const rfqItemsScBom: RfqItem[] = rfqItems.filter(item => item.type === RFQ_ITEM_TYPE.SC_BOM);  // Loại BOM
        const rfqItemsRetail: RfqItem[] = rfqItems.filter(item => item.type === RFQ_ITEM_TYPE.RETAIL); // Loại Retail

        const uniqueRetailItems: RfqItem[] = [];
        rfqItemsRetail.forEach(item => {
            const isDuplicate = uniqueRetailItems.some(existingItem =>
                existingItem.project === item.project && existingItem.accountingCode === item.accountingCode
            );

            if (!isDuplicate) {
                uniqueRetailItems.push({ project: item.project, accountingCode: item.accountingCode }); // Tạo item mới và thêm vào danh sách
            }
        });

        this.rfqItemsDisplay = [
            ...rfqItemsScBom,
            ...uniqueRetailItems
        ];
    }

    genItemsData() {
        this.rfqOld?.items?.forEach(item => {
            item.rfqItemDates?.forEach(entry => {
                if (entry.date !== undefined) {
                    this.uniqueTimes.add(entry.date);

                    // Tạo key từ các trường của PriceAskItem + time
                    const key = `${item.project || ''}-${item.accountingCode || ''}-${item.productName || ''}-${item.productShortName || ''}-${entry.date}`;
                    this.itemQuantityMap.set(key, entry.quantity || 0);
                }
            });
        });
        this.sortedUniqueTimes = Array.from(this.uniqueTimes).sort((a, b) => a - b);

        this.genRfqItemsDisplay(this.rfqOld?.items);
    }

    exportSupplierAskPrice(event) {

    }

    saveGeneralInfo() {
        const rfqNewData = this.rfqForm.getRawValue();
        const dataUpdate = {...this.rfqOld, ...rfqNewData};
        this.loadingService.show();
        this.rfqService.update(dataUpdate).subscribe({
            next: (res) => {
                this.alertService.success('Cập nhật thông tin thành công');
                this.loadingService.hide();
                this.rfqOld = {...this.rfqOld, name: dataUpdate.name, note: dataUpdate.note};
            },
            error: (error) => {
                this.loadingService.hide();
                this.alertService.handleError(error);
            },
        });
    }

    getQuantity(item: RfqItem, time: number): number {
        return this.itemQuantityMap.get(this.generateKey(item, time)) || 0;
    }

    private generateKey(item: RfqItem, time: number): string {
        return [
            item.project || '',
            item.accountingCode || '',
            item.productName || '',
            item.productShortName || '',
            time
        ].join('-');
    }

    // Material
    genRfqMaterialsData() {
        this.rfqOld?.materials?.forEach(material => {
            material.rfqMaterialDates?.forEach(entry => {
                if (entry.date !== undefined) {
                    this.uniqueTimesMaterials.add(entry.date);

                    // Tạo key từ các trường của PriceAskItem + time
                    const key = `${material.id || ''}-${entry.date}`;
                    this.itemQuantityMapMaterials.set(key, entry.quantity || 0);
                }
            });
        });
        this.sortedUniqueTimesMaterials = Array.from(this.uniqueTimesMaterials).sort((a, b) => a - b);

        // Level warning
        const materials = this.materials.controls || [];
        const filteredControls = materials.filter(mat => mat.get('checkNcc')?.value === true)

        this.filteredMaterialArray = new FormArray(filteredControls);
    }

    onToggleShowOnlyCheckNccTrue(value: boolean) {
        this.showOnlyCheckNccTrue = value;
    }

    getMaterialQuantity(item: FormGroup, time: number): number {
        return this.roundQuantity(this.itemQuantityMapMaterials.get(this.generateMaterialQuantityKey(item.getRawValue(), time)) || 0);
    }

    private generateMaterialQuantityKey(item: RfqMaterial, time: number): string {
        return [
            item.id || '',
            time
        ].join('-');
    }
    // End Material

    exportMaterial(event: EventPopupSubmit<unknown>) {
        const supplierIds: number[] = this.rowSupplierSelects.map(item => item.id);
        if (supplierIds?.length > 0) {
            this.loadingService.show();
            this.rfqMaterialSupplierService.export(this.rfqOld.id, supplierIds).subscribe({
                next: (res) => {
                    this.loadingService.hide();
                    const contentDisposition = res.headers.get("Content-Disposition");
                    const fileName = this.getFileNameFromHeader(contentDisposition) || "downloaded-file";

                    this.fileService.downloadBlob(res.body, fileName); // res.body chính là blob
                    event.close();
                },
                error: () => {
                    this.loadingService.hide();
                },
            });
        } else {
            this.alertService.error("Chọn ít nhất một Nhà cung cấp");
        }
    }

    getFileNameFromHeader(header: string | null): string | null {
        if (!header) return null;

        const fileNameMatch = header.match(/filename\*=UTF-8''(.+?)(;|$)/)
            || header.match(/filename="?(.+?)"?($|;)/);

        if (fileNameMatch && fileNameMatch[1]) {
            return decodeURIComponent(fileNameMatch[1]);
        }
        return null;
    }

    saveMaterialData(materialControl: FormGroup) {
        // console.log(materialControl.getRawValue());
        let dataUpdate = materialControl.getRawValue();
        this.loadingService.show();
        this.rfqMaterialService.update(dataUpdate).subscribe({
            next: (res) => {
                this.alertService.success('Cập nhật thông tin linh kiện thành công');
                this.loadingService.hide();
            },
            error: (error) => {
                this.loadingService.hide();
                this.alertService.handleError(error);
            },
        });
    }

    saveMaterialSupplierNote(supplier, newNote: string) {
        this.loadingService.show();
        supplier.note = newNote;
        this.supplierInforService.update(supplier).subscribe({
            next: (res) => {
                this.alertService.success('Cập nhật thông tin Nhà cung cấp thành công');
                this.loadingService.hide();
            },
            error: (error) => {
                this.loadingService.hide();
                this.alertService.handleError(error);
            },
        });
    }

    roundQuantity(value: number): number {
        return Math.ceil(value);
    }

    getTotalRevenue(unitPrice: string, totalValue: number) {
        if (totalValue == null) {
            return '';
        }

        const formattedValue = totalValue.toLocaleString('en-US');
        if (unitPrice === 'USD') {
            return `$${formattedValue}`;
        } else {
            return `đ${formattedValue}`;
        }
    }

    getVisibleIndex(index: number): number {
        let count = 0;
        for (let i = 0; i <= index; i++) {
            const m = this.materials.controls[i];
            if (!this.showOnlyCheckNccTrue || !m.get('checkNcc')?.value) {
                count++;
            }
        }
        return count;
    }

    protected readonly RFQ_ITEM_TYPE = RFQ_ITEM_TYPE;
}
