/* eslint-disable @typescript-eslint/no-explicit-any */
import { AbstractControl, AbstractControlOptions, FormArray, FormGroup, ValidatorFn } from '@angular/forms';

export class FormArrayCustom<T extends AbstractControl = AbstractControl> extends FormArray {
    constructor(controls: T[], validatorOrOpts?: ValidatorFn | ValidatorFn[] | AbstractControlOptions | null) {
        super(controls, validatorOrOpts);
        this.initializeSubmitState(this);
    }

    // Getter để truy cập chính instance này như một FormArray
    get formArray(): FormArray {
        return this;
    }

    // Khởi tạo trạng thái submit cho tất cả các control
    private initializeSubmitState(control: AbstractControl): void {
        (control as any).isSubmited = false;
        (control as any).countSubmit = 0;
        (control as any).lastSubmitTime = null;
        (control as any).markAsSubmit = () => this.markAsSubmit(control);

        if (control instanceof FormGroup || control instanceof FormArray) {
            Object.values(control.controls).forEach((child) => this.initializeSubmitState(child));
        }
    }

    // Đánh dấu submit
    private markAsSubmit(control: AbstractControl, isProcessing: Set<AbstractControl> = new Set()): void {
        if (isProcessing.has(control)) return;
        isProcessing.add(control);
        (control as any).isSubmited = true;
        (control as any).countSubmit = ((control as any).countSubmit || 0) + 1;
        (control as any).lastSubmitTime = new Date();
        isProcessing.delete(control);
    }

    // Reset trạng thái
    resetForm(): void {
        this.resetControls(this);
        super.reset();
    }

    private resetControls(control: AbstractControl): void {
        (control as any).isSubmited = false;
        (control as any).countSubmit = 0;
        (control as any).lastSubmitTime = null;

        if (control instanceof FormGroup || control instanceof FormArray) {
            Object.values(control.controls).forEach((child) => this.resetControls(child));
        }
    }
}
