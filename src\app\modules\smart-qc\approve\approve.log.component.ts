import { CommonModule } from '@angular/common';
import { Component, OnInit, inject } from '@angular/core';
import { ActivatedRoute, Router, RouterLink, RouterModule } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { AuthService } from 'src/app/core/auth/auth.service';
import { ApproveLog } from 'src/app/models/interface/smart-qc';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { TagModule } from 'primeng/tag';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { ApproveLogService } from 'src/app/services/smart-qc/masterdata/approve-log.service';
import { TableModule } from 'primeng/table';

@Component({
    selector: 'app-approve-log',
    standalone: true,
    imports: [
        CommonModule,
        RouterModule,
        RouterLink,
        ButtonModule,
        TagModule,
        HasAnyAuthorityDirective,
        SubHeaderComponent,
        TableModule,
    ],
    templateUrl: './approve.log.component.html',
    styleUrls: ['./approve.component.scss'],
    providers: [ApproveLogService],
})
export class ApproveLogComponent implements OnInit {
    taskId: number;
    contractId: number;

    authService = inject(AuthService);
    loadingService = inject(LoadingService);
    route = inject(ActivatedRoute);
    router = inject(Router);
    alertService = inject(AlertService);
    approveLogService = inject(ApproveLogService);

    itemsHeader = [{ label: 'Quản lý phê duyệt', url: '/sqc/approve' }, { label: 'Lịch sử phê duyệt' }];

    // lịch sử
    arrayApproveLog: ApproveLog[] = [];
    loading: boolean = true;

    ngOnInit() {
        this.route.paramMap.subscribe((params) => {
            this.taskId = params.get('id') ? Number(params.get('id')) : null;
            this.contractId = params.get('contractId') ? Number(params.get('contractId')) : null;

            this.approveLogService.getPage(`query=taskId==${this.taskId}&page=0&size=100&order=id,desc`).subscribe({
                next: (res) => {
                    this.arrayApproveLog = res.body;
                    this.loading = false;
                },
                error: () => {
                    this.loading = false;
                },
            });
        });
    }

    getKeys(obj: object): string[] {
        return Object.keys(obj);
    }
}
