// change-history-popup.component.ts
import { Component, Input, Output, EventEmitter } from '@angular/core';
import { DialogModule } from 'primeng/dialog';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { ChangeHistoryRecord } from 'src/app/models/interface/pms';

@Component({
    selector: 'app-change-history-popup',
    standalone: true,
    imports: [DialogModule, TableModule, ButtonModule],
    template: `
        <p-dialog header="LỊCH SỬ THAY ĐỔI" [(visible)]="visible" [modal]="true" [style]="{ width: dialogWidth || '90vw' }" (onHide)="close()">
            <p-table [value]="records" [style]="{ 'min-width': '100%' }" styleClass="p-datatable-gridlines p-datatable-sm">
                <!-- Header nhóm cột -->
                <ng-template pTemplate="header">
                    <tr>
                        <th rowspan="2">STT</th>
                        <th rowspan="2"><PERSON><PERSON> tác thay đổi</th>
                        <th rowspan="2"><PERSON> tiết cập nhật</th>
                        <th colspan="3">Trước thay đổi</th>
                        <th colspan="3">Sau thay đổi</th>
                        <th rowspan="2">Ghi chú</th>
                        <th rowspan="2">Người thực hiện</th>
                        <th rowspan="2">Thời điểm thực hiện</th>
                    </tr>
                    <tr>
                        <th>File</th>
                        <th>Version</th>
                        <th>Build time</th>
                        <th>File</th>
                        <th>Version</th>
                        <th>Build time</th>
                    </tr>
                </ng-template>

                <!-- Body -->
                <ng-template pTemplate="body" let-row>
                    <tr>
                        <td>{{ row.stt }}</td>
                        <td>{{ row.action }}</td>
                        <td>{{ row.detail }}</td>
                        <td>{{ row.beforeFile || '-' }}</td>
                        <td>{{ row.beforeVersion || '-' }}</td>
                        <td>{{ row.beforeBuildtime || '-' }}</td>
                        <td>{{ row.afterFile || '-' }}</td>
                        <td>{{ row.afterVersion || '-' }}</td>
                        <td>{{ row.afterBuildtime || '-' }}</td>
                        <td>{{ row.note || '-' }}</td>
                        <td>{{ row.user || '-' }}</td>
                        <td>{{ row.timestamp }}</td>
                    </tr>
                </ng-template>
            </p-table>

            <!-- Footer với nút Đóng -->
            <ng-template pTemplate="footer">
                <div class="p-d-flex p-jc-center">
                    <p-button label="Đóng" [text]="true" [raised]="true" severity="secondary" (click)="close()"></p-button>
                </div>
            </ng-template>
        </p-dialog>
    `,
})
export class ChangeHistoryPopupComponent {
    /** Hiển thị dialog */
    @Input() visible: boolean = false;
    @Output() visibleChange = new EventEmitter<boolean>();

    /** Có thể override width từ parent, ví dụ '600px' hoặc '80%' */
    @Input() dialogWidth?: string;
    @Input() records: ChangeHistoryRecord[] = [];

    close() {
        this.visible = false;
        this.visibleChange.emit(this.visible);
    }
}
