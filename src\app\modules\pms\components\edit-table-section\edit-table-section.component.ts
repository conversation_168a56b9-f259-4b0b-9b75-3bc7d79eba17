import { Component, Input, Output, EventEmitter, OnInit, SimpleChanges, OnChanges, ViewChildren, QueryList } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, FormArray } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { InputValidationComponent } from 'src/app/shared/components/input-validation/input.validation.component';
import { TableModule } from 'primeng/table';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { AlertService } from 'src/app/shared/services/alert.service';
import { md5File } from 'src/app/utils/hash-md5';
import { UploadService } from 'src/app/services/upload/upload.service';
import { catchError, debounceTime, finalize, Observable, of, Subscription, switchMap, tap } from 'rxjs';
import { ColumnTableSection } from 'src/app/models/interface/pms';
import { AsyncSingleSelect } from 'src/app/shared/components/async-single-select/async-single-select.component';
import { ProductSoftwareService } from 'src/app/services/pms/production-software/product-software.service';
import { v4 as uuid } from 'uuid';
import { TooltipModule } from 'primeng/tooltip';
import { environment } from 'src/environments/environment';
import { UploadCustomComponent } from 'src/app/shared/components/upload-custom/upload-custom.component';
import { FileUploadManagerService } from 'src/app/services/upload/file-upload-manager.service';
/**
 * Reactive editable table wrapped in a PrimeNG Panel,
 * với mỗi dòng là 1 FormGroup trong FormArray.
 */
@Component({
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        ButtonModule,
        TableModule,
        InputTextModule,
        UploadCustomComponent,
        InputValidationComponent,
        DropdownModule,
        AsyncSingleSelect,
        TooltipModule,
    ],
    selector: 'app-edit-table-section',
    templateUrl: './edit-table-section.component.html',
    styleUrls: ['./edit-table-section.component.scss'],
})
export class EditTableSectionComponent implements OnInit, OnChanges {
    // @Input() title = 'Editable Table';
    @ViewChildren('DocumentUpload') documentUploads!: QueryList<UploadCustomComponent>;
    @Input() columns: Array<ColumnTableSection> = [];
    @Input() initialRows: any[] = [];
    @Input() isHideAction: boolean = false;
    @Input() mode: string;
    @Input() productSoftwareOptions: any[] = [];
    @Input() updatedAt: string | number = '';
    @Output() rowsChange = new EventEmitter<any[]>();
    @Output() softwareSelected = new EventEmitter<{ rowIndex: number; field: string; rootId: number }>();
    @Output() versionSelected = new EventEmitter<{ rowIndex: number; version: any }>();
    @Output() loadingRowsChange = new EventEmitter<Map<number, boolean>>();

    // @Output() saveRowEvent = new EventEmitter<{
    //     index: number;
    //     row: any;
    //     done: (updated: any) => void;
    // }>();
    @Output() deleteRowEvent = new EventEmitter<{
        index: number;
        row: any;
        done: () => void;
    }>();
    @Input() reloadTrigger!: number;
    // map để giữ uploadId và subscription cho mỗi rowIndex
    private uploadIds = new Map<number, string>();
    public loadingRows = new Map<number, boolean>();
    private subs = new Map<number, Subscription>();
    /** dropdownOptions[rowIndex] = danh sách option cho dropdown tương ứng với phần mêm đã chọn*/
    dropdownOptions: any[][] = [];
    dropdownSoftwareOptions: any[][] = [];
    progress = -1;
    form!: FormGroup;
    downloadBaseUrl: string = environment.STORAGE_BASE_URL;
    fileNamePattern = /^[^_]+_[^_]+_[^_]+_[^_]+$/;
    // 1. Khai báo mảng patterns
    public fileAcceptPatterns: string[] = [
        // extensions
        '.pdf',
        '.xls',
        '.xlsx',
        '.doc',
        '.docx',
        '.csv',
        '.ppt',
        '.pptx',
        '.rtf',
        '.odt',
        '.ods',
        '.odp',
        '.txt',
        '.md',
        '.zip',
        '.rar',
        '.7z',
        '.tar',
        '.gz',
        '.bz2',
        '.tgz',
        '.xz',
        '.jar',
        '.gbr',
    ];

    // 2. Chuyển mảng thành chuỗi comma-separated
    public fileAcceptString = this.fileAcceptPatterns.join(',');
    constructor(
        private fb: FormBuilder,
        private alertService: AlertService,
        private uploadService: UploadService,
        private productSoftwareService: ProductSoftwareService,
        private fileUploadManagerService: FileUploadManagerService,
    ) {}

    ngOnInit() {
        this.initFormAndRows(this.initialRows);
    }
    // **Getter kiểm tra xem tất cả columns có minWidth hay không**
    get hasMinWidth(): boolean {
        if (!this.columns || this.columns.length === 0) {
            return false;
        }
        return this.columns.every((col) => !!col.minWidth);
    }
    ngOnChanges(changes: SimpleChanges) {
        if (changes['reloadTrigger'] && !changes['reloadTrigger'].firstChange) {
            this.initFormAndRows(this.initialRows);

            // Nếu có bất cứ item nào documentType = 2, loop để fetch dropdownOptions rồi set version
            if (this.initialRows.some((item) => item.documentType === 2)) {
                this.initialRows.forEach((item, index) => {
                    if (item.documentType === 2 && item.filePath != null && String(item.filePath).trim() !== '' && item.softwareId != null) {
                        const rootId = item.softwareId;
                        const rowGroup = this.rowsArray.at(index) as FormGroup;

                        const ctrl = rowGroup.get('softwareResource')!;
                        ctrl.setValue({ rootId: item.filePath });

                        this.getDropdownOptions(rootId, index, rowGroup).subscribe({
                            next: () => {
                                // Dữ liệu dropdownOptions[index] đã sẵn sàng
                                const opts = this.dropdownOptions[index];
                                // Tìm phần tử có shortName trùng item.filePath
                                const match = opts.find((o) => o.id === +item.filePath);

                                if (match) {
                                    rowGroup.get('version')!.setValue(match);
                                    rowGroup.get('selected')!.setValue(match.softwareName);
                                    this.applyDisableState();
                                }
                            },
                            error: () => {},
                        });
                    }
                });
            }
            return;
        }
        if (changes['mode']) {
            this.applyDisableState();
        }
        if (changes['initialRows'] && !changes['initialRows'].firstChange) {
            this.updateFormRows(changes['initialRows'].currentValue);
        }
    }
    // **Getter kiểm tra xem tất cả columns có minWidth hay không**

    private initFormAndRows(rows: any[]) {
        // 1) Tạo FormArray mới
        this.form = this.fb.group({ rows: this.fb.array([]) });

        // 2) Thêm từng row vào form
        rows.forEach((r) => this.addRow(r));
        // 3) Thiết lập subscribe và disable state
        this.rowsArray.valueChanges.pipe(debounceTime(300)).subscribe(() => this.rowsChange.emit(this.rowsArray.getRawValue()));
        this.applyDisableState();
    }

    /** Đồng bộ lại form khi parent cập nhật initialRows */
    private updateFormRows(newRows: any[]) {
        const fa = this.rowsArray;
        // 1) Điều chỉnh số lượng controls
        while (fa.length > newRows.length) {
            fa.removeAt(fa.length - 1);
            this.dropdownOptions.pop();
        }
        while (fa.length < newRows.length) {
            const idx = fa.length;
            fa.push(this.createRowGroup(newRows[idx]));
            this.dropdownOptions.push([]);
        }
        // 2) Patch từng control
        newRows.forEach((rowData, idx) => {
            const fg = fa.at(idx) as FormGroup;
            // chỉ patch các field tồn tại
            const patch: any = {};
            this.columns.forEach((col) => {
                if (rowData.hasOwnProperty(col.field)) {
                    patch[col.field] = rowData[col.field];
                }
            });
            // và cả id, filePath, fileName nếu bạn có
            patch['id'] = rowData.id;
            patch['filePath'] = rowData.filePath;
            patch['fileName'] = rowData.fileName;
            fg.patchValue(patch, { emitEvent: false });
        });
    }

    private applyDisableState() {
        if (!this.form) return;
        if (this.mode === 'view') {
            this.form.disable({ emitEvent: false });
        } else {
            // this.form.enable({ emitEvent: false });
        }
    }
    /** Truy cập nhanh FormArray */
    get rowsArray(): FormArray {
        return this.form.get('rows') as FormArray;
    }

    /** Tạo FormGroup cho 1 dòng, gán default + validators */
    private createRowGroup(data?: any): FormGroup {
        const controls: Record<string, any> = {};

        this.columns.forEach(({ field, type }) => {
            // Giá trị mặc định
            const value = data?.[field] ?? (type === 'file' ? null : '');
            // Tính trạng thái disabled
            const disabled = type === 'readonly' || (field === 'version' && ['select', 'select-one'].includes(type));

            controls[field] = this.fb.control({ value, disabled });

            // Controls phụ cho select-one và select
            if (type === 'select-one' && data?.document === 'Phần mềm hỗ trợ sản xuất') {
                controls['selected'] = this.fb.control(data.softwareName || '');
            }
            if (type === 'select-one' && data?.document === 'RD BOM') {
                controls['selected'] = this.fb.control(data.fileName || '');
            }
            if (type === 'select' && data?.document === 'Phần mềm hỗ trợ sản xuất') {
                controls['fileUrlSoftware'] = this.fb.control(data?.fileUrlSoftware ?? '');
                controls['fileUrlUserguide'] = this.fb.control(data?.fileUrlUserguide ?? '');
            }
        });

        // Thêm metadata
        ['isDefault', 'id', 'filePath', 'fileName', 'category'].forEach((key) => {
            const val = data?.[key] ?? (key === 'isDefault' ? null : '');
            controls[key] = this.fb.control(val);
        });

        // 2. Khởi tạo FormGroup
        const fg = this.fb.group(controls);

        // mặc định không áp dụng cho bảng RD BOM và phần mêm hỗ trợ sản xuất
        if (fg.get('file')) {
            // 3. Subscribe vào file changes để enable/disable các field
            fg.get('file')!.valueChanges.subscribe((fileVal) => {
                const shouldDisable = !fileVal;
                ['name', 'buildtime', 'version'].forEach((f) => {
                    const ctrl = fg.get(f);
                    if (!ctrl) return;
                    if (shouldDisable) {
                        ctrl.disable({ emitEvent: false });
                    } else {
                        ctrl.enable({ emitEvent: false });
                    }
                });
            });

            // 4. Thiết lập trạng thái ban đầu
            if (!data?.file) {
                ['name', 'buildtime', 'version'].forEach((f) => {
                    fg.get(f)!.disable({ emitEvent: false });
                });
            }
        }

        return fg;
    }

    getDownloadUrl(filePath: string) {
        return `${environment.STORAGE_BASE_URL}/${filePath}`;
    }
    /** Thêm dòng mới (hoặc với data nếu truyền vào) */
    addRow(data?: any) {
        const fg = this.createRowGroup(data);
        this.rowsArray.push(fg);
    }

    // saveRow(index: number) {
    //     const row = this.rowsArray.at(index).getRawValue();
    //     console.log('check row', row);
    //     this.saveRowEvent.emit({
    //         index,
    //         row,
    //         done: (updated) => this.updateSingleRow(index, updated),
    //     });
    // }
    // public updateSingleRow(index: number, data: any) {
    //     const fg = this.rowsArray.at(index);
    //     fg.patchValue(data);
    //     // nếu bạn có control id hoặc filePath riêng, cũng patch ở đây
    //     if (data.id != null) fg.get('id')?.setValue(data.id);
    //     if (data.filePath != null) fg.get('filePath')?.setValue(data.filePath);
    // }
    /** Xóa dòng tại index */
    deleteRow(index: number) {
        const row = this.rowsArray.at(index).getRawValue();

        if (row.id) {
            // Nếu dòng đã có id, emit cho parent trước
            this.deleteRowEvent.emit({
                index,
                row,
                done: () => {
                    this.rowsArray.removeAt(index);
                    this.dropdownOptions.splice(index, 1);
                    this.dropdownSoftwareOptions.splice(index, 1);
                },
            });
        } else {
            // Nếu dòng chưa có id (chưa lưu), xóa ngay
            this.rowsArray.removeAt(index);
            this.dropdownOptions.splice(index, 1);
            this.dropdownSoftwareOptions.splice(index, 1);
        }
    }

    /** Đếm số file đã upload (có value) */
    getTotalUploaded(): number {
        return this.rowsArray.controls.reduce((sum, grp) => {
            return sum + this.columns.filter((c) => c.type === 'file' || c.type === 'select').filter((c) => !!grp.get(c.field)!.value).length;
        }, 0);
    }

    /** Tổng số file có thể upload */
    getTotalPossibleUploads(): number {
        const fileCols = this.columns.filter((c) => c.type === 'file' || c.type === 'select').length;
        return this.rowsArray.length * fileCols;
    }
    /** Xử lý khi upload file */
    async handleFileUpload(files: File[], rowIndex: number) {
        // Lấy file và thiết lập uploadId, loading state
        const file = files[0];
        const fileName = file.name;
        const uploadId = uuid();
        this.uploadIds.set(rowIndex, uploadId);
        this.loadingRows.set(rowIndex, true);
        this.loadingRowsChange.emit(this.loadingRows);

        // Bắt sự kiện hủy upload: unsubscribe và xóa subscription
        this.fileUploadManagerService.start(uploadId, () => {
            const sub = this.subs.get(rowIndex);
            if (sub) {
                sub.unsubscribe();
                this.subs.delete(rowIndex);
            }
        });

        // Lấy FormGroup tương ứng
        const rowGroup = this.rowsArray.at(rowIndex);
        // Cập nhật control 'file' với tên file
        rowGroup.get('file')?.setValue(fileName);

        // Phân tích tên file: prefix_docName_version_buildtime.ext
        const parts = fileName.split('_');
        const hasMeta = parts.length === 4;
        const docName = hasMeta ? parts[1] : '';
        const version = hasMeta ? parts[2] : '';
        const buildtime = hasMeta ? parts[3].replace(/\.[^/.]+$/, '') : '';

        // Tính MD5
        const md5 = await md5File(file);

        // Cập nhật đồng loạt các control nếu tồn tại
        const updates: Record<string, any> = {
            md5,
            name: docName,
            version,
            buildtime,
            fileName,
        };
        Object.entries(updates).forEach(([key, value]) => {
            rowGroup.get(key)?.setValue(value);
        });

        // Tạo pipeline upload: analyze -> presigned -> finalize
        const sub = this.uploadService
            .analyzeFile(fileName, 'DOCUMENT')
            .pipe(
                tap((meta) => rowGroup.get('filePath')?.setValue(meta.objectPath)),
                switchMap((meta) => this.uploadService.uploadToPresignedUrl(file, meta.presignedUrl)),
                finalize(() => this.fileUploadManagerService.finish(uploadId)),
            )
            .subscribe({
                next: (progress) => (this.progress = progress),
                error: () => {
                    // Nếu lỗi, clear component upload hàng tương ứng
                    const uploads = this.documentUploads.toArray();
                    uploads[rowIndex]?.clearAll();
                },
                complete: () => {
                    // Kết thúc upload, tắt loading
                    this.loadingRows.set(rowIndex, false);
                    this.loadingRowsChange.emit(this.loadingRows);
                },
            });

        // Lưu subscription để có thể hủy nếu cần
        this.subs.set(rowIndex, sub);
    }
    onCellClear(rowIndex: number, field: string) {
        const rowGroup = this.rowsArray.at(rowIndex);

        // áp dụng bảng RDBOM
        if (field === 'rdBomType') {
            rowGroup.reset({
                id: rowGroup.get('id')?.value,
                document: rowGroup.get('document')?.value,
                fileCode: rowGroup.get('fileCode')?.value,
                category: rowGroup.get('category')?.value,
                isDefault: rowGroup.get('isDefault')?.value,
            });
        }

        // tương tự nếu cần emit ngay:
        // this.rowsChange.emit(this.rowsArray.getRawValue());

        // áp dụng tab Hồ sơ sản xuất
        if (field === 'softwareResource') {
            this.dropdownOptions[rowIndex] = [];
            rowGroup.reset({
                id: rowGroup.get('id')?.value,
                category: rowGroup.get('category')?.value,
                fileCode: rowGroup.get('fileCode')?.value,
                isDefault: rowGroup.get('isDefault')?.value,
            });
            // Disable lại control version
            rowGroup.get('version')?.disable({ emitEvent: false });
        }
    }

    /** Khi user chọn 1 giá trị */
    onCellSelect(rowIndex: number, field: string, value: any) {
        // B1 cập nhật trường software Resource vào formControl
        const rowGroup = this.rowsArray.at(rowIndex) as FormGroup;
        const ctrl = rowGroup.get(field)!;
        ctrl.setValue(value);
        // B2 cập nhật các trường còn lại vào formControl

        if (rowGroup.get('fileName')) rowGroup.get('fileName')!.setValue(value.name);
        if (rowGroup.get('version')) rowGroup.get('version')!.setValue(value.version);
        if (rowGroup.get('status')) rowGroup.get('status')!.setValue(value.status);

        // Nếu user chọn phần mềm (softwareResource), gọi API lấy versions
        if (field === 'softwareResource') {
            if (rowGroup.get('file')) rowGroup.get('file')!.setValue(null);
            if (rowGroup.get('md5')) rowGroup.get('md5')!.setValue(null);
            if (rowGroup.get('buildtime')) rowGroup.get('buildtime')!.setValue(null);

            const rootId = value?.rootId;
            if (!rootId) {
                this.dropdownOptions[rowIndex] = [];
                return;
            }

            // Subscribe để chờ kết quả về
            this.getDropdownOptions(rootId, rowIndex, rowGroup).subscribe();
        }
    }
    getDropdownOptions(rootId: number, rowIndex: number, rowGroup: FormGroup) {
        return this.productSoftwareService.getVersions(rootId).pipe(
            tap((opts) => {
                this.dropdownOptions[rowIndex] = opts.map((item) => ({
                    ...item,
                    shortName: item.versionName || '',
                }));
                rowGroup.get('version')?.enable({ emitEvent: false });
            }),
            catchError(() => {
                this.dropdownOptions[rowIndex] = [];
                rowGroup.get('version')?.disable({ emitEvent: false });
                return of([]);
            }),
        );
    }
    onDropdownCleared(rowIndex: number, field: string) {
        const rowGroup = this.rowsArray.at(rowIndex);
        // xóa version trong dropdown PMHTSX
        if (field === 'version') {
            rowGroup.reset({
                id: rowGroup.get('id')?.value,
                category: rowGroup.get('category')?.value,
                fileCode: rowGroup.get('fileCode')?.value,
                isDefault: rowGroup.get('isDefault')?.value,
                selected: rowGroup.get('selected')?.value,
            });
        }
    }
    /** Xử lý khi clear file */
    clearFile(rowIndex: number) {
        const uploadId = this.uploadIds.get(rowIndex);
        if (uploadId) {
            this.fileUploadManagerService.cancel(uploadId);
            this.uploadIds.delete(rowIndex);
        }
        const sub = this.subs.get(rowIndex);
        if (sub) {
            sub.unsubscribe();
            this.subs.delete(rowIndex);
        }
        const rowGroup = this.rowsArray.at(rowIndex);
        rowGroup.get('file')?.setValue(null);
        if (rowGroup.get('name')) rowGroup.get('name')?.setValue('');
        if (rowGroup.get('version')) rowGroup.get('version')?.setValue('');
        if (rowGroup.get('buildtime')) rowGroup.get('buildtime')?.setValue('');
        if (rowGroup.get('fileName')) rowGroup.get('fileName')?.setValue('');
        if (rowGroup.get('filePath')) rowGroup.get('filePath')?.setValue('');
        if (rowGroup.get('md5')) rowGroup.get('md5')?.setValue('');
    }
    onDropdownSelect(selectedValue: any, rowIndex: number, field: string) {
        const rowGroup = this.rowsArray.at(rowIndex);

        // trường hợp cập nhật version tại dropdown PMHTSX
        if (field === 'version') {
            if (rowGroup.get('fileUrlUserguide')) rowGroup.get('fileUrlUserguide')?.setValue(selectedValue?.userGuide);
            if (rowGroup.get('fileUrlSoftware')) rowGroup.get('fileUrlSoftware')?.setValue(selectedValue?.path);
            if (rowGroup.get('softwareResourceName')) rowGroup.get('softwareResourceName')?.setValue(selectedValue?.name);
            if (rowGroup.get('instructionName')) rowGroup.get('instructionName')?.setValue(selectedValue?.instructionName);
            if (rowGroup.get('md5')) rowGroup.get('md5')?.setValue(selectedValue?.md5);
            if (rowGroup.get('buildtime')) rowGroup.get('buildtime')?.setValue(selectedValue?.buildTime);
        }

        // 3) Nếu sau khi “fill” xong bạn muốn emit lại ra parent:
        // this.rowsChange.emit(this.rowsArray.getRawValue());
    }
    onCellSearch(rowIndex: number, loaderFn: ((row: any, query: string) => Observable<any[]>) | undefined, term: string) {
        // tìm row hiện tại
        const row = this.rowsArray.at(rowIndex).getRawValue();
        loaderFn(row, term).subscribe({
            next: (opts) => (this.dropdownSoftwareOptions[rowIndex] = opts),
            error: () => (this.dropdownSoftwareOptions[rowIndex] = []),
        });
    }
}
