<app-sub-header [items]="itemsHeader" [action]="actionHeader"> </app-sub-header>
<ng-template #actionHeader>
    <app-wizard [currentState]="activeIndex" [states]="itemsStep"></app-wizard>
</ng-template>
<div style="padding: 1rem 1rem 0 1rem">
    <!-- steps -->
    <!-- <div class="card">
        <p-steps [model]="itemsStep" [(activeIndex)]="activeIndex" [readonly]="true" />
    </div> -->
    <!-- product-info -->
    <app-product-info
        header="Thông tin sản phẩm"
        [fieldsLeft]="[
            { label: 'Dòng sản phẩm', value: 'lineName' },
            { label: 'Tên sản phẩm', value: 'name' },
            { label: 'Mô tả', value: 'description' },
            { label: 'VNPT P/N', value: 'vnptManPn' },
            { label: 'HW version', value: 'hardwareVersion' },
            { label: 'FW version', value: 'firmwareVersion' },
        ]"
        [fieldsRight]="[
            { label: 'Tên thương mại', value: 'tradeName' },
            { label: 'Mã thương mại', value: 'tradeCode' },
            { label: 'Model', value: 'modelName' },
            { label: 'Generation', value: 'generation' },
            { label: 'Hình ảnh sản phẩm', value: 'imageName', type: 'link', url: 'imageUrl' },
            { label: 'Giai đoạn', value: 'stage', type: 'tag' },
        ]"
        [product]="currentProduct"
    ></app-product-info>
    <div class="tw-grid tw-grid-cols-12 tw-my-4">
        <!-- cho nút chiếm toàn bộ 12 cột, rồi text-align sang phải -->
        <div class="tw-col-span-12 tw-text-right">
            <ng-container *ngIf="mode !== 'create'">
                <p-button *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'version_detail_view']" severity="success" (click)="handleExport()" label="Xuất file">
                </p-button>
            </ng-container>
        </div>
    </div>
    <app-form [formGroup]="filterForm" (onSubmit)="onSearch($event, 7)" layout="vertical" *ngIf="this.mode === 'create' && !isNotClone">
        <div class="tw-grid tw-grid-cols-12 tw-my-4">
            <div class="tw-col-span-10 filter-container tw-flex tw-items-center tw-flex-wrap mr-2">
                <span class="tw-font-bold">Nhân bản từ HSSP khác</span>
                <app-custom-form-item layout="horizontal" label="" [control]="filterForm.get('selectedProduct')">
                    <ng-container *ngIf="currentProduct?.lineId != null">
                        <app-custom-filter-table
                            [control]="filterForm.get('selectedProduct')"
                            type="select-one"
                            [configSelect]="configSelect"
                            placeholder="Chọn sản phẩm"
                            [disabled]="true"
                        ></app-custom-filter-table>
                    </ng-container>
                </app-custom-form-item>
                <app-custom-form-item layout="horizontal" label="">
                    <input disabled class="tw-w-full" type="text" pInputText formControlName="partNumber" placeholder="Chọn P/N" />
                </app-custom-form-item>
                <ng-container *ngIf="showStageFilter">
                    <app-custom-form-item layout="horizontal" label="" [control]="filterForm.get('selectedStage')">
                        <app-custom-filter-table
                            [control]="filterForm.get('selectedStage')"
                            type="select-one"
                            [configSelect]="{
                                fieldValue: 'value',
                                fieldLabel: 'label',
                                rsql: false,
                                option: (stageOptions$ | async),
                            }"
                            placeholder="Chọn giai đoạn"
                            [disabled]="!filterForm.get('selectedProduct')?.value"
                        ></app-custom-filter-table>
                    </app-custom-form-item>
                </ng-container>
                <ng-container *ngIf="showStageFilter">
                    <app-custom-form-item layout="horizontal" label="" [control]="filterForm.get('selectedVersion')">
                        <app-custom-filter-table
                            [control]="filterForm.get('selectedVersion')"
                            type="select-one"
                            [configSelect]="{
                                fieldValue: 'value',
                                fieldLabel: 'label',
                                rsql: false,
                                option: (productVersionOptions$ | async),
                            }"
                            placeholder="Chọn version hồ sơ"
                            [disabled]="!filterForm.get('selectedStage')?.value"
                        ></app-custom-filter-table>
                    </app-custom-form-item>
                </ng-container>
            </div>
            <div class="tw-col-span-2 tw-flex gap-4">
                <p-button [disabled]="isSearching" [loading]="isSearching" type="submit" label="Áp dụng" />
            </div>
        </div>
    </app-form>
    <!-- Design documentation -->
    <p-panel header="Tư liệu thiết kế" styleClass="product-info-panel">
        <ng-template *ngIf="this.mode === 'create'" pTemplate="icons">
            <span>Chọn giai đoạn hồ sơ: </span>
            <p-dropdown [options]="optionsLifecycleStage" [(ngModel)]="selectedLifecycleStage" optionLabel="label" />
        </ng-template>
        <app-tab-view [tabs]="itemsTab" (tabChange)="onTabChange($event)"> </app-tab-view>
        <div class="tw-flex tw-gap-4 tw-justify-center">
            <!-- Khi view: chỉ có Đóng -->
            <ng-container *ngIf="version && version.status === 16">
                <p-button *ngIf="mode === 'view'" label="Phê duyệt" size="small" severity="success" (click)="onOpenApproval()"></p-button>
            </ng-container>

            <p-button *ngIf="mode === 'view'" label="Hủy" size="small" styleClass="p-button-secondary" (click)="handleClose()"></p-button>
            <!-- Khi create: có Lưu + Hủy -->
            <ng-container *ngIf="mode === 'create'">
                <p-button
                    label="Thêm"
                    size="small"
                    (click)="saveAllTabs()"
                    [loading]="isSaving || isUploading"
                    loadingIcon="pi pi-spinner pi-spin"
                    [disabled]="isSaving || isUploading"
                ></p-button>

                <p-button label="Hủy" size="small" styleClass="p-button-secondary" (click)="handleCancel()"></p-button>
            </ng-container>

            <!-- Khi edit:-->
            <ng-container *ngIf="mode === 'edit'">
                <p-button
                    label="Lưu"
                    size="small"
                    (click)="saveAllTabs()"
                    [loading]="isSaving || isUploading"
                    loadingIcon="pi pi-spinner pi-spin"
                    [disabled]="isSaving || isUploading"
                ></p-button>

                <p-button label="Hủy" size="small" styleClass="p-button-secondary" (click)="handleCancel()"></p-button>
            </ng-container>
        </div>
    </p-panel>
</div>
<p-dialog header="Phê duyệt HSSP" [(visible)]="visibleApprovalPopup" [modal]="true" [style]="{ width: '60vw' }" (onHide)="close()">
    <app-form [formGroup]="formApprovalPopup" styleClass="tw-grid tw-grid-cols-1 tw-gap-4" *ngIf="formApprovalPopup">
        <div class="tw-grid tw-grid-cols-2 tw-gap-4">
            <app-custom-form-item label="Ghi chú:">
                <span *ngIf="formApprovalPopup.get('noteOfSendApproval')?.value" class="tw-leading-8">
                    {{ formApprovalPopup.get('noteOfSendApproval')?.value }}
                </span>
            </app-custom-form-item>

            <app-custom-form-item label="Người gửi phê duyệt:">
                <span *ngIf="formApprovalPopup.get('fromUser')?.value" class="tw-leading-8">{{ formApprovalPopup.get('fromUser')?.value }}</span>
            </app-custom-form-item>
            <a href="#" (click)="onViewHistory($event)" style="cursor: pointer; text-decoration: none"> Xem lịch sử version HSSP </a>
            <app-custom-form-item label="Ngày gửi phê duyệt:">
                <span *ngIf="formApprovalPopup.get('created')?.value" class="tw-leading-8">{{
                    formApprovalPopup.get('created')?.value | date: 'dd/MM/yyyy'
                }}</span>
            </app-custom-form-item>
            <app-custom-form-item [control]="formApprovalPopup.get('note')" label="Ghi chú (người phê duyệt):">
                <textarea rows="5" class="tw-w-full" pInputTextarea [autoResize]="true" formControlName="note" maxlength="1000"></textarea>
            </app-custom-form-item>
        </div>
    </app-form>

    <ng-template pTemplate="footer">
        <div class="tw-flex tw-gap-2 tw-justify-center">
            <p-button label="Phê duyệt" size="small" severity="success" (click)="submitFormApproval('CONFIRM')"></p-button>

            <p-button label="Từ chối" size="small" styleClass="p-button-danger" (click)="submitFormApproval('REJECT')"> </p-button>
        </div>
    </ng-template>
</p-dialog>
<!-- popup-history-version -->
<p-dialog
    header="Lịch sử version HSSP"
    [(visible)]="visibleHistoryVersion"
    [modal]="true"
    [style]="{ width: '60vw' }"
    (onHide)="close('visibleHistoryVersion')"
>
    <p-table [value]="records" [responsiveLayout]="'scroll'" [paginator]="true" [rows]="5" class="p-datatable-sm">
        <ng-template pTemplate="header">
            <tr>
                <th>Ngày</th>
                <th>Version</th>
                <th>Người thực hiện</th>
                <th>Nội dung cập nhật</th>
                <th>Version nhân bản</th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-item>
            <tr>
                <td>{{ item.date }}</td>
                <td>{{ item.version }}</td>
                <td>{{ item.author }}</td>
                <td>{{ item.updateContent }}</td>
                <td>{{ item.cloneVersion || '-' }}</td>
            </tr>
        </ng-template>
    </p-table>

    <ng-template pTemplate="footer">
        <div class="p-d-flex p-jc-end p-gap-2">
            <p-button label="Xuất excel" size="small" severity="success" (click)="onOpenExportExcel()"></p-button>
            <p-button label="Đóng" [text]="true" [raised]="true" size="small" severity="secondary" (click)="close('visibleHistoryVersion')"></p-button>
        </div>
    </ng-template>
</p-dialog>
<!-- popup-export-version -->
<app-popup
    #ExportFilePopup
    header="Xuất file"
    severity="success"
    [dialogWidth]="'20vw'"
    [isButtonVisible]="isButtonVisible"
    [formGroup]="formExportFilePopup"
    (onSubmit)="submitFileTransfer($event)"
>
    <app-form
        [labelCol]="'tw-col-span-7'"
        [wrapperCol]="'tw-col-span-5'"
        [formGroup]="formExportFilePopup"
        styleClass="tw-grid tw-grid-cols-1 tw-gap-5"
        *ngIf="formExportFilePopup"
    >
        <app-custom-form-item label="Xuất đến version" [control]="formExportFilePopup.get('selectedVersion')">
            <p-dropdown [options]="versionOptions" optionLabel="label" optionValue="value" appendTo="body" formControlName="selectedVersion"
        /></app-custom-form-item>
    </app-form>
</app-popup>
