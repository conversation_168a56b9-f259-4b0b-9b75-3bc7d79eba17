import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, OnDestroy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { RouterLink } from '@angular/router';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { ConfirmationService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { InputSwitchModule } from 'primeng/inputswitch';
import { TagModule } from 'primeng/tag';
import { BehaviorSubject, Subscription } from 'rxjs';
import { TABLE_KEY } from 'src/app/models/constant';
import { Column, Process } from 'src/app/models/interface';
import { ProcessService } from 'src/app/services/administration/process/process.service';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { TableCommonService } from 'src/app/shared/table-module/table.common.service';

@Component({
    selector: 'app-process',
    standalone: true,
    templateUrl: './process.component.html',
    imports: [
        TableCommonModule,
        CommonModule,
        RouterLink,
        SubHeaderComponent,
        TagModule,
        ButtonModule,
        InputSwitchModule,
        FormsModule,
    ],
    providers: [ProcessService],
})
export class ProcessComponent implements OnInit, AfterViewInit, OnDestroy {
    @ViewChild('templateActive') templateActive: TemplateRef<Element>;
    @ViewChild('templateArrange') templateArrange: TemplateRef<Element>;

    tableId: string = TABLE_KEY.PROCESS;
    state: QueryObserverBaseResult<Process[]>;
    columns: Column[] = [];
    itemsHeader = [{ label: 'Quản lý tiến trình' }, { label: 'Danh sách' }];
    loading$ = new BehaviorSubject<boolean>(false); // Observable cho trạng thái loading
    private subscription!: Subscription;

    constructor(
        private processService: ProcessService,
        private tableCommonService: TableCommonService,
        private confirmationService: ConfirmationService,
        private loadingService: LoadingService,
    ) {}
    ngAfterViewInit(): void {
        setTimeout(() => {
            this.columns = [
                { field: 'name', header: 'Tên', default: true, type: 'link', url: '/administration/process/{id}' },
                { field: 'description', header: 'Mô tả' },
                { field: 'keyEntity', header: 'Khóa' },
                { field: 'arrange', header: 'Arrange', body: this.templateArrange },
                { field: 'active', header: 'Active', body: this.templateActive },
                { field: 'created', header: 'Ngày tạo', type: 'date', format: 'dd/MM/yyyy' },
            ];
        });
    }

    ngOnInit(): void {
        this.tableCommonService
            .init<Process>({
                tableId: this.tableId,
                queryFn: (filter) => this.processService.getPageTableCustom(filter),
                configFilterRSQL: {
                    name: 'Text',
                    description: 'Text',
                    keyEntity: 'Text',
                },
            })
            .subscribe((state) => {
                this.state = state;
            });

        this.subscription = this.loading$.subscribe((isLoading) => {
            if (isLoading) {
                this.loadingService.show();
            } else {
                this.loadingService.hide();
            }
        });
    }

    ngOnDestroy(): void {
        // Hủy đăng ký subscription để tránh rò rỉ bộ nhớ
        if (this.subscription) {
            this.subscription.unsubscribe();
        }
    }

    handleActive(process: Process) {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: `Bạn có chắc chắn muốn ${process.active ? 'Inactive' : 'Active'}`,
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.loading$.next(true);
                this.processService.update({ ...process, active: !process.active }).subscribe({
                    next: () => {
                        this.state.refetch();
                        this.loading$.next(false);
                    },
                    error: () => {
                        this.state.refetch();

                        this.loading$.next(false);
                    },
                });
            },
        });
    }

    deleteSelectedProcess = (ids: number[]) => {
        return this.processService.batchDelete(ids);
    };

    rowSelectable = (rowData: Process) => {
        return !rowData.active;
    };
}
