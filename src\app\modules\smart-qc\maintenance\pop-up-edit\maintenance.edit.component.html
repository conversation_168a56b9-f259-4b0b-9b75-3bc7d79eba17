<p-dialog
    [header]="oldMaintenance ? '<PERSON> tiết thông tin lỗi' : '<PERSON><PERSON> báo thông tin xử lý lỗi'"
    [(visible)]="isVisible"
    [modal]="true"
    [breakpoints]="{ '1199px': '80vw' }"
    [style]="{ width: '60vw' }"
    (onHide)="closeDialog()"
>
    <hr style="margin: 0" />
    <br />
    <app-skeleton-loading [isLoading]="isLoading">
        <app-form formId="poTransferExport" [formGroup]="formGroup" (onSubmit)="handleSubmit()" layout="vertical" *ngIf="formGroup">
            <p-panel header="Thông tin trạm">
                <div class="tw-grid tw-grid-cols-2 tw-gap-4">
                    <app-form-item label="Tên trạm">
                        <app-filter-table
                            formControlName="stationName"
                            type="select-one"
                            [configSelect]="{
                                fieldValue: 'name',
                                fieldLabel: 'name',
                                paramForm: 'name',
                                url: '/smart-qc/api/station/combobox',
                                rsql: false,
                                body: {
                                    workType: 1,
                                },
                            }"
                            (onChange)="handleChangeStationName($event)"
                        ></app-filter-table>
                    </app-form-item>
                    <app-form-item label="Mã trạm">
                        <app-filter-table
                            formControlName="stationId"
                            type="select-one"
                            [configSelect]="{
                                fieldValue: 'id',
                                fieldLabel: 'code',
                                url: '/smart-qc/api/station/combobox',
                                paramForm: 'id',
                                rsql: false,
                                body: {
                                    name: objectFilter.stationName,
                                },
                            }"
                            (onChange)="handleChangeStationId($event)"
                        ></app-filter-table>
                    </app-form-item>
                    <app-form-item label="Dự án">
                        <input type="text" class="tw-w-full" pInputText formControlName="contractName" />
                    </app-form-item>

                    <app-form-item label="Tỉnh/TP">
                        <input type="text" class="tw-w-full" pInputText formControlName="areaName" />
                    </app-form-item>

                    <app-form-item label="SubPM">
                        <app-filter-table
                            formControlName="subPmId"
                            type="select-one"
                            [configSelect]="{
                                fieldValue: 'id',
                                fieldLabel: 'displayName',
                                options: optionSubPM,
                                filterLocal: true,
                            }"
                            [disabled]="!formGroup.getRawValue().stationId"
                        ></app-filter-table>
                    </app-form-item>
                </div>
            </p-panel>
            <br />
            <p-panel header="Thông tin lỗi">
                <div class="tw-grid tw-grid-cols-3 tw-gap-4">
                    <app-form-item label="Ngày lỗi">
                        <p-calendar
                            [showButtonBar]="true"
                            placeholder="dd/MM/yyyy"
                            [showIcon]="true"
                            dateFormat="dd/mm/yy"
                            class="tw-w-full"
                            formControlName="createDateCustom"
                            appendTo="body"
                        ></p-calendar>
                    </app-form-item>
                    <app-form-item label="Ngày xử lý xong">
                        <p-calendar
                            [showButtonBar]="true"
                            placeholder="dd/MM/yyyy"
                            [showIcon]="true"
                            dateFormat="dd/mm/yy"
                            class="tw-w-full"
                            formControlName="doneDateCustom"
                            appendTo="body"
                        ></p-calendar>
                    </app-form-item>
                    <app-form-item label="Số lần quay lại">
                        <p-inputNumber inputId="integeronly" class="tw-w-full" formControlName="backTimes" />
                    </app-form-item>
                </div>
                <br />
                <app-form-item label="Chi tiết xử lý">
                    <textarea class="tw-w-full" pInputTextarea formControlName="processDetail" style="field-sizing: content" rows="3"></textarea>
                </app-form-item>

                <app-form-item label="Ghi chú">
                    <textarea class="tw-w-full" pInputTextarea formControlName="note" style="field-sizing: content" rows="3"></textarea>
                </app-form-item>
                <br />
                <ng-container formArrayName="maintenanceCards">
                    <p-table [value]="maintenanceCards.controls" styleClass="p-datatable-gridlines">
                        <ng-template pTemplate="header">
                            <tr>
                                <th>Thao tác</th>
                                <th>Tên card</th>
                                <th>Số lượng</th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                            <tr *ngIf="item.value.isEdit" [formGroupName]="rowIndex">
                                <td>
                                    <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                        <button
                                            class="p-link tw-p-2 bg-green-300 hover:tw-bg-green-400 tw-text-white"
                                            (click)="saveItem(rowIndex)"
                                            pTooltip="Lưu"
                                            tooltipPosition="top"
                                        >
                                            <span class="pi pi-save"></span>
                                        </button>
                                        <button
                                            class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                            (click)="cancelCreate(rowIndex)"
                                            pTooltip="Hủy"
                                            tooltipPosition="top"
                                        >
                                            <span class="pi pi-times"></span>
                                        </button>
                                    </div>
                                </td>
                                <td style="min-width: 15rem">
                                    <app-form-item label="">
                                        <app-filter-table
                                            formControlName="cardId"
                                            [control]="maintenanceCards.controls[rowIndex].get('cardId')"
                                            type="select-one"
                                            [configSelect]="{
                                                fieldValue: 'id',
                                                fieldLabel: 'name',
                                                url: '/smart-qc/api/card/search',
                                                paramForm: 'id',
                                                rsql: true,
                                                body: { contractId: formGroup.getRawValue().contractId },
                                            }"
                                            (onChange)="handleChangeCard($event)"
                                        ></app-filter-table>
                                    </app-form-item>
                                </td>
                                <td style="min-width: 15rem">
                                    <app-form-item label="">
                                        <p-inputNumber
                                            p-inputNumber
                                            inputId="integeronly"
                                            class="tw-w-full"
                                            formControlName="quantity"
                                            placeholder="số lượng"
                                            [min]="0"
                                            [max]="99"
                                        />
                                    </app-form-item>
                                </td>
                            </tr>

                            <tr *ngIf="!item.value.isEdit">
                                <td>
                                    <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                        <button
                                            class="p-link p-link tw-p-2 bg-green-300 hover:tw-bg-green-400 tw-text-white"
                                            (click)="editItem(rowIndex)"
                                            pTooltip="Sửa"
                                            tooltipPosition="top"
                                            *ngIf="!isAdding && !isEditing"
                                        >
                                            <span class="pi pi-pencil"></span>
                                        </button>

                                        <button
                                            class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                            (click)="removeItem(rowIndex)"
                                            pTooltip="Xóa"
                                            tooltipPosition="top"
                                            *ngIf="!isAdding && !isEditing"
                                        >
                                            <span class="pi pi-trash"></span>
                                        </button>
                                    </div>
                                </td>
                                <td>
                                    {{ item.getRawValue()?.cardName }}
                                </td>

                                <td>
                                    {{ item.getRawValue()?.quantity | number }}
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <div class="tw-mt-4">
                        <p-button size="small" [disabled]="isEditing || isAdding || !formGroup.getRawValue().contractId" label="Thêm mới" (onClick)="addItem()">
                        </p-button>
                    </div>
                </ng-container>
            </p-panel>
        </app-form>
    </app-skeleton-loading>

    <br />
    <ng-template pTemplate="footer">
        <div>
            <p-button
                label="Xác nhận"
                severity="primary"
                type="submit"
                (click)="handleSubmit()"
                [disabled]="formGroup ? formGroup.invalid : false"
                size="small"
            ></p-button>
            <p-button label="Hủy" [text]="true" [raised]="true" size="small" severity="secondary" (click)="closeDialog()"></p-button>
        </div>
    </ng-template>
</p-dialog>
