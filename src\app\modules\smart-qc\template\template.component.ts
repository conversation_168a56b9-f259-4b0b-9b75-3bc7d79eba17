import { ChangeDetectorRef, Component, inject, TemplateRef, ViewChild, OnInit, AfterViewInit } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TableModule } from 'primeng/table';
import { PaginatorModule } from 'primeng/paginator';
import { ButtonModule } from 'primeng/button';
import { TableCommonService } from '../../../shared/table-module/table.common.service';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { Column } from '../../../models/interface';
import { HasAnyAuthorityDirective } from '../../../shared/directives/has-any-authority.directive';
import { AuthService } from '../../../core/auth/auth.service';
import { TooltipModule } from 'primeng/tooltip';
import { TABLE_KEY } from 'src/app/models/constant';
import { ConfirmationService } from 'primeng/api';
import { isArray, isEmpty } from 'lodash';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { catchError, of } from 'rxjs';
import { AlertService } from 'src/app/shared/services/alert.service';
import { TemplateService } from '../../../services/smart-qc/template/template.service';
import { Template } from '../../../models/interface/smart-qc';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';

@Component({
    selector: 'app-template',
    templateUrl: './template.component.html',
    styleUrls: ['./template.style.scss'],
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        TableModule,
        PaginatorModule,
        ButtonModule,
        RouterLink,
        HasAnyAuthorityDirective,
        TooltipModule,
        SubHeaderComponent,
        TableCommonModule,
    ],
    providers: [TemplateService],
})
export class TemplateComponent implements OnInit, AfterViewInit {
    @ViewChild('actionTemplate') actionTemplate: TemplateRef<Element>;
    @ViewChild('templateName') templateName: TemplateRef<Element>;
    @ViewChild('templateWorkType') templateWorkType: TemplateRef<Element>;
    confirmationService = inject(ConfirmationService);
    loadingService = inject(LoadingService);
    alertService = inject(AlertService);

    state: QueryObserverBaseResult<Template[]>;
    columns: Column[] = [];
    rowSelects: Template[] = [];
    tableId: string = TABLE_KEY.TEMPLATE;
    itemsHeader = [{ label: 'Quản lý mẫu kiểm tra' }, { label: 'Danh sách mẫu kiểm tra' }];

    workTypes: { label: string; value: number }[] = [
        { label: 'Khảo sát', value: 0 },
        { label: 'Lắp đặt', value: 1 },
        { label: 'QA-QC', value: 2 },
        { label: 'Hiệu chỉnh', value: 3 },
        { label: 'Chuyển hàng', value: 4 },
        { label: 'Phát sóng', value: 5 },
    ];

    constructor(
        private templateService: TemplateService,
        private router: Router,
        private tableCommonService: TableCommonService,
        protected authService: AuthService,
        private cdr: ChangeDetectorRef,
    ) {}

    ngOnInit() {
        this.tableCommonService
            .init<Template>({
                tableId: this.tableId,
                queryFn: (filter) => this.templateService.getPageTableCustom(filter),
                configFilterRSQL: {
                    id: 'SetLong',
                    code: 'SetLong',
                    workType: 'SetLong',
                },
                initialData: [],
            })
            .subscribe((state) => {
                this.state = state;
            });

        this.tableCommonService.getRowSelect(this.tableId).subscribe((state: Template) => {
            if (isArray(state)) {
                this.rowSelects = state;
            }
        });
    }

    ngAfterViewInit(): void {
        if (this.authService.isAdminOrPM()) {
            this.columns = [
                { header: 'Tên mẫu kiểm tra', body: this.templateName, default: true },
                { field: 'code', header: 'Mã mẫu kiểm tra' },
                { field: 'workType', header: 'Công việc', body: this.templateWorkType },
                { field: 'action', header: 'Thao tác', body: this.actionTemplate },
            ];
        } else {
            this.columns = [
                { header: 'Tên mẫu kiểm tra', body: this.templateName, default: true },
                { field: 'code', header: 'Mã mẫu kiểm tra' },
                { field: 'workType', header: 'Công việc', body: this.templateWorkType },
            ];
        }
        this.cdr.detectChanges();
    }

    copyTemplate(id: number) {
        this.router.navigate(['/sqc/template/' + id + '/create']);
    }

    deleteSelectedTemplate = (ids: number[]) => {
        return this.templateService.batchDelete(ids);
    };

    isRowSelectable = (data: Template) => {
        return data.used === 0;
    };

    getWorkTypeByValue(value: number) {
        return this.workTypes.find((workType) => workType.value === value).label;
    }

    updateChapter() {
        let ids = [];

        if (
            this.tableCommonService.getRowSelect(this.tableId).getValue() &&
            isArray(this.tableCommonService.getRowSelect(this.tableId).getValue())
        ) {
            ids = [].concat(this.tableCommonService.getRowSelect(this.tableId).getValue()).map((e) => e['id']);
        }
        if (isEmpty(ids)) return;

        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn chấp nhận kết quả của các trạm này không',

            icon: 'pi pi-exclamation-triangle',
            acceptIcon: 'none',
            rejectIcon: 'none',
            rejectButtonStyleClass: 'p-button-text',
            acceptButtonStyleClass: 'p-button-outlined',
            acceptLabel: 'Đồng ý',
            rejectLabel: 'Hủy',
            accept: () => {
                this.loadingService.show();
                this.templateService
                    .updateChapter(ids)
                    .pipe(
                        catchError(() => {
                            this.loadingService.hide();
                            return of(null);
                        }),
                    )
                    .subscribe({
                        next: () => {
                            this.alertService.success('Thành công', 'Cập nhật chapter thành công');
                            this.loadingService.hide();
                            this.state.refetch();
                        },
                        error: () => {
                            this.loadingService.hide();
                        },
                    });
            },
        });
    }
}
