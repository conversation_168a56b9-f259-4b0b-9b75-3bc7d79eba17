import { canAuthorize } from "../../../core/auth/auth.guard";

export const TemplateRouting = {
    path: 'template',
    title: 'Mẫu kiểm tra',
    children: [
        {
            path: '',
            canActivate: [canAuthorize],
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'ROLE_QC_SUBPM'] },
            loadComponent: () => import('./template.component').then((c) => c.TemplateComponent),
        },
        {
            path: ':idCopy/create',
            canActivate: [canAuthorize],
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM'] },
            loadComponent: () =>
                import('./template.create.component').then((c) => c.TemplateCreateComponent),
        },
        {
            path: 'create',
            canActivate: [canAuthorize],
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM'] },
            loadComponent: () =>
                import('./template.create.component').then((c) => c.TemplateCreateComponent),
        },
        {
            path: ':id/edit',
            canActivate: [canAuthorize],
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'ROLE_QC_SUBPM'] },
            loadComponent: () =>
                import('./template.create.component').then((c) => c.TemplateCreateComponent),
        },
    ],
}
