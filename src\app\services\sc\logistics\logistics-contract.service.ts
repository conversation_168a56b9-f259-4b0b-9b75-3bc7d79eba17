import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { LogisticsContract } from '../../../models/interface/sc';
import { ApiResponse } from 'src/app/models/interface';

@Injectable()
export class LogisticsContractService extends BaseService<LogisticsContract> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/logistics-contract');
    }

    importFile(files: File[]) {
        const formData: FormData = new FormData();
        files.forEach((file) => formData.append('files', file)); // Append each file separately

        return this.http.post<ApiResponse>('/sc/api/logistics-contract/import-file', formData);
    }
}
