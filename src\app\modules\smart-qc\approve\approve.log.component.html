<app-sub-header [items]="itemsHeader" [action]="actionHeader">
    <ng-template #actionHeader>
        <p-button
            label="Hủy"
            severity="secondary"
            [routerLink]="'/sqc/approve/' + taskId + '/' + contractId"
        ></p-button>
    </ng-template>
</app-sub-header>
<div style="padding: 1rem 1rem 0 1rem">
    <p-table styleClass="p-datatable-gridlines " [value]="arrayApproveLog" [loading]="loading">
        <ng-template pTemplate="header">
            <tr>
                <th><PERSON><PERSON><PERSON> cập nhật</th>
                <th>Ngư<PERSON><PERSON> cập nhật</th>
                <th>Lo<PERSON><PERSON> cập nhật</th>
                <th>Nội dung</th>
            </tr>
        </ng-template>

        <ng-template pTemplate="body" let-log>
            <tr>
                <td>{{ log.created | date: 'HH:mm:ss dd/MM/yyyy' }}</td>
                <td>
                    {{ log.createdBy }}
                </td>
                <td>
                    <p-tag *ngIf="log.action === 0" severity="danger" value="Từ chối" />
                    <p-tag *ngIf="log.action === 1" severity="success" value="Chấp nhận" />
                </td>
                <td>
                    <div *ngFor="let key of getKeys(log.logs)">
                        <p>
                            <b>{{ key }}</b>
                        </p>
                        <div>
                            <ul>
                                <li *ngFor="let response of log.logs[key]">
                                    {{ response }}
                                </li>
                            </ul>
                        </div>
                    </div>
                </td>
            </tr>
        </ng-template>
        <ng-template pTemplate="emptymessage">
            <tr>
                <td colspan="30" class="text-center">Không có dữ liệu</td>
            </tr>
        </ng-template>
    </p-table>
</div>
