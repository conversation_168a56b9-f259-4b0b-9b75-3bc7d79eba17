import { Component, OnInit } from '@angular/core';
import { CardModule } from 'primeng/card';
import { SummarySCC } from 'src/app/models/interface/sc';
import { DashboardScService } from './../../../services/sc/dashboard.service';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { Router } from '@angular/router';
@Component({
    selector: 'app-sc-dashboard',
    templateUrl: './dashboard.component.html',
    styleUrls: ['dashboard.component.scss'],
    standalone: true,
    imports: [CardModule],
})
export class DashboardSCCComponent implements OnInit {
    summaryReport: SummarySCC;
    constructor(
        private dashboardScService: DashboardScService,
        private loadingService: LoadingService,
        private router: Router,
    ) {}
    ngOnInit(): void {
        this.loadingService.show();
        this.dashboardScService.summary().subscribe({
            next: (res) => {
                this.summaryReport = res;
                this.loadingService.hide();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    navigateSupplier(state: number) {
        this.router.navigate(['/sc/supplier-infor'], {
            queryParams: {
                rsql: JSON.stringify({ state: [state] }),
            },
        });
    }

    navigatePo(state: number) {
        this.router.navigate(['/sc/po'], {
            queryParams: {
                rsql: JSON.stringify({ state: state }),
            },
        });
    }
}
