<div
    class="surface-ground flex align-items-center justify-content-center min-h-screen min-w-screen overflow-hidden"
    style="
        background-image: url('/assets/images/background/BG.jpg ');
        background-position: center;
        background-repeat: no-repeat;
        background-size: cover;
    "
>
    <img
        src="/assets/images/logo/vnpt-logo.png"
        alt="vnpt logo"
        style="position: absolute; width: 200px;  top: 32px; left: 32px"
    />

    <div class="flex flex-column align-items-center justify-content-center" style="margin: 32px">
        <div
            class="w-full surface-card py-8 px-5"
            style="border-radius: 16px; padding-top: 3rem !important; padding-bottom: 2rem !important"
        >
            <div class="in-login-container">
                <div  class="in-login-card">
                    <div >
                        <img src="/assets/images/background/QA.png" alt="vnpt logo" style="width: 100%" />
                    </div>
                </div>
                <div  class="in-login-card">
                    <p-messages />
                    <form #loginForm="ngForm">
<!--                        <label for="email1" class="block text-gray-500 text-base font-medium mb-2">Tài khoản/Email</label>-->
                        <input
                            #email="ngModel"
                            required
                            type="email"
                            name="email"
                            [(ngModel)]="account.email"
                            placeholder="Tài khoản/Email"
                            pInputText
                            class="w-full mb-2 "
                            style="padding: 1rem"
                        />
                        <app-input-validate [control]="email.control" fieldName="tài khoản/email"></app-input-validate>
                        <br>
<!--                        <label-->
<!--                            for="password1"-->
<!--                            class="block text-gray-500 font-medium text-base mb-2"-->
<!--                            style="padding-top: 16px"-->
<!--                        >Mật khẩu</label-->
<!--                        >-->
                        <p-password
                            #password="ngModel"
                            autocomplete="password"
                            required
                            name="password"
                            id="password1"
                            [(ngModel)]="account.password"
                            placeholder="Mật khẩu"
                            [toggleMask]="true"
                            [feedback]="false"
                            inputStyleClass="w-full p-3 w-26rem "
                            style="color: #0b655b"
                        ></p-password>
                        <app-input-validate [control]="password.control" fieldName="mật khẩu"></app-input-validate>

                        <div class="flex align-items-center justify-content-between mb-5 tw-mt-5 gap-5">
                            <div class="flex align-items-center">
                                <p-checkbox
                                    id="rememberme1"
                                    name="rememberme1"
                                    [binary]="true"
                                    [(ngModel)]="account.rememberMe"
                                    styleClass="mr-2"
                                ></p-checkbox>
                                <label for="rememberme1">Lưu mật khẩu</label>
                            </div>
                            <a
                                class="font-medium no-underline ml-2 text-right cursor-pointer"
                                style="color: var(--primary-color)"
                                (click)="account.visible = true"
                            >Quên mật khẩu?</a
                            >
                        </div>
                        <button pButton pRipple label="Đăng nhập" class="w-full p-3 text-xl" (click)="onLogin()"></button>
                    </form>
                </div>
            </div>


            <div style="display: inline-block">


            </div>
        </div>
    </div>

    <p-dialog
        [modal]="true"
        [(visible)]="account.visible"
        [style]="{ width: '40rem', 'border-radius': '53px' }"
        [breakpoints]="{ '1199px': '75vw', '575px': '90vw' }"
    >
        <div class="surface-ground flex align-items-center justify-content-center overflow-hidden">
            <div class="flex flex-column align-items-center justify-content-center">
                <div class="w-full surface-card py-8 px-5 sm:px-8">
                    <div class="text-center mb-5">
                        <div class="text-900 text-3xl font-medium mb-3">Quên mật khẩu</div>
                    </div>

                    <form>
                        <label for="email1" class="block text-900 text-xl font-medium mb-2">Email</label>
                        <input
                            id="email1"
                            name="email"
                            type="text"
                            [(ngModel)]="account.email"
                            placeholder="Địa chỉ Email"
                            pInputText
                            class="w-full md:w-30rem mb-5"
                            style="padding: 1rem"
                        />
                    </form>
                    <div
                        *ngIf="isSendEmailResetPasswordSuccess"
                        class="tw-mb-5 tw-p-2 tw-text-white tw-rounded"
                        style="background-color: #8bc34a"
                    >
                        Hệ thống đã gửi link khôi phục mật khẩu vào tài khoản email của bạn. Vui lòng kiểm tra email để
                        lấy mật khẩu!
                    </div>
                    <button
                        *ngIf="!isSendEmailResetPasswordSuccess"
                        pButton
                        pRipple
                        label="Gửi"
                        class="w-full p-3 text-xl"
                        (click)="onSubmitForgotPassword()"
                    ></button>
                    <button
                        *ngIf="isSendEmailResetPasswordSuccess"
                        pButton
                        pRipple
                        label="Đóng"
                        class="w-full p-3 text-xl"
                        (click)="account.visible = false"
                    ></button>
                </div>
            </div>
        </div>
    </p-dialog>
</div>
