import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, OnInit, TemplateRef, ViewChild, inject } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { isArray } from 'lodash';
import { ConfirmationService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';
import { ChipModule } from 'primeng/chip';
import { AuthService } from 'src/app/core/auth/auth.service';
import { TABLE_KEY } from 'src/app/models/constant';
import { Column, EventPopupSubmit } from 'src/app/models/interface';
import { ApproveService } from 'src/app/services/smart-qc/masterdata/approve.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { TableCommonService } from 'src/app/shared/table-module/table.common.service';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { CriteriaBuy, CriteriaBuySupplier, Po, Rfq } from 'src/app/models/interface/sc';
import { PoService } from 'src/app/services/sc/po/po.service';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { MAP_PO_STATE, PO_STATE } from 'src/app/models/constant/sc';
import { PopupComponent } from 'src/app/shared/components/popup/popup.component';
import { FileService } from 'src/app/shared/services/file.service';
import { DialogModule } from 'primeng/dialog';
import { FormCustomModule } from '../../../../shared/form-module/form.custom.module';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { PaginatorModule } from 'primeng/paginator';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { RfqService } from '../../../../services/sc/sc-bom/rfq.service';
import Common from '../../../../utils/common';
import { FormComponent } from '../../../../shared/form-module/form-base/form.component';

@Component({
    selector: 'app-sc-po',
    standalone: true,
    imports: [
        TableCommonModule,
        CommonModule,
        RouterModule,
        ButtonModule,
        TagModule,
        TooltipModule,
        ChipModule,
        SubHeaderComponent,
        HasAnyAuthorityDirective,
        PopupComponent,
        DialogModule,
        FormCustomModule,
        InputTextModule,
        InputTextareaModule,
        PaginatorModule,
        ReactiveFormsModule,
    ],
    templateUrl: './list.component.html',
    providers: [ApproveService, TableCommonService, PoService, RfqService],
})
export class ListComponent implements OnInit, AfterViewInit {
    // Inject service
    orderService = inject(PoService);
    fileService = inject(FileService);
    tableCommonService = inject(TableCommonService);
    loadingService = inject(LoadingService);
    confirmationService = inject(ConfirmationService);
    alertService = inject(AlertService);
    authService = inject(AuthService);
    priceAskService = inject(RfqService);

    // End inject service

    state: QueryObserverBaseResult<Rfq[]>;
    columns: Column[] = [];
    tableId: string = TABLE_KEY.PRICE_ASK_LIST;
    actionHeader: TemplateRef<Element>;
    bodyFilter: Record<string, unknown> = {
        contractIds: null,
        actionIds: null,
    };
    rowSelects: Po[] = [];
    mapState = MAP_PO_STATE;
    optionPoState = PO_STATE;

    // Popup create
    isOpenAddModal: boolean = false;
    addFormGroup: FormGroup;
    @ViewChild('form') formComponent!: FormComponent;
    // End Popup create

    constructor(
        private fb: FormBuilder,
        private router: Router,
    ) {}

    ngOnInit() {
        this.tableCommonService
            .init<Rfq>({
                tableId: this.tableId,
                queryFn: (filter) => this.priceAskService.getPageTableCustom(filter),
                configFilterRSQL: {
                    code: 'Text',
                    name: 'Text',
                    note: 'Text',
                    created: 'DateRange',
                    createdBy: 'Text',
                },
                filterUrl: true,
            })
            .subscribe((state) => {
                this.state = state;
            });

        this.tableCommonService.getRowSelect(this.tableId).subscribe((state) => {
            if (isArray(state)) {
                this.rowSelects = state;
            }
        });

        this.initAddForm();
    }

    initAddForm() {
        this.addFormGroup = this.fb.group({
            code: [{ value: null, disabled: true }, []],
            name: [null, [Validators.maxLength(255)]],
            note: [null, []],
        });

        Common.resetFormState(this.addFormGroup);
        if (this.formComponent) {
            this.formComponent.resetSubmitState();
        }
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.columns = [
                {
                    header: 'Mã đợt hỏi giá',
                    field: 'code',
                    type: 'link',
                    url: './{id}',
                    default: true,
                },
                {
                    field: 'name',
                    header: 'Tên đợt hỏi giá',
                    style: { 'max-width': '8rem' },
                },
                {
                    field: 'note',
                    header: 'Ghi chú',
                    style: { 'max-width': '8rem' },
                },
                {
                    field: 'created',
                    header: 'Thời gian tạo',
                    type: 'date',
                    format: 'dd/MM/yyyy',
                },
                {
                    field: 'createdBy',
                    header: 'Người tạo',
                    style: { 'max-width': '8rem' },
                },
            ];
        }, 0);
    }

    delete = (ids: number[]) => {
        return this.priceAskService.batchDelete(ids);
    };

    /*exportExcel(event: EventPopupSubmit<unknown>) {
        this.loadingService.show();
        this.orderService
            .exportExcel(
                this.tableCommonService.getColumnVisible(this.tableId).value,
                this.tableCommonService.getParams(this.tableId),
            )
            .subscribe({
                next: (res: Blob) => {
                    // Gọi hàm downloadBlob để tải file
                    this.fileService.downloadBlob(res, `Danh sách PO - ${new Date().getTime()}.xlsx`);
                    this.loadingService.hide();
                    event.close();
                },
                error: () => {
                    this.alertService.error('Có lỗi xảy ra');
                    this.loadingService.hide();
                },
            });
    }*/

    onSubmitCreate(formData) {
        this.loadingService.show();
        const data = this.addFormGroup.getRawValue();
        this.priceAskService.create(data).subscribe({
            next: (res) => {
                this.alertService.success('Thành công', 'Tạo đợt hỏi giá thành công');
                this.loadingService.hide();
                const rfq: Rfq = res.body as unknown as Rfq;
                this.router.navigate(['/sc/rfq/' + rfq.id]);
            },
            error: (error) => {
                this.loadingService.hide();
                this.alertService.handleError(error);
            },
        });
    }
}
