<p-table [value]="hsspComparison">
    <ng-template pTemplate="colgroup">
        <colgroup>
            <col style="width: 33%" />
            <col style="width: 37%" />
            <col style="width: 33%" />
        </colgroup>
    </ng-template>
    <ng-template pTemplate="header">
        <tr>
            <th></th>
            <th>{{ products[0]?.name }}</th>
            <th>{{ products[1]?.name }}</th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-row>
        <tr>
            <td class="font-bold">{{ row.label }}</td>
            <td>{{ row.key === 'versionProfile' ? (products[0]?.version ?? '-') : (products[0]?.lifecycleStage ?? '-') }}</td>
            <td>{{ row.key === 'versionProfile' ? (products[1]?.version ?? '-') : (products[1]?.lifecycleStage ?? '-') }}</td>
        </tr>
    </ng-template>
</p-table>
<app-tab-view [tabs]="itemsTab" (tabChange)="onTabChange($event)"></app-tab-view>
