<app-sub-header [items]="[{ label: 'Quản lý thông tin mua hàng' }, { label: 'Hỏi giá' }]" [action]="actionHeader"></app-sub-header>

<ng-template #actionHeader>
    <p-button
        (click)="isOpenAddModal = true; initAddForm()"
        label="Tạo mới"
        severity="primary"
        *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_rfq_edit']"
    />

    <!--<app-popup
        header="Xuất danh sách PO"
        label="Xuất danh sách"
        (onSubmit)="exportExcel($event)"
        typePopup="download"
        severity="success"
    ></app-popup>-->
</ng-template>
<div style="padding: 1rem 1rem 0 1rem">
    <app-table-common
        [tableId]="tableId"
        [columns]="columns"
        [data]="state.data"
        [loading]="state.isFetching"
        [filterTemplate]="filterTemplate"
        name="<PERSON>h sách đợt hỏi giá"
        [funcDelete]="delete"
        [authoritiesDelete]="['ROLE_SYSTEM_ADMIN', 'sc_rfq_delete']"
    >
        <ng-template #filterTemplate>
            <tr>
                <th></th>
                <th [appFilter]="[tableId, 'code']">
                    <app-filter-table [tableId]="tableId" field="code"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'name']">
                    <app-filter-table [tableId]="tableId" field="name"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'note']">
                    <app-filter-table [tableId]="tableId" field="note"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'created']" style="max-width: 8rem">
                    <app-filter-table [tableId]="tableId" [rsql]="true" field="created" type="date-range" placeholder="Thời gian tạo"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'createdBy']">
                    <app-filter-table [tableId]="tableId" field="createdBy"></app-filter-table>
                </th>
            </tr>
        </ng-template>
    </app-table-common>
</div>

<p-dialog [style]="{ minWidth: '500px' }" [(visible)]="isOpenAddModal" [modal]="true" [closable]="true" [header]="'Tạo mới đợt hỏi giá'">
    <div class="tw-mt-1">
        <app-form #form [formGroup]="addFormGroup" layout="horizontal" (onSubmit)="onSubmitCreate($event)">
            <app-form-item label="Mã đợt hỏi giá" isRequired="true">
                <input type="text" class="tw-w-full" pInputText formControlName="code" placeholder="Hệ thống tự sinh" />
            </app-form-item>
            <br />
            <app-form-item label="Tên đợt hỏi giá">
                <input placeholder="Tên đợt hỏi giá" type="text" class="tw-w-full" pInputText formControlName="name" />
            </app-form-item>
            <br />
            <app-form-item label="Ghi chú">
                <textarea placeholder="Ghi chú" rows="5" formControlName="note" pInputTextarea class="tw-w-full"></textarea>
            </app-form-item>
        </app-form>
        <div class="tw-flex tw-justify-end tw-mt-4 tw-space-x-3">
            <p-button label="Đóng" (click)="isOpenAddModal = false" severity="secondary" size="small"></p-button>
            <p-button label="Lưu" (click)="form.handleSubmit()" severity="primary" size="small"></p-button>
        </div>
    </div>
</p-dialog>
