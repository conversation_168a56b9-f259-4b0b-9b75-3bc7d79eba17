<app-sub-header [items]="itemsHeader" [action]="actionHeader">
    <ng-template #actionHeader>
        <p-button
            size="small"
            *ngIf="authService.isAdminOrPM()"
            label="Chấp nhận"
            severity="success"
            (click)="acceptMultiByPm()"
            [disabled]="rowSelects.length === 0"
        />
        <p-button size="small" label="Xuất Excel" (click)="exportExcel()" />
    </ng-template>
</app-sub-header>
<div style="padding: 1rem 1rem 0 1rem">
    <app-table-common
        [tableId]="tableId"
        [selectionMode]="authService.isAdminOrPM() ? 'multiple' : ''"
        [columns]="columns"
        [data]="state.data"
        [loading]="state.isFetching"
        [filterTemplate]="filterTemplate"
        [funcDelete]="null"
        name="Danh sách phê duyệt"
        [rowSelectable]="rowSelectable"
    >
        <ng-template #filterTemplate>
            <tr>
                <th></th>
                <th *ngIf="authService.isAdminOrPM()"></th>
                <th [appFilter]="[tableId, 'contractName']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="contractIds"
                        [rsql]="false"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            rsql: false,
                            paramForm: 'id',
                            url: '/smart-qc/api/contract/combobox',
                        }"
                        (onChange)="changeFilter($event, 'contractIds')"
                        placeholder="Tên dự án"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'actionName']" style="max-width: 10rem">
                    <app-filter-table
                        [tableId]="tableId"
                        field="actionIds"
                        [rsql]="false"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'display',
                            rsql: false,
                            param: 'name',
                            paramForm: 'ids',
                            url: '/smart-qc/api/action/combobox',
                            body: {
                                contractIds: bodyFilter.contractIds,
                            },
                        }"
                        (onChange)="changeFilter($event, 'actionIds')"
                        placeholder="Công việc"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'areaName']" style="max-width: 8rem">
                    <app-filter-table
                        [tableId]="tableId"
                        field="areaIds"
                        [rsql]="false"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            filterLocal: true,
                            paramForm: 'id',
                            url: '/smart-qc/api/area/search',
                        }"
                        placeholder="Tỉnh/Tp"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'stationName']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="stationIds"
                        [rsql]="false"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            rsql: false,
                            paramForm: 'ids',
                            url: '/smart-qc/api/station/combobox',
                            body: {
                                actionIds: bodyFilter.actionIds,
                                contractIds: bodyFilter.contractIds,
                            },
                        }"
                        placeholder="Trạm"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'subPM']" *ngIf="authService.isAdminOrPM()">
                    <app-filter-table
                        [tableId]="tableId"
                        field="subPMIds"
                        [rsql]="false"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'fullNameAndEmail',
                            rsql: false,
                            url: '/smart-qc/api/combobox/user',
                            body: {
                                privileges: ['ROLE_QC_SUBPM'],
                            },
                            filterLocal: true,
                        }"
                        placeholder="SubPM"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'employee']" *ngIf="authService.isSubPM()">
                    <app-filter-table
                        [tableId]="tableId"
                        field="employeeIds"
                        [rsql]="false"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'id',
                            rsql: false,
                            fieldLabel: 'fullNameAndEmail',
                            url: '/smart-qc/api/combobox/user',
                            body: {
                                privileges: ['ROLE_QC_EMPLOYEE'],
                            },
                            filterLocal: true,
                        }"
                        placeholder="Đội thi công"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'submitDate']" *ngIf="authService.isAdminOrPM()" style="max-width: 8rem">
                    <app-filter-table
                        [tableId]="tableId"
                        [rsql]="false"
                        field="startSubmitDate&endSubmitDate"
                        type="date-range"
                        placeholder="Ngày gửi"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'submitDateEp']" *ngIf="authService.isSubPM()" style="max-width: 8rem">
                    <app-filter-table
                        [tableId]="tableId"
                        [rsql]="false"
                        field="startSubmitDateEp&endSubmitDateEp"
                        type="date-range"
                        placeholder="Ngày gửi"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'status']" style="max-width: 8rem">
                    <app-filter-table
                        [tableId]="tableId"
                        field="statuses"
                        type="select"
                        [rsql]="false"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'lable',
                            options: authService.isAdminOrPM() ? optionStatusPM : optionStatus,
                        }"
                        placeholder="Trạng thái"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'responses']" style="width: 8rem">
                    <app-filter-table [tableId]="tableId" [rsql]="false" field="response"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'submitTime']" style="width: 8rem">
                    <app-filter-table [tableId]="tableId" [rsql]="false" field="submitTime"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'errors']" style="width: 8rem">
                    <app-filter-table [tableId]="tableId" [rsql]="false" field="error"></app-filter-table>
                </th>
            </tr>
        </ng-template>

        <ng-template #templateStatus let-rowData>
            <p-tag [severity]="getSeverity(rowData.state)" [value]="getStateText(rowData.state)"></p-tag>
        </ng-template>

        <ng-template #templateAction let-rowData>
            <a
                class="tw-py-1 tw-px-2 bg-blue-400 text-white tw-rounded"
                [routerLink]="
                    authService.isAdminOrPM()
                        ? rowData.id + '/' + rowData.contractId
                        : authService.isCustomer()
                          ? '/sqc/approve/customer/' + rowData.id
                          : '/sqc/approve/sub/' + rowData.id
                "
            >
                <i class="pi pi-eye" style="font-size: 1rem"></i>
            </a>
        </ng-template>

        <ng-template #templateResponses let-rowData>
            <p-chip *ngIf="rowData.responses" label="xem" icon="pi pi-eye" [pTooltip]="tooltipContent" tooltipPosition="left" tooltipStyleClass="tooltip" />

            <ng-template #tooltipContent>
                <div *ngFor="let item of rowData.responses ? rowData.responses.toString().split('*') : []">
                    <div class="item-text">{{ item }}</div>
                </div>
            </ng-template>
        </ng-template>
        <ng-template #templateErrors let-rowData>
            <p-chip *ngIf="rowData.errors" label="xem" icon="pi pi-eye" [pTooltip]="tooltipContent" tooltipPosition="left" tooltipStyleClass="tooltip" />

            <ng-template #tooltipContent>
                <div *ngFor="let item of rowData.errors ? rowData.errors.toString().split('*') : []">
                    <div class="item-text">{{ item }}</div>
                </div>
            </ng-template>
        </ng-template>
    </app-table-common>
</div>
