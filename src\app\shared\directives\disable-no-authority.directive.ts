import {
    Directive,
    Input,
    OnInit,
    OnDestroy,
    ElementRef,
    Renderer2,
    TemplateRef,
    ViewContainerRef,
} from '@angular/core';
import { AuthService } from '../../core/auth/auth.service'; // Adjust the path as necessary
import { Subscription } from 'rxjs';

@Directive({
    selector: '[appDisableNoAuthority]',
    standalone: true,
})
export class DisableNoAuthorityDirective implements OnInit, OnDestroy {
    private userSubscription: Subscription;
    private authorities: string[] = [];

    constructor(
        private authService: AuthService,
        private templateRef: TemplateRef<Element>,
        private viewContainer: ViewContainerRef,
        private renderer: Renderer2,
        private el: ElementRef,
    ) {}

    @Input()
    set disableNoAuthority(value: string[]) {
        this.authorities = value;
    }

    ngOnInit(): void {
        this.authService.userObserver.subscribe(() => {
            if (this.authService.hasAuthority(this.authorities)) {
                const embeddedViewRef = this.viewContainer.createEmbeddedView(this.templateRef);
                const embeddedViewRootEl = embeddedViewRef.rootNodes[0] as HTMLElement;
                this.renderer.setProperty(embeddedViewRootEl, 'disabled', true);
            } else {
            }
        });
    }

    ngOnDestroy(): void {
        if (this.userSubscription) {
            this.userSubscription.unsubscribe();
        }
    }
}
