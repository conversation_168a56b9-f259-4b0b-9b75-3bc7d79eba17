<p-panel [header]="'G<PERSON>i đoạn ' + (lot?.type === 0 ? '5' : '4') + ': N<PERSON><PERSON> thuế và thông quan hàng hóa'" [toggleable]="true" *ngIf="lot">
    <app-form #form [formGroup]="formGroup" layout="vertical">
        <div class="tw-grid tw-grid-cols-2 tw-gap-4">
            <app-form-item label="Tờ khai thông quan">
                <app-button-group-file
                    simpleUpload=""
                    (onFileSelected)="handleUploadFileCustoms($event)"
                    [attachment]="formGroup.getRawValue().clearanceAttachment"
                    formControlName="clearanceAttachmentId"
                    [types]="['excel']"
                ></app-button-group-file>
            </app-form-item>
            <div></div>
            <app-form-item label="Ngày thông quan">
                <p-calendar
                    [showButtonBar]="true"
                    placeholder="dd/MM/yyyy"
                    dateFormat="dd/mm/yy"
                    appendTo="body"
                    formControlName="clearanceDateCustom"
                ></p-calendar>
            </app-form-item>
            <hr style="margin: 0" class="tw-col-span-2" />
            <app-form-item label="Ghi chú" class="tw-col-span-2">
                <textarea rows="5" formControlName="note" pInputTextarea class="tw-w-full"></textarea>
            </app-form-item>
            <hr style="margin: 0" class="tw-col-span-2" />
            <app-form-item label="Đính kèm">
                <app-button-group-file
                    simpleUpload=""
                    (onFileSelected)="handleUploadFile($event)"
                    [attachments]="formGroup.getRawValue().attachments"
                    formControlName="attachmentIds"
                    [multiple]="true"
                ></app-button-group-file>

                <app-button-group-file
                    *ngIf="formGroup.getRawValue().attachments && formGroup.getRawValue().attachments.length > 0"
                    class="tw-col-span-2"
                    (onFileSelected)="handleUploadFile($event, 'attachments')"
                    [multiple]="true"
                    simpleUpload=""
                    formControlName="attachmentIds"
                ></app-button-group-file>
            </app-form-item>
        </div>
    </app-form>
    <div class="tw-flex tw-justify-end tw-space-x-3">
        <p-button label="Thông báo cho người tiếp nhận" severity="primary" size="small" (click)="visibleSubmit = true"></p-button>
    </div>
</p-panel>

<p-dialog
    header="Thông báo cho người tiếp nhận"
    [(visible)]="visibleSubmit"
    [style]="{ width: '80vw' }"
    [breakpoints]="{ '1199px': '80vw', '575px': '100vw' }"
    (onHide)="formGroupSubmit.reset()"
>
    <hr style="margin: 0" />
    <br />
    <app-form #formSubmit *ngIf="visibleSubmit" [formGroup]="formGroupSubmit" layout="vertical" (onSubmit)="sendNotification($event)">
        <app-form-item label="Nội dung">
            <textarea rows="5" formControlName="content" pInputTextarea class="tw-w-full"></textarea>
        </app-form-item>
        <app-form-item label="Người tiếp nhận" [isRequired]="true">
            <app-filter-table
                type="select"
                [configSelect]="{
                    fieldValue: 'id',
                    fieldLabel: 'email',
                    options: receivers,
                    filterLocal: true,
                }"
                (onChange)="handleChangeReceivers($event)"
            ></app-filter-table>
        </app-form-item>
    </app-form>
    <ng-template pTemplate="footer">
        <div>
            <p-button
                type="button"
                severity="primary"
                size="small"
                (click)="formSubmit.handleSubmit()"
                label="Xác nhận gửi"
                [disabled]="formGroupSubmit.invalid"
                *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_lot_edit']"
            ></p-button>
            <p-button
                type="button"
                label="Hủy"
                [text]="true"
                [raised]="true"
                size="small"
                severity="secondary"
                (click)="visibleSubmit = false; formGroupSubmit.reset()"
            ></p-button>
        </div>
    </ng-template>
</p-dialog>
