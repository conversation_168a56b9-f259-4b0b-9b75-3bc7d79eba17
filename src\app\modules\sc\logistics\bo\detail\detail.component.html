<app-sub-header
    [items]="[{ label: 'Quản lý yêu cầu vận chuyển', url: '/sc/bo' }, { label: oldbo ? oldbo.code : 'Tạo mới' }]"
    [action]="action"
></app-sub-header>

<ng-template #action>
    <p-button label="Lưu" (click)="form.handleSubmit()" severity="success" size="small" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_bo_edit']" />
    <p-button label="Đóng" routerLink="/sc/bo" severity="secondary" size="small" />
</ng-template>

<div class="tw-p-4">
    <app-form #form [formGroup]="formGroup" layout="vertical" (onSubmit)="onSubmit($event)">
        <p-panel header="Thông tin booking" [toggleable]="true">
            <div class="tw-grid lg:tw-grid-cols-2 tw-grid-cols-1 tw-gap-4">
                <app-form-item label="Mã BO">
                    <input type="text" class="tw-w-full" pInputText formControlName="code" placeholder="Hệ thống tự sinh" />
                </app-form-item>

                <app-form-item label="Tổng giá trị shipment dự kiến (USD)">
                    <app-inputNumber class="tw-w-full" formControlName="shipmentValue" mode="decimal" maxlength="20"></app-inputNumber>
                </app-form-item>

                <app-form-item label="Phân loại">
                    <app-filter-table
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'label',
                            options: TYPE_SHIPPING,
                            filterLocal: true,
                        }"
                        formControlName="type"
                    ></app-filter-table>
                </app-form-item>

                <app-form-item label="Điều kiện thanh toán" class="tw-row-span-2">
                    <textarea rows="6" pInputTextarea class="tw-w-full" formControlName="paymentCondition"></textarea>
                </app-form-item>

                <app-form-item label="Tên hàng">
                    <input type="text" class="tw-w-full" pInputText formControlName="goodsName" />
                </app-form-item>

                <app-form-item label="Tên NCC">
                    <input type="text" class="tw-w-full" pInputText formControlName="supplierName" />
                </app-form-item>

                <app-form-item label="Số PO">
                    <input type="text" class="tw-w-full" pInputText formControlName="poNumber" />
                </app-form-item>

                <app-form-item label="Địa chỉ NCC">
                    <input type="text" class="tw-w-full" pInputText formControlName="supplierAddress" />
                </app-form-item>

                <app-form-item label="Số thứ tự shipment của PO">
                    <app-inputNumber class="tw-w-full" formControlName="indexShipment"></app-inputNumber>
                </app-form-item>

                <app-form-item label="Thông tin NCC" class="tw-row-span-2">
                    <textarea rows="6" pInputTextarea class="tw-w-full" formControlName="supplierInfo"></textarea>
                </app-form-item>

                <app-form-item label="Mã kế toán/Mã vụ việc">
                    <input type="text" class="tw-w-full" pInputText formControlName="accountingCode" />
                </app-form-item>

                <app-form-item label="Khối lượng tổng của lô hàng (kg)">
                    <app-inputNumber class="tw-w-full" formControlName="totalWeight" mode="decimal" maxlength="20"></app-inputNumber>
                </app-form-item>

                <app-form-item label="Thời gian hàng hóa ready tại kho">
                    <p-calendar
                        [showButtonBar]="true"
                        placeholder="dd/MM/yyyy"
                        [showIcon]="true"
                        dateFormat="dd/mm/yy"
                        class="tw-w-full"
                        appendTo="body"
                        [formControl]="formGroup.get('readyDateCustom')"
                    ></p-calendar>
                </app-form-item>

                <div class="tw-grid tw-grid-flow-col tw-gap-4">
                    <app-form-item label="Số kiện của lô hàng (carton/pallet)">
                        <app-inputNumber class="tw-w-full" formControlName="packageNumber" mode="decimal" maxLength="20"></app-inputNumber>
                    </app-form-item>
                    <app-form-item label="&nbsp;" class="tw-col-span-2">
                        <app-autocomplete
                            optionLabel="name"
                            optionValue="id"
                            formControlName="unit"
                            [options]="packageTypes"
                            (onAdd)="handleAddPackagegType($event)"
                            (onDelete)="handleDeletePackagegType($event)"
                            [rowDelete]="rowDelete"
                        ></app-autocomplete>
                    </app-form-item>
                </div>

                <app-form-item [label]="formGroup?.get('type').value === 1 ? 'Thời gian dự kiến xuất khỏi nhà máy' : 'Thời gian yêu cầu về tới nhà máy'">
                    <p-calendar
                        [showButtonBar]="true"
                        placeholder="dd/MM/yyyy"
                        [showIcon]="true"
                        dateFormat="dd/mm/yy"
                        appendTo="body"
                        [formControl]="formGroup.get('requiredArrivedDateCustom')"
                        class="tw-w-full"
                    ></p-calendar>
                </app-form-item>

                <app-form-item label="Điều kiện giao hàng">
                    <input type="text" class="tw-w-full" pInputText formControlName="deliveryCondition" />
                </app-form-item>

                <app-form-item label="Phòng/Ban">
                    <app-filter-table
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            rsql: true,
                            url: '/sc/api/department/search',
                            paramForm: 'id',
                        }"
                        formControlName="departmentId"
                    ></app-filter-table>
                </app-form-item>

                <app-form-item label="Thông tin đính kèm">
                    <app-button-group-file
                        simpleUpload=""
                        (onFileSelected)="handleUploadFile($event, 'bo')"
                        [attachments]="formGroup.getRawValue().attachments"
                        formControlName="attachmentIds"
                        [multiple]="true"
                    ></app-button-group-file>
                    <app-button-group-file
                        *ngIf="formGroup.getRawValue().attachments && formGroup.getRawValue().attachments.length > 0"
                        class="tw-col-span-2"
                        (onFileSelected)="handleUploadFile($event, 'bo')"
                        [multiple]="true"
                        simpleUpload=""
                        formControlName="attachmentIds"
                    ></app-button-group-file>
                </app-form-item>
                <app-form-item class="tw-col-span-2" label="Người nhận hàng (Consignee)" *ngIf="formGroup?.get('type').value === 1">
                    <textarea rows="5" cols="30" pInputTextarea class="tw-w-full" formControlName="consignee"></textarea>
                </app-form-item>

                <app-form-item class="tw-col-span-2" label="Địa chỉ nhận hàng cuối cùng" *ngIf="formGroup?.get('type').value === 0">
                    <textarea rows="5" cols="30" pInputTextarea class="tw-w-full" formControlName="finalDeliveryAddress"></textarea>
                </app-form-item>
                <app-form-item label="Ghi chú" class="tw-col-span-2">
                    <textarea rows="5" cols="30" pInputTextarea class="tw-w-full" formControlName="note"></textarea>
                </app-form-item>
            </div>
        </p-panel>

        <br />
        <!--<ng-container formArrayName="boItems">
            <p-panel header="Thông tin vật tư">
                <ng-template pTemplate="icons">
                    <app-button-group-file
                        [urlError]="urlErrorBoItems"
                        service="/sc/api"
                        (onFileSelected)="handleUploadFile($event, 'boItems')"
                        (onClearFile)="handleClearFile('boItems')"
                        [attachment]="formGroup.getRawValue().attachmentItem"
                        urlTemplate="bo_items_template.xlsx"
                        [types]="['excel']"
                        errorWrongFileMessage="File import sai định dạng, vui lòng thử lại với file excel"
                    ></app-button-group-file>
                </ng-template>
                <p-table [value]="boItems.controls" styleClass="p-datatable-gridlines">
                    <ng-template pTemplate="header">
                        <tr>
                            <th style="width: 5rem" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_bo_edit']"></th>
                            <th>VNPT MAN PN <span class="tw-text-red-500">*</span></th>
                            <th class="tw-whitespace-nowrap">Mã NSX</th>
                            <th>Mô tả</th>
                            <th class="tw-whitespace-nowrap">Đơn giá <span class="tw-text-red-500">*</span></th>
                            <th class="tw-whitespace-nowrap">Số lượng <span class="tw-text-red-500">*</span></th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                        <tr *ngIf="item.value.isEdit" [formGroupName]="rowIndex">
                            <td *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_bo_edit']">
                                <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                    <button
                                        type="button"
                                        class="p-link tw-p-2 bg-green-300 hover:tw-bg-green-400 tw-text-white"
                                        (click)="saveBoItem(rowIndex)"
                                        pTooltip="Lưu"
                                        tooltipPosition="top"
                                    >
                                        <span class="pi pi-save"></span>
                                    </button>
                                    <button
                                        type="button"
                                        class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                        (click)="cancelCreate(rowIndex)"
                                        pTooltip="Hủy"
                                        tooltipPosition="top"
                                    >
                                        <span class="pi pi-times"></span>
                                    </button>
                                </div>
                            </td>
                            <td>
                                <app-form-item label="">
                                    <app-filter-table
                                        type="select-one"
                                        [configSelect]="{
                                            fieldValue: 'internalReference',
                                            fieldLabel: 'internalReference',
                                            rsql: true,
                                            url: '/sc/api/inventory-product/search',
                                            paramForm: 'internalReference',
                                            body: {
                                                productId: boItems.controls[rowIndex].get('productId')?.value,
                                                manufacturerId: boItems.controls[rowIndex].get('manufacturerId')?.value,
                                            },
                                        }"
                                        (onChange)="handleChangeInternalReference($event, rowIndex)"
                                        formControlName="internalReference"
                                    ></app-filter-table>
                                </app-form-item>
                            </td>
                            <td>
                                <app-form-item label="">
                                    <app-filter-table
                                        type="select-one"
                                        [configSelect]="{
                                            fieldValue: 'manufacturerPn',
                                            fieldLabel: 'manufacturerPn',
                                            rsql: true,
                                            url: '/sc/api/product-manpn/search',
                                            paramForm: 'manufacturerPn',
                                        }"
                                        formControlName="manPn"
                                        (onChange)="handleChangeManufacture($event, rowIndex)"
                                    ></app-filter-table>
                                </app-form-item>
                            </td>
                            <td>{{ item.getRawValue()?.productDescription }}</td>
                            <td>
                                <div class="tw-grid tw-grid-flow-col tw-gap-4">
                                    <app-form-item label="" class="tw-col-span-10">
                                        <app-inputNumber class="tw-w-full" formControlName="price" mode="decimal" placeholder="giá thành" maxlength="20" />
                                    </app-form-item>
                                    <app-form-item label="" class="tw-col-span-2">
                                        <p-dropdown
                                            [options]="UnitPriceArr"
                                            optionLabel="label"
                                            optionValue="value"
                                            appendTo="body"
                                            formControlName="unit"
                                            placeholder="tiền tệ"
                                            class="tw-w-full"
                                        />
                                    </app-form-item>
                                </div>
                            </td>
                            <td>
                                <app-form-item label="">
                                    <app-inputNumber class="tw-w-full" formControlName="quantity" placeholder="số lượng" />
                                </app-form-item>
                            </td>
                        </tr>
                        <tr *ngIf="!item.value.isEdit">
                            <td *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_bo_edit']">
                                <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                    <button
                                        type="button"
                                        class="p-link tw-p-2 bg-green-300 hover:tw-bg-green-400 tw-text-white"
                                        (click)="editBoItem(rowIndex)"
                                        pTooltip="Sửa"
                                        tooltipPosition="top"
                                        *ngIf="!isAddingBoItem && !isEditingBoItem"
                                    >
                                        <span class="pi pi-pencil"></span>
                                    </button>
                                    <button
                                        type="button"
                                        class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                        (click)="removeBoItem(rowIndex)"
                                        pTooltip="Xóa"
                                        tooltipPosition="top"
                                        *ngIf="!isAddingBoItem && !isEditingBoItem"
                                    >
                                        <span class="pi pi-trash"></span>
                                    </button>
                                </div>
                            </td>
                            <td>{{ item.getRawValue()?.internalReference }}</td>
                            <td class="tw-whitespace-nowrap">{{ item.getRawValue()?.manPn }}</td>
                            <td class="tw-whitespace-nowrap">{{ item.getRawValue()?.productDescription }}</td>
                            <td class="tw-whitespace-nowrap">
                                {{ item.getRawValue()?.price | currency: (item.getRawValue()?.unit === 0 ? 'USD' : 'VND') : 'symbol' : '1.0-4' }}
                                {{ MAP_TYPE_MONEY[item.getRawValue()?.unit] }}
                            </td>
                            <td class="tw-whitespace-nowrap">{{ item.getRawValue()?.quantity | number }}</td>
                        </tr>
                    </ng-template>
                </p-table>
                <div class="tw-mt-4">
                    <p-button
                        size="small"
                        [disabled]="isEditingBoItem || isAddingBoItem"
                        label="Thêm mới"
                        (onClick)="addBoItem()"
                        *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_bo_edit']"
                    ></p-button>
                </div>
            </p-panel>
        </ng-container>-->

        <br />
        <app-time-line
            *ngIf="oldbo"
            [items]="timelineItems"
            (onStatusClick)="onStatusSelected($event)"
            [activeValue]="activeTabStatus"
            [value]="oldbo?.status"
        ></app-time-line>

        <br />
        <p-tabView *ngIf="oldbo && activeTabStatus === 0">
            <p-tabPanel header="Forwarder" [disabled]="isAddingBoShipping || isEditingBoShipping">
                <app-form #formShippingMethod [formGroup]="formShippingMethodGroup" layout="vertical">
                    <ng-container formArrayName="boShippingMethods">
                        <p-table styleClass="p-datatable-gridlines" [value]="shippingMethods.controls" [scrollable]="true">
                            <ng-template pTemplate="header">
                                <tr>
                                    <ng-container *ngIf="oldbo?.status === activeTabStatus">
                                        <th style="max-width: 5rem" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_bo_edit']">Thao tác</th>
                                    </ng-container>
                                    <th style="min-width: 9rem">Tên forwarder <span class="tw-text-red-500">*</span></th>
                                    <th style="min-width: 10rem">Phương thức vận chuyển/ dịch vụ <span class="tw-text-red-500">*</span></th>
                                    <th style="min-width: 10rem">Cách thức vận chuyển</th>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                                <tr *ngIf="item.value.isEdit" [formGroupName]="rowIndex">
                                    <ng-container *ngIf="oldbo?.status === activeTabStatus">
                                        <td *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_bo_edit']">
                                            <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                                <button
                                                    type="button"
                                                    class="p-link tw-p-2 bg-green-300 hover:tw-bg-green-400 tw-text-white"
                                                    (click)="saveItemBoShipping(rowIndex)"
                                                    pTooltip="Lưu"
                                                    tooltipPosition="top"
                                                >
                                                    <span class="pi pi-save"></span>
                                                </button>
                                                <button
                                                    type="button"
                                                    class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                                    (click)="cancelCreateBoShipping(rowIndex)"
                                                    pTooltip="Hủy"
                                                    tooltipPosition="top"
                                                >
                                                    <span class="pi pi-times"></span>
                                                </button>
                                            </div></td
                                    ></ng-container>
                                    <td>
                                        <app-form-item label="">
                                            <app-filter-table
                                                type="select-one"
                                                [configSelect]="{
                                                    fieldValue: 'id',
                                                    fieldLabel: 'shortName',
                                                    rsql: true,
                                                    url: '/sc/api/logistics/search',
                                                    paramForm: 'id',
                                                    body: { type: 0 },
                                                }"
                                                formControlName="logisticFwdId"
                                                (onChange)="handleChangeLogistic($event, rowIndex)"
                                            ></app-filter-table>
                                        </app-form-item>
                                    </td>
                                    <td>
                                        <app-form-item label="">
                                            <app-autocomplete
                                                optionLabel="name"
                                                optionValue="id"
                                                formControlName="shippingMethodId"
                                                [options]="TYPE_SHIPPING_METHOD"
                                                (onAdd)="handleAddMethod($event)"
                                                (onDelete)="handleDeleteMethod($event)"
                                                [rowDelete]="rowDelete"
                                            ></app-autocomplete>
                                        </app-form-item>
                                    </td>
                                    <td>
                                        <app-form-item label="">
                                            <input pInputText class="tw-w-full" formControlName="roadNote" />
                                        </app-form-item>
                                    </td>
                                </tr>
                                <tr *ngIf="!item.value.isEdit" [formGroupName]="rowIndex">
                                    <ng-container *ngIf="oldbo?.status === activeTabStatus">
                                        <td *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_bo_edit']">
                                            <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                                <!--<button
                                                type="button"
                                                class="p-link tw-p-2 bg-green-300 hover:tw-bg-green-400 tw-text-white"
                                                (click)="editItemBoShipping(rowIndex)"
                                                pTooltip="Sửa"
                                                tooltipPosition="top"
                                                *ngIf="!isAddingBoShipping && !isEditingBoShipping"
                                            >
                                                <span class="pi pi-pencil"></span>
                                            </button>-->
                                                <button
                                                    type="button"
                                                    class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                                    (click)="deleteItemBoShipping(rowIndex)"
                                                    pTooltip="Xóa"
                                                    tooltipPosition="top"
                                                    *ngIf="!isAddingBoShipping && !isEditingBoShipping"
                                                >
                                                    <span class="pi pi-trash"></span>
                                                </button>
                                            </div>
                                        </td>
                                    </ng-container>
                                    <td>{{ item.getRawValue().logisticsFwd?.shortName }}</td>
                                    <td>{{ MAP_TYPE_SHIPPING_METHOD ? MAP_TYPE_SHIPPING_METHOD[item.getRawValue().shippingMethodId] : '' }}</td>
                                    <td>{{ item.getRawValue().roadNote }}</td>
                                </tr>
                            </ng-template>
                        </p-table>
                        <div class="tw-mt-3">
                            <ng-container *ngIf="oldbo?.status === activeTabStatus">
                                <p-button
                                    type="button"
                                    label="Chọn FWD"
                                    icon="pi pi-plus"
                                    severity="info"
                                    size="small"
                                    [disabled]="isEditingBoShipping || isAddingBoShipping"
                                    (click)="addBoShipping('forwarder')"
                                    *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_bo_edit']"
                                ></p-button>
                            </ng-container>
                        </div>
                    </ng-container>
                </app-form>
            </p-tabPanel>

            <p-tabPanel header="Bảo hiểm" [disabled]="isAddingBoShipping || isEditingBoShipping">
                <app-form #formShippingMethodInsurances [formGroup]="formShippingMethodInsurancesGroup" layout="vertical">
                    <ng-container formArrayName="boShippingInsurances">
                        <p-table styleClass="p-datatable-gridlines" [value]="shippingMethodInsurances.controls" [scrollable]="true">
                            <ng-template pTemplate="header">
                                <tr>
                                    <ng-container *ngIf="oldbo?.status === activeTabStatus">
                                        <th style="max-width: 5rem" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_bo_edit']">Thao tác</th>
                                    </ng-container>
                                    <th style="min-width: 9rem">Phương thức vận chuyển <span class="tw-text-red-500">*</span></th>
                                    <th style="min-width: 10rem">Nhà cung cấp bảo hiểm</th>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                                <tr *ngIf="item.value.isEdit" [formGroupName]="rowIndex">
                                    <ng-container *ngIf="oldbo?.status === activeTabStatus">
                                        <td *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_bo_edit']">
                                            <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                                <button
                                                    type="button"
                                                    class="p-link tw-p-2 bg-green-300 hover:tw-bg-green-400 tw-text-white"
                                                    (click)="saveItemBoShippingInsurances(rowIndex)"
                                                    pTooltip="Lưu"
                                                    tooltipPosition="top"
                                                >
                                                    <span class="pi pi-save"></span>
                                                </button>
                                                <button
                                                    type="button"
                                                    class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                                    (click)="cancelCreateBoShippingInsurances(rowIndex)"
                                                    pTooltip="Hủy"
                                                    tooltipPosition="top"
                                                >
                                                    <span class="pi pi-times"></span>
                                                </button>
                                            </div>
                                        </td>
                                    </ng-container>
                                    <td>{{ MAP_TYPE_SHIPPING_METHOD ? MAP_TYPE_SHIPPING_METHOD[item.getRawValue().shippingMethodId] : '' }}</td>
                                    <td>
                                        <app-form-item label="">
                                            <app-combobox
                                                fieldValue="id"
                                                fieldLabel="shortName"
                                                rsql="true"
                                                url="/sc/api/logistics/search"
                                                formControlName="logisticInsuranceIds"
                                                [body]="{ type: 1 }"
                                                (onChange)="handleChangeLogisticInsurance($event, rowIndex)"
                                            >
                                            </app-combobox>
                                        </app-form-item>
                                    </td>
                                </tr>
                                <tr *ngIf="!item.value.isEdit" [formGroupName]="rowIndex">
                                    <ng-container *ngIf="oldbo?.status === activeTabStatus">
                                        <td *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_bo_edit']">
                                            <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                                <button
                                                    type="button"
                                                    class="p-link tw-p-2 bg-green-300 hover:tw-bg-green-400 tw-text-white"
                                                    (click)="editItemBoShippingInsurances(rowIndex)"
                                                    pTooltip="Sửa"
                                                    tooltipPosition="top"
                                                    *ngIf="!isAddingBoShipping && !isEditingBoShipping"
                                                >
                                                    <span class="pi pi-pencil"></span>
                                                </button>
                                            </div>
                                        </td>
                                    </ng-container>
                                    <td>{{ MAP_TYPE_SHIPPING_METHOD ? MAP_TYPE_SHIPPING_METHOD[item.getRawValue().shippingMethodId] : '' }}</td>
                                    <td>
                                        <app-form-item label="" *ngIf="item.getRawValue()?.logisticInsuranceIds?.length > 0">
                                            <app-combobox
                                                [disabled]="true"
                                                fieldValue="id"
                                                fieldLabel="shortName"
                                                rsql="true"
                                                url="/sc/api/logistics/search"
                                                formControlName="logisticInsuranceIds"
                                                [body]="{ type: 1 }"
                                            >
                                            </app-combobox>
                                        </app-form-item>
                                    </td>
                                </tr>
                            </ng-template>
                        </p-table>
                    </ng-container>
                </app-form>
            </p-tabPanel>
        </p-tabView>

        <ng-container *ngIf="oldbo && activeTabStatus === 1">
            <br />
            <app-bo-negotiate
                #boNegotiateComponent
                [bo]="oldbo"
                [formBo]="formGroup"
                (onNegotiate)="onNegotiate($event)"
                [mapTypeShippingMethod]="MAP_TYPE_SHIPPING_METHOD"
            ></app-bo-negotiate>
        </ng-container>

        <ng-container *ngIf="oldbo && activeTabStatus === 2">
            <br />
            <p-table [value]="[oldbo]" styleClass="p-datatable-gridlines">
                <ng-template pTemplate="header">
                    <tr>
                        <th>Nội dung</th>
                        <th>Nhà cung cấp vận chuyển</th>
                        <th>Phương thức vận chuyển</th>
                        <th *ngIf="isShowEstimatedInsurances">Nhà cung cấp bảo hiểm</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-item>
                    <tr>
                        <td>Lựa chọn</td>
                        <td>
                            <app-filter-table
                                type="select-one"
                                [configSelect]="{
                                    fieldValue: 'id',
                                    fieldLabel: 'shortName',
                                    rsql: true,
                                    url: '/sc/api/logistics/search',
                                    param: 'id',
                                }"
                                [initValue]="item.logisticForwarderId"
                                [disabled]="true"
                            ></app-filter-table>
                        </td>
                        <td>{{ MAP_TYPE_SHIPPING_METHOD ? MAP_TYPE_SHIPPING_METHOD[item.shippingMethodId] : '' }}</td>

                        <td *ngIf="isShowEstimatedInsurances">
                            <app-filter-table
                                type="select-one"
                                [configSelect]="{
                                    fieldValue: 'id',
                                    fieldLabel: 'shortName',
                                    rsql: true,
                                    url: '/sc/api/logistics/search',
                                    param: 'id',
                                }"
                                [initValue]="item.logisticInsuranceId"
                                [disabled]="true"
                            ></app-filter-table>
                        </td>
                    </tr>
                </ng-template>
            </p-table>
            <br />
            <div class="tw-flex tw-justify-end tw-gap-4">
                <p-button label="Theo dõi" severity="primary" (click)="goToLotPage()" size="small"></p-button>
            </div>
        </ng-container>
    </app-form>
</div>
