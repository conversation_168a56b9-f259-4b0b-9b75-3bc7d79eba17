<div class="img-magnifier-container" *ngIf="!isRemoved">
    <p
        *ngIf="(authService.isAdminOrPM() || authService.isSubPM()) && !loading"
        class="tw-absolute bg-gray-300 tw-px-2"
        [style]="{
            top: '100%',
            left: '0px',
            transform: 'translate(0%, -100%)',
            'z-index': 3,
        }"
    >
        By: {{ uploadBy === 0 ? 'SubPM' : 'Đội thi công' }}
    </p>
    <img (click)="showDialog()" [appLazyLoadImage]="imageUrl" class="tw-w-full tw-h-auto tw-cursor-pointer" />
    <!-- <lib-ngx-image-zoom
        #imageZoom
        [thumbImage]="imageUrl"
        [fullImage]="imageUrl"
        zoomMode="toggle-click"
        (imagesLoaded)="onLoaded($event)"
        style="width: 100%; height: auto; cursor: zoom-in"
    ></lib-ngx-image-zoom> -->
    <!-- <div style="width: 100%; overflow : hidden;  position: relative;"
         [style.height.px]="containerHeight"
        #container>
        <img
            #img
            [src]="imageUrl"
            (click)="onClick($event)"
            (mousemove)="mouseMoveHandler($event)"
            style="cursor: zoom-in; width:100%; height: auto; position: absolute; object-fit: contain;"
            alt="img"
            (load)="onImageLoad()"
        />
    </div> -->
    <p-button
        *ngIf="remove"
        class="absolute-right-top"
        icon="pi pi-times"
        [rounded]="true"
        [text]="true"
        severity="danger"
        (click)="handleRemove()"
    ></p-button>
</div>

<p-dialog [modal]="true" [showHeader]="false" [(visible)]="visible">
    <ng-template pTemplate="content">
        <p-button
            class="tw-fixed tw-right-0 tw-top-0 tw-bg-gray-100/[0.3] tw-rounded-none p-link"
            icon="pi pi-times"
            [text]="true"
            severity="danger"
            (click)="visible = false"
        ></p-button>

        <lib-ngx-image-zoom
            #imageZoom
            [thumbImage]="imageUrl"
            [fullImage]="imageUrl"
            zoomMode="toggle-click"
            (imagesLoaded)="onLoaded($event)"
            style="width: 100%; height: auto; cursor: zoom-in"
            magnification="2"
        ></lib-ngx-image-zoom>
    </ng-template>
</p-dialog>
