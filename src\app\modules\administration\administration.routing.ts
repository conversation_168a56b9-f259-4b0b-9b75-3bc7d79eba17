import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { canAuthorize } from '../../core/auth/auth.guard';
import { DashBoardAdministrationRouting } from './dashboard/dashboard.routing';

@NgModule({
    imports: [
        RouterModule.forChild([
            {
                path: '',
                redirectTo: 'dashboard',
                title: 'Trang chủ',
                pathMatch: 'full',
            },

            {
                path: 'role',
                data: {
                    authorize: [
                        'ROLE_SYSTEM_ADMIN',
                        'ROLE_SUB_ADMIN',
                        'role_view',
                        'role_create',
                        'role_delete',
                        'role_edit',
                    ],
                },
                title: '<PERSON>ai trò',
                canActivate: [canAuthorize],
                children: [
                    {
                        path: '',
                        title: '<PERSON>ai trò',
                        loadComponent: () => import('./role/role.component').then((c) => c.RoleComponent),
                    },
                    {
                        path: ':id',
                        title: '<PERSON> tiết vai trò',
                        loadComponent: () => import('./role/role.edit.component').then((c) => c.RoleEditComponent),
                    },
                    {
                        path: 'create',
                        title: 'Tạo mới vai trò',
                        loadComponent: () => import('./role/role.edit.component').then((c) => c.RoleEditComponent),
                    },
                ],
            },
            {
                path: 'user',
                canActivate: [canAuthorize],
                data: {
                    authorize: [
                        'ROLE_SYSTEM_ADMIN',
                        'ROLE_SUB_ADMIN',
                        'user_view',
                        'user_create',
                        'user_delete',
                        'user_edit',
                    ],
                },
                children: [
                    {
                        path: '',
                        canActivate: [canAuthorize],
                        title: 'Danh sách người dùng',
                        loadComponent: () => import('./user/user.component').then((c) => c.UserComponent),
                        data: { mode: 'view' },
                    },
                    {
                        path: ':id',
                        canActivate: [canAuthorize],
                        title: 'Xem chi tiết người dùng',
                        loadComponent: () =>
                            import('./user/user-detail.component').then((c) => c.UserDetailComponent),
                        data: { mode: 'view' },
                    },
                    {
                        path: ':id/edit',
                        canActivate: [canAuthorize],
                        title: 'Sửa thông tin người dùng',
                        loadComponent: () =>
                            import('./user/user-detail.component').then((c) => c.UserDetailComponent),
                        data: {
                            mode: 'edit',
                            authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_SUB_ADMIN', 'user_create', 'user_edit'],
                        },
                    },
                    {
                        path: 'create',
                        canActivate: [canAuthorize],
                        title: 'Tạo mới người dùng',
                        loadComponent: () =>
                            import('./user/user-detail.component').then((c) => c.UserDetailComponent),
                        data: {
                            mode: 'create',
                            authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_SUB_ADMIN', 'user_create', 'user_edit'],
                        },
                    },
                ],
            },
            {
                path: 'privilege',
                data: { authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_SUB_ADMIN', 'privilege_view'] },
                title: 'Quyền',
                canActivate: [canAuthorize],
                loadComponent: () => import('./privilege/privilege.component').then((c) => c.PrivilegeComponent),
            },
            {
                path: 'process',
                data: { authorize: ['ROLE_SYSTEM_ADMIN'] },
                title: 'Tiến trình',
                canActivate: [canAuthorize],
                children: [
                    {
                        path: '',
                        data: { authorize: ['ROLE_SYSTEM_ADMIN'] },
                        title: 'Tiến trình',
                        canActivate: [canAuthorize],
                        loadComponent: () => import('./process/process.component').then((c) => c.ProcessComponent),
                    },
                    {
                        path: 'create',
                        data: { authorize: ['ROLE_SYSTEM_ADMIN'] },
                        title: 'Tạo mới tiến trình',
                        canActivate: [canAuthorize],
                        loadComponent: () =>
                            import('./process/process.edit.component').then((c) => c.ProcessEditComponent),
                    },
                    {
                        path: ':id',
                        data: { authorize: ['ROLE_SYSTEM_ADMIN'] },
                        title: 'Chi tiết tiến trình',
                        canActivate: [canAuthorize],
                        loadComponent: () =>
                            import('./process/process.edit.component').then((c) => c.ProcessEditComponent),
                    },
                ],
            },

            {
                path: 'state',
                data: { authorize: ['ROLE_SYSTEM_ADMIN'] },
                title: 'Trạng thái',
                canActivate: [canAuthorize],
                loadComponent: () => import('./state/state.component').then((c) => c.StateComponent),
            },
            DashBoardAdministrationRouting,
        ]),
    ],
    exports: [RouterModule],
})
export class AdministrationRoutingModule {}
