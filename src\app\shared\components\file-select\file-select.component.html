<ng-component>
    <span class="qc-upload-bttn">
        <div style="display: flex">
            <span class="material-icons input-upload-icon" style="color: #3490dc; margin: auto; font-size: x-large">
                backup
            </span>
            <span style="margin-left: 9px; margin-top: 4px">Chọn file</span>
        </div>
        <input
            #fileInput
            type="file"
            accept=".xls, .xlsx, xlsm"
            class="qc-upload-input"
            (input)="onSelectFile($event)"
        />
    </span>
    <div *ngIf="!errorFileUrl" class="tw-mt-2">
        Vui lòng tải file mẫu
        <a class="tw-text-green-500" [href]="templateUrl">tại đây</a>
    </div>
    <div *ngIf="errorFileUrl" class="tw-mt-2">
        Tải xuống file lỗi <a download [href]="errorFileUrl" class="text-red-500">tại đây</a>
    </div>
</ng-component>
