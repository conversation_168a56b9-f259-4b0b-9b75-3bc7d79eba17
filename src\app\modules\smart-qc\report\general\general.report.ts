import { Component } from '@angular/core';
import { DropdownModule } from 'primeng/dropdown';
import { HasAnyAuthorityDirective } from '../../../../shared/directives/has-any-authority.directive';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { <PERSON><PERSON>orOf, NgIf } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ContractService } from '../../../../services/smart-qc/masterdata/contract.service';
import { ButtonModule } from 'primeng/button';
import { GeneralReportService } from '../../../../services/smart-qc/report/general.report.service';
import { LoadingService } from '../../../../shared/services/loading.service';
import { TableModule } from 'primeng/table';
import { CalendarModule } from 'primeng/calendar';
import { finalize } from 'rxjs';
import { AuthService } from '../../../../core/auth/auth.service';
import Common from '../../../../utils/common';
import { DialogModule } from 'primeng/dialog';
import { environment } from '../../../../../environments/environment';
import { EventChangeFilter } from '../../../../models/interface';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';

@Component({
    selector: 'app-general',
    templateUrl: './general.report.html',
    standalone: true,
    imports: [
        DropdownModule,
        HasAnyAuthorityDirective,
        InputTextModule,
        InputTextareaModule,
        NgIf,
        ReactiveFormsModule,
        ButtonModule,
        TableModule,
        NgForOf,
        CalendarModule,
        DialogModule,
        TableCommonModule,
        SubHeaderComponent,
    ],
})
export class GeneralReportComponent {
    reportForm: FormGroup;
    data: unknown;
    itemsHeader = [{ label: 'Báo cáo' }, { label: 'Báo cáo tiến độ tổng hợp', url: '' }];

    showReport: boolean = false;
    isDisplayDistrict: boolean = false;
    reportDate: string;
    isOpenModalExport: boolean = false;
    downloadExportFileLink: string = '';

    constructor(
        private formBuilder: FormBuilder,
        private contractService: ContractService,
        private generalReportService: GeneralReportService,
        private loadingService: LoadingService,
        protected authService: AuthService,
    ) {
        this.reportForm = this.formBuilder.group({
            contractId: [null, [Validators.required]],
            areaId: [null, []],
            districtId: [null, []],
            date: [null, []],
        });
    }

    selectContract(data: EventChangeFilter) {
        this.reportForm.patchValue({
            contractId: data.value,
            areaId: null,
            districtId: null,
        });
    }

    selectArea(data: EventChangeFilter) {
        this.reportForm.patchValue({
            areaId: data.value,
            districtId: null,
        });
    }

    selectDistrict(data: EventChangeFilter) {
        this.reportForm.patchValue({
            districtId: data.value,
        });
    }

    getReport() {
        if (this.reportForm.valid) {
            let date: Date = this.reportForm.get('date').value;
            if (!date) {
                date = Common.getCurrentDateFromHCMTimeZone();
            }
            this.reportDate = Common.formatDateWithPattern(Common.getTimeFromHCMTimeZone(date), 'dd/mm/yyyy');

            const filter = {
                contractId: this.reportForm.get('contractId').value,
                areaId: this.reportForm.get('areaId').value,
                districtId: this.reportForm.get('districtId').value,
                date: Common.getTimeFromHCMTimeZone(date),
            };
            this.loadingService.show();
            this.generalReportService
                .getReport(filter)
                .pipe(
                    finalize(() => {
                        this.loadingService.hide();
                    }),
                )
                .subscribe({
                    next: (res) => {
                        this.data = res;
                        this.showReport = true;
                        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
                        this.reportForm.get('areaId').value ? (this.isDisplayDistrict = true) : (this.isDisplayDistrict = false);
                    },
                    complete: () => {},
                });
        } else {
            Common.markAllAsTouchedForm(this.reportForm);
        }
    }

    getActionInfo(row, actionName, key) {
        const actionInfo = row.actionRateInfos.find((info) => info.actionName === actionName);
        if (!actionInfo) {
            if (key === 'completeQuantity') {
                return '0';
            }
            if (key === 'rateComplete') {
                return '0%';
            }
        }
        if (key === 'rateComplete') {
            return actionInfo[key] + '%';
        }
        return actionInfo[key];
    }

    export() {
        // Init filter
        let date: number = null;
        if (this.reportForm.get('date').value) {
            const selectedDate = new Date(this.reportForm.get('date').value);
            date = selectedDate.getTime();
        } else {
            const selectedDate = new Date();
            date = selectedDate.getTime();
        }
        const filter = {
            contractId: this.reportForm.get('contractId').value,
            areaId: this.reportForm.get('areaId').value,
            districtId: this.reportForm.get('districtId').value,
            date: date,
        };
        this.loadingService.show();
        this.generalReportService
            .exportGeneralReport(filter)
            .pipe(
                finalize(() => {
                    this.loadingService.hide();
                }),
            )
            .subscribe({
                next: (res: unknown) => {
                    this.isOpenModalExport = true;
                    this.downloadExportFileLink = environment.HOST_GW + '/smart-qc/api/download?filePath=' + res['body']['message'];
                },
                error: () => {},
                complete: () => {},
            });
    }
}
