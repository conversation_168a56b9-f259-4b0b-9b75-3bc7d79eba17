import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ConfirmationService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { AlertService } from 'src/app/shared/services/alert.service';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { Card, Maintenance, MaintenanceCard, Station } from '../../../../models/interface/smart-qc';
import { MaintenanceService } from 'src/app/services/smart-qc/maintenance/maintenance.service';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { FormArray, FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { DialogModule } from 'primeng/dialog';
import { SkeletonLoadingComponent } from 'src/app/shared/components/skeleton-loading/skeleton-loading.component';
import { uniqueFieldValidator } from 'src/app/utils/validator';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { CalendarModule } from 'primeng/calendar';
import { InputTextModule } from 'primeng/inputtext';
import { InputNumberModule } from 'primeng/inputnumber';
import { PanelModule } from 'primeng/panel';
import { EventChangeFilter } from 'src/app/models/interface';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { TableModule } from 'primeng/table';
import { ContractService } from 'src/app/services/smart-qc/masterdata/contract.service';
import { MaintenanceCardService } from 'src/app/services/smart-qc/maintenance/maintenance-card.service';
import { forkJoin } from 'rxjs';

@Component({
    selector: 'app-maintenance-edit',
    standalone: true,
    imports: [
        CommonModule,
        ButtonModule,
        DialogModule,
        FormCustomModule,
        SkeletonLoadingComponent,
        InputTextareaModule,
        CalendarModule,
        InputTextModule,
        InputNumberModule,
        PanelModule,
        TableCommonModule,
        TableModule,
        ReactiveFormsModule,
    ],
    templateUrl: './maintenance.edit.component.html',
    providers: [MaintenanceService, MaintenanceCardService],
})
export class MaintenanceEditComponent implements OnInit {
    constructor(
        private readonly loadingService: LoadingService,
        private readonly confirmationService: ConfirmationService,
        private readonly alertService: AlertService,
        private readonly maintenanceService: MaintenanceService,
        private readonly maintenanceCardService: MaintenanceCardService,
        private readonly contractService: ContractService,
        private fb: FormBuilder,
    ) {}

    itemsHeader = [{ label: 'Quản lý lỗi sau triển khai', url: '/sqc/maintenance' }, { label: 'Tạo mới' }];

    // data
    @Input({ required: true }) isVisible: boolean = false;
    @Input({ required: false }) oldId: number;
    @Input({ required: false }) stationId: number;
    @Output() onSuccess = new EventEmitter();
    @Output() onClose = new EventEmitter();

    formGroup: FormGroup;
    isDisabled: boolean = false;
    isLoading: boolean = false;
    oldMaintenance: Maintenance;
    isEditing: boolean = false;
    isAdding: boolean = false;
    backUpItem: MaintenanceCard;

    optionSubPM = [];

    // filter
    objectFilter = {
        stationName: null,
        stationId: null,
        contractId: null,
        cardName: null,
    };

    ngOnInit() {
        if (this.oldId) {
            this.isLoading = true;
            this.isDisabled = true;
            setTimeout(() => {
                forkJoin([
                    this.maintenanceService.getOne(this.oldId),
                    this.contractService.getContractMaintenance(this.stationId),
                ]).subscribe({
                    next: ([res1, res2]) => {
                        this.oldMaintenance = res1.body;
                        this.isLoading = false;
                        this.objectFilter = {
                            stationName: this.oldMaintenance.stationName,
                            stationId: this.oldMaintenance.stationId,
                            contractId: this.oldMaintenance.contractId,
                            cardName: null,
                        };
                        this.optionSubPM = res2.subPms || [];
                        this.initForm(this.oldMaintenance);
                    },
                });
            });
        } else {
            this.isDisabled = false;
            this.initForm(null);
        }
    }

    initForm(maintenance: Maintenance | null) {
        this.formGroup = this.fb.group({
            id: new FormControl({
                value: maintenance?.id,
                disabled: this.isDisabled,
            }),
            contractId: new FormControl({
                value: maintenance ? maintenance.contractId : null,
                disabled: this.isDisabled,
            }),
            contractName: new FormControl({
                value: maintenance ? maintenance.contractName : null,
                disabled: true,
            }),
            areaId: new FormControl({
                value: maintenance ? maintenance.areaId : null,
                disabled: this.isDisabled,
            }),
            areaName: new FormControl({
                value: maintenance ? maintenance.areaName : null,
                disabled: true,
            }),
            stationName: new FormControl({
                value: maintenance ? maintenance.stationName : null,
                disabled: this.isDisabled,
            }),

            stationId: new FormControl(
                {
                    value: maintenance ? maintenance.stationId : null,
                    disabled: this.isDisabled,
                },
                [Validators.required],
            ),
            subPmId: new FormControl(maintenance ? maintenance.subPmId : null, [Validators.required]),

            createDateCustom: new FormControl(
                maintenance?.createDate ? new Date(maintenance.createDate) : null,

                [Validators.required],
            ),
            doneDateCustom: new FormControl(
                maintenance?.doneDate ? new Date(maintenance.doneDate) : null,

                [Validators.required],
            ),
            note: new FormControl(maintenance?.note),
            backTimes: new FormControl(maintenance?.backTimes, [Validators.required]),
            processDetail: new FormControl(maintenance?.processDetail, [Validators.required]),
            hw: new FormControl(maintenance?.hw),
            maintenanceCards: this.fb.array(this.initItems(maintenance?.maintenanceCards || [])),
        });
    }

    initItems(items: MaintenanceCard[]): FormGroup[] {
        return items.map((item) =>
            this.fb.group({
                id: [item?.id],
                created: [item?.created],
                updated: [item?.updated],
                createdBy: [item?.createdBy],
                updatedBy: [item?.updatedBy],
                tenantId: [item?.tenantId],
                active: [item?.active],
                maintenanceId: [item?.maintenanceId, Validators.required],
                cardId: [item?.cardId, [Validators.required, uniqueFieldValidator('cardId', 'Tên card đã tồn tại')]],
                cardName: [item?.cardName, Validators.required],
                quantity: [item?.quantity, [Validators.required, Validators.min(1), Validators.max(99)]],
                isEdit: [false],
            }),
        );
    }

    get maintenanceCards(): FormArray {
        return this.formGroup.get('maintenanceCards') as FormArray;
    }

    addItem(): void {
        const newItem = this.fb.group({
            maintenanceId: [this.oldId],
            cardId: [null, [Validators.required, uniqueFieldValidator('cardId', 'Tên card đã tồn tại')]],
            cardName: [null],
            quantity: [null, [Validators.required, Validators.min(1)]],
            isEdit: [true],
        });
        this.maintenanceCards.push(newItem);
        this.isAdding = true;
    }

    editItem(index: number): void {
        const item = this.maintenanceCards.at(index);
        this.backUpItem = item.getRawValue();
        item.patchValue({ isEdit: true });
        this.isEditing = true;
    }

    saveItem(index: number): void {
        const item = this.maintenanceCards.at(index);

        const itemValue = item.getRawValue();

        // Kiểm tra tính hợp lệ của item
        if (item.valid) {
            item.enable();

            // Cập nhật isEdit thành false
            item.setValue({ ...itemValue, isEdit: false, cardName: this.objectFilter.cardName });

            // Đặt trạng thái isEditing thành false
            this.isEditing = false;
            this.isAdding = false;

            if (this.oldId) {
                this.loadingService.show();
                if (item.getRawValue()?.id) {
                    this.maintenanceCardService.update(item.getRawValue()).subscribe({
                        next: () => {
                            this.loadingService.hide();
                        },
                        error: () => {
                            this.loadingService.hide();
                        },
                    });
                } else {
                    this.maintenanceCardService.create(item.getRawValue()).subscribe({
                        next: () => {
                            this.loadingService.hide();
                        },
                        error: () => {
                            this.loadingService.hide();
                        },
                    });
                }
            }
        }
    }

    cancelCreate(index: number): void {
        if (this.isAdding) {
            this.maintenanceCards.removeAt(index);
        } else if (this.isEditing) {
            this.maintenanceCards.at(index).enable();
            this.maintenanceCards.at(index).patchValue(this.backUpItem);
        }
        this.isAdding = false;
        this.isEditing = false;
    }

    removeItem(index: number): void {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                const item = this.maintenanceCards.at(index).getRawValue() as MaintenanceCard;
                if (item?.id) {
                    this.loadingService.show();
                    this.maintenanceCardService.delete(item.id).subscribe({
                        next: () => {
                            this.isEditing = false;

                            this.maintenanceCards.removeAt(index);
                            this.loadingService.hide();
                        },
                        error: () => {
                            this.loadingService.hide();
                        },
                    });
                } else {
                    this.isEditing = false;

                    this.maintenanceCards.removeAt(index);
                }
            },
        });
    }

    closeDialog(): void {
        this.isVisible = false;
        this.formGroup?.reset();
        this.onClose.emit();
    }

    handleChangeStationName(e: EventChangeFilter) {
        if (e.value && e.objects.length > 0) {
            const station = e.objects[0] as Station;
            this.objectFilter.stationName = station?.name;
        } else {
            this.objectFilter.stationName = null;
            this.formGroup.get('stationId').patchValue(null);
            this.formGroup.get('stationName').patchValue(null);
            this.formGroup.get('contractId').patchValue(null);
            this.formGroup.get('contractName').patchValue(null);
            this.formGroup.get('areaId').patchValue(null);
            this.formGroup.get('areaName').patchValue(null);
            this.formGroup.get('subPmId').patchValue(null);
        }
    }

    handleChangeStationId(e: EventChangeFilter) {
        this.objectFilter.stationName = e.value;

        if (e.value) {
            const station = e.objects[0] as Station;

            this.formGroup.get('stationName').setValue(station.name);
            this.contractService.getContractMaintenance(station.id).subscribe({
                next: (res) => {
                    this.formGroup.get('contractId').patchValue(res?.id);
                    this.formGroup.get('contractName').patchValue(res?.name);
                    this.formGroup.get('areaId').patchValue(res?.areaId);
                    this.formGroup.get('areaName').patchValue(res?.areaName);
                    if (res && res.subPms.length > 0) {
                        this.optionSubPM = res.subPms;
                        if (
                            (this.optionSubPM[0].completedTasks && this.optionSubPM[0].completedTasks > 0) ||
                            res.subPms.length === 1
                        ) {
                            this.formGroup.get('subPmId').patchValue(res?.subPms[0].id);
                        } else {
                            this.formGroup.get('subPmId').patchValue(null);
                        }
                    } else {
                        this.optionSubPM = [];
                        this.formGroup.get('subPmId').patchValue(null);
                    }
                },
            });
        } else {
            this.formGroup.get('stationName').patchValue(null);
            this.formGroup.get('contractId').patchValue(null);
            this.formGroup.get('contractName').patchValue(null);
            this.formGroup.get('areaId').patchValue(null);
            this.formGroup.get('areaName').patchValue(null);
            this.formGroup.get('subPmId').patchValue(null);
        }
    }

    handleChangeCard(e: EventChangeFilter) {
        if (e.value && e.objects.length > 0) {
            const card = e.objects[0] as Card;

            this.objectFilter.cardName = card.name;
        } else {
            this.objectFilter.cardName = null;
        }
    }

    handleSubmit(): void {
        if (this.isEditing || this.isAdding) {
            this.alertService.error('Vui lòng hoàn thành phiếu');
            return;
        }
        if (this.formGroup.valid) {
            const maintenance: Maintenance = this.formGroup.getRawValue();
            const createDate = maintenance?.createDateCustom ? maintenance?.createDateCustom.getTime() : null;
            const doneDate = maintenance?.doneDateCustom ? maintenance?.doneDateCustom.getTime() : null;
            this.loadingService.show();
            if (maintenance?.id) {
                this.maintenanceService
                    .update({ ...this.oldMaintenance, ...maintenance, doneDate, createDate })
                    .subscribe({
                        next: () => {
                            this.alertService.success('Thành công');
                            this.onSuccess.emit();
                            this.closeDialog();
                            this.loadingService.hide();
                        },
                        error: (res) => {
                            this.alertService.handleError(res);
                            this.loadingService.hide();
                        },
                    });
            } else {
                this.maintenanceService.create({ ...maintenance, doneDate, createDate }).subscribe({
                    next: () => {
                        this.alertService.success('Thành công');
                        this.onSuccess.emit();
                        this.closeDialog();
                        this.loadingService.hide();
                    },
                    error: (res) => {
                        this.alertService.handleError(res);
                        this.loadingService.hide();
                    },
                });
            }
        }
    }
}
