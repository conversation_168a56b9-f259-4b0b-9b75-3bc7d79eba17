import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PanelModule } from 'primeng/panel';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { FormCustomModule } from '../../../../../../shared/form-module/form.custom.module';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { CalendarModule } from 'primeng/calendar';
import { ButtonModule } from 'primeng/button';
import {
    Lot,
    PaymentLot,
    PaymentLotDelivery,
    PaymentLotDeliverPort,
    PaymentLotImportPort,
    PaymentLotLocal,
    PaymentLotTax,
} from '../../../../../../models/interface/sc';
import { DialogModule } from 'primeng/dialog';
import { LotService } from 'src/app/services/sc/lot/lot.service';
import { FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { InputNumberModule } from 'primeng/inputnumber';
import { FieldsetModule } from 'primeng/fieldset';
import { PaymentLotService } from 'src/app/services/sc/lot/payment-lot.service';
import { ApiResponse, User } from 'src/app/models/interface';
import { ButtonGroupFileComponent } from 'src/app/shared/components/button-group-file/button-group-file.component';
import { PaymentLotLocalService } from 'src/app/services/sc/lot/payment-lot-local.service';
import { PaymentLotDeliveryPortService } from 'src/app/services/sc/lot/payment-lot-delivery-port.service';
import { PaymentLotDeliveryService } from 'src/app/services/sc/lot/payment-lot-delivery.service';
import { PaymentLotTaxService } from 'src/app/services/sc/lot/payment-lot-tax.service';
import { PaymentLotImportPortService } from 'src/app/services/sc/lot/payment-lot-import-port.service';
import { InputNumberComponent } from 'src/app/shared/components/inputnumber/inputnumber.component';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { EditableInputComponent } from '../../../../../../shared/edit/editable-input.component';
import { debounce } from 'lodash';

@Component({
    selector: 'app-lot-state-seven',
    standalone: true,
    templateUrl: './state-seven.component.html',
    styleUrls: ['./state-seven.component.scss'],
    imports: [
        CommonModule,
        PanelModule,
        InputTextModule,
        InputNumberModule,
        InputTextareaModule,
        FormCustomModule,
        ReactiveFormsModule,
        OverlayPanelModule,
        CalendarModule,
        FormCustomModule,
        ButtonModule,
        DialogModule,
        FieldsetModule,
        ButtonGroupFileComponent,
        InputNumberComponent,
        HasAnyAuthorityDirective,
        EditableInputComponent,
    ],
    providers: [
        LotService,
        PaymentLotService,
        PaymentLotLocalService,
        PaymentLotDeliveryPortService,
        PaymentLotDeliveryService,
        PaymentLotTaxService,
        PaymentLotImportPortService,
    ],
})
export class StateSevenComponent implements OnChanges, OnInit {
    @Input() lot: Lot;
    @Input() receivers: User[];

    // Form group
    formPaymentLot: FormGroup;
    formPaymentLotTax: FormGroup;
    formPaymentLotLocal: FormGroup;
    formPaymentLotImportPort: FormGroup;
    formPaymentLotDeliverPortExport: FormGroup;
    formPaymentLotDeliverPortImport: FormGroup;
    formPaymentLotDelivery: FormGroup;

    paymentLot: PaymentLot;
    paymentLotTax: PaymentLotTax;
    paymentLotLocal: PaymentLotLocal;
    paymentLotImportPort: PaymentLotImportPort;
    paymentLotDeliverPortExport: PaymentLotDeliverPort;
    paymentLotDeliverPortImport: PaymentLotDeliverPort;
    paymentLotDeliver: PaymentLotDelivery;
    paymentResult: { totalFeeAfterTax: number; totalFeeDifference: number; totalFeePreTax: number };

    //
    visibleSubmit: boolean = false;
    @Output('onComplete') onComplete: EventEmitter<Lot> = new EventEmitter();
    debouncedRefreshPaymentSummary: () => void;
    isLoadingPaymentSummary: boolean = false;
    constructor(
        private fb: FormBuilder,
        private loadingService: LoadingService,
        private alertService: AlertService,
        private lotService: LotService,
        private paymentLotService: PaymentLotService,
        private paymentLotLocalService: PaymentLotLocalService,
        private paymentLotDeliveryPortService: PaymentLotDeliveryPortService,
        private paymentLotDeliveryService: PaymentLotDeliveryService,
        private paymentLotTaxService: PaymentLotTaxService,
        private paymentLotImportPortService: PaymentLotImportPortService,
    ) {
        this.initPaymentLotTax();
        this.initPaymentLotLocal();
        this.initPaymentLotImportPort();
        this.initPaymentLotDeliverPortExport();
        this.initPaymentLotDeliverPortImport();
        this.initPaymentLotDeliver();
    }
    ngOnInit(): void {
        this.debouncedRefreshPaymentSummary = debounce(() => {
            this.refreshPaymentSummary();
        }, 500); // 500ms debounce
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['lot'] && !changes['lot'].previousValue && changes['lot'].currentValue) {
            this.loadData();
        }
    }

    loadData() {
        this.loadingService.show();
        this.lotService.getInfoPaymentLot(this.lot.id).subscribe({
            next: (res) => {
                this.initPaymentLot(res.paymentLot);
                this.initPaymentLotTax(res.paymentLotTax);
                this.initPaymentLotLocal(res.paymentLotLocal);
                this.initPaymentLotImportPort(res.paymentLotImportPort);
                this.initPaymentLotDeliverPortExport(res.paymentLotDeliverPortExport);
                this.initPaymentLotDeliverPortImport(res.paymentLotDeliverPortImport);
                this.initPaymentLotDeliver(res.paymentLotDelivery);
                this.paymentLot = res.paymentLot;
                this.paymentLotTax = res.paymentLotTax;
                this.paymentLotLocal = res.paymentLotLocal;
                this.paymentLotImportPort = res.paymentLotImportPort;
                this.paymentLotDeliverPortImport = res.paymentLotDeliverPortImport;
                this.paymentLotDeliverPortExport = res.paymentLotDeliverPortExport;
                this.paymentLotDeliver = res.paymentLotDelivery;
                this.paymentResult = {
                    totalFeeAfterTax: res.totalFeeAfterTax,
                    totalFeeDifference: res.totalFeeDifference,
                    totalFeePreTax: res.totalFeePreTax,
                };
                this.loadingService.hide();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    refreshPaymentSummary() {
        this.isLoadingPaymentSummary = true;

        this.lotService.getInfoPaymentLot(this.lot.id).subscribe({
            next: (res) => {
                this.paymentResult = {
                    totalFeeAfterTax: res.totalFeeAfterTax,
                    totalFeeDifference: res.totalFeeDifference,
                    totalFeePreTax: res.totalFeePreTax,
                };
                this.isLoadingPaymentSummary = false;
            },
            error: () => {
                this.isLoadingPaymentSummary = false;
            },
        });
    }

    private initPaymentLot(paymentLot?: PaymentLot) {
        this.formPaymentLot = new FormGroupCustom(this.fb, {
            id: [paymentLot?.id],
            created: [paymentLot?.created],
            updated: [paymentLot?.updated],
            createdBy: [paymentLot?.createdBy],
            updatedBy: [paymentLot?.updatedBy],
            tenantId: [paymentLot?.tenantId],
            active: [paymentLot?.active],

            lotId: [paymentLot?.lotId || null],
            boId: [paymentLot?.boId || null],
            completeDate: [paymentLot?.completeDate || null],
            completeDateCustom: [paymentLot?.completeDate ? new Date(paymentLot?.completeDate) : null],
            receiver: [paymentLot?.receiver || null],
            lotCode: [paymentLot?.lotCode || null],
            declarationCode: [paymentLot?.declarationCode || null],
            totalWeightPck: [paymentLot?.totalWeightPck || null],
            totalWeightLot: [paymentLot?.totalWeightLot || null],
            weightFee: [paymentLot?.weightFee || null],
            weightFeeUnit: [paymentLot?.weightFeeUnit || null],
            shippingMethod: [paymentLot?.shippingMethod || null],
            shippingBrand: [paymentLot?.shippingBrand || null],
            deliveryCondition: [paymentLot?.deliveryCondition || null],
            deliveryAddress: [paymentLot?.deliveryAddress || null],
            exportPort: [paymentLot?.exportPort || null],
            importPort: [paymentLot?.importPort || null],
            deliveryAddressFinal: [paymentLot?.deliveryAddressFinal || null],
            da: [paymentLot?.da || null],
            accountingCode: [paymentLot?.accountingCode || null],
            totalAmount: [paymentLot?.totalAmount || null],
            rateMoney: [paymentLot?.rateMoney || null],
            totalShippingFee: [paymentLot?.totalShippingFee || null],
            monthlyExpensesId: [paymentLot?.monthlyExpensesId || null],
        });
    }

    private initPaymentLotTax(paymentLotTax?: PaymentLotTax) {
        this.formPaymentLotTax = new FormGroupCustom(this.fb, {
            id: [paymentLotTax?.id],
            created: [paymentLotTax?.created],
            updated: [paymentLotTax?.updated],
            createdBy: [paymentLotTax?.createdBy],
            updatedBy: [paymentLotTax?.updatedBy],
            tenantId: [paymentLotTax?.tenantId],
            active: [paymentLotTax?.active],

            lotId: [paymentLotTax?.lotId || null],
            boId: [paymentLotTax?.boId || null],
            vatTax: [paymentLotTax?.vatTax || null],
            importTax: [paymentLotTax?.importTax || null],
            monthlyExpensesId: [paymentLotTax?.monthlyExpensesId || null],
        });
    }

    private initPaymentLotLocal(paymentLotLocal?: PaymentLotLocal) {
        this.formPaymentLotLocal = new FormGroupCustom(this.fb, {
            id: [paymentLotLocal?.id],
            created: [paymentLotLocal?.created],
            updated: [paymentLotLocal?.updated],
            createdBy: [paymentLotLocal?.createdBy],
            updatedBy: [paymentLotLocal?.updatedBy],
            tenantId: [paymentLotLocal?.tenantId],
            active: [paymentLotLocal?.active],

            lotId: [paymentLotLocal?.lotId || null],
            boId: [paymentLotLocal?.boId || null],
            insuranceFee: [paymentLotLocal?.insuranceFee || null],
            localFee: [paymentLotLocal?.localFee || null],
            localFeeOld: [paymentLotLocal?.localFee || null],
            description: [paymentLotLocal?.description || null],
            monthlyExpensesId: [paymentLotLocal?.monthlyExpensesId || null],
            attachmentIds: [paymentLotLocal?.attachmentIds],
            attachments: [paymentLotLocal?.attachments],
        });
    }

    private initPaymentLotImportPort(paymentLotImportPort?: PaymentLotImportPort) {
        this.formPaymentLotImportPort = new FormGroupCustom(this.fb, {
            id: [paymentLotImportPort?.id],
            created: [paymentLotImportPort?.created],
            updated: [paymentLotImportPort?.updated],
            createdBy: [paymentLotImportPort?.createdBy],
            updatedBy: [paymentLotImportPort?.updatedBy],
            tenantId: [paymentLotImportPort?.tenantId],
            active: [paymentLotImportPort?.active],

            lotId: [paymentLotImportPort?.lotId || null],
            boId: [paymentLotImportPort?.boId || null],
            infrastructureFee: [paymentLotImportPort?.infrastructureFee || null],
            storageFee: [paymentLotImportPort?.storageFee || null],
            cleanFee: [paymentLotImportPort?.cleanFee || null],
            otherFee: [paymentLotImportPort?.otherFee || null],
            vatTax: [paymentLotImportPort?.vatTax || null],
            monthlyExpensesId: [paymentLotImportPort?.monthlyExpensesId || null],
        });
    }

    private initPaymentLotDeliverPortExport(paymentLotDeliverPortExport?: PaymentLotDeliverPort) {
        this.formPaymentLotDeliverPortExport = new FormGroupCustom(this.fb, {
            id: [paymentLotDeliverPortExport?.id],
            created: [paymentLotDeliverPortExport?.created],
            updated: [paymentLotDeliverPortExport?.updated],
            createdBy: [paymentLotDeliverPortExport?.createdBy],
            updatedBy: [paymentLotDeliverPortExport?.updatedBy],
            tenantId: [paymentLotDeliverPortExport?.tenantId],
            active: [paymentLotDeliverPortExport?.active],

            lotId: [paymentLotDeliverPortExport?.lotId || null],
            boId: [paymentLotDeliverPortExport?.boId || null],
            type: [paymentLotDeliverPortExport?.type ?? 0], // Default to 0 for export
            exportLicenseFee: [paymentLotDeliverPortExport?.exportLicenseFee || null],
            coFee: [paymentLotDeliverPortExport?.coFee || null],
            toExportFee: [paymentLotDeliverPortExport?.toExportFee || null],
            customsFee: [paymentLotDeliverPortExport?.customsFee || null],
            exportFee: [paymentLotDeliverPortExport?.exportFee || null],
            vatTax: [paymentLotDeliverPortExport?.vatTax || null],
            toImportFee: [paymentLotDeliverPortExport?.toImportFee || null],
            monthlyExpensesId: [paymentLotDeliverPortExport?.monthlyExpensesId || null],
        });
    }

    private initPaymentLotDeliverPortImport(paymentLotDeliverPortImport?: PaymentLotDeliverPort) {
        this.formPaymentLotDeliverPortImport = new FormGroupCustom(this.fb, {
            id: [paymentLotDeliverPortImport?.id],
            created: [paymentLotDeliverPortImport?.created],
            updated: [paymentLotDeliverPortImport?.updated],
            createdBy: [paymentLotDeliverPortImport?.createdBy],
            updatedBy: [paymentLotDeliverPortImport?.updatedBy],
            tenantId: [paymentLotDeliverPortImport?.tenantId],
            active: [paymentLotDeliverPortImport?.active],

            lotId: [paymentLotDeliverPortImport?.lotId || null],
            boId: [paymentLotDeliverPortImport?.boId || null],
            type: [paymentLotDeliverPortImport?.type ?? 1], // Default to 1 for import
            exportLicenseFee: [paymentLotDeliverPortImport?.exportLicenseFee || null],
            coFee: [paymentLotDeliverPortImport?.coFee || null],
            toExportFee: [paymentLotDeliverPortImport?.toExportFee || null],
            customsFee: [paymentLotDeliverPortImport?.customsFee || null],
            exportFee: [paymentLotDeliverPortImport?.exportFee || null],
            vatTax: [paymentLotDeliverPortImport?.vatTax || null],
            toImportFee: [paymentLotDeliverPortImport?.toImportFee || null],
            monthlyExpensesId: [paymentLotDeliverPortImport?.monthlyExpensesId || null],
        });
    }

    private initPaymentLotDeliver(paymentLotDeliver?: PaymentLotDelivery) {
        this.formPaymentLotDelivery = new FormGroupCustom(this.fb, {
            id: [paymentLotDeliver?.id],
            created: [paymentLotDeliver?.created],
            updated: [paymentLotDeliver?.updated],
            createdBy: [paymentLotDeliver?.createdBy],
            updatedBy: [paymentLotDeliver?.updatedBy],
            tenantId: [paymentLotDeliver?.tenantId],
            active: [paymentLotDeliver?.active],

            lotId: [paymentLotDeliver?.lotId || null],
            boId: [paymentLotDeliver?.boId || null],
            deliveryFee: [paymentLotDeliver?.deliveryFee || null],
            fuelFee: [paymentLotDeliver?.fuelFee || null],
            otherFee: [paymentLotDeliver?.otherFee || null],
            monthlyExpensesId: [paymentLotDeliver?.monthlyExpensesId || null],
        });
    }

    handleUploadFile(files: File[]) {
        this.loadingService.show();
        this.paymentLotService.importFile(files).subscribe({
            next: (res: ApiResponse) => {
                const itemValue = this.formPaymentLotLocal.getRawValue() as PaymentLotLocal;

                const attachmentIds = itemValue?.attachmentIds ?? [];
                const attachments = itemValue?.attachments ?? [];
                if (res.code === 1) {
                    // Nối mảng cũ và mảng mới
                    const newAttachmentIds = [...attachmentIds, ...(res.data['attachmentIds'] || [])];
                    const newAttachments = [...attachments, ...(res.data['attachments'] || [])];
                    this.formPaymentLotLocal.patchValue({
                        attachmentIds: newAttachmentIds,
                        attachments: newAttachments,
                    });
                } else {
                    this.alertService.error('Có lỗi xảy ra khi lưu file');
                    this.formPaymentLotLocal.patchValue({ attachmentIds: attachmentIds, attachments: attachments });
                }

                this.loadingService.hide();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    savePaymentLotDeliverPort(type: 'export' | 'import') {
        switch (type) {
            case 'export':
                this.paymentLotDeliveryPortService.update(this.formPaymentLotDeliverPortExport.getRawValue()).subscribe({
                    next: () => {
                        this.isLoadingPaymentSummary = true;
                        this.debouncedRefreshPaymentSummary();
                    },
                    error: () => {},
                });
                break;
            case 'import':
                this.paymentLotDeliveryPortService.update(this.formPaymentLotDeliverPortImport.getRawValue()).subscribe({
                    next: () => {
                        this.isLoadingPaymentSummary = true;
                        this.debouncedRefreshPaymentSummary();
                    },
                    error: () => {},
                });
                break;
        }
    }
    savePaymentLotImportPort() {
        this.paymentLotImportPortService.update(this.formPaymentLotImportPort.getRawValue()).subscribe({
            next: () => {
                this.isLoadingPaymentSummary = true;
                this.debouncedRefreshPaymentSummary();
            },
            error: () => {},
        });
    }
    savePaymentLotTax() {
        this.paymentLotTaxService.update(this.formPaymentLotTax.getRawValue()).subscribe({
            next: () => {
                this.isLoadingPaymentSummary = true;
                this.debouncedRefreshPaymentSummary();
            },
            error: () => {},
        });
    }
    savePaymentLotDelivery() {
        this.paymentLotDeliveryService.update(this.formPaymentLotDelivery.getRawValue()).subscribe({
            next: () => {
                this.isLoadingPaymentSummary = true;
                this.debouncedRefreshPaymentSummary();
            },
            error: () => {},
        });
    }
    savePaymentLotLocal() {
        if (this.formPaymentLotLocal.get('localFee')?.value !== this.formPaymentLotLocal.get('localFeeOld')?.value) {
            this.paymentLotLocalService.update(this.formPaymentLotLocal.getRawValue()).subscribe({
                next: () => {
                    // this.loadData();
                    this.isLoadingPaymentSummary = true;
                    this.debouncedRefreshPaymentSummary();
                },
                error: () => {},
            });
        }
    }

    savePaymentLot() {
        if (this.formPaymentLot.invalid) return;
        const value = this.formPaymentLot.getRawValue() as PaymentLot;
        if (value.completeDateCustom) {
            value.completeDate = value.completeDateCustom.getTime();
        } else {
            value.completeDate = null;
        }
        this.paymentLotService.update(value).subscribe({
            next: () => {},
            error: () => {},
        });
    }
}
