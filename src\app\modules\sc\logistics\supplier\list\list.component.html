<app-sub-header [items]="[{ label: 'Quản lý nhà cung cấp dịch vụ Logistics' }, { label: 'Danh sách' }]" [action]="actionHeader"></app-sub-header>

<ng-template #actionHeader>
    <p-button
        routerLink="create"
        [queryParams]="{ type: tableActiveId }"
        label="Tạo mới"
        severity="primary"
        *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_logistics_edit']"
    />
    <app-popup
        header="Xuất danh sách Nhà cung cấp dịch vụ Logistics"
        severity="success"
        label="Xuất Excel"
        (onSubmit)="exportSupplierLogistics($event)"
        typePopup="download"
    ></app-popup>
</ng-template>
<div style="padding: 1rem 1rem 0 1rem">
    <app-table-multi
        [tableIds]="[tableIdForwarder, tableIdEXPRESS_DELIVERY, tableIdInsurance, tableIdOther]"
        [configMulti]="{
            LOGISTICS_FORWARDER: {
                name: 'Forwarder',
                columns: columnsForwarder,
                selectionMode: 'multiple',
                loading: stateForwarder && stateForwarder?.isFetching,
                funcDelete: deleteSelected,
                data: stateForwarder && stateForwarder?.data,
                filterTemplate: filterTemplateForwarder,
                authoritiesDelete: ['ROLE_SYSTEM_ADMIN', 'sc_logistics_delete'],
            },
            LOGISTICS_EXPRESS_DELIVERY: {
                name: 'Chuyển phát nhanh',
                columns: columnsEXPRESS_DELIVERY,
                selectionMode: 'multiple',
                funcDelete: deleteSelected,
                loading: stateEXPRESS_DELIVERY && stateEXPRESS_DELIVERY?.isFetching,
                data: stateEXPRESS_DELIVERY && stateEXPRESS_DELIVERY?.data,
                filterTemplate: filterTemplateEXPRESS_DELIVERY,
                authoritiesDelete: ['ROLE_SYSTEM_ADMIN', 'sc_logistics_delete'],
            },
            LOGISTICS_INSURANCE: {
                name: 'Bảo hiểm',
                columns: columnsInsurance,
                selectionMode: 'multiple',
                funcDelete: deleteSelected,
                loading: stateInsurance && stateInsurance?.isFetching,
                data: stateInsurance && stateInsurance?.data,
                filterTemplate: filterTemplateInsurance,
                authoritiesDelete: ['ROLE_SYSTEM_ADMIN', 'sc_logistics_delete'],
            },

            LOGISTICS_OTHER: {
                name: 'Khác',
                columns: columnsOther,
                selectionMode: 'multiple',
                funcDelete: deleteSelected,
                loading: stateOther && stateOther?.isFetching,
                data: stateOther && stateOther?.data,
                filterTemplate: filterTemplateOther,
                authoritiesDelete: ['ROLE_SYSTEM_ADMIN', 'sc_logistics_delete'],
            },
        }"
    >
        <ng-template #filterTemplateForwarder>
            <tr>
                <th></th>
                <th [appFilter]="[tableIdForwarder, 'fullName']">
                    <app-filter-table [tableId]="tableIdForwarder" field="fullName" placeholder="Tên đầy đủ"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdForwarder, 'shortName']">
                    <app-filter-table [tableId]="tableIdForwarder" field="shortName" placeholder="Tên viết tắt"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdForwarder, 'serviceProvided']">
                    <app-filter-table [tableId]="tableIdForwarder" field="serviceProvided" placeholder="Dịch vụ cung cấp"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdForwarder, 'productSupplied']">
                    <app-filter-table [tableId]="tableIdForwarder" field="productSupplied" placeholder="Hàng hóa"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdForwarder, 'address']">
                    <app-filter-table [tableId]="tableIdForwarder" field="address" placeholder="Địa chỉ"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdForwarder, 'national']">
                    <app-filter-table [tableId]="tableIdForwarder" field="national" placeholder="Quốc gia"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdForwarder, 'website']">
                    <app-filter-table [tableId]="tableIdForwarder" field="website" placeholder="Website"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdForwarder, 'contactPerson']">
                    <app-filter-table [tableId]="tableIdForwarder" field="contactPerson" placeholder="Liên hệ"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdForwarder, 'position']">
                    <app-filter-table [tableId]="tableIdForwarder" field="position" placeholder="Chức vụ"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdForwarder, 'email']">
                    <app-filter-table [tableId]="tableIdForwarder" field="email" placeholder="Email"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdForwarder, 'phone']">
                    <app-filter-table [tableId]="tableIdForwarder" field="phone" placeholder="SĐT"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdForwarder, 'tradingYear']">
                    <app-filter-table
                        [tableId]="tableIdForwarder"
                        field="tradingYear"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'label',
                            filterLocal: true,
                            options: yearRange,
                        }"
                        placeholder="Năm bắt đầu giao dịch"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableIdForwarder, 'contractStartTime']">
                    <app-filter-table
                        [tableId]="tableIdForwarder"
                        [rsql]="true"
                        field="contractStartTime"
                        type="date-range"
                        placeholder="Ngày hợp đồng gần nhất"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableIdForwarder, 'status']">
                    <app-filter-table
                        [tableId]="tableIdForwarder"
                        field="status"
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'label',
                            filterLocal: true,
                            options: STATE_LOGISTICS,
                        }"
                        placeholder="Trạng thái"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableIdForwarder, 'contractEndTime']">
                    <app-filter-table
                        [tableId]="tableIdForwarder"
                        [rsql]="true"
                        field="contractEndTime"
                        type="date-range"
                        placeholder="Thời hạn hợp đồng"
                    ></app-filter-table>
                </th>
            </tr>
        </ng-template>

        <ng-template #filterTemplateEXPRESS_DELIVERY>
            <tr>
                <th></th>

                <th [appFilter]="[tableIdEXPRESS_DELIVERY, 'fullName']">
                    <app-filter-table [tableId]="tableIdEXPRESS_DELIVERY" field="fullName" placeholder="Tên đầy đủ"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdEXPRESS_DELIVERY, 'shortName']">
                    <app-filter-table [tableId]="tableIdEXPRESS_DELIVERY" field="shortName" placeholder="Tên viết tắt"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdEXPRESS_DELIVERY, 'serviceProvided']">
                    <app-filter-table [tableId]="tableIdEXPRESS_DELIVERY" field="serviceProvided" placeholder="Dịch vụ cung cấp"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdEXPRESS_DELIVERY, 'productSupplied']">
                    <app-filter-table [tableId]="tableIdEXPRESS_DELIVERY" field="productSupplied" placeholder="Hàng hóa"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdEXPRESS_DELIVERY, 'address']">
                    <app-filter-table [tableId]="tableIdEXPRESS_DELIVERY" field="address" placeholder="Địa chỉ"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdEXPRESS_DELIVERY, 'national']">
                    <app-filter-table [tableId]="tableIdEXPRESS_DELIVERY" field="national" placeholder="Quốc gia"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdEXPRESS_DELIVERY, 'website']">
                    <app-filter-table [tableId]="tableIdEXPRESS_DELIVERY" field="website" placeholder="Website"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdEXPRESS_DELIVERY, 'contactPerson']">
                    <app-filter-table [tableId]="tableIdEXPRESS_DELIVERY" field="contactPerson" placeholder="Liên hệ"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdEXPRESS_DELIVERY, 'position']">
                    <app-filter-table [tableId]="tableIdEXPRESS_DELIVERY" field="position" placeholder="Chức vụ"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdEXPRESS_DELIVERY, 'email']">
                    <app-filter-table [tableId]="tableIdEXPRESS_DELIVERY" field="email" placeholder="Email"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdEXPRESS_DELIVERY, 'phone']">
                    <app-filter-table [tableId]="tableIdEXPRESS_DELIVERY" field="phone" placeholder="SĐT"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdEXPRESS_DELIVERY, 'tradingYear']">
                    <app-filter-table
                        [tableId]="tableIdEXPRESS_DELIVERY"
                        field="tradingYear"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'label',
                            filterLocal: true,
                            options: yearRange,
                        }"
                        placeholder="Năm bắt đầu giao dịch"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableIdEXPRESS_DELIVERY, 'contractStartTime']">
                    <app-filter-table
                        [tableId]="tableIdEXPRESS_DELIVERY"
                        [rsql]="true"
                        field="contractStartTime"
                        type="date-range"
                        placeholder="Ngày hợp đồng gần nhất"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableIdEXPRESS_DELIVERY, 'status']">
                    <app-filter-table
                        [tableId]="tableIdEXPRESS_DELIVERY"
                        field="status"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'label',
                            filterLocal: true,
                            options: STATE_LOGISTICS,
                        }"
                        placeholder="Trạng thái"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableIdEXPRESS_DELIVERY, 'contractEndTime']">
                    <app-filter-table
                        [tableId]="tableIdEXPRESS_DELIVERY"
                        [rsql]="true"
                        field="contractEndTime"
                        type="date-range"
                        placeholder="Thời hạn hợp đồng"
                    ></app-filter-table>
                </th>
            </tr>
        </ng-template>

        <ng-template #filterTemplateInsurance>
            <tr>
                <th></th>

                <th [appFilter]="[tableIdInsurance, 'fullName']">
                    <app-filter-table [tableId]="tableIdInsurance" field="fullName" placeholder="Tên đầy đủ"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdInsurance, 'shortName']">
                    <app-filter-table [tableId]="tableIdInsurance" field="shortName" placeholder="Tên viết tắt"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdInsurance, 'serviceProvided']">
                    <app-filter-table [tableId]="tableIdInsurance" field="serviceProvided" placeholder="Dịch vụ cung cấp"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdInsurance, 'productSupplied']">
                    <app-filter-table [tableId]="tableIdInsurance" field="productSupplied" placeholder="Hàng hóa"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdInsurance, 'address']">
                    <app-filter-table [tableId]="tableIdInsurance" field="address" placeholder="Địa chỉ"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdInsurance, 'national']">
                    <app-filter-table [tableId]="tableIdInsurance" field="national" placeholder="Quốc gia"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdInsurance, 'website']">
                    <app-filter-table [tableId]="tableIdInsurance" field="website" placeholder="Website"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdInsurance, 'contactPerson']">
                    <app-filter-table [tableId]="tableIdInsurance" field="contactPerson" placeholder="Liên hệ"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdInsurance, 'position']">
                    <app-filter-table [tableId]="tableIdInsurance" field="position" placeholder="Chức vụ"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdInsurance, 'email']">
                    <app-filter-table [tableId]="tableIdInsurance" field="email" placeholder="Email"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdInsurance, 'phone']">
                    <app-filter-table [tableId]="tableIdInsurance" field="phone" placeholder="SĐT"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdInsurance, 'tradingYear']">
                    <app-filter-table
                        [tableId]="tableIdInsurance"
                        field="tradingYear"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'label',
                            filterLocal: true,
                            options: yearRange,
                        }"
                        placeholder="Năm bắt đầu giao dịch"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableIdInsurance, 'contractStartTime']">
                    <app-filter-table
                        [tableId]="tableIdInsurance"
                        [rsql]="true"
                        field="contractStartTime"
                        type="date-range"
                        placeholder="Ngày hợp đồng gần nhất"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableIdInsurance, 'status']">
                    <app-filter-table
                        [tableId]="tableIdInsurance"
                        field="status"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'label',
                            filterLocal: true,
                            options: STATE_LOGISTICS,
                        }"
                        placeholder="Trạng thái"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableIdInsurance, 'contractEndTime']">
                    <app-filter-table
                        [tableId]="tableIdInsurance"
                        [rsql]="true"
                        field="contractEndTime"
                        type="date-range"
                        placeholder="Thời hạn hợp đồng"
                    ></app-filter-table>
                </th>
            </tr>
        </ng-template>
        <ng-template #filterTemplateOther>
            <tr>
                <th></th>

                <th [appFilter]="[tableIdOther, 'fullName']">
                    <app-filter-table [tableId]="tableIdOther" field="fullName" placeholder="Tên đầy đủ"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdOther, 'shortName']">
                    <app-filter-table [tableId]="tableIdOther" field="shortName" placeholder="Tên viết tắt"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdOther, 'serviceProvided']">
                    <app-filter-table [tableId]="tableIdOther" field="serviceProvided" placeholder="Dịch vụ cung cấp"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdOther, 'productSupplied']">
                    <app-filter-table [tableId]="tableIdOther" field="productSupplied" placeholder="Hàng hóa"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdOther, 'address']">
                    <app-filter-table [tableId]="tableIdOther" field="address" placeholder="Địa chỉ"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdOther, 'national']">
                    <app-filter-table [tableId]="tableIdOther" field="national" placeholder="Quốc gia"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdOther, 'website']">
                    <app-filter-table [tableId]="tableIdOther" field="website" placeholder="Website"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdOther, 'contactPerson']">
                    <app-filter-table [tableId]="tableIdOther" field="contactPerson" placeholder="Liên hệ"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdOther, 'position']">
                    <app-filter-table [tableId]="tableIdOther" field="position" placeholder="Chức vụ"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdOther, 'email']">
                    <app-filter-table [tableId]="tableIdOther" field="email" placeholder="Email"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdOther, 'phone']">
                    <app-filter-table [tableId]="tableIdOther" field="phone" placeholder="SĐT"></app-filter-table>
                </th>
                <th [appFilter]="[tableIdOther, 'tradingYear']">
                    <app-filter-table
                        [tableId]="tableIdOther"
                        field="tradingYear"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'label',
                            filterLocal: true,
                            options: yearRange,
                        }"
                        placeholder="Năm bắt đầu giao dịch"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableIdOther, 'contractStartTime']">
                    <app-filter-table
                        [tableId]="tableIdOther"
                        [rsql]="true"
                        field="contractStartTime"
                        type="date-range"
                        placeholder="Ngày hợp đồng gần nhất"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableIdOther, 'status']">
                    <app-filter-table
                        [tableId]="tableIdOther"
                        field="status"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'label',
                            filterLocal: true,
                            options: STATE_LOGISTICS,
                        }"
                        placeholder="Trạng thái"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableIdOther, 'contractEndTime']">
                    <app-filter-table
                        [tableId]="tableIdOther"
                        [rsql]="true"
                        field="contractEndTime"
                        type="date-range"
                        placeholder="Thời hạn hợp đồng"
                    ></app-filter-table>
                </th>
            </tr>
        </ng-template>
        <ng-template #templateStateLogistics let-rowData>
            <p-tag [value]="MAP_STATE_LOGISTICS[rowData.status]" [severity]="getSeverity(rowData.status)"></p-tag>
        </ng-template>
    </app-table-multi>
</div>
