import { Component, EventEmitter, forwardRef, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { InputTextModule } from 'primeng/inputtext';
import { ControlValueAccessor, NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'app-inputNumber',
    standalone: true,
    imports: [CommonModule, InputTextModule, ReactiveFormsModule],
    templateUrl: './inputnumber.component.html',
    styleUrls: ['./inputnumber.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => InputNumberComponent),
            multi: true,
        },
    ],
})
export class InputNumberComponent implements ControlValueAccessor {
    @Input() maxLength?: number = 20;
    @Input() decimalMaxLength?: number;
    @Input() min?: number;
    @Input() max?: number;
    @Input() mode: 'integer' | 'decimal' = 'integer';
    @Input() allowNegative: boolean = false;

    @Output() onBlur = new EventEmitter<number | null>();
    @Output() onInput = new EventEmitter<number | null>();

    value: number | null = null;
    displayValue: string = '';
    disabled: boolean = false;

    onChange: (value: number | null) => void = () => {};
    onTouched: () => void = () => {};

    writeValue(value: number | null): void {
        this.value = value;
        this.displayValue = this.formatDisplayValue(value);
    }

    registerOnChange(fn: (value: number | null) => void): void {
        this.onChange = fn;
    }

    registerOnTouched(fn: () => void): void {
        this.onTouched = fn;
    }

    setDisabledState(isDisabled: boolean): void {
        this.disabled = isDisabled;
    }

    onInputChange(event: Event): void {
        const input = event.target as HTMLInputElement;
        let inputValue = input.value.trim();
        const cursorPosition = input.selectionStart || 0;

        // Xóa ký tự không hợp lệ
        const validPattern = this.allowNegative
            ? this.mode === 'integer'
                ? /^[-0-9,]*$/
                : /^[-0-9,.]*$/
            : this.mode === 'integer'
              ? /^[0-9,]*$/
              : /^[0-9,.]*$/;
        if (!validPattern.test(inputValue)) {
            inputValue = inputValue.replace(
                this.allowNegative ? (this.mode === 'integer' ? /[^-0-9,]/g : /[^-0-9,.]/g) : this.mode === 'integer' ? /[^0-9,]/g : /[^0-9,.]/g,
                '',
            );
        }

        // Chỉ cho phép dấu '-' ở đầu chuỗi nếu allowNegative là true
        if (this.allowNegative && inputValue.indexOf('-') > 0) {
            inputValue = inputValue.replace(/-/g, '');
        }

        // Xử lý trường hợp chỉ có dấu '-'
        if (this.allowNegative && inputValue === '-') {
            this.value = null;
            this.displayValue = '-';
            input.value = this.displayValue;
            this.onChange(this.value);
            this.onTouched();
            this.onInput.emit(this.value);
            return;
        }

        // Xử lý dấu phân tách thập phân
        if (this.mode === 'decimal') {
            const dotCount = (inputValue.match(/\./g) || []).length;
            if (dotCount > 1) {
                const firstDotIndex = inputValue.indexOf('.');
                inputValue = inputValue.substring(0, firstDotIndex + 1) + inputValue.substring(firstDotIndex + 1).replace(/\./g, '');
            }

            // Giới hạn độ dài phần thập phân
            if (this.decimalMaxLength !== undefined && inputValue.includes('.')) {
                const [integerPart, decimalPart] = inputValue.split('.');
                if (decimalPart && decimalPart.length > this.decimalMaxLength) {
                    inputValue = `${integerPart}.${decimalPart.slice(0, this.decimalMaxLength)}`;
                }
            }
        }

        // Kiểm tra maxLength (loại bỏ dấu "," nhưng giữ dấu "." và "-")
        if (this.maxLength !== undefined) {
            const lengthWithoutCommas = inputValue.replace(/,/g, '').length;
            if (lengthWithoutCommas > this.maxLength) {
                const charsWithoutCommas = inputValue.replace(/,/g, '').slice(0, this.maxLength);
                inputValue = charsWithoutCommas;
            }
        }

        // Xử lý dấu "," phân tách hàng nghìn
        const isNegative = this.allowNegative && inputValue.startsWith('-');
        const cleanValue = inputValue.replace(/,/g, '').replace('-', '');
        let formattedValue = cleanValue;
        if (this.mode === 'decimal' && cleanValue.includes('.')) {
            const [integerPart, decimalPart] = cleanValue.split('.');
            const formattedInteger = this.formatIntegerPart(integerPart);
            formattedValue = decimalPart !== undefined ? `${formattedInteger}.${decimalPart}` : formattedInteger;
        } else {
            formattedValue = this.formatIntegerPart(cleanValue);
        }
        formattedValue = isNegative && formattedValue ? `-${formattedValue}` : formattedValue;

        // Chuyển đổi thành số
        let numericValue: number;
        if (this.mode === 'integer') {
            numericValue = cleanValue ? parseInt(cleanValue, 10) : NaN;
        } else {
            numericValue = cleanValue ? parseFloat(cleanValue) : NaN;
        }
        if (isNegative && !isNaN(numericValue)) {
            numericValue = -numericValue;
        }

        let newValue: number | null = isNaN(numericValue) ? null : numericValue;

        // Áp dụng giới hạn min/max
        if (newValue !== null) {
            if (this.mode === 'integer') {
                newValue = Math.round(newValue);
            }
            if (this.min !== undefined && newValue < this.min) {
                newValue = this.min;
                formattedValue = this.formatDisplayValue(this.min);
            }
            if (this.max !== undefined && newValue > this.max) {
                newValue = this.max;
                formattedValue = this.formatDisplayValue(this.max);
            }
        }

        // Cập nhật giá trị và hiển thị
        this.value = newValue;
        this.displayValue = formattedValue;
        input.value = this.displayValue;

        // Khôi phục vị trí con trỏ
        const newCursorPosition = this.adjustCursorPosition(inputValue, formattedValue, cursorPosition);
        input.setSelectionRange(newCursorPosition, newCursorPosition);

        this.onChange(this.value);
        this.onTouched();
        this.onInput.emit(this.value);
    }

    onKeyPress(event: KeyboardEvent): void {
        const allowedKeys =
            this.mode === 'integer'
                ? ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab']
                : ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '.', 'Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'];

        if (this.allowNegative) {
            allowedKeys.push('-');
        }

        if (!allowedKeys.includes(event.key)) {
            event.preventDefault();
        }

        // Chỉ cho phép dấu '-' ở đầu chuỗi nếu allowNegative là true
        if (this.allowNegative && event.key === '-') {
            const input = event.target as HTMLInputElement;
            const cursorPosition = input.selectionStart || 0;
            // Chỉ cho phép '-' nếu con trỏ ở đầu và chuỗi chưa có '-'
            if (cursorPosition !== 0 || input.value.includes('-')) {
                event.preventDefault();
            }
        }

        // Kiểm tra maxLength khi nhập (loại bỏ dấu "," nhưng giữ dấu "." và "-")
        if (this.maxLength !== undefined && event.key !== 'Backspace' && event.key !== 'Delete') {
            const input = event.target as HTMLInputElement;
            const currentValue = input.value;
            const lengthWithoutCommas = currentValue.replace(/,/g, '').length;
            if (lengthWithoutCommas >= this.maxLength) {
                event.preventDefault();
            }
        }

        // Giới hạn độ dài phần thập phân
        if (this.mode === 'decimal' && this.decimalMaxLength !== undefined && event.key !== 'Backspace' && event.key !== 'Delete') {
            const input = event.target as HTMLInputElement;
            const currentValue = input.value;
            if (
                currentValue.includes('.') &&
                currentValue.split('.')[1].length >= this.decimalMaxLength &&
                (input.selectionStart || 0) > currentValue.indexOf('.')
            ) {
                event.preventDefault();
            }
        }
    }

    onBlurInput(): void {
        if (this.mode === 'decimal' && this.displayValue.endsWith('.')) {
            this.displayValue = this.displayValue.slice(0, -1);
            this.value = this.displayValue.replace(/,/g, '') ? parseFloat(this.displayValue.replace(/,/g, '')) : null;
            this.onChange(this.value);
        } else if (this.allowNegative && this.displayValue === '-') {
            this.displayValue = '';
            this.value = null;
            this.onChange(this.value);
        }
        this.displayValue = this.formatDisplayValue(this.value);
        this.onTouched();
        this.onBlur.emit(this.value);
    }

    private formatDisplayValue(value: number | null): string {
        if (value === null || isNaN(value)) {
            return '';
        }

        const isNegative = this.allowNegative && value < 0;
        const absValue = Math.abs(value);

        if (this.mode === 'integer') {
            const formatted = absValue.toLocaleString('en-US', { useGrouping: true }).replace(/,/g, ',');
            return isNegative ? `-${formatted}` : formatted;
        }

        const formatter = new Intl.NumberFormat('en-US', {
            useGrouping: true,
            minimumFractionDigits: 0,
            maximumFractionDigits: this.decimalMaxLength || 20,
        });
        const formatted = formatter.format(absValue).replace(/,/g, ',').replace('.', '.');
        return isNegative ? `-${formatted}` : formatted;
    }

    private formatIntegerPart(integerPart: string): string {
        const numericValue = parseInt(integerPart.replace(/,/g, ''), 10);
        if (isNaN(numericValue)) {
            return integerPart;
        }
        return numericValue.toLocaleString('en-US', { useGrouping: true }).replace(/,/g, ',');
    }

    private adjustCursorPosition(oldValue: string, newValue: string, oldPosition: number): number {
        const oldWithoutCommas = oldValue.replace(/,/g, '');
        const newWithoutCommas = newValue.replace(/,/g, '');
        let newPosition = oldPosition;

        if (oldWithoutCommas.length === newWithoutCommas.length) {
            const commasBeforeOld = (oldValue.slice(0, oldPosition).match(/,/g) || []).length;
            const commasBeforeNew = (newValue.slice(0, newPosition).match(/,/g) || []).length;
            newPosition += commasBeforeNew - commasBeforeOld;
        } else {
            const newCommas = (newValue.match(/,/g) || []).length;
            const oldCommas = (oldValue.match(/,/g) || []).length;
            newPosition += newCommas - oldCommas;

            if (this.allowNegative) {
                if (newValue.startsWith('-') && !oldValue.startsWith('-')) {
                    newPosition += 1;
                } else if (!newValue.startsWith('-') && oldValue.startsWith('-')) {
                    newPosition -= 1;
                }
            }
        }

        return Math.min(newPosition, newValue.length);
    }
}
