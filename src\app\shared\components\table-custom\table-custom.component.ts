import { Component, Input, ContentChildren, QueryList, AfterContentInit, TemplateRef } from '@angular/core';
import { DialogModule } from 'primeng/dialog';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { PopupComponent } from 'src/app/shared/components/popup/popup.component';
import { CommonModule } from '@angular/common';
import { TableCellDirective } from 'src/app/shared/directives/table-cell.directive';
export interface TrackColumn {
    field: string;
    header: string;
    width?: string;
}

@Component({
    selector: 'app-table-custom',
    standalone: true,
    imports: [DialogModule, TableModule, ButtonModule, PopupComponent, CommonModule, TableCellDirective],
    templateUrl: './table-custom.component.html',
    styleUrls: ['./table-custom.component.scss'],
})
export class TableCustomComponent implements AfterContentInit {
    /** Các template tuỳ chỉnh được truyền từ parent */
    @ContentChildren(TableCellDirective)
    private customCells!: QueryList<TableCellDirective>;

    /** Map để look-up template theo field */
    cellTemplateMap = new Map<string, TemplateRef<any>>();

    ngAfterContentInit() {
        this.customCells.forEach((d) => {
            this.cellTemplateMap.set(d.field, d.template);
        });
    }
    /** Title of dialog */

    /** Table column definitions */
    @Input() columns: TrackColumn[] = [];

    /** Data to display */
    @Input() data: any[] = [];

    /** Pagination settings */
    @Input() paginator: boolean = true;
    @Input() rows: number = 10;
    @Input() rowsPerPageOptions: number[] = [5, 10, 20, 50];
}
