import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, OnInit, TemplateRef, ViewChild, inject } from '@angular/core';
import { RouterModule } from '@angular/router';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { ConfirmationService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { AuthService } from 'src/app/core/auth/auth.service';
import { TABLE_KEY } from 'src/app/models/constant';
import { Column, EventChangeFilter, EventPopupSubmit, GeneralEntity } from 'src/app/models/interface';
import { AlertService } from 'src/app/shared/services/alert.service';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { MaintenanceDTO, MaintenanceFilter } from '../../../models/interface/smart-qc';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { TableCommonService } from '../../../shared/table-module/table.common.service';
import { TableCommonModule } from '../../../shared/table-module/table.common.module';
import { MaintenanceService } from 'src/app/services/smart-qc/maintenance/maintenance.service';
import { MaintenanceEditComponent } from './pop-up-edit/maintenance.edit.component';
import { PopupUploadComponent } from 'src/app/shared/components/popup-upload/popup-upload.component';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { FileService } from 'src/app/shared/services/file.service';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { PopupComponent } from 'src/app/shared/components/popup/popup.component';

@Component({
    selector: 'app-maintenance',
    standalone: true,
    imports: [
        TableCommonModule,
        CommonModule,
        RouterModule,
        ButtonModule,
        SubHeaderComponent,
        MaintenanceEditComponent,
        PopupUploadComponent,
        HasAnyAuthorityDirective,
        FormCustomModule,
        ReactiveFormsModule,
        PopupComponent,
    ],
    templateUrl: './maintenance.component.html',
    providers: [TableCommonService, MaintenanceService],
})
export class MaintenanceComponent implements OnInit, AfterViewInit {
    @ViewChild('templateAction') templateAction: TemplateRef<Element>;

    constructor(
        private readonly loadingService: LoadingService,
        private readonly tableCommonService: TableCommonService,
        private readonly confirmationService: ConfirmationService,
        private readonly alertService: AlertService,
        private readonly maintenanceService: MaintenanceService,
        private readonly fileService: FileService,
    ) {}

    authService = inject(AuthService);
    state: QueryObserverBaseResult<MaintenanceDTO[]>;
    columns: Column[] = [];
    tableId: string = TABLE_KEY.MANTENANCE;
    itemsHeader = [{ label: 'Quản lý lỗi sau triển khai' }, { label: 'Danh sách ' }];
    actionHeader: TemplateRef<Element>;

    bodyFilter: Record<string, unknown> = {
        contractId: null,
    };
    // popup
    isVisible: boolean = false;
    oldId: number;
    stationId: number;
    // popup file import
    isLoading: boolean = false;
    itemsFormPopup: { [key: string]: FormControl } = {};

    contractIdFile: number;
    urlErrorCreate: string;
    ngOnInit() {
        this.tableCommonService
            .init<MaintenanceDTO>({
                tableId: this.tableId,
                queryFn: (filter, body: Partial<MaintenanceFilter>) =>
                    this.maintenanceService.getPageCustom(filter, body),
                configFilter: [
                    'contractId',
                    'areaId',
                    'stationName',
                    'stationCode',
                    'subPmId',
                    'hw',
                    'createDateStart&createDateEnd',
                    'doneDateStart&doneDateEnd',
                    'processDetail',
                    'note',
                ],
                filterUrl: true,
            })
            .subscribe((state) => {
                this.state = state;
            });

        this.itemsFormPopup = {
            contractId: new FormControl(null, Validators.required),
        };
    }
    ngAfterViewInit(): void {
        setTimeout(() => {
            this.columns = [
                {
                    header: 'Chi tiết',
                    body: this.templateAction,
                    default: true,
                    style: { 'max-width': '5rem' },
                    fixed: 'right',
                    field: 'id',
                },
                { field: 'contractName', header: 'Dự án', default: true },
                { field: 'areaName', header: 'Tỉnh/Tp', style: { 'max-width': '8rem' } },
                { field: 'stationName', header: 'Tên trạm', style: { 'max-width': '20rem' } },
                { field: 'stationCode', header: 'Mã trạm', style: { 'max-width': '20rem' }, default: true },
                {
                    field: 'subPm',
                    header: 'SubPM',
                },
                {
                    field: 'hw',
                    header: 'Chi tiết lỗi HW',
                    default: true,
                },
                {
                    field: 'createDate',
                    header: 'Ngày lỗi',
                    type: 'date',
                    format: 'dd/MM/yyyy',
                    style: { 'max-width': '8rem' },
                },
                {
                    field: 'doneDate',
                    header: 'Ngày xử lý xong',
                    type: 'date',
                    format: 'dd/MM/yyyy',
                    style: { 'max-width': '8rem' },
                },
                {
                    field: 'backTimes',
                    header: 'Số lần quay lại',
                },
                {
                    field: 'processDetail',
                    header: 'Chi tiết xử lý',
                    default: true,
                },
                {
                    field: 'note',
                    header: 'Ghi chú',
                },
            ];
        }, 0);
    }

    changeFilter(e, key) {
        this.bodyFilter[key] = e.value;
    }

    changeContractId(e: EventChangeFilter) {
        this.contractIdFile = e.value as number;
    }

    handleShowPopup(item?: MaintenanceDTO) {
        if (item) {
            this.oldId = item.id;
            this.stationId = item.stationId;
        }
        this.isVisible = true;
    }

    handleClosePopup() {
        this.isVisible = false;
        this.oldId = null;
        this.stationId = null;
    }

    handleClickDownFile() {
        if (!this.contractIdFile) {
            this.alertService.error('Vui lòng chọn dự án để nhận file mẫu');
            return;
        }
        this.isLoading = true;
        this.maintenanceService.getFileImport(this.contractIdFile).subscribe({
            next: (res: GeneralEntity) => {
                this.fileService.downLoadFileByService(res.url, '/smart-qc/api');
                this.isLoading = false;
            },
            error: () => {
                this.isLoading = false;
            },
        });
    }

    handleUploadImportCreate(data: { value: { file: File; contractId: number }; close: () => void }) {
        this.loadingService.show();

        this.maintenanceService.importCreate(data.value.file, data.value.contractId).subscribe({
            next: (res) => {
                this.loadingService.hide();
                if (res.code === 0) {
                    this.loadingService.hide();
                    this.urlErrorCreate = this.fileService.updateUrlDownload(res.message, '/smart-qc/api');
                    this.alertService.error('File không hợp lệ');
                } else {
                    this.alertService.success('Thành công');
                    this.urlErrorCreate = null;
                    this.state.refetch();
                    data.close();
                }
            },
            error: (e) => {
                this.loadingService.hide();
                this.alertService.handleError(e);
            },
        });
    }

    exportExcel(event: EventPopupSubmit<unknown>) {
        this.loadingService.show();
        this.maintenanceService.exportListExcel(this.tableCommonService.getBody(this.tableId)).subscribe({
            next: (res: GeneralEntity) => {
                this.fileService.downLoadFileByService(res.url, '/smart-qc/api');
                this.loadingService.hide();
                event.close();
            },
            error: () => {
                this.alertService.error('Có lỗi xảy ra');
                this.loadingService.hide();
            },
        });
    }

    deleteSelected = (ids: number[]) => {
        return this.maintenanceService.batchDelete(ids);
    };
}
