import { Component, ElementRef, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild, forwardRef, inject, OnDestroy } from '@angular/core';
import { AlertService } from '../../services/alert.service';
import { FileService } from '../../services/file.service';
import { Attachment } from 'src/app/models/interface';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { CommonModule } from '@angular/common';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { isArray } from 'lodash';

@Component({
    selector: 'app-button-group-file',
    templateUrl: './button-group-file.component.html',
    styleUrls: ['./button-group-file.component.scss'],
    standalone: true,
    imports: [ButtonModule, DialogModule, CommonModule],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => ButtonGroupFileComponent),
            multi: true,
        },
        AlertService,
        FileService,
    ],
})
export class ButtonGroupFileComponent implements OnInit, OnChanges, ControlValueAccessor, OnDestroy {
    @Input() types: string[] | null = null;
    @Input() urlTemplate: string = '';
    @Input() errorWrongFileMessage: string;
    @Input() service: '/auth/api' | '/smart-qc/api' | '/sc/api' = '/sc/api';
    @Input() simpleUpload: string = null;
    @Input() disabled: boolean = false;
    @Input() attachment: Attachment;
    @Input() attachments: Attachment[] = [];
    @Input() urlError: string;
    @Input() blodResponseError: Blob;
    @Input() multiple: boolean = false;
    @Output() onFileSelected = new EventEmitter<File | File[]>();
    @Output() onClickDowload = new EventEmitter<null>();
    @Output() onClearFile = new EventEmitter<number>();

    @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>; // Tham chiếu tới input file
    @ViewChild('fileInputMultiple') fileInputMultiple: ElementRef<HTMLInputElement>; // Tham chiếu tới input file

    acceptTypes: string;
    alertService = inject(AlertService);
    fileService = inject(FileService);
    fileSelected: File[] = [];
    idAttachments: number[] = [];

    // ControlValueAccessor callbacks
    private onChangeCallback: (value: number | number[] | null) => void = () => {};
    private onTouchedCallback: () => void = () => {};

    ngOnInit() {
        this.acceptTypes = this.getAcceptTypes(this.types);
        if (this.urlTemplate) {
            this.urlTemplate = this.fileService.updateUrlTemplate(this.urlTemplate, this.service);
        }
        if (this.attachment) {
            this.attachment.url = this.fileService.updateUrlDownload(this.attachment.url, this.service);
        }
        if (this.attachments && isArray(this.attachments)) {
            this.attachments.forEach((att) => {
                att.url = this.fileService.updateUrlDownload(att.url, this.service);
            });
        }
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['urlError'] && changes['urlError'].currentValue) {
            this.urlError = this.fileService.updateUrlDownload(this.urlError, this.service);
        } else if (changes['urlError'] && !changes['urlError'].currentValue && changes['urlError'].previousValue) {
            this.fileSelected = [];

            if (this.fileInput) {
                this.fileInput.nativeElement.value = ''; // Đặt lại giá trị input file
            }

            if (this.fileInputMultiple) {
                this.fileInputMultiple.nativeElement.value = ''; // Đặt lại giá trị input file
            }
        }
        if (changes['attachment'] && changes['attachment'].currentValue) {
            this.attachment.url = this.fileService.updateUrlDownload(this.attachment.url, this.service);
            if (this.fileInput) {
                this.fileInput.nativeElement.value = ''; // Đặt lại giá trị input file
            }
            this.fileSelected = [];
        }
        if (changes['attachments'] && changes['attachments'].currentValue && isArray(changes['attachments'].currentValue)) {
            this.attachments.filter((att) => {
                att.url = this.fileService.updateUrlDownload(att.url, this.service);
            });
            if (this.fileInputMultiple) {
                this.fileInputMultiple.nativeElement.value = ''; // Đặt lại giá trị input file
            }
            this.fileSelected = [];
        }
        if (changes['urlTemplate'] && changes['urlTemplate'].currentValue) {
            this.urlTemplate = this.fileService.updateUrlTemplate(this.urlTemplate, this.service);
        }
    }

    ngOnDestroy() {
        if (this.fileInput) {
            this.fileInput.nativeElement.value = ''; // Đặt lại giá trị input file
        }

        if (this.fileInputMultiple) {
            this.fileInputMultiple.nativeElement.value = ''; // Đặt lại giá trị input file
        }
        this.fileSelected = [];
        this.attachment = null;
        this.attachments = [];
        this.urlError = null;
    }

    // ControlValueAccessor implementation
    writeValue(value: number | number[] | null): void {
        if (value) {
            this.idAttachments = Array.isArray(value) ? value : [value];
            this.fileSelected = [];
        } else {
            this.idAttachments = [];
        }

        if (this.idAttachments.length === 0) {
            this.attachment = null;
            this.attachments = [];
        }

        // Xóa giá trị của input file
        if (this.fileInput) {
            this.fileInput.nativeElement.value = ''; // Đặt lại giá trị input file
        }
        if (this.fileInputMultiple) {
            this.fileInputMultiple.nativeElement.value = ''; // Đặt lại giá trị input file
        }
    }

    registerOnChange(fn: (value: number | number[] | null) => void): void {
        this.onChangeCallback = fn;
    }

    registerOnTouched(fn: () => void): void {
        this.onTouchedCallback = fn;
    }

    setDisabledState(isDisabled: boolean): void {
        this.disabled = isDisabled;
    }

    onSelectFile(event: Event) {
        if (this.disabled) return;
        const input = event.target as HTMLInputElement;
        const files: FileList = input.files;

        if (files && files.length > 0) {
            const selectedFiles: File[] = Array.from(files);

            if (this.types && !selectedFiles.every((file) => this.isValidType(file))) {
                this.alertService.error('Lỗi', this.errorWrongFileMessage || `Định dạng file không hợp lệ. Chỉ chấp nhận: ${this.types.join(', ')}`);
                this.removeFile(-1, input);
                return;
            }

            this.fileSelected = selectedFiles;
            const valueToEmit = this.multiple ? this.fileSelected : this.fileSelected[0];
            this.onFileSelected.emit(valueToEmit);
            // this.onChangeCallback(valueToEmit); // Update FormControl
            this.onTouchedCallback(); // Mark as touched
        }
    }

    downloadTemplate(): void {
        if (this.disabled) return;
        this.onClickDowload.emit();
    }

    downloadFileError() {
        this.fileService.downloadBlob(this.blodResponseError, this.fileSelected.length > 0 ? this.fileSelected[0].name : 'error_file');
    }

    private getAcceptTypes(types: string[] | null): string {
        if (!types || types.length === 0) {
            // Mặc định chỉ chấp nhận tài liệu: PDF, Word, Excel
            return 'image/jpeg,image/png,image/gif,image/bmp,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel.sheet.macroEnabled.12';
        }
        return types.join(',');
    }

    private isValidType(file: File): boolean {
        const mimeTypes = this.getMimeTypes(this.types);
        return mimeTypes.length === 0 || mimeTypes.includes(file.type);
    }

    private getMimeTypes(types: string[] | null): string[] {
        if (!types || types.length === 0) {
            // Mặc định chỉ chấp nhận tài liệu
            return [
                'image/jpeg',
                'image/png',
                'image/gif',
                'image/bmp',
                'application/pdf', // PDF
                'application/msword', // Word (DOC)
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // Word (DOCX)
                'application/vnd.ms-excel', // Excel (XLS)
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // Excel (XLSX)
            ];
        }
        const mimeTypes: string[] = [];
        types.forEach((type) => {
            switch (type.toLowerCase()) {
                case 'image':
                    mimeTypes.push('image/jpeg', 'image/png', 'image/gif', 'image/bmp');
                    break;
                case 'pdf':
                    mimeTypes.push('application/pdf');
                    break;
                case 'excel':
                    mimeTypes.push('application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
                    break;
                case 'word':
                    mimeTypes.push('application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
                    break;
                default:
                    mimeTypes.push(type);
                    break;
            }
        });
        return mimeTypes;
    }

    downloadAttachment(attachment: Attachment) {
        if (!attachment) return;
        this.fileService.downLoadFileByService(attachment.url, this.service);
    }

    removeFile(index: number, fileInput?: HTMLInputElement) {
        // Reset file input nếu có
        if (fileInput) {
            fileInput.value = '';
        }

        if (index === -1) {
            // Xóa toàn bộ danh sách nếu index = -1
            this.fileSelected = [];
            this.idAttachments = [];
            this.attachments = [];
            this.attachment = null;
        } else {
            // Xóa file tại index cụ thể
            if (this.fileSelected.length > 0 && index < this.fileSelected.length) {
                this.fileSelected.splice(index, 1);
            }
            if (this.idAttachments.length > 0 && index < this.idAttachments.length) {
                this.idAttachments.splice(index, 1);
            }
            if (this.attachments.length > 0 && index < this.attachments.length) {
                this.attachments.splice(index, 1);
            }
        }

        // Reset lỗi
        this.urlError = null;
        this.blodResponseError = null;

        // Emit sự kiện xóa
        this.onClearFile.emit(index);

        // Cập nhật giá trị FormControl
        const newValue = this.multiple ? this.idAttachments : null;
        this.onChangeCallback(newValue);
        this.onTouchedCallback();
    }
}
