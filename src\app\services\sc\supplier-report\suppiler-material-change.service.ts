import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { SupplierMaterialChange } from '../../../models/interface/sc';

@Injectable()
export class SupplierMaterialChangeService extends BaseService<SupplierMaterialChange> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/supplier-material-change');
    }

    getListLastPrice(supplierId: number) {
        return this.http.get<SupplierMaterialChange[]>(`/sc/api/supplier-material-change/last-price/${supplierId}`);
    }
}
