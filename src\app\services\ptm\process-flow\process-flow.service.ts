import { HttpClient, HttpErrorResponse, HttpParams, HttpResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { catchError, Observable, throwError } from 'rxjs';
import { ProcessFlow, ProcessFlowDetail } from 'src/app/models/interface/ptm/process-flow';

@Injectable({ providedIn: 'root' })
export class ProcessFlowService {
    path = '/pr/api/production-instruction';
    #http = inject(HttpClient);

    constructor(private http: HttpClient) {}

    getProcessFlow(id: string): Observable<ProcessFlow[]> {
        const url = `${this.path}/${id}/process-flow`;
        return this.#http.get<ProcessFlow[]>(url);
    }

    create(formData: any, instructionId: string): Observable<any> {
        const url = `${this.path}/${instructionId}/process-flow`;
        return this.http.post(url, formData).pipe(catchError(this.handleError));
    }

    updateProcessFlow(processFlow: ProcessFlow) {
        const url = `${this.path}/process-flow/${processFlow.id}`;
        return this.#http.put<ProcessFlow>(url, processFlow).pipe(catchError(this.handleError));
    }

    deleteProcessFlow(id: number) {
        const url = `${this.path}/process-flow/${id}`;
        return this.#http.delete(url).pipe(catchError(this.handleError));
    }

    exportProcessFlow(id: string) {
        const url = `${this.path}/${id}/process-flow/export`;
        return this.#http.get(url, { responseType: 'text' }).pipe(catchError(this.handleError));
    }

    previewProcessFlow(formData: any, id: string) {
        const url = `/pr/api/approval/production-instruction/${id}`;
        return this.#http.post(url, formData).pipe(catchError(this.handleError));
    }

    comfirmProcessFlow(formData: any, id: string) {
        const url = `/pr/api/approval/production-instruction/${id}/confirm`;
        return this.#http.post(url, formData).pipe(catchError(this.handleError));
    }

    /** Xử lý chung lỗi HTTP */
    private handleError(error: HttpErrorResponse) {
        // có thể tuỳ chỉnh thông báo, logging, …
        console.error('API error', error);
        return throwError(() => error);
    }
}
