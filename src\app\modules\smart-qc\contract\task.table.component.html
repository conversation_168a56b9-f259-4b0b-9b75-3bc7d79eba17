<style>
    .qc-action-disabled {
        cursor: not-allowed !important;
        opacity: 0.5;
    }
</style>
<ng-container>
    <div class="border-bottom-2 border-gray-300">
        <div class="tw-font-bold tw-mb-1">Phân công trạm - thành viên dự án:</div>
        <div class="tw-flex tw-justify-between tw-mb-3">
            <span>
                <p-button
                    *ngIf="isPMOrAdmin"
                    (click)="deleteMany()"
                    label="Xóa"
                    [text]="true"
                    [raised]="true"
                    severity="danger"
                    size="small"
                    class="mr-2"
                    [disabled]="canOpenEditForm || selectedTasks.length === 0"
                ></p-button>
                <p-button
                    *ngIf="contractId && isPMOrAdmin"
                    type="button"
                    (click)="
                        visiblemFileForm = true;
                        visbleFileDelete = true;
                        visibleImportForm = false;
                        fileUpload = null;
                        errorFileUrl = null
                    "
                    size="small"
                    label="Import Xóa"
                    [text]="true"
                    [raised]="true"
                    severity="danger"
                    icon="pi pi-folder-open"
                    class="p-button-success mr-2"
                    [disabled]="canOpenEditForm"
                ></p-button>
            </span>

            <span class="tw-flex tw-justify-end">
                <p-button
                    *ngIf="isPMOrAdmin"
                    (click)="openEditTask({}, true); ngFormTask.submitted = false"
                    type="button"
                    label="Thêm trạm"
                    icon="pi pi-plus"
                    class="mr-2"
                    severity="info"
                    size="small"
                    [disabled]="canOpenEditForm"
                ></p-button>
                <p-button
                    type="button"
                    (click)="
                        visiblemFileForm = true;
                        visibleImportForm = true;
                        visbleFileDelete = false;
                        fileUpload = null;
                        errorFileUrl = null
                    "
                    label="Import"
                    icon="pi pi-folder-open"
                    size="small"
                    severity="info"
                    class="p-button-success mr-2"
                    [disabled]="canOpenEditForm"
                ></p-button>
                <p-button
                    type="button"
                    (click)="
                        visiblemFileForm = true;
                        visibleImportForm = false;
                        visbleFileDelete = false;
                        errorFileUrl = null
                    "
                    label="Export"
                    size="small"
                    icon="pi pi-file-export"
                    class="mr-2"
                    severity="success"
                    [disabled]="canOpenEditForm"
                ></p-button>
            </span>
        </div>
    </div>

    <p-table
        #dt1
        [value]="displayTasks"
        [paginator]="true"
        [rows]="5"
        [rowHover]="true"
        styleClass="p-datatable-striped"
        responsiveLayout="scroll"
        (selectionChange)="onSelectionChange($event)"
        [loading]="tableLoading"
        [lazy]="this.actionId"
        [totalRecords]="totalTask"
        (onPage)="logEvent($event)"
        [(first)]="pageable.first"
    >
        <ng-template pTemplate="header">
            <tr>
                <th *ngIf="isPMOrAdmin"></th>
                <th>STT</th>
                <th style="min-width: 9rem">
                    <div class="tw-flex">Tỉnh/Tp</div>
                </th>

                <th style="min-width: 9rem">
                    <div class="tw-flex">Quận huyện</div>
                </th>

                <th style="min-width: 10rem">
                    <div class="tw-flex">Tên trạm</div>
                </th>

                <th style="min-width: 10rem">
                    <div class="tw-flex">Mã trạm</div>
                </th>

                <th style="min-width: 16rem" *ngIf="isPMOrAdmin">
                    <div class="tw-flex">SubPM</div>
                </th>

                <th style="min-width: 16rem" *ngIf="isSubPM">
                    <div class="tw-flex">Đội thi công</div>
                </th>

                <th style="min-width: 10rem">
                    <div class="tw-flex tw-justify-center">Thao tác</div>
                </th>
            </tr>
            <tr>
                <th *ngIf="isPMOrAdmin">
                    <p-tableHeaderCheckbox />
                </th>
                <th></th>
                <th>
                    <ng-select
                        appendTo="body"
                        dropdownPosition="bottom"
                        [virtualScroll]="true"
                        [items]="comboboxArea"
                        bindLabel="name"
                        bindValue="id"
                        [(ngModel)]="searchObject.areaId"
                        (change)="loadTask()"
                        #areaCbb
                    ></ng-select>
                </th>
                <th>
                    <ng-select
                        appendTo="body"
                        dropdownPosition="bottom"
                        [virtualScroll]="true"
                        [items]="comboboxDistrict"
                        bindLabel="name"
                        bindValue="id"
                        [(ngModel)]="searchObject.districtId"
                        (ngModelChange)="loadTask()"
                        #districtCbb
                    ></ng-select>
                </th>
                <th>
                    <input
                        pInputText
                        style="max-width: 10rem"
                        [(ngModel)]="searchObject.stationName"
                        (ngModelChange)="loadTask()"
                        #stationNameInput
                    />
                </th>
                <th>
                    <input
                        pInputText
                        style="max-width: 8rem"
                        [(ngModel)]="searchObject.stationCode"
                        (ngModelChange)="loadTask()"
                        #stationCodeInput
                    />
                </th>
                <th *ngIf="isPMOrAdmin">
                    <ng-select
                        appendTo="body"
                        dropdownPosition="bottom"
                        [virtualScroll]="true"
                        [items]="userList"
                        bindLabel="displayName"
                        bindValue="id"
                        [(ngModel)]="searchObject.subPMId"
                        (ngModelChange)="loadTask($event)"
                    ></ng-select>
                </th>
                <th *ngIf="isSubPM">
                    <ng-select
                        appendTo="body"
                        dropdownPosition="bottom"
                        [virtualScroll]="true"
                        [items]="userList"
                        bindLabel="displayName"
                        bindValue="id"
                        [(ngModel)]="searchObject.employeeId"
                        (ngModelChange)="loadTask()"
                    ></ng-select>
                </th>
                <th></th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-task let-rowIndex="rowIndex">
            <tr>
                <td *ngIf="isPMOrAdmin">
                    <p-tableCheckbox [value]="task"></p-tableCheckbox>
                </td>
                <td>
                    {{ rowIndex + 1 }}
                </td>
                <td style="max-width: 200px" [title]="task.station?.areaName">
                    {{ task.station?.areaName }}
                </td>
                <td style="max-width: 200px" [title]="task.station?.districtName">
                    {{ task.station?.districtName }}
                </td>
                <td style="max-width: 200px" [title]="task.station?.name">
                    {{ task.station?.name }}
                </td>
                <td style="max-width: 200px" [title]="task.station?.code">
                    {{ task.station?.code }}
                </td>
                <td *ngIf="isPMOrAdmin" style="max-width: 300px">
                    {{ task.subPm }}
                </td>
                <td *ngIf="isSubPM" style="max-width: 300px">
                    {{ task.employee }}
                </td>
                <td style="max-width: 150px">
                    <div class="tw-flex tw-justify-center">
                        <i
                            class="pi pi-file-edit tw-text-2xl tw-text-green-400 tw-p-1 tw-cursor-pointer"
                            title="Sửa"
                            (click)="openEditTask(task, false)"
                        ></i>
                        <i
                            *ngIf="isPMOrAdmin"
                            class="pi pi-trash tw-text-2xl tw-text-red-500 tw-p-1 tw-cursor-pointer"
                            title="Xóa"
                            (click)="deleteTask(task, rowIndex)"
                        ></i>
                    </div>
                </td>
            </tr>
        </ng-template>
    </p-table>

    <form [formGroup]="taskForm" #ngFormTask="ngForm" (ngSubmit)="saveTask()">
        <p-dialog
            [header]="isNewTask ? 'Thêm trạm' : 'Sửa trạm'"
            [(visible)]="visibleTaskForm"
            [modal]="true"
            [breakpoints]="{ '1199px': '50vw', '575px': '30vw' }"
            [style]="{ width: '40vw' }"
            [draggable]="true"
            [resizable]="true"
        >
            <div class="tw-grid tw-gap-4 lg:tw-grid-cols-2 sm:tw-grid-cols-1 tw-p-2">
                <input formControlName="stationId" class="tw-hidden" />
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <div>
                        <label class="tw-font-bold">
                            Tên trạm
                            <span class="tw-text-red-600">*</span>
                            :
                        </label>
                        <input
                            formControlName="name"
                            style="width: 100%"
                            pInputText
                            type="text"
                            class="flex-auto tw-border-2 tw-border-gray-300"
                        />
                        <div
                            *ngIf="
                                taskForm.get('name').errors && (taskForm.get('name').touched || ngFormTask.submitted)
                            "
                            class="text-red-600"
                        >
                            <div *ngIf="taskForm.get('name').errors['required']">Tên trạm là trường bắt buộc</div>
                            <div *ngIf="taskForm.get('name').errors['maxlength']">
                                Tên trạm không vượt quá 255 ký tự
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <div>
                        <label class="tw-font-bold">
                            Mã trạm
                            <span class="tw-text-red-600">*</span>
                            :
                        </label>
                        <input
                            formControlName="code"
                            style="width: 100%"
                            pInputText
                            type="text"
                            class="flex-auto tw-border-2 tw-border-gray-300"
                            (change)="searchStation($event)"
                        />
                        <div
                            *ngIf="
                                taskForm.get('code').errors && (taskForm.get('code').touched || ngFormTask.submitted)
                            "
                            class="text-red-600"
                        >
                            <div *ngIf="taskForm.get('code').errors['required']">Mã trạm là trường bắt buộc</div>
                            <div *ngIf="taskForm.get('code').errors['maxlength']">Mã trạm không vượt quá 255 ký tự</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="tw-grid tw-gap-4 lg:tw-grid-cols-2 sm:tw-grid-cols-1 tw-p-2">
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <div>
                        <label class="tw-font-bold">
                            Tỉnh/ thành phố
                            <span class="tw-text-red-600">*</span>
                            :
                        </label>
                        <ng-select
                            formControlName="areaId"
                            [(items)]="areaList"
                            bindLabel="name"
                            bindValue="id"
                            placeholder="Chọn tỉnh/ thành phố"
                            (change)="filterDistrictByAreaForm($event)"
                        ></ng-select>
                        <div
                            *ngIf="
                                taskForm.get('areaId').errors &&
                                (taskForm.get('areaId').touched || ngFormTask.submitted)
                            "
                            class="text-red-600 tw-pt-1"
                        >
                            <div *ngIf="taskForm.get('areaId').errors['required']">
                                Tỉnh/ thành phố là trường bắt buộc
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <div>
                        <label class="tw-font-bold">
                            Quận/ huyện
                            <span class="tw-text-red-600">*</span>
                            :
                        </label>
                        <ng-select
                            formControlName="districtId"
                            [items]="districtListFilterForm"
                            bindLabel="name"
                            bindValue="id"
                            placeholder="Chọn quận huyện"
                        ></ng-select>
                        <div
                            *ngIf="
                                taskForm.get('districtId').errors &&
                                (taskForm.get('districtId').touched || ngFormTask.submitted)
                            "
                            class="text-red-600 tw-pt-1"
                        >
                            <div *ngIf="taskForm.get('districtId').errors['required']">
                                Quận/ huyện là trường bắt buộc
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="tw-grid tw-gap-4 lg:tw-grid-cols-2 sm:tw-grid-cols-1 tw-p-2">
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <div>
                        <label class="tw-font-bold">
                            Latitude
                            <span class="tw-text-red-600">*</span>
                            :
                        </label>
                        <input
                            formControlName="latitude"
                            style="width: 100%"
                            pInputText
                            type="text"
                            class="flex-auto tw-border-2 tw-border-gray-300"
                        />
                        <div
                            *ngIf="
                                taskForm.get('latitude').errors &&
                                (taskForm.get('latitude').touched || ngFormTask.submitted)
                            "
                            class="text-red-600 tw-pt-1"
                        >
                            <div *ngIf="taskForm.get('latitude').errors['required']">Latitude là trường bắt buộc</div>
                            <div *ngIf="taskForm.get('latitude').errors['maxlength']">
                                Latitude không vượt quá 255 ký tự
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <div>
                        <label class="tw-font-bold">
                            Longitude
                            <span class="tw-text-red-600">*</span>
                            :
                        </label>
                        <input
                            formControlName="longitude"
                            style="width: 100%"
                            pInputText
                            type="text"
                            class="flex-auto tw-border-2 tw-border-gray-300"
                        />
                        <div
                            *ngIf="
                                taskForm.get('longitude').errors &&
                                (taskForm.get('longitude').touched || ngFormTask.submitted)
                            "
                            class="text-red-600 tw-pt-1"
                        >
                            <div *ngIf="taskForm.get('longitude').errors['required']">Longitude là trường bắt buộc</div>
                            <div *ngIf="taskForm.get('longitude').errors['maxlength']">
                                Latitude không vượt quá 255 ký tự
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="tw-grid tw-gap-4 tw-grid-cols-1 tw-p-2">
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <label class="tw-font-bold">Cấu hình :</label>
                    <input
                        formControlName="config"
                        style="width: 100%"
                        pInputText
                        type="text"
                        class="flex-auto tw-border-2 tw-border-gray-300"
                    />
                </div>
            </div>

            <div class="tw-grid tw-gap-4 tw-grid-cols-1 tw-p-2">
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <label class="tw-font-bold">Địa chỉ :</label>
                    <input
                        formControlName="address"
                        style="width: 100%"
                        pInputText
                        type="text"
                        class="flex-auto tw-border-2 tw-border-gray-300"
                    />
                </div>
            </div>

            <div class="tw-grid tw-gap-4 lg:tw-grid-cols-2 sm:tw-grid-cols-1 tw-p-2">
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <div>
                        <label class="tw-font-bold">Quản lý trực tiếp :</label>
                        <input
                            formControlName="manage"
                            style="width: 100%"
                            pInputText
                            type="text"
                            class="flex-auto tw-border-2 tw-border-gray-300"
                        />
                    </div>
                </div>
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <div>
                        <label class="tw-font-bold">Số điện thoại :</label>
                        <input
                            formControlName="phone"
                            style="width: 100%"
                            pInputText
                            type="text"
                            class="flex-auto tw-border-2 tw-border-gray-300"
                        />
                        <div
                            *ngIf="
                                taskForm.get('phone').errors && (taskForm.get('phone').touched || ngFormTask.submitted)
                            "
                            class="text-red-600 tw-pt-1"
                        >
                            <div *ngIf="taskForm.get('phone').errors['pattern']">
                                Số điện thoại không đúng định dạng
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="tw-grid tw-gap-4 lg:tw-grid-cols-2 sm:tw-grid-cols-1 tw-p-2">
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <div *ngIf="isPMOrAdmin">
                        <label class="tw-font-bold">
                            Thành viên
                            <span class="tw-text-red-600">*</span>
                            :
                        </label>
                        <ng-select
                            formControlName="subPMId"
                            [items]="userList"
                            bindLabel="fullName"
                            bindValue="id"
                            placeholder="Chọn thành viên"
                            (change)="setAssigneeCallBack($event)"
                        ></ng-select>
                        <div
                            *ngIf="
                                taskForm.get('subPMId').errors &&
                                (taskForm.get('subPMId').touched || ngFormTask.submitted)
                            "
                            class="text-red-600 tw-pt-1"
                        >
                            <div *ngIf="taskForm.get('subPMId').errors['required']">Thành viên là trường bắt buộc</div>
                        </div>
                    </div>
                    <div *ngIf="!isPMOrAdmin">
                        <label class="tw-font-bold">
                            Thành viên
                            <span class="tw-text-red-600">*</span>
                            :
                        </label>
                        <ng-select
                            formControlName="employeeId"
                            [items]="userList"
                            bindLabel="fullName"
                            bindValue="id"
                            placeholder="Chọn thành viên đội"
                            (change)="setAssigneeCallBack($event)"
                        ></ng-select>
                        <div
                            *ngIf="
                                taskForm.get('employeeId').errors &&
                                (taskForm.get('employeeId').touched || ngFormTask.submitted)
                            "
                            class="text-red-600 tw-pt-1"
                        >
                            <div *ngIf="taskForm.get('employeeId').errors['required']">
                                Thành viên là trường bắt buộc
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <div>
                        <label class="tw-font-bold">
                            Tài khoản
                            <span class="tw-text-red-600">*</span>
                            :
                        </label>
                        <input
                            formControlName="email"
                            style="width: 100%"
                            pInputText
                            type="text"
                            class="flex-auto tw-border-2 tw-border-gray-300"
                        />
                        <div
                            *ngIf="
                                taskForm.get('email').errors && (taskForm.get('email').touched || ngFormTask.submitted)
                            "
                            class="text-red-600 tw-pt-1"
                        >
                            <div *ngIf="taskForm.get('email').errors['required']">Tài khoản là trường bắt buộc</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="tw-grid tw-gap-4 lg:tw-grid-cols-2 sm:tw-grid-cols-1 tw-p-2">
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <div>
                        <label class="tw-font-bold">Ngày bắt đầu :</label>
                        <p-calendar
                            appendTo="body"
                            [keepInvalid]="true"
                            dateFormat="dd/mm/yy"
                            formControlName="startTime"
                            [style]="{ width: '100%' }"
                            [showIcon]="true"
                            inputId="icondisplay"
                        ></p-calendar>
                        <div *ngIf="taskForm.errors?.timespan" class="tw-text-red-600 tw-pt-1">
                            Ngày bắt đầu không được lớn hơn ngày kết thúc
                        </div>
                        <div *ngIf="taskForm.get('startTime')?.errors?.dateType" class="tw-text-red-600 tw-pt-1">
                            Ngày phải có định dạng dd/mm/yyyy
                        </div>
                    </div>
                </div>
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <div>
                        <label class="tw-font-bold">Ngày kết thúc :</label>
                        <p-calendar
                            appendTo="body"
                            [keepInvalid]="true"
                            dateFormat="dd/mm/yy"
                            formControlName="endTime"
                            [style]="{ width: '100%' }"
                            [showIcon]="true"
                            inputId="icondisplay"
                        ></p-calendar>
                        <div *ngIf="taskForm.errors?.timespan" class="tw-text-red-600 tw-pt-1">
                            Ngày bắt đầu không được lớn hơn ngày kết thúc
                        </div>
                        <div *ngIf="taskForm.get('endTime')?.errors?.dateType" class="tw-text-red-600 tw-pt-1">
                            Ngày phải có định dạng dd/mm/yyyy
                        </div>
                    </div>
                </div>
            </div>

            <div class="tw-grid tw-gap-4 lg:tw-grid-cols-2 sm:tw-grid-cols-1 tw-p-2">
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <label class="tw-font-bold">Ngày bắt đầu thực tế :</label>
                    <p-calendar
                        dateFormat="dd/mm/yy"
                        [keepInvalid]="true"
                        formControlName="realStartTime"
                        [style]="{ width: '100%' }"
                        [showIcon]="true"
                        inputId="icondisplay"
                    ></p-calendar>
                </div>
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <label class="tw-font-bold">Ngày kết thúc thực tế :</label>
                    <p-calendar
                        dateFormat="dd/mm/yy"
                        [keepInvalid]="true"
                        formControlName="realEndTime"
                        [style]="{ width: '100%' }"
                        [showIcon]="true"
                        inputId="icondisplay"
                    ></p-calendar>
                </div>
            </div>
            <ng-template pTemplate="footer">
                <div class="flex justify-content-end gap-2">
                    <p-button label="Lưu" severity="success" type="submit" />
                    <p-button label="Đóng" severity="secondary" (click)="visibleTaskForm = false" />
                </div>
            </ng-template>
        </p-dialog>
    </form>

    <p-dialog
        [header]="visibleImportForm && !visbleFileDelete ? 'Import trạm' : 'Import xóa trạm'"
        [(visible)]="visiblemFileForm"
        [modal]="true"
        [breakpoints]="{ '1199px': '50vw', '575px': '30vw' }"
        [style]="{ width: '40vw' }"
        (onHide)="visibleImportForm = false"
        #dialogFile
    >
        <ng-template pTemplate="header">
            <div class="tw-w-full">
                <h4>{{ visibleImportForm || visbleFileDelete ? dialogFile.header : 'Xuất dữ liệu trạm' }}</h4>
                <hr />
            </div>
        </ng-template>
        <div *ngIf="visibleImportForm || visbleFileDelete">
            <span class="qc-upload-bttn">
                <div style="display: flex">
                    <span
                        class="material-icons input-upload-icon"
                        style="color: #3490dc; margin: auto; font-size: x-large"
                    >
                        backup
                    </span>
                    <span style="margin-left: 9px; margin-top: 4px">Chọn file</span>
                </div>
                <input type="file" accept=".xls, .xlsx, xlsm" class="qc-upload-input" (input)="onSelectFile($event)" />
            </span>
            <span style="padding-left: 3px">{{ fileUpload ? fileUpload.name : '' }}</span>
            <div *ngIf="!errorFileUrl" class="tw-mt-2">
                Vui lòng tải file mẫu
                <a class="tw-text-green-500 tw-cursor-pointer" (click)="downLoadTemplateImport()">tại đây</a>
            </div>
            <div *ngIf="errorFileUrl" class="tw-mt-2">
                Tải xuống file lỗi <a download [href]="errorFileUrl" class="text-red-500">tại đây</a>
            </div>
        </div>

        <a
            href="javascript:void(0)"
            (click)="downLoadTemplate()"
            *ngIf="!visibleImportForm && !visbleFileDelete"
            class="tw-text-green-500"
            >Xuất danh sách trạm</a
        >

        <ng-template pTemplate="footer">
            <div>
                <p-button *ngIf="visibleImportForm || visbleFileDelete" severity="primary" (click)="onUpload(fileInput)"
                    >Xác nhận</p-button
                >
                <p-button
                    label="Hủy"
                    [text]="true"
                    [raised]="true"
                    severity="secondary"
                    (click)="visiblemFileForm = false; visibleImportForm = false; errorFileUrl = null"
                ></p-button>
            </div>
        </ng-template>
    </p-dialog>
</ng-container>
