import { Component, inject, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { ProductInfoComponent } from 'src/app/modules/pms/components/product-info/product-info.component';
import { ButtonModule } from 'primeng/button';
import { DropdownModule } from 'primeng/dropdown';
import { FormBuilder, FormGroup, FormsModule, Validators } from '@angular/forms';
import { PopupComponent } from 'src/app/shared/components/popup/popup.component';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { TabViewComponent } from 'src/app/shared/components/tab-view/tab-view.component';
import { DesignProfileComponent } from 'src/app/modules/pms/product-file/edit/components/design-profile/design-profile.component';
import { ProductionProfileComponent } from 'src/app/modules/pms/product-file/edit/components/production-profile/production-profile.component';
import { QualityProfileComponent } from 'src/app/modules/pms/product-file/edit/components/quality-profile/quality-profile.component';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { WizardComponent } from 'src/app/shared/components/wizard/wizard.component';
import { InputTextModule } from 'primeng/inputtext';
import { RouterLink } from '@angular/router';
import { StepsModule } from 'primeng/steps';
import { ITEMS_STEP, LIFECYCLE_STAGE_DOC_MAP, LIFECYCLE_STAGE_PRODUCT_MAP, STATUS_MAP } from 'src/app/models/constant/pms';
import { PanelModule } from 'primeng/panel';
import { ApprovalRequest, DropdownOption, ProductDetail, ProductRecordVersion, ProductSoftware, TabItem, VersionRecord } from 'src/app/models/interface/pms';
import { BehaviorSubject, catchError, concatMap, finalize, from, map, Observable, of, Subscription, switchMap, tap, toArray } from 'rxjs';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { ProductFileService } from 'src/app/services/pms/product-file/product-file.service';
import { ActivatedRoute, Router } from '@angular/router';
import { ConfirmationService } from 'primeng/api';
import { ProductSoftwareService } from 'src/app/services/pms/production-software/product-software.service';
import { TableModule } from 'primeng/table';
import { EventBusService } from 'src/app/services/eventBus.service';
import { ParamsTable } from 'src/app/shared/table-module/table.common.service';
import { HttpResponse } from '@angular/common/http';
import sampleData from 'src/assets/sample-data/sample-data-hssp.json';
import { AlertService } from 'src/app/shared/services/alert.service';
import { DialogModule } from 'primeng/dialog';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { ProductFileStateService } from '../../../../services/pms/product-file/product-file-state.service';
import { environment } from '../../../../../environments/environment';

import { ComboboxNonRSQLComponent } from 'src/app/shared/components/combobox-nonRSQL/combobox-nonRSQL.component';
import { DateUtils } from 'src/app/utils/date-utils';
import { FileUploadManagerService } from 'src/app/services/upload/file-upload-manager.service';

@Component({
    selector: 'app-product-file.edit',
    standalone: true,
    imports: [
        SubHeaderComponent,
        ProductInfoComponent,
        ButtonModule,
        DropdownModule,
        FormsModule,
        PopupComponent,
        FormCustomModule,
        HasAnyAuthorityDirective,
        CommonModule,
        InputTextModule,
        RouterLink,
        StepsModule,
        TabViewComponent,
        DesignProfileComponent,
        ProductionProfileComponent,
        QualityProfileComponent,
        WizardComponent,
        PanelModule,
        TableCommonModule,
        DialogModule,
        InputTextareaModule,
        TableModule,
        ComboboxNonRSQLComponent,
    ],
    templateUrl: './product-file.edit.component.html',
    styleUrls: ['./product-file.edit.component.scss'],
    providers: [ProductFileService],
})
export class ProductFileEditComponent implements OnInit, OnDestroy {
    @ViewChild('ExportFilePopup') exportFilePopup!: PopupComponent;
    //inject deps
    productFileStateService = inject(ProductFileStateService);
    private fb = inject(FormBuilder);
    private designPayload: any = null;
    private productionPayload: any = null;
    private qualityPayload: any = null;
    public STORAGE_BASE_URL = environment.STORAGE_BASE_URL;
    //state
    isSaving: boolean = false;
    /** Cờ xác định đã gọi submit chưa (để guard 1 lần duy nhất) */
    private hasSubmitted = false;
    filterForm!: FormGroup;
    formApprovalPopup: FormGroup;
    formExportFilePopup: FormGroup;
    private productVersionOptions = new BehaviorSubject<DropdownOption[]>([]);
    public productVersionOptions$: Observable<DropdownOption[]> = this.productVersionOptions.asObservable();
    private stageOptions = new BehaviorSubject<DropdownOption[]>([]);
    public stageOptions$: Observable<DropdownOption[]> = this.stageOptions.asObservable();
    productSoftwareOptions: ProductSoftware[] = [];
    activeIndex: number = 0;
    versionOptions: { label: string; value: string | number }[] = [];
    itemsStep = ITEMS_STEP;
    currentProduct?: ProductDetail;
    version!: ProductRecordVersion;
    mode!: 'create' | 'view' | 'edit';
    selectedProduct: any;
    selectedPartNumber: any;
    selectedVersion: any;
    resultUrl: string | null = null;
    dataVersionClone: any[];
    visibleApprovalPopup: boolean = false;
    records: VersionRecord[] = [];
    isNotClone: boolean = false;
    showStageFilter = true;
    shouldUseVersionRef: boolean = false;
    visibleHistoryVersion: boolean = false;
    isSearching = false;
    isButtonVisible: boolean = false;
    isUploading = false;

    bomMetadata: {
        timeCheck?: number;
        url?: string;
    } = {};
    updatedAtRdbom: string | number = '';
    private uploadSub!: Subscription;
    versionRef: number;
    itemsTab: TabItem[] = [];
    private designSub!: Subscription;
    private productionSub!: Subscription;
    private qualitySub!: Subscription;
    selectedLifecycleStage = {
        label: 'Prototype',
        value: 1,
    };

    optionsLifecycleStage = Object.entries(LIFECYCLE_STAGE_DOC_MAP).map(([key, label]) => ({
        value: Number(key),
        label: label,
    }));
    itemsHeader = [{ label: 'Quản lý hồ sơ sản phẩm' }, { label: 'Danh sách hồ sơ sản phẩm', url: '/pms/product-file' }, { label: 'Tạo mới' }];

    productSoftwareService = inject(ProductSoftwareService);
    productFileService = inject(ProductFileService);

    constructor(
        private route: ActivatedRoute,
        private bus: EventBusService,
        private router: Router,
        private alertService: AlertService,
        private confirmationService: ConfirmationService,
        private fileUploadManagerService: FileUploadManagerService,
    ) {}

    ngOnDestroy() {
        this.designSub.unsubscribe();
        this.productionSub.unsubscribe();
        this.qualitySub.unsubscribe();
        this.uploadSub.unsubscribe();
    }

    ngOnInit() {
        this.uploadSub = this.fileUploadManagerService.isUploading$.subscribe((flag) => (this.isUploading = flag));
        this.designSub = this.bus.on<any>('RESPONSE_PAYLOAD_DESIGN').subscribe(({ payload, timeCheck, url }) => {
            this.bomMetadata = {
                timeCheck: timeCheck,
                url: url,
            };
            this.designPayload = payload;
            this.trySubmitAll();
        });
        this.productionSub = this.bus.on<any>('RESPONSE_PAYLOAD_PRODUCTION').subscribe((payload) => {
            this.productionPayload = payload;
            this.trySubmitAll();
        });
        this.qualitySub = this.bus.on<any>('RESPONSE_PAYLOAD_QUALITY').subscribe((payload) => {
            this.qualityPayload = payload;
            this.trySubmitAll();
        });
        const params: ParamsTable = {
            native: '',
            pageable: '&page=0&size=100', // tuỳ bạn set page/size
        };
        this.productSoftwareService
            .getPage(params)
            .pipe(
                // lấy body (mảng ProductSoftware[]), hoặc [] nếu null
                map((res: HttpResponse<ProductSoftware[]>) => res.body || []),
                // map từng item thành DropdownOption
                map((list) =>
                    list.map((item) => ({
                        label: item.name,
                        value: item.id,
                        rootId: item.rootId,
                    })),
                ),
            )
            .subscribe((options) => {
                this.productSoftwareOptions = options as ProductSoftware[];
            });
        this.route.url.subscribe((segments) => {
            // segments là 1 mảng UrlSegment,
            this.mode = segments[0].path as 'create' | 'edit' | 'view';
        });
        this.bus.on<void>('refreshTabs').subscribe(() => {
            this.refreshTabs();
        });
        const docObs$ = of(null);
        const navState = window.history.state || {};

        if (navState && navState.currentProduct) {
            this.currentProduct = navState.currentProduct;
        }
        if (navState.version && this.mode === 'edit') {
            this.version = navState.version;
            this.activeIndex = ITEMS_STEP.findIndex((item) => item.name === this.version.statusName);

            const productVersions = this.currentProduct.productVersions;
            const matched = productVersions.find((item) => item.id === this.version.id);
            if (matched) {
                const stage = matched.lifecycleStage;
                this.selectedLifecycleStage = {
                    label: LIFECYCLE_STAGE_DOC_MAP[stage],
                    value: stage,
                };
            } else {
                this.selectedLifecycleStage = {
                    label: 'Prototype',
                    value: 1,
                };
            }
        } else if (navState.version && this.mode === 'create') {
            //    Trường hợp nhân bản từ danh sách hồ sơ
            this.isNotClone = true;
            const payload = {
                selectedProduct: navState?.currentProduct,
                selectedVersion: navState?.version?.id,
                selectedStage: navState?.version?.lifecycleStage,
            };

            this.onSearch(payload, navState.option);
        } else if (navState.version && this.mode === 'view') {
            // const id = +this.route.snapshot.paramMap.get('versionId')!;

            this.version = navState.version as ProductRecordVersion;
            this.activeIndex = ITEMS_STEP.findIndex((item) => item.name === navState.version.statusName);
        }

        if (!navState.version && this.mode !== 'create') {
            // Trường hợp khi mở hồ sơ từ tab khác trạng thái edit
            const productId = +this.route.snapshot.paramMap.get('productId')!;
            const id = +this.route.snapshot.paramMap.get('versionId')!;
            this.version = { id: id } as ProductRecordVersion;
            this.productFileService.getProductVersionById(this.version.id).subscribe({
                next: (res) => {
                    this.version = { ...this.version, ...res };
                },
            });
            this.productFileService.getDetailProduct(productId).subscribe({
                next: (res) => {
                    this.currentProduct = {
                        id: res.id,
                        lineId: res.lineId || 0,
                        lineName: res.lineName ? `${res.lineName}` : '',
                        name: res.name,
                        description: res.description ?? '',
                        vnptManPn: res.vnptManPn,
                        hardwareVersion: res.hardwareVersion ?? '',
                        firmwareVersion: res.firmwareVersion ?? '',
                        tradeName: res.tradeName,
                        tradeCode: res.tradeCode,
                        modelName: res.modelName ?? '',
                        generation: res.generation ?? '',
                        imageUrl: res.imageUrl ?? '',
                        imageName: res.imageName ?? '',
                        stage: LIFECYCLE_STAGE_PRODUCT_MAP[res.lifecycleStage] || '',
                        productVersions: res.productVersions,
                        softwareResourceDtos: res.softwareResourceDtos,
                    };

                    if (res && res.productVersions?.length > 0) {
                        this.version = res.productVersions.filter((item) => {
                            if (item.id === id) {
                                return item;
                            }
                        })[0];
                        if (this.version) {
                            const statusCode = this.version.status;
                            const statusLabel = STATUS_MAP[statusCode];
                            const idx = ITEMS_STEP.findIndex((item) => item.name === statusLabel);
                            this.activeIndex = idx !== -1 ? idx : 0;
                            const stage = this.version.lifecycleStage;
                            this.selectedLifecycleStage = {
                                label: LIFECYCLE_STAGE_DOC_MAP[stage],
                                value: stage,
                            };
                        } else {
                            this.selectedLifecycleStage = {
                                label: 'Prototype',
                                value: 1,
                            };
                        }
                    }
                },
                error: () => {},
            });
        }

        const lastLabel = this.mode === 'create' ? 'Tạo mới' : this.mode === 'edit' ? 'Chỉnh sửa' : 'Chi tiết';

        this.itemsHeader = [{ label: 'Quản lý hồ sơ sản phẩm' }, { label: 'Danh sách hồ sơ sản phẩm', url: '/pms/product-file' }, { label: lastLabel }];
        docObs$
            .pipe(
                switchMap(() => {
                    if (this.mode === 'create' && this.currentProduct) {
                        return of(sampleData as Record<string, any>);
                    }

                    if ((this.mode === 'edit' || this.mode === 'view') && this.version) {
                        return this.productFileService.getProductDoc({
                            versionId: this.version.id,
                            types: 7, // get all tab
                        });
                    }

                    // fallback: không gọi API
                    return of(null);
                }),
                switchMap((docRes) => {
                    if (docRes && docRes['1']?.[1]?.[0]?.filePath) {
                        return this.getRDBomById(docRes['1'][1][0].filePath).pipe(map((updated) => ({ docRes, updated })));
                    } else {
                        return of({ docRes, updated: null });
                    }
                }),
            )
            .subscribe(({ docRes, updated }) => {
                if (docRes) {
                    this.updatedAtRdbom = updated;
                    this.itemsTab = [
                        {
                            header: 'Hồ sơ thiết kế',
                            type: 1,
                            component: DesignProfileComponent,
                            inputs: {
                                data: docRes['1'],
                                updatedAt: this.updatedAtRdbom,
                                version: this.version,
                                currentProduct: this.currentProduct,
                                mode: this.mode,
                            },
                        },
                        {
                            header: 'Hồ sơ sản xuất',
                            type: 2,
                            component: ProductionProfileComponent,
                            inputs: {
                                data: [],
                                version: this.version,
                                currentProduct: this.currentProduct,
                                productSoftwareOptions: this.productSoftwareOptions,
                                mode: this.mode,
                            },
                        },
                        {
                            header: 'Hồ sơ chất lượng',
                            type: 4,
                            component: QualityProfileComponent,
                            inputs: {
                                data: docRes['4'],
                                version: this.version,
                                currentProduct: this.currentProduct,
                                mode: this.mode,
                            },
                        },
                    ];
                    // 1) Lấy mảng gốc
                    const originalItems: any[] = docRes['2'][2];

                    // 2) Gắn thêm __originalIndex để sau này tái lắp đúng vị trí
                    const itemsWithIndex = originalItems.map((item, index) => ({
                        ...item,
                        __originalIndex: index,
                    }));

                    // 3) Chỉ giữ những phần tử thực sự có filePath (khác null/undefined/khoảng trắng)
                    const toBeProcessed = itemsWithIndex.filter((item) => item.filePath != null && String(item.filePath).trim() !== '');

                    // 4) Từ mảng đã lọc, chạy Observable tuần tự để gọi API

                    from(toBeProcessed)
                        .pipe(
                            // concatMap để mỗi item sẽ chờ xong cả API1 + API2 mới tới item tiếp theo
                            concatMap((item) =>
                                this.productSoftwareService.getSoftwareVersionById(+item.filePath).pipe(
                                    tap((res1) => {
                                        item.softwareVersionName = res1.versionName;
                                        item.softwareName = res1.softwareName;
                                        item.softwareId = res1.rootId;
                                        item.fileUrlSoftware = res1.path;
                                        item.fileUrlUserguide = res1.userGuide;
                                        item.instructionName = res1.instructionName;
                                    }),
                                    // switchMap((res1) =>
                                    //     this.productSoftwareService.getSoftwareResource(res1.rootId).pipe(
                                    //         tap((res2) => {
                                    //             item.softwareName = res2.softwareName;
                                    //             item.softwareVersionName = res2.versionName;
                                    //         }),
                                    //     ),
                                    // ),
                                    // Cuối cùng emit lại chính item đã enrich
                                    map(() => item),
                                ),
                            ),
                            toArray(), // gom lại thành mảng khi tất cả item đã chạy xong
                        )
                        .subscribe({
                            next: (updatedItemsArray) => {
                                // 5) Khởi tạo lại mảng kết quả dựa trên mảng gốc
                                const result: any[] = [...originalItems];

                                // Đè lần lượt từng item đã enrich vào đúng index ban đầu
                                updatedItemsArray.forEach((itm) => {
                                    result[itm.__originalIndex] = itm;
                                    delete itm.__originalIndex; // nếu bạn không cần giữ trường này sau khi lắp xong
                                });

                                // 6) Gán mảng mới vào docRes và cập nhật itemsTab
                                docRes['2'][2] = result;
                                const idx = this.itemsTab.findIndex((t) => t.type === 2);
                                if (idx > -1) {
                                    this.itemsTab[idx].inputs['data'] = docRes['2'];
                                }
                            },
                            error: (err) => {
                                console.error('Có lỗi trong quá trình gọi API:', err);
                            },
                        });
                }
            });
        this.filterForm = this.fb.group({
            selectedProduct: [{ value: null, disabled: this.mode === 'view' }, Validators.required],
            selectedStage: [{ value: null, disabled: true }, Validators.required],
            selectedVersion: [{ value: null, disabled: true }, Validators.required],
            partNumber: [{ value: null, disabled: true }],
        });

        this.formApprovalPopup = this.fb.group({
            note: [''],
            noteOfSendApproval: [''],
            fromUser: [''],
            created: [''],
        });

        this.formExportFilePopup = this.fb.group({
            selectedVersion: [null],
        });
        this.filterForm.get('selectedProduct')!.valueChanges.subscribe((line) => {
            this.showStageFilter = false;
            setTimeout(() => (this.showStageFilter = true), 0);
            const selectedStageValue = this.filterForm.get('selectedStage')!;
            selectedStageValue.reset(null, { emitEvent: false });
            this.filterForm.get('selectedVersion')!.reset();
            this.stageOptions.next([]);
            this.productVersionOptions.next([]);

            if (line) {
                const uniqueStages = Array.from(new Set(line?.productVersions?.map((v) => v.lifecycleStage))).map((stage: number) => ({
                    value: stage,
                    label: LIFECYCLE_STAGE_DOC_MAP[stage],
                }));

                this.stageOptions.next(uniqueStages);
                selectedStageValue.enable({ emitEvent: false });
                this.filterForm.get('partNumber')!.patchValue(line?.vnptManPn || null, { emitEvent: false });
            } else {
                this.filterForm.get('selectedVersion')!.reset();
                this.filterForm.get('partNumber')!.patchValue(null, { emitEvent: false });
                this.stageOptions.next([]);
                selectedStageValue.reset();
                selectedStageValue.disable({ emitEvent: false });
            }
        });
        this.filterForm.get('selectedStage')!.valueChanges.subscribe((stage) => {
            this.showStageFilter = false;
            setTimeout(() => (this.showStageFilter = true), 0);
            const nameCtrl = this.filterForm.get('selectedVersion')!;
            nameCtrl.reset(null, { emitEvent: false });
            const valueSelectedProduct = this.filterForm.get('selectedProduct')!.value;
            if (stage) {
                const nextOptions = valueSelectedProduct?.productVersions.filter((v) => v.lifecycleStage === stage);
                const mapNextOptions: DropdownOption[] = nextOptions.map((item) => {
                    return {
                        label: item.versionName,
                        value: item.id,
                    };
                });

                this.productVersionOptions.next(mapNextOptions);
                nameCtrl.enable({ emitEvent: false });
            } else {
                this.productVersionOptions.next([]);
                nameCtrl.reset();
                nameCtrl.disable({ emitEvent: false });
            }
        });
    }

    getRDBomById(id: number): Observable<string> {
        return this.productFileService.fetchRdBomById(id).pipe(
            map((res) => {
                return res?.updatedAt || '';
            }),
            catchError((error) => {
                return of('');
            }),
        );
    }

    get configSelect() {
        // Chỉ gọi khi currentProduct đã khác null
        return {
            url: '/pr/api/product',
            usePost: false,
            rsql: false,
            page: 0,
            size: 100,
            body: {
                unpaged: false,
                modelId: 0,
                customerId: 0,
                lineId: this.currentProduct!.lineId,
            },
            fieldLabel: 'name',
            dataKey: 'id',
        };
    }

    onTabChange(type: number) {}

    onSearch(formValue: any, type: number): void {
        if (this.isSearching) {
            return;
        }
        this.isSearching = true;
        const state = window.history.state;
        //   TH nhân bản bên ngoài danh sách
        if (state && state?.version) {
            this.shouldUseVersionRef = state?.version && this.mode === 'create';
            this.versionRef = this.shouldUseVersionRef ? state.version.id : undefined;
        }
        //TH nhân bản bên trông màn thêm mới có cùng VNPT
        else {
            if (formValue.selectedProduct.id === this.currentProduct.id) {
                this.versionRef = formValue.selectedVersion;
            } else {
                this.versionRef = undefined;
            }
        }

        const versionId = formValue.selectedVersion;
        // 1 hồ sơ thiết kê , 2 hồ sơ sản xuất, 4 hồ sơ chất lượng , 7 cả ba
        const types = type;

        this.productFileService
            .filterByVersion({ versionId, types })
            .pipe(
                finalize(() => {
                    this.isSearching = false;
                }),
            )
            .subscribe((docRes) => {
                this.dataVersionClone = docRes;
                if (this.dataVersionClone && (types === 7 || types === 2 || types === 3 || types === 6)) {
                    // 1) Lấy mảng gốc
                    const originalItems: any[] = this.dataVersionClone['2'][2];

                    // 2) Gắn thêm __originalIndex để sau này tái lắp đúng vị trí
                    const itemsWithIndex = originalItems.map((item, index) => ({
                        ...item,
                        __originalIndex: index,
                    }));

                    // 3) Chỉ giữ những phần tử thực sự có filePath (khác null/undefined/khoảng trắng)
                    const toBeProcessed = itemsWithIndex.filter((item) => item.filePath != null && String(item.filePath).trim() !== '');

                    // 4) Từ mảng đã lọc, chạy Observable tuần tự để gọi API

                    from(toBeProcessed)
                        .pipe(
                            // concatMap để mỗi item sẽ chờ xong cả API1 + API2 mới tới item tiếp theo
                            concatMap((item) =>
                                this.productSoftwareService.getSoftwareVersionById(+item.filePath).pipe(
                                    tap((res1) => {
                                        item.softwareVersionName = res1.versionName;
                                        item.softwareName = res1.softwareName;
                                        item.softwareId = res1.rootId;
                                        item.fileUrlSoftware = res1.path;
                                        item.fileUrlUserguide = res1.userGuide;
                                        item.instructionName = res1.instructionName;
                                    }),
                                    // switchMap((res1) =>
                                    //     this.productSoftwareService.getSoftwareResource(res1.rootId).pipe(
                                    //         tap((res2) => {
                                    //             item.softwareName = res2.name;
                                    //         }),
                                    //     ),
                                    // ),
                                    // Cuối cùng emit lại chính item đã enrich
                                    map(() => item),
                                ),
                            ),
                            toArray(), // gom lại thành mảng khi tất cả item đã chạy xong
                        )
                        .subscribe({
                            next: (updatedItemsArray) => {
                                // 5) Khởi tạo lại mảng kết quả dựa trên mảng gốc
                                const result: any[] = [...originalItems];

                                // Đè lần lượt từng item đã enrich vào đúng index ban đầu
                                updatedItemsArray.forEach((itm) => {
                                    result[itm.__originalIndex] = itm;
                                    delete itm.__originalIndex; // nếu bạn không cần giữ trường này sau khi lắp xong
                                });

                                // 6) Gán mảng mới vào docRes và cập nhật itemsTab
                                this.dataVersionClone['2'][2] = result;

                                this.itemsTab.forEach((tab) => {
                                    const newData = this.dataVersionClone[tab.type] ?? null;
                                    tab.inputs = { ...tab.inputs, data: newData };
                                });
                            },
                            error: (err) => {
                                console.error('Có lỗi trong quá trình gọi API:', err);
                            },
                        });
                } else {
                    this.itemsTab.forEach((section) => {
                        const key = String(section.type);
                        if (this.dataVersionClone[key]) {
                            section.inputs['data'] = this.dataVersionClone[key];
                        }
                    });
                }
            });
    }

    private refreshTabs() {
        this.productFileService
            .getProductDoc({ versionId: this.version.id, types: 7 })

            .subscribe((docRes) => {
                if (docRes) {
                    this.itemsTab = [
                        {
                            header: 'Hồ sơ thiết kế',
                            type: 1,
                            component: DesignProfileComponent,
                            inputs: {
                                data: docRes['1'],
                                version: this.version,
                                currentProduct: this.currentProduct,
                                mode: this.mode,
                            },
                        },
                        {
                            header: 'Hồ sơ sản xuất',
                            type: 2,
                            component: ProductionProfileComponent,
                            inputs: {
                                data: [],
                                version: this.version,
                                currentProduct: this.currentProduct,
                                productSoftwareOptions: this.productSoftwareOptions,
                                mode: this.mode,
                            },
                        },
                        {
                            header: 'Hồ sơ chất lượng',
                            type: 4,
                            component: QualityProfileComponent,
                            inputs: {
                                data: docRes['4'],
                                version: this.version,
                                currentProduct: this.currentProduct,
                                mode: this.mode,
                            },
                        },
                    ];
                    // 1) Lấy mảng gốc
                    const originalItems: any[] = docRes['2'][2];

                    // 2) Gắn thêm __originalIndex để sau này tái lắp đúng vị trí
                    const itemsWithIndex = originalItems.map((item, index) => ({
                        ...item,
                        __originalIndex: index,
                    }));

                    // 3) Chỉ giữ những phần tử thực sự có filePath (khác null/undefined/khoảng trắng)
                    const toBeProcessed = itemsWithIndex.filter((item) => item.filePath != null && String(item.filePath).trim() !== '');

                    // 4) Từ mảng đã lọc, chạy Observable tuần tự để gọi API

                    from(toBeProcessed)
                        .pipe(
                            // concatMap để mỗi item sẽ chờ xong cả API1 + API2 mới tới item tiếp theo
                            concatMap((item) =>
                                this.productSoftwareService.getSoftwareVersionById(+item.filePath).pipe(
                                    tap((res1) => {
                                        item.softwareVersionName = res1.versionName;
                                        item.softwareName = res1.softwareName;
                                        item.softwareId = res1.rootId;
                                        item.fileUrlSoftware = res1.path;
                                        item.fileUrlUserguide = res1.userGuide;
                                        item.instructionName = res1.instructionName;
                                    }),
                                    // switchMap((res1) =>
                                    //     this.productSoftwareService.getSoftwareResource(res1.rootId).pipe(
                                    //         tap((res2) => {
                                    //             item.softwareName = res2.name;
                                    //         }),
                                    //     ),
                                    // ),
                                    // Cuối cùng emit lại chính item đã enrich
                                    map(() => item),
                                ),
                            ),
                            toArray(), // gom lại thành mảng khi tất cả item đã chạy xong
                        )
                        .subscribe({
                            next: (updatedItemsArray) => {
                                // 5) Khởi tạo lại mảng kết quả dựa trên mảng gốc
                                const result: any[] = [...originalItems];

                                // Đè lần lượt từng item đã enrich vào đúng index ban đầu
                                updatedItemsArray.forEach((itm) => {
                                    result[itm.__originalIndex] = itm;
                                    delete itm.__originalIndex; // nếu bạn không cần giữ trường này sau khi lắp xong
                                });

                                // 6) Gán mảng mới vào docRes và cập nhật itemsTab
                                docRes['2'][2] = result;
                                const idx = this.itemsTab.findIndex((t) => t.type === 2);
                                if (idx > -1) {
                                    this.itemsTab[idx].inputs['data'] = docRes['2'];
                                }
                            },
                            error: (err) => {
                                console.error('Có lỗi trong quá trình gọi API:', err);
                            },
                        });
                }
            });
    }

    saveAllTabs(): void {
        // Reset payload cũ
        this.designPayload = null;
        this.productionPayload = null;
        this.qualityPayload = null;
        this.hasSubmitted = false;

        this.bus.emit('REQUEST_PAYLOAD_ALL');
        // Bây giờ chờ các component con lắng nghe và emit về RESPONSE_PAYLOAD_…
    }

    /** Khi đã nhận đủ cả 3 payload, gom lại và gọi API duy nhất */
    private trySubmitAll(): void {
        // Nếu đã submit lần trước rồi, không làm gì nữa
        if (this.hasSubmitted) {
            return;
        }

        // Nếu vẫn còn payload nào chưa về (null), thì chưa đủ, dừng lại
        if (this.designPayload === null || this.productionPayload === null || this.qualityPayload === null) {
            return;
        }

        // Khi đủ cả ba, gán cờ để không submit thêm lần nào nữa

        this.hasSubmitted = true;

        // Gom ba phần payload lại
        this.isSaving = true;
        const combinedPayload = [...this.designPayload, ...this.productionPayload, ...this.qualityPayload];
        this.isSaving = false;

        this.productFileService
            .createUpdateProductDocument(
                this.currentProduct.id,
                combinedPayload,
                this.selectedLifecycleStage.value,
                this.versionRef,
                this.bomMetadata.timeCheck,
                this.bomMetadata.url,
            )
            .pipe(finalize(() => (this.isSaving = false)))
            .subscribe({
                next: (versionId) => {
                    this.alertService.success('Thành công', 'Lưu hồ sơ thành công');
                    this.router.navigate(['/pms/product-file'], {});
                    // Kiểm tra xem URL đã chứa segment 'edit' chưa
                    // const urlSegments = this.route.snapshot.url.map((s) => s.path);
                    // const alreadyEdit = urlSegments[0] === 'edit';

                    // if (!alreadyEdit) {
                    //     this.router.navigate(['/pms/product-file/edit', this.currentProduct.id, versionId], {
                    //         state: {
                    //             currentProduct: this.currentProduct,
                    //             version: this.version,
                    //             mode: 'edit',
                    //         },
                    //         replaceUrl: true, // tránh tạo thêm history entry
                    //     });
                    // } else {
                    //     this.refreshTabs();
                    // }
                },
            });
    }
    handleClose() {
        this.router.navigate(['/pms/product-file'], {
            replaceUrl: true, // tránh tạo thêm history entry
        });
    }
    handleCancel() {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn Hủy',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.router.navigate(['/pms/product-file'], {
                    replaceUrl: true, // tránh tạo thêm history entry
                });
            },
        });
    }
    handleDeleteVersion(v: ProductRecordVersion): void {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.productFileService.deleteVersion(this.version.id).subscribe({
                    next: () => {
                        this.alertService.success('Thành công', 'Xóa thành công');
                        this.router.navigate(['/pms/product-file'], {
                            replaceUrl: true, // tránh tạo thêm history entry
                        });
                    },
                    error: (err) => {
                        this.alertService.error('Lỗi', 'Xóa thất bại');
                        console.error('Delete error:', err);
                    },
                });
            },
        });
    }
    onOpenApproval() {
        this.formApprovalPopup.reset();
        this.productFileService.getInfoApprovalVersion(this.version.id).subscribe({
            next: (res) => {
                this.formApprovalPopup.patchValue({
                    noteOfSendApproval: res.note,
                    fromUser: res.fromUser,
                    created: res.created,
                });
            },
            error: (err) => {
                console.error('Lỗi ', err);
            },
        });

        this.visibleApprovalPopup = true;
    }
    submitFormApproval(action: string): void {
        let type: number;
        if (action === 'CONFIRM') {
            type = 3;
        } else {
            type = 4;
        }
        const note = this.formApprovalPopup.get('note')!.value;

        const req: ApprovalRequest = {
            productVersionId: this.version.id,
            type: type,
            note: note,
        };

        this.productFileService.confirmApprovalRequest(req).subscribe({
            next: (resp) => {
                if (action === 'CONFIRM') {
                    this.alertService.success('Thành công', 'Phê duyệt thành công');
                } else {
                    this.alertService.success('Thành công', 'Từ chối thành công');
                }

                this.formApprovalPopup.reset();

                const currentUrl = this.router.url;
                this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
                    this.router.navigateByUrl(currentUrl, { replaceUrl: true });
                });
                this.visibleApprovalPopup = false;
            },
            error: (err) => {
                this.formApprovalPopup.reset();
                this.visibleApprovalPopup = false;

                console.error('Lỗi khi gọi API phê duyệt:', err);
            },
        });
    }
    close(type?: string) {
        if (type === 'visibleHistoryVersion') {
            this.visibleHistoryVersion = false;
        } else if (type === 'visibleApprovalPopup') {
            this.formApprovalPopup.reset();
            this.visibleApprovalPopup = false;
        }
    }

    onViewHistory(event: Event): void {
        event.preventDefault(); // bỏ hành động điều hướng mặc định của <a>
        this.onOpenHistoryVersion();
    }
    onOpenHistoryVersion() {
        this.visibleHistoryVersion = true;
        this.loadHistoryVersion();
    }
    loadHistoryVersion() {
        if (!this.currentProduct?.id) return;

        this.productFileService.getVersionHistory(this.currentProduct.id).subscribe({
            next: (res) => {
                this.records = this.transformApiData(res);
            },
            error: (err) => {
                console.error('Lỗi khi tải lịch sử version:', err);
            },
        });
    }
    onOpenExportExcel(version: ProductRecordVersion) {
        this.versionOptions = this.records
            .filter((item) => item.version && item.version !== '-')
            .map((item) => {
                return {
                    label: item.version,
                    value: item.id,
                };
            });
        if (this.versionOptions.length > 0) {
            this.formExportFilePopup.get('selectedVersion').setValue(this.versionOptions[0].value);
        }
        this.exportFilePopup.openDialog(version);
    }
    private transformApiData(apiData: any[]): VersionRecord[] {
        return apiData.map((item) => {
            // Xử lý nội dung cập nhật
            let updateContent = '';

            // Nếu có changeFieldDetails, lấy thông tin từ các field thay đổi
            if (item.changeFieldDetails && item.changeFieldDetails.length > 0) {
                updateContent = item.changeFieldDetails
                    .map((change: any) => {
                        // Hiển thị thẳng description và newValue không qua mapping
                        return `${change.description}: ${change.newValue}`;
                    })
                    .join('; ');
            }

            return {
                id: item.id,
                date: this.formatDate(item.created || item.updated), // Sử dụng cả created và updated
                version: item.numberVersion || item.version || '-', // Thêm fallback cho version
                author: item.createdBy || '-',
                updateContent: updateContent,
                cloneVersion: item.cloneNumberVersion || item.cloneVersion || '-', // Thêm fallback cho clone version
            };
        });
    }
    private formatDate(timestamp: number): string {
        if (!timestamp) return '-';
        const date = new Date(timestamp);
        return date.toISOString().split('T')[0]; // Format YYYY-MM-DD
    }
    submitFileTransfer(version: ProductRecordVersion) {
        const selectedVersionId = this.formExportFilePopup.get('selectedVersion').value;

        // Lấy thông tin version đã chọn từ versionOptions
        const selectedVersion = this.versionOptions.find((v) => v.value === selectedVersionId);

        this.productFileService
            .exportProductVersionHistory({
                productId: this.currentProduct.id, // ID sản phẩm hiện tại
                historyId: selectedVersionId, // ID phiên bản đã chọn
            })
            .subscribe({
                next: (url) => this.downloadFile(url, selectedVersion.label),
                error: (err) => {
                    console.error('Lỗi khi tải xuống file:', err);
                },
            });
    }
    private downloadFile(url, versionName: string) {
        // const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = this.STORAGE_BASE_URL + url;
        a.download = `product_version_${versionName}_history.xlsx`; // Tên file khi tải về

        a.click();
        a.remove();
    }
    handleExport() {
        const v = this.version;
        this.productFileService
            .exportFileProdVer(v.id)
            .pipe(
                // ngay khi có relPath, tiếp tục request blob
                switchMap((relPath) => this.productFileService.getFileBlob(relPath)),
            )
            .subscribe({
                next: (blob: Blob) => {
                    // 3) Build tên file mong muốn
                    let fileName;
                    if (v.approvalTime) {
                        fileName = `${this.currentProduct.name}_Checklist CGSX_${v.version}_${DateUtils.formatDateWithPattern(
                            v.approvalTime.getTime(),
                            'dd/mm/yyyy',
                        )}.xlsx`;
                    } else {
                        fileName = `${this.currentProduct.name}_Checklist CGSX_${v.version}_${DateUtils.formatDateWithPattern(
                            v.created.getTime(),
                            'dd/mm/yyyy',
                        )}.xlsx`;
                    }

                    // 4) Tạo blob URL và trigger download
                    const blobUrl = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = blobUrl;
                    a.download = fileName;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(blobUrl);
                },
                error: (err) => console.error('Error downloading file:', err),
            });
    }
}
