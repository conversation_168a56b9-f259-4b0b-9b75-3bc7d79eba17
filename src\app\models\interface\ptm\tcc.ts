export interface InstructionInfo {
    id: number | null; // ID của instructionInfo
    instructionId: number | null; // ID của Version HS CNSX
    status: number | null; // Trạng thái
    type: number | null; // Loại Instruction
    reviewers: string | null; // Danh sách người phê <PERSON> (dạng chuỗi JSON: "[1,2,3]")
    creator: string | null; // Người tạo
    createdAt: string | null; // <PERSON><PERSON><PERSON> tạo (ISO 8601)
    updatedAt: string | null; // Ng<PERSON>y cập nhật (ISO 8601)
}
export interface TccUpdate {
    instructionInfoId: number | null; // ID của Instruction Info
    instructionInfo: InstructionInfo; // Thông tin chi tiết Instruction Info
    limitStation: number | null; // Giới hạn số lượng trạm
    tccSections: TccSection[]; // Danh sách các section
}
export interface TccSection {
    id: number | null; // ID TccSection
    instructionId: number | null; // ID của Instruction
    line: number | null; // Line sản xuất
    tccProductionPlan: TccProductionPlan; // Kế hoạch sản xuất
    tccProductionTime: TccProductionTime; // Thời gian sản xuất
    tccDirectLaborOnline: TccDirectLabor; // Lao động trực tiếp Online
    tccDirectLaborOffline: TccDirectLabor; // Lao động trực tiếp Offline
}
export interface TccProductionPlan {
    id: number | null; // ID kế hoạch sản xuất
    tccSectionId: number | null; // Liên kết đến TccSection
    planOutput: number | null; // Sản lượng kế hoạch
    totalLabor: number | null; // Tổng lao động
    norm: number | null; // Định mức
    taktTime: number | null; // Takt time
}
export interface TccProductionTime {
    id: number | null; // ID thời gian sản xuất
    tccSectionId: number | null; // Liên kết đến TccSection
    shiftsPerDay: number | null; // Số ca mỗi ngày
    timePerShift: number | null; // Thời gian mỗi ca
    timePerShiftUnit: number | null; // Đơn vị thời gian mỗi ca (1: phút, 2: giờ, ...)
    restTime: number | null; // Thời gian nghỉ
    lunchTime: number | null; // Thời gian ăn trưa
    snackTime: number | null; // Thời gian ăn phụ
    productionTime: number | null; // Tổng thời gian sản xuất
    productionTimeUnit: number | null; // Đơn vị của tổng thời gian sản xuất
}
export interface TccDirectLabor {
    id: number | null; // ID lao động trực tiếp
    tccSectionId: number | null; // Liên kết đến TccSection
    lineBalanceIndex: number | null; // Chỉ số cân bằng chuyền
    smoothingIndex: number | null; // Chỉ số làm mượt
    cycleTime: number | null; // Chu kỳ sản xuất
    laborCount: number | null; // Số lao động
    chartUrl: string | null; // Đường dẫn biểu đồ
    laborType: number | null; // Loại lao động (1: Online, 2: Offline)
    stations: TccStation[] | null; // Danh sách trạm
}
export interface TccStation {
    id: number | null; // ID trạm
    tccDirectLaborId: number | null; // Liên kết đến TccDirectLabor
    stationName: string | null; // Tên trạm
    stationCode: string | null; // Mã trạm
    refStationCode: string | null; // Mã trạm tham chiếu
    od: string | null; // OD
    spec: string | null; // Quy cách
    laborTime: number | null; // Thời gian lao động
    machineTime: number | null; // Thời gian máy
    quantityStation: number | null; // Số lượng trạm
    quantityDirectLabor: number | null; // Số lao động tại trạm
    cycleTime: number | null; // Chu kỳ tại trạm
    output: number | null; // Sản lượng tại trạm
}
