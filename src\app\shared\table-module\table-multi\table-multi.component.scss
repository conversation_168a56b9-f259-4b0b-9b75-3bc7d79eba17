:host ::ng-deep .p-datatable-tbody > tr:hover {
    background: #f9fcfe;
}

:host ::ng-deep .p-datatable-thead > tr > th {
    background: #f5f7fa !important;
}

:host ::ng-deep .qc-disabled .p-button {
    background-color: var(--background-disabled-qc);
    cursor: not-allowed !important;
}

::ng-deeep .table-btn-delete .p-button {
    width: 36px;
    height: 36px;
}

:host ::ng-deep .p-datatable .p-datatable-header {
    padding: 0 0 1 0;
    border-bottom: 1px solid #e9ecef;
}
:host ::ng-deep .p-datatable-wrapper {
    position: relative;
    height: calc(100vh - 19rem);
    min-height: 500px;
}

:host ::ng-deep .table-container .p-datatable-thead {
    position: sticky;
    top: 0;
    z-index: 2;
}

.table-container {
    padding: 0 0rem 0 0rem;
}
.table-wrapper {
    background-color: white;
    border-radius: 6px;
    border: 1px solid #ebeff5;
}
.table-caption {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 36px;
    span {
        font-size: 14px;
        line-height: 20px;
        font-weight: 600;
    }
    .table-caption-toolbar {
        display: flex;
        align-items: center;
        gap: 12px;

        .table-caption-toolbar-item {
            display: flex;
            align-items: center;
            justify-content: space-evenly;
            align-items: center;
            gap: 12px;

            > span {
                font-size: 14;
                line-height: 20px;
                font-weight: 400;
            }
        }
    }
}


/* Ẩn thanh cuộn trên Chrome, Safari */
.scrollbar-hidden::-webkit-scrollbar {
    display: none;
  }
  
  /* Ẩn thanh cuộn trên Firefox */
  .scrollbar-hidden {
    scrollbar-width: none;
  }
  