// src/app/components/sop-base.component.ts
import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, OnChanges, SimpleChanges, ViewChildren, QueryList } from '@angular/core';
import { CommonModule, formatDate, registerLocaleData } from '@angular/common';
import { FormBuilder, FormArray, ReactiveFormsModule, FormGroup, Validators } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { CalendarModule } from 'primeng/calendar';
import { MultiSelectModule } from 'primeng/multiselect';
import { DropdownModule } from 'primeng/dropdown';
import { TagModule } from 'primeng/tag';
import { PanelModule } from 'primeng/panel';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { ProductDetail } from 'src/app/models/interface/pms';
import localeVi from '@angular/common/locales/vi';
import { FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { FormArrayCustom } from 'src/app/shared/form-module/from-array.custom';
import { SectionSobBase } from 'src/app/models/interface/ptm';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { TabActionButtonsComponent } from '../tab-action-buttons/tab-action-buttons.component';
import { FileUploadManagerService } from 'src/app/services/upload/file-upload-manager.service';
import { combineLatest, finalize, map, Subscription, switchMap, tap } from 'rxjs';
import { FormActionService } from 'src/app/services/ptm/manufacturing-technology-records/form-action.service';
import { UploadCustomComponent } from 'src/app/shared/components/upload-custom/upload-custom.component';
import { v4 as uuid } from 'uuid';
import { FullscreenToggleDirective } from 'src/app/shared/directives/fullscreen-toggle.directive';
import { UploadService } from 'src/app/services/upload/upload.service';
registerLocaleData(localeVi, 'vi-VN');
@Component({
    selector: 'app-sop-application',
    standalone: true,
    imports: [
        ReactiveFormsModule,
        CommonModule,
        InputTextModule,
        CalendarModule,
        MultiSelectModule,
        DropdownModule,
        TagModule,
        PanelModule,
        TableModule,
        ButtonModule,
        FormCustomModule,
        TabActionButtonsComponent,
        UploadCustomComponent,
        FullscreenToggleDirective,
    ],
    templateUrl: './sop-application.component.html',
    styleUrls: ['./sop-application.component.scss'],
    providers: [FormActionService],
})
export class SopApplicationComponent implements OnInit, OnChanges, OnDestroy {
    @Input() product!: ProductDetail;
    @Input() mode: 'view' | 'create' | 'edit' = 'create';
    @Input() version: { status: number } = { status: 1 };
    @ViewChildren('RowUploader') rowUploader!: QueryList<UploadCustomComponent>;
    private subs = new Map<number, Subscription>();
    public isBusy$ = combineLatest([this.formSvc.isSaving$, this.fileUploadManagerService.isUploading$]).pipe(
        map(([saving, uploading]) => saving || uploading),
    );
    public loadingRows = new Map<number, boolean>();
    private uploadIds = new Map<number, string>();
    sectionsData: SectionSobBase[] = [
        {
            code: 'SMTTOP',
            entries: [
                {
                    id: 1,
                    STT: 1,
                    OC: 'P010',
                    mainOperationBlock: 'abc',
                    operationDescription: 'Lắp ráp',
                    focusLevel: 1,
                    workArea: '10s',
                    equipmentName: 'Máy 1',
                    equipmentQty: 2,
                    materialCode: 'UJG87',
                    materialName: 'Keo dán',
                    materialQty: 'Chú ý mối hàn',
                    workStationNotes: '7s',
                    details: '7s',
                    basicOperationTime: 'a',
                },
                {
                    id: 2,
                    STT: 2,
                    OC: 'P010',
                    mainOperationBlock: 'abc',
                    operationDescription: 'Lắp ráp',
                    focusLevel: 1,
                    workArea: '10s',
                    equipmentName: 'Máy 1',
                    equipmentQty: 2,
                    materialCode: 'UJG87',
                    materialName: 'Keo dán',
                    materialQty: 'Chú ý mối hàn',
                    workStationNotes: '7s',
                    details: '7s',
                    basicOperationTime: 'a',
                },
            ],
        },
        {
            code: 'SMTBOT',
            entries: [
                {
                    id: 1,
                    STT: 1,
                    OC: 'P010',
                    mainOperationBlock: 'abc',
                    operationDescription: 'Lắp ráp',
                    focusLevel: 1,
                    workArea: '10s',
                    equipmentName: 'Máy 1',
                    equipmentQty: 2,
                    materialCode: 'UJG87',
                    materialName: 'Keo dán',
                    materialQty: 'Chú ý mối hàn',
                    workStationNotes: '7s',
                    details: '7s',
                    basicOperationTime: 'a',
                },
                {
                    id: 2,
                    STT: 2,
                    OC: 'P010',
                    mainOperationBlock: 'abc',
                    operationDescription: 'Lắp ráp',
                    focusLevel: 1,
                    workArea: '10s',
                    equipmentName: 'Máy 1',
                    equipmentQty: 2,
                    materialCode: 'UJG87',
                    materialName: 'Keo dán',
                    materialQty: 'Chú ý mối hàn',
                    workStationNotes: '7s',
                    details: '7s',
                    basicOperationTime: 'a',
                },
            ],
        },
    ];
    @Output() submitted = new EventEmitter<any>();
    // --- form cho Thông tin chung ---
    generalInfoForm!: FormGroupCustom<{
        reviewDate: Date | null;
        approveDate: Date | null;
        reviewers: string[];
        approver: string | null;
    }>;
    // --- form cho Các khối SMT động ---
    sectionsForm!: FormGroupCustom<{
        sections: SectionSobBase[];
    }>;
    // formGroup!: FormGroupCustom<any>;
    accountsCanReview = [
        { id: 'user1', label: 'Nguyễn Văn A' },
        { id: 'user2', label: 'Trần Thị B' },
    ];
    sopName = '';
    applyDate = '';

    // cấu hình chung tái sử dụng
    private generalInfoControls = {
        reviewDate: [null],
        approveDate: [null],
        reviewers: [[]],
        approver: [null],
    };

    constructor(
        private fb: FormBuilder,
        private fileUploadManagerService: FileUploadManagerService,
        private uploadService: UploadService,
        public formSvc: FormActionService<{
            reviewDate: Date | null;
            approveDate: Date | null;
            reviewers: string[];
            approver: string | null;
            sections: SectionSobBase[];
        }>,
    ) {
        // khởi tạo sơ bộ để tránh lỗi undefined
        this.buildGeneralInfo([]);
        this.buildSections([]);
    }

    ngOnInit(): void {
        if (this.product) {
            this.setupGeneralInfo();
            this.buildGeneralInfo(this.generalInfoControls);
            this.buildSections(this.sectionsData);
        }
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['product'] && this.product) {
            this.setupGeneralInfo();
            this.buildGeneralInfo(this.generalInfoControls);
            this.buildSections(this.sectionsData);
        }
    }

    ngOnDestroy(): void {}

    private setupGeneralInfo() {
        this.sopName = `SOPBASE-${this.product.tradeName}-${this.product.vnptManPn}`;
        this.applyDate = formatDate(new Date(), 'HH:mm dd/MM/yyyy', 'vi-VN');
    }

    private buildGeneralInfo(ctrls: any) {
        this.generalInfoForm = new FormGroupCustom(this.fb, {
            ...this.generalInfoControls,
        });
    }

    private buildSections(data: SectionSobBase[]) {
        const sectionControls = data.map((sec) => {
            const entries = sec.entries.map(
                (e) =>
                    new FormGroupCustom(this.fb, {
                        id: [uuid()],
                        sequenceNumber: [e.STT],
                        operationCode: [e.OC],
                        mainOperationBlock: [e.mainOperationBlock],
                        operationDescription: [e.operationDescription],
                        focusLevel: [e.focusLevel],
                        workArea: [e.workArea],
                        equipmentName: [e.equipmentName],
                        equipmentQty: [e.equipmentQty],
                        materialCode: [e.materialCode],
                        materialName: [e.materialName],
                        materialQty: [e.materialQty],
                        workStationNotes: [e.workStationNotes],
                        details: [e.details],
                        basicOperationTime: [e.basicOperationTime],
                    }),
            );
            return new FormGroupCustom(this.fb, {
                code: [sec.code],
                entries: new FormArrayCustom(entries),
            });
        });

        this.sectionsForm = new FormGroupCustom(this.fb, {
            sections: new FormArrayCustom(sectionControls),
        });
    }

    get sections() {
        return this.sectionsForm.get('sections') as FormArrayCustom<any>;
    }

    onSubmitGeneralInfo() {
        if (this.generalInfoForm.status === 'VALID') {
            this.submitted.emit({ general: this.generalInfoForm.getRawValue() });
        }
    }

    onSubmitSections() {
        if (this.sectionsForm.status === 'VALID') {
            this.submitted.emit({ sections: this.sectionsForm.getRawValue().sections });
        }
    }
    onFileSelected(files: any[], rowId: number) {
        const file = files[0];
        const fileName: string = files[0].name;
        if (!file) return;
        this.loadingRows.set(rowId, true);
        const uploadId = uuid();
        this.uploadIds.set(rowId, uploadId);

        // Find the row form group by id
        const idx = this.sections.controls.findIndex((ctrl) => ctrl.get('id')?.value === rowId);
        if (idx === -1) return;
        const ctrl = this.sections.at(idx);

        ctrl.patchValue({
            file,
        });
        const sub = this.uploadService
            .analyzeFile(fileName, 'DOCUMENT')

            .pipe(
                tap((meta) => {
                    if (ctrl.get('details')) ctrl.get('details')?.setValue(meta.objectPath);
                }),
                switchMap((meta) => this.uploadService.uploadToPresignedUrl(files[0], meta.presignedUrl)),
                // dù complete hay error, luôn finish uploadId
                finalize(() => this.fileUploadManagerService.finish(uploadId)),
            )
            .subscribe({
                next: (p) => ({}),
                error: (e) => {
                    const list = this.rowUploader.toArray();
                    if (list[idx]) list[idx].clearAll();
                },
                complete: () => {
                    this.loadingRows.set(rowId, false);
                },
            });
        this.subs.set(rowId, sub);
        // Hook cancel callback
        this.fileUploadManagerService.start(uploadId, () => {
            const s = this.subs.get(rowId);
            if (s) {
                s.unsubscribe();
                this.subs.delete(rowId);
            }
        });
    }
    onClearFile(rowId: number) {
        const uploadId = this.uploadIds.get(rowId);
        if (uploadId) {
            this.fileUploadManagerService.cancel(uploadId);
            this.uploadIds.delete(rowId);
        }

        // Reset controls for this row
        const idx = this.sections.controls.findIndex((ctrl) => ctrl.get('id')?.value === rowId);
        if (idx !== -1) {
            const ctrl = this.sections.at(idx);
            ctrl.patchValue({ file: null });
        }
    }
}
