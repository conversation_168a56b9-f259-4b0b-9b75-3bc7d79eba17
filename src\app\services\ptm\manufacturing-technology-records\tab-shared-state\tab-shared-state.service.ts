// src/app/services/tab-shared-state.service.ts
import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable()
export class TabSharedStateService {
    // BehaviorSubject: luôn giữ giá trị cuối cùng
    private productInstructionId$ = new BehaviorSubject<number | null>(0);
    private mode$ = new BehaviorSubject<'view' | 'create' | 'edit'>('create');
    private productVersionId$ = new BehaviorSubject<number | null>(0);
    private phase$ = new BehaviorSubject<number | null>(1);
    private tabIndex$ = new BehaviorSubject<number | null>(1);

    // SETTER
    setProductInstructionId(val: number) {
        this.productInstructionId$.next(val);
    }
    setMode(val: 'view' | 'create' | 'edit') {
        this.mode$.next(val);
    }
    setProductVersionId(val: number) {
        this.productVersionId$.next(val);
    }
    setPhase(val: number) {
        this.phase$.next(val);
    }

    setTabIndex(val: number) {
        this.tabIndex$.next(val);
    }

    // GETTER (observable)
    getProductInstructionId$() {
        return this.productInstructionId$.asObservable();
    }
    getMode$() {
        return this.mode$.asObservable();
    }
    getProductVersionId$() {
        return this.productVersionId$.asObservable();
    }
    getPhase$() {
        return this.phase$.asObservable();
    }
    getTabIndex() {
        return this.tabIndex$.asObservable();
    }

    // GET giá trị hiện tại (nếu cần)
    getProductInstructionId() {
        return this.productInstructionId$.getValue();
    }
    getMode() {
        return this.mode$.getValue();
    }
    getProductVersionId() {
        return this.productVersionId$.getValue();
    }
    getPhase() {
        return this.phase$.getValue();
    }
}
