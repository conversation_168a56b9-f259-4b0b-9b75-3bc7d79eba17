import { Component, OnChanges, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import {
    LogisticExpensesDeliveryDTO,
    Logistics,
    LogisticsAgent,
    LogisticsContract,
    LogisticsDocument,
    LogisticsEvaluate,
    LogisticsFee,
} from 'src/app/models/interface/sc';
import { LogisticsService } from 'src/app/services/sc/logistics/logistics.service';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { ButtonModule } from 'primeng/button';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { AbstractControlCustom, FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { DEFAULT_LOGISTIC_DOCUMENT, LOGISTICS_STATUS, LOGISTICS_TYPE } from 'src/app/models/constant/sc';
import { AbstractControl, FormArray, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { AlertService } from 'src/app/shared/services/alert.service';
import { ConfirmationService } from 'primeng/api';
import { FileService } from 'src/app/shared/services/file.service';
import { LogisticsEvaluateService } from 'src/app/services/sc/logistics/logistics-evaluate.service';
import { LogisticsContractService } from 'src/app/services/sc/logistics/logistics-contract.service';
import { LogisticsDocumentService } from 'src/app/services/sc/logistics/logistics-document.service';
import { LogisticsAgentService } from 'src/app/services/sc/logistics/logistics-agent.service';
import { DateUtils } from 'src/app/utils/date-utils';
import { ApiResponse, Column } from 'src/app/models/interface';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { emailValidator, nonEmptyArrayValidator, requiredValidator } from 'src/app/utils/validator';
import { FormArrayCustom } from 'src/app/shared/form-module/from-array.custom';
import { cloneDeep, isArray } from 'lodash';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { TableModule } from 'primeng/table';
import { DropdownModule } from 'primeng/dropdown';
import { DialogModule } from 'primeng/dialog';
import { PanelModule } from 'primeng/panel';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { CalendarModule } from 'primeng/calendar';
import { TooltipModule } from 'primeng/tooltip';
import { ButtonGroupFileComponent } from 'src/app/shared/components/button-group-file/button-group-file.component';
import { AttachmentComponent } from 'src/app/shared/components/attachment/attachment.component';
import { InputNumberModule } from 'primeng/inputnumber';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { CheckboxModule } from 'primeng/checkbox';
import { CommonModule } from '@angular/common';
import { InputNumberComponent } from 'src/app/shared/components/inputnumber/inputnumber.component';
@Component({
    selector: 'app-logistic-detail',
    templateUrl: './logistics-supplier.edit.html',
    styleUrls: ['./logistics-supplier.component.scss'],
    standalone: true,
    imports: [
        RouterLink,
        SubHeaderComponent,
        ButtonModule,
        ReactiveFormsModule,
        ButtonModule,
        InputTextModule,
        InputTextareaModule,
        TableModule,
        DropdownModule,
        DialogModule,
        PanelModule,
        FormCustomModule,
        CalendarModule,
        TooltipModule,
        ButtonGroupFileComponent,
        AttachmentComponent,
        InputNumberModule,
        HasAnyAuthorityDirective,
        OverlayPanelModule,
        CommonModule,
        CheckboxModule,
        InputNumberComponent,
    ],
    providers: [LogisticsService, LogisticsEvaluateService, LogisticsContractService, LogisticsDocumentService, LogisticsAgentService],
})
export class LogisticsEditComponent implements OnInit, OnChanges {
    // Logistics Supplier
    oldLogistics: Logistics;
    logisticId: number;

    formGroup: FormGroupCustom<Logistics>;
    @ViewChild('form', { static: false }) formComponent!: FormComponent;

    // option
    optionType = LOGISTICS_TYPE;
    optionStatus = LOGISTICS_STATUS;
    yearRange: { label: string; value: number }[];

    // Contract
    isAddingContract: boolean = false;
    isEditingContract: boolean = false;
    backUpContract: LogisticsContract;
    // End contract

    // document
    isAddingDocument: boolean = false;
    isEditingDocument: boolean = false;
    backUpDocument: LogisticsDocument;

    // agent
    isAddingAgent: boolean = false;
    isEditingAgent: boolean = false;
    backUpAgent: LogisticsAgent;

    // evaluate
    isAddingEvaluate: boolean = false;
    isEditingEvaluate: boolean = false;
    backUpEvaluate: LogisticsEvaluate;

    constructor(
        private activatedRoute: ActivatedRoute,
        private router: Router,
        private loadingService: LoadingService,
        private fb: FormBuilder,
        private alertService: AlertService,
        private confirmationService: ConfirmationService,
        private logisticsService: LogisticsService,
        private fileService: FileService,
        private logisticsEvaluateService: LogisticsEvaluateService,
        private logisticsContractService: LogisticsContractService,
        private logisticsDocumentService: LogisticsDocumentService,
        private logisticsAgentService: LogisticsAgentService,
    ) {
        const queryParams = this.activatedRoute.snapshot.queryParams;
        this.initForm(this.oldLogistics, queryParams['type'] ? Number(queryParams['type']) : 0);
        this.yearRange = DateUtils.generateYearRange(2000);
        this.columnChoose = this.columns.filter((col: Column) => {
            return !col.hide;
        });
        this.columnChooseInsurance = this.columnsInsurance.filter((col: Column) => {
            return !col.hide;
        });
        this.columnsChoseDelivery = this.columnsDelivery.filter((col: Column) => {
            return !col.hide;
        });
    }
    ngOnInit(): void {
        this.loadData();
    }

    loadData = () => {
        const paramString = this.activatedRoute.snapshot.paramMap.get('id');
        if (paramString) {
            this.logisticId = Number(paramString);
        }
        if (this.logisticId) {
            this.loadingService.show();
            this.logisticsService.getOne(this.logisticId).subscribe({
                next: (res) => {
                    this.loadingService.hide();
                    this.oldLogistics = res.body;
                    this.initForm(this.oldLogistics);
                    this.sortContracts();
                },
                error: () => {
                    this.loadingService.hide();
                    this.router.navigate(['./..']);
                },
            });
        }
    };

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['oldLogistics'] && changes['oldLogistics'].currentValue && !changes['oldLogistics'].previousValue) {
            this.initForm(this.oldLogistics);
            this.sortContracts();
        }
    }

    initForm(data: Logistics | null, type?: number) {
        this.formGroup = new FormGroupCustom<Logistics>(this.fb, {
            id: [data?.id],
            fullName: [data?.fullName, Validators.required],
            shortName: [data?.shortName, Validators.required],
            type: [{ value: data ? data.type : type, disabled: true }, Validators.required],
            serviceProvided: [data?.serviceProvided],
            address: [data?.address],
            national: [data?.national],
            website: [data?.website],
            contactPerson: [data?.contactPerson],
            position: [data?.position],
            email: [data?.email, [Validators.required, emailValidator]],
            phone: [data?.phone],
            tradingYear: [data?.tradingYear],
            status: [data ? data.status : this.optionStatus[0].value],
            note: [data?.note],
            contractStartTime: [data?.contractStartTime],
            contractEndTime: [data?.contractEndTime],
            logisticsContracts: new FormArrayCustom(this.initFormContact(data?.logisticsContracts || [])),
            logisticsDocuments: new FormArrayCustom(this.initFormDocument(data?.logisticsDocuments || DEFAULT_LOGISTIC_DOCUMENT)),
            logisticsAgents: new FormArrayCustom(this.initFormAgent(data?.logisticsAgents || [])),
            logisticsEvaluates: new FormArrayCustom(this.initFormEvaluate(data?.logisticsEvaluates || [])),
            logisticsFee: this.initFormFee(data?.logisticsFee),
        });
    }

    initFormContact(items: LogisticsContract[]): FormGroup[] {
        return items.map(
            (item) =>
                new FormGroupCustom(this.fb, {
                    id: [item?.id],
                    logisticId: [item?.logisticId ?? this.logisticId],
                    created: [item?.created],
                    updated: [item?.updated],
                    createdBy: [item?.createdBy],
                    updatedBy: [item?.updatedBy],
                    tenantId: [item?.tenantId],
                    active: [item?.active],
                    attachments: [item?.attachments, requiredValidator('Thiếu file đính kèm')],
                    attachmentIds: [item?.attachmentIds, [requiredValidator('Thiếu file đính kèm'), nonEmptyArrayValidator('Thiếu file đính kèm')]],
                    startTimeCustom: [item?.startTime ? new Date(item?.startTime) : null, Validators.required],
                    startTime: [item?.startTime],
                    endTimeCustom: [item?.endTime ? new Date(item?.endTime) : null],
                    endTime: [item?.endTime],
                    isEdit: [false],
                }),
        );
    }

    initFormDocument(items: LogisticsDocument[]): FormGroup[] {
        return items.map(
            (item) =>
                new FormGroupCustom(this.fb, {
                    id: [item?.id],
                    logisticId: [item?.logisticId ?? this.logisticId],
                    attachments: [item?.attachments, requiredValidator('Thiếu file đính kèm')],
                    attachmentIds: [item?.attachmentIds, [requiredValidator('Thiếu file đính kèm'), nonEmptyArrayValidator('Thiếu file đính kèm')]],
                    created: [item?.created],
                    updated: [item?.updated],
                    createdBy: [item?.createdBy],
                    updatedBy: [item?.updatedBy],
                    tenantId: [item?.tenantId],
                    active: [item?.active],
                    name: [item?.name, Validators.required],
                    isEdit: [false],
                }),
        );
    }

    initFormAgent(items: LogisticsAgent[]): FormGroup[] {
        return items.map(
            (item) =>
                new FormGroupCustom(this.fb, {
                    id: [item?.id],
                    logisticId: [item?.logisticId ?? this.logisticId],
                    created: [item?.created],
                    updated: [item?.updated],
                    createdBy: [item?.createdBy],
                    updatedBy: [item?.updatedBy],
                    tenantId: [item?.tenantId],
                    active: [item?.active],
                    name: [item?.name, Validators.required],
                    address: [item?.address, Validators.required],
                    isEdit: [false],
                }),
        );
    }

    initFormEvaluate(items: LogisticsEvaluate[]): FormGroup[] {
        return items.map(
            (item) =>
                new FormGroupCustom(this.fb, {
                    id: [item?.id],
                    created: [item?.created],
                    updated: [item?.updated],
                    createdBy: [item?.createdBy],
                    updatedBy: [item?.updatedBy],
                    tenantId: [item?.tenantId],
                    active: [item?.active],
                    logisticId: [item?.logisticId ?? this.logisticId],
                    result: [item?.result],
                    year: [item?.year, Validators.required],
                    rank: [item?.rank],
                    note: [item?.note],
                    attachment: [item?.attachment, requiredValidator('Thiếu file đính kèm')],
                    attachmentId: [item?.attachmentId, requiredValidator('Thiếu file đính kèm')],
                    isEdit: [false],
                }),
        );
    }
    initFormFee(item: LogisticsFee): FormGroupCustom {
        return new FormGroupCustom(this.fb, {
            id: [item?.id],
            logisticId: [this.logisticId],
            currency: [item?.currency ?? 'USD'],
            seaRoad: [item?.seaRoad],
            airRoad: [item?.airRoad],
            lcAirSea: [item?.lcAirSea],
            internalRoad: [item?.internalRoad],
            internationalRoad: [item?.internationalRoad],
            minFee: [item?.minFee],
            created: [item?.created],
            updated: [item?.updated],
            createdBy: [item?.createdBy],
            updatedBy: [item?.updatedBy],
            active: [item?.active],
            tenantId: [item?.tenantId],
            isEdit: [false],
        });
    }

    get contracts(): FormArray {
        return this.formGroup.get('logisticsContracts') as FormArray;
    }
    get documents(): FormArray {
        return this.formGroup.get('logisticsDocuments') as FormArray;
    }
    get agents(): FormArray {
        return this.formGroup.get('logisticsAgents') as FormArray;
    }
    get evaluates(): FormArray {
        return this.formGroup.get('logisticsEvaluates') as FormArray;
    }
    get fee(): FormArray {
        return this.formGroup.get('logisticsFee') as FormArray;
    }

    addItem(type: 'contract' | 'document' | 'agent' | 'evaluate'): void {
        let newItem;

        switch (type) {
            case 'contract':
                newItem = new FormGroupCustom(this.fb, {
                    id: [null],
                    created: [null],
                    updated: [null],
                    createdBy: [null],
                    updatedBy: [null],
                    tenantId: [null],
                    active: [null],
                    logisticId: [this.logisticId],
                    startTimeCustom: [null, Validators.required],
                    endTimeCustom: [null],
                    attachments: [null, [requiredValidator('Thiếu file đính kèm'), nonEmptyArrayValidator('Thiếu file đính kèm')]],
                    attachmentIds: [null, [requiredValidator('Thiếu file đính kèm'), nonEmptyArrayValidator('Thiếu file đính kèm')]],
                    isEdit: [true],
                });
                this.contracts.push(newItem);
                this.isAddingContract = true;
                break;

            case 'document':
                newItem = new FormGroupCustom(this.fb, {
                    id: [null],
                    created: [null],
                    updated: [null],
                    createdBy: [null],
                    updatedBy: [null],
                    tenantId: [null],
                    logisticId: [this.logisticId],
                    name: [null, Validators.required],
                    attachments: [null, [requiredValidator('Thiếu file đính kèm'), nonEmptyArrayValidator('Thiếu file đính kèm')]],
                    attachmentIds: [null, [requiredValidator('Thiếu file đính kèm'), nonEmptyArrayValidator('Thiếu file đính kèm')]],
                    active: [true],
                    isEdit: [true],
                });
                this.documents.push(newItem);
                this.isAddingDocument = true;
                break;

            case 'agent':
                newItem = new FormGroupCustom(this.fb, {
                    id: [null],
                    created: [null],
                    updated: [null],
                    createdBy: [null],
                    updatedBy: [null],
                    tenantId: [null],
                    active: [null],
                    logisticId: [this.logisticId || null],
                    name: [null, Validators.required],
                    address: [null, Validators.required],
                    isEdit: [true],
                });
                this.agents.push(newItem);
                this.isAddingAgent = true;
                break;

            case 'evaluate':
                newItem = new FormGroupCustom(this.fb, {
                    id: [null],
                    created: [null],
                    updated: [null],
                    createdBy: [null],
                    updatedBy: [null],
                    tenantId: [null],
                    active: [null],
                    logisticId: [this.logisticId || null],
                    year: [null, Validators.required],
                    result: [null],
                    rank: [null],
                    note: [null],
                    attachment: [null, requiredValidator('Thiếu file đính kèm')],
                    attachmentId: [null, requiredValidator('Thiếu file đính kèm')],
                    isEdit: [true],
                });
                this.evaluates.push(newItem);
                this.isAddingEvaluate = true;
                break;
        }
    }

    editItem(index: number, type: 'contract' | 'document' | 'agent' | 'evaluate'): void {
        let item;

        switch (type) {
            case 'contract':
                item = this.contracts.at(index);
                this.backUpContract = item ? cloneDeep(item.getRawValue()) : null;
                item?.patchValue({ isEdit: true });
                this.isEditingContract = true;
                break;

            case 'document':
                item = this.documents.at(index);
                this.backUpDocument = item ? cloneDeep(item.getRawValue()) : null;
                item?.patchValue({ isEdit: true });
                this.isEditingDocument = true;
                break;

            case 'agent':
                item = this.agents.at(index);
                this.backUpAgent = item ? cloneDeep(item.getRawValue()) : null;
                item?.patchValue({ isEdit: true });
                this.isEditingAgent = true;
                break;

            case 'evaluate':
                item = this.evaluates.at(index);
                this.backUpEvaluate = item ? cloneDeep(item.getRawValue()) : null;
                item?.patchValue({ isEdit: true });
                this.isEditingEvaluate = true;
                break;
        }
    }
    saveItem(index: number, type: 'contract' | 'document' | 'agent' | 'evaluate'): void {
        let item: AbstractControlCustom;
        let itemValue;
        let service;

        switch (type) {
            case 'contract':
                item = this.contracts.at(index) as AbstractControlCustom;
                service = this.logisticsContractService;
                break;
            case 'document':
                item = this.documents.at(index) as AbstractControlCustom;
                service = this.logisticsDocumentService;
                break;
            case 'agent':
                item = this.agents.at(index) as AbstractControlCustom;
                service = this.logisticsAgentService;
                break;
            case 'evaluate':
                item = this.evaluates.at(index) as AbstractControlCustom;
                service = this.logisticsEvaluateService;
                break;
        }

        item.isSubmited = true;

        if (item && item.valid) {
            itemValue = item.getRawValue();

            if (this.logisticId) {
                if (type === 'contract') {
                    itemValue.startTime = itemValue.startTimeCustom ? itemValue.startTimeCustom.getTime() : null;
                    itemValue.endTime = itemValue.endTimeCustom ? itemValue.endTimeCustom.getTime() : null;
                }
                this.loadingService.show();
                const saveObservable = itemValue.id ? service.update(itemValue) : service.create(itemValue);
                saveObservable.subscribe({
                    next: (res) => {
                        this.loadingService.hide();
                        item.patchValue({ ...res.body, isEdit: false });
                        this.alertService.success('Thành công');
                        this.handleEndEdit(type);
                        if (type === 'contract') this.sortContracts();
                    },
                    error: () => {
                        this.loadingService.hide();
                    },
                });
            } else {
                item.patchValue({ ...itemValue, isEdit: false });
                if (type === 'contract') this.sortContracts();
                this.handleEndEdit(type);
            }
        }
    }

    cancelCreate(index: number, type: 'contract' | 'document' | 'agent' | 'evaluate'): void {
        let item: AbstractControl;
        switch (type) {
            case 'contract':
                item = this.contracts.at(index);
                if (this.isAddingContract) {
                    this.contracts.removeAt(index);
                } else if (this.isEditingContract) {
                    item?.patchValue(this.backUpContract);
                }
                break;

            case 'document':
                item = this.documents.at(index);
                if (this.isAddingDocument) {
                    this.documents.removeAt(index);
                } else if (this.isEditingDocument) {
                    item?.patchValue(this.backUpDocument);
                }
                break;

            case 'agent':
                item = this.agents.at(index);
                if (this.isAddingAgent) {
                    this.agents.removeAt(index);
                } else if (this.isEditingAgent) {
                    item?.patchValue(this.backUpAgent);
                }
                break;

            case 'evaluate':
                item = this.evaluates.at(index);
                if (this.isAddingEvaluate) {
                    this.evaluates.removeAt(index);
                } else if (this.isEditingEvaluate) {
                    item?.patchValue(this.backUpEvaluate);
                }
                break;
        }

        this.handleEndEdit(type);
    }

    deleteItem(index: number, type: 'contract' | 'document' | 'agent' | 'evaluate'): void {
        let item;
        let itemValue;
        let service;
        let list;

        switch (type) {
            case 'contract':
                item = this.contracts.at(index);
                service = this.logisticsContractService;
                list = this.contracts;
                break;
            case 'document':
                item = this.documents.at(index);
                service = this.logisticsDocumentService;
                list = this.documents;
                break;
            case 'agent':
                item = this.agents.at(index);
                service = this.logisticsAgentService;
                list = this.agents;
                break;
            case 'evaluate':
                item = this.evaluates.at(index);
                service = this.logisticsEvaluateService;
                list = this.evaluates;
                break;
        }

        if (item) {
            itemValue = item.getRawValue();
            this.confirmationService.confirm({
                key: 'app-confirm',
                header: 'Xác nhận xóa',
                message: 'Bạn có chắc chắn muốn xóa?',
                icon: 'pi pi-exclamation-triangle',
                accept: () => {
                    if (itemValue.id) {
                        this.loadingService.show();
                        service.delete(itemValue.id).subscribe({
                            next: () => {
                                this.loadingService.hide();
                                list.removeAt(index);
                                this.alertService.success('Xóa thành công');
                            },
                            error: () => {
                                this.loadingService.hide();
                            },
                        });
                    } else {
                        list.removeAt(index);
                    }
                },
            });
        }
    }

    sortContracts() {
        const contractsArray = this.contracts.controls as FormGroup[];
        const sortedContracts = contractsArray.sort((a, b) => {
            const dateA = a.get('endTimeCustom')?.value;
            const dateB = b.get('endTimeCustom')?.value;

            if (!dateA) return -1;
            if (!dateB) return 1;

            return new Date(dateB).getTime() - new Date(dateA).getTime();
        });

        this.formGroup.setControl('logisticsContracts', this.fb.array(sortedContracts));

        if (isArray(sortedContracts) && sortedContracts.length > 0) {
            const first = sortedContracts[0].getRawValue() as LogisticsContract;

            const contractStartTime = first.startTimeCustom ? first.startTimeCustom.getTime() : null;
            const contractEndTime = first.endTimeCustom ? first.endTimeCustom.getTime() : null;
            this.formGroup.get('contractStartTime').setValue(contractStartTime);
            this.formGroup.get('contractEndTime').setValue(contractEndTime);
            if (this.logisticId) {
                this.logisticsService.updateContractDate(this.formGroup.getRawValue()).subscribe({
                    next: () => {
                        console.log('update success contract date');
                    },
                });
            }
        }
    }

    handleEndEdit(type: 'contract' | 'document' | 'agent' | 'evaluate') {
        switch (type) {
            case 'contract':
                this.isEditingContract = false;
                this.isAddingContract = false;
                break;
            case 'document':
                this.isEditingDocument = false;
                this.isAddingDocument = false;
                break;
            case 'agent':
                this.isEditingAgent = false;
                this.isAddingAgent = false;
                break;
            case 'evaluate':
                this.isEditingEvaluate = false;
                this.isAddingEvaluate = false;
                break;
        }
    }

    onSubmit(value: Logistics): void {
        if (this.formGroup.form.valid) {
            this.loadingService.show();
            if (isArray(value.logisticsContracts)) {
                value.logisticsContracts.forEach((item) => {
                    item.startTime = item.startTimeCustom ? item.startTimeCustom.getTime() : null;
                    item.endTime = item.endTimeCustom ? item.endTimeCustom.getTime() : null;
                });
            }
            if (this.logisticId) {
                const logisticsFee =
                    this.oldLogistics.type === LOGISTICS_TYPE[1].value
                        ? { ...this.oldLogistics.logisticsFee, ...this.formGroup.get('logisticsFee').getRawValue() }
                        : null;
                this.logisticsService.update({ ...this.oldLogistics, ...value, logisticsFee }).subscribe({
                    next: () => {
                        this.loadingService.hide();
                        this.alertService.success('Thành công');
                    },
                    error: () => {
                        this.loadingService.hide();
                    },
                });
            } else {
                this.logisticsService.create(value).subscribe({
                    next: (res) => {
                        this.loadingService.hide();
                        this.alertService.success('Thành công');
                        this.router.navigate([`/sc/logistics/${res.body.id}`]);
                    },
                    error: () => {
                        this.loadingService.hide();
                    },
                });
            }
        }
    }

    handleClearFile(index: number, type: 'evaluate') {
        let item: AbstractControl;
        switch (type) {
            case 'evaluate':
                item = this.evaluates.at(index);
                item.enable();
                item.patchValue({ attachment: null, attachmentId: null, result: null, rank: null, note: null });
                break;
        }
    }

    handleUploadFileEvaluate(file: File, index: number) {
        this.loadingService.show();
        const item = this.evaluates.at(index);
        item.enable();
        this.logisticsEvaluateService.importFile(file).subscribe({
            next: (res: ApiResponse) => {
                if (res.code === 1) {
                    item.patchValue(res.data);
                } else {
                    item.patchValue({ attachment: null, attachmentId: null, result: null, rank: null, note: null });
                    this.alertService.error('Có lỗi xảy ra khi đọc file');
                }
                this.loadingService.hide();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    handleUploadFileContract(files: File[], index: number) {
        this.loadingService.show();
        this.logisticsContractService.importFile(files).subscribe({
            next: (res: ApiResponse) => {
                const item = this.contracts.at(index);
                item.enable();
                const itemValue = item.getRawValue(); // Lấy giá trị hiện tại của item
                const attachmentIds = itemValue?.attachmentIds ?? []; // Mảng cũ, mặc định là []
                const attachments = itemValue?.attachments ?? []; // Mảng cũ, mặc định là []
                if (res.code === 1) {
                    // Nối mảng cũ và mảng mới
                    const newAttachmentIds = [...attachmentIds, ...(res.data['attachmentIds'] || [])];
                    const newAttachments = [...attachments, ...(res.data['attachments'] || [])];
                    item.patchValue({
                        attachmentIds: newAttachmentIds,
                        attachments: newAttachments,
                    });
                } else {
                    this.alertService.error('Có lỗi xảy ra khi lưu file');
                    item.patchValue({ attachmentIds: attachmentIds, attachments: attachments });
                }
                this.loadingService.hide();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    handleUploadFileDocument(files: File[], index: number) {
        this.loadingService.show();
        this.logisticsDocumentService.importFile(files).subscribe({
            next: (res: ApiResponse) => {
                const item = this.documents.at(index);
                item.enable();
                const itemValue = item.getRawValue() as LogisticsDocument;
                const attachmentIds = itemValue?.attachmentIds ?? [];
                const attachments = itemValue?.attachments ?? [];
                if (res.code === 1) {
                    // Nối mảng cũ và mảng mới
                    const newAttachmentIds = [...attachmentIds, ...(res.data['attachmentIds'] || [])];
                    const newAttachments = [...attachments, ...(res.data['attachments'] || [])];
                    item.patchValue({
                        attachmentIds: newAttachmentIds,
                        attachments: newAttachments,
                    });
                } else {
                    this.alertService.error('Có lỗi xảy ra khi lưu file');
                    item.patchValue({ attachmentIds: attachmentIds, attachments: attachments });
                }
                this.loadingService.hide();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    columnChoose: Column[] = [];

    columns: Column[] = [
        { header: 'STT', field: 'rowIndex', default: true, hide: false, group: 'shipmentInfo' },
        { header: 'Số BO', field: 'boCode', default: true, hide: false, group: 'shipmentInfo' },
        { header: 'Số PO', field: 'poNumber', default: true, hide: false, group: 'shipmentInfo' },
        { header: 'Ngày hoàn thành', field: 'completeDate', default: true, hide: false, group: 'shipmentInfo' },
        {
            header: 'Tên người giao hàng(với lô hàng nhập khẩu), Tên người nhận hàng(với lô hàng xuất khẩu)',
            field: 'shipperName',
            default: true,
            hide: false,
            group: 'shipmentInfo',
        },
        { header: 'Số vận đơn', field: 'trackingNumber', default: true, hide: false, group: 'shipmentInfo' },
        { header: 'Số tờ khai', field: 'declarationNumber', default: true, hide: false, group: 'shipmentInfo' },
        { header: 'Khối lượng tổng trên packing list(KG)', field: 'totalWeightKgPacking', hide: false, group: 'shipmentInfo' },
        { header: 'Khối lượng tổng trên vận đơn(KG)', field: 'totalWeightKgOrder', hide: false, group: 'shipmentInfo' },
        { header: 'Khối lượng tính phí', field: 'chargeableWeight', hide: false, group: 'shipmentInfo' },
        { header: 'DVT khối lượng tính phí', field: 'chargeableWeightUnit', hide: false, group: 'shipmentInfo' },
        { header: 'Phương thức vận chuyển', field: 'deliveryMethod', hide: false, group: 'shipmentInfo' },
        { header: 'Hãng vận chuyển', field: 'deliveryProvider', hide: false, group: 'shipmentInfo' },
        { header: 'Điều kiện giao hàng', field: 'deliveryConditions', hide: false, group: 'shipmentInfo' },
        { header: 'Địa điểm giao hàng', field: 'shippingAddress', hide: false, group: 'shipmentInfo' },
        { header: 'Cảng xuất', field: 'departurePort', hide: false, group: 'shipmentInfo' },
        { header: 'Cảng nhập', field: 'importPort', hide: false, group: 'shipmentInfo' },
        { header: 'Địa điểm nhận hàng cuối cùng', field: 'finalDeliveryLocation', hide: false, group: 'shipmentInfo' },
        { header: 'Số hợp đồng/PO/DA', field: 'contractNumber', hide: false, group: 'shipmentInfo' },
        { header: 'Mã số kế toán', field: 'accountingCode', hide: false, group: 'shipmentInfo' },
        { header: 'Tổng giá trị lô hàng (USD)', field: 'totalShipmentValueInUsd', hide: false, group: 'shipmentInfo' },
        { header: 'Tỷ giá(USD/VNĐ)', field: 'exchangeRateUsdToVnd', hide: false, group: 'shipmentInfo' },
        { header: 'Giấy phép xuất khẩu', field: 'exportLicenseFee', hide: false, group: 'feeDelivery' },
        { header: 'C/O', field: 'coFee', hide: false, group: 'feeDelivery' },
        { header: 'Vận tải từ địa điểm giao hàng đến cảng xuất', field: 'transportFeeToExportPort', hide: false, group: 'feeDelivery' },
        { header: 'Dịch vụ hải quan', field: 'customsClearanceFee', hide: false, group: 'feeDelivery' },
        { header: 'Phí tại cảng xuất', field: 'portHandlingFeeAtExport', hide: false, group: 'feeDelivery' },
        { header: 'Thuế GTGT', field: 'vatTaxToExportPort', hide: false, group: 'feeDelivery' },
        {
            header: 'Cước hàng không/biển/bộ qua biên giới',
            field: 'crossBorderFreightCost',
            hide: false,
            group: 'exportToImportCost',
        },
        { header: 'Giấy phép nhập khẩu', field: 'importLicenseFee', hide: false, group: 'feeToFinalDelivery' },
        { header: 'Dịch vụ hải quan', field: 'customsServiceFee', hide: false, group: 'feeToFinalDelivery' },
        { header: 'Phí tại cảng nhập', field: 'importPortFee', hide: false, group: 'feeToFinalDelivery' },
        { header: 'Vận tải từ địa điểm giao hàng đến cảng xuất', field: 'inlandTransportFee', hide: false, group: 'feeToFinalDelivery' },
        { header: 'Thuế GTGT', field: 'vatTax', hide: false, group: 'feeToFinalDelivery' },
        { header: 'Hạ tầng', field: 'infrastructureFee', hide: false, group: 'importPortFee' },
        { header: 'Lưu kho', field: 'storageFee', hide: false, group: 'importPortFee' },
        {
            header: 'Nâng hạ sửa chữa vệ sinh container',
            field: 'containerFee',
            hide: false,
            group: 'importPortFee',
        },
        { header: 'Khác', field: 'otherFees', hide: false, group: 'importPortFee' },
        { header: 'Thuế GTGT', field: 'importPortVat', hide: false, group: 'importPortFee' },
        {
            header: 'Tổng chi phí logistic',
            field: 'totalLogisticsCost',
            hide: false,
            default: true,
            group: 'totalLogisticsCost',
        },
    ];

    // Getters for visibility and colspan
    get isShipmentInfoVisible(): boolean {
        return this.columns.slice(0, 22).some((col) => !col.hide);
    }

    get shipmentInfoColspan(): number {
        return this.columns.slice(0, 22).filter((col) => !col.hide).length;
    }

    get isFeeDeliveryVisible(): boolean {
        return this.columns.slice(22, 28).some((col) => !col.hide);
    }

    get feeDeliveryColspan(): number {
        return this.columns.slice(22, 28).filter((col) => !col.hide).length;
    }

    get isExportToImportCostVisible(): boolean {
        return this.columns.slice(28, 29).some((col) => !col.hide);
    }

    get exportToImportCostColspan(): number {
        return this.columns.slice(28, 29).filter((col) => !col.hide).length;
    }
    get isFeeToFinalDeliveryColspanVisible(): boolean {
        return this.columns.slice(29, 34).some((col) => !col.hide);
    }
    get feeToFinalDeliveryColspan(): number {
        return this.columns.slice(29, 34).filter((col) => !col.hide).length;
    }
    get isImportPortFeeVisible(): boolean {
        return this.columns.slice(34, 39).some((col) => !col.hide);
    }

    get importPortFeeColspan(): number {
        return this.columns.slice(34, 39).filter((col) => !col.hide).length;
    }

    get isTotalLogisticsCostVisible(): boolean {
        return !this.columns[39].hide;
    }

    get totalLogisticsCostColspan(): number {
        return this.columns[39].hide ? 0 : 1;
    }

    get visibleColumnCount(): number {
        return this.columns.filter((col) => !col.hide).length + (this.isShipmentInfoVisible ? 0 : -7); // Adjust for default columns
    }

    setColumnSelection(selectedColumns: Column[], columns: Column[]): void {
        if (selectedColumns.length === 0) {
            columns.forEach((col) => {
                if (!col.default) {
                    col.hide = true;
                }
            });
        } else {
            columns.forEach((col) => {
                if (!col.default) {
                    col.hide = !selectedColumns.some((s) => s.field === col.field);
                }
            });
        }
    }

    columnChooseInsurance: Column[] = [];

    columnsInsurance: Column[] = [
        { header: 'STT', field: 'rowIndex', default: true, hide: false },
        { header: 'Số BO', field: 'boCode', default: true, hide: false },
        { header: 'Ngày cấp', field: 'issueDate', default: true, hide: false },
        { header: 'Tỷ lệ phí (%)', field: 'feeRate', default: true, hide: false },
        { header: 'Phương thức vận chuyển', field: 'deliveryMethod', default: true, hide: false },
        { header: 'Giá trị mua bảo hiểm (USD)', field: 'insuranceValue', default: true, hide: false },
        { header: 'Số chi phí BH (VNĐ)', field: 'insuranceFee', default: true, hide: false },
        { header: 'Số vận đơn', field: 'orderNumber', hide: false },
        { header: 'Số hóa đơn', field: 'billNumber', hide: false },
        { header: 'Trọng lượng (kg)', field: 'totalWeight', hide: false },
        { header: 'Vận chuyển từ', field: 'shippingFrom', hide: false },
        { header: 'Đến', field: 'shippingTo', hide: false },
        { header: 'Hợp đồng mua bán', field: 'salesContract', hide: false },
        { header: 'Số PO', field: 'poNumber', hide: false },
        { header: 'Mã KT', field: 'technicalCode', hide: false },
        { header: 'Phòng ban', field: 'department', hide: false },
        { header: 'Ghi chú', field: 'note', hide: false },
    ];

    columnsChoseDelivery: Column[] = [];

    columnsDelivery: Column[] = [
        { header: 'STT', field: 'rowIndex', default: true, hide: false },
        { header: 'Số BO', field: 'boCode', default: true, hide: false },
        { header: 'Ngày giao hàng', field: 'deliveryDate', default: true, hide: false },
        { header: 'Chi phí vận chuyển', field: 'deliveryFee', default: true, hide: false },
        { header: 'Chi phí khác', field: 'otherCosts', default: true, hide: false },
        { header: 'Phụ phí xăng dầu', field: 'fuelFee', default: true, hide: false },
        { header: 'Tổng cộng', field: 'totalAmount', default: true, hide: false },
        { header: 'Số vận đơn', field: 'awb', hide: false },
        { header: 'Tên người gửi', field: 'fullName', hide: false },
        { header: 'SỐ PO/ HÀNG MẪU/ TÀI LIỆU', field: 'po', hide: false },
        {
            header: 'MÃ DỰ ÁN (NẾU CÓ PO) HOẶC PHÒNG BAN NHẬN HÀNG (NẾU LÀ HÀNG MẪU/ TÀI LIỆU)',
            field: 'projectCodeOrManagingDepartment',
            hide: false,
        },
        { header: 'ĐƠN VỊ PHỤ TRÁCH', field: 'managingDepartment', hide: false },
        { header: 'NƯỚC XUẤT', field: 'exportCountry', hide: false },
        { header: 'LOẠI DỊCH VỤ HÀNG NHANH', field: 'expressServiceType', hide: false },
        { header: 'KHỐI LƯỢNG', field: 'weight', hide: false },
    ];

    getProjectCodeOrManagingDepartment(expenses: LogisticExpensesDeliveryDTO): string {
        return expenses.po ? expenses.projectCode : expenses.managingDepartment;
    }
}
