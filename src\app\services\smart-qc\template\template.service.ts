import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { Template } from 'src/app/models/interface/smart-qc';

@Injectable()
export class TemplateService extends BaseService<Template> {
    constructor(protected override http: HttpClient) {
        super(http, '/smart-qc/api/template');
    }

    updateChapter(ids: number[]) {
        return this.http.post<void>('/smart-qc/api/template/update-chapter', ids);
    }
}
