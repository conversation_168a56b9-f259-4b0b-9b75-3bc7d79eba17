// src/app/shared/components/async-single-select/async-single-select.component.ts
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AutoCompleteModule } from 'primeng/autocomplete';

@Component({
    standalone: true,
    selector: 'app-async-single-select',
    imports: [CommonModule, FormsModule, AutoCompleteModule],
    templateUrl: './async-single-select.component.html',
})
export class AsyncSingleSelect {
    // cơ bản
    @Input() options: any[] = [];
    @Input() selected: any = '';
    @Input() displayField = 'name';
    @Input() placeholder = '';
    @Input() disabled = false;
    // search
    @Input() minLength = 1;
    @Input() delay = 300;

    // virtual scroll / lazy load
    @Input() virtualScroll = false;
    @Input() lazy = false;
    @Input() virtualScrollItemSize = 34;
    /** số items trên 1 “page” để tính toán pageIndex */
    @Input() pageSize = 100;

    /** gõ mới search */
    @Output() search = new EventEmitter<string>();
    /** scroll xuống đáy => lazy load page tiếp */
    @Output() lazyLoad = new EventEmitter<{ query: string; page: number }>();
    @Output() selectionChange = new EventEmitter<any>();
    @Output() clear = new EventEmitter<void>();
    onSearch(event: { query: string }) {
        const q = event.query?.trim() ?? '';
        this.search.emit(q.length >= this.minLength ? q : '');
    }

    onLazy(event: { first: number; query: string }) {
        const q = event.query?.trim() ?? '';
        const page = Math.floor(event.first / this.pageSize) + 1;
        this.lazyLoad.emit({ query: q, page });
    }

    onSelect(item: any) {
        this.selectionChange.emit(item.value);
    }
    onClear() {
        // reset local model nếu cần
        this.selected = null;
        // emit cho parent biết
        this.clear.emit();
    }
}
