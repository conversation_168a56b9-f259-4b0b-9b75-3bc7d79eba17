# README - Bàn giao kho dự án

## Tổng quan
Dự án này chứa các module và cấu trúc được xây dựng cho ứng dụng **PMS**. Các thay đổi trong kho bao gồm việc cấu hình các module, route, và các tính năng chính của ứng dụng.

## Các thay đổi chính
Dưới đây là các thay đổi chính đã được thực hiện:

1. **Cấu hình Routing**:
  - thêm cấu hình routing: src\app\app-routing.module.ts

2. **Cấu hình module**:
  - thêm thư mục PMS( Product Management System) trong modules : src\app\modules\pms
  thêm thư mục shared trong modules : src\app\modules\shared       <!-- - component chung dùng trong modules -->
3. **c<PERSON>u hình guard**:
   - thêm cấu hình guard cho route pms : src\app\core\auth\auth.guard.ts
4. **cấu hình môi trường**:
   - cấu hình biến môi trường vivas
5. **các component chung bị ảnh hưởng**:
   -  popup.component.ts  (thêm một số input custom giao diện)
   - form-item.component.ts (thêm một số input custom giao diện)

. **Mô tả chi tiết**:
    - Thêm services pms
    - thêm thư mục shared trong modules (chưa tất cả component dùng chung của các module vivas phát triển)
    - thêm custom width cho component chung popup.component src\app\shared\components\popup
    - thêm component chung tab-view trong shared src\app\shared\components\tab-view
    - thêm component chung upload-custom trong shared src\app\shared\components\upload-custom
    - thêm constant pms 
    - thêm service chung upload
    - thêm directicve table-cell (custom ô trong bảng)
    - thêm hàm tính formatAmPmDate trong common
    -thêm thư viên spark-md5 để hash md5
    - bổ sung mã lỗi trong vi.json
    - thêm const menu pms : src\app\models\constant\index.ts
   - thêm menu dashboard: src\app\modules\dashboard\dashboard.component.html
   - thêm menu topBar: app.topbar.component.html
    - thêm tính năng ẩn hiện cột thao tác tại component table-common.compoennt
    - thêm layout pms : src\app\layout\components\sidebar\data\pms.ts
