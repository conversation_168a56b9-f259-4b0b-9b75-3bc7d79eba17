// Khoảng cách giữa các panel
.section-wrapper {
    margin-bottom: 20px;
}

// Container chia 2 cột cho RDBOM
.rdbom-wrapper {
    display: flex;
    gap: 16px; // khoảng cách giữa 2 cột
    align-items: flex-start;
}

// Cột trái: bảng chiếm ~2/3
.rdbom-left {
    flex: 2;
}

// Cột phải: chiếm ~1/3
.rdbom-right {
    padding: 1rem;
    flex: 1;
    display: flex;
    justify-content: flex-end;
}

// Bên trong cột phải, căn giữa dọc
.rdbom-actions {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
}
