/* eslint-disable @typescript-eslint/no-explicit-any */
import { AfterViewInit, Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { RouterLink, Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import {Column, EventPopupSubmit, User} from 'src/app/models/interface';
import { TABLE_KEY } from 'src/app/models/constant';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { BoChangeStateDTO, BoRequestDTO, BoResponseITF } from 'src/app/models/interface/sc'; // Sử dụng BoResponse và BoRequest
import { BoService } from 'src/app/services/sc/bo/bo.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { TableCommonService } from 'src/app/shared/table-module/table.common.service';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { BO_STATUS_CONSTANT, BoStatus, MAP_TYPE_SHIPPING, TYPE_SHIPPING } from 'src/app/models/constant/sc';
import { SCDepartmentService } from 'src/app/services/sc/bo/department.service';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { TagModule } from 'primeng/tag';
import { DialogModule } from 'primeng/dialog';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { PaginatorModule } from 'primeng/paginator';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { BaseUserService } from 'src/app/services/administration/admin/user.service';
import { FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { ConfirmationService } from 'primeng/api';
import {PopupComponent} from "../../../../shared/components/popup/popup.component";
import {FileService} from "../../../../shared/services/file.service";
import {ConfirmDialogModule} from "primeng/confirmdialog";

@Component({
    selector: 'app-request-shipping-list',
    standalone: true,
    imports: [
        CommonModule,
        SubHeaderComponent,
        RouterLink,
        ButtonModule,
        TableCommonModule,
        HasAnyAuthorityDirective,
        TagModule,
        DialogModule,
        FormCustomModule,
        InputTextareaModule,
        PaginatorModule,
        ReactiveFormsModule,
        PopupComponent,
        ConfirmDialogModule,
    ],
    templateUrl: './list.component.html',
    styleUrls: ['./list.component.scss'],
    providers: [BoService, SCDepartmentService],
})
export class ListComponent implements OnInit, AfterViewInit {
    @ViewChild('templateType') templateType: TemplateRef<Element>;
    @ViewChild('templateAction') templateAction: TemplateRef<Element>;
    @ViewChild('templateCode') templateCode: TemplateRef<Element>;
    @ViewChild('templateDepartment') templateDepartment: TemplateRef<Element>;
    @ViewChild('templateStatus') templateStatus: TemplateRef<Element>;
    columns: Column[] = [];
    tableId: string = TABLE_KEY.REQUEST_SHIPPING;
    state: QueryObserverBaseResult<BoResponseITF[]>;

    // Options
    TYPE_SHIPPING = TYPE_SHIPPING;
    MAP_TYPE_SHIPPING = MAP_TYPE_SHIPPING;
    mapDepartment: Record<number, string> = {};

    // Form
    isOpenModalNotification: boolean = false;
    formNotification: FormGroup;
    @ViewChild('formSubmit', { static: false }) formSubmit: FormComponent;
    receivers: User[] = [];

    constructor(
        private loadingService: LoadingService,
        private alertService: AlertService,
        private tableCommonService: TableCommonService,
        private boService: BoService,
        private sCDepartmentService: SCDepartmentService,
        private userService: BaseUserService,
        private fb: FormBuilder,
        private confirmationService: ConfirmationService,
        private router: Router,
        private fileService: FileService,
    ) {}

    ngOnInit(): void {
        // Load departments
        this.sCDepartmentService.getPage('query=&page=0&size=100&sort=id,desc').subscribe({
            next: (res) => {
                res.body.forEach((item) => {
                    this.mapDepartment[item.id] = item.name;
                });
            },
        });

        // Initialize table
        this.tableCommonService
            .init<BoResponseITF>({
                tableId: this.tableId,
                queryFn: (filter, body: unknown) => this.boService.getPageTableNative(filter, body as BoRequestDTO),
                configFilter: [
                    'boIds',
                    'poNumber',
                    'indexShipment',
                    'accountingCode',
                    'startReadyDate&endReadyDate',
                    'startRequiredArrived&endRequiredArrived',
                    'totalWeight',
                    'packageNumber',
                    'shipmentValue',
                    'startArrivedDate&endArrivedDate',
                    'startCompleteDate&endCompleteDate',
                    'note',
                    'status',
                    'typeModule',
                ],
                defaultParams: {
                    typeModule: 1,
                },
            })
            .subscribe({
                next: (res) => {
                    this.state = res;
                },
                error: (error) => {
                    this.alertService.handleError(error);
                },
            });

        // Load receivers for notification form
        this.userService.getAllQcUser(['ROLE_SUPPLIER_CHAIN']).subscribe({
            next: (res) => {
                this.receivers = res;
            },
        });

        // Initialize notification form
        this.formNotification = new FormGroupCustom(this.fb, {
            receiverId: [null, Validators.required],
            content: [null],
            status: [null],
            boId: [null],
        });
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.columns = [
                { field: 'action', header: 'Thao tác', style: { 'min-width': '8rem' }, body: this.templateAction },
                {
                    header: 'Số BO',
                    field: 'boCode',
                    body: this.templateCode,
                    default: true,
                },
                {
                    field: 'poNumber',
                    header: 'Số PO',
                    style: { 'min-width': '10rem' },
                    default: true,
                },
                { field: 'indexShipment', header: 'STT shipment Po', type: 'number' },
                { field: 'accountingCode', header: 'Mã kế toán/mã vụ việc' },
                {
                    field: 'readyDate',
                    header: 'Thời gian hàng hóa ready',
                    type: 'date',
                    format: 'dd/MM/yyyy',
                },
                {
                    field: 'requiredArrivedDate',
                    header: 'Thời gian yêu cầu hàng về',
                    type: 'date',
                    format: 'dd/MM/yyyy',
                },
                { field: 'totalWeight', header: 'Khối lượng', type: 'number' },
                { field: 'packageNumber', header: 'Số kiện', type: 'number' },
                { field: 'shipmentValue', header: 'Tổng giá trị shipment dự kiến (USD)', type: 'number' },
                {
                    field: 'dateArrive',
                    header: 'Thời gian dự kiến về kho',
                    type: 'date',
                    format: 'dd/MM/yyyy',
                },
                {
                    field: 'completeDate',
                    header: 'Ngày hoàn thành',
                    type: 'date',
                    format: 'dd/MM/yyyy',
                },
                { field: 'note', header: 'Ghi chú', style: { 'min-width': '10rem' } },
                { field: 'status', header: 'Trạng thái', body: this.templateStatus },
            ];
        });
    }

    deleteSelected = (ids: number[]) => {
        return this.boService.batchDelete(ids);
    };

    onEdit(row: BoResponseITF) {
        this.router.navigate(['/sc/request-shipping', row.id]);
    }

    onForwardLogistic(row) {
        this.formNotification.patchValue({
            status: BO_STATUS_CONSTANT.NEW,
            boId: row.id,
        });
        this.isOpenModalNotification = true;
    }

    onDelete(row) {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.loadingService.show();
                this.boService.batchDelete([row.id]).subscribe({
                    next: () => {
                        this.alertService.success('Thành công');
                        this.loadingService.hide();
                        this.state.refetch();
                    },
                    error: () => {
                        this.loadingService.hide();
                    },
                });
            },
        });
    }

    onUndo(row) {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn thu hồi Yêu cầu vận chuyển',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Đồng ý',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.loadingService.show();
                const formData: BoChangeStateDTO = {
                    status: BO_STATUS_CONSTANT.WAITING,
                };
                this.boService.updateState(row.id, formData).subscribe({
                    next: () => {
                        this.alertService.success('Thành công');
                        this.loadingService.hide();
                        this.isOpenModalNotification = false;
                        this.state.refetch();
                    },
                    error: () => {
                        this.loadingService.hide();
                    },
                });
            },
        });
    }

    getSeverityStatus(status: number) {
        switch (status) {
            case -1:
                return 'secondary';
            case 0:
                return 'secondary';
            case 1:
                return 'info';
            case 2:
                return 'info';
            case 3:
                return 'success';
            case 4:
                return 'success';
            default:
                return 'secondary';
        }
    }

    getStatusLabel(statusValue: number) {
        return BoStatus.find((status) => status.value === statusValue)?.label;
    }

    sendNotification() {
        this.loadingService.show();
        const formData = this.formNotification.getRawValue();
        this.boService.updateState(formData.boId, formData).subscribe({
            next: () => {
                this.alertService.success('Thành công');
                this.loadingService.hide();
                this.isOpenModalNotification = false;
                this.state.refetch();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    handleChangeReceivers(event: any) {
        if (event.value !== null && event.objects !== null && event.objects.length > 0) {
            const ids = event.objects.map((obj: User) => obj.id);
            this.formNotification.patchValue({
                receiverId: ids,
            });
        } else {
            this.formNotification.patchValue({
                receiverId: null,
            });
        }
    }

    exportRequestShipping(event: EventPopupSubmit<unknown>) {
        this.loadingService.show();
        this.boService.exportRequestShipping(this.tableCommonService.getBody(this.tableId)).subscribe({
            next: (res) => {
                this.loadingService.hide();
                this.fileService.downLoadFileByService(res.url, '/sc/api');
                event.close();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    protected readonly boStatus = BoStatus;
    protected readonly BO_STATUS_CONSTANT = BO_STATUS_CONSTANT;
}
