<app-sub-header [items]="itemsHeader" [action]="actionHeader">
    <ng-template #actionHeader>
        <ng-container *ngIf="approveDetail?.state === ApproveStatus.PENDING || approveDetail?.state === ApproveStatus.REJECT_PM">
            <p-button
                label="Chấp nhận"
                [disabled]="!isAllGood"
                (click)="approve('accept')"
                *appHasAnyAuthority="['ROLE_DAC', 'approve_submit', 'ROLE_QC_SUBPM']"
            ></p-button>
        </ng-container>
        <ng-container *ngIf="approveDetail?.state === ApproveStatus.PENDING || approveDetail?.state === ApproveStatus.REJECT_PM">
            <p-button
                label="Từ chối"
                *appHasAnyAuthority="['ROLE_DAC', 'approve_refuse', 'ROLE_QC_SUBPM']"
                severity="warning"
                (click)="approve('reject')"
                [disabled]="!isAnyBad"
            ></p-button>
        </ng-container>
        <p-button label="Hủy" severity="secondary" routerLink="/sqc/approve"></p-button>
    </ng-template>
</app-sub-header>

<div style="padding: 1rem 1rem 0 1rem">
    <p-panel header="Thông tin chung" [toggleable]="true" [collapsed]="collapsedInfor" (collapsedChange)="collapsedChange($event)">
        <div class="tw-grid tw-grid-cols-2 md:tw-grid-cols-6 tw-gap-4">
            <b>Tên dự án:</b>
            <div class="md:tw-col-span-2">{{ approveDetail?.contractName }}</div>
            <b>Tên công việc:</b>
            <div class="md:tw-col-span-2">{{ approveDetail?.actionName }}</div>
            <b>Tỉnh/Tp:</b>
            <div class="md:tw-col-span-2">{{ approveDetail?.areaName }}</div>

            <b>Quận/huyện:</b>
            <div class="md:tw-col-span-2">{{ approveDetail?.districtName }}</div>

            <b>Mã trạm:</b>
            <div class="md:tw-col-span-2">{{ approveDetail?.stationCode }}</div>

            <b>Tên trạm:</b>
            <div class="md:tw-col-span-2">{{ approveDetail?.stationName }}</div>

            <b>Địa chỉ:</b>
            <div class="md:tw-col-span-2">{{ approveDetail?.address }}</div>

            <b>Ngày gửi:</b>
            <div class="md:tw-col-span-2">
                {{ approveDetail?.submitDateEp | date: 'dd/MM/yyyy' }}
            </div>

            <b class="tw-col-span-1">Đội thi công:</b>
            <div class="tw-col-span-1 md:tw-col-span-2">{{ approveDetail?.employee }}</div>

            <b class="tw-col-span-1">Trạng thái:</b>
            <p-tag class="tw-col-span-1 md:tw-col-span-2" [severity]="getSeverity(approveDetail?.state)" [value]="getStateText(approveDetail?.state)" />
            <b class="tw-col-span-2 md:tw-col-span-6">Ghi chú:</b>
            <textarea
                rows="2"
                class="tw-col-span-2 md:tw-col-span-6"
                [disabled]="!(approveDetail?.state === ApproveStatus.PENDING || approveDetail?.state === ApproveStatus.REJECT_PM)"
                pInputTextarea
                [(ngModel)]="note"
            ></textarea>
        </div>
    </p-panel>
    <br />
    <div class="tw-grid tw-grid-cols-1 md:tw-grid-cols-12 tw-gap-4 relative">
        <div #treeDom class="md:tw-col-span-4">
            <p-panel header="Danh sách checklist" [toggleable]="true" [ngStyle]="{ position: isSticky ? 'sticky' : 'static', top: isSticky ? '50px' : '0' }">
                <div style="overflow: auto" [ngStyle]="{ 'max-height': heightPanelChecklist + 'px' }">
                    <p-tree
                        class="w-full"
                        selectionMode="single"
                        [value]="approveDetail?.treeCheckList"
                        [(selection)]="selectedCheckList"
                        (onNodeSelect)="nodeSelect($event)"
                    >
                        <ng-template let-node pTemplate="default">
                            <b>{{ node.label }}</b>
                        </ng-template>
                    </p-tree>
                </div>
            </p-panel>
        </div>
        <div class="md:tw-col-span-8">
            <p-panel [header]="selectedNode?.label || ''" [toggleable]="true">
                <div #approveChecklistDetail style="overflow: auto" [ngStyle]="{ 'max-height': heightPanelChecklist + 'px' }">
                    <ng-container *ngFor="let item of selectedNode?.data; let i = index">
                        <div class="tw-grid tw-grid-cols-12">
                            <p
                                class="tw-text-ellipsis tw-col-span-1 border-right-1 border-left-1 border-bottom-1 tw-border-gray-400 tw-p-2"
                                [ngClass]="{ 'border-top-1 ': i === 0 }"
                            >
                                {{ selectedNode.stt }}{{ i + 1 }}
                            </p>
                            <div class="tw-col-span-10 border-right-1 border-bottom-1 tw-border-gray-400" [ngClass]="{ 'border-top-1 ': i === 0 }">
                                <div class="tw-col-span-10 tw-grid tw-grid-cols-2">
                                    <p class="tw-text-ellipsis tw-p-2 border-right-1 tw-border-gray-400">
                                        {{ item?.checklistDetail?.name }}
                                    </p>
                                    <ng-container *ngIf="item.type !== CheckListType.IMAGE && !objectTaskDetail[item.id].ignored">
                                        <div class="tw-p-2 tw-border-gray-400" [ngSwitch]="item.type">
                                            <div>
                                                <textarea
                                                    *ngSwitchCase="CheckListType.TEXT"
                                                    [disabled]="
                                                        !(approveDetail?.state === ApproveStatus.PENDING || approveDetail?.state === ApproveStatus.REJECT_PM) ||
                                                        objectTaskDetail[item.id].ignored
                                                    "
                                                    pInputTextarea
                                                    [(ngModel)]="objectTaskDetail[item.id].value"
                                                    placeholder="Nhập giá trị"
                                                    style="field-sizing: content"
                                                ></textarea>

                                                <input
                                                    *ngSwitchCase="CheckListType.BARCODE"
                                                    type="text"
                                                    pInputText
                                                    [disabled]="
                                                        !(approveDetail?.state === ApproveStatus.PENDING || approveDetail?.state === ApproveStatus.REJECT_PM) ||
                                                        objectTaskDetail[item.id].ignored
                                                    "
                                                    [(ngModel)]="objectTaskDetail[item.id].value"
                                                    placeholder="Nhập giá trị"
                                                />
                                                <input
                                                    *ngSwitchCase="CheckListType.QRCODE"
                                                    type="text"
                                                    pInputText
                                                    [disabled]="
                                                        !(approveDetail?.state === ApproveStatus.PENDING || approveDetail?.state === ApproveStatus.REJECT_PM) ||
                                                        objectTaskDetail[item.id].ignored
                                                    "
                                                    [(ngModel)]="objectTaskDetail[item.id].value"
                                                    placeholder="Nhập giá trị"
                                                />
                                                <p-dropdown
                                                    *ngSwitchCase="CheckListType.OPTION"
                                                    [options]="item.checklistDetail.jsonOption | jsonParse"
                                                    [disabled]="
                                                        !(approveDetail?.state === ApproveStatus.PENDING || approveDetail?.state === ApproveStatus.REJECT_PM) ||
                                                        objectTaskDetail[item.id].ignored
                                                    "
                                                    [(ngModel)]="objectTaskDetail[item.id].value"
                                                    optionLabel="label"
                                                    optionValue="value"
                                                    placeholder="Chọn giá trị"
                                                ></p-dropdown>
                                                <ng-container *ngSwitchCase="CheckListType.YES_NO">
                                                    <div *ngFor="let ra of item.checklistDetail.jsonOption | jsonParse" class="field-checkbox">
                                                        <p-radioButton
                                                            name="value"
                                                            [value]="ra.value"
                                                            [disabled]="
                                                                !(
                                                                    approveDetail?.state === ApproveStatus.PENDING ||
                                                                    approveDetail?.state === ApproveStatus.REJECT_PM
                                                                ) || objectTaskDetail[item.id].ignored
                                                            "
                                                            [inputId]="ra.value"
                                                            [(ngModel)]="objectTaskDetail[item.id].value"
                                                        ></p-radioButton>
                                                        <label [for]="ra.value" class="ml-2">{{ ra.label }}</label>
                                                    </div>
                                                </ng-container>

                                                <input
                                                    pInputText
                                                    *ngSwitchCase="CheckListType.NUMBER"
                                                    [min]="0"
                                                    [(ngModel)]="objectTaskDetail[item.id].value"
                                                    [disabled]="
                                                        !(approveDetail?.state === ApproveStatus.PENDING || approveDetail?.state === ApproveStatus.REJECT_PM) ||
                                                        objectTaskDetail[item.id].ignored
                                                    "
                                                    placeholder="Nhập số"
                                                />

                                                <p-inputNumber
                                                    *ngSwitchCase="CheckListType.ANGLE"
                                                    [min]="-360"
                                                    [max]="360"
                                                    [disabled]="
                                                        !(approveDetail?.state === ApproveStatus.PENDING || approveDetail?.state === ApproveStatus.REJECT_PM) ||
                                                        objectTaskDetail[item.id].ignored
                                                    "
                                                    [(ngModel)]="objectTaskDetail[item.id].value"
                                                    placeholder="Nhập góc độ"
                                                >
                                                </p-inputNumber>
                                            </div>
                                            <div
                                                *ngIf="
                                                    item.type !== CheckListType.IMAGE && !objectTaskDetail[item.id].ignored && !objectTaskDetail[item.id].value
                                                "
                                            >
                                                <span class="text-red-500">Trường này yêu cầu dữ liệu</span>
                                            </div>
                                        </div>
                                    </ng-container>
                                    <div *ngIf="item.type === CheckListType.IMAGE && !item.ignored" class="tw-col-span-10 border-top-1 tw-border-gray-400">
                                        <div class="tw-col-span-10 tw-p-2">
                                            <div
                                                class="tw-grid tw-grid-cols-1 tw-gap-4"
                                                [class]="item.imageTaskList && item.imageTaskList.length > 1 ? 'md:tw-grid-cols-2' : ''"
                                            >
                                                <ng-container *ngFor="let image of item.imageTaskList">
                                                    <!-- <p-image [src]="image.url" alt="Image" loading="lazy"></p-image> -->
                                                    <app-image-magnifier
                                                        [imageUrl]="image.url"
                                                        [remove]="
                                                            approveDetail?.state === ApproveStatus.PENDING || approveDetail?.state === ApproveStatus.REJECT_PM
                                                        "
                                                        (onRemove)="onRemove($event)"
                                                        [uploadBy]="image.uploadBy"
                                                        [key]="item"
                                                    ></app-image-magnifier>
                                                </ng-container>
                                            </div>
                                            <div class="tw-col-span-12" *ngIf="item.imageTaskList.length < item.checklistDetail?.numberImage">
                                                <app-upload
                                                    *ngIf="
                                                        !(approveDetail?.state === ApproveStatus.WAITING_PM || approveDetail?.state === ApproveStatus.COMPLETED)
                                                    "
                                                    [disabled]="objectTaskDetail[item.id].ignored"
                                                    type="image"
                                                    [multiple]="true"
                                                    [limit]="(item.checklistDetail.numberImage || 0) - item.imageTaskList.length"
                                                    [label]="'Thêm ảnh ' + item.imageTaskList.length + '/' + item.checklistDetail?.numberImage"
                                                    (onChange)="onUpload($event, item)"
                                                ></app-upload>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <p class="tw-col-span-1 tw-p-2 border-right-1 border-bottom-1" [ngClass]="{ 'border-top-1 ': i === 0 }">
                                <p-checkbox
                                    [(ngModel)]="objectTaskDetail[item.id].ignored"
                                    [inputId]="item.id"
                                    [binary]="true"
                                    [disabled]="!(approveDetail?.state === ApproveStatus.PENDING || approveDetail?.state === ApproveStatus.REJECT_PM)"
                                    (onChange)="setValueTaskDetail($event, item.id)"
                                ></p-checkbox>
                                <label class="tw-ml-2" [for]="item.id">N/A</label>
                            </p>
                        </div>
                    </ng-container>
                    <div *ngIf="objectTaskResponse[selectedNode?.key]">
                        <p-divider align="left">
                            <div class="inline-flex align-items-center">
                                <i class="pi pi-user mr-2"></i>
                                <b>Đội thi công phản hồi</b>
                            </div>
                        </p-divider>
                        <textarea
                            *ngIf="objectTaskResponse[selectedNode.key]"
                            rows="2"
                            [disabled]="true"
                            [readOnly]="true"
                            style="width: 100%"
                            pInputTextarea
                            [(ngModel)]="objectTaskResponse[selectedNode.key].employeeToSubPm"
                        ></textarea>

                        <p-divider align="left">
                            <div class="inline-flex align-items-center">
                                <i class="pi pi-user mr-2"></i>
                                <b>PM phản hồi</b>
                            </div>
                        </p-divider>
                        <textarea
                            *ngIf="objectTaskResponse[selectedNode.key]"
                            rows="2"
                            [disabled]="true"
                            [readOnly]="true"
                            style="width: 100%"
                            pInputTextarea
                            [(ngModel)]="objectTaskResponse[selectedNode.key].pmToSubPm"
                        ></textarea>

                        <p-divider align="left">
                            <div class="inline-flex align-items-center">
                                <i class="pi pi-user mr-2"></i>
                                <b>Gửi cho PM</b>
                            </div>
                        </p-divider>
                        <textarea
                            *ngIf="objectTaskResponse[selectedNode.key]"
                            rows="2"
                            style="width: 100%"
                            pInputTextarea
                            [disabled]="!(approveDetail?.state === ApproveStatus.PENDING || approveDetail?.state === ApproveStatus.REJECT_PM)"
                            [(ngModel)]="objectTaskResponse[selectedNode.key].subPmToPm"
                        ></textarea>
                        <p-divider align="left">
                            <div class="inline-flex align-items-center">
                                <i class="pi pi-user mr-2"></i>
                                <b>Gửi cho Đội thi công</b>
                            </div>
                        </p-divider>
                        <textarea
                            *ngIf="objectTaskResponse[selectedNode.key]"
                            rows="2"
                            style="width: 100%"
                            pInputTextarea
                            [disabled]="!(approveDetail?.state === ApproveStatus.PENDING || approveDetail?.state === ApproveStatus.REJECT_PM)"
                            [(ngModel)]="objectTaskResponse[selectedNode.key].subPmToEmployee"
                        ></textarea>
                        <span
                            class="text-red-500"
                            *ngIf="
                                objectTaskResponse[selectedNode.key]?.rate === 0 &&
                                (!objectTaskResponse[selectedNode.key].subPmToEmployee ||
                                    objectTaskResponse[selectedNode.key].subPmToEmployee.trim().length === 0) &&
                                approveDetail?.state === ApproveStatus.PENDING
                            "
                            >Trường này yêu cầu có dữ liệu</span
                        >
                        <br />
                        <div style="height: 80px"></div>
                        <div
                            class="tw-grid tw-grid-cols-3 p-5 tw-bg-slate-100 tw-shadow-lg tw-rounded-md border-1 border-blue-200"
                            style="position: fixed; bottom: 0; right: 1rem"
                        >
                            <p>Đánh giá</p>
                            <div class="flex align-items-center">
                                <p-radioButton
                                    *ngIf="objectTaskResponse[selectedNode.key]"
                                    name="rate"
                                    [value]="1"
                                    [(ngModel)]="objectTaskResponse[selectedNode.key].rate"
                                    inputId="good"
                                    [disabled]="!(approveDetail?.state === ApproveStatus.PENDING || approveDetail?.state === ApproveStatus.REJECT_PM)"
                                    (onClick)="rate(objectTaskResponse[selectedNode.key])"
                                ></p-radioButton>
                                <label for="good" class="ml-2">Tốt</label>
                            </div>

                            <div class="flex align-items-center">
                                <p-radioButton
                                    *ngIf="objectTaskResponse[selectedNode.key]"
                                    name="rate"
                                    [value]="0"
                                    [(ngModel)]="objectTaskResponse[selectedNode.key].rate"
                                    inputId="notGood"
                                    [disabled]="!(approveDetail?.state === ApproveStatus.PENDING || approveDetail?.state === ApproveStatus.REJECT_PM)"
                                    (onClick)="rate(objectTaskResponse[selectedNode.key])"
                                ></p-radioButton>
                                <label for="notGood" class="ml-2">Không tốt</label>
                            </div>
                        </div>
                    </div>
                </div>
            </p-panel>
        </div>
    </div>
</div>
