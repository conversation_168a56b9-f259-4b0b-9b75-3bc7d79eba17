import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { ErpManufacturer } from '../../../models/interface/sc';

@Injectable()
export class ErpManufacturerService extends BaseService<ErpManufacturer> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/erp-manufacturer');
    }
}
