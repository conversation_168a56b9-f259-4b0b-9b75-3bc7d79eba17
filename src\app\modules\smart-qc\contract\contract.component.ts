import { AfterViewInit, Component, OnInit, TemplateRef, ViewChild, inject } from '@angular/core';
import { RouterLink } from '@angular/router';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { Contract, ContractFilter } from 'src/app/models/interface/smart-qc';
import { TABLE_KEY } from 'src/app/models/constant';
import { Column, User } from 'src/app/models/interface';
import { ContractService } from 'src/app/services/smart-qc/masterdata/contract.service';
import { TableCommonService } from 'src/app/shared/table-module/table.common.service';
import { ButtonModule } from 'primeng/button';
import { FileUploadModule } from 'primeng/fileupload';
import { TagModule } from 'primeng/tag';
import { AuthService } from 'src/app/core/auth/auth.service';
import { CommonModule } from '@angular/common';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { listContractStatus } from '../../../models/constant/smart-qc';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';

@Component({
    selector: 'app-contract',
    standalone: true,
    templateUrl: './contract.component.html',
    imports: [
        RouterLink,
        ButtonModule,
        FileUploadModule,
        TagModule,
        CommonModule,
        HasAnyAuthorityDirective,
        TableCommonModule,
        SubHeaderComponent,
    ],
})
export class ContractComponent implements OnInit, AfterViewInit {
    @ViewChild('templateName') templateName: TemplateRef<Element>;
    @ViewChild('templateStatus') templateStatus: TemplateRef<Element>;

    contractService = inject(ContractService);
    authService = inject(AuthService);
    tableCommonService = inject(TableCommonService);
    state: QueryObserverBaseResult<Contract[]>;
    columns: Column[] = [];
    tableId: string = TABLE_KEY.CONTRACT;
    itemsHeader = [{ label: 'Quản lý dự án' }, { label: 'Danh sách dự án' }];
    optionStatus = listContractStatus;
    user: User;

    ngOnInit() {
        this.tableCommonService
            .init<Contract>({
                tableId: this.tableId,
                queryFn: (filter, body: ContractFilter) => this.contractService.getPageTableCustom(filter, body),
                configFilter: [
                    'ids',
                    'names',
                    'codes',
                    'statuses',
                    'totalAction',
                    'totalStation',
                    'totalSubPM',
                    'startTimeStart&startTimeEnd',
                    'endTimeStart&endTimeEnd',
                ],
            })
            .subscribe((state) => {
                this.state = state;
            });

        this.user = this.authService.userObserver.getValue();
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.columns = [
                { body: this.templateName, header: 'Tên dự án', default: true },
                { field: 'status', header: 'Trạng thái', body: this.templateStatus },
                {
                    field: 'totalAction',
                    header: 'Tổng số công việc',
                },
                { field: 'totalStation', header: 'Tổng số trạm' },
                {
                    field: 'totalSubPM',
                    header: 'Tổng số SubPM',
                    hide: !this.authService.isAdminOrPM(),
                },
                {
                    field: 'totalEmployee',
                    header: 'Tổng số thành viên',
                    hide: !this.authService.isSubPM(),
                },
                {
                    field: 'startTime',
                    header: 'Ngày bắt đầu',
                    type: 'date',
                    format: 'dd/MM/yyyy',
                },
                {
                    field: 'endTime',
                    header: 'Ngày kết thúc',
                    type: 'date',
                    format: 'dd/MM/yyyy',
                },
            ];
        }, 0);
    }
    deleteSelectedContract = (ids: number[]) => {
        return this.contractService.batchDelete(ids);
    };

    hiddenSelect = (e: Contract) => {
        return e.createdBy !== this.user.email;
    };

    rowSelectable = (rowData: Contract) => {
        return rowData.createdBy === this.user.email;
    };
}
