<p-fileUpload
    hidden
    #uploader
    mode="basic"
    name="files"
    [auto]="false"
    [customUpload]="true"
    [accept]="accept"
    [multiple]="multiple"
    [disabled]="disabled"
    chooseLabel=""
    chooseIcon="pi pi-upload"
    (onSelect)="handleSelect($event, uploader)"
></p-fileUpload>
<div class="upload-wrapper">
    <a *ngIf="!uploadedFileName" class="tw-cursor-pointer" style="display: flex; align-items: center" (click)="openFileDialog(uploader)">
        <span class="pi pi-cloud-upload" style="color: #3490dc; margin-right: 5px; font-size: x-large"></span>
        <span>Chọn file</span>
    </a>
    <!-- Tên file upload, nằm sát bên phải button và căn giữa -->
    <ng-container *ngIf="uploadedFileName">
        <i *ngIf="loading" class="pi pi-spin pi-spinner uploaded-spinner"></i>
        <a
            [autoHide]="false"
            *ngIf="filePath && !loading"
            [href]="downloadUrl"
            download
            class="uploaded-name"
            pTooltip="{{ uploadedFileName }}"
            tooltipPosition="top"
        >
            {{ uploadedFileName }}
        </a>
        <div *ngIf="!filePath || loading" class="uploaded-name" pTooltip="{{ uploadedFileName }}" tooltipPosition="top">
            {{ uploadedFileName }}
        </div>
    </ng-container>

    <div *ngIf="uploadedFileName && !disabled" class="">
        <i class="pi pi-times delete-icon" (click)="clearUpload(uploader)" title="Xóa file"></i>
    </div>
</div>
