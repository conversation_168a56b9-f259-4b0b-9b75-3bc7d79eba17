import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { AbstractControl, FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Calendar, CalendarModule } from 'primeng/calendar';
import { InputTextModule } from 'primeng/inputtext';
import { Ng<PERSON>lass, NgForOf, NgIf, NgStyle } from '@angular/common';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { InputValidationComponent } from '../components/input-validation/input.validation.component';
import { InputNumberModule } from 'primeng/inputnumber';
import { ValidateMessage } from '../../models/interface';

@Component({
    selector: 'app-editable-input',
    standalone: true,
    templateUrl: './editable-input.component.html',
    styles: [
        `
            :host {
                display: block;
            }

            .cursor-pointer {
                cursor: pointer;
            }
        `,
    ],
    imports: [
        CalendarModule,
        ReactiveFormsModule,
        InputTextModule,
        NgIf,
        InputTextareaModule,
        InputValidationComponent,
        InputNumberModule,
        NgForOf,
        NgClass,
        NgStyle,
    ],
})
export class EditableInputComponent implements OnInit {
    oldValue: any;
    @Input() control!: AbstractControl<any, any>; // Nhận vào FormControl
    @Input() data: any; // Nếu không có control, sẽ dùng data để khởi tạo
    @Input() placeholder: string = 'Nhập nội dung'; // Placeholder mặc định
    @Input() type: 'input' | 'input-number' | 'textarea' | 'calendar' = 'input'; // Kiểu hiển thị
    @Input() dateFormat: string = 'dd/mm/yy'; // Định dạng ngày tháng cho p-calendar
    @Output() save = new EventEmitter<string>(); // Sự kiện bắn ra khi lưu
    @Input() trim: boolean = false;
    @Input() inTable: boolean = false;
    @Input() fieldName: string;
    @Input('validateMessage') validateMessages: ValidateMessage[];
    @Input('class') class;
    @Input('style') style;
    @Input('pattern') pattern: string;
    typeValidate: string;

    internalForm!: FormGroup; // Tạo FormGroup nội bộ nếu không có control

    // Element
    @ViewChild('inputElement') inputElement!: ElementRef;
    @ViewChild('calendar', { static: false }) calendar!: Calendar;
    // End Element

    isEditing = false; // Trạng thái chỉnh sửa

    isReadonly = true; // Mặc định không chỉnh sửa được

    ngOnInit() {
        /*if (this.type === 'calendar') {
            this.control.valueChanges.subscribe(value => {
                if (value) {
                    this.save.emit(value);
                }
                console.log(value);
            });
        }*/
        if (!this.control) {
            this.internalForm = new FormGroup({
                internalControl: new FormControl(this.data ?? ''), // Dùng internalControl nếu không có control truyền vào
            });
            this.control = this.internalForm.get('internalControl')!;
        }

        this.oldValue = this.control?.value;

        if (this.type === 'input-number') {
            this.typeValidate = 'number';
        } else {
            this.typeValidate = 'text';
        }
    }

    startEditing() {
        this.isReadonly = false;

        setTimeout(() => {
            if (this.type === 'input' || this.type === 'textarea') {
                if (this.inputElement?.nativeElement) {
                    const el = this.inputElement.nativeElement;
                    el.focus();
                    el.setSelectionRange(el.value.length, el.value.length);
                }
            } else if (this.type === 'calendar') {
                setTimeout(() => {
                    // Thêm setTimeout nữa để đảm bảo p-calendar đã render
                    /*console.log(this.calendar)
                    console.log(this.calendar.overlayVisible)*/
                    if (this.calendar) {
                        this.calendar.toggle();
                    }
                });
            }
        });
    }

    saveValue() {
        if (this.control?.errors) {
            this.control.markAsTouched();
            this.control.markAsDirty();
            return;
        }
        if (this.trim && this.control?.value != null && (this.type === 'input' || this.type === 'textarea')) {
            this.control.setValue(this.control.value.trim());
        }
        if (this.oldValue !== this.control?.value && !this.isReadonly) {
            this.isReadonly = true; // Khóa lại khi mất focus
            this.save.emit(this.control.value);
            this.oldValue = this.control.value;
        } else {
            this.isReadonly = true; // Khóa lại khi mất focus
        }

        // console.log(this.control.value)
    }

    formatDate(date: any): string {
        if (!date) return '';
        const d = new Date(date);
        return d.toLocaleDateString('vi-VN'); // Format dd/mm/yyyy
    }

    onKeyPress(event: KeyboardEvent) {
        if (this.inTable) {
            event.stopPropagation();
        }
    }
}
