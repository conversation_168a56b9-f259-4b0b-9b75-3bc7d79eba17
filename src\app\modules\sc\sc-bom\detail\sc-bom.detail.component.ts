import { Component, inject, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormArray, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { ChipModule } from 'primeng/chip';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { PaginatorModule } from 'primeng/paginator';
import { PanelModule } from 'primeng/panel';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';
import { Bom, BomItem, BomItemDay, LogLevel } from '../../../../models/interface/sc';
import { ApiResponse } from '../../../../models/interface';
import { ApproveService } from '../../../../services/smart-qc/masterdata/approve.service';
import { BomService } from '../../../../services/sc/sc-bom/bom.service';
import { PoService } from '../../../../services/sc/po/po.service';
import { LoadingService } from '../../../../shared/services/loading.service';
import { AlertService } from '../../../../shared/services/alert.service';
import { TableCommonService } from '../../../../shared/table-module/table.common.service';
import { SubHeaderComponent } from '../../../../shared/components/sub-header/sub-header.component';
import { ButtonGroupFileComponent } from '../../../../shared/components/button-group-file/button-group-file.component';
import { HasAnyAuthorityDirective } from '../../../../shared/directives/has-any-authority.directive';
import { FormCustomModule } from '../../../../shared/form-module/form.custom.module';
import { TableCommonModule } from '../../../../shared/table-module/table.common.module';
import { InputNumberComponent } from '../../../../shared/components/inputnumber/inputnumber.component';
import { FormComponent } from '../../../../shared/form-module/form-base/form.component';

interface StatusOption {
    label: string;
    value: number;
}

interface StatusMap {
    [key: number]: string;
}

interface StatusSeverityMap {
    [key: number]: string;
}

interface DataApiResponse {
    bom: Bom;
    bomItems: BomItem[];
    warning: Record<string, string> | null;
    logLevels: LogLevel[];
}

@Component({
    selector: 'app-sc-bom',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        RouterModule,
        ButtonModule,
        ChipModule,
        DialogModule,
        DropdownModule,
        InputTextModule,
        InputTextareaModule,
        PaginatorModule,
        PanelModule,
        TableModule,
        TagModule,
        TooltipModule,
        FormCustomModule,
        TableCommonModule,
        SubHeaderComponent,
        ButtonGroupFileComponent,
        HasAnyAuthorityDirective,
        InputNumberComponent,
    ],
    providers: [ApproveService, TableCommonService, PoService, BomService],
    templateUrl: './sc-bom.detail.component.html',
    styleUrls: ['./sc-bom.detail.component.scss'],
})
export class ScBomDetailComponent implements OnInit {
    private bomService = inject(BomService);
    private loadingService = inject(LoadingService);
    private alertService = inject(AlertService);
    private formBuilder = inject(FormBuilder);
    private activatedRoute = inject(ActivatedRoute);
    private router = inject(Router);

    @ViewChild('form') form: TemplateRef<FormComponent>;

    isLoading = true;
    bomId: number | null = null;
    scBomOld: Bom | null = null;
    scBomForm: FormGroup;
    filterForm: FormGroup;
    urlError: string | null = null;
    uniqueDates: number[] = [];
    bomItemDayMap = new Map<string, number>();
    warning: Record<string, string> | null = null;
    warningEntries: { level: string; error: string }[] = [];
    isOpenConfirmModal = false;
    isOpenLogLevel = false;
    levelRowspanMap: { [key: string]: { rowspan: number; firstIndex: number } } = {};
    filteredBomItems: BomItem[] = [];
    logLevels: LogLevel[] = [];

    readonly statusOptions: StatusOption[] = [
        { label: 'Chưa tạo đơn', value: 0 },
        { label: 'Đã tạo đơn nháp', value: 1 },
        { label: 'Đã duyệt đơn', value: 2 },
    ];

    readonly mapStatus: StatusMap = {
        0: 'Chưa tạo đơn',
        1: 'Đã tạo đơn nháp',
        2: 'Đã duyệt đơn',
    };

    readonly mapStatusSeverity: StatusSeverityMap = {
        0: 'secondary',
        1: 'primary',
        2: 'success',
    };

    ngOnInit(): void {
        this.initForms();
        this.activatedRoute.paramMap.subscribe(() => this.loadData());
    }

    private initForms(): void {
        this.scBomForm = this.formBuilder.group({
            id: [{ value: null, disabled: false }],
            code: ['', Validators.required],
            accountingCode: ['', Validators.required],
            rdBomId: [null],
            rdBom: [null],
            updateItem: [false],
            note: [''],
            bomItems: this.formBuilder.array([]),
            attachmentId: [null],
            bomCost: [null],
            eBom: [null],
            mBom: [null],
            pBom: [null],
        });

        this.filterForm = this.formBuilder.group({
            internalReference: [''],
            manPn: [''],
            productDescription: [''],
            manufacturer: [''],
            supplierShortName: [''],
            contractNumber: [''],
            statusInternalReference: [null],
        });
    }

    private loadData(): void {
        const idString = this.activatedRoute.snapshot.paramMap.get('id');
        this.bomId = idString ? Number(idString) : null;

        if (this.bomId) {
            this.loadingService.show();
            this.bomService.getOne(this.bomId).subscribe({
                next: (res) => {
                    this.scBomOld = res.body;
                    this.isLoading = false;
                    this.updateFormWithData(res.body);
                    this.filterData();
                    this.logLevels = res.body.logLevels || [];
                    this.preprocessLogLevels();
                    this.loadingService.hide();
                },
                error: () => {
                    this.loadingService.hide();
                    this.alertService.error('Lỗi', 'Không thể truy cập thông tin');
                    this.router.navigate(['/sc/bom']);
                    this.isLoading = false;
                },
            });
        } else {
            this.isLoading = false;
            this.filterData();
        }
    }

    private updateFormWithData(bom: Bom): void {
        this.scBomForm.patchValue({
            id: bom.id,
            code: bom.code,
            accountingCode: bom.accountingCode,
            rdBomId: bom.rdBomId,
            rdBom: bom.rdBom,
            updateItem: bom.updateItem,
            note: bom.note,
            bomCost: bom.bomCost,
            eBom: bom.eBom,
            mBom: bom.mBom,
            pBom: bom.pBom,
        });

        if (bom.status === 1) {
            this.scBomForm.disable();
        }

        const bomItemsFormArray = this.scBomForm.get('bomItems') as FormArray;
        bomItemsFormArray.clear();
        this.bomItemDayMap = new Map<string, number>();

        bom.bomItems?.forEach((item, index) => {
            const itemFormGroup = this.createItemFormGroup(item, index);
            bomItemsFormArray.push(itemFormGroup);
            item.bomItemDays?.forEach((day) => {
                this.bomItemDayMap.set(`${index}-${day.date}`, day.quantity);
            });
        });

        this.uniqueDates = Array.from(new Set(bom.bomItems?.flatMap((item) => item.bomItemDays?.map((day) => day.date) || []) || [])).sort((a, b) => a - b);
    }

    private createItemFormGroup(item: BomItem, key: number): FormGroup {
        return this.formBuilder.group({
            key: [key],
            id: [item?.id ?? null],
            bomId: [item?.bomId ?? null],
            level: [item?.level ?? null],
            qtyPerProduct: [item?.qtyPerProduct ?? null],
            attritionRate: [item?.attritionRate ?? null],
            requiredQuantity: [item?.requiredQuantity ?? null],
            type: [item?.type ?? null],
            availableQuantity: [item?.availableQuantity ?? null],
            orderedQuantity: [item?.orderedQuantity ?? null],
            internalReference: [item?.internalReference ?? null],
            manPn: [item?.manPn ?? null],
            productDescription: [item?.productDescription ?? null],
            manufacturerId: [item?.manufacturerId ?? null],
            manufacturer: [item?.manufacturer ?? null],
            estimatedPrice: [item?.estimatedPrice ?? null],
            usdAskPrice: [item?.usdAskPrice ?? null],
            vndAskPrice: [item?.vndAskPrice ?? null],
            usdPrice: [item?.usdPrice ?? null],
            vndPrice: [item?.vndPrice ?? null],
            priceDifferenceAsk: [item?.priceDifferenceAsk ?? null],
            priceDifference: [item?.priceDifference ?? null],
            spq: [item?.spq ?? null],
            moq: [item?.moq ?? null],
            deliveryTime: [item?.deliveryTime ?? null],
            finalOrderedQuantity: [item?.finalOrderedQuantity ?? null],
            foc: [item?.foc ?? null],
            usdValue: [item?.usdValue ?? null],
            vndValue: [item?.vndValue ?? null],
            excessQuantity: [item?.excessQuantity ?? null],
            excessValue: [item?.excessValue ?? null],
            valueDifference: [item?.valueDifference ?? null],
            deliveryCondition: [item?.deliveryCondition ?? null],
            supplierId: [item?.supplierId ?? null],
            supplierShortName: [item?.supplierShortName ?? null],
            supplierCode: [item?.supplierCode ?? null],
            contractNumber: [item?.contractNumber ?? null],
            note: [item?.note ?? null],
            bomItemDays: [item?.bomItemDays ?? null],
            statusInternalReference: [item?.statusInternalReference ?? null],
        });
    }

    get bomItems(): FormArray {
        return this.scBomForm.get('bomItems') as FormArray;
    }

    onSubmitCreate(): void {
        if (this.warning) {
            this.isOpenConfirmModal = true;
        } else {
            this.createBomData();
        }
    }

    createBomData(): void {
        const bomData = this.scBomForm.getRawValue();
        this.loadingService.show();

        const request = this.scBomOld ? { ...this.scBomOld, ...bomData, logLevels: this.logLevels } : { ...bomData, logLevels: this.logLevels };
        const action = this.scBomOld?.id ? this.bomService.update(request) : this.bomService.create(request);

        action.subscribe({
            next: (res) => {
                this.alertService.success('Thành công');
                this.loadingService.hide();
                this.isOpenConfirmModal = false;
                if (!this.scBomOld?.id) {
                    this.router.navigate(['/sc/bom', res.body.id]);
                }
            },
            error: () => {
                this.loadingService.hide();
                this.alertService.error('Lỗi', 'Không thể lưu thông tin');
            },
        });
    }

    handleImportFile(file: File): void {
        this.loadingService.show();
        this.bomService.import(file, this.scBomForm.get('rdBomId')?.value).subscribe({
            next: (res: ApiResponse) => {
                this.loadingService.hide();
                const data = res.data as DataApiResponse;
                if (res.code === 1) {
                    this.updateScBomForm(res);
                    this.alertService.success('Thành công');
                    this.scBomForm.patchValue({ updateItem: true });
                    this.logLevels = data.logLevels || [];
                } else {
                    this.alertService.error('Tồn tại một số bản ghi lỗi trong file, vui lòng kiểm tra lại');
                    this.urlError = res.message;
                    this.updateScBomForm(res);
                    this.logLevels = [];
                }
                this.preprocessLogLevels();
            },
            error: (error) => {
                this.loadingService.hide();
                this.alertService.handleError(error);
            },
        });
    }

    private updateScBomForm(res: ApiResponse): void {
        if (!res.data) return;

        const dataImport = res.data as DataApiResponse;

        this.scBomForm.patchValue({
            attachmentId: res.attachment?.id,
            bomCost: dataImport?.bom?.bomCost,
            eBom: dataImport?.bom?.eBom,
            mBom: dataImport?.bom?.mBom,
            pBom: dataImport?.bom?.pBom,
        });

        const bomItemsFormArray = this.scBomForm.get('bomItems') as FormArray;
        bomItemsFormArray.clear();
        this.bomItemDayMap = new Map<string, number>();

        if (this.scBomOld) {
            this.scBomOld.bomItems = dataImport.bomItems;
        }

        dataImport.bomItems?.forEach((item: BomItem, index: number) => {
            const itemFormGroup = this.createItemFormGroup(item, index);
            bomItemsFormArray.push(itemFormGroup);
            item.bomItemDays?.forEach((day: BomItemDay) => {
                this.bomItemDayMap.set(`${index}-${day.date}`, day.quantity);
            });
        });

        this.uniqueDates = Array.from(
            new Set(dataImport.bomItems?.flatMap((item: BomItem) => item.bomItemDays?.map((day: BomItemDay) => day.date) || []) || []),
        ).sort((a, b) => a - b);

        this.warning = dataImport.warning && Object.keys(dataImport.warning).length > 0 ? dataImport.warning : null;
        if (this.warning) {
            this.warningEntries = Object.entries(this.warning).map(([level, error]) => ({ level, error }));
        }

        this.filterData();
    }

    getQuantity(item: BomItem, date: number): number {
        return this.bomItemDayMap.get(`${item.key}-${date}`) || 0;
    }

    handleChangeRdBom(data): void {
        if (data?.objects?.length > 0) {
            this.scBomForm.patchValue({ rdBom: data.objects[0].name });
        }
    }

    filterData(): void {
        const filterValues = this.filterForm.getRawValue();
        this.filteredBomItems = this.bomItems.getRawValue().filter((item: BomItem) => {
            return (
                (!filterValues.internalReference || item.internalReference === filterValues.internalReference) &&
                (!filterValues.manPn || item.manPn === filterValues.manPn) &&
                (!filterValues.productDescription || item.productDescription?.toLowerCase().includes(filterValues.productDescription.toLowerCase())) &&
                (!filterValues.manufacturer || item.manufacturer?.toLowerCase().includes(filterValues.manufacturer.toLowerCase())) &&
                (!filterValues.supplierShortName || item.supplierShortName?.toLowerCase().includes(filterValues.supplierShortName.toLowerCase())) &&
                (!filterValues.contractNumber || item.contractNumber?.toLowerCase().includes(filterValues.contractNumber.toLowerCase())) &&
                (filterValues.statusInternalReference === null || item.statusInternalReference === filterValues.statusInternalReference)
            );
        });

        this.uniqueDates = Array.from(
            new Set(this.filteredBomItems.flatMap((item: BomItem) => item.bomItemDays?.map((day: BomItemDay) => day.date) || [])),
        ).sort((a, b) => a - b);
    }

    preprocessLogLevels(): void {
        this.levelRowspanMap = {};

        this.logLevels
            .sort((a, b) => a.level.localeCompare(b.level))
            .forEach((item, index) => {
                this.levelRowspanMap[item.level] = this.levelRowspanMap[item.level] || { rowspan: 0, firstIndex: index };
                this.levelRowspanMap[item.level].rowspan++;
            });
    }

    isFirstOccurrence(level: string, index: number): boolean {
        return this.levelRowspanMap[level]?.firstIndex === index;
    }

    getRowspan(level: string): number {
        return this.levelRowspanMap[level]?.rowspan || 1;
    }
}
