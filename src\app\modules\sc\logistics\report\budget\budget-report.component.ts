import { Component } from '@angular/core';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { LoadingService } from '../../../../../shared/services/loading.service';
import { TableModule } from 'primeng/table';
import { CalendarModule } from 'primeng/calendar';
import { finalize } from 'rxjs';
import { AuthService } from '../../../../../core/auth/auth.service';
import Common from '../../../../../utils/common';
import { DialogModule } from 'primeng/dialog';
import { environment } from '../../../../../../environments/environment';
import { EventChangeFilter } from '../../../../../models/interface';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { TabViewModule } from 'primeng/tabview';
import { NgIf } from '@angular/common';
import { LogisticsReportService } from 'src/app/services/sc/logistics/logistics-report.service';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import {DateUtils} from "../../../../../utils/date-utils";

@Component({
    selector: 'budget-report',
    templateUrl: './budget-report.component.html',
    standalone: true,
    imports: [
        NgIf,
        DropdownModule,
        TabViewModule,
        InputTextModule,
        InputTextareaModule,
        ReactiveFormsModule,
        ButtonModule,
        TableModule,
        CalendarModule,
        DialogModule,
        TableCommonModule,
        SubHeaderComponent,
        InputTextModule,
        HasAnyAuthorityDirective,
        FormsModule,
    ],
    providers: [LogisticsReportService],
})
export class ReportComponent {
    reportForm: FormGroup;
    data: any = [];
    itemsHeader = [{ label: 'Báo cáo thống kê' }, { label: 'Báo cáo ngân sách', url: '' }];

    showReport: boolean = false;
    isDisplayDistrict: boolean = false;
    reportDate: string;
    isOpenModalExport: boolean = false;
    downloadExportFileLink: string = '';
    activeIndex = 0;
    constructor(
        private formBuilder: FormBuilder,
        private logisticsReportService: LogisticsReportService,
        private loadingService: LoadingService,
        protected authService: AuthService,
    ) {
        this.initReportForm();
    }

    initReportForm() {
        const currentDate = new Date();
        const previousMonthDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1); // Lùi 1 tháng, lấy ngày 1
        const timestampHCM = DateUtils.convertToTimestampHCM(previousMonthDate);

        this.reportForm = this.formBuilder.group({
            departmentId: [null, []],
            sccBudget: [null, []],
            otherBudget: [null, []],
            time: [new Date(timestampHCM), []],
            note: [null, []],
            type: [null, []],
        });

        this.getReport();
    }

    selectDate(data: EventChangeFilter) {
        this.getReport();
    }

    budgetMap = [
        'Chi phí Logistics',
        'Thuế nộp NSNN',
        'Chi phí vận chuyển',
        'Chi phí bảo hiểm',
        'CP vận chuyển - ',
        'CP CPN - ',
        'CP nội địa, lẻ khác',
        'Bảo hiểm ',
    ];

    getReport() {
        if (this.reportForm.get('time').value) {
            let time: number = Common.getTimeFromHCMTimeZone(this.reportForm.get('time').value);

            this.loadingService.show();
            this.logisticsReportService
                .getBudgetReport(time)
                .pipe(
                    finalize(() => {
                        this.loadingService.hide();
                    }),
                )
                .subscribe({
                    next: (res) => {
                        this.data = res;

                        this.data.forEach((e) => {
                            e.displayName = this.budgetMap[e.indexDisplay] + (e.logisticName != null ? e.logisticName : '');
                        });
                        console.log(this.data);
                    },
                    complete: () => {},
                });
        } else {
            Common.markAllAsTouchedForm(this.reportForm);
        }
    }

    export() {
        if (this.reportForm.get('time').value) {
            let time: number = Common.getTimeFromHCMTimeZone(this.reportForm.get('time').value);

            this.loadingService.show();
            this.logisticsReportService
                .exportBudgetReport(time)
                .pipe(
                    finalize(() => {
                        this.loadingService.hide();
                    }),
                )
                .subscribe({
                    next: (res: unknown) => {
                        this.isOpenModalExport = true;
                        this.downloadExportFileLink = environment.HOST_GW + '/sc/api/download?filePath=' + res['url'];
                    },
                    error: () => {},
                    complete: () => {},
                });
        }
    }

    editedReport = null;
    editedField = null;
    valueType = ['LOGISTIC', 'TAX', 'SHIPPING', 'INSURANCE_TOTAL'];
    styleList = {
        LOGISTIC: 'tw-bg-orange-200',
        TAX: 'tw-bg-orange-100',
        SHIPPING: 'tw-bg-orange-100',
        INSURANCE_TOTAL: 'tw-bg-orange-100',
    };
    editableType = ['TAX'];
    originalValue = null;
    isEditable = (type) => {
        return this.editableType.includes(type);
    };

    setEditedField = (report, id, currentValue) => {
        if (!this.authService.hasAuthority(['ROLE_SYSTEM_ADMIN', 'sc_report_logistic_edit'])) return;
        this.originalValue = currentValue;
        this.editedReport = report;
        this.editedField = id;
        setTimeout(() => {
            const input = document.getElementById(id) as HTMLInputElement;
            input?.focus();
        }, 0);
    };

    typeMap = new Map([
        ['LOGISTIC', 0],
        ['TAX', 1],
        ['SHIPPING', 2],
        ['INSURANCE_TOTAL', 3],
        ['LOCAL_FEE', 4],
    ]);

    saveReportProcess = (report, newValue) => {
        let type = report.logisticId ? null : this.typeMap.get(report.type);
        console.log(newValue);

        let date: number = Common.getTimeFromHCMTimeZone(this.reportForm.get('time').value);
        if (this.originalValue != newValue) {
            this.loadingService.show();
            this.logisticsReportService
                .updateMonthlyProcess({ date: date, type: type, process: newValue, logisticId: report.logisticId })
                .pipe(
                    // finalize(() => {
                    //     this.loadingService.hide();
                    // }),
                )
                .subscribe({
                    next: (res: unknown) => {
                        this.getReport();
                    },
                    error: () => {
                        report.process = this.originalValue;
                        this.loadingService.hide();
                    },
                    complete: () => {},
                });
        }
    };

    saveReportNote = (report, newValue) => {
        let type = report.logisticId ? null : this.typeMap.get(report.type);
        let date: number = Common.getTimeFromHCMTimeZone(this.reportForm.get('time').value);
        if (this.originalValue != newValue) {
            this.loadingService.show();
            this.logisticsReportService
                .updateMonthlyNote({ date: date, logisticId: report.logisticId, note: report.note, type: type })
                .pipe(
                    // finalize(() => {
                    //     this.loadingService.hide();
                    // }),
                )
                .subscribe({
                    next: (res: unknown) => {
                        this.getReport();
                    },
                    error: () => {
                        report.note = this.originalValue;
                        this.loadingService.hide();
                    },
                    complete: () => {},
                });
        }
    };

    saveReportTax = (report, newValue, department) => {
        let type = this.editableType.indexOf(report.type);
        if (department == 0) report.sccBudget = this.originalValue + 0.0;
        else if (department == 1) report.otherBudget = this.originalValue + 0.0;
        let date: number = Common.getTimeFromHCMTimeZone(this.reportForm.get('time').value);
        if (this.originalValue != newValue) {
            this.loadingService.show();
            this.logisticsReportService
                .updateMonthlyTax({ date: date, tax: newValue, department: department })
                .pipe(
                    // finalize(() => {
                    //     this.loadingService.hide();
                    // }),
                )
                .subscribe({
                    next: (res: unknown) => {
                        this.getReport();
                    },
                    error: () => {
                        if (department == 0) report.sccBudget = this.originalValue;
                        else if (department == 1) report.otherBudget = this.originalValue;
                        this.loadingService.hide();
                    },
                    complete: () => {},
                });
        }
    };
}
