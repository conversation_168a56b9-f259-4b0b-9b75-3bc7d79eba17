import { CommonModule } from '@angular/common';
import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { TableModule } from 'primeng/table';
import {ApiResponse, User} from 'src/app/models/interface';
import {
    Po,
    PoBoq, PoBoqDeliver,
    PoBoqProjectQuantity,
    PoBoqProjectValue,
    PoBoQuantity,
} from 'src/app/models/interface/sc';
import { PoService } from 'src/app/services/sc/po/po.service';
import { ButtonGroupFileComponent } from 'src/app/shared/components/button-group-file/button-group-file.component';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { AlertService } from 'src/app/shared/services/alert.service';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { PO_STATE_CONSTANT } from '../../../../models/constant/sc';
import {FormBuilder, FormGroup, Validators, FormArray, AbstractControl} from '@angular/forms';
import {EditableInputComponent} from "../../../../shared/edit/editable-input.component";
import {AuthService} from "../../../../core/auth/auth.service";

@Component({
    selector: 'app-po-boq',
    templateUrl: './po-boq.component.html',
    styleUrls: ['./po-boq.component.scss'],
    standalone: true,
    imports: [TableModule, ButtonGroupFileComponent, CommonModule, HasAnyAuthorityDirective, EditableInputComponent],
    providers: [PoService],
})
export class PoBOQComponent implements OnInit, OnChanges {
    @Input() po: Po;
    poBoqQuantityFirst: PoBoQuantity;
    urlError: string;
    PO_STATE_CONSTANT = PO_STATE_CONSTANT;

    // Form
    poForm: FormGroup;
    // End form
    user: User = { email: '' };
    hasViewPriceRole: boolean = false;

    constructor(
        private loadingService: LoadingService,
        private alertService: AlertService,
        private poService: PoService,
        private fb: FormBuilder,
        public auth: AuthService,
    ) {
        this.auth.userObserver.subscribe((data) => {
            this.user = data;
            if (this.user.authorities && (this.user.authorities.includes('ROLE_SYSTEM_ADMIN') || this.user.authorities.includes('sc_view_price'))) {
                this.hasViewPriceRole = true;
                console.log("Can view price");
            }
        });
    }

    ngOnInit(): void {
        if (!this.po.poBoqQuantityLst) {
            this.sortProjectDeliver();
            this.po.poBoqQuantityLst = [];
        }
        this.initForm();
    }

    ngOnChanges(changes: SimpleChanges): void {
        // console.log(changes['po'].currentValue)
        if (changes['po'].currentValue) {
            this.sortProjectDeliver();
            this.poBoqQuantityFirst = changes['po'].currentValue.poBoqQuantityLst?.length > 0 ? changes['po'].currentValue.poBoqQuantityLst[0] : null;
            this.initForm();
        }
    }

    private sortProjectDeliver() {
        this.po?.poBoqQuantityLst?.forEach((item: PoBoQuantity) => {
            if (Array.isArray(item.projectDeliver)) {
                item.projectDeliver.sort((a: PoBoqDeliver, b: PoBoqDeliver) => a.date - b.date);
            }
        });
    }

    private initForm(): void {
        this.poForm = this.fb.group({
            id: [this.po.id ?? null],
            orderNo: [this.po.orderNo ?? null],
            orderDate: [this.po.orderDate ?? null],
            orderDateCustom: [this.po.orderDateCustom ?? null],
            supplierId: [this.po.supplierId ?? null],
            supplierName: [this.po.supplierName ?? null],
            imputation: [this.po.imputation ?? null],
            requestNo: [this.po.requestNo ?? null],
            supplierNo: [this.po.supplierNo ?? null],
            updateBoq: [this.po.updateBoq ?? false],
            boqAttachment: [this.po.boqAttachment ?? null],
            unitPrice: [this.po.unitPrice ?? null],
            removeAttachments: [this.po.removeAttachments ?? []],
            poDetails: [this.po.poDetails ?? []],
            state: [this.po.state ?? null],
            totalValue: [this.po.totalValue ?? null],
            invNumberTransferByDate: [this.po.invNumberTransferByDate ?? {}],
            accountingCode: [this.po.accountingCode ?? null],
            poBoqQuantityLst: this.fb.array(this.initBoqQuantityArray()),
            poDetailDays: [this.po.poDetailDays ?? []],
            detailDaysMap: [this.po.detailDaysMap ?? null],
            deliveredQuantityMap: [this.po.deliveredQuantityMap ?? null],
            internalReferenceByLevelMap: [this.po.internalReferenceByLevelMap ?? null],
        });
    }

    private initBoqQuantityForm() {
        this.poForm.setControl('poBoqQuantities', this.fb.array(this.initBoqQuantityArray()));
    }

    private initBoqQuantityArray(): FormGroup[] {
        return (this.po.poBoqQuantityLst ?? []).map(item => this.createBoqQuantityFb(item));
    }

    createBoqQuantityFb(poBoQuantity: PoBoQuantity): FormGroup {
        return this.fb.group({
            indexLevel: [poBoQuantity?.indexLevel ?? null],
            estimatedPrice: [poBoQuantity?.estimatedPrice ?? null],
            requestQuantity: [poBoQuantity?.requestQuantity ?? null],
            availableQuantity: [poBoQuantity?.availableQuantity ?? null],
            spq: [poBoQuantity?.spq ?? null],
            moq: [poBoQuantity?.moq ?? null],
            unitQuantity: [poBoQuantity?.unitQuantity ?? null],
            orderQuantity: [{ value: poBoQuantity?.orderQuantity ?? null, disabled: this.po.state === PO_STATE_CONSTANT.COMPLETED }],
            orderPrice: [poBoQuantity?.orderPrice ?? null],
            unitPrice: [poBoQuantity?.unitPrice ?? null],
            excessQuantity: [poBoQuantity?.excessQuantity ?? null],
            excessMoney: [poBoQuantity?.excessMoney ?? null],
            deliveryTime: [poBoQuantity?.deliveryTime ?? null],
            unitDeliveryTime: [poBoQuantity?.unitDeliveryTime ?? null],
            totalValue: [poBoQuantity?.totalValue ?? null],
            note: [poBoQuantity?.note ?? null],
            orderDate: [poBoQuantity?.orderDate ?? null],
            poBoqList: this.fb.array(this.initBoqArray(poBoQuantity.poBoqList)),
            projectQuantities: this.fb.array(this.initProjectQuantityArray(poBoQuantity?.indexLevel, poBoQuantity.projectQuantities)),
            projectValue: this.fb.array(this.initProjectValueArray(poBoQuantity.projectValue)),
            projectDeliver: this.fb.array(this.initProjectDeliverArray(poBoQuantity.projectDeliver)),
        });
    }

    initBoqArray(poBoqList: PoBoq[]): FormGroup[] {
        return (poBoqList ?? []).map(item => this.createBoqFb(item));
    }

    initProjectQuantityArray(indexLevel, projectQuantities: PoBoqProjectQuantity[]): FormGroup[] {
        return (projectQuantities ?? []).map(item => this.createProjectQuantityFb(indexLevel, item));
    }

    initProjectValueArray(poBoqProjectValues: PoBoqProjectValue[]): FormGroup[] {
        return (poBoqProjectValues ?? []).map(item => this.createProjectValueFb(item));
    }

    initProjectDeliverArray(poBoqDelivers: PoBoqDeliver[]): FormGroup[] {
        return (poBoqDelivers ?? []).map(item => this.createProjectDeliverFb(item));
    }

    createBoqFb(poBoq: PoBoq): FormGroup {
        return this.fb.group({
            indexLevel: [poBoq?.indexLevel ?? null],
            productName: [poBoq?.productName ?? null],
            manufacturerCode: [poBoq?.manufacturerCode ?? null],
            description: [poBoq?.description ?? null],
            manufacturer: [poBoq?.manufacturer ?? null],
            estimatedPrice: [poBoq?.estimatedPrice ?? null],
            requestQuantity: [poBoq?.requestQuantity ?? null],
            spq: [poBoq?.spq ?? null],
            moq: [poBoq?.moq ?? null],
            unitQuantity: [poBoq?.unitQuantity ?? null],
            orderQuantity: [poBoq?.orderQuantity ?? null],
            orderPrice: [poBoq?.orderPrice ?? null],
            unitPrice: [poBoq?.unitPrice ?? null],
            excessQuantity: [poBoq?.excessQuantity ?? null],
            excessMoney: [poBoq?.excessMoney ?? null],
            deliveryTime: [poBoq?.deliveryTime ?? null],
            totalValue: [poBoq?.totalValue ?? null],
            note: [poBoq?.note ?? null],
        });
    }

    createProjectQuantityFb(indexLevel, poBoqProjectQuantity: PoBoqProjectQuantity): FormGroup {
        const key = indexLevel + '-' + poBoqProjectQuantity.name;
        const minQuantity = this.po?.deliveredQuantityMap?.[key] ?? 0;
        return this.fb.group({
            name: [poBoqProjectQuantity?.name ?? null],
            quantity: [{ value: poBoqProjectQuantity?.quantity ?? null, disabled: this.po.state === PO_STATE_CONSTANT.COMPLETED }, [Validators.min(minQuantity)]],
        });
    }

    createProjectValueFb(poBoqProjectValue: PoBoqProjectValue): FormGroup {
        return this.fb.group({
            name: [poBoqProjectValue?.name ?? null],
            amount: [poBoqProjectValue?.amount ?? null],
        });
    }

    createProjectDeliverFb(poBoqDeliver: PoBoqDeliver): FormGroup {
        return this.fb.group({
            date: [poBoqDeliver?.date ?? null],
            quantity: [poBoqDeliver?.quantity ?? null],
        });
    }

    get poBoqQuantityLst() {
        return this.poForm.get('poBoqQuantityLst') as FormArray;
    }

    getProjectQuantities(boq: AbstractControl): FormArray {
        return boq.get('projectQuantities') as FormArray;
    }

    getProjectValue(boq: AbstractControl): FormArray {
        return boq.get('projectValue') as FormArray;
    }

    getProjectDeliver(boq: AbstractControl): FormArray {
        return boq.get('projectDeliver') as FormArray;
    }

    handleSelectFileBOQ(file: File) {
        this.loadingService.show();
        this.poService.importBOQ(file, this.po?.id).subscribe({
            next: (res: ApiResponse) => {
                if (res.code === 1) {
                    this.po.poBoqQuantityLst = (res?.data as unknown as PoBoQuantity[]) ?? [];
                    if (this.po.poBoqQuantityLst.length > 0) {
                        this.poBoqQuantityFirst = this.po.poBoqQuantityLst[0];
                        this.initForm();
                    } else {
                        this.poBoqQuantityFirst = null;
                    }
                    this.urlError = null;
                    this.po.boqAttachment = res.attachment;
                    this.po.updateBoq = true;
                } else {
                    this.po.poBoqQuantityLst = [];
                    this.poBoqQuantityFirst = null;
                    this.urlError = res.message;
                    this.po.updateBoq = false;
                    this.po.boqAttachment = null;
                }
                this.loadingService.hide();
            },
            error: () => {
                this.loadingService.hide();
                this.po.updateBoq = false;
            },
        });
    }

    handleClearFile() {
        if (this.po.boqAttachment) {
            this.po.removeAttachments = [this.po.boqAttachment.id];
        }
        this.po.updateBoq = true;
        this.po.boqAttachment = null;
        this.poBoqQuantityFirst = null;
        this.po.poBoqQuantityLst = [];
    }

    saveOrderQuantityData(itemControl: AbstractControl) {
        this.poForm.get('updateBoq').setValue(true);
        this.loadingService.show();
        this.poService.update(this.poForm.getRawValue()).subscribe({
            next: (res) => {
                this.loadingService.hide();
                this.alertService.success('Thành công');
            },
            error: () => {
                this.loadingService.hide();
                this.po.updateBoq = false;
            },
        });
    }

    saveQuantityData(itemControl: AbstractControl) {
        this.poForm.get('updateBoq').setValue(true);
        this.loadingService.show();
        this.poService.update(this.poForm.getRawValue()).subscribe({
            next: (res) => {
                this.loadingService.hide();
                this.alertService.success('Thành công');
            },
            error: () => {
                this.loadingService.hide();
                this.po.updateBoq = false;
            },
        });
    }
}
