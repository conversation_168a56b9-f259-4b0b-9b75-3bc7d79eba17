<!-- Input Text -->
<input
    *ngIf="type === 'input' && control"
    #inputElement
    [formControl]="control"
    type="text"
    [placeholder]="placeholder"
    class="tw-w-full"
    pInputText
    [readonly]="isReadonly"
    (dblclick)="startEditing()"
    (blur)="saveValue()"
    (keydown)="onKeyPress($event)"
/>

<p-inputNumber
    *ngIf="type === 'input-number' && control"
    #inputElement
    [formControl]="control"
    [placeholder]="placeholder"
    class="tw-w-full"
    [readonly]="isReadonly"
    (dblclick)="startEditing()"
    (onBlur)="saveValue()"
    [minFractionDigits]="0"
    [maxFractionDigits]="10"
></p-inputNumber>

<!-- Textarea -->
<textarea
    *ngIf="type === 'textarea'"
    #inputElement
    [formControl]="control"
    [placeholder]="placeholder"
    class="tw-w-full"
    [readonly]="isReadonly"
    (dblclick)="startEditing()"
    (blur)="saveValue()"
    pInputTextarea
></textarea>

<input
    *ngIf="type === 'calendar' && isReadonly"
    type="text"
    class="tw-w-full"
    pInputText
    [value]="formatDate(control.value)"
    readonly
    (dblclick)="startEditing()"
/>

<!-- Trường ngày: Khi đã double-click -->
<p-calendar
    *ngIf="type === 'calendar' && !isReadonly"
    #inputElement
    #calendar
    [formControl]="control"
    [dateFormat]="'dd/mm/yy'"
    [placeholder]="placeholder"
    (onSelect)="saveValue()"
    (onBlur)="saveValue()"
/>

<app-input-validate [control]="control" [fieldName]="fieldName" [type]="typeValidate" />

<!--<div [ngClass]="class" [ngStyle]="style">
    <ng-content></ng-content>
    <ng-container *ngIf="control?.errors && control?.touched">
        <p *ngIf="control?.errors['pattern']" class="text-red-400">
            {{ "Trường " + fieldName + " phải có định dạng " + pattern }}
        </p>
        <p *ngIf="control?.errors['required']" class="text-red-400">
            {{ "Trường " + fieldName + " là bắt buộc" }}
        </p>
        <p *ngIf="control?.errors['minlength']" class="text-red-400">
            {{ "Trường " + fieldName + " không vượt quá " + control?.errors['minlength'].requiredLength + " ký tự" }}
        </p>
        <p *ngIf="control?.errors['maxlength']" class="text-red-400">
            {{ "Trường " + fieldName + " không vượt quá " + control?.errors['maxlength'].requiredLength + " ký tự" }}
        </p>
        <ng-container *ngFor="let validateMessage of validateMessages">
            <p *ngIf="control?.errors[validateMessage.type] || control?.hasError(validateMessage.type)" class="text-red-400">
                {{ validateMessage.message }}
            </p>
        </ng-container>
    </ng-container>
    <ng-container *ngIf="control?.invalid && control?.touched">
        <ng-container *ngFor="let validateMessage of validateMessages">
            <p *ngIf="control?.hasError(validateMessage.type)" class="text-red-400">
                {{ validateMessage.message }}
            </p>
        </ng-container>
    </ng-container>
</div>-->
