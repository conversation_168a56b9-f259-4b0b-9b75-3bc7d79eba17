import { Injectable } from '@angular/core';
import { HttpClient, HttpBackend, HttpRequest, HttpEvent, HttpEventType, HttpHeaders, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { AnalyzeResp } from 'src/app/models/interface/pms';

@Injectable({ providedIn: 'root' })
export class UploadService {
    // Tạo 1 HttpClient “sạch” (không qua interceptor, không thêm header mặc định)
    private rawHttp: HttpClient;

    constructor(
        private http: HttpClient,
        private backend: HttpBackend,
    ) {
        this.rawHttp = new HttpClient(backend);
    }

    analyzeFile(fileName: string, type: string): Observable<AnalyzeResp> {
        let rawName = fileName;
        return this.http.post<AnalyzeResp>(`/pr/api/files/analyze-file`, { fileName: rawName, type });
    }

    uploadToPresignedUrl(file: File, presignedUrl: string): Observable<number> {
        let rawName = file.name;

        const encoded = encodeURIComponent(rawName)
            .replace(/['()]/g, (c) => '%' + c.charCodeAt(0).toString(16).toUpperCase())
            .replace(/\*/g, '%2A'); // mã hóa cả kí tự ! ~ * ' ( )
        // 1) Khai báo HttpHeaders đúng kiểu

        const headers = new HttpHeaders({
            'Content-Disposition': `attachment; filename*=UTF-8''${encoded}`,
        });

        // 2) Khởi tạo HttpRequest, body là raw File, headers là HttpHeaders
        const req = new HttpRequest<File>('PUT', presignedUrl, file, {
            headers,
            reportProgress: true,
            responseType: 'text', // S3/MinIO trả empty body
        });

        // 3) Gửi qua rawHttp.request để không có header mặc định nào khác
        return this.rawHttp.request(req).pipe(
            // Lọc chỉ những event cần: UploadProgress và Response
            filter((evt: HttpEvent<any>) => evt.type === HttpEventType.UploadProgress || evt.type === HttpEventType.Response),
            map((evt) => {
                if (evt.type === HttpEventType.UploadProgress && evt.total) {
                    // trả về phần trăm upload
                    return Math.round((100 * evt.loaded) / evt.total);
                }
                // khi đã xong → 100%
                return 100;
            }),
        );
    }
    getFileHeaders(fileUrl: string): Observable<{ [key: string]: string }> {
        // Dùng rawHttp để không qua interceptor
        return this.rawHttp
            .get(fileUrl, {
                observe: 'response',
                responseType: 'blob', // có thể là 'arraybuffer' hoặc 'blob'
            })
            .pipe(
                map((resp: HttpResponse<Blob>) => {
                    // Trả ra object các header cần
                    return {
                        'content-disposition': resp.headers.get('Content-Disposition') || '',
                        'content-type': resp.headers.get('Content-Type') || '',
                        // Thêm các header cần thiết khác nếu muốn
                    };
                }),
            );
    }
}
