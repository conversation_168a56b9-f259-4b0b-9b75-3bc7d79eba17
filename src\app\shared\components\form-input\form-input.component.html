<div [formGroup]="formGroup">
    <ng-select
        style="
            border-spacing: 0px;
            font-size: 1rem;
            color: rgb(4, 20, 42);
            background: #ffffff;
            border-radius: 6px;
            margin: 0;
            width: 100%;
            box-sizing: border-box;
        "
        [id]="selectizeId"
        highlightColor="#176ee8"
        highlightTextColor="#ed160e"
        [items]="options"
        [virtualScroll]="true"
        [multiple]="isMultiple"
        [bindLabel]="itemLable"
        [bindValue]="itemValue"
        [placeholder]="placeHolder"
        [typeahead]="input$"
        (scrollToEnd)="fetchMore(select.filterValue)"
        (search)="onSearch($event)"
        (add)="onAdd($event)"
        (remove)="onRemove($event)"
        (clear)="onClear()"
        (change)="onChange($event)"
        (open)="onOpen()"
        [formControlName]="formControlName"
        appendTo="body"
        dropdownPosition="bottom"
        #select
    >
        <ng-template ng-option-tmp let-item="item" let-index="index" let-search="searchTerm">
            <span [ngOptionHighlight]="search">{{ item[itemLable] }}</span>
        </ng-template>
    </ng-select>
</div>
