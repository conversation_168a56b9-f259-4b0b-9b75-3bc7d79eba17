import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ConfirmationService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { DialogModule } from 'primeng/dialog';
import { DropdownChangeEvent, DropdownModule } from 'primeng/dropdown';
import { InputNumberModule } from 'primeng/inputnumber';
import { InputTextModule } from 'primeng/inputtext';
import { MultiSelectModule } from 'primeng/multiselect';
import { PanelModule } from 'primeng/panel';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';
import { MAP_TRANSFER_STATE, PO_STATE_CONSTANT, TRANSFER_STATE_OBJECT } from 'src/app/models/constant/sc';
import { CanExportDTO, CanImportDTO, Po, PoDetail, PoDetailDTO, PoTransfer, PoTransferItem } from 'src/app/models/interface/sc';
import { PoDetailService } from 'src/app/services/sc/po/po-detail.service';
import { PoTransferItemService } from 'src/app/services/sc/po/po-transfer-item.service';
import { PoTransferService } from 'src/app/services/sc/po/po-transfer.service';
import { AttributeAuthorityDirective } from 'src/app/shared/directives/attribute-authority.directive';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { AlertService } from 'src/app/shared/services/alert.service';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { TrimmedFormControl } from 'src/app/utils/form';
import { focQuantityValidator, maxValidator, nonEmptyArrayValidator } from 'src/app/utils/validator';
import { isEmpty, isEqual } from 'lodash';
import { ApiResponse } from 'src/app/models/interface';
import { ButtonGroupFileComponent } from 'src/app/shared/components/button-group-file/button-group-file.component';
import { FileService } from 'src/app/shared/services/file.service';
import { DateUtils } from '../../../../../utils/date-utils';

@Component({
    selector: 'app-edit-po-transfer-export',
    templateUrl: './edit.po-export.component.html',
    standalone: true,
    imports: [
        TableModule,
        InputTextModule,
        DropdownModule,
        CalendarModule,
        DialogModule,
        ReactiveFormsModule,
        InputNumberModule,
        FormCustomModule,
        PanelModule,
        ButtonModule,
        CommonModule,
        TooltipModule,
        MultiSelectModule,
        TableCommonModule,
        TagModule,
        AttributeAuthorityDirective,
        ButtonGroupFileComponent,
    ],
    providers: [PoTransferService, PoTransferItemService, PoDetailService],
})
export class EditPoTransferExportComponent implements OnInit {
    @Input() po: Po;
    @Output() updatePoDetails = new EventEmitter<PoDetailDTO>();
    @Input() poTransfer: PoTransfer;
    @Input() visible: boolean = false;
    @Output() onClose = new EventEmitter<boolean>();
    @Output() onSuccess = new EventEmitter(); // Emits selected file(s)

    formGroup: FormGroup;
    isEditing: boolean = false;
    isAdding: boolean = false;
    loading: boolean = false;
    optionPoDetail: CanExportDTO[] = [];
    optionPoDetailOrigin: CanExportDTO[] = [];
    backUpPoTransferItem: PoTransferItem;
    isDisabled: boolean = false;
    isCanCancel: boolean = false;
    mapStateTransfer = MAP_TRANSFER_STATE;
    PO_STATE_CONSTANT = PO_STATE_CONSTANT;

    // object disabled các mã vnpt đã chọn
    poDetailDisabled: Record<string, CanExportDTO> = {};
    poTransferItemsInit: PoTransferItem[] = [];

    // Quantity level
    quantityByReference: Map<string, { quantity: number; focQuantity: number }>;
    // End quantity level

    // import file
    urlError: string;
    poTransferItemsOldData: PoTransferItem[];
    poTransferItemsDelete: number[];
    isImportFile: boolean = false; // Check if user import file

    constructor(
        private fb: FormBuilder,
        private poTransferService: PoTransferService,
        private poDetailService: PoDetailService,
        private poTransferExportItemService: PoTransferItemService,
        private alertService: AlertService,
        private loadingService: LoadingService,
        private confirmationService: ConfirmationService,
        private fileService: FileService,
    ) {}

    manufacturerCodeMap = new Map<string, string>();
    descriptionMap = new Map<string, string>();
    unitPriceString: string = 'USD';

    ngOnInit(): void {
        this.initForm(this.poTransfer, this.po);

        if (this.poTransfer && this.poTransfer?.id) {
            this.isDisabled =
                this.poTransfer && this.poTransfer.state !== TRANSFER_STATE_OBJECT.NEW && this.poTransfer.state !== TRANSFER_STATE_OBJECT.PROCESSING;
            this.isCanCancel = this.poTransfer && this.poTransfer.state === TRANSFER_STATE_OBJECT.PROCESSING;

            this.poTransferExportItemService.getAll(`query=transferId==${this.poTransfer.id}`).subscribe({
                next: (value) => {
                    this.poTransfer.poTransferItems = value.body;
                    this.poTransferItemsInit = value.body;
                    this.poTransfer.isUpdatePoDetail = false;
                    this.poTransferItemsOldData = value.body;
                    this.loading = false;
                    this.initForm(this.poTransfer, this.po);
                    this.getOptionsExport();
                },
            });
        } else {
            this.getOptionsExport();
        }

        if (!this.poTransfer) {
            this.poTransfer = this.formGroup.getRawValue();
            this.poTransfer.poTransferItemsDelete = [];
            this.poTransfer.poId = this.po.id;
        }

        this.po.poBoqQuantityLst?.forEach((quantity) => {
            quantity.poBoqList?.forEach((boq) => {
                this.manufacturerCodeMap.set(boq.productName, boq.manufacturerCode);
                this.descriptionMap.set(boq.description, boq.description);
            });
        });

        if (this.po.poDetails.length > 0) {
            // Set Unit Price as Po Detail Unit Price first
            this.unitPriceString = this.po.poDetails[0].unitPrice;
            //console.log(this.unitPriceValue);
        }
    }

    private getOptionsExport() {
        this.poDetailService
            .getCanExport({
                poId: this.po.id,
            })
            .subscribe({
                next: (value) => {
                    this.optionPoDetail = value;
                    this.optionPoDetailOrigin = value;
                    this.quantityByReference = new Map(
                        this.optionPoDetail.map((item) => [item.internalReference, { quantity: item.quantity, focQuantity: item.focQuantity }]),
                    );

                    // remove items if exists from options
                    this.removeItemsFromOptionPoDetail();
                    this.updateDisabledOnInit();
                },
            });
    }

    removeItemsFromOptionPoDetail(): void {
        const internalRefsToRemove = this.poTransferItems.controls.map((control) => control.get('internalReference')?.value);

        this.optionPoDetail = this.optionPoDetail.filter((item) => !internalRefsToRemove.includes(item.internalReference));
    }

    initForm(poTransfer: PoTransfer | null, po: Po) {
        // console.log(poTransfer?.poTransferItems)
        this.formGroup = this.fb.group({
            type: new FormControl({
                value: poTransfer ? poTransfer.type : 1,
                disabled: this.isDisabled,
            }),
            contract: new FormControl({
                value: poTransfer ? poTransfer.contract : null,
                disabled: this.isDisabled,
            }),
            invTransferNumber: new FormControl({
                value: poTransfer ? poTransfer?.invTransferNumber : null,
                disabled: true,
            }),
            supplierName: new FormControl({
                value: po ? po.supplierName : null,
                disabled: true,
            }),
            dateCustom: new FormControl({
                value: poTransfer?.date ? new Date(poTransfer.date) : new Date(),
                disabled: this.isDisabled,
            }),
            orderNo: new FormControl({ value: po.orderNo, disabled: true }),
            sourceDocument: new TrimmedFormControl({ value: poTransfer?.sourceDocument, disabled: this.isDisabled }),
            poTransferItems: this.fb.array(this.initPoTransferItems(poTransfer?.poTransferItems || []), [nonEmptyArrayValidator()]),
        });
        // console.log(this.formGroup)
    }

    initPoTransferItems(items: PoTransferItem[]): FormGroup[] {
        return items.map((item) =>
            this.fb.group({
                id: [item?.id],
                transferId: [item?.transferId],
                created: [item?.created],
                updated: [item?.updated],
                createdBy: [item?.createdBy],
                updatedBy: [item?.updatedBy],
                tenantId: [item?.tenantId],
                active: [item?.active],
                transferType: [1],
                indexLevel: [{ value: item?.indexLevel, disabled: true }, []],
                internalReference: [item?.internalReference, Validators.required],
                manufacturerCode: [{ value: item?.manufacturerCode ?? null, disabled: true }, []],
                description: [{ value: item?.description, disabled: true }, Validators.required],
                quantity: [item?.quantity, [Validators.required, Validators.min(1)]],
                focQuantity: [item?.focQuantity, [Validators.min(0)]],
                price: [{ value: item?.price, disabled: true }, [Validators.required, Validators.min(0)]],
                amount: [{ value: item?.amount, disabled: true }, Validators.required],
                note: [item?.note],
                isEdit: [false],
            }),
        );
    }

    get poTransferItems(): FormArray {
        return this.formGroup.get('poTransferItems') as FormArray;
    }

    addPoTransferItem(): void {
        const newItem = this.fb.group({
            indexLevel: [{ value: null }, []],
            transferId: [this.poTransfer?.id],
            internalReference: [null, [Validators.required]],
            manufacturerCode: [{ value: null, disabled: true }, []],
            description: [{ value: null, disabled: true }, [Validators.required]],
            quantity: [null, [Validators.required, Validators.min(1)]],
            focQuantity: [null, [Validators.min(0)]],
            price: [{ value: null, disabled: true }, [Validators.required]],
            amount: [{ value: null, disabled: true }, [Validators.required]],
            note: [null],
            isEdit: [true],
            transferType: [1],
        });
        this.poTransferItems.push(newItem);
        this.isAdding = true;

        newItem.get('quantity').valueChanges.subscribe({
            next: (value) => {
                this.handleChangeQuantity(
                    newItem.get('quantity') as FormControl,
                    newItem.get('focQuantity') as FormControl,
                    newItem.get('amount') as FormControl,
                    value,
                    newItem.get('price').getRawValue(),
                );

                newItem.get('focQuantity').updateValueAndValidity({ onlySelf: true });
            },
        });
        newItem.get('focQuantity').valueChanges.subscribe({
            next: (value) => {
                this.handleChangeFocQuantity(
                    newItem.get('quantity') as FormControl,
                    newItem.get('amount') as FormControl,
                    value,
                    newItem.get('price').getRawValue(),
                );
            },
        });
    }

    handleChangeQuantity(controlQuantity: FormControl, controlFocQuantity: FormControl, controlAmount: FormControl, value: number, price: number) {
        if (!isEmpty(controlQuantity.errors)) return;

        const focQuantity = controlFocQuantity.getRawValue() ?? 0;
        controlAmount.patchValue((value - focQuantity) * price);
    }

    handleChangeFocQuantity(controlQuantity: FormControl, controlAmount: FormControl, value: number, price: number) {
        if (!isEmpty(controlQuantity.errors)) return;

        const quantity = controlQuantity.getRawValue() ?? 0;
        const focQuantity = value ?? 0;
        if (quantity <= focQuantity) {
            controlAmount.patchValue(0);
        } else {
            controlAmount.patchValue((quantity - focQuantity) * price);
        }
    }

    editTransferItem(index: number): void {
        const item = this.poTransferItems.at(index);
        this.backUpPoTransferItem = item.getRawValue();
        item.patchValue({ isEdit: true });
        this.isEditing = true;
        item.get('description').disable();
        this.updateOptionsOnEdit(item);

        item.get('quantity').valueChanges.subscribe({
            next: (value) => {
                this.handleChangeQuantity(
                    item.get('quantity') as FormControl,
                    item.get('focQuantity') as FormControl,
                    item.get('amount') as FormControl,
                    value,
                    item.get('price').getRawValue(),
                );
                item.get('focQuantity').updateValueAndValidity({ onlySelf: true });
            },
        });
        item.get('focQuantity').valueChanges.subscribe({
            next: (value) => {
                this.handleChangeFocQuantity(item.get('quantity') as FormControl, item.get('amount') as FormControl, value, item.get('price').getRawValue());
            },
        });
    }

    saveTransferItem(index: number): void {
        const item = this.poTransferItems.at(index);

        const itemValue = item.getRawValue();

        // Kiểm tra tính hợp lệ của item
        if (item.valid) {
            item.enable();

            // Cập nhật isEdit thành false
            if (itemValue.focQuantity == null) {
                itemValue.focQuantity = 0;
            }
            item.setValue({ ...itemValue, isEdit: false });

            // Đặt trạng thái isEditing thành false
            this.isEditing = false;
            this.isAdding = false;

            let targetIndex = -1;
            let targetElement: PoDetail;

            for (let i = 0; i < this.optionPoDetail.length; i++) {
                if (this.optionPoDetail[i].internalReference === itemValue.internalReference) {
                    targetIndex = i;
                    targetElement = this.optionPoDetail[i];
                    break;
                }
            }

            if (targetIndex !== -1) {
                this.optionPoDetail.splice(targetIndex, 1);
            }

            this.poDetailDisabled[targetElement.internalReference] = targetElement;
            this.poTransfer.isUpdatePoDetail = true;
        }
    }

    cancelCreate(index: number): void {
        if (this.isAdding) {
            this.poTransferItems.removeAt(index);
        } else if (this.isEditing) {
            this.poTransferItems.at(index).enable();
            this.poTransferItems.at(index).patchValue(this.backUpPoTransferItem);
            this.removeInternalReferenceFromOptions(this.poTransferItems.at(index)?.getRawValue());
        }
        this.isAdding = false;
        this.isEditing = false;
    }

    removeTransferItem(index: number): void {
        this.isEditing = false;

        const itemValue = this.poTransferItems.at(index).getRawValue() as PoTransferItem;

        this.optionPoDetail.unshift(this.poDetailDisabled[itemValue.internalReference]);
        // Xóa mục tại index trong poTransferItems
        this.poTransferItems.removeAt(index);
        if (itemValue.id) {
            this.poTransfer.poTransferItemsDelete.push(itemValue.id);
            this.poTransfer.isUpdatePoDetail = true;
        }
    }

    handleSubmit(): void {
        if (this.formGroup.valid) {
            const poTransfer: PoTransfer = this.formGroup.getRawValue();
            const date = poTransfer?.dateCustom ? DateUtils.convertToTimestampHCM(poTransfer?.dateCustom) : null;
            this.loadingService.show();
            if (this.poTransfer.id) {
                if (!isEqual(date, this.poTransfer.date)) {
                    this.poTransfer.isUpdatePoDetail = true;
                }
                if (this.isImportFile) {
                    this.poTransfer.poTransferItemsDelete = this.poTransferItemsDelete ? this.poTransferItemsDelete : [];
                }
                this.poTransferService.update({ ...this.poTransfer, ...poTransfer, date }).subscribe({
                    next: () => {
                        // Call API lay bang ke
                        this.poDetailService.getPoDetailByPo(this.poTransfer.poId).subscribe({
                            next: (res) => {
                                this.updatePoDetails.emit(res as PoDetailDTO);
                                this.alertService.success('Thành công');
                                this.onSuccess.emit();
                                this.closeDialog();
                                this.loadingService.hide();
                            },
                            error: (res) => {
                                this.alertService.handleError(res);
                                this.loadingService.hide();
                            },
                        });
                    },
                    error: (res) => {
                        this.alertService.handleError(res);
                        this.loadingService.hide();
                    },
                });
            } else {
                this.poTransferService.create({ ...this.poTransfer, ...poTransfer, date }).subscribe({
                    next: () => {
                        // Call API lay bang ke
                        this.poDetailService.getPoDetailByPo(this.poTransfer.poId).subscribe({
                            next: (res) => {
                                this.updatePoDetails.emit(res as PoDetailDTO);
                                this.alertService.success('Thành công');
                                this.onSuccess.emit();
                                this.closeDialog();
                                this.loadingService.hide();
                            },
                            error: (res) => {
                                this.alertService.handleError(res);
                                this.loadingService.hide();
                            },
                        });
                    },
                    error: (res) => {
                        this.alertService.handleError(res);
                        this.loadingService.hide();
                    },
                });
            }
        }
    }
    closeDialog() {
        this.visible = false;
        this.onClose.emit(false);
    }

    getSeverity() {
        switch (this.poTransfer.state) {
            case 3:
                return 'danger';
            case 0:
                return 'primary';
            case 1:
                return 'primary';
            case 2:
                return 'success';
            default:
                return 'primary';
        }
    }

    confirmCancel() {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn hủy phiếu',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.loadingService.show();
                this.poTransferService.cancel(this.poTransfer.id).subscribe({
                    next: () => {
                        // Call API lay bang ke
                        this.poDetailService.getPoDetailByPo(this.poTransfer.poId).subscribe({
                            next: (res) => {
                                this.updatePoDetails.emit(res as PoDetailDTO);
                                this.alertService.success('Thành công');
                                this.poTransfer.state = TRANSFER_STATE_OBJECT.CANCEL;
                                this.loadingService.hide();
                            },
                            error: (res) => {
                                this.alertService.handleError(res);
                                this.loadingService.hide();
                            },
                        });
                    },
                    error: (e) => {
                        this.alertService.handleError(e);
                        this.loadingService.hide();
                    },
                });
            },
        });
    }

    handleChangeInternal(event: DropdownChangeEvent, index: number) {
        const formControl = this.poTransferItems.at(index);

        if (event.value) {
            const poDetail = this.optionPoDetail.find((p) => p.internalReference === event.value);

            if (poDetail) {
                // Kiểm tra xem poDetail có tồn tại không
                // Cập nhật giá trị của form control
                formControl.patchValue({
                    indexLevel: poDetail.indexLevel,
                    internalReference: event.value,
                    manufacturerCode: this.manufacturerCodeMap.get(event.value) ?? null,
                    description: poDetail.description,
                    quantity: null, // Đặt giá trị mặc định cho quantity để đảm bảo validator hoạt động
                    focQuantity: null,
                    price: poDetail.price,
                    amount: null,
                    note: null,
                });

                // Đặt validator cho trường quantity
                const quantityControl = formControl.get('quantity');
                const max = poDetail.quantity ?? 1;

                quantityControl.setValidators([Validators.required, Validators.min(1), Validators.max(max)]);
                quantityControl.updateValueAndValidity(); // Cậ

                const focQuantityControl = formControl.get('focQuantity');
                const maxFoc = poDetail.focQuantity ?? 0;
                focQuantityControl.setValidators([
                    Validators.min(0),
                    maxValidator(maxFoc, `Số lượng FOC phải <= ${maxFoc}`),
                    focQuantityValidator(`Số lượng FOC phải <= ${max}`),
                ]);
                focQuantityControl.updateValueAndValidity(); // Cập nhật lại trạng thái validator của quantity
            }
        } else {
            formControl.patchValue({
                indexLevel: null,
                internalReference: null,
                description: null,
                quantity: null,
                focQuantity: null,
                price: null,
                amount: null,
                note: null,
            });
            // Loại bỏ tất cả các validator khỏi trường quantity
            const quantityControl = formControl.get('quantity');
            quantityControl.clearValidators();
            quantityControl.updateValueAndValidity(); // Cập nhật lại trạng thái validator của form control
        }
    }

    getValueTagState() {
        return this.mapStateTransfer[this.poTransfer.state];
    }

    handleSelectFile(file: File) {
        this.loadingService.show();
        this.poTransferService.importFileExport(file, this.po.id, this.poTransfer?.id).subscribe({
            next: (res: ApiResponse) => {
                if (res.code === 1) {
                    const data = (res?.data as unknown as PoTransferItem[]) ?? [];
                    this.poTransferItemsDelete = this.poTransferItemsOldData?.map((item) => item.id) || [];
                    this.isImportFile = true;
                    this.poTransferItems.controls.forEach((control, index) => {
                        this.removeTransferItem(index);
                    });
                    /*this.poTransferItems.clear();*/
                    data.forEach((item, index) => {
                        const manufacturerCode = this.manufacturerCodeMap.get(item.internalReference) ?? null;
                        const newItem = this.fb.group({
                            transferId: [this.poTransfer?.id],
                            internalReference: [item.internalReference, [Validators.required]],
                            manufacturerCode: [{ value: manufacturerCode, disabled: true }, []],
                            description: [{ value: item.description, disabled: true }, [Validators.required]],
                            quantity: [item.quantity, [Validators.required, Validators.min(1)]],
                            focQuantity: [item.focQuantity ?? 0, [Validators.min(0)]],
                            price: [{ value: item.price, disabled: true }, [Validators.required]],
                            amount: [{ value: item.amount, disabled: true }, [Validators.required]],
                            indexLevel: [{ value: item?.indexLevel, disabled: true }, []],
                            note: [item.note],
                            isEdit: [false],
                            transferType: [1],
                        });
                        this.poTransferItems.push(newItem);
                        this.saveTransferItem(index);
                        /*this.removeItemsFromOptionPoDetail();*/
                        /*this.updateDisabledOnInit();*/
                    });

                    this.urlError = null;
                    this.alertService.success('Thành công');
                } else {
                    this.poTransferItems.clear();
                    this.urlError = res.message;
                }
                this.loadingService.hide();
            },
            error: (e) => {
                this.loadingService.hide();
                this.alertService.handleError(e);
            },
        });
    }

    handleClearFile() {}

    handleDownload() {
        this.poTransferService.getFileTemplateExport({ poId: this.po.id }).subscribe({
            next: (res) => {
                this.fileService.downLoadFileByService(res.url, '/sc/api');
            },
        });
    }

    getTotalAmount() {
        return this.poTransferItems.controls.reduce((sum, control) => {
            const amount = control.get('amount')?.value || 0;
            const roundedDelivered = this.roundAmount(amount || 0);
            return sum + roundedDelivered;
        }, 0);
    }

    private roundAmount(amount: number): number {
        const isUSD = this.unitPriceString === 'USD';
        if (isUSD) {
            const formatter = new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
                useGrouping: false,
            });
            return Number(formatter.format(amount));
        }
        return Math.round(amount);
    }

    getFocQuantityByReference(internalReference: string) {
        if (!internalReference) {
            return 0;
        } else {
            return this.quantityByReference.get(internalReference)?.focQuantity;
        }
    }

    private updateOptionsOnEdit(item: AbstractControl) {
        const internalReference = item.get('internalReference')?.getRawValue();

        const exists = this.optionPoDetail?.some((po) => po.internalReference === internalReference);

        if (!exists && this.optionPoDetail) {
            const canImportDTO = this.poDetailDisabled[internalReference]; // lấy từ Record thay vì find trong array

            if (canImportDTO) {
                this.optionPoDetail.push(canImportDTO);
                // Đặt validator cho trường quantity
                const quantityControl = item.get('quantity');
                const max = canImportDTO.quantity ?? 1;

                quantityControl.setValidators([Validators.required, Validators.min(1), Validators.max(max)]);
                quantityControl.updateValueAndValidity(); // Cậ

                const focQuantityControl = item.get('focQuantity');
                const maxFoc = canImportDTO.focQuantity ?? 0;
                focQuantityControl.setValidators([
                    Validators.min(0),
                    maxValidator(maxFoc, `Số lượng FOC phải <= ${maxFoc}`),
                    focQuantityValidator(`Số lượng FOC phải <= ${max}`),
                ]);
                focQuantityControl.updateValueAndValidity(); // Cập nhật lại trạng thái validator của quantity
            }
        }
    }

    private removeInternalReferenceFromOptions(itemValue) {
        let targetIndex = -1;
        let targetElement: CanExportDTO;

        for (let i = 0; i < this.optionPoDetail.length; i++) {
            if (this.optionPoDetail[i]?.internalReference === itemValue?.internalReference) {
                targetIndex = i;
                targetElement = this.optionPoDetail[i];
                break;
            }
        }

        if (targetIndex !== -1) {
            this.optionPoDetail.splice(targetIndex, 1);
        }

        this.poDetailDisabled[targetElement?.internalReference] = targetElement;
    }

    private updateDisabledOnInit() {
        this.poDetailDisabled = {};
        this.poTransferItemsInit.forEach((itemValue) => {
            const internalRef = itemValue.internalReference;

            this.poDetailDisabled[internalRef] = {
                indexLevel: itemValue.indexLevel,
                internalReference: internalRef,
                quantity: itemValue.quantity ?? 0,
                focQuantity: itemValue.focQuantity ?? 0,
                price: itemValue.price,
                amount: itemValue.amount,
                description: itemValue.description,
                display: `${internalRef} - ${itemValue.quantity}`,
            };
        });

        if (this.optionPoDetailOrigin) {
            this.optionPoDetailOrigin.forEach((po) => {
                const internalRef = po.internalReference;

                if (this.poDetailDisabled[internalRef]) {
                    this.poDetailDisabled[internalRef].quantity += po.quantity ?? 0;
                    this.poDetailDisabled[internalRef].focQuantity += po.focQuantity ?? 0;

                    // Cập nhật lại display để reflect quantity mới
                    this.poDetailDisabled[internalRef].display = `${internalRef} - ${this.poDetailDisabled[internalRef].quantity}`;
                }
            });
        }
        console.log(this.poDetailDisabled);
    }
}
