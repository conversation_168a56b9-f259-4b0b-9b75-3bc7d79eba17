import { HttpClient } from '@angular/common/http';
import { Component, EventEmitter, Input, OnChanges, Output, OnInit } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { Subject } from 'rxjs';
import Common from 'src/app/utils/common';

@Component({
    selector: 'app-form-input',
    templateUrl: './form-input.component.html',
    styleUrls: ['./form-input.component.scss'],
    standalone: true,
    imports: [ReactiveFormsModule, NgSelectModule],
})
export class FormInputComponent implements OnChanges, OnInit {
    @Input() selectizeId;
    @Input() url: string;
    @Input() itemLable;
    @Input() itemValue;
    @Input() placeHolder;
    @Input() isMultiple = false;
    @Input() model;
    @Output('handleChangeSelected') handleChangeSelected: EventEmitter<unknown> = new EventEmitter();
    @Input('control') formGroup;
    @Input('controlName') formControlName;
    // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
    @Input('callback') callback: Function = () => {};
    @Input('options') options: unknown[] = [];

    constructor(public http: HttpClient) {}

    ngOnChanges(): void {
        this.page = 0;
        this.originPage = 0;
        this.input$ = new Subject<string>();
        this.selected = [];
        this.term = '';
    }
    page: number = 0;
    originPage: number = 0;
    input$ = new Subject<string>();
    selected = [];
    term = '';

    ngOnInit(): void {
        // const api: string = this.genQuery('');
        this.fetchMore('');
    }

    fetchMore(term) {
        const api: string = this.genQuery(term);
        this.http.get<unknown[]>(api, { observe: 'response' }).subscribe((res) => {
            this.options = Common.mergeArray(this.options, res.body, (a, b) => a.id === b.id);

            if (term === '' || term === undefined) {
                this.originPage = ++this.page;
            }
        });
    }

    onSearch(e) {
        this.page = e.term === '' || e.term === undefined ? this.originPage : 0;
        this.term = e.term;
        this.fetchMore(this.term);
    }

    onRemove(e) {
        this.page = this.originPage;
        this.fetchMore('');
        this.selected = this.selected.filter((obj) => obj.id !== e.id);
        this.handleChangeSelected.emit([this.selected]);

        this.term = '';
    }

    onClear() {
        this.page = this.originPage;
        this.fetchMore('');
        this.term = '';
    }

    onAdd(e) {
        const selected = this.options.filter((obj) => obj['id'] === e.id);
        this.selected.push(...selected);
        this.handleChangeSelected.emit([this.selected]);
        this.term = '';
    }

    genQuery(param) {
        let query = this.url;
        if (param !== '' && param !== undefined) {
            query += `${this.itemLable}=='*${param}*'`;
        }
        return `${query}&page=${this.page}&size=10&sort=id,desc`;
    }

    onChange(e) {
        this.callback(e);
    }

    onOpen() {
        this.fetchMore(this.term);
    }
}
