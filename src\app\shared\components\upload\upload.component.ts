import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, inject } from '@angular/core';
import { AlertService } from '../../services/alert.service';
import { ButtonModule } from 'primeng/button';

@Component({
    selector: 'app-upload',
    templateUrl: './upload.component.html',
    styleUrls: ['./upload.component.scss'],
    standalone: true,
    imports: [ButtonModule],
})
export class UploadComponent implements OnInit, OnChanges {
    @Input() type: string;
    @Input() label: string = 'Chọn file';
    @Input() limit: number = 1;
    @Input() disabled: boolean = false;
    @Input('multiple') multiple: boolean = false;
    @Output() onChange = new EventEmitter<File[]>();

    acceptTypes: string;
    alertService = inject(AlertService);

    ngOnInit() {
        // Thiết lập kiểu file chấp nhận dựa trên đầu vào
        this.acceptTypes = this.getAcceptTypes(this.type);
        // Thiết lập cho phép nhiều file nếu limit > 1
        this.multiple = this.limit > 1;
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['limit'] && changes['limit'].currentValue !== changes['limit'].previousValue) {
            this.multiple = this.limit > 1;
        }
    }

    onFileSelected(event: Event) {
        const target = event.target as HTMLInputElement;
        const files: FileList = target.files;
        const validFiles: File[] = [];
        const invalidFiles: File[] = [];

        Array.from(files).forEach((file) => {
            if (this.isValidType(file)) {
                validFiles.push(file);
            } else {
                invalidFiles.push(file);
            }
        });

        if (invalidFiles.length > 0) {
            this.alertService.error('Lỗi', `Định dạng file không hợp lệ.`);
            this.clearFiles();
            return;
        }

        if (validFiles.length > this.limit) {
            this.alertService.error('Lỗi', `Tải lên tối đa ${this.limit} file.`);
            this.clearFiles();
            return;
        }
        this.onChange.emit(validFiles);
    }

    clearFiles() {
        setTimeout(() => {
            const fileInput = document.querySelector('input[type=file]') as HTMLInputElement;
            if (fileInput) {
                fileInput.value = null; // Clear selected files
            }
        }, 1000);
    }

    private getAcceptTypes(type: string): string {
        switch (type) {
            case 'image':
                return 'image/*';
            case 'pdf':
                return 'application/pdf';
            case 'excel':
                return 'application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
            case 'word':
                return 'application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document';
            default:
                return '*/*';
        }
    }

    private isValidType(file: File): boolean {
        const mimeTypes = this.getMimeTypes(this.type);
        return mimeTypes.includes(file.type);
    }

    private getMimeTypes(type: string): string[] {
        switch (type) {
            case 'image':
                return ['image/jpeg', 'image/png', 'image/gif', 'image/bmp'];
            case 'pdf':
                return ['application/pdf'];
            case 'excel':
                return [
                    'application/vnd.ms-excel',
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                ];
            case 'word':
                return [
                    'application/msword',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                ];
            default:
                return [];
        }
    }
}
