import { canAuthorize } from '../../../../core/auth/auth.guard';

export const AcceptanceRouting = {
    path: 'report/acceptance-document',
    title: '<PERSON><PERSON> sơ nghiệm thu',
    children: [
        {
            path: '',
            title: '<PERSON><PERSON> sơ nghiệm thu',
            canActivate: [canAuthorize],
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'ROLE_QC_SUBPM'] },
            loadComponent: () => import('./acceptance.document').then((c) => c.AcceptanceDocumentComponent),
        },
        {
            path: ':id/edit',
            title: 'Chỉnh s<PERSON><PERSON> hồ sơ nghiệm thu',
            canActivate: [canAuthorize],
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'ROLE_QC_SUBPM'] },
            loadComponent: () => import('./acceptance.document.create').then((c) => c.AcceptanceDocumentCreate),
        },
        {
            path: 'create',
            title: '<PERSON><PERSON><PERSON> <PERSON>ồ sơ nghiệm thu',
            canActivate: [canAuthorize],
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'ROLE_QC_SUBPM'] },
            loadComponent: () => import('./acceptance.document.create').then((c) => c.AcceptanceDocumentCreate),
        }
    ],
};
