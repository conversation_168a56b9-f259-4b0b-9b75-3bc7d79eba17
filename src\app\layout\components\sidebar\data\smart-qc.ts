import { SideBarItem } from '../sidebar.menu.component';

export const SIDE_BAR_SMART_QC: SideBarItem[] = [
    {
        label: 'Trang chủ',
        icon: 'pi pi-fw pi-home',
        routerLink: '/sqc/dashboard',
        authorize: ['ROL<PERSON>_SYSTEM_ADMIN', 'ROLE_QC_PM', 'ROLE_QC_SUBPM', 'ROLE_QC_CUSTOMER'],
    },
    {
        label: 'Quản lý người dùng',
        icon: 'pi pi-fw pi-user',
        authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'ROLE_QC_SUBPM'],
        routerLink: '/sqc/user',
    },
    {
        label: 'Nghiệ<PERSON> vụ',
        icon: 'pi pi-fw pi-list',
        authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'ROLE_QC_SUBPM', 'ROL<PERSON>_QC_CUSTOMER'],
        items: [
            {
                label: 'Quản lý mẫu kiểm tra',
                routerLink: '/sqc/template',
                authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'ROLE_QC_SUBPM'],
            },
            {
                label: 'Quản lý dự án',
                routerLink: '/sqc/contract',
                authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'ROLE_QC_SUBPM'],
            },
            {
                label: 'Quản lý phê duyệt',
                routerLink: '/sqc/approve',
                authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'ROLE_QC_SUBPM', 'ROLE_QC_CUSTOMER'],
            },
            {
                label: 'Quản lý lỗi sau triển khai',
                routerLink: '/sqc/maintenance',
                authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'sqc_maintenance_view'],
            },
            {
                label: 'Thông báo',
                routerLink: '/sqc/notification',
                authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'ROLE_QC_SUBPM'],
            },
        ],
    },
    {
        label: 'Báo cáo',
        icon: 'pi pi-fw pi-book',
        authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'ROLE_QC_SUBPM', 'ROLE_QC_CUSTOMER'],
        items: [
            {
                label: 'Báo cáo tiến độ tổng hợp',
                routerLink: '/sqc/report/general-report',
                authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'ROLE_QC_SUBPM', 'ROLE_QC_CUSTOMER'],
            },
            {
                label: 'Hồ sơ nghiệm thu',
                routerLink: '/sqc/report/acceptance-document',
                authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'ROLE_QC_SUBPM'],
            },
        ],
    },
    {
        label: 'Danh mục',
        icon: 'pi pi-fw pi-th-large',
        authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'ROLE_QC_SUBPM'],
        items: [
            {
                label: 'Tỉnh/ thành phố',
                routerLink: '/sqc/area',
                authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'ROLE_QC_SUBPM'],
            },
            {
                label: 'Quận/ huyện',
                routerLink: '/sqc/district',
                authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'ROLE_QC_SUBPM'],
            },
        ],
    },
];
