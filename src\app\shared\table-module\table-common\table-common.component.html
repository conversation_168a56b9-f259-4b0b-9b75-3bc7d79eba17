<div class="table-wrapper">
    <div class="table-container">
        <p-table
            [columns]="columnVisible"
            [value]="data"
            [loading]="loading"
            [scrollable]="false"
            [tableStyle]="combinedTableStyle"
            [styleClass]="combinedTableClass"
            [selection]="rowSelects"
            (selectionChange)="setRowSelection($event)"
            [rows]="pagination.size"
            [dataKey]="fieldSelect"
            [rowsPerPageOptions]="[5, 10, 20, 50]"
            [rowSelectable]="rowSelectableWrapper"
        >
            <ng-template *ngIf="!hideButtonHeader" pTemplate="caption">
                <div class="table-caption tw-p-4" *ngIf="showCaption">
                    <span>{{ name }}</span>
                    <div class="table-caption-toolbar">
                        <ng-container *ngIf="summaryTemplate" [ngTemplateOutlet]="summaryTemplate"></ng-container>
                        <div *ngIf="funcDelete && selectionMode" class="table-caption-toolbar-item">
                            <span class="md:tw-inline-block tw-hidden">
                                Đã chọn:
                                <span style="width: 10px; font-weight: 400">
                                    {{ rowSelects.length || 0 }}
                                </span>
                            </span>

                            <p-button
                                title="Xóa"
                                [appAuthorities]="authoritiesDelete"
                                icon="pi pi-trash"
                                [severity]="rowSelects.length === 0 ? 'secondary' : 'danger'"
                                [outlined]="true"
                                [size]="'small'"
                                [disabled]="rowSelects.length === 0"
                                [ngClass]="{
                                    'qc-disabled': rowSelects.length === 0,
                                    'table-btn-delete': true,
                                }"
                                (click)="deleteRecord()"
                            ></p-button>
                            <div style="border-right: 1px solid #ebeff5; height: 2.25rem; width: 1px"></div>
                            <p-button
                                *ngIf="deleteAllButton"
                                label="Xóa tất cả"
                                [severity]="'danger'"
                                [outlined]="true"
                                [size]="'small'"
                                [ngClass]="{
                                    'table-btn-delete': true,
                                }"
                                (click)="deleteAllRecord()"
                            ></p-button>
                            <div *ngIf="deleteAllButton" style="border-right: 1px solid #ebeff5; height: 2.25rem; width: 1px"></div>
                        </div>
                        <ng-container *ngIf="actionTemplate" [ngTemplateOutlet]="actionTemplate"></ng-container>

                        <div class="table-top-toolbar-item">
                            <p-button title="Lọc cột" icon="pi pi-filter" [outlined]="true" [size]="'small'" (click)="op.toggle($event)"></p-button>
                        </div>

                        <div class="table-caption-toolbar-item" *ngIf="funcDownload">
                            <div style="border-right: 1px solid #ebeff5; height: 2.25rem; width: 1px"></div>
                            <p-button
                                title="Tải xuống"
                                icon="pi pi-download"
                                [outlined]="true"
                                [size]="'small'"
                                (click)="(funcDownload)"
                                [ngClass]="{
                                    'qc-disabled': !funcDownload,
                                    'table-btn-dowload': true,
                                }"
                            ></p-button>
                        </div>
                    </div>
                </div>
            </ng-template>
            <ng-template pTemplate="header" let-columns>
                <tr>
                    <th *ngIf="rowExpandTemplate" style="width: 5rem"></th>
                    <th *ngIf="selectionMode" style="width: 4rem">
                        <ng-container *ngIf="selectionMode === 'multiple'">
                            <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                        </ng-container>
                    </th>
                    <th *ngIf="stt" style="width: 4rem">STT</th>
                    <th
                        *ngFor="let col of columns"
                        (click)="onSort(col)"
                        [style.white-space]="'nowrap'"
                        [style]="col.style"
                        pFrozenColumn
                        [alignFrozen]="col?.fixed"
                        [frozen]="col?.fixed ? true : false"
                        [attr.colspan]="col?.colspan || 1"
                        [ngClass]="{ 'tw-cursor-pointer !tw-bg-blue-50': col?.sort }"
                    >
                        {{ col.header }}
                        <i class="pi pi-sort-amount-down-alt tw-text-blue-500" *ngIf="col?.sort && col?.typeSort === 'asc'"></i>
                        <i class="pi pi-sort-amount-down tw-text-blue-500" *ngIf="col?.sort && col?.typeSort === 'desc'"></i>
                    </th>
                </tr>
                <ng-container *ngIf="headerTemplate" [ngTemplateOutlet]="headerTemplate"></ng-container>
                <ng-container *ngIf="filterTemplate" [ngTemplateOutlet]="filterTemplate"></ng-container>
            </ng-template>

            <ng-template pTemplate="body" let-rowData let-columns="columns" let-rowIndex="rowIndex" let-expanded="expanded">
                <tr
                    [ngClass]="{ 'bg-red-200': isWarning(rowData) }"
                    [pSelectableRowIndex]="rowIndex"
                    [pSelectableRow]="rowData"
                    [pExpandableRow]="rowExpandTemplate ? true : false"
                    class="tw-h-fit"
                >
                    <td *ngIf="rowExpandTemplate">
                        <p-button
                            type="button"
                            pRipple
                            [pRowToggler]="rowData"
                            [text]="true"
                            [rounded]="true"
                            [plain]="true"
                            [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"
                        />
                    </td>
                    <td *ngIf="selectionMode" style="width: 4rem">
                        <ng-container *ngIf="selectionMode === 'multiple'; else radioBtn">
                            <p-tableCheckbox [value]="rowData" [hidden]="!rowSelectable(rowData)" />
                        </ng-container>
                        <ng-template #radioBtn>
                            <p-tableRadioButton [value]="rowData" [hidden]="!rowSelectable(rowData)" />
                        </ng-template>
                    </td>
                    <td *ngIf="stt" style="width: 4rem" [rowspan]="">
                        {{ pagination.page * pagination.size + rowIndex + 1 }}
                    </td>
                    <ng-container *ngFor="let col of columns">
                        <td
                            *ngIf="!col.bodyWrapper; else bodyWrapper"
                            [style.white-space]="'nowrap'"
                            [style.overflow]="'hidden'"
                            [style.text-overflow]="'ellipsis'"
                            [style]="col.style"
                            pFrozenColumn
                            [alignFrozen]="col?.fixed"
                            [frozen]="col?.fixed ? true : false"
                        >
                            <ng-container [ngSwitch]="true">
                                <!-- Check if there's a template for the column -->
                                <ng-container *ngSwitchCase="!!col.body">
                                    <ng-container
                                        [ngTemplateOutlet]="col.body"
                                        [ngTemplateOutletContext]="{
                                            $implicit: rowData,
                                        }"
                                    ></ng-container>
                                </ng-container>
                                <!-- Default content rendering -->
                                <ng-container *ngSwitchCase="col.type === 'date'">
                                    <!-- Format the date if the column is marked as a date -->
                                    {{ rowData[col.field] | date: col.format }}
                                </ng-container>
                                <ng-container *ngSwitchCase="col.type === 'currency'">
                                    <!-- Format the number if the column is marked as currency -->
                                    {{ rowData[col.field] | currency: 'VND' : 'symbol' : '1.0-4' }}
                                </ng-container>
                                <ng-container *ngSwitchCase="col.type === 'number'">
                                    <!-- Format the number if the column is marked as currency -->
                                    {{ rowData[col.field] | number }}
                                </ng-container>
                                <!-- Default content rendering -->
                                <ng-container *ngSwitchCase="col.type === 'link'">
                                    <a [routerLink]="getLink(col.url, rowData)">{{ rowData[col.field] }}</a>
                                </ng-container>
                                <ng-container *ngSwitchCase="col.type === 'newTab'">
                                    <a [href]="rowData[col.field]" target="_blank" rel="noopener noreferrer">{{ rowData[col.field] }}</a>
                                </ng-container>
                                <ng-container *ngSwitchDefault>
                                    {{ rowData[col.field] }}
                                </ng-container>
                            </ng-container>
                        </td>
                        <ng-template #bodyWrapper>
                            <ng-container
                                [ngTemplateOutlet]="col.bodyWrapper"
                                [ngTemplateOutletContext]="{
                                    $implicit: rowData,
                                }"
                            ></ng-container>
                        </ng-template>
                    </ng-container>
                </tr>
            </ng-template>
            <ng-template pTemplate="rowexpansion" let-data>
                <tr>
                    <td [attr.colspan]="columnTable.length + 2" style="padding: 0 !important">
                        <ng-container
                            *ngIf="rowExpandTemplate"
                            [ngTemplateOutlet]="rowExpandTemplate"
                            [ngTemplateOutletContext]="{ $implicit: data }"
                        ></ng-container>
                    </td>
                </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="30" class="text-center">Không có dữ liệu</td>
                </tr>
            </ng-template>
        </p-table>
    </div>
    <app-paginator
        [tableId]="tableId"
        [totalCount]="pagination.totalCount"
        [currentPage]="pagination.page + 1"
        [totalPages]="pagination.totalPage"
        [refetch]="pagination.refetch"
        [size]="pagination.size"
    ></app-paginator>
</div>

<p-overlayPanel #op>
    <p-table [value]="columnAll" [selection]="columnChoose" (selectionChange)="setColumnSelection($event)" [scrollable]="true" scrollHeight="500px">
        <ng-template pTemplate="header">
            <tr>
                <th>
                    <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                </th>

                <th>
                    <!-- <p-columnFilter field="header" matchMode="equals" [showMenu]="false">
            <ng-template
              pTemplate="filter"
              let-value
              let-filter="filterCallback"
            >
              <p-dropdown
                [ngModel]="value"
                (onChange)="filter($event.value)"
                [options]="columnVisible"
                optionLabel="header"
                placeholder="Select One"
                [showClear]="true"
              ></p-dropdown>
            </ng-template>
          </p-columnFilter> -->
                    Tất cả
                </th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rowData>
            <tr>
                <td style="font-size: 14px; padding: 8px 12px">
                    <p-tableCheckbox [value]="rowData" [hidden]="rowData.default" />
                </td>
                <td style="font-size: 14px; padding: 8px 12px">
                    {{ rowData['header'] }}
                </td>
            </tr>
        </ng-template>
    </p-table>
</p-overlayPanel>
