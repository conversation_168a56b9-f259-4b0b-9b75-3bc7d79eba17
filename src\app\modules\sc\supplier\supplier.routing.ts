import { canAuthorize } from '../../../core/auth/auth.guard';

export const SupplierRouting = {
    path: '',
    title: '<PERSON><PERSON><PERSON><PERSON> lý nhà cung cấp',
    children: [
        {
            path: 'supplier-type',
            children: [
                {
                    path: '',
                    title: 'Loại nhà cung cấp',
                    data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_supplier_type_view'] },
                    canActivate: [canAuthorize],
                    loadComponent: () =>
                        import('../supplier/supplier-type/supplier-type.component').then(
                            (c) => c.SupplierTypeComponent,
                        ),
                },
                {
                    path: ':id',
                    title: 'Chi tiết loại nhà cung cấp',
                    canActivate: [canAuthorize],
                    data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_supplier_type_view', 'sc_supplier_type_edit'] },
                    loadComponent: () =>
                        import('../supplier/supplier-type/supplier-type.edit.component').then(
                            (c) => c.SupplierTypeEditComponent,
                        ),
                },
            ],
        },
        {
            path: 'supplier-infor',
            canActivate: [canAuthorize],
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_supplier_view'] },
            children: [
                {
                    path: '',
                    title: 'Danh sách nhà cung cấp',
                    loadComponent: () =>
                        import('../../sc/supplier/infor/list.component').then((c) => c.SupplierListComponent),
                },
                {
                    path: ':id',
                    title: 'Chi tiết nhà cung cấp',
                    data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_supplier_view', 'sc_supplier_edit'] },
                    loadComponent: () =>
                        import('../../sc/supplier/infor/detail.component').then((c) => c.SupplierDetailComponent),
                },
                {
                    path: 'create',
                    title: 'Tạo nhà cung cấp',
                    data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_supplier_view', 'sc_supplier_edit'] },

                    loadComponent: () =>
                        import('../../sc/supplier/infor/detail.component').then((c) => c.SupplierDetailComponent),
                },
            ],
        },
        {
            path: 'supplier-kpi',
            title: 'Cấu hình quy tắc',
            canActivate: [canAuthorize],
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_supplier_kpi_view', 'sc_supplier_kpi_edit'] },
            loadComponent: () => import('./kpi/supplier.kpi.component').then((c) => c.SupplierKpiComponent),
        },
        {
            path: 'criteria-buy',
            title: 'Đánh giá trước mua',
            children: [
                {
                    path: '',
                    data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_criteria_buy_view'] },
                    canActivate: [canAuthorize],
                    loadComponent: () =>
                        import('./criteria-buy/list/criteria-buy.component').then((c) => c.CriteriaBuyComponent),
                },
                {
                    path: 'create',
                    title: 'Tạo loại đánh giá',
                    canActivate: [canAuthorize],
                    data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_criteria_buy_edit'] },
                    loadComponent: () =>
                        import('./criteria-buy/detail/criteria-buy.edit.component').then(
                            (c) => c.CriteriaBuyEditComponent,
                        ),
                },
                {
                    path: ':id',
                    title: 'Chi tiết loại đánh giá',
                    canActivate: [canAuthorize],
                    data: {
                        authorize: [
                            'ROLE_SYSTEM_ADMIN',
                            'sc_criteria_buy_view',
                            'sc_criteria_buy_edit',
                        ],
                    },
                    loadComponent: () =>
                        import('./criteria-buy/detail/criteria-buy.edit.component').then(
                            (c) => c.CriteriaBuyEditComponent,
                        ),
                },
            ],
        },
    ],
};
