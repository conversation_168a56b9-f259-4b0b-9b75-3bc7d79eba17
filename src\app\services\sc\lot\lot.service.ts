import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { BoGoodsArrive, BoPayTax, Lot, LotCustom, LotFilter, PaymentDTO } from '../../../models/interface/sc';
import { ParamsTable } from 'src/app/shared/table-module/table.common.service';
import { ApiResponse } from 'src/app/models/interface';

@Injectable()
export class LotService extends BaseService<Lot> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/lot');
    }

    getPageTableNative({ pageable = '&page=0&size=10' }: ParamsTable, body: LotFilter) {
        return this.http.post<Lot[]>(`/sc/api/lot/search-native?${pageable}`, body, {
            observe: 'response',
        });
    }

    importFile(files: File[]) {
        const formData: FormData = new FormData();
        files.forEach((file) => formData.append('files', file)); // Append each file separately

        return this.http.post<ApiResponse>('/sc/api/lot/import-file', formData);
    }

    stateOneUpdate(lot: Lot) {
        return this.http.post<Lot>('/sc/api/lot/update-detail', lot);
    }

    stateDeliveryUpdate(lot: Lot) {
        return this.http.post<Lot>('/sc/api/lot/update-delivery', lot);
    }

    stateFourUpdate(body: LotCustom) {
        return this.http.post<Lot>('/sc/api/lot/update-lot-customs', body);
    }

    stateFiveUpdate(boPayTax: BoPayTax) {
        return this.http.post<Lot>('/sc/api/lot/update-bo-pay-tax', boPayTax);
    }

    stateSixUpdate(body: BoGoodsArrive) {
        return this.http.post<Lot>('/sc/api/lot/update-bo-good-arrive', body);
    }

    getInfoPaymentLot(lotId: number) {
        return this.http.get<PaymentDTO>('/sc/api/lot/get-info-payment', {
            params: { lotId },
        });
    }

    sendNotification(
        lotId: number,
        body: {
            receiverIds: number[]; // Tập hợp Id người nhận
            content: string; // Nội dung
        },
    ) {
        return this.http.post(`/sc/api/lot/notification/${lotId}`, body);
    }
}
