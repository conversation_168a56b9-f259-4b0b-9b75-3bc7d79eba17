import { CommonModule, Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { CheckboxModule } from 'primeng/checkbox';
import { InputTextModule } from 'primeng/inputtext';
import { PasswordModule } from 'primeng/password';
import { AuthService } from 'src/app/core/auth/auth.service';
import { MessagesModule } from 'primeng/messages';
import { DialogModule } from 'primeng/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { InputValidationComponent } from 'src/app/shared/components/input-validation/input.validation.component';
import { LoadingService } from 'src/app/shared/services/loading.service';

@Component({
    selector: 'app-login',
    templateUrl: './login.component.html',
    styles: [
        `
            :host ::ng-deep .pi-eye,
            :host ::ng-deep .pi-eye-slash {
                transform: scale(1.6);
                margin-right: 1rem;
                color: var(--primary-color) !important;
            }
        `,
    ],
    standalone: true,
    imports: [
        CommonModule,
        ButtonModule,
        CheckboxModule,
        InputTextModule,
        FormsModule,
        PasswordModule,
        DialogModule,
        MessagesModule,
        InputValidationComponent,
    ],
})
export class LoginComponent implements OnInit {
    account = {
        rememberMe: true,

        password: '',

        email: '',

        visible: false,
    };

    isSendEmailResetPasswordSuccess: boolean = false;

    redirectURL: string;

    constructor(
        private router: Router,
        private activeRoute: ActivatedRoute,
        private auth: AuthService,
        private messageService: MessageService,
        private loadingService: LoadingService,
        private location: Location,
    ) {}
    ngOnInit(): void {
        this.redirectURL = this.activeRoute.snapshot.queryParamMap.get('redirectURL');
        if (this.redirectURL !== null && this.redirectURL.includes('login')) this.redirectURL = '';
        if (this.auth.userObserver.getValue()) {
            this.location.back();
        }
    }

    onLogin() {
        this.messageService.clear();
        if (!this.account.email || !this.account.password) {
            return;
        }

        // if (this.account.password.length < 8 || this.account.password.length > 32) {
        //     this.messageService.add({ severity: 'error', detail: 'Mật khẩu tối thiểu 8 ký tự' });
        //     return;
        // }

        setTimeout(() => this.loadingService.show());
        this.auth
            .login({
                account: this.account.email,
                password: this.account.password,
                rememberMe: this.account.rememberMe,
                email: null,
            })
            .subscribe({
                next: (res) => {
                    this.auth.authenticateSuccess(res.id_token);
                    this.auth.userObserver.subscribe({
                        next: (user) => {
                            if (user) {
                                this.router.navigateByUrl(this.redirectURL || '');
                            }
                        },
                    });
                    this.loadingService.hide();
                },
                error: () => {
                    this.messageService.add({ severity: 'error', detail: 'Sai mật khẩu' });
                    this.loadingService.hide();
                },
            });
    }

    onSubmitForgotPassword() {
        const fullDomain = window.location.origin;
        this.auth
            .forgotPassword({
                email: this.account.email,
                domain: fullDomain,
            })
            .subscribe({
                next: () => {
                    this.isSendEmailResetPasswordSuccess = true;
                },
                error: (error) => {
                    console.error(error);
                },
            });
    }
}
