<div class="tw-bg-white tw-space-y-6 tw-border tw-border-gray-200 tw-p-6 tw-shadow-sm tw-rounded-md">
    <ol class="tw-ms-3 tw-border-s tw-border-gray-200 tw-p-0 tw-grid" [ngClass]="direction === 'horizontal' ? 'tw-grid-flow-col ' : ' tw-grid-flow-row  '">
        <ng-container *ngFor="let item of items; let i = index">
            <li
                class="tw-relative tw-list-none tw-flex tw-gap-4"
                [ngClass]="{
                    'tw-flex-col tw-pr-8': direction === 'horizontal',
                    'tw-flex-row tw-pb-8 ': direction !== 'horizontal',
                    'li-horizontal': i !== items.length - 1 && direction === 'horizontal',
                    'li-vertical': i !== items.length - 1 && direction !== 'horizontal',
                }"
            >
                <span
                    *ngIf="indexActive >= i"
                    style="z-index: 2"
                    class="tw-bg-blue-200 tw-p-3 tw-h-8 tw-w-8 tw-flex tw-items-center tw-justify-center tw-rounded-full tw-ring-8 tw-ring-white"
                    [ngClass]="{
                        '!tw-bg-green-400': activeValue === item.value,
                        'tw-cursor-pointer': !item.disabled,
                        'tw-cursor-not-allowed': item.disabled,
                    }"
                    (click)="statusClick(item)"
                >
                    <i class="pi pi-check" [ngStyle]="{ color: activeValue === item.value ? '#fff' : 'var(--primary-500)' }"></i>
                </span>

                <span
                    *ngIf="indexActive < i"
                    style="z-index: 2"
                    class="tw-p-3 tw-h-8 tw-w-8 tw-flex tw-items-center tw-justify-center tw-rounded-full tw-bg-gray-200 tw-ring-8 tw-ring-white"
                    [ngClass]="{
                        '!tw-bg-green-400': activeValue === item.value,
                        'tw-cursor-pointer': !item.disabled,
                        'tw-cursor-not-allowed': item.disabled,
                    }"
                    (click)="statusClick(item)"
                >
                    <i [ngClass]="getClasses(item)" [ngStyle]="{ color: activeValue === item.value ? '#fff' : 'var(--surface-500)' }"></i>
                </span>
                <div>
                    <h4
                        class="!tw-text-lg tw-font-semibold tw-px-3"
                        [ngClass]="{
                            '!tw-text-blue-500': indexActive >= i,
                            '!tw-text-gray-900': indexActive < i,
                        }"
                    >
                        {{ item.name }}
                    </h4>
                    <p
                        *ngIf="item.des"
                        class="tw-text-sm tw-font-normal tw-text-gray-500"
                        [ngClass]="{
                            '!tw-text-blue-500': indexActive >= i,
                            '!tw-text-gray-500': indexActive < i,
                        }"
                    >
                        {{ item.des }}
                    </p>
                </div>
            </li>
        </ng-container>
    </ol>
</div>
