import { Component, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { Observable, of } from 'rxjs';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ToastModule } from 'primeng/toast';
import { CommonModule } from '@angular/common';
import { ConfirmationService } from 'primeng/api';
import { AppTopBarComponent } from '../components/topbar/app.topbar.component';
import { AppSidebarMenuComponent } from '../components/sidebar/sidebar.menu.component';
import { LayoutService, SideBarState } from '../service/app.layout.service';
import { Module } from 'src/app/models/constant';

@Component({
    selector: 'app-layout',
    templateUrl: './app.layout.component.html',
    styleUrls: ['./app.layout.style.scss'],
    standalone: true,
    imports: [
        CommonModule,
        RouterOutlet,
        ToastModule,
        ConfirmDialogModule,
        AppTopBarComponent,
        AppSidebarMenuComponent,
    ],
    providers: [ConfirmationService],
})
export class AppLayoutComponent implements OnInit {
    loading$: Observable<boolean> = of(false);

    constructor(private layoutService: LayoutService) {}

    sideBarState: SideBarState = {
        items: [],
        state: window.innerWidth <= 768 ? 'close' : 'expand',
        device: window.innerWidth <= 768 ? 'mobile' : 'desktop',
    };
    module: Module;

    ngOnInit() {
        this.layoutService.sideBarState.subscribe((state) => {
            this.sideBarState = state;
        });

        this.layoutService.module.subscribe((state) => {
            this.module = state;
        });
    }

    hiddenMark() {
        this.layoutService.changeSideBarState();
    }
}
