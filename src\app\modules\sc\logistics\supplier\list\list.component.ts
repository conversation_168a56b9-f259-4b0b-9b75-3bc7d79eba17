import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { TagModule } from 'primeng/tag';
import { SupplierTypeService } from 'src/app/services/sc/supplier/suppiler-type.service';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { TableCommonService } from 'src/app/shared/table-module/table.common.service';
import { AlertService } from '../../../../../shared/services/alert.service';
import { LogisticsService } from '../../../../../services/sc/logistics/logistics.service';
import { PopupComponent } from '../../../../../shared/components/popup/popup.component';
import { FileService } from '../../../../../shared/services/file.service';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { Column, EventPopupSubmit } from 'src/app/models/interface';
import { Logistics } from 'src/app/models/interface/sc';
import { TABLE_KEY } from 'src/app/models/constant';
import { DateUtils } from 'src/app/utils/date-utils';
import { LOGISTICS_TYPE_MAP, MAP_STATE_LOGISTICS, STATE_LOGISTICS } from 'src/app/models/constant/sc';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';

@Component({
    selector: 'app-sc-logistics-list',
    templateUrl: './list.component.html',
    styleUrls: ['./list.component.scss'],
    standalone: true,
    imports: [RouterLink, ButtonModule, CommonModule, TagModule, SubHeaderComponent, PopupComponent, TableCommonModule, HasAnyAuthorityDirective],
    providers: [SupplierTypeService, LogisticsService],
})
export class ListComponent implements OnInit, AfterViewInit {
    // Tab express delivery
    @ViewChild('templateStateLogistics') templateStateLogistics: TemplateRef<Element>;
    yearRange = DateUtils.generateYearRange(2000);
    mapSupplierType: { [key: number]: string } = {};
    stateEXPRESS_DELIVERY: QueryObserverBaseResult<Logistics[]>;
    columnsEXPRESS_DELIVERY: Column[] = [];
    tableIdEXPRESS_DELIVERY: string = TABLE_KEY.LOGISTICS_EXPRESS_DELIVERY;
    protected readonly STATE_LOGISTICS = STATE_LOGISTICS;
    protected readonly MAP_STATE_LOGISTICS = MAP_STATE_LOGISTICS;
    // End tab

    // tab forwarder
    stateForwarder: QueryObserverBaseResult<Logistics[]>;
    columnsForwarder: Column[] = [];
    tableIdForwarder: string = TABLE_KEY.LOGISTICS_FORWARDER;

    // tab insurance
    stateInsurance: QueryObserverBaseResult<Logistics[]>;
    columnsInsurance: Column[] = [];
    tableIdInsurance: string = TABLE_KEY.LOGISTICS_INSURANCE;

    // tab other

    stateOther: QueryObserverBaseResult<Logistics[]>;
    columnsOther: Column[] = [];
    tableIdOther: string = TABLE_KEY.LOGISTICS_OTHER;

    mapTableIdType = {
        [TABLE_KEY.LOGISTICS_FORWARDER]: 0,
        [TABLE_KEY.LOGISTICS_INSURANCE]: 1,
        [TABLE_KEY.LOGISTICS_EXPRESS_DELIVERY]: 2,
        [TABLE_KEY.LOGISTICS_OTHER]: 3,
    };

    constructor(
        private tableCommonService: TableCommonService,
        private alertService: AlertService,
        private logisticsService: LogisticsService,
        private fileService: FileService,
        private supplierTypeService: SupplierTypeService,
        private activatedRoute: ActivatedRoute,
        private loadingService: LoadingService,
    ) {}

    ngOnInit() {
        this.tableCommonService
            .init<Logistics>({
                tableId: this.tableIdEXPRESS_DELIVERY,
                queryFn: (filter) => this.logisticsService.getPageTableCustom(filter),
                configFilterRSQL: {
                    type: 'Number',
                    fullName: 'Text',
                    shortName: 'Text',
                    serviceProvided: 'Text',
                    productSupplied: 'Text',
                    address: 'Text',
                    national: 'Text',
                    website: 'TextURL',
                    contactPerson: 'Text',
                    position: 'Text',
                    email: 'Text',
                    phone: 'Text',
                    tradingYear: 'SetLong',
                    contractStartTime: 'DateRange',
                    contractEndTime: 'DateRangeInfinity',
                    status: 'Number',
                },
                defaultParamsRSQL: {
                    type: LOGISTICS_TYPE_MAP.EXPRESS_DELIVERY,
                },
            })
            .subscribe({
                next: (res) => {
                    this.stateEXPRESS_DELIVERY = res;
                },
                error: (error) => {
                    this.alertService.handleError(error);
                },
            });

        this.tableCommonService
            .init<Logistics>({
                tableId: this.tableIdForwarder,
                queryFn: (filter) => this.logisticsService.getPageTableCustom(filter),
                configFilterRSQL: {
                    type: 'Number',
                    fullName: 'Text',
                    shortName: 'Text',
                    serviceProvided: 'Text',
                    productSupplied: 'Text',
                    address: 'Text',
                    national: 'Text',
                    website: 'TextURL',
                    contactPerson: 'Text',
                    position: 'Text',
                    email: 'Text',
                    phone: 'Text',
                    tradingYear: 'SetLong',
                    contractStartTime: 'DateRange',
                    contractEndTime: 'DateRangeInfinity',
                    status: 'Number',
                },
                defaultParamsRSQL: {
                    type: LOGISTICS_TYPE_MAP.FORWARDER,
                },
            })
            .subscribe({
                next: (res) => {
                    this.stateForwarder = res;
                },
                error: (error) => {
                    this.alertService.handleError(error);
                },
            });
        this.tableCommonService
            .init<Logistics>({
                tableId: this.tableIdInsurance,
                queryFn: (filter) => this.logisticsService.getPageTableCustom(filter),
                configFilterRSQL: {
                    type: 'Number',
                    fullName: 'Text',
                    shortName: 'Text',
                    serviceProvided: 'Text',
                    productSupplied: 'Text',
                    address: 'Text',
                    national: 'Text',
                    website: 'TextURL',
                    contactPerson: 'Text',
                    position: 'Text',
                    email: 'Text',
                    phone: 'Text',
                    tradingYear: 'SetLong',
                    contractStartTime: 'DateRange',
                    contractEndTime: 'DateRangeInfinity',
                    status: 'Number',
                },
                defaultParamsRSQL: {
                    type: LOGISTICS_TYPE_MAP.INSURANCE,
                },
            })
            .subscribe({
                next: (res) => {
                    this.stateInsurance = res;
                },
                error: (error) => {
                    this.alertService.handleError(error);
                },
            });

        this.tableCommonService
            .init<Logistics>({
                tableId: this.tableIdOther,
                queryFn: (filter) => this.logisticsService.getPageTableCustom(filter),
                configFilterRSQL: {
                    type: 'Number',
                    fullName: 'Text',
                    shortName: 'Text',
                    serviceProvided: 'Text',
                    productSupplied: 'Text',
                    address: 'Text',
                    national: 'Text',
                    website: 'TextURL',
                    contactPerson: 'Text',
                    position: 'Text',
                    email: 'Text',
                    phone: 'Text',
                    tradingYear: 'SetLong',
                    contractStartTime: 'DateRange',
                    contractEndTime: 'DateRangeInfinity',
                    status: 'Number',
                },
                defaultParamsRSQL: {
                    type: LOGISTICS_TYPE_MAP.OTHER,
                },
            })
            .subscribe({
                next: (res) => {
                    this.stateOther = res;
                },
                error: (error) => {
                    this.alertService.handleError(error);
                },
            });
        this.loadSupplierType();
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.columnsEXPRESS_DELIVERY = [
                {
                    header: 'Tên đầy đủ',
                    field: 'fullName',
                    type: 'link',
                    url: '/sc/logistics/{id}',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                    default: true,
                },
                {
                    header: 'Viết tắt',
                    field: 'shortName',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Dịch vụ cung cấp',
                    field: 'serviceProvided',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Địa chỉ',
                    field: 'address',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Quốc gia',
                    field: 'national',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Website',
                    field: 'website',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Liên hệ',
                    field: 'contactPerson',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Chức vụ',
                    field: 'position',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Email',
                    field: 'email',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'SĐT',
                    field: 'phone',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Năm bắt đầu giao dịch',
                    field: 'tradingYear',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Ngày hợp đồng gần nhất',
                    field: 'contractStartTime',
                    type: 'date',
                    format: 'dd/MM/yyyy',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Trạng thái',
                    field: 'status',
                    body: this.templateStateLogistics,
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Thời hạn hợp đồng',
                    fallBackValue: 'Không thời hạn',
                    field: 'contractEndTime',
                    type: 'date',
                    format: 'dd/MM/yyyy',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
            ];

            this.columnsForwarder = [
                {
                    header: 'Tên đầy đủ',
                    field: 'fullName',
                    type: 'link',
                    url: '/sc/logistics/{id}',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                    default: true,
                },
                {
                    header: 'Viết tắt',
                    field: 'shortName',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Dịch vụ cung cấp',
                    field: 'serviceProvided',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Địa chỉ',
                    field: 'address',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Quốc gia',
                    field: 'national',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Website',
                    field: 'website',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Liên hệ',
                    field: 'contactPerson',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Chức vụ',
                    field: 'position',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Email',
                    field: 'email',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'SĐT',
                    field: 'phone',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Năm bắt đầu giao dịch',
                    field: 'tradingYear',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Ngày hợp đồng gần nhất',
                    field: 'contractStartTime',
                    type: 'date',
                    format: 'dd/MM/yyyy',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Trạng thái',
                    field: 'status',
                    body: this.templateStateLogistics,
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Thời hạn hợp đồng',
                    fallBackValue: 'Không thời hạn',
                    field: 'contractEndTime',
                    type: 'date',
                    format: 'dd/MM/yyyy',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
            ];

            this.columnsInsurance = [
                {
                    header: 'Tên đầy đủ',
                    field: 'fullName',
                    type: 'link',
                    url: '/sc/logistics/{id}',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                    default: true,
                },
                {
                    header: 'Viết tắt',
                    field: 'shortName',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Dịch vụ cung cấp',
                    field: 'serviceProvided',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Địa chỉ',
                    field: 'address',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Quốc gia',
                    field: 'national',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Website',
                    field: 'website',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Liên hệ',
                    field: 'contactPerson',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Chức vụ',
                    field: 'position',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Email',
                    field: 'email',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'SĐT',
                    field: 'phone',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Năm bắt đầu giao dịch',
                    field: 'tradingYear',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Ngày hợp đồng gần nhất',
                    field: 'contractStartTime',
                    type: 'date',
                    format: 'dd/MM/yyyy',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Trạng thái',
                    field: 'status',
                    body: this.templateStateLogistics,
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Thời hạn hợp đồng',
                    fallBackValue: 'Không thời hạn',
                    field: 'contractEndTime',
                    type: 'date',
                    format: 'dd/MM/yyyy',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
            ];

            this.columnsOther = [
                {
                    header: 'Tên đầy đủ',
                    field: 'fullName',
                    type: 'link',
                    url: '/sc/logistics/{id}',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                    default: true,
                },
                {
                    header: 'Viết tắt',
                    field: 'shortName',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Dịch vụ cung cấp',
                    field: 'serviceProvided',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Địa chỉ',
                    field: 'address',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Quốc gia',
                    field: 'national',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Website',
                    field: 'website',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Liên hệ',
                    field: 'contactPerson',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Chức vụ',
                    field: 'position',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Email',
                    field: 'email',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'SĐT',
                    field: 'phone',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Năm bắt đầu giao dịch',
                    field: 'tradingYear',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Ngày hợp đồng gần nhất',
                    field: 'contractStartTime',
                    type: 'date',
                    format: 'dd/MM/yyyy',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Trạng thái',
                    field: 'status',
                    body: this.templateStateLogistics,
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
                {
                    header: 'Thời hạn hợp đồng',
                    fallBackValue: 'Không thời hạn',
                    field: 'contractEndTime',
                    type: 'date',
                    format: 'dd/MM/yyyy',
                    style: {
                        minWidth: '200px',
                        maxWidth: '400px',
                    },
                },
            ];
        });
    }

    get tableActiveId() {
        const queryParams = this.activatedRoute.snapshot.queryParams;

        return this.mapTableIdType[queryParams['tableId']];
    }

    loadSupplierType() {
        this.supplierTypeService.advancedGroup({}).subscribe({
            next: (response) => {
                response.body.filter((item) => {
                    this.mapSupplierType[item.id] = item.displayName;
                });
            },
        });
    }

    getSeverity(state: number): string {
        switch (state) {
            case 1:
                return 'danger';
            case 0:
                return 'success';
            default:
                return 'info';
        }
    }

    deleteSelected = (ids: number[]) => {
        return this.logisticsService.batchDelete(ids);
    };

    exportSupplierLogistics(event: EventPopupSubmit<unknown>) {
        this.loadingService.show();
        this.logisticsService.exportSupplierLogistics().subscribe({
            next: (res) => {
                this.loadingService.hide();
                this.fileService.downLoadFileByService(res.url, '/sc/api');
                event.close();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }
}
