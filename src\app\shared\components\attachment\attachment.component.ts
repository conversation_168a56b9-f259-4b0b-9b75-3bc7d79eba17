import { Component, inject, Input, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Attachment } from 'src/app/models/interface';
import { FileService } from '../../services/file.service';

@Component({
    selector: 'app-attachment',
    standalone: true,
    imports: [CommonModule],
    templateUrl: './attachment.component.html',
    styleUrls: ['./attachment.component.scss'],
})
export class AttachmentComponent implements OnChanges {
    @Input() attachment: Attachment;
    @Input() service: '/auth/api' | '/smart-qc/api' | '/sc/api' = '/sc/api';

    fileSevice = inject(FileService);

    download() {
        if (!this.attachment) return;

        this.fileSevice.downLoadFileByService(this.attachment.url, this.service);
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['attachment'].currentValue) {
            if (this.attachment && this.attachment.type) return;
            this.attachment.type = this.getFileTypeFromName(this.attachment.name);
        }
    }

    getFileTypeFromName(fileName: string): number {
        if (!fileName) return 0; // Trả về 0 nếu không có tên file

        const extension = fileName.split('.').pop()?.toLowerCase(); // Lấy phần mở rộng file

        switch (extension) {
            case 'pdf':
                return 1; // PDF
            case 'doc':
            case 'docx':
                return 2; // WORD
            case 'xls':
            case 'xlsx':
                return 3; // EXCEL
            default:
                return 0; // Giá trị mặc định nếu không khớp
        }
    }
}
