<app-sub-header [items]="[{ label: '<PERSON> dõi yêu cầu vận chuyển', url: '/sc/lot' }, { label: oldLot?.code ?? '' }]" [action]="action"></app-sub-header>

<ng-template #action>
    <p-button label="Lưu" (click)="form.handleSubmit()" severity="success" size="small" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_lot_edit']" />
    <p-button label="Đóng" routerLink="/sc/lot" severity="secondary" size="small" />
</ng-template>

<div class="tw-p-4">
    <app-form #form *ngIf="formGroup" [formGroup]="formGroup" layout="vertical" (onSubmit)="onSubmit($event)">
        <p-panel header="Thông tin booking" [toggleable]="true">
            <div class="tw-grid lg:tw-grid-cols-2 tw-grid-cols-1 tw-gap-4">
                <app-form-item label="Mã BO">
                    <input type="text" class="tw-w-full" pInputText formControlName="code" placeholder="Hệ thống tự sinh" />
                </app-form-item>

                <app-form-item label="Tổng giá trị shipment dự kiến (USD)">
                    <app-inputNumber class="tw-w-full" formControlName="shipmentValue" mode="decimal"></app-inputNumber>
                </app-form-item>

                <app-form-item label="Phân loại">
                    <app-filter-table
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'label',
                            options: TYPE_SHIPPING,
                            filterLocal: true,
                        }"
                        formControlName="type"
                    ></app-filter-table>
                </app-form-item>

                <app-form-item label="Điều kiện thanh toán" class="tw-row-span-2">
                    <textarea rows="6" pInputTextarea class="tw-w-full" formControlName="paymentCondition"></textarea>
                </app-form-item>

                <app-form-item label="Tên hàng">
                    <input type="text" class="tw-w-full" pInputText formControlName="goodsName" />
                </app-form-item>

                <app-form-item label="Tên NCC">
                    <input type="text" class="tw-w-full" pInputText formControlName="supplierName" />
                </app-form-item>

                <app-form-item label="Số PO">
                    <input type="text" class="tw-w-full" pInputText formControlName="poNumber" />
                </app-form-item>

                <app-form-item label="Địa chỉ NCC">
                    <input type="text" class="tw-w-full" pInputText formControlName="supplierAddress" />
                </app-form-item>

                <app-form-item label="Số thứ tự shipment của PO">
                    <app-inputNumber class="tw-w-full" formControlName="indexShipment"></app-inputNumber>
                </app-form-item>

                <app-form-item label="Thông tin NCC" class="tw-row-span-2">
                    <textarea rows="6" pInputTextarea class="tw-w-full" formControlName="supplierInfo"></textarea>
                </app-form-item>

                <app-form-item label="Mã kế toán/Mã vụ việc">
                    <input type="text" class="tw-w-full" pInputText formControlName="accountingCode" />
                </app-form-item>

                <app-form-item label="Khối lượng tổng của lô hàng (kg)">
                    <app-inputNumber class="tw-w-full" formControlName="totalWeight" mode="decimal" maxlength="20"></app-inputNumber>
                </app-form-item>

                <app-form-item label="Thời gian hàng hóa ready tại kho">
                    <p-calendar
                        [showButtonBar]="true"
                        placeholder="dd/MM/yyyy"
                        [showIcon]="true"
                        dateFormat="dd/mm/yy"
                        class="tw-w-full"
                        [formControl]="formGroup.get('readyDateCustom')"
                    ></p-calendar>
                </app-form-item>

                <div class="tw-grid tw-grid-flow-col tw-gap-4">
                    <app-form-item label="Số kiện của lô hàng (carton/pallet)">
                        <app-inputNumber class="tw-w-full" formControlName="packageNumber" mode="decimal" maxLength="20"></app-inputNumber>
                    </app-form-item>
                    <app-form-item label="&nbsp;" class="tw-col-span-2">
                        <app-autocomplete
                            optionLabel="name"
                            optionValue="id"
                            formControlName="unit"
                            [options]="packageTypes"
                            (onAdd)="handleAddPackagegType($event)"
                            (onDelete)="handleDeletePackagegType($event)"
                            [rowDelete]="rowDelete"
                        ></app-autocomplete>
                    </app-form-item>
                </div>

                <app-form-item [label]="formGroup?.get('type').value === 1 ? 'Thời gian dự kiến xuất khỏi nhà máy' : 'Thời gian yêu cầu về tới nhà máy'">
                    <p-calendar
                        [showButtonBar]="true"
                        placeholder="dd/MM/yyyy"
                        [showIcon]="true"
                        dateFormat="dd/mm/yy"
                        [formControl]="formGroup.get('requiredArrivedDateCustom')"
                        class="tw-w-full"
                    ></p-calendar>
                </app-form-item>

                <app-form-item label="Điều kiện giao hàng">
                    <input type="text" class="tw-w-full" pInputText formControlName="deliveryCondition" />
                </app-form-item>

                <app-form-item label="Phòng/Ban">
                    <app-filter-table
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            rsql: true,
                            url: '/sc/api/department/search',
                            paramForm: 'id',
                        }"
                        formControlName="departmentId"
                    ></app-filter-table>
                </app-form-item>

                <app-form-item label="Thông tin đính kèm">
                    <app-button-group-file
                        simpleUpload=""
                        (onFileSelected)="handleUploadFile($event)"
                        [attachments]="formGroup.getRawValue().attachments"
                        formControlName="attachmentIds"
                        [multiple]="true"
                    ></app-button-group-file>

                    <app-button-group-file
                        *ngIf="formGroup.getRawValue().attachments && formGroup.getRawValue().attachments.length > 0"
                        class="tw-col-span-2"
                        (onFileSelected)="handleUploadFile($event)"
                        [multiple]="true"
                        simpleUpload=""
                        formControlName="attachmentIds"
                    ></app-button-group-file>
                </app-form-item>
                <app-form-item class="tw-col-span-2" label="Người nhận hàng (Consignee)" *ngIf="formGroup?.get('type').value === 1">
                    <textarea rows="5" cols="30" pInputTextarea class="tw-w-full" formControlName="consignee"></textarea>
                </app-form-item>

                <app-form-item class="tw-col-span-2" label="Địa chỉ nhận hàng cuối cùng" *ngIf="formGroup?.get('type').value === 0">
                    <textarea rows="5" cols="30" pInputTextarea class="tw-w-full" formControlName="finalDeliveryAddress"></textarea>
                </app-form-item>
                <app-form-item label="Ghi chú" class="tw-col-span-2">
                    <textarea rows="5" cols="30" pInputTextarea class="tw-w-full" formControlName="note"></textarea>
                </app-form-item>
            </div>
        </p-panel>
        <br />
    </app-form>

    <app-time-line [items]="timelineItems" (onStatusClick)="onStatusSelected($event)" [activeValue]="activeTabState" [value]="oldLot?.state"></app-time-line>
    <br />

    <div [ngSwitch]="activeTabState">
        <div *ngSwitchCase="1">
            <app-lot-state-one [lot]="oldLot" [receivers]="receivers" (onComplete)="onCompleteState($event)"></app-lot-state-one>
        </div>
        <div *ngSwitchCase="2">
            <app-lot-state-two [lot]="oldLot" [receivers]="receivers" (onComplete)="onCompleteState($event)"></app-lot-state-two>
        </div>
        <div *ngSwitchCase="3">
            <app-lot-state-three [lot]="oldLot" [receivers]="receivers" (onComplete)="onCompleteState($event)"></app-lot-state-three>
        </div>
        <div *ngSwitchCase="4">
            <app-lot-state-four [lot]="oldLot" [receivers]="receivers" (onComplete)="onCompleteState($event)"></app-lot-state-four>
        </div>
        <div *ngSwitchCase="5">
            <app-lot-state-five [lot]="oldLot" [receivers]="receivers" (onComplete)="onCompleteState($event)"></app-lot-state-five>
        </div>
        <div *ngSwitchCase="6">
            <app-lot-state-six [lot]="oldLot" [receivers]="receivers" (onComplete)="onCompleteState($event)"></app-lot-state-six>
        </div>
        <div *ngSwitchCase="7">
            <app-lot-state-seven [lot]="oldLot" [receivers]="receivers"></app-lot-state-seven>
        </div>
    </div>
</div>
