import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, OnInit, TemplateRef, ViewChild, inject } from '@angular/core';
import { RouterModule } from '@angular/router';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { isArray, isEmpty } from 'lodash';
import { ConfirmationService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';
import { ChipModule } from 'primeng/chip';
import { catchError, of } from 'rxjs';
import { AuthService } from 'src/app/core/auth/auth.service';
import { TABLE_KEY } from 'src/app/models/constant';
import { Column } from 'src/app/models/interface';
import { ApproveService } from 'src/app/services/smart-qc/masterdata/approve.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { environment } from 'src/environments/environment';
import { listApproveStatus, listApproveStatusPM } from '../../../models/constant/smart-qc';
import { ApproveDTO, ApproveFilter } from '../../../models/interface/smart-qc';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { TableCommonService } from '../../../shared/table-module/table.common.service';
import { TableCommonModule } from '../../../shared/table-module/table.common.module';

@Component({
    selector: 'app-approve',
    standalone: true,
    imports: [
        TableCommonModule,
        CommonModule,
        RouterModule,
        ButtonModule,
        TagModule,
        TooltipModule,
        ChipModule,
        SubHeaderComponent,
    ],
    templateUrl: './approve.component.html',
    styleUrls: ['./approve.component.scss'],
    providers: [ApproveService, TableCommonService],
})
export class ApproveComponent implements OnInit, AfterViewInit {
    @ViewChild('templateStatus') templateStatus: TemplateRef<Element>;
    @ViewChild('templateResponses') templateResponses: TemplateRef<Element>;
    @ViewChild('templateErrors') templateErrors: TemplateRef<Element>;
    @ViewChild('templateAction') templateAction: TemplateRef<Element>;

    optionStatus = listApproveStatus;
    optionStatusPM = listApproveStatusPM;
    approveService = inject(ApproveService);
    tableCommonService = inject(TableCommonService);
    loadingService = inject(LoadingService);
    confirmationService = inject(ConfirmationService);
    alertService = inject(AlertService);
    authService = inject(AuthService);
    state: QueryObserverBaseResult<ApproveDTO[]>;
    columns: Column[] = [];
    tableId: string = TABLE_KEY.APPROVE;
    itemsHeader = [{ label: 'Quản lý phê duyệt' }, { label: 'Danh sách phê duyệt', url: 'approve' }];
    actionHeader: TemplateRef<Element>;
    bodyFilter: Record<string, unknown> = {
        contractIds: null,
        actionIds: null,
    };
    rowSelects: ApproveDTO[] = [];
    ngOnInit() {
        this.tableCommonService
            .init<ApproveDTO>({
                tableId: this.tableId,
                queryFn: (filter, body: ApproveFilter) => this.approveService.getPageTableCustom(filter, body),
                configFilter: [
                    'contractIds',
                    'actionIds',
                    'areaIds',
                    'stationIds',
                    'subPMIds',
                    'employeeIds',
                    'statuses',
                    'startSubmitDate&endSubmitDate',
                    'startSubmitDateEp&endSubmitDateEp',
                    'error',
                    'response',
                    'submitTime',
                ],
                filterUrl: true,
            })
            .subscribe((state) => {
                this.state = state;
            });

        this.tableCommonService.getRowSelect(this.tableId).subscribe((state: ApproveDTO) => {
            if (isArray(state)) {
                this.rowSelects = state;
            }
        });
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.columns = [
                {
                    header: 'Chi tiết',
                    body: this.templateAction,
                    default: true,
                    style: { 'max-width': '5rem' },
                    fixed: 'right',
                },
                { field: 'contractName', header: 'Dự án', default: true },
                { field: 'actionName', header: 'Công việc', style: { 'max-width': '10rem' } },
                { field: 'areaName', header: 'Tỉnh/Tp', style: { 'max-width': '8rem' } },
                { field: 'stationName', header: 'Tên trạm', style: { 'max-width': '20rem' } },
                {
                    field: 'subPM',
                    header: 'SubPM',
                    hide: !this.authService.isAdminOrPM(),
                },
                {
                    field: 'employee',
                    header: 'Đội thi công',
                    hide: !this.authService.isSubPM(),
                },
                {
                    field: 'submitDate',
                    header: 'Ngày gửi',
                    type: 'date',
                    format: 'dd/MM/yyyy',
                    hide: !this.authService.isAdminOrPM(),
                    style: { 'max-width': '8rem' },
                },
                {
                    field: 'submitDateEp',
                    header: 'Ngày gửi',
                    type: 'date',
                    format: 'dd/MM/yyyy',
                    hide: !this.authService.isSubPM(),
                    style: { 'max-width': '8rem' },
                },
                { field: 'status', header: 'Trạng thái', body: this.templateStatus, style: { 'max-width': '5rem' } },
                {
                    field: 'responses',
                    header: this.authService.isAdminOrPM() ? 'Phản hồi cho SubPM' : 'PM phản hồi',
                    body: this.templateResponses,
                },
                {
                    field: 'submitTime',
                    header: 'Số lần gửi',
                    hide: !this.authService.isAdminOrPM(),
                },
                {
                    field: 'errors',
                    header: 'Đánh giá lỗi',
                    hide: !this.authService.isAdminOrPM(),
                    body: this.templateErrors,
                },
            ];
        }, 0);
    }

    getSeverity(state: number): string {
        switch (state) {
            case 4:
                return 'success';
            case 3:
                return 'danger';
            case 2:
                return 'primary';
            case 1:
                return 'danger';
            case 0:
                return 'warning';
            default:
                return 'info';
        }
    }

    getStateText(state: number): string {
        if (this.authService.isAdminOrPM()) {
            switch (state) {
                case 4:
                    return 'Hoàn thành';
                case 3:
                    return 'Từ chối';
                case 2:
                    return 'Chờ duyệt';
                default:
                    return ':)';
            }
        } else {
            switch (state) {
                case 4:
                    return 'Hoàn thành';
                case 3:
                    return 'PM Từ chối';
                case 2:
                    return 'Chờ PM duyệt';
                case 1:
                    return 'Từ chối';
                case 0:
                    return 'Chờ duyệt';
                default:
                    return ':)';
            }
        }
    }

    changeFilter(e, key) {
        this.bodyFilter[key] = e.value;
    }

    acceptMultiByPm() {
        let ids = [];

        if (
            this.tableCommonService.getRowSelect(this.tableId).getValue() &&
            isArray(this.tableCommonService.getRowSelect(this.tableId).getValue())
        ) {
            ids = [].concat(this.tableCommonService.getRowSelect(this.tableId).getValue()).map((e) => e['id']);
        }
        if (isEmpty(ids)) return;

        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn chấp nhận kết quả của các trạm này không',

            icon: 'pi pi-exclamation-triangle',
            acceptIcon: 'none',
            rejectIcon: 'none',
            rejectButtonStyleClass: 'p-button-text',
            acceptButtonStyleClass: 'p-button-outlined',
            acceptLabel: 'Đồng ý',
            rejectLabel: 'Hủy',
            accept: () => {
                this.loadingService.show();
                this.approveService
                    .acceptMultiByPm(ids)
                    .pipe(
                        catchError(() => {
                            this.loadingService.hide();
                            return of(null);
                        }),
                    )
                    .subscribe({
                        next: () => {
                            this.alertService.success('Thành công', 'Phê duyệt thành công');
                            this.loadingService.hide();
                            this.state.refetch();
                        },
                        error: () => {
                            this.loadingService.hide();
                        },
                    });
            },
        });
    }

    exportExcel() {
        this.loadingService.show();
        this.approveService
            .export(this.tableCommonService.getBody(this.tableId))
            .pipe(
                catchError(() => {
                    this.loadingService.hide();
                    return of(null);
                }),
            )
            .subscribe({
                next: (res) => {
                    this.loadingService.hide();
                    const url = environment.HOST_GW + '/smart-qc/api/download?filePath=' + res.message;
                    const a: HTMLAnchorElement = document.createElement('a');
                    a.href = url;
                    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
                    a.download;
                    a.click();
                },
                error: () => {
                    this.loadingService.hide();
                },
            });
    }

    rowSelectable = (rowData: ApproveDTO) => {
        if (this.authService.isAdminOrPM()) {
            return rowData.state === 2;
        } else {
            return false;
        }
    };
}
