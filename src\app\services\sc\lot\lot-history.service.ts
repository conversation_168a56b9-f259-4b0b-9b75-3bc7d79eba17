import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { BoDto, LotHistory } from 'src/app/models/interface/sc';
import { ApiResponse } from 'src/app/models/interface';

@Injectable()
export class LotHistoryService extends BaseService<LotHistory> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/lot-history');
    }

    getLast(lotId: number, type: number) {
        return this.http.get<LotHistory>('/sc/api/lot-history/last', {
            params: {
                lotId,
                type,
            },
        });
    }

    importFile(files: File[]) {
        const formData: FormData = new FormData();
        files.forEach((file) => formData.append('files', file)); // Append each file separately

        return this.http.post<ApiResponse>('/sc/api/lot-history/import-file', formData);
    }

    getInfo(lotId: number) {
        return this.http.get<BoDto>('/sc/api/lot-history/get-info', {
            params: {
                lotId,
            },
        });
    }
}
