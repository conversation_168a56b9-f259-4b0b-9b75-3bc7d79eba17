import { Component, EventEmitter, inject, Input, Output } from '@angular/core';
import { AuthService } from 'src/app/core/auth/auth.service';
import { LazyLoadImageDirective } from '../../directives/lazyLoadImage.directive';
import { NgIf, NgOptimizedImage } from '@angular/common';
import { NgxImageZoomModule } from 'ngx-image-zoom';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { ImageModule } from 'primeng/image';
@Component({
    selector: 'app-image-magnifier',
    templateUrl: './image-magnifier.component.html',
    standalone: true,
    imports: [
        LazyLoadImageDirective,
        NgIf,
        NgxImageZoomModule,
        ButtonModule,
        NgOptimizedImage,
        DialogModule,
        ImageModule,
    ],
    styleUrls: ['./image-magnifier.component.scss'],
})
export class ImageMagnifierComponent {
    isZoomed = false;
    private mouseLeaveListener: () => void;
    @Input() key: string; // Input property for the image URL
    @Input() imageUrl: string; // Input property for the image URL
    @Input() zoomLevel: number = 5; // Input property for the image URL
    @Input() remove: boolean = false;
    @Input() uploadBy: number;
    @Output('onRemove') onRemove: EventEmitter<unknown> = new EventEmitter();

    isRemoved = false;
    loading: boolean = true; // Thuộc tính mới để theo dõi trạng thái tải hình ảnh
    authService = inject(AuthService);

    visible: boolean = false;
    onImageLoad() {
        this.loading = false;
    }

    handleRemove() {
        this.onRemove.emit({ value: this.imageUrl, key: this.key });
        this.isRemoved = true;
    }

    onLoaded(loaded: boolean) {
        this.loading = !loaded;
    }

    showDialog() {
        this.visible = true;
    }
}
