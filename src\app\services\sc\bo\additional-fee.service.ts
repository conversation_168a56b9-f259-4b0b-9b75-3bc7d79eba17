import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { AdditionalFee } from '../../../models/interface/sc';

@Injectable()
export class AdditionalFeeService extends BaseService<AdditionalFee> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/additional-fee');
    }

    deleteByBo(boId: number, name: string) {
        return this.http.delete(`/sc/api/additional-fee/delete-by-bo/${boId}/${name}`);
    }

    updateByBo(body: Array<unknown>) {
        return this.http.post(`/sc/api/additional-fee/update-by-bo`, body);
    }
}
