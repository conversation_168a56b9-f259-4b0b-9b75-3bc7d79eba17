import { AfterContentInit, Component, inject, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { RouterLink } from '@angular/router';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { ButtonModule } from 'primeng/button';
import { TABLE_KEY } from 'src/app/models/constant';
import { MAP_STATE_PROFILE_SUPPLIER, MAP_STATE_SUPPLIER, STATE_PROFILE_SUPPLIER, STATE_SUPPLIER } from 'src/app/models/constant/sc';
import { Column, EventPopupSubmit } from 'src/app/models/interface';
import { SupplierInforService } from 'src/app/services/sc/supplier/suppiler-infor.service';
import { SupplierTypeService } from 'src/app/services/sc/supplier/suppiler-type.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { TableCommonService } from 'src/app/shared/table-module/table.common.service';
import { DateUtils } from 'src/app/utils/date-utils';
import { TagModule } from 'primeng/tag';
import { FileService } from 'src/app/shared/services/file.service';
import { DropdownChangeEvent, DropdownModule } from 'primeng/dropdown';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { NgClass, NgIf } from '@angular/common';
import { HasAnyAuthorityDirective } from '../../../../shared/directives/has-any-authority.directive';
import { Supplier, SupplierType } from 'src/app/models/interface/sc';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { PopupUploadComponent } from 'src/app/shared/components/popup-upload/popup-upload.component';
import { TooltipModule } from 'primeng/tooltip';
import { PopupComponent } from 'src/app/shared/components/popup/popup.component';
import { CalendarModule } from 'primeng/calendar';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { SupplierDeliveryQualityService } from 'src/app/services/sc/supplier-report/suppiler-delivery-quality.service';
import { isArray } from 'lodash';
import { SupplierItemService } from 'src/app/services/sc/supplier/suppiler-item.service';
import { ConfirmationService } from 'primeng/api';
import { SupplierMaterialService } from '../../../../services/sc/supplier/suppiler-material.service';

@Component({
    selector: 'app-supplier-infor',
    templateUrl: './list.component.html',
    styleUrls: ['./infor.component.scss'],
    standalone: true,
    imports: [
        ButtonModule,
        RouterLink,
        TagModule,
        DropdownModule,
        NgIf,
        ReactiveFormsModule,
        HasAnyAuthorityDirective,
        TableCommonModule,
        SubHeaderComponent,
        PopupUploadComponent,
        PopupComponent,
        TooltipModule,
        DropdownModule,
        CalendarModule,
        FormCustomModule,
        FormsModule,
        OverlayPanelModule,
        NgClass,
    ],
    providers: [SupplierInforService, SupplierTypeService, FileService, SupplierDeliveryQualityService, SupplierItemService],
})
export class SupplierListComponent implements OnInit, AfterContentInit {
    @ViewChild('templateSupplierType') templateSupplierType: TemplateRef<Element>;
    @ViewChild('templateStateSupplier') templateStateSupplier: TemplateRef<Element>;
    @ViewChild('templateProfileStatus') templateProfileStatus: TemplateRef<Element>;
    itemsHeader = [{ label: 'Quản lý nhà cung cấp' }, { label: 'Quản lý thông tin nhà cung cấp' }];
    // inject deps
    tableCommonService = inject(TableCommonService);
    private supplierService = inject(SupplierInforService);
    private supplierItemService = inject(SupplierItemService);
    private supplierMaterialService = inject(SupplierMaterialService);
    private supplierDeliveryQualityService = inject(SupplierDeliveryQualityService);
    private supplierTypeService = inject(SupplierTypeService);
    private loadingService = inject(LoadingService);
    private alertService = inject(AlertService);
    private fileService = inject(FileService);
    private fb = inject(FormBuilder);
    private confirmationService = inject(ConfirmationService);
    private supplierTypeServie = inject(SupplierTypeService);
    // state
    state: QueryObserverBaseResult<Supplier[]>;
    mapSupplieType: { [key: number]: string } = {};
    mapStateSupplier = MAP_STATE_SUPPLIER;
    mapStateProfileSupplier = MAP_STATE_PROFILE_SUPPLIER;
    columns: Column[] = [];
    columns2: Column[] = [];
    tableId: string = TABLE_KEY.SUPPLIER_INFOR;
    tableIdDelete: string = TABLE_KEY.SUPPLIER_INFOR_DELETE;
    stateDelete: QueryObserverBaseResult<SupplierType[]>;

    idSelectDeletes: number[] = [];
    //options
    yearRange: { label: string; value: number }[];
    monthRange: { label: string; value: number }[];
    supplierStateOption = STATE_SUPPLIER;
    profileStateOption = STATE_PROFILE_SUPPLIER;

    // state form popup
    itemsFormPopup: { [key: string]: FormControl } = {};
    formPopupExport: FormGroup;
    urlErrorReport: string;
    urlErrorCreate: string;
    urlErrorCreateItem: string;
    urlErrorQualityDelivery: string;
    urlTemplateReportQuality: string = 'bao_cao_phan_hang.xlsx';
    urlTemplateImportCreate: string = 'import_supplier.xlsx';

    // state popup
    isVisibleList: boolean = false;
    isVisibleAccessory: boolean = false;
    isVisibleReportQuality: boolean = false;
    isVisibleDelivery: boolean = false;

    ngOnInit() {
        this.initFormPopup();

        this.tableCommonService
            .init<Supplier>({
                tableId: this.tableId,
                queryFn: (filter) => this.supplierService.getPageTableCustom(filter),
                configFilterRSQL: {
                    name: 'Text',
                    shortName: 'Text',
                    code: 'Text',
                    supplierTypeId: 'SetLong',
                    productSupplied: 'Text',
                    address: 'Text',
                    country: 'Text',
                    website: 'TextURL',
                    contact: 'Text',
                    position: 'Text',
                    email: 'Text',
                    phone: 'Text',
                    yearBegin: 'SetLong',
                    profileStatus: 'SetLong',
                    point: 'Number',
                    rate: 'Text',
                    state: 'SetLong',
                    note: 'Text',
                    deletedAt: 'IsNull',
                },
                defaultParamsRSQL: {
                    deletedAt: true,
                },
                filterUrl: true,
            })
            .subscribe({
                next: (res) => {
                    this.state = res;
                },
                error: (error) => {
                    this.alertService.handleError(error);
                },
            });

        this.tableCommonService
            .init<Supplier>({
                tableId: this.tableIdDelete,
                queryFn: (filter) => this.supplierService.getPageTableCustom(filter),
                configFilterRSQL: {
                    name: 'Text',
                    shortName: 'Text',
                    code: 'Text',
                    supplierTypeId: 'SetLong',
                    productSupplied: 'Text',
                    address: 'Text',
                    country: 'Text',
                    website: 'TextURL',
                    contact: 'Text',
                    position: 'Text',
                    email: 'Text',
                    phone: 'Text',
                    yearBegin: 'SetLong',
                    profileStatus: 'SetLong',
                    point: 'Number',
                    rate: 'Text',
                    state: 'SetLong',
                    note: 'Text',
                    deletedAt: 'IsNull',
                },
                defaultParamsRSQL: {
                    deletedAt: false,
                },
                filterUrl: true,
            })
            .subscribe((state) => {
                this.stateDelete = state;
            });
        this.supplierTypeService.advancedGroup({}).subscribe({
            next: (response) => {
                response.body.filter((item) => {
                    this.mapSupplieType[item.id] = item.displayName;
                });
            },
            error: (error) => {
                this.alertService.handleError(error);
            },
        });
        this.monthRange = DateUtils.generateMonthRange();

        this.tableCommonService.getRowSelect(this.tableIdDelete).subscribe((state: Supplier[]) => {
            if (isArray(state)) {
                this.idSelectDeletes = state.map((item) => item.id);
            }
        });
    }

    initFormPopup() {
        const date = new Date();
        this.yearRange = DateUtils.generateYearRange(2000);

        this.itemsFormPopup = {
            year: new FormControl(date.getFullYear(), Validators.required),
            month: new FormControl(date.getMonth() + 1, Validators.required),
        };

        this.formPopupExport = this.fb.group({
            year: new FormControl(null, Validators.required),
            type: new FormControl(null, Validators.required),
        });
    }

    ngAfterContentInit() {
        setTimeout(() => {
            this.initColum();
        });
    }

    deleteSelected = (ids: number[]) => {
        return this.supplierService.batchDelete(ids);
    };

    softDeleteSelected = (ids: number[]) => {
        return this.supplierService.tempDelete(ids);
    };
    initColum() {
        this.columns = [
            {
                header: 'Tên đầy đủ',
                field: 'name',
                type: 'link',
                url: '/sc/supplier-infor/{id}',
                default: true,
            },
            {
                header: 'Viết tắt',
                field: 'shortName',
                default: true,
            },
            {
                header: 'ID',
                field: 'code',
                style: {
                    'min-width': '6rem',
                },
            },
            {
                header: 'Loại hình NCC',
                field: 'supplierTypeId',
                default: true,
                body: this.templateSupplierType,
            },
            {
                header: 'Hàng hóa cung cấp',
                field: 'productSupplied',
                style: {
                    'max-width': '15rem',
                },
            },
            {
                header: 'Địa chỉ',
                field: 'address',
                style: {
                    'max-width': '15rem',
                },
            },
            {
                header: 'Quốc gia',
                field: 'country',
            },
            {
                header: 'Website',
                field: 'website',
                style: {
                    'max-width': '20rem',
                },
            },
            {
                header: 'Người liên hệ',
                field: 'contact',
            },
            {
                header: 'Chức vụ',
                field: 'position',
            },
            {
                header: 'Email',
                field: 'email',
            },
            {
                header: 'Số điện thoại',
                field: 'phone',
            },
            {
                header: 'Năm bắt đầu giao dịch',
                field: 'yearBegin',
            },
            {
                header: 'Hồ sơ năng lực',
                field: 'profileLink',
                type: 'newTab',
                style: {
                    'max-width': '20rem',
                },
            },
            {
                header: 'Trạng thái HSNL',
                field: 'profileStatus',
                body: this.templateProfileStatus,
            },
            {
                header: 'Điểm số',
                field: 'point',
            },
            {
                header: 'Xếp hạng',
                field: 'rate',
            },
            {
                header: 'Trạng thái',
                field: 'state',
                body: this.templateStateSupplier,
            },
            {
                header: 'Ghi chú',
                field: 'note',
            },
        ];
    }

    getSeverity(state: number): string {
        switch (state) {
            case 4:
                return 'success';
            case 3:
                return 'danger';
            case 2:
                return 'primary';
            case 1:
                return 'danger';
            case 0:
                return 'warning';
            default:
                return 'info';
        }
    }

    exportExcel(event: EventPopupSubmit<unknown>) {
        console.log(event);
        if (event) return;
        this.loadingService.show();
        this.supplierService
            .exportExcel(this.tableCommonService.getColumnVisible(this.tableId).value, this.tableCommonService.getParams(this.tableId))
            .subscribe({
                next: (res: Blob) => {
                    // Gọi hàm downloadBlob để tải file
                    this.fileService.downloadBlob(res, 'Suppliers.xlsx');
                    this.loadingService.hide();
                    event.close();
                },
                error: () => {
                    this.alertService.error('Có lỗi xảy ra');
                    this.loadingService.hide();
                },
            });
    }

    changeYearAndMonth(e: DropdownChangeEvent, formGroup: FormGroup, key: 'year' | 'month') {
        const oldValue = formGroup.value;
        formGroup.setValue({ ...oldValue, [key]: e.value });
    }

    handleUploadReportQuality(data: { value: { file: File; year: number; month: number }; close: () => void }) {
        const date = new Date(data.value.year, data.value.month - 1);
        this.loadingService.show();
        this.supplierService.importReportQuality(data.value.file, date.getTime()).subscribe({
            next: (res) => {
                this.loadingService.hide();
                if (res.code === 0) {
                    this.loadingService.hide();
                    this.urlErrorReport = this.fileService.updateUrlDownload(res.message, '/sc/api');
                    this.alertService.error('File không hợp lệ');
                } else {
                    this.alertService.success('Thành công');
                    this.urlErrorReport = null;
                    data.close();
                    this.state.refetch();
                }
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    handleUploadImportCreate(data: { value: { file: File }; close: () => void }) {
        this.loadingService.show();
        this.supplierService.importCreate(data.value.file).subscribe({
            next: (res) => {
                this.loadingService.hide();
                if (res.code === 0) {
                    this.loadingService.hide();
                    this.urlErrorCreate = this.fileService.updateUrlDownload(res.message, '/sc/api');
                    this.alertService.error('File không hợp lệ');
                } else {
                    this.alertService.success('Thành công');
                    this.urlErrorCreate = null;
                    this.state.refetch();
                    data.close();
                }
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    handleUploadImportCreateItem(data: { value: { file: File }; close: () => void }) {
        this.loadingService.show();
        this.supplierMaterialService.importCreate(data.value.file).subscribe({
            next: (res) => {
                this.loadingService.hide();
                if (res.code === 0) {
                    this.loadingService.hide();
                    this.urlErrorCreateItem = this.fileService.updateUrlDownload(res.message, '/sc/api');
                    this.alertService.error('File không hợp lệ');
                } else {
                    this.alertService.success('Thành công');
                    this.urlErrorCreateItem = null;
                    data.close();
                }
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    handleExportReport(event: EventPopupSubmit<{ year: Date; type: string }>) {
        console.log(event);
        this.loadingService.show();
        const year = event.value.year.getFullYear();
        const startTime = new Date(year, 0, 1).getTime();
        const endTime = new Date(year, 11, 31, 23, 59, 59, 999).getTime();

        this.supplierService.exportReport({ startTime: startTime, endTime: endTime, type: event.value.type }).subscribe({
            next: (res: Blob) => {
                this.fileService.downloadBlob(res, event.value.type === 'PDF' ? 'report_supplier.pdf' : 'report_supplier.docx');
                this.loadingService.hide();
                event.close();
            },
            error: () => {
                this.loadingService.hide();
                this.alertService.error('Năm xuất báo cáo không có giao dịch');
            },
        });
    }

    handleUploadImportQualityDelivery(data: { value: { file: File }; close: () => void }) {
        this.loadingService.show();
        this.supplierDeliveryQualityService.importCreate(data.value.file).subscribe({
            next: (res) => {
                this.loadingService.hide();
                if (res.code === 0) {
                    this.loadingService.hide();
                    this.urlErrorQualityDelivery = this.fileService.updateUrlDownload(res.message, '/sc/api');
                    this.alertService.error('File không hợp lệ');
                } else {
                    this.alertService.success('Thành công');
                    this.urlErrorQualityDelivery = null;
                    data.close();
                }
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    restore() {
        if (!this.idSelectDeletes || this.idSelectDeletes.length === 0) return;
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn khôi phục NCC',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.loadingService.show();

                this.supplierService.restore(this.idSelectDeletes).subscribe({
                    next: () => {
                        this.loadingService.hide();
                        this.alertService.success('Thành công');
                        this.stateDelete.refetch();
                    },
                    error: () => {
                        this.loadingService.hide();
                    },
                });
            },
        });
    }
}
