import { canAuthorize } from '../../../core/auth/auth.guard';

export const DashBoardRouting = {
    path: 'dashboard',
    title: 'Tổng quan',
    data: {
        authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'ROLE_QC_SUBPM', 'ROLE_QC_CUSTOMER'],
    },
    canActivate: [canAuthorize],
    loadComponent: () =>
        import('src/app/modules/smart-qc/dashboard/dashboard.component').then((m) => m.DashboardComponent),
};
