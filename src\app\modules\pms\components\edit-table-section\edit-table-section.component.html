<form [formGroup]="form">
    <!-- formArrayName ở đây -->
    <div class="table-wrapper" formArrayName="rows">
        <p-table
            [value]="rowsArray.controls"
            [scrollable]="true"
            [resizableColumns]="true"
            [tableStyle]="{ 'table-layout': 'auto', width: '100%' }"
            styleClass="p-datatable-gridlines p-datatable-sm"
        >
            <!-- 1. Nếu TẤT CẢ columns có minWidth thì hiển thị colgroup động -->
            <ng-container *ngIf="hasMinWidth; else defaultColgroup">
                <ng-template pTemplate="colgroup">
                    <!-- Cột STT cố định 60px (bạn thích để bao nhiêu thì chỉnh) -->
                    <col [style.min-width]="'40px'" />

                    <!-- Sinh các <col> tương ứng với phần tử columns, lấy từ col.minWidth -->
                    <col *ngFor="let col of columns" [style.min-width]="col.minWidth" />

                    <!-- Nếu cần cột action (nút +, –) -->
                    <col *ngIf="!isHideAction" [style.min-width]="'40px'" />

                    <!-- Nếu cần cột “Uploaded” -->
                    <col *ngIf="!isHideAction" [style.min-width]="'80px'" />
                </ng-template>
            </ng-container>

            <!-- 2. Nếu KHÔNG đủ minWidth, fallback về default cứng định nghĩa sẵn -->
            <ng-template #defaultColgroup>
                <ng-template pTemplate="colgroup">
                    <!-- STT  -->
                    <col style="min-width: 40px" />
                    <!-- Mã tài liệu -->
                    <col style="min-width: 40px" />
                    <!-- Tài liệu -->
                    <col style="min-width: 250px" />
                    <!-- File -->
                    <col style="min-width: 150px" />
                    <!-- Tên tài liệu -->
                    <col style="min-width: 150px" />
                    <!-- MD5 -->
                    <col style="min-width: 150px" />
                    <!-- Buildtime -->
                    <col style="min-width: 150px" />
                    <!-- Version -->
                    <col style="min-width: 150px" />
                    <!-- note -->
                    <col style="min-width: 250px" />
                    <!-- Nếu cần action / Uploaded tương ứng -->
                    <col *ngIf="!isHideAction" style="min-width: 40px" />
                    <col *ngIf="!isHideAction" style="min-width: 80px" />
                </ng-template>
            </ng-template>
            <!-- HEADER -->
            <ng-template pTemplate="header">
                <tr>
                    <th>STT</th>
                    <th *ngFor="let col of columns">{{ col.header }}</th>
                    <th *ngIf="!isHideAction; else header_save" style="width: 40px; text-align: center">
                        <button
                            [disabled]="mode === 'view'"
                            pButton
                            type="button"
                            icon="pi pi-plus"
                            class="p-button-secondary p-button-sm"
                            (click)="addRow()"
                        ></button>
                    </th>
                    <!-- <ng-template #header_save> <th style="width: 40px; text-align: center"></th></ng-template> -->

                    <th *ngIf="!isHideAction" style="width: 80px; text-align: center">Uploaded</th>
                </tr>
            </ng-template>

            <!-- BODY -->
            <ng-template pTemplate="body" let-rowGroup let-i="rowIndex">
                <!-- formGroupName để kéo từng FormGroup con ra -->
                <tr [formGroupName]="i">
                    <!-- STT -->
                    <td class="tw-text-center">{{ i + 1 }}</td>

                    <!-- DYNAMIC CELLS -->
                    <td class="tw-truncate tw-max-w-[400px]" *ngFor="let col of columns">
                        <ng-container [ngSwitch]="col.type">
                            <!-- TEXT INPUT -->
                            <input class="tw-w-full" *ngSwitchCase="'text'" type="text" pInputText formControlName="{{ col.field }}" maxlength="1000" />
                            <div
                                *ngIf="
                                    rowGroup.get(col.field)!.value === 'RD BOM' &&
                                    rowGroup.get('category')!.value === 1 &&
                                    (mode === 'view' || mode === 'edit') &&
                                    updatedAt
                                "
                                class="tw-text-xs tw-text-gray-500 tw-mt-1"
                            >
                                Cập nhật gần nhất lúc {{ updatedAt | date: 'HH:mm' }} ngày {{ updatedAt | date: 'dd/MM/yyyy' }}
                            </div>

                            <ng-container *ngSwitchCase="'select'">
                                <p-dropdown
                                    [disabled]="mode === 'view'"
                                    [options]="dropdownOptions[i]"
                                    [optionLabel]="col.optionLabel"
                                    placeholder="{{ col.placeholder }}"
                                    formControlName="{{ col.field }}"
                                    appendTo="body"
                                    [filter]="true"
                                    [showClear]="true"
                                    (filterEvent)="onDropdownFilter($event, i)"
                                    (onChange)="onDropdownSelect($event.value, i, col.field)"
                                    (onClear)="onDropdownCleared(i, col.field)"
                                >
                                </p-dropdown>
                            </ng-container>

                            <!-- SELECT -->
                            <ng-container *ngSwitchCase="'select-one'">
                                <app-async-single-select
                                    [disabled]="mode === 'view'"
                                    [options]="dropdownSoftwareOptions[i]"
                                    [minLength]="col.minLength || 1"
                                    [displayField]="col.optionLabel"
                                    [placeholder]="col.placeholder"
                                    (search)="onCellSearch(i, col.lazyLoadFn, $event)"
                                    (clear)="onCellClear(i, col.field)"
                                    (selectionChange)="onCellSelect(i, col.field, $event)"
                                    [selected]="rowsArray.at(i).get('selected')?.value"
                                ></app-async-single-select>
                            </ng-container>

                            <!-- FILE UPLOAD -->
                            <app-upload-custom
                                #DocumentUpload
                                [loading]="loadingRows.get(i)"
                                [fileNamePattern]="fileNamePattern"
                                [strictFileNamePattern]="false"
                                fileNamePatternMessage="Tên file không đúng format, vui lòng kiểm tra lại theo format Sản phẩm_Tên tài liệu_Version_Buildtime!"
                                *ngSwitchCase="'file'"
                                [limit]="1"
                                [filePath]="rowsArray.at(i).get('filePath')?.value"
                                [initialFileName]="rowsArray.at(i).get('fileName')?.value"
                                (onChange)="handleFileUpload($event, i, rowsArray)"
                                (onClear)="clearFile(i, col.field)"
                                [disabled]="mode === 'view'"
                            ></app-upload-custom>

                            <!-- READONLY -->
                            <div
                                *ngSwitchCase="'readonly'"
                                class="tw-truncate tw-max-w-[400px]"
                                [autoHide]="false"
                                pTooltip="{{ rowGroup.get(col.field)!.value }}"
                                tooltipPosition="top"
                            >
                                {{ rowGroup.get(col.field)!.value }}
                            </div>
                            <a
                                *ngSwitchCase="'software-resource'"
                                class="tw-truncate tw-max-w-[400px]"
                                [autoHide]="false"
                                pTooltip="{{ rowGroup.get(col.field)!.value }}"
                                tooltipPosition="top"
                                [href]="getDownloadUrl(rowGroup.get('fileUrlSoftware')?.value)"
                                download
                            >
                                {{ rowGroup.get(col.field)!.value }}
                            </a>
                            <a
                                *ngSwitchCase="'user-guide'"
                                class="tw-truncate tw-max-w-[400px]"
                                [autoHide]="false"
                                pTooltip="{{ rowGroup.get(col.field)!.value }}"
                                tooltipPosition="top"
                                [href]="getDownloadUrl(rowGroup.get('fileUrlUserguide')?.value)"
                                download
                            >
                                {{ rowGroup.get(col.field)!.value }}
                            </a>
                        </ng-container>
                    </td>

                    <!-- DELETE ROW -->
                    <td *ngIf="!isHideAction; else body_save" class="text-center">
                        <div class="tw-flex tw-items-center tw-justify-center tw-space-x-2 text-center">
                            <button
                                [disabled]="mode === 'view' || rowGroup.get('isDefault')?.value === 1"
                                pButton
                                type="button"
                                icon="pi pi-minus"
                                style="color: white"
                                class="p-button-sm p-button-danger tw-text-white"
                                (click)="deleteRow(i)"
                            ></button>
                            <!-- <button
                                pButton
                                type="button"
                                icon="pi pi-check"
                                class="p-button-sm p-button-success"
                                (click)="saveRow(i)"
                            ></button> -->
                        </div>
                    </td>
                    <!-- <ng-template #body_save>
                        <td class="text-center">
                            <button
                                pButton
                                type="button"
                                icon="pi pi-check"
                                class="p-button-sm p-button-success"
                                (click)="saveRow(i)"
                            ></button>
                        </td>
                    </ng-template> -->

                    <!-- UPLOADED COUNT MERGED -->
                    <td *ngIf="i === 0 && !isHideAction" [attr.rowspan]="rowsArray.length" style="text-align: center; vertical-align: middle">
                        {{ getTotalUploaded() }} / {{ getTotalPossibleUploads() }}
                    </td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</form>
