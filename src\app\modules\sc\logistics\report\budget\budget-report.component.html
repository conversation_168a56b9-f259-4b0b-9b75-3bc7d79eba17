<style>
    .custom-table th,
    .custom-table td {
        border: 1px solid #ddd;
    }

    .custom-table {
        border-collapse: collapse;
    }

    textarea {
        resize: vertical; /* Allows only vertical resizing */
    }
</style>

<app-sub-header [items]="itemsHeader" [action]="actionHeader"></app-sub-header>
<ng-template #actionHeader>
    <p-button
        *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_report_logistic_export']"
        (click)="export()"
        [disabled]="data?.length == 0"
        size="small"
        label="Xuất Excel"
        severity="success"
    />
</ng-template>
<div class="tw-p-5">
    <div class="tw-bg-white tw-p-5 tw-rounded-xl tw-mb-5">
        <div class="tw-grid lg:tw-grid-cols-3 md:tw-grid-cols-1 sm:tw-grid-cols-1 tw-gap-4 tw-mb-3">
            <div [formGroup]="reportForm"  class="tw-flex tw-flex-col tw-space-y-3">
                <div class="tw-font-bold">Thời gian b<PERSON>o cáo</div>
                <p-calendar formControlName="time" [showIcon]="true" view="month" dateFormat="mm/yy" [readonlyInput]="true" (onSelect)="selectDate($event)" />
            </div>
        </div>
        <br />
        <div class="tw-relative">
            <p-table [value]="data" [rowHover]="true" responsiveLayout="scroll">
                <ng-template pTemplate="header">
                    <tr>
                        <th style="min-width: 9rem">
                            <div class="tw-flex">Nội dung</div>
                        </th>
                        <th style="min-width: 9rem">
                            <div class="tw-flex">SCC (tr VND)</div>
                        </th>

                        <th style="min-width: 9rem">
                            <div class="tw-flex">HEC, BC, DVKT, NM (tr VND)</div>
                        </th>

                        <th style="min-width: 10rem">
                            <div class="tw-flex">Tổng tiền (tr VND)</div>
                        </th>

                        <th style="min-width: 10rem">
                            <div class="tw-flex">Tỉ lệ % thực hiện</div>
                        </th>

                        <th style="min-width: 16rem">
                            <div class="tw-flex">Ghi chú</div>
                        </th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-report let-rowIndex="rowIndex">
                    <tr [className]="styleList[report.type]">
                        <td style="max-width: 200px" [title]="report?.displayName">
                            {{ report.displayName }}
                        </td>
                        <td style="max-width: 200px" [title]="report?.sccBudget" (dblclick)="setEditedField(report, rowIndex + '-scc', report.sccBudget)">
                            <input
                                *ngIf="isEditable(report.type) && editedReport === report && editedField == rowIndex + '-scc'; else value_scc"
                                id="{{ rowIndex }}-scc"
                                (blur)="saveReportTax(report, report.sccBudget, 0); editedReport = null"
                                [(ngModel)]="report.sccBudget"
                                type="number"
                                class="tw-w-full"
                                pInputText
                            />
                            <ng-template #value_scc>
                                {{ report.sccBudget }}
                            </ng-template>
                        </td>
                        <td style="max-width: 200px" [title]="report?.otherBudget" (dblclick)="setEditedField(report, rowIndex + '-other', report.otherBudget)">
                            <input
                                *ngIf="isEditable(report.type) && editedReport === report && editedField == rowIndex + '-other'; else value_other"
                                id="{{ rowIndex }}-other"
                                (blur)="saveReportTax(report, report.otherBudget, 1); editedReport = null"
                                [(ngModel)]="report.otherBudget"
                                type="number"
                                class="tw-w-full"
                                pInputText
                            />
                            <ng-template #value_other>
                                {{ report.otherBudget }}
                            </ng-template>
                        </td>
                        <td style="max-width: 200px" [title]="report?.totalBudget">
                            {{ report.totalBudget }}
                        </td>
                        <td style="max-width: 300px" (dblclick)="setEditedField(report, rowIndex + '-process', report.process)">
                            <input
                                *ngIf="editedReport === report && editedField == rowIndex + '-process'; else value_process"
                                id="{{ rowIndex }}-process"
                                (blur)="saveReportProcess(report, report.process); editedReport = null"
                                [(ngModel)]="report.process"
                                type="number"
                                class="tw-w-full"
                                pInputText
                            />
                            <ng-template #value_process>
                                {{ report.process }}
                            </ng-template>
                        </td>
                        <td style="max-width: 300px" (dblclick)="setEditedField(report, rowIndex + '-note', report.note)">
                            <input
                                *ngIf="editedReport === report && editedField == rowIndex + '-note'; else value_note"
                                id="{{ rowIndex }}-note"
                                (blur)="saveReportNote(report, report.note); editedReport = null"
                                [(ngModel)]="report.note"
                                type="text"
                                class="tw-w-full"
                                pInputText
                            />
                            <ng-template #value_note>
                                {{ report.note }}
                            </ng-template>
                        </td>
                    </tr>
                </ng-template>
            </p-table>
        </div>
    </div>
</div>

<p-dialog header="Xuất dữ liệu" [modal]="true" [(visible)]="isOpenModalExport" [style]="{ width: '45rem' }">
    <a [href]="downloadExportFileLink">Bấm vào đây để tải xuống</a>
    <div class="flex justify-content-end gap-2">
        <p-button label="Đóng" severity="secondary" (click)="isOpenModalExport = false" />
    </div>
</p-dialog>
