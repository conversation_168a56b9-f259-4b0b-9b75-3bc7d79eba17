<div
    *ngFor="let state of versionStates"
    [ngClass]="[
      'wizard-double-row',
      (isActivePreviousState && state?.[valueField] <= stateIdSelected) || state?.[valueField] === stateIdSelected
        ? 'wizard-double-row-dr-active'
        : 'wizard-double-row-dr'
    ]"
    (click)="selectState(state?.[valueField])"
    style="cursor: pointer"
>
    <div class="wizard-double-row-item" [ngStyle]="{'min-width': minWidth}">
        <div style="text-align: center">
            {{ state?.name }}
        </div>
        <div style="text-align: center">
            {{ state?.description }}
        </div>
    </div>
</div>
