import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { PanelModule } from 'primeng/panel';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { CalendarModule } from 'primeng/calendar';
import { TableModule } from 'primeng/table';
import { TooltipModule } from 'primeng/tooltip';
import { ButtonGroupFileComponent } from 'src/app/shared/components/button-group-file/button-group-file.component';
import { Lot, PackageType } from 'src/app/models/interface/sc';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { ConfirmationService } from 'primeng/api';
import { AlertService } from 'src/app/shared/services/alert.service';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { MAP_TYPE_MONEY, TYPE_SHIPPING, TYPE_SHIPPING_EXPORT, UnitPriceArr } from 'src/app/models/constant/sc';
import { TimeLineComponent, TimelineItem } from 'src/app/shared/components/time-line/time-line.component';
import { TabViewModule } from 'primeng/tabview';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { InputNumberModule } from 'primeng/inputnumber';
import { DialogModule } from 'primeng/dialog';
import { LotService } from '../../../../../services/sc/lot/lot.service';
import { FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { StateOneComponent } from '../state/state-one/state-one.component';
import { ApiResponse, User } from 'src/app/models/interface';
import { StateTwoComponent } from '../state/state-two/state-two.component';
import { StateThreeComponent } from '../state/state-three/state-three.component';
import { StateFourComponent } from '../state/state-four/state-four.component';
import { StateFiveComponent } from '../state/state-five/state-five.component';
import { StateSixComponent } from '../state/state-six/state-six.component';
import { StateSevenComponent } from '../state/state-seven/state-seven.component';
import { isEqual } from 'lodash';
import { BaseUserService } from 'src/app/services/administration/admin/user.service';
import { InputNumberComponent } from 'src/app/shared/components/inputnumber/inputnumber.component';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { PackageTypeService } from 'src/app/services/sc/bo/package-type.service';
import { AutocompleteComponent } from 'src/app/shared/components/autocomplete/autocomplete.component';

@Component({
    selector: 'app-lot-detail',
    standalone: true,
    imports: [
        CommonModule,
        SubHeaderComponent,
        RouterLink,
        ButtonModule,
        ReactiveFormsModule,
        FormCustomModule,
        PanelModule,
        InputTextModule,
        InputTextareaModule,
        DropdownModule,
        CalendarModule,
        TableModule,
        TooltipModule,
        ButtonGroupFileComponent,
        TimeLineComponent,
        TabViewModule,
        TableModule,
        DropdownModule,
        TableCommonModule,
        InputNumberModule,
        DialogModule,
        StateOneComponent,
        StateTwoComponent,
        StateThreeComponent,
        StateFourComponent,
        StateFiveComponent,
        StateSixComponent,
        StateSevenComponent,
        InputNumberComponent,
        HasAnyAuthorityDirective,
        AutocompleteComponent,
    ],
    templateUrl: './detail.component.html',
    styleUrls: ['./detail.component.scss'],
    providers: [LotService, PackageTypeService],
})
export class DetailComponent implements OnInit {
    oldLot: Lot;
    lotId: number;
    formGroup: FormGroup;
    @ViewChild('form') form: TemplateRef<FormComponent>;

    //option
    TYPE_SHIPPING = TYPE_SHIPPING;
    UnitPriceArr = UnitPriceArr;
    packageTypes: PackageType[] = [];

    MAP_TYPE_MONEY = MAP_TYPE_MONEY;

    timelineItems: TimelineItem[] = [
        {
            value: 1,
            name: 'Giai đoạn 1',
        },
        {
            value: 2,
            name: 'Giai đoạn 2',
        },
        {
            value: 3,
            name: 'Giai đoạn 3',
        },
        {
            value: 4,
            name: 'Giai đoạn 4',
        },
        {
            value: 5,
            name: 'Giai đoạn 5',
        },
        {
            value: 6,
            name: 'Giai đoạn 6',
        },
        {
            value: 7,
            name: 'Giai đoạn 7',
        },
    ];

    activeTabState: number;
    receivers: User[] = [];

    constructor(
        private activatedRoute: ActivatedRoute,
        private router: Router,
        private loadingService: LoadingService,
        private fb: FormBuilder,
        private alertService: AlertService,
        private confirmationService: ConfirmationService,
        private lotService: LotService,
        private userService: BaseUserService,
        private packageTypeService: PackageTypeService,
    ) {
        this.initForm(this.oldLot);
    }
    ngOnInit(): void {
        if (this.activatedRoute.snapshot.queryParams['activeTabState']) {
            this.activeTabState = Number(this.activatedRoute.snapshot.queryParams['activeTabState']);
        }
        this.loadData();

        this.userService.getUserHavePrivilege(['sc_bo_view', 'sc_bo_edit', 'sc_lot_view', 'sc_lot_edit']).subscribe({
            next: (res) => {
                this.receivers = res;
            },
        });
        this.fetchPackageTypes();
    }

    initForm(data: Lot | null) {
        this.formGroup = new FormGroupCustom(this.fb, {
            id: [data?.id],
            created: [data?.created],
            createdBy: [data?.createdBy],
            updated: [data?.updated],
            updatedBy: [data?.updatedBy],
            active: [data?.active],
            tenantId: [data?.tenantId],
            code: [{ value: data?.code, disabled: true }],
            shipmentValue: [data?.shipmentValue],
            type: [{ value: data?.type, disabled: !!data?.id }, Validators.required],
            paymentCondition: [data?.paymentCondition],
            supplierAddress: [data?.supplierAddress],
            supplierName: [data?.supplierName],
            goodsName: [data?.goodsName],
            poNumber: [data?.poNumber],
            indexShipment: [data?.indexShipment],
            supplierInfo: [data?.supplierInfo],
            accountingCode: [data?.accountingCode],
            totalWeight: [data?.totalWeight],
            unit: [data?.unit || 1],
            readyDateCustom: [data && data.readyDate ? new Date(data.readyDate) : null],
            packageNumber: [data?.packageNumber],
            requiredArrivedDateCustom: [data && data.requiredArrivedDate ? new Date(data.requiredArrivedDate) : null],
            deliveryCondition: [data?.deliveryCondition],
            departmentId: [data?.departmentId, Validators.required],
            note: [data?.note],
            attachmentIds: [data?.attachmentIds],
            attachments: [data?.attachments],
            consignee: [data?.consignee],
            finalDeliveryAddress: [data?.finalDeliveryAddress],
        });
    }

    loadData = () => {
        const idString = this.activatedRoute.snapshot.paramMap.get('id');
        if (idString) {
            this.lotId = Number(idString);
        }

        if (this.lotId) {
            this.loadingService.show();
            this.lotService.getOne(this.lotId).subscribe({
                next: (res) => {
                    this.loadingService.hide();
                    this.oldLot = res.body;
                    this.activeTabState = this.oldLot.state;
                    this.initForm(this.oldLot);
                    if (this.oldLot.type === TYPE_SHIPPING_EXPORT) {
                        this.timelineItems = [
                            {
                                value: 1,
                                name: 'Giai đoạn 1',
                            },
                            {
                                value: 2,
                                name: 'Giai đoạn 2',
                            },
                            {
                                value: 3,
                                name: 'Giai đoạn 3',
                            },
                            {
                                value: 5,
                                name: 'Giai đoạn 4',
                            },
                            {
                                value: 6,
                                name: 'Giai đoạn 5',
                            },
                            {
                                value: 7,
                                name: 'Giai đoạn 6',
                            },
                        ];
                    }
                },
                error: () => {
                    this.loadingService.hide();
                    this.router.navigate(['/sc/lot']);
                },
            });
        } else {
            this.router.navigate(['/sc/lot']);
        }
    };

    fetchPackageTypes() {
        this.packageTypeService.getPage('query=&page=0&size=100&sort=name,asc').subscribe({
            next: (res) => {
                this.packageTypes = res.body;
            },
        });
    }
    rowDelete = (rowData: PackageType) => {
        return rowData.createdBy !== 'system';
    };

    onSubmit(value: Lot): void {
        value.readyDate = value.readyDateCustom ? value.readyDateCustom.getTime() : null;
        value.requiredArrivedDate = value.requiredArrivedDateCustom ? value.requiredArrivedDateCustom.getTime() : null;
        this.loadingService.show();
        this.lotService.update({ ...this.oldLot, ...value }).subscribe({
            next: (res) => {
                this.loadingService.hide();
                this.oldLot = res.body;
                this.alertService.success('Thành công');
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    onStatusSelected(item: TimelineItem) {
        if (isEqual(item.value, this.activeTabState)) return;
        this.activeTabState = item.value as number;
    }

    onCompleteState(lot: Lot) {
        this.oldLot = lot;
        // this.activeTabState = lot.state;
    }

    handleUploadFile(files: File[]) {
        this.loadingService.show();
        this.lotService.importFile(files).subscribe({
            next: (res: ApiResponse) => {
                const itemValue = this.formGroup.getRawValue() as Lot;
                const attachmentIds = itemValue?.attachmentIds ?? [];
                const attachments = itemValue?.attachments ?? [];
                if (res.code === 1) {
                    // Nối mảng cũ và mảng mới
                    const newAttachmentIds = [...attachmentIds, ...(res.data['attachmentIds'] || [])];
                    const newAttachments = [...attachments, ...(res.data['attachments'] || [])];
                    this.formGroup.patchValue({
                        attachmentIds: newAttachmentIds,
                        attachments: newAttachments,
                    });
                } else {
                    this.alertService.error('Có lỗi xảy ra khi lưu file');
                    this.formGroup.patchValue({ attachmentIds: attachmentIds, attachments: attachments });
                }
                this.loadingService.hide();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    handleAddPackagegType(event: { value: PackageType; callback: (result: PackageType) => void }) {
        this.loadingService.show();
        this.packageTypeService.create({ ...event.value, id: null }).subscribe({
            next: (res) => {
                event.callback(res.body);
                this.loadingService.hide();
                this.packageTypes.push(res.body);
            },
            error: () => {
                this.loadingService.hide();
                event.callback({ ...event.value, id: null });
            },
        });
    }

    handleDeletePackagegType(event: { value: PackageType; callback: (result: boolean) => void }) {
        this.loadingService.show();

        this.packageTypeService.delete(event.value.id).subscribe({
            next: () => {
                event.callback(true);
                this.loadingService.hide();
                this.fetchPackageTypes();
            },
            error: () => {
                this.loadingService.hide();
                event.callback(false);
            },
        });
    }
}
