<app-sub-header [items]="itemsHeader" [action]="actionHeader"></app-sub-header>

<ng-template #actionHeader>
    <p-button routerLink="create" label="Tạo mới" severity="success" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'contract_pc_create']" />
</ng-template>
<div style="padding: 1rem 1rem 0 1rem">
    <app-table-common
        [tableId]="tableId"
        [columns]="columns"
        [data]="state.data"
        [loading]="state.isFetching"
        [filterTemplate]="filterTemplate"
        name="Danh sách dự án"
        [selectionMode]="authService.isAdmin() ? 'multiple' : null"
        [funcDelete]="deleteSelectedContract"
        [rowSelectable]="rowSelectable"
    >
        <ng-template #filterTemplate>
            <tr>
                <th *ngIf="authService.isAdminOrPM()"></th>
                <th>
                    <app-filter-table
                        [tableId]="tableId"
                        field="ids"
                        [rsql]="false"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            rsql: false,
                            param: 'name',
                            url: '/smart-qc/api/contract/combobox',
                        }"
                        placeholder="Tên dự án"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'status']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="statuses"
                        type="select"
                        [rsql]="false"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'lable',
                            filterLocal: true,
                            options: optionStatus,
                        }"
                        placeholder="Trạng thái"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'totalAction']">
                    <app-filter-table [tableId]="tableId" field="totalAction" [rsql]="false" placeholder="Tổng công việc"></app-filter-table>
                </th>

                <th [appFilter]="[tableId, 'totalStation']">
                    <app-filter-table [tableId]="tableId" field="totalStation" [rsql]="false" placeholder="Tổng trạm"></app-filter-table>
                </th>

                <th [appFilter]="[tableId, 'totalSubPM']" *ngIf="authService.isAdminOrPM()">
                    <app-filter-table [tableId]="tableId" field="totalSubPM" [rsql]="false" placeholder="Tổng SubPM"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'totalEmployee']" *ngIf="authService.isSubPM()">
                    <app-filter-table [tableId]="tableId" field="totalEmployee" [rsql]="false" placeholder="Tổng nhân viên"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'startTime']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="startTimeStart&startTimeEnd"
                        type="date-range"
                        [rsql]="false"
                        placeholder="Ngày bắt đầu"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'endTime']">
                    <app-filter-table
                        [tableId]="tableId"
                        [rsql]="false"
                        field="endTimeStart&endTimeEnd"
                        type="date-range"
                        placeholder="Ngày kết thúc"
                    ></app-filter-table>
                </th>
            </tr>
        </ng-template>
        <ng-template #templateName let-rowData>
            <a [routerLink]="rowData.id + '/edit'">{{ rowData.name }}</a>
        </ng-template>
        <ng-template #templateStatus let-rowData>
            <p-tag [severity]="rowData.status === 2 ? 'success' : rowData.status === 1 ? 'primary' : 'muted'">
                {{ rowData.status === 2 ? 'Hoàn thành' : rowData.status === 1 ? 'Đang thi công' : 'Chưa thực hiện' }}
            </p-tag>
        </ng-template>
    </app-table-common>
</div>
