import { Compo<PERSON>, <PERSON>ement<PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { DropdownModule } from 'primeng/dropdown';
import { HasAnyAuthorityDirective } from '../../../shared/directives/has-any-authority.directive';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { Ng<PERSON><PERSON>, NgForOf, NgIf, NgOptimizedImage } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { TableModule } from 'primeng/table';
import { DialogModule } from 'primeng/dialog';
import { CalendarModule } from 'primeng/calendar';
import { AuthService } from '../../../core/auth/auth.service';
import { NotificationDTO } from '../../../models/notification/NotificationDTO';
import { NotificationService } from '../../../services/smart-qc/notification/notification.service';
import Common from '../../../utils/common';
import { Router } from '@angular/router';
import { Chart } from 'chart.js/auto';
import { DashboardService } from '../../../services/smart-qc/dashboard/dashboard.service';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { MessageService } from 'primeng/api';
import { debounce, isArray, isEqual } from 'lodash';
import { Action, Contract } from 'src/app/models/interface/smart-qc';
import { EventChangeFilter, User } from '../../../models/interface';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';

@Component({
    selector: 'app-sm-dashboard',
    templateUrl: './dashboard.component.html',
    styleUrls: ['dashboard.component.scss'],
    standalone: true,
    imports: [
        DropdownModule,
        HasAnyAuthorityDirective,
        InputTextModule,
        InputTextareaModule,
        NgIf,
        ReactiveFormsModule,
        ButtonModule,
        TableModule,
        NgForOf,
        DialogModule,
        CalendarModule,
        NgOptimizedImage,
        ProgressSpinnerModule,
        NgClass,
        TableCommonModule,
        SubHeaderComponent,
    ],
})
export class DashboardComponent implements OnInit {
    taskProcessForm: FormGroup;
    chartForm: FormGroup;
    user: User = null;

    // Previous value
    previousValues: { [key: string]: unknown } = {};
    previousValuesDate: { [key: string]: unknown } = {};
    lastFilter: { [key: string]: unknown };
    firstFetchTaskProcess = true;
    debounceTask = debounce(this.getTaskProcess.bind(this), 400);
    debounceChart = debounce(this.getTaskProcessChart.bind(this), 400);

    // Task process
    isLoadingTaskProcess: boolean = false;
    taskProcessData: unknown;

    itemsHeader = [{ label: 'Tổng quan' }];

    // Chart
    @ViewChild('chartCanvas') chartCanvas: ElementRef<HTMLCanvasElement>;
    chart: Chart;
    chartData: unknown = [];
    today: Date;
    isLoadingChart: boolean = false;

    // Notification
    notifications: NotificationDTO[] = [];
    isLoadingNotification: boolean = true;

    constructor(
        private formBuilder: FormBuilder,
        protected authService: AuthService,
        private notificationService: NotificationService,
        private router: Router,
        private dashboardService: DashboardService,
        private messageService: MessageService,
    ) {
        this.taskProcessForm = this.formBuilder.group({
            contractId: [null, [Validators.required]],
            actionId: [null, [Validators.required]],
            subPmId: [null, []],
        });
        this.today = new Date();
        this.today.setHours(0, 0, 0, 0);
        this.chartForm = this.formBuilder.group({
            startTime: [this.getSevenDaysAgo(), []],
            endTime: [this.today, []],
        });
        this.authService.userObserver.subscribe({
            next: (res) => {
                this.user = res;
            },
        });
    }

    ngOnInit() {
        this.previousValues = {
            actionId: this.taskProcessForm.get('actionId')?.value,
            subPmId: this.taskProcessForm.get('subPmId')?.value,
        };
        this.previousValues = {
            startTime: this.chartForm.get('startTime')?.value,
            endTime: this.chartForm.get('endTime')?.value,
        };

        this.taskProcessForm.valueChanges.subscribe(() => {
            const currentValues = this.taskProcessForm.value;
            if (this.taskProcessForm.valid) {
                const hasChanged = currentValues.actionId !== this.previousValues['actionId'] || currentValues.subPmId !== this.previousValues['subPmId'];

                if (hasChanged) {
                    // Cập nhật các giá trị trước đó

                    this.previousValues = { actionId: currentValues?.actionId, subPmId: currentValues?.subPmId };
                    this.debounceTask();
                    this.debounceChart();
                }
            }
        });

        this.chartForm.valueChanges.subscribe(() => {
            const currentValues = this.chartForm.value;
            if (this.chartForm.valid) {
                const hasChanged =
                    currentValues.startTime !== this.previousValuesDate['startTime'] || currentValues.endTime !== this.previousValuesDate['endTime'];
                if (hasChanged) {
                    // Cập nhật các giá trị trước đó
                    this.previousValuesDate = { startTime: currentValues.startTime, endTime: currentValues.endTime };
                    this.getTaskProcessChart();
                }
            }
        });

        //this.getTaskProcessChart();

        const params = 'query=&page=0&size=10&sort=created,desc';
        this.notificationService.getPage(params).subscribe({
            next: (res) => {
                this.notifications = res.body;
                this.isLoadingNotification = false;
            },
            error: () => {
                this.isLoadingNotification = false;
            },
            complete: () => {},
        });
    }

    selectContract(data: EventChangeFilter) {
        this.taskProcessForm.patchValue({
            contractId: data.value,
            actionId: null,
            subPmId: null,
        });
    }

    selectAction(data: EventChangeFilter) {
        this.taskProcessForm.patchValue({
            actionId: data.value,
            subPmId: null,
        });
    }

    selectSubPm(data: EventChangeFilter) {
        this.taskProcessForm.patchValue({
            subPmId: data.value,
        });
    }

    protected readonly Common = Common;

    navigateContract() {
        this.router.navigate(['/sqc/contract/' + this.taskProcessForm.get('contractId').value + '/edit']);
    }

    navigateNotificationPage() {
        this.router.navigate(['/sqc/notification']);
    }

    navigateApprovePage() {
        const params = {
            native: `{"contractIds":[${this.taskProcessForm.get('contractId').value}],"statuses":[4]}`,
        };
        this.router.navigate(['/sqc/approve'], { queryParams: params });
    }

    onSelectFirstContractDone(data: Contract[]) {
        if (data?.length) {
            this.taskProcessForm.patchValue({
                contractId: data[0].id,
                actionId: null,
            });
        }
    }

    onselectFirstActionDone(data: Action[]) {
        if (data?.length) {
            this.taskProcessForm.patchValue({
                actionId: data[0].id,
            });
        }
    }

    getTaskProcess() {
        if (this.taskProcessForm.valid) {
            const filter = {
                contractId: this.taskProcessForm.get('contractId').value,
                actionId: this.taskProcessForm.get('actionId').value,
                subPmId: this.taskProcessForm.get('subPmId').value,
            };
            if (this.authService.isSubPM()) {
                filter.subPmId = this.user.id;
            }
            this.isLoadingTaskProcess = true;
            if (this.hasFilterTaskProcessChanged(filter)) {
                this.dashboardService.getTaskProcess(filter).subscribe({
                    next: (res: unknown) => {
                        this.taskProcessData = res;
                        this.isLoadingTaskProcess = false;
                        this.lastFilter = filter;
                    },
                    error: () => {
                        this.isLoadingTaskProcess = false;
                    },
                    complete: () => {},
                });
            }
        }
    }

    hasFilterTaskProcessChanged(filter: unknown): boolean {
        if (this.firstFetchTaskProcess) {
            this.firstFetchTaskProcess = false;
            return true;
        }

        return (
            !isEqual(filter['subPmId'], this.lastFilter['subPmId']) ||
            filter['contractId'] !== this.lastFilter['contractId'] ||
            filter['actionId'] !== this.lastFilter['actionId']
        );
    }

    getTaskProcessChart() {
        let startTime: number, endTime: number;
        if (this.chartForm.get('startTime').value && this.chartForm.get('endTime').value) {
            startTime = Common.getTimeFromHCMTimeZone(this.chartForm.get('startTime').value);
            endTime = Common.getTimeFromHCMTimeZone(this.chartForm.get('endTime').value);
        } else if (this.chartForm.get('startTime').value && !this.chartForm.get('endTime').value) {
            startTime = Common.getTimeFromHCMTimeZone(this.chartForm.get('startTime').value);
            endTime = Common.getTimeFromHCMTimeZone(this.today);
        } else if (!this.chartForm.get('startTime').value && this.chartForm.get('endTime').value) {
            startTime = Common.getTimeFromHCMTimeZone(this.getSevenDaysAgoFromDate(new Date(this.chartForm.get('endTime').value.getTime())));
            endTime = Common.getTimeFromHCMTimeZone(this.chartForm.get('endTime').value);
        } else {
            return;
        }
        const filter = {
            startTime: startTime,
            endTime: endTime,
            contractId: this.taskProcessForm.get('contractId').value,
            actionId: this.taskProcessForm.get('actionId').value,
            subPmId: this.taskProcessForm.get('subPmId').value,
        };
        const diffTime = Math.abs(filter.endTime - filter.startTime);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        if (diffDays > 30) {
            this.messageService.add({
                key: 'app-alert',
                severity: 'error',
                summary: 'Lỗi',
                detail: 'Hiển thị tối đa 30 ngày',
            });
            return;
        }
        this.isLoadingChart = true;
        this.dashboardService.getTaskProcessChart(filter).subscribe({
            next: (res: unknown) => {
                this.chartData = res;
                if (this.chart) {
                    this.chart.destroy();
                }
                this.genNewChart();
                this.isLoadingChart = false;
            },
            error: () => {
                this.isLoadingChart = false;
            },
            complete: () => {},
        });
    }

    private getSevenDaysAgo(): Date {
        const sevenDaysAgo = new Date(this.today);
        sevenDaysAgo.setHours(0, 0, 0, 0);
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        return sevenDaysAgo;
    }

    private getSevenDaysAgoFromDate(date: Date): Date {
        const sevenDaysAgo = date;
        sevenDaysAgo.setHours(0, 0, 0, 0);
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        return sevenDaysAgo;
    }

    genNewChart() {
        this.chart = new Chart(this.chartCanvas.nativeElement, {
            type: 'bar',
            data: {
                labels: isArray(this.chartData) ? this.chartData.map((row) => row['time']) : [],
                datasets: [
                    {
                        label: 'Trạm hoàn thành',
                        data: isArray(this.chartData) ? this.chartData.map((row) => row.dayQuantity) : [],
                        backgroundColor: '#00b87b',
                        borderColor: '#00b87b',
                        borderWidth: 1,
                        yAxisID: 'y-axis-bar',
                        type: 'bar',
                    },
                    {
                        label: 'Trạm hoàn thành lũy kế',
                        data: isArray(this.chartData) ? this.chartData.map((row) => row.cumulativeQuantity) : [],
                        backgroundColor: '#08549E',
                        borderColor: '#08549E',
                        borderWidth: 1,
                        yAxisID: 'y-axis-line',
                        type: 'line',
                        fill: false,
                    },
                ],
            },
            options: {
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Thời gian',
                        },
                    },
                    'y-axis-bar': {
                        type: 'linear',
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Số lượng trạm hoàn thành theo ngày',
                        },
                    },
                    'y-axis-line': {
                        type: 'linear',
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Số lượng trạm hoàn thành lũy kế',
                        },
                        grid: {
                            drawOnChartArea: false, // Only want the grid lines for one axis to show up
                        },
                    },
                },
            },
        });
    }
}
