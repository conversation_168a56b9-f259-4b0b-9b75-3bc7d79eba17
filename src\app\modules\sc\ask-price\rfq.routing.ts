import { canAuthorize } from '../../../core/auth/auth.guard';

export const RfqRouting = {
    path: 'rfq',
    title: 'Hỏi giá',
    canActivate: [canAuthorize],
    data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_rfq_view'] },
    children: [
        {
            path: '',
            title: '<PERSON><PERSON> sách đợt hỏi giá',
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_rfq_view', 'sc_rfq_edit'] },
            loadComponent: () => import('./list/list.component').then((c) => c.ListComponent),
        },
        {
            path: ':id',
            title: 'Chi tiết đợt hỏi giá',
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'sc_rfq_view', 'sc_rfq_edit'] },
            loadComponent: () => import('./edit/detail.component').then((c) => c.DetailComponent),
        }
    ],
};
