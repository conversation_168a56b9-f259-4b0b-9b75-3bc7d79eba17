/* eslint-disable @typescript-eslint/no-explicit-any */
import { Component, Input, OnChanges, OnInit, SimpleChanges, TemplateRef, inject } from '@angular/core';
import { Column } from 'src/app/models/interface';
import { Pagination, TableCommonService } from '../table.common.service';
import { Observable } from 'rxjs';
import { isArray } from 'lodash';
import Common from 'src/app/utils/common';
import { ConfirmationService } from 'primeng/api';
import { AlertService } from '../../services/alert.service';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
    selector: 'app-table-multi',
    templateUrl: './table-multi.component.html',
    styleUrls: ['./table-multi.component.scss'],
})
export class TableMultiComponent implements OnInit, OnChanges {
    @Input() showCaption: boolean = true;
    @Input() tableIds: string[] = [];
    @Input() activeTableId: Extract<(typeof this.tableIds)[number], string>;
    @Input() configMulti: {
        [K in Extract<(typeof this.tableIds)[number], string>]: {
            name: string;
            columns: Column[];
            data: unknown[] | undefined;
        } & Partial<{
            loading: boolean;
            styleClass: string;
            stt: boolean;
            hiddenSelect: (rowData: unknown) => boolean;
            fieldSelect: string;
            rowSelectable: (rowData: unknown) => boolean;
            selectionMode: 'multiple' | 'single';
            rowExpandTemplate: TemplateRef<Element>;
            filterTemplate: TemplateRef<Element>;
            headerTemplate: TemplateRef<Element>;
            footerTemplate: TemplateRef<Element>;
            funcDelete: (ids: unknown[], rowData?: unknown[]) => Observable<unknown>;
            funcDeleteAll: () => Observable<unknown>;
            authoritiesDelete: string[];
            funcDownload: Observable<unknown>;
            tableStyle: { [klass: string]: unknown };
            hideButtonHeader: boolean;
            rowHide: unknown[];
            deleteAllButton: boolean;
            deleteButton: boolean;
        }>;
    };
    /**
     * Các biến mặc định cho table
     */
    defaultValue = {
        style: { 'min-width': '20rem' },
        class: [], //'p-datatable-striped' 'p-datatable-sm'
        classError: 'bg-red-200',
    };
    /**
     *
     */

    warningIds: unknown[] = [];
    combinedTableStyle: unknown = {};
    combinedTableClass: string;
    rowSelects: unknown[] = [];
    pagination: Pagination;
    idSelects: unknown[] = [];
    columnAll: Column[] = [];
    columnVisible: Column[] = [];
    columnChoose: Column[] = [];

    private tableCommonService = inject(TableCommonService);
    private confirmationService = inject(ConfirmationService);
    private alertService = inject(AlertService);
    private route = inject(ActivatedRoute);

    private router = inject(Router);

    ngOnInit() {
        const queryParams = this.route.snapshot.queryParams;
        if (queryParams['tableId']) {
            console.log(queryParams['tableId']);

            this.activeTableId = queryParams['tableId'];
        }
        if (!this.activeTableId && this.tableIds && this.tableIds.length > 0) {
            this.activeTableId = this.tableIds[0];
        }
        if (!this.configMulti[this.activeTableId].rowSelectable) {
            this.configMulti[this.activeTableId].rowSelectable = () => true;
        }
        if (this.activeTableId && (!queryParams || !queryParams['tableId'])) {
            this.router.navigate([], {
                queryParams: { tableId: this.activeTableId },
                queryParamsHandling: 'merge',
                replaceUrl: true, // Tránh lưu lại lịch sử điều hướng không cần thiết
            });
        }

        this.autoUpdateSelect();
        this.tableCommonService.getPagination(this.activeTableId).subscribe((state) => (this.pagination = state));

        // this.initColums();
        this.initStyle();
        this.initClass();
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['configMulti'] && this.activeTableId) {
            // this.initStyle();
            if (this.configMulti[this.activeTableId].columns) {
                this.initColums();
            }
            this.initData();
        }
    }

    private autoUpdateSelect() {
        this.warningIds = [];

        this.tableCommonService.getRowSelect(this.activeTableId).subscribe((state: unknown) => {
            if (isArray(state)) {
                this.rowSelects = state;
                this.idSelects = state.map((e: unknown) => {
                    return e[this.configMulti[this.activeTableId]?.fieldSelect || 'id'];
                });
            }
        });
    }

    private initData() {
        if (isArray(this.configMulti[this.activeTableId].rowHide) && isArray(this.configMulti[this.activeTableId].data)) {
            this.configMulti[this.activeTableId].data = this.configMulti[this.activeTableId].data.filter(
                (row) => !this.configMulti[this.activeTableId].rowHide.includes(row[this.configMulti[this.activeTableId]?.fieldSelect || 'id']),
            );
        }
    }

    private initStyle(): void {
        this.combinedTableStyle = {
            ...this.defaultValue.style,
            ...this.configMulti[this.activeTableId].tableStyle,
        };
    }

    private initClass(): void {
        this.combinedTableClass = this.defaultValue.class.join(' ') + this.configMulti[this.activeTableId].styleClass;
    }

    private initColums(): void {
        this.columnAll = this.columnVisible = this.configMulti[this.activeTableId].columns.filter((col: Column, index: number) => {
            col.index = index;
            if (col.typeSort === undefined || col.typeSort === null) {
                col.typeSort = 'asc';
            }
            return !col.hide && !col.visible;
        });
        this.columnChoose = this.configMulti[this.activeTableId].columns.filter((col: Column) => {
            return !col.hide || (col.header && col.header.trim().length > 0);
        });
        this.tableCommonService.updateColumnVisible(
            this.activeTableId,
            this.columnVisible.map((c) => c.field),
        );
    }

    rowSelectableWrapper = (row: unknown) => {
        if (!this.configMulti[this.activeTableId].rowSelectable) return true;
        return this.configMulti[this.activeTableId].rowSelectable(row['data']);
    };
    onSort(col: Column): void {
        if (this.pagination.refetch && !this.pagination.local && col.sort) {
            col.typeSort = col.typeSort === 'asc' ? 'desc' : 'asc';
            this.tableCommonService.updateFilter(this.activeTableId, {
                sort: `${col.sort || col.field},${col.typeSort}`,
            });
        }
    }

    isWarning(rowData: unknown): boolean {
        return this.warningIds.includes(rowData[`${this.configMulti[this.activeTableId]?.fieldSelect || 'id'}`]);
    }

    setRowSelection(selectedRows: unknown | unknown[]) {
        this.tableCommonService.updateRowSelect(this.activeTableId, isArray(selectedRows) ? selectedRows : [selectedRows]);
    }

    setColumnSelection(selectedColumns: Column[]) {
        const columsShow = selectedColumns.length === 0 ? this.columnAll.filter((col) => col.default) : selectedColumns.filter((col) => col.hide !== true);
        this.columnVisible = Common.sortByOriginalOrder(this.configMulti[this.activeTableId].columns, columsShow);

        this.tableCommonService.updateColumnVisible(
            this.activeTableId,
            this.columnVisible.map((c) => c.field),
        );
    }

    // hideCheckbox(rowData: unknown): boolean {
    //     if (this.hiddenSelect === undefined) return false;
    //     return this.hiddenSelect(rowData);
    // }

    deleteRecord() {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                if (this.configMulti[this.activeTableId].funcDelete) {
                    this.configMulti[this.activeTableId].loading = true;
                    this.configMulti[this.activeTableId].funcDelete(this.idSelects, this.rowSelects).subscribe((res: number[]) => {
                        if (isArray(res) && res.length > 0) {
                            this.warningIds = res;
                            this.configMulti[this.activeTableId].loading = false;
                            this.rowSelects = [];
                            this.idSelects = [];
                            this.alertService.error('Lỗi!', 'Không thể xóa các bản ghi không phù hợp');
                        } else {
                            this.warningIds = [];
                            this.rowSelects = [];
                            this.idSelects = [];
                            this.pagination.refetch();
                            this.alertService.success('Thành công', 'Xóa bản ghi thành công');
                        }
                    });
                }
            },
        });
    }

    deleteAllRecord() {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn xóa tất cả',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                if (this.configMulti[this.activeTableId].funcDeleteAll) {
                    this.configMulti[this.activeTableId].loading = true;
                    this.configMulti[this.activeTableId].funcDeleteAll().subscribe({
                        next: () => {
                            this.warningIds = [];
                            this.rowSelects = [];
                            this.idSelects = [];
                            this.pagination.refetch();
                            this.alertService.success('Thành công', 'Xóa tất cả bản ghi thành công');
                        },
                        error: () => {
                            this.alertService.success('Thành công', 'Xóa tất cả bản ghi thất bại');
                        },
                        complete: () => {},
                    });
                }
            },
        });
    }

    getLink(colUrl: string, rowData: unknown): string {
        return colUrl.replace(/{(\w+)}/g, (_, key) => rowData[key]);
    }

    hanleActiveTable(tableId: string) {
        if (this.activeTableId === tableId) return;
        this.activeTableId = tableId;
        this.clearUrlFilter(this.tableCommonService.getState(tableId));
        this.tableCommonService.getPagination(this.activeTableId).subscribe((state) => (this.pagination = state));
        this.pagination.refetch();

        this.tableCommonService.updateRowSelect(tableId, []);
        this.autoUpdateSelect();
        this.initClass();
        this.initStyle();
    }

    clearUrlFilter(state: any) {
        const filterParams = state.props?.filterUrl
            ? {
                  native: JSON.stringify(state.filter.native),
                  rsql: JSON.stringify(state.filter.rsql),
              }
            : {};

        this.router.navigate([], {
            queryParams: {
                tableId: this.activeTableId,
                ...filterParams,
            },
            queryParamsHandling: '',
            replaceUrl: true,
        });
    }
}
