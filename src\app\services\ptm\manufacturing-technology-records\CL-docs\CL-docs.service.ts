import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { CLDocDto, GetCLDocumentsResponse, UpdateCLDocsPayload } from 'src/app/models/interface/ptm/CL-docs';

@Injectable({ providedIn: 'root' })
export class CLDocsService {
    path = '/pr/api/production-instruction';
    #http = inject(HttpClient);

    saveCLDocs(productInstructionId: string | number, payload: UpdateCLDocsPayload): Observable<CLDocDto[]> {
        const url = `${this.path}/${productInstructionId}/quality-document`;
        return this.#http.post<CLDocDto[]>(url, payload);
    }
    getCLDocuments(productInstructionId: number): Observable<GetCLDocumentsResponse> {
        const url = `${this.path}/${productInstructionId}/quality-document`;
        return this.#http.get<GetCLDocumentsResponse>(url);
    }
}
