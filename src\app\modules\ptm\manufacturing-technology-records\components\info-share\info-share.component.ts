import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ViewChild, AfterViewInit, SimpleChanges, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormsModule } from '@angular/forms';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { ComboboxNonRSQLComponent } from 'src/app/shared/components/combobox-nonRSQL/combobox-nonRSQL.component';
import { STATUS_MAP } from 'src/app/models/constant/pms';
@Component({
    selector: 'app-info-share',
    standalone: true,
    imports: [CommonModule, FormsModule, InputTextareaModule, FormCustomModule, ComboboxNonRSQLComponent],
    templateUrl: './info-share.component.html',
    styleUrls: ['./info-share.component.scss'],
})
export class InfoShareComponent implements OnInit, OnDestroy, AfterViewInit, OnChanges {
    @ViewChild('userApproval') userApproval!: ComboboxNonRSQLComponent;
    @ViewChild('userApprovals') userApprovals!: ComboboxNonRSQLComponent;
    // 🧠 INPUTS từ cha truyền xuống
    @Input() title: string = '';
    @Input() detailInfo: any;
    @Input() name: string = '';
    @Input() mode: 'view' | 'create' | 'edit' = 'create';
    @Output() changeValueApprover = new EventEmitter<any>();
    @Output() changeValueApprovers = new EventEmitter<any>();

    constructor(private fb: FormBuilder) {}

    formGroup: any;
    STATUS_MAP = STATUS_MAP;

    ngOnDestroy(): void {
        console.log('🧹 [InfoShareComponent] Unmounted');
    }

    ngOnInit(): void {
        const isViewMode = this.mode === 'view';

        this.formGroup = this.fb.group({
            reviewers: [
                {
                    value: this.detailInfo?.instructionInfo?.reviewers?.map((item: any) => item.id) || [],
                    disabled: isViewMode,
                },
            ],
            approvers: [
                {
                    value: this.detailInfo?.instructionInfo?.approvers?.map((item: any) => item.id) || [],
                    disabled: isViewMode,
                },
            ],
        });
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['detailInfo'] && changes['detailInfo'].currentValue) {
            // Gọi lại formGroup patch nếu detailInfo thay đổi
            const reviewers = changes['detailInfo'].currentValue.instructionInfo?.reviewers?.map((item: any) => item.id);
            if (this.formGroup) {
                this.formGroup.patchValue({ reviewers });
            }
            const approvers = changes['detailInfo'].currentValue.instructionInfo?.approvers?.map((item: any) => item.id);
            if (this.formGroup) {
                this.formGroup.patchValue({ approvers });
            }
            const selected = this.detailInfo?.instructionInfo?.reviewers || [];
            const selectedApp = this.detailInfo?.instructionInfo?.approvers || [];
            // Fill vào combobox thủ công
            this.userApproval.objectValue = selected;
            this.userApprovals.objectValue = selectedApp;
            this.setUserFilterOptions(this.userApproval);
            this.setUserFilterOptions(this.userApprovals);
        }
    }

    ngAfterViewInit(): void {
        this.userApproval.debouncedGetOptions('');
        this.setUserFilterOptions(this.userApproval);
        this.userApprovals.debouncedGetOptions('');
        this.setUserFilterOptions(this.userApprovals);
    }

    setUserFilterOptions(...combos: ComboboxNonRSQLComponent[]) {
        combos.forEach((combo) => {
            const origDebounce = combo.debouncedGetOptions.bind(combo);
            combo.filterOptions = (term: string) => {
                const rsql = `email==*${term}*`;
                origDebounce(rsql);
            };
        });
    }

    changeApprover(event: any) {
        // console.log(event);
        this.changeValueApprover.emit(event.objects);
    }

    changeApprovers(event: any) {
        this.changeValueApprovers.emit(event.objects);
    }

    getInfo(): string {
        if (this.title === 'MANBOM') {
            return 'Người phê duyệt: ';
        } else {
            return 'Người soát xét: ';
        }
    }
}
