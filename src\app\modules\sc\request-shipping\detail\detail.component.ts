import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { FormArray, FormBuilder, FormGroup, ReactiveFormsModule, Validators, AbstractControl } from '@angular/forms';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { PanelModule } from 'primeng/panel';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { CalendarModule } from 'primeng/calendar';
import { TableModule } from 'primeng/table';
import { TooltipModule } from 'primeng/tooltip';
import { ButtonGroupFileComponent } from 'src/app/shared/components/button-group-file/button-group-file.component';
import { BoItem, Bo, InventoryProduct, ShippingMethod, ProductManPN, BaseDepartment, PoInfoDTO, Po, PackageType } from 'src/app/models/interface/sc';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { ConfirmationService } from 'primeng/api';
import { AlertService } from 'src/app/shared/services/alert.service';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { MAP_TYPE_MONEY, PO_STATE_CONSTANT, TYPE_SHIPPING, TYPE_SHIPPING_IMPORT, UnitPriceArr, UnitPriceMappingValue } from 'src/app/models/constant/sc';
import { ApiResponse, EventChangeFilter } from 'src/app/models/interface';
import { BoItemService } from 'src/app/services/sc/bo/boItems.service';
import { TabViewModule } from 'primeng/tabview';
import { BoService } from 'src/app/services/sc/bo/bo.service';
import { ErpManufacturerService } from 'src/app/services/sc/bo/erp-manufacturer.service';
import { InventoryProductService } from 'src/app/services/sc/bo/inventory-product.service';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { InputNumberModule } from 'primeng/inputnumber';
import { FilterChangeEvent } from 'src/app/shared/table-module/filter-table/filter-table.component';
import { cloneDeep, isArray, isEqual } from 'lodash';
import { BoShippingMethodService } from 'src/app/services/sc/bo/bo-shipping-method.service';
import { ShippingMethodService } from 'src/app/services/sc/bo/shipping-method.service';
import { AbstractControlCustom, FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { FormArrayCustom } from 'src/app/shared/form-module/from-array.custom';
import { InputNumberComponent } from 'src/app/shared/components/inputnumber/inputnumber.component';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { SCDepartmentService } from 'src/app/services/sc/bo/department.service';
import { focQuantityValidator, focQuantityValidatorBoItem, maxValidator, uniqueInternalReferenceValidator } from '../../../../utils/validator';
import { ComboboxComponent } from 'src/app/shared/components/combobox/combobox.component';
import { FileService } from 'src/app/shared/services/file.service';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { PackageTypeService } from 'src/app/services/sc/bo/package-type.service';
import { AutocompleteComponent } from 'src/app/shared/components/autocomplete/autocomplete.component';

@Component({
    imports: [
        CommonModule,
        SubHeaderComponent,
        RouterLink,
        ButtonModule,
        ReactiveFormsModule,
        FormCustomModule,
        PanelModule,
        InputTextModule,
        InputTextareaModule,
        DropdownModule,
        CalendarModule,
        TableModule,
        TooltipModule,
        ButtonGroupFileComponent,
        TabViewModule,
        TableModule,
        DropdownModule,
        TableCommonModule,
        InputNumberModule,
        InputNumberComponent,
        HasAnyAuthorityDirective,
        ComboboxComponent,
        ConfirmDialogModule,
        AutocompleteComponent,
    ],
    providers: [
        BoService,
        BoItemService,
        ErpManufacturerService,
        InventoryProductService,
        BoShippingMethodService,
        ShippingMethodService,
        SCDepartmentService,
        PackageTypeService,
    ],
    selector: 'app-shipping-detail',
    standalone: true,
    styleUrls: ['./detail.component.scss'],
    templateUrl: './detail.component.html',
})
export class DetailComponent implements OnInit {
    oldbo: Bo;
    boId: number;
    formGroup: FormGroupCustom<Bo>;
    formShippingMethodGroup: FormGroup;
    @ViewChild('form') form: TemplateRef<FormComponent>;

    //option
    TYPE_SHIPPING = TYPE_SHIPPING;
    TYPE_SHIPPING_METHOD: ShippingMethod[] = [];
    UnitPriceArr = UnitPriceArr;
    packageTypes: PackageType[] = [];

    MAP_TYPE_MONEY = MAP_TYPE_MONEY;
    optionsDepartment: BaseDepartment[] = [];
    // BoItem
    isAddingBoItem: boolean = false;
    isEditingBoItem: boolean = false;
    backUpBoItem: BoItem;
    poOptions: Po[];
    internalReferenceOptionsByPo = new Map<number, PoInfoDTO[]>();
    poDetailDisabledByPo = new Map<number, Map<string, PoInfoDTO>>();

    // file
    urlErrorBoItems: string;
    defaultDepartment: BaseDepartment;
    disabledUploadBoItem: boolean = true;
    //
    quantityByLevelByPo = new Map<number, Map<number, { availableQuantity: number; remainingFocQuantity: number }>>();
    quantityByLevelOriginByPo = new Map<number, Map<number, { availableQuantity: number; remainingFocQuantity: number }>>();
    poStateNotDraft: number[] = [];

    // Tong tien vat tu
    totalItemsPrice = '';

    isLoading: boolean = true;

    constructor(
        private activatedRoute: ActivatedRoute,
        private router: Router,
        private loadingService: LoadingService,
        private fb: FormBuilder,
        private alertService: AlertService,
        private confirmationService: ConfirmationService,
        private boService: BoService,
        private boItemService: BoItemService,
        private route: ActivatedRoute,
        private inventoryProductService: InventoryProductService,
        private sCDepartmentService: SCDepartmentService,
        private fileService: FileService,
        private packageTypeService: PackageTypeService,
    ) {}

    ngOnInit(): void {
        this.route.paramMap.subscribe(() => {
            this.loadData();
        });
        this.fetchPackageTypes();
        this.poStateNotDraft = Object.values(PO_STATE_CONSTANT).filter((v) => v !== -1);
    }

    fetchPackageTypes() {
        this.packageTypeService.getPage('query=&page=0&size=100&sort=name,asc').subscribe({
            next: (res) => {
                this.packageTypes = res.body;
            },
        });
    }
    rowDelete = (rowData: PackageType) => {
        return rowData.createdBy !== 'system';
    };

    loadDepartment() {
        this.sCDepartmentService.getPage('query=&page=0&size=100&sort=id,desc').subscribe({
            next: (res) => {
                this.optionsDepartment = res.body;
                this.defaultDepartment = this.optionsDepartment.find((item) => item.name.includes('SCC'));
                this.loadingService.hide();
                if (this.defaultDepartment) {
                    this.formGroup.patchValue({
                        departmentId: this.defaultDepartment.id,
                    });
                }
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    initForm(data: Bo | null) {
        this.formGroup = new FormGroupCustom(this.fb, {
            code: [{ value: data?.code, disabled: true }],
            shipmentValue: [data?.shipmentValue],
            type: [{ value: data?.type ?? TYPE_SHIPPING_IMPORT, disabled: !!data?.id }, Validators.required],
            paymentCondition: [data?.paymentCondition],
            supplierAddress: [data?.supplierAddress],
            supplierName: [data?.supplierName],
            goodsName: [data?.goodsName],
            poNumber: [data?.poNumber],
            indexShipment: [data?.indexShipment, []],
            supplierInfo: [data?.supplierInfo],
            accountingCode: [data?.accountingCode],
            totalWeight: [data?.totalWeight],
            unit: [data?.unit || 1],
            readyDateCustom: [data && data.readyDate ? new Date(data.readyDate) : null],
            packageNumber: [data?.packageNumber, []],
            requiredArrivedDateCustom: [data && data.requiredArrivedDate ? new Date(data.requiredArrivedDate) : null],
            deliveryCondition: [data?.deliveryCondition],
            departmentId: [data ? data.departmentId : this.defaultDepartment?.id, [Validators.required]],
            note: [data?.note],
            typeModule: [data ? data.typeModule : 1], // 1 Bo cho module PO
            poIds: [{ value: this.formatPoIds(data), disabled: false }, [Validators.required]],
            attachmentIds: [data?.attachmentIds],
            attachments: [data?.attachments],
            attachmentItemId: [data?.attachmentItemId],
            attachmentItem: [data?.attachmentItem],
            poList: [data?.poList],
            consignee: [data?.consignee],
            finalDeliveryAddress: [data?.finalDeliveryAddress],
            boItems: this.initBoItems(data?.boItems || []),
        });

        this.initPoOptionsWhenInitForm(data);
    }

    initPoOptionsWhenInitForm(bo: Bo) {
        if (bo?.poList) {
            this.poOptions = bo?.poList;
        }
        const poIds = this.formatPoIds(bo);

        this.boItems.controls.forEach((control) => {
            const poId = control.get('poId')?.value;
            const internalReference = control.get('internalReference')?.value;
            const poInfo: PoInfoDTO = {
                internalReference,
            };

            if (!this.poDetailDisabledByPo.has(poId)) {
                this.poDetailDisabledByPo.set(poId, new Map<string, PoInfoDTO>());
            }

            this.poDetailDisabledByPo.get(poId)!.set(internalReference, poInfo);
        });

        if (poIds !== null && poIds.length > 0) {
            this.boItemService.getByPo(poIds).subscribe({
                next: (res) => {
                    this.updateQuantityByLevel(res);

                    poIds.forEach((poId) => {
                        this.updateQuantityOptions(poId);
                    });
                },
                error: () => {
                    // handle error here
                },
            });
        }
    }

    formatPoIds(data: Bo) {
        return isArray(data?.poList) && data?.poList.length > 0 ? data?.poList?.map((po) => po.id) : null;
    }

    loadData = () => {
        const idString = this.activatedRoute.snapshot.paramMap.get('id');
        if (idString) {
            this.boId = Number(idString);
        }

        if (this.boId) {
            this.loadingService.show();
            this.boService.getOne(this.boId).subscribe({
                next: (res) => {
                    this.loadingService.hide();
                    this.oldbo = res.body;
                    this.initForm(this.oldbo);
                    this.isLoading = false;
                    this.loadDepartment();
                },
                error: () => {
                    this.loadingService.hide();
                    this.alertService.error('Lỗi', 'Không thể truy cập thông tin ');
                    this.router.navigate(['/sc/bo']);
                    this.isLoading = false;
                },
            });
        } else {
            this.initForm(null);
            this.isLoading = false;
            this.loadDepartment();
        }
    };

    handleAddPackagegType(event: { value: PackageType; callback: (result: PackageType) => void }) {
        this.loadingService.show();
        this.packageTypeService.create({ ...event.value, id: null }).subscribe({
            next: (res) => {
                event.callback(res.body);
                this.loadingService.hide();
                this.packageTypes.push(res.body);
            },
            error: () => {
                this.loadingService.hide();
                event.callback({ ...event.value, id: null });
            },
        });
    }

    handleDeletePackagegType(event: { value: PackageType; callback: (result: boolean) => void }) {
        this.loadingService.show();

        this.packageTypeService.delete(event.value.id).subscribe({
            next: () => {
                event.callback(true);
                this.loadingService.hide();
                this.fetchPackageTypes();
            },
            error: () => {
                this.loadingService.hide();
                event.callback(false);
            },
        });
    }

    handleClearFile(type: 'boItems' | 'bo') {
        switch (type) {
            case 'boItems':
                this.formGroup.patchValue({
                    boItems: this.initBoItems([]),
                    attachmentItem: null,
                    attachmentItemId: null,
                });
                break;
            case 'bo':
                this.formGroup.patchValue({
                    attachment: null,
                    attachmentId: null,
                });
                break;
        }
    }

    handleUploadFile(file: File | File[], type: 'boItems' | 'bo') {
        const poIds = this.formGroup.get('poIds').getRawValue();
        if (!poIds || (isArray(poIds) && poIds.length === 0)) {
            this.alertService.error('Vui lòng chọn Số hợp đồng/đơn hàng (PO) trước khi tải lên file');
            return;
        }
        this.loadingService.show();
        switch (type) {
            case 'boItems':
                this.handleUpdateBoItemsFile(file);
                break;
            case 'bo':
                this.handleUploadBoFile(file);
                break;
        }
    }

    handleUpdateBoItemsFile(file) {
        this.loadingService.show();
        const poIds = this.formGroup.get('poIds').getRawValue();
        this.boItemService.importFile(file as File, this.boId, poIds).subscribe({
            next: (res: ApiResponse) => {
                if (res.code === 1) {
                    const data = res.data as BoItem[];
                    data.forEach((item) => {
                        item.boId = this.boId;
                    });
                    const boItemsFormArray = this.initBoItems(data); // Hàm trả về FormArray
                    boItemsFormArray.controls.forEach((item) => {
                        this.disableExceptQuantity(item);
                    });
                    this.formGroup.setControl('boItems', boItemsFormArray);
                    this.urlErrorBoItems = null;
                    this.alertService.success('Thành công');
                } else {
                    this.urlErrorBoItems = res.message;
                    this.formGroup.patchValue({
                        boItems: [],
                        attachmentItemId: null,
                        attachmentItem: null,
                    });
                    this.alertService.error('Có lỗi xảy ra khi đọc file');
                }
                this.loadingService.hide();

                if (this.boId !== null) {
                    const poIds: number[] = this.boItems.controls.map((control) => control.get('poId')?.value);
                    if (poIds !== null && poIds.length > 0) {
                        this.getOptionsFromPos(poIds);
                    }
                }
            },
            error: () => {
                this.formGroup.patchValue({
                    boItems: [],
                    attachmentItemId: null,
                    attachmentItem: null,
                });
                this.loadingService.hide();
            },
        });
    }

    handleUploadBoFile(file) {
        this.loadingService.show();
        this.boService.importFile(file as File[]).subscribe({
            next: (res: ApiResponse) => {
                const itemValue = this.formGroup.getRawValue() as Bo;
                const attachmentIds = itemValue?.attachmentIds ?? [];
                const attachments = itemValue?.attachments ?? [];
                if (res.code === 1) {
                    const newAttachmentIds = [...attachmentIds, ...(res.data['attachmentIds'] || [])];
                    const newAttachments = [...attachments, ...(res.data['attachments'] || [])];

                    this.formGroup.patchValue({
                        attachments: newAttachments,
                        attachmentIds: newAttachmentIds,
                    });
                } else {
                    this.formGroup.patchValue({
                        attachments: attachments,
                        attachmentIds: attachmentIds,
                    });
                    this.alertService.error('Có lỗi xảy ra khi đọc file');
                }
                this.loadingService.hide();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    getOptionsFromPos(poIds: number[]) {
        this.boItemService.getByPo(poIds).subscribe({
            next: (res) => {
                this.updateQuantityByLevel(res);

                poIds.forEach((poId) => {
                    this.updateQuantityOptions(poId);
                });

                const boItems = this.formGroup.get('boItems') as FormArray;

                boItems.controls.forEach((item) => {
                    this.updateOptionsSaveItem(item);
                });

                poIds.forEach((poId) => {
                    this.updateQuantityOptions(poId);
                });
            },
            error: () => {
                // handle error here
            },
        });
    }

    private updateQuantityByLevel(poInfoDTOS: PoInfoDTO[]) {
        const groupedByPoId = new Map<number, PoInfoDTO[]>();

        for (const item of poInfoDTOS) {
            if (!groupedByPoId.has(item.poId)) {
                groupedByPoId.set(item.poId, []);
            }
            groupedByPoId.get(item.poId)!.push(item);
        }

        // Xử lý từng poId
        for (const [poId, items] of groupedByPoId.entries()) {
            // Tạo display cho mỗi item
            const internalReferenceOptions = items
                .filter((option) => {
                    const disabledMap = this.poDetailDisabledByPo.get(poId);
                    return !(disabledMap && disabledMap.has(option.internalReference));
                })
                .map((option) => ({
                    ...option,
                    display: `${option.internalReference} - ${option.availableQuantity}`,
                }));
            this.internalReferenceOptionsByPo.set(poId, internalReferenceOptions);

            // Tạo quantityByLevel
            const quantityByLevel = items.reduce((map, item) => {
                if (!map.has(item.indexLevel)) {
                    map.set(item.indexLevel, { availableQuantity: 0, remainingFocQuantity: 0 });
                }
                const current = map.get(item.indexLevel)!;
                current.availableQuantity = item.availableQuantity;
                current.remainingFocQuantity = item.remainingFocQuantity;
                return map;
            }, new Map<number, { availableQuantity: number; remainingFocQuantity: number }>());

            // Clone origin
            const quantityByLevelOrigin = new Map(Array.from(quantityByLevel, ([key, value]) => [key, { ...value }]));

            this.quantityByLevelByPo.set(poId, quantityByLevel);
            this.quantityByLevelOriginByPo.set(poId, quantityByLevelOrigin);
        }
    }

    initBoItems(items: BoItem[]): FormArray {
        return new FormArrayCustom(
            items.map(
                (item) =>
                    new FormGroupCustom(this.fb, {
                        id: [item?.id],
                        boId: [item?.boId],
                        poId: [item?.poId],
                        orderNo: [item?.orderNo],
                        created: [item?.created],
                        updated: [item?.updated],
                        createdBy: [item?.createdBy],
                        updatedBy: [item?.updatedBy],
                        tenantId: [item?.tenantId],
                        active: [item?.active],
                        internalReference: [item?.internalReference, Validators.required],
                        manufacturerId: [item?.manufacturerId],
                        indexLevel: [item?.indexLevel],
                        productId: [item?.productId],
                        manPn: [item?.manPn],
                        quantity: [item?.quantity, [Validators.required, Validators.min(1)]],
                        foc: [item?.foc ?? 0, [focQuantityValidatorBoItem('Số lượng FOC <= Tổng số lượng')]],
                        price: [item?.price, [Validators.required, Validators.min(0)]],
                        productDescription: [{ value: item?.productDescription, disabled: true }],
                        note: [item?.note],
                        unit: [item?.unit, Validators.required],
                        isEdit: [false],
                        isSaved: [true],
                    }),
            ),
            { validators: [uniqueInternalReferenceValidator()] },
        );
    }

    get boItems(): FormArray {
        return this.formGroup.get('boItems') as FormArray;
    }

    addBoItem(): void {
        const newItem = this.fb.group({
            id: [null],
            created: [null],
            updated: [null],
            createdBy: [null],
            updatedBy: [null],
            tenantId: [null],
            active: [null],
            orderNo: [null],
            poId: [null, Validators.required],
            boId: [this.boId],
            internalReference: [{ value: null, disabled: true }, Validators.required],
            indexLevel: [{ value: null, disabled: true }],
            manufacturerId: [{ value: null, disabled: true }],
            productId: [{ value: null, disabled: true }],
            manPn: [{ value: null, disabled: true }],
            quantity: [{ value: null, disabled: true }, [Validators.required, Validators.min(1)]],
            foc: [{ value: 0, disabled: true }, [Validators.min(0), focQuantityValidatorBoItem('Số lượng FOC <= Tổng số lượng')]],
            price: [{ value: null, disabled: true }, [Validators.required]],
            productDescription: [{ value: null, disabled: true }],
            note: [{ value: null, disabled: true }],
            unit: [{ value: null, disabled: true }, Validators.required],
            isEdit: [true],
            isSaved: [false],
        });
        newItem.get('poId').valueChanges.subscribe((value) => {
            const internalReferenceControl = newItem.get('internalReference');
            const quantityControl = newItem.get('quantity');
            const focQuantityControl = newItem.get('foc');
            const noteControl = newItem.get('note');
            if (value) {
                internalReferenceControl.enable();
                quantityControl.enable();
                focQuantityControl.enable();
                noteControl.enable();
            } else {
                internalReferenceControl.disable();
                quantityControl.disable();
                focQuantityControl.disable();
                noteControl.disable();
            }
        });
        this.boItems.push(newItem);
        this.isAddingBoItem = true;
    }

    editBoItem(index: number): void {
        const item = this.boItems.at(index);
        this.backUpBoItem = cloneDeep(item.getRawValue());
        item.patchValue({ isEdit: true });
        this.isEditingBoItem = true;

        this.updateQuantityOptions(item.get('poId')?.value, true);
        this.disableExceptQuantity(item);
    }

    saveBoItem(index: number): void {
        const item: AbstractControlCustom = this.boItems.at(index) as AbstractControlCustom;
        const parent: AbstractControlCustom = this.formGroup.get('boItems') as AbstractControlCustom;

        const itemValue = item.getRawValue();
        item.isSubmited = true;
        parent.isSubmited = true;
        if (item.valid) {
            if (this.boId) {
                this.loadingService.show();
                if (item.get('id').getRawValue() === null) {
                    this.boItemService.create(item.getRawValue()).subscribe({
                        next: (res) => {
                            this.loadingService.hide();
                            item.patchValue({ ...res.body, isEdit: false, isSaved: true });
                            this.alertService.success('Thành công');
                            // Đặt trạng thái isEditing thành false
                            this.isEditingBoItem = false;
                            this.isAddingBoItem = false;
                            this.disableExceptQuantity(item);

                            // Call api save
                        },
                        error: () => {
                            this.loadingService.hide();
                        },
                    });
                } else {
                    this.boItemService.update(item.getRawValue()).subscribe({
                        next: (res) => {
                            this.loadingService.hide();
                            item.patchValue({ ...res.body, isEdit: false, isSaved: true });
                            this.alertService.success('Thành công');
                            // Đặt trạng thái isEditing thành false
                            this.isEditingBoItem = false;
                            this.isAddingBoItem = false;
                            this.disableExceptQuantity(item);

                            // Call api save
                        },
                        error: () => {
                            this.loadingService.hide();
                        },
                    });
                }
            } else {
                item.patchValue({ ...itemValue, isEdit: false, isSaved: true });
                // Đặt trạng thái isEditing thành false
                this.isEditingBoItem = false;
                this.isAddingBoItem = false;
                this.disableExceptQuantity(item);

                // Remove Ma 16 tu option
                this.updateOptionsSaveItem(item);

                this.updateQuantityOptions(item.get('poId')?.value);
            }
        }
    }

    updateOptionsSaveItem(item: AbstractControl): void {
        let targetIndex = -1;
        let targetElement: PoInfoDTO;
        const poId = item.get('poId').value;
        const internalReferenceOptions = this.internalReferenceOptionsByPo.get(poId);

        for (let i = 0; i < internalReferenceOptions?.length; i++) {
            if (internalReferenceOptions[i].internalReference === item.get('internalReference').value) {
                targetIndex = i;
                targetElement = internalReferenceOptions[i];
                break;
            }
        }

        if (targetIndex !== -1) {
            internalReferenceOptions.splice(targetIndex, 1);
        }
        this.internalReferenceOptionsByPo.set(poId, internalReferenceOptions);

        if (!this.poDetailDisabledByPo.has(poId)) {
            this.poDetailDisabledByPo.set(poId, new Map<string, PoInfoDTO>());
        }

        this.poDetailDisabledByPo.get(poId)?.set(targetElement?.internalReference ?? '', targetElement);
    }

    private disableExceptQuantity(item) {
        item.disable(); // Disable toàn bộ
        item.get('quantity')?.enable(); // Bật lại quantity
        item.get('foc')?.enable(); // Bật lại quantity
        item.get('isEdit')?.enable();
    }

    cancelCreate(index: number): void {
        const boItem = this.boItems.at(index).getRawValue() as BoItem;
        if (this.isAddingBoItem) {
            this.boItems.removeAt(index);
        } else if (this.isEditingBoItem) {
            this.boItems.at(index).enable();
            this.boItems.at(index).patchValue(this.backUpBoItem);
            this.disableExceptQuantity(this.boItems.at(index));
        }
        this.isAddingBoItem = false;
        this.isEditingBoItem = false;

        this.updateQuantityOptions(boItem.poId);
    }

    removeBoItem(index: number): void {
        const item = this.boItems.at(index);

        const itemValue = item.getRawValue() as BoItem;
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận xóa',
            message: 'Bạn có chắc chắn muốn xóa?',
            icon: 'pi pi-exclamation-triangle',
            accept: () => {
                if (itemValue?.id) {
                    this.loadingService.show();
                    this.boItemService.delete(itemValue.id).subscribe({
                        next: () => {
                            this.isEditingBoItem = false;
                            this.boItems.removeAt(index);
                            this.loadingService.hide();
                            this.alertService.success('Thành công');
                        },
                        error: (res) => {
                            this.alertService.handleError(res);
                            this.loadingService.hide();
                        },
                    });
                } else {
                    this.isEditingBoItem = false;
                    const poId = itemValue.poId;
                    this.boItems.removeAt(index);

                    if (this.poDetailDisabledByPo.has(poId)) {
                        const disabledMap = this.poDetailDisabledByPo.get(itemValue.poId);
                        const disabledDetail = disabledMap?.get(itemValue.internalReference);

                        if (disabledDetail) {
                            this.internalReferenceOptionsByPo.get(poId).unshift(disabledDetail);
                        }
                    }

                    this.updateQuantityOptions(poId);
                }
            },
        });
    }

    handleChangeManufacture(e: FilterChangeEvent, index: number) {
        const objects = e.objects as unknown as ProductManPN[];
        const productMan = isArray(objects) && objects.length > 0 ? objects[0] : (null as ProductManPN);
        if (e?.value !== null && productMan !== null) {
            this.boItems.controls[index].patchValue({
                manPn: productMan?.manufacturerPn,
                manufacturerId: productMan?.manufacturerId,
                productId: productMan?.productId,
            });
            const query = `query=manufacturerId==${productMan?.manufacturerId};productId==${productMan?.productId}`;
            this.inventoryProductService.getPage(query).subscribe({
                next: (res) => {
                    this.loadingService.hide();
                    const data = res.body as unknown as InventoryProduct[];
                    if (isArray(data) && data.length > 0) {
                        this.boItems.controls[index].patchValue({
                            internalReference: data[0].internalReference,
                        });
                    }
                },
                error: () => {
                    this.loadingService.hide();
                },
            });
        } else {
            this.boItems.controls[index].patchValue({
                manPn: null,
            });
        }
    }

    handleChangeInternalReference(e: FilterChangeEvent, item: AbstractControl) {
        const objects = e.objects as unknown as PoInfoDTO[];
        // console.log(objects);

        if (objects !== null && objects.length > 0) {
            const poInfoDTO = objects[0] as PoInfoDTO;
            item.patchValue({
                unit: poInfoDTO.unitPrice !== null ? UnitPriceMappingValue[poInfoDTO.unitPrice] : null,
                price: poInfoDTO.price,
                productDescription: poInfoDTO.description,
                description: poInfoDTO.description,
                manPn: poInfoDTO.manPn,
                indexLevel: poInfoDTO.indexLevel,
            });

            const quantityControl = item.get('quantity');
            quantityControl.setValidators([Validators.required, Validators.min(1), Validators.max(poInfoDTO.availableQuantity)]);
            quantityControl.updateValueAndValidity(); // Cập nhật lại trạng thái validator của quantity
            const max = item.get('quantity').getRawValue() !== null ? item.get('quantity').getRawValue() : 1;

            const focControl = item.get('foc');
            const poId = item.get('poId').getRawValue();
            const maxFoc = poInfoDTO.remainingFocQuantity;
            focControl.setValidators([
                Validators.min(0),
                maxValidator(maxFoc, `Số lượng FOC phải <= ${maxFoc}`),
                focQuantityValidator(`Số lượng FOC <= Tổng số lượng`),
            ]);
            focControl.updateValueAndValidity(); // Cập nhật lại trạng thái validator của quantity
        }
    }

    onSubmit(value: Bo): void {
        value.readyDate = value.readyDateCustom ? value.readyDateCustom.getTime() : null;
        value.requiredArrivedDate = value.requiredArrivedDateCustom ? value.requiredArrivedDateCustom.getTime() : null;
        // Count total
        let totalAmount = 0;

        this.boItems.controls.forEach((item) => {
            const quantity = +item.get('quantity')?.value || 0;
            const foc = +item.get('foc')?.value || 0;
            const price = +item.get('price')?.value || 0;

            const itemTotal = (quantity - foc) * price;
            console.log('price: ', price);

            totalAmount += itemTotal;
        });

        const shipmentValue = this.formGroup.get('shipmentValue').getRawValue();
        console.log('totalAmount: ', totalAmount);
        console.log('shipmentValue: ', shipmentValue);

        if (!isEqual(totalAmount, shipmentValue)) {
            this.confirmPass(value);
        } else {
            this.callApiSaveBo(value);
        }
    }

    private confirmPass(data) {
        this.confirmationService.confirm({
            key: 'app-confirm-pass',
            header: 'Cảnh báo',
            message: 'Tổng giá trị shipment khác Tổng giá trị linh kiện',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Thông qua',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.callApiSaveBo(data);
            },
        });
    }

    callApiSaveBo(data) {
        if (this.boId) {
            if (!this.checkChangePoUpdate()) {
                this.alertService.error('Vui lòng cập nhật lại thông tin vật tư');
                return;
            }
            this.loadingService.show();
            this.boService.update({ ...this.oldbo, ...data }).subscribe({
                next: (res) => {
                    this.loadingService.hide();
                    this.oldbo = res.body;
                    this.alertService.success('Thành công');
                },
                error: () => {
                    this.loadingService.hide();
                },
            });
        } else {
            this.loadingService.show();
            this.boService.create(data).subscribe({
                next: (res) => {
                    this.loadingService.hide();
                    this.alertService.success('Thành công');
                    this.router.navigate([`/sc/request-shipping/${res.body.id}`]);
                },
                error: () => {
                    this.loadingService.hide();
                },
            });
        }
    }

    handleChangePo(data: EventChangeFilter) {
        if (data?.objects && data?.objects.length > 0) {
            this.poOptions = data?.objects as Po[];
        } else {
            this.poOptions = [];
        }
        if (isArray(data.value)) {
            this.disabledUploadBoItem = data.value.length > 0 ? false : true;
        }

        this.formGroup.patchValue({
            poList: this.poOptions,
        });
        //console.log(this.poOptions)
        this.getOptionsFromPos(this.poOptions?.map((po) => po.id));

        if (!this.boId) {
            // Neu man Tao moi
            this.filterItemsChangePo();
        }
    }

    private filterItemsChangePo() {
        const validOrderNos = new Set(this.poOptions.map((po) => po.orderNo));
        for (let i = 0; i < this.boItems.length; i++) {
            const orderNo = this.boItems.at(i).get('orderNo')?.value;
            if (!validOrderNos.has(orderNo)) {
                this.boItems.removeAt(i);
            }
        }
    }

    private checkChangePoUpdate() {
        const validOrderNos = new Set(this.poOptions.map((po) => po.orderNo));
        for (let i = 0; i < this.boItems.length; i++) {
            const orderNo = this.boItems.at(i).get('orderNo')?.value;
            if (!validOrderNos.has(orderNo)) {
                return false;
            }
        }
        return true;
    }

    handleSelectPoForBoItem(data, itemControl: AbstractControl) {
        if (data?.objects && data?.objects.length > 0) {
            const po = data?.objects[0];
            itemControl.patchValue({
                orderNo: po?.orderNo,
            });

            // Check options
            if (this.internalReferenceOptionsByPo.get(po?.id) === null || this.boId) {
                // Get options
                this.boItemService.getByPo([po.id]).subscribe({
                    next: (res) => {
                        this.updateQuantityByLevel(res);
                    },
                    error: () => {},
                });
            }
        } else {
            // Remove
            itemControl.patchValue({
                unit: null,
                price: null,
                description: null,
                indexLevel: null,
                quantity: null,
                foc: 0,
                note: null,
                internalReference: null,
            });
        }
    }

    private updateQuantityOptions(poId: number, isEditTransferItem: boolean = false) {
        const quantityByLevel = structuredClone(this.quantityByLevelOriginByPo.get(poId));

        // Duyệt qua FormArray
        this.boItems.controls.forEach((item: AbstractControl) => {
            const internalReference = item.get('internalReference')?.value;
            if (!internalReference) {
                return;
            }
            const level = item.get('indexLevel')?.value;
            const levelData = quantityByLevel.get(level);
            if (!levelData) return;

            // Nếu đang Edit, bỏ qua item có isEdit = true
            if (isEditTransferItem && item.get('isEdit')?.value) {
                return;
            }

            // Trừ số lượng đã chọn
            const quantity = Number(item.get('quantity')?.value) || 0;
            const focQuantity = Number(item.get('foc')?.value) || 0;

            levelData.availableQuantity = Math.max(0, levelData.availableQuantity - quantity);
            levelData.remainingFocQuantity = Math.max(0, levelData.remainingFocQuantity - focQuantity);
            // levelData.focQuantity = Math.max(0, levelData.focQuantity - focQuantity);
        });

        // Cập nhật optionPoDetail
        let internalReferenceOptions = this.internalReferenceOptionsByPo.get(poId);
        internalReferenceOptions = internalReferenceOptions
            ?.filter((option) => {
                const disabledMap = this.poDetailDisabledByPo.get(poId);
                return !(disabledMap && disabledMap.has(option.internalReference));
            })
            .map((option) => {
                const updatedLevelData = quantityByLevel.get(option.indexLevel);
                return updatedLevelData
                    ? {
                          ...option,
                          availableQuantity: updatedLevelData.availableQuantity,
                          remainingFocQuantity: updatedLevelData.remainingFocQuantity,
                          display: `${option.internalReference} - ${updatedLevelData.availableQuantity}`,
                      }
                    : option;
            });
        this.internalReferenceOptionsByPo.set(poId, internalReferenceOptions);
    }

    getOptionsForInternalReference(item: AbstractControl) {
        const poId = item.get('poId').value;
        if (poId !== null) {
            return this.internalReferenceOptionsByPo.get(poId);
        }
        return [];
    }

    handleDownloadTemplate() {
        const poIds = this.formGroup.get('poIds').getRawValue();
        if (!poIds || (isArray(poIds) && poIds.length === 0)) {
            this.alertService.error('Vui lòng chọn PO trước khi tải lên file');
            return;
        }
        this.loadingService.show();
        this.boItemService.getTempalate(poIds).subscribe({
            next: (res) => {
                this.loadingService.hide();
                this.fileService.downLoadFileByService(res.url, '/sc/api');
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }
}
