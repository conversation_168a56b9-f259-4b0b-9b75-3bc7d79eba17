import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { Bo, BoChangeStateDTO, BoNegotiate, BoRequestDTO, BoResponseITF } from '../../../models/interface/sc';
import { ApiResponse, GeneralEntity } from 'src/app/models/interface';
import { ParamsTable } from 'src/app/shared/table-module/table.common.service';

@Injectable()
export class BoService extends BaseService<Bo> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/bo');
    }

    importFile(files: File[]) {
        const formData: FormData = new FormData();
        files.forEach((file) => formData.append('files', file));
        return this.http.post<ApiResponse>('/sc/api/bo/import-file', formData);
    }

    negotiate(id: number, boNegotiate: BoNegotiate) {
        return this.http.post<Bo>(`/sc/api/bo/negotiate/${id}`, boNegotiate);
    }

    exportNegotiate(body) {
        return this.http.post<GeneralEntity>(`/sc/api/bo/export-negotiate`, body);
    }

    toNegotiate(boId: number) {
        return this.http.put<Bo>(`/sc/api/bo/to-negotiate/${boId}`, null);
    }

    updateState(boId: number, boChangeStateDTO: BoChangeStateDTO) {
        return this.http.post<GeneralEntity>(`/sc/api/bo/update-state/${boId}`, boChangeStateDTO);
    }

    getPageTableNative({ pageable = '&page=0&size=10' }: ParamsTable, body: BoRequestDTO) {
        return this.http.post<BoResponseITF[]>(`/sc/api/bo/search-bo-request?${pageable}`, body, {
            observe: 'response',
        });
    }

    exportRequestShipping(boRequestDTO: any) {
        return this.http.post<GeneralEntity>(`/sc/api/bo/export-bo-request`, boRequestDTO);
    }
}
