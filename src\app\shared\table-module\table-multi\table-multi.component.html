<div class="table-wrapper">
    <div class="table-container">
        <p-table
            [columns]="columnVisible"
            [value]="configMulti[activeTableId].data"
            [loading]="configMulti[activeTableId].loading"
            [scrollable]="false"
            [tableStyle]="combinedTableStyle"
            [styleClass]="combinedTableClass"
            [selection]="rowSelects"
            (selectionChange)="setRowSelection($event)"
            [rows]="pagination.size"
            [dataKey]="configMulti[activeTableId].fieldSelect"
            [rowsPerPageOptions]="[5, 10, 20, 50]"
            [rowSelectable]="rowSelectableWrapper"
        >
            <ng-template *ngIf="!configMulti[activeTableId].hideButtonHeader" pTemplate="caption">
                <div class="table-caption" *ngIf="showCaption">
                    <div class="tw-flex tw-flex-row tw-gap-4 tw-h-full tw-w-60 md:tw-w-max md:tw-overflow-clip tw-overflow-auto scrollbar-hidden">
                        <span
                            *ngFor="let tableId of tableIds"
                            class="tw-whitespace-nowrap tw-pt-1 tw-cursor-pointer tw-h-full border-bottom-2 tw-border-transparent tw-transition-all"
                            [ngClass]="{
                                ' border-blue-500 text-blue-700': tableId === activeTableId,
                                '  hover:border-400': tableId !== activeTableId,
                            }"
                            (click)="hanleActiveTable(tableId)"
                        >
                            {{ configMulti[tableId]?.name ?? 'Table' }}
                        </span>
                    </div>
                    <div class="table-caption-toolbar">
                        <div *ngIf="configMulti[activeTableId].funcDelete && configMulti[activeTableId].selectionMode" class="table-caption-toolbar-item">
                            <span class="md:tw-inline-block tw-hidden">
                                Đã chọn:
                                <span style="width: 10px; font-weight: 400">
                                    {{ rowSelects.length || 0 }}
                                </span>
                            </span>

                            <p-button
                                title="Xóa"
                                [appAuthorities]="configMulti[activeTableId].authoritiesDelete"
                                icon="pi pi-trash"
                                [severity]="rowSelects.length === 0 ? 'secondary' : 'danger'"
                                [outlined]="true"
                                [size]="'small'"
                                [disabled]="rowSelects.length === 0"
                                [ngClass]="{
                                    'qc-disabled': rowSelects.length === 0,
                                    'table-btn-delete': true,
                                }"
                                (click)="deleteRecord()"
                            ></p-button>
                            <div style="border-right: 1px solid #ebeff5; height: 2.25rem; width: 1px"></div>

                            <p-button
                                *ngIf="configMulti[activeTableId].deleteAllButton"
                                label="Xóa tất cả"
                                [severity]="'danger'"
                                [outlined]="true"
                                [size]="'small'"
                                [ngClass]="{
                                    'table-btn-delete': true,
                                }"
                                (click)="deleteAllRecord()"
                            ></p-button>
                            <div *ngIf="configMulti[activeTableId].deleteAllButton" style="border-right: 1px solid #ebeff5; height: 2.25rem; width: 1px"></div>
                        </div>

                        <ng-container
                            *ngIf="configMulti[activeTableId].actionTemplate"
                            [ngTemplateOutlet]="configMulti[activeTableId].actionTemplate"
                        ></ng-container>
                        <div class="table-top-toolbar-item">
                            <p-button title="Lọc cột" icon="pi pi-filter" [outlined]="true" [size]="'small'" (click)="op.toggle($event)"></p-button>
                        </div>

                        <div class="table-top-toolbar-item" *ngIf="configMulti[activeTableId].funcDownload">
                            <div style="border-right: 1px solid #ebeff5; height: 2.25rem; width: 1px"></div>
                            <p-button
                                icon="pi pi-download"
                                [outlined]="true"
                                [size]="'small'"
                                (click)="(configMulti[activeTableId].funcDownload)"
                                [ngClass]="{
                                    'qc-disabled': configMulti[activeTableId].funcDownload,
                                    'table-btn-dowload': true,
                                }"
                            ></p-button>
                        </div>
                    </div>
                </div>
            </ng-template>
            <ng-template pTemplate="header" let-columns>
                <tr>
                    <th *ngIf="configMulti[activeTableId].rowExpandTemplate" style="width: 5rem"></th>
                    <th *ngIf="configMulti[activeTableId].selectionMode" style="width: 4rem">
                        <ng-container *ngIf="configMulti[activeTableId].selectionMode === 'multiple'">
                            <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                        </ng-container>
                    </th>
                    <th *ngIf="stt" style="width: 4rem">STT</th>
                    <th
                        *ngFor="let col of columns"
                        [pSortableColumn]="col.sort"
                        (click)="onSort(col)"
                        [style.white-space]="'nowrap'"
                        [style]="col.style"
                        pFrozenColumn
                        [alignFrozen]="col?.fixed"
                        [frozen]="col?.fixed ? true : false"
                    >
                        {{ col.header }}
                        <p-sortIcon *ngIf="col.sort" field="{{ col.sort || col.field }}" />
                    </th>
                </tr>
                <ng-container *ngIf="configMulti[activeTableId].headerTemplate" [ngTemplateOutlet]="configMulti[activeTableId].headerTemplate"></ng-container>
                <ng-container *ngIf="configMulti[activeTableId].filterTemplate" [ngTemplateOutlet]="configMulti[activeTableId].filterTemplate"></ng-container>
            </ng-template>

            <ng-template pTemplate="body" let-rowData let-columns="columns" let-rowIndex="rowIndex" let-expanded="expanded">
                <tr
                    [ngClass]="{ 'bg-red-200': isWarning(rowData) }"
                    [pSelectableRowIndex]="rowIndex"
                    [pSelectableRow]="rowData"
                    [pExpandableRow]="configMulti[activeTableId].rowExpandTemplate ? true : false"
                    class="tw-h-fit"
                >
                    <td *ngIf="configMulti[activeTableId].rowExpandTemplate">
                        <p-button
                            type="button"
                            pRipple
                            [pRowToggler]="rowData"
                            [text]="true"
                            [rounded]="true"
                            [plain]="true"
                            [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"
                        />
                    </td>
                    <td *ngIf="configMulti[activeTableId].selectionMode" style="width: 4rem">
                        <ng-container *ngIf="configMulti[activeTableId].selectionMode === 'multiple'; else radioBtn">
                            <p-tableCheckbox
                                [value]="rowData"
                                [hidden]="configMulti[activeTableId]?.rowSelectable && !configMulti[activeTableId]?.rowSelectable(rowData)"
                            />
                        </ng-container>
                        <ng-template #radioBtn>
                            <p-tableRadioButton
                                [value]="rowData"
                                [hidden]="configMulti[activeTableId]?.rowSelectable && !configMulti[activeTableId]?.rowSelectable(rowData)"
                            />
                        </ng-template>
                    </td>
                    <td *ngIf="stt" style="width: 4rem" [rowspan]="">
                        {{ pagination.page * pagination.size + rowIndex + 1 }}
                    </td>
                    <td
                        *ngFor="let col of columns"
                        [style.white-space]="'nowrap'"
                        [style.overflow]="'hidden'"
                        [style.text-overflow]="'ellipsis'"
                        [style]="col.style"
                        pFrozenColumn
                        [alignFrozen]="col?.fixed"
                        [frozen]="col?.fixed ? true : false"
                    >
                        <ng-container [ngSwitch]="true">
                            <!-- Check if there's a template for the column -->
                            <ng-container *ngSwitchCase="!!col.body">
                                <ng-container
                                    [ngTemplateOutlet]="col.body"
                                    [ngTemplateOutletContext]="{
                                        $implicit: rowData,
                                    }"
                                ></ng-container>
                            </ng-container>
                            <!-- Default content rendering -->
                            <ng-container *ngSwitchCase="col.type === 'date'">
                                <!-- Format the date if the column is marked as a date -->
                                {{ rowData[col.field] ? (rowData[col.field] | date: col.format ?? 'dd/MM/yyyy') : col?.fallBackValue }}
                            </ng-container>
                            <ng-container *ngSwitchCase="col.type === 'currency'">
                                <!-- Format the number if the column is marked as currency -->
                                {{ rowData[col.field] ? (rowData[col.field] | currency: 'VND' : 'symbol' : '1.0-4') : col?.fallBackValue }}
                            </ng-container>
                            <ng-container *ngSwitchCase="col.type === 'number'">
                                <!-- Format the number if the column is marked as currency -->
                                {{ rowData[col.field] | number }}
                            </ng-container>
                            <!-- Default content rendering -->
                            <ng-container *ngSwitchCase="col.type === 'link'">
                                <a [routerLink]="getLink(col.url, rowData)">{{ rowData[col.field] ?? col?.fallBackValue }}</a>
                            </ng-container>
                            <ng-container *ngSwitchCase="col.type === 'newTab'">
                                <a [href]="rowData[col.field]" target="_blank" rel="noopener noreferrer">{{ rowData[col.field] ?? col?.fallBackValue }}</a>
                            </ng-container>
                            <ng-container *ngSwitchDefault>
                                {{ rowData[col.field] ?? col?.fallBackValue }}
                            </ng-container>
                        </ng-container>
                    </td>
                </tr>
            </ng-template>
            <ng-template pTemplate="rowexpansion" let-data>
                <tr>
                    <td [attr.colspan]="columnTable.length + 2" style="padding: 0 !important">
                        <ng-container
                            *ngIf="configMulti[activeTableId].rowExpandTemplate"
                            [ngTemplateOutlet]="rowExpandTemplate"
                            [ngTemplateOutletContext]="{ $implicit: data }"
                        ></ng-container>
                    </td>
                </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="30" class="text-center">Không có dữ liệu</td>
                </tr>
            </ng-template>
        </p-table>
    </div>
    <app-paginator
        [tableId]="activeTableId"
        [totalCount]="pagination.totalCount"
        [currentPage]="pagination.page + 1"
        [totalPages]="pagination.totalPage"
        [refetch]="pagination.refetch"
        [size]="pagination.size"
    ></app-paginator>
</div>

<p-overlayPanel #op>
    <p-table [value]="columnAll" [selection]="columnChoose" (selectionChange)="setColumnSelection($event)" [scrollable]="true" scrollHeight="500px">
        <ng-template pTemplate="header">
            <tr>
                <th>
                    <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                </th>

                <th>
                    <!-- <p-columnFilter field="header" matchMode="equals" [showMenu]="false">
            <ng-template
              pTemplate="filter"
              let-value
              let-filter="filterCallback"
            >
              <p-dropdown
                [ngModel]="value"
                (onChange)="filter($event.value)"
                [options]="columnVisible"
                optionLabel="header"
                placeholder="Select One"
                [showClear]="true"
              ></p-dropdown>
            </ng-template>
          </p-columnFilter> -->
                    Tất cả
                </th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rowData>
            <tr>
                <td style="font-size: 14px; padding: 8px 12px">
                    <p-tableCheckbox [value]="rowData" [hidden]="rowData.default" />
                </td>
                <td style="font-size: 14px; padding: 8px 12px">
                    {{ rowData['header'] }}
                </td>
            </tr>
        </ng-template>
    </p-table>
</p-overlayPanel>
