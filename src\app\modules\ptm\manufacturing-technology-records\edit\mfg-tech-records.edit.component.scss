.card {
    background: var(--surface-card);
    padding: 1rem;
    border-radius: 10px;
    margin-bottom: 1rem;
}

.filter-container {
    display: grid;

    // column-gap: 1rem;
    align-items: baseline;

    background: #f5f7fa;
    // padding: 1rem;
    border-radius: 4px;

    /* Cho 4 dropdown bằng nhau, và 1 cột auto cho nút */
    grid-template-columns: 15% repeat(4, 20%);
    gap: 1rem;
}
:host ::ng-deep .p-inputtext[disabled] {
    opacity: 1;
    color: #333;
}
