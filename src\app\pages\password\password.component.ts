import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { CheckboxModule } from 'primeng/checkbox';
import { InputTextModule } from 'primeng/inputtext';
import { PasswordModule } from 'primeng/password';
import { AuthService } from 'src/app/core/auth/auth.service';
import { DialogModule } from 'primeng/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { InputValidationComponent } from 'src/app/shared/components/input-validation/input.validation.component';
import { LoadingService } from 'src/app/shared/services/loading.service';

@Component({
    selector: 'app-password',
    templateUrl: './password.component.html',
    styles: [
        `
            :host ::ng-deep .pi-eye,
            :host ::ng-deep .pi-eye-slash {
                transform: scale(1.6);
                margin-right: 1rem;
                color: var(--primary-color) !important;
            }
        `,
    ],
    standalone: true,
    imports: [
        CommonModule,
        ButtonModule,
        CheckboxModule,
        InputTextModule,
        FormsModule,
        PasswordModule,
        DialogModule,
        InputValidationComponent,
    ],
})
export class PasswordComponent {
    account = {
        email: null,
        rememberMe: true,
        confirmPassword: null,
        forgotPasswordToken: null,
        newPassword: null,
    };
    isActive = '0';

    isSendEmailResetPasswordSuccess: boolean = false;

    constructor(
        private router: Router,
        private auth: AuthService,
        private activeRoute: ActivatedRoute,
        private loadinService: LoadingService,
    ) {
        this.account.forgotPasswordToken = this.activeRoute.snapshot.queryParamMap.get('token');
        this.account.email = this.activeRoute.snapshot.queryParamMap.get('email');
        this.isActive = this.activeRoute.snapshot.queryParamMap.get('active');
    }

    setPassword() {
        // this.auth;
        if (
            !this.account.newPassword ||
            !this.account.newPassword ||
            this.account.newPassword !== this.account.confirmPassword ||
            this.account.newPassword.length < 8
        )
            return;
        setTimeout(() => {
            this.loadinService.show();
        });
        const subcribe = {
            next: () => {
                this.router.navigate(['/login']);
                this.loadinService.hide();
            },
            error: () => {
                this.router.navigate(['/login']);
                this.loadinService.hide();
            },
        };
        if (this.isActive === '1') {
            this.auth.activeNewuser(this.account).subscribe(subcribe);
        } else {
            this.auth.setPassword(this.account).subscribe(subcribe);
        }
    }

    isWeekPassword = () => {
        return this.account.newPassword &&
            !this.account.newPassword.match('^(?=.*[a-z])(?=.*[A-Z])(?=.*d)(?=.*[@$!%*?&])[A-Za-zd@$!%*?&]{8,}$')
            ? true
            : false;
    };
}
