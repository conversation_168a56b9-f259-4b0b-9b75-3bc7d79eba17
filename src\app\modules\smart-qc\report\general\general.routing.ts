import { canAuthorize } from '../../../../core/auth/auth.guard';

export const GeneralRouting = {
    path: 'report/general-report',
    title: '<PERSON><PERSON><PERSON> c<PERSON>o tiến độ tổng hợp',
    canActivate: [canAuthorize],
    data: { authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'ROLE_QC_SUBPM', 'ROLE_QC_CUSTOMER'] },
    loadComponent: () => import('./general.report').then((c) => c.GeneralReportComponent),
};
