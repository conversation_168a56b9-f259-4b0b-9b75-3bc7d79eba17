import { Injectable, NgZone } from '@angular/core';
import { pick, omitBy, isNil, isEmpty, isString, isDate, isArray, every, isUndefined, debounce } from 'lodash';
import { BehaviorSubject, Observable } from 'rxjs';
import { FetchStatus, QueryObserverBaseResult, QueryObserverResult, QueryStatus, injectQuery } from '@ngneat/query';
import { HttpHeaders, HttpResponse } from '@angular/common/http';
import { ActivatedRoute, Router } from '@angular/router';
import { TypeValueRsql } from 'src/app/models/interface';
import { DateUtils } from '../../utils/date-utils';

export interface TableServiceProps<T> {
    tableId: string;
    queryFn?: (filter: ParamsTable, body: Record<string, unknown>) => Observable<HttpResponse<T[]>>;
    manualSearch?: boolean;
    configFilter?: string[];
    configFilterRSQL?: Record<string, TypeValueRsql>;
    defaultParams?: Record<string, unknown>;
    defaultParamsRSQL?: Record<string, unknown>;
    initialData?: T[];
    queryConfig?: Record<string, unknown>;
    filterUrl?: boolean;
    filterLocal?: boolean;
    requiredFilter?: string[];
    before?: (state: TableState<T>) => void; // Added before hook
    after?: (state: TableState<T>) => void; // Added after hook
}

export interface ParamsTable {
    native?: string;
    rsql?: string;
    pageable?: string;
    body?: Record<string, unknown>;
}

export interface FilterTable {
    native: Record<string, unknown>;
    rsql: Record<string, unknown>;
    pageable: Pagination;
}

export interface Pagination {
    page: number;
    size: number;
    sort: string;
    totalPage?: number;
    totalCount?: number;
    first?: boolean;
    last?: boolean;
    refetch?: (() => void) | undefined;
    local?: boolean;
}

export interface TableState<T> {
    props: TableServiceProps<T>;
    params: ParamsTable;
    filter: FilterTable;
    result$: BehaviorSubject<QueryObserverBaseResult<T[], Error>>;
    pagination$: BehaviorSubject<Pagination>;
    rowSelects$: BehaviorSubject<T[]>;
    columnVisible: BehaviorSubject<string[]>;
}

@Injectable()
export class TableCommonService {
    #query = injectQuery();

    private states: Record<string, TableState<unknown>> = {};

    private pageable = {
        page: 0,
        size: 10,
        sort: 'id,desc',
    };

    private debouncedCallApis: Record<string, (tableId: string) => void> = {};
    constructor(
        private router: Router,
        private route: ActivatedRoute,
        private ngZone: NgZone,
    ) {}

    init<T>(props: TableServiceProps<T>): Observable<QueryObserverBaseResult<T[]>> {
        const {
            tableId,
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            manualSearch = false,
            configFilter = [],
            configFilterRSQL = {},
            defaultParams = {},
            defaultParamsRSQL = {},
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            queryConfig = {},
            initialData = [],
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            filterUrl = true,
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            filterLocal = false,
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            requiredFilter = [],
        } = props;

        const queryParams = this.route.snapshot.queryParams;
        const tableIdParam = queryParams['tableId'];

        const prePageable = this.states[tableId] ? this.states[tableId].filter.pageable : this.pageable;
        const queryParamsNative = queryParams['native'] && (!tableIdParam || (tableId && tableId === tableIdParam)) ? JSON.parse(queryParams['native']) : null;

        if (queryParamsNative) {
            Object.keys(queryParamsNative).forEach((key) => {
                const lowerKey = key.toLocaleLowerCase();
                if (lowerKey.includes('date') || lowerKey.includes('time')) {
                    const typeTime = typeof queryParamsNative[key];
                    switch (typeTime) {
                        case 'string':
                            queryParamsNative[key] = new Date(queryParamsNative[key]);
                            break;

                        case 'object':
                            if (Array.isArray(queryParamsNative[key]) && queryParamsNative[key].length === 2) {
                                if (queryParamsNative[key][0] && queryParamsNative[key][0] !== 'null') {
                                    queryParamsNative[key][0] = new Date(queryParamsNative[key][0]);
                                } else {
                                    queryParamsNative[key][0] = null;
                                }
                                if (queryParamsNative[key][1] && queryParamsNative[key][1] !== 'null') {
                                    queryParamsNative[key][1] = new Date(queryParamsNative[key][1]);
                                } else {
                                    queryParamsNative[key][1] = null;
                                }
                            }
                            break;

                        default:
                            break;
                    }
                }
            });
        }

        const filterNative = queryParamsNative ? queryParamsNative : this.buildFilter(defaultParams, configFilter);

        const filterRSQL =
            queryParams['rsql'] && (!tableIdParam || (tableId && tableId === tableIdParam))
                ? JSON.parse(queryParams['rsql'])
                : this.buildFilter(defaultParamsRSQL, Object.keys(configFilterRSQL));

        this.adjustDateRangeFilters(filterRSQL, configFilterRSQL);

        const filterPageable = this.buildPageableFilter(prePageable, defaultParams);

        const state: TableState<T> = {
            props,
            columnVisible: new BehaviorSubject<string[]>([]),
            params: this.buildParams(configFilterRSQL, filterNative, filterRSQL, filterPageable),
            filter: {
                native: filterNative,
                rsql: filterRSQL,
                pageable: filterPageable as unknown as Pagination,
            },
            result$: new BehaviorSubject(this.buildBehaviorSubject('pending', initialData)),
            pagination$: new BehaviorSubject(this.buildInitialPagination(filterPageable, initialData)),
            rowSelects$: new BehaviorSubject<T[]>([]),
        };
        this.states[tableId] = state;
        // Tạo debounce riêng cho tableId nếu chưa có
        if (!this.debouncedCallApis[tableId]) {
            this.debouncedCallApis[tableId] = debounce(this.callApi.bind(this), 300);
        }
        // Gọi API với debounce riêng cho tableId
        this.debouncedCallApis[tableId](tableId);

        return state.result$.asObservable();
    }

    private callApi<T>(tableId: string): void {
        const state = this.states[tableId];

        if (!this.checkRequiredFilter(state.filter, state.props.requiredFilter)) return;

        if (state.props.filterUrl) {
            const queryParams = this.route.snapshot.queryParams;
            const tableIdParam = queryParams['tableId'];
            if (!tableIdParam || (tableId && tableId === tableIdParam)) {
                this.updateUrl(state.filter);
            }
        }

        if (state.props.queryFn) {
            this.ngZone.runOutsideAngular(() => {
                this.query<T>(state, tableId);
            });
        } else {
            this.filterLocalTable(tableId);
        }
    }

    private debouncedAfter = debounce((state) => {
        state.props.after(state);
    }, 300);

    private query<T>(state: TableState<unknown>, tableId: string) {
        // Create a dummy HttpResponse
        const placeholderData: HttpResponse<unknown[]> = new HttpResponse({
            body: state.result$.getValue().data,
            headers: new HttpHeaders({
                'X-Total-Count': state.pagination$.getValue().totalCount,
            }),
            status: 200,
            statusText: 'OK',
            url: '',
        });
        if (state.props.before) {
            state.props.before(state);
        }

        this.#query({
            queryKey: [tableId, state.filter.rsql, state.filter.native, state.filter.pageable],
            queryFn: () => state.props.queryFn(state.params, this.getBody(tableId)),
            placeholderData: placeholderData,
            refetchOnWindowFocus: false,
            staleTime: 0,
            ...state.props.queryConfig,
        }).result$.subscribe((result: QueryObserverResult<HttpResponse<T[]>, Error>) => {
            this.handleApiResponse(result, state);
            if (state.props.after && !result.isFetching) {
                this.debouncedAfter(state);
            }
        });
    }

    private handleApiResponse<T>(result: QueryObserverResult<HttpResponse<T[]>, Error>, state: TableState<T>) {
        const { data, ...rest } = result;
        const headers: HttpHeaders = data.headers;
        const totalCount = headers.get('X-Total-Count') || '0';
        const newResult = {
            ...rest,
            data: data.body,
        };
        this.ngZone.run(() => {
            state.result$.next(newResult as unknown as QueryObserverBaseResult<T[], Error>);

            // Update the pagination BehaviorSubject if the request was successful
            state.pagination$.next(this.buildPagination(state, totalCount, result.refetch));

            // Ensure that any UI updates or other operations run within Angular's NgZone

            // Additional logic that needs to be executed within Angular's zone
        });
    }

    private buildPagination(state: TableState<unknown>, totalCount: string, refetch: (() => void) | undefined): Pagination {
        const pageable = state.filter.pageable;
        return {
            page: pageable.page,
            size: pageable.size,
            sort: pageable.sort,
            totalCount: Number(totalCount),
            totalPage: Math.ceil(Number(totalCount) / pageable.size),
            first: pageable.page === 0,
            last: pageable.page === Math.ceil(Number(totalCount) / pageable.size) - 1,
            refetch,
            local: isUndefined(state.props.queryFn),
        };
    }

    private buildInitialPagination(pageable: Record<string, unknown>, data: unknown[]): Pagination {
        return {
            page: pageable['page'] as number,
            size: pageable['size'] as number,
            sort: pageable['sort'] as string,
            first: true,
            last: false,
            totalCount: data.length,
            totalPage: Math.ceil(data.length / Number(pageable['size'])),
            local: true,
        };
    }

    private buildParams(
        configRsql: Record<string, string>,
        filterNative: Record<string, unknown>,
        filterRSQL: Record<string, unknown>,
        filterPageable: Record<string, unknown>,
    ): ParamsTable {
        return {
            native: this.genQueryNative(filterNative) || '',
            rsql: this.genQueryRSQL(configRsql, filterRSQL) || '',
            pageable: this.genQueryNative(filterPageable),
        };
    }

    private buildFilter(defaultParams: Record<string, unknown>, configFilter: string[]): Record<string, unknown> {
        return omitBy(pick(defaultParams, configFilter), (value) => isNil(value) || (isString(value) && isEmpty(value)));
    }

    private buildPageableFilter(prePageable: Record<string, unknown>, defaultParams: Record<string, unknown>): Record<string, unknown> {
        return pick({ ...this.pageable, ...prePageable, ...defaultParams }, ['page', 'size', 'sort']);
    }

    private updateUrl(filter: FilterTable) {
        this.router.navigate([], {
            queryParams: {
                ...this.route.snapshot.queryParams,
                native: JSON.stringify(filter.native),
                rsql: JSON.stringify(filter.rsql),
            },
            replaceUrl: true,
            queryParamsHandling: 'merge',
        });
    }

    private checkRequiredFilter(filter: FilterTable, requiredFilter: string[] | undefined): boolean {
        if (!requiredFilter || requiredFilter.length === 0) return true;

        const filterKeys = [...Object.keys(filter.native), ...Object.keys(filter.rsql)];
        return requiredFilter.every((requiredKey) => filterKeys.includes(requiredKey));
    }

    private adjustDateRangeFilters(filterRSQL: Record<string, unknown>, configFilterRSQL: Record<string, string>) {
        Object.keys(configFilterRSQL).forEach((key) => {
            if (configFilterRSQL[key] === 'DateRange' && filterRSQL[key]) {
                filterRSQL[key] = (filterRSQL[key] as string[]).map((date) => new Date(date));
            }
        });
    }

    filterLocalTable(tableId: string) {
        const state = this.states[tableId];
        const { native } = state.filter;

        state.result$.next(this.buildBehaviorSubject('pending', state.result$.getValue().data));
        let filterData;

        if (isEmpty(native)) {
            filterData = state.props.initialData;
        } else {
            filterData = state.props.initialData.filter((item) => {
                for (const key in native) {
                    const value = native[key];

                    if (Array.isArray(value) && !isEmpty(value) && !value.includes(item[key])) {
                        // Nếu là mảng giá trị
                        return false;
                    } else if (isString(value)) {
                        // Nếu là chuỗi, kiểm tra xem item[key] có chứa value không
                        if (!item[key]?.toString().includes(value)) {
                            return false;
                        }
                    } else if (this.isDateRange(value)) {
                        // Nếu là ngày, so sánh theo timestamp
                        const splitKey = key.split('&');
                        if (value[0] && item[splitKey[0]] < value[0].getTime()) {
                            return false;
                        }
                        if (value[1] && item[splitKey[1]] >= value[1].getTime()) {
                            return false;
                        }
                    }
                }

                return true;
            });
        }

        // Cập nhật kết quả lọc
        state.result$.next(this.buildBehaviorSubject('success', filterData));
        // Cập nhật thông tin phân trang
        state.pagination$.next({
            page: Number(state.filter.pageable['page']),
            size: Number(state.filter.pageable['size']),
            sort: String(state.filter.pageable['sort']),
            first: state.filter.pageable['page'] === 0,
            last: state.filter.pageable['page'] === Math.ceil(filterData.length / Number(state.filter.pageable['size'])) - 1,
            totalCount: filterData.length,
            totalPage: Math.ceil(filterData.length / Number(state.filter.pageable['size'])),
            local: true,
        });

        this.states[tableId] = state;
    }

    getBody(tableId: string) {
        const state = this.states[tableId];
        const body = { ...state.filter.native, ...state.filter.rsql };
        for (const property in body) {
            let splitKey = [];
            let value = body[`${property}`];
            if (property.includes('&')) {
                splitKey = property.split('&');
            }
            if (isDate(value)) {
                value = value.getTime() + 86400000;
            }

            if (this.isDateRange(value)) {
                if (splitKey[0]) {
                    body[`${splitKey[0]}`] = value[0] ? value[0].getTime() : null;
                }
                if (splitKey[1]) {
                    body[`${splitKey[1]}`] = value[1] ? value[1].getTime() + 86400000 : null; // date-range cần cộng
                }
            } else if (isArray(value) && value.length > 0) {
                body[`${property}Sql`] = value.map((item) => (typeof item === 'string' ? `'${item}'` : item)).join(',');
            }
        }
        return body;
    }

    updateFilter(tableId: string, newFilter: Partial<FilterTable['native']>) {
        const state = this.states[tableId];
        if (this.isObjectEmptyOrUndefined(newFilter) && this.isObjectEmptyOrUndefined(state.filter.native)) return;
        // const extraFilter = state.props?.configFilter || [];
        // state.filter.native = omitBy(
        //     pick({ ...state.filter.native, ...newFilter }, extraFilter),
        //     (value) => isNil(value) || (isString(value) && isEmpty(value)),
        // );
        state.filter.native = omitBy({ ...state.filter.native, ...newFilter }, (value) => isNil(value) || (isString(value) && isEmpty(value)));
        state.params.native = this.genQueryNative(state.filter.native);
        state.filter.pageable['page'] = 0;
        state.params.pageable = this.genQueryNative(this.convertPaginationToRecord(state.filter.pageable));
        this.states[tableId] = state;
        if (!state.props.manualSearch) {
            this.debouncedCallApis[tableId](tableId);
        }
    }

    updateFilterPageable(tableId: string, newFilter: Partial<FilterTable['pageable']>) {
        const state = this.states[tableId];

        state.filter.pageable = omitBy(
            pick({ ...state.filter.pageable, ...newFilter }, ['page', 'size', 'sort', 'local']),
            (value) => isNil(value) || (isString(value) && isEmpty(value)),
        ) as unknown as Pagination;
        state.params.pageable = this.genQueryNative(this.convertPaginationToRecord(state.filter.pageable));
        this.states[tableId] = state;

        this.debouncedCallApis[tableId](tableId);
    }

    updateFilterRSQL(tableId: string, newFilter: Partial<FilterTable['rsql']>) {
        const state = this.states[tableId];
        if (this.isObjectEmptyOrUndefined(newFilter) && this.isObjectEmptyOrUndefined(state.filter.rsql)) return;
        const extraFilterRSQL = Object.keys(state.props?.configFilterRSQL || {});
        state.filter.rsql = omitBy(
            pick({ ...state.filter.rsql, ...newFilter }, extraFilterRSQL),
            (value) => isNil(value) || (isString(value) && isEmpty(value)),
        );
        state.params.rsql = this.genQueryRSQL(state.props.configFilterRSQL, state.filter.rsql);
        state.filter.pageable['page'] = 0;
        state.params.pageable = this.genQueryNative(this.convertPaginationToRecord(state.filter.pageable));
        this.states[tableId] = state;
        if (!state.props.manualSearch) {
            this.debouncedCallApis[tableId](tableId);
        }
    }

    getColumnVisible(tableId: string): BehaviorSubject<string[]> {
        return this.states[tableId].columnVisible;
    }

    updateColumnVisible(tableId: string, arrayColumn: string[]) {
        if (!tableId || !this.states[tableId]) return;

        this.states[tableId].columnVisible.next(arrayColumn);
    }

    genQueryRSQL(configFilterRSQL: Record<TypeValueRsql, string>, filterRSQL: Record<string, unknown>): string {
        const conditions = [];
        for (const key in configFilterRSQL) {
            const filterValue = filterRSQL[key]; // Store the filterRSQL[key] value in a variable

            if (filterValue === null && filterValue === undefined) continue;
            if (isArray(filterValue) && filterValue.length > 0) {
                let type;
                if (configFilterRSQL[key] === 'DateRange') {
                    type = 'object';
                } else {
                    type = typeof filterValue[0];
                }
                switch (type) {
                    case 'number':
                        this.handleIsArrayNumber(filterValue, configFilterRSQL[key], key, conditions);
                        break;
                    case 'string':
                        this.handleIsArrayString(filterValue, configFilterRSQL[key], key, conditions);
                        break;
                    case 'boolean':
                        this.handleIsArrayBoolean(filterValue, configFilterRSQL[key], key, conditions);
                        break;
                    case 'object':
                        if ((filterValue[0] && isDate(filterValue[0])) || (filterValue[1] && isDate(filterValue[1]))) {
                            this.handleIsDateRange(filterValue, configFilterRSQL[key], key, conditions);
                        }
                        break;
                }
            } else {
                if (configFilterRSQL[key] === 'Month') {
                    this.handleIsMonth(filterValue as Date, key, conditions);
                } else {
                    switch (typeof filterValue) {
                        case 'number':
                            this.handleIsNumber(filterValue, configFilterRSQL[key], key, conditions);
                            break;
                        case 'string':
                            this.handleIsString(filterValue, configFilterRSQL[key], key, conditions);
                            break;
                        case 'boolean':
                            this.handleIsBoolean(filterValue, configFilterRSQL[key], key, conditions);
                            break;
                    }
                }
            }
        }

        return conditions.join(';');
    }

    private handleIsString(filterValue: string, configFilterRSQL: TypeValueRsql, key: string, conditions: unknown[]) {
        switch (configFilterRSQL) {
            case 'Text':
                conditions.push(`${key}=="*${filterValue}*"`); // Safely use filterValue
                break;
            case 'TextURL':
                conditions.push(`${key}=="*${filterValue.replaceAll('http', '').replaceAll('https', '')}*"`); // Safely use filterValue
                break;
            case 'TextExact':
                conditions.push(`${key}=="${filterValue}"`);
                break;
            case 'TextOr':
                const arrKey = key.split('Or');
                const orConditions = arrKey.map((currentKey) => `${currentKey}==*${filterValue}*`).join(' or ');
                conditions.push(`(${orConditions})`);
                break;
        }
    }

    private handleIsNumber(filterValue: number, configFilterRSQL: TypeValueRsql, key: string, conditions: unknown[]) {
        switch (configFilterRSQL) {
            case 'Number':
                conditions.push(`${key}==${filterValue}`);
                break;
            case 'NotEqual':
                if (filterValue !== null) {
                    conditions.push(`${key}!=${filterValue}`);
                }
                break;
        }
    }

    private handleIsMonth(date: Date, key: string, conditions: unknown[]) {
        if (date) {
            conditions.push(`${key}==${DateUtils.convertToTimestampHCM(date)}`);
        }
    }

    private handleIsBoolean(filterValue: boolean, configFilterRSQL: TypeValueRsql, key: string, conditions: unknown[]) {
        switch (configFilterRSQL) {
            case 'Boolean':
                if (filterValue) {
                    conditions.push(`${key}==${filterValue}`);
                }
                break;
            case 'IsNull':
                if (filterValue === true) {
                    conditions.push(`${key}==null`);
                } else {
                    conditions.push(`${key}!=null`);
                }
                break;
        }
    }

    private handleIsArrayString(filterValue: string[], configFilterRSQL: TypeValueRsql, key: string, conditions: unknown[]) {
        let searchValue;
        switch (configFilterRSQL) {
            case 'MultiText':
                if (Array.isArray(filterValue) && filterValue.length > 0) {
                    searchValue = filterValue.toString();
                    conditions.push(`${key}=in=("${searchValue.replace(/,/g, '","')}")`);
                }
                break;
            case 'MultiTextOr':
                if (Array.isArray(filterValue) && filterValue.length > 0) {
                    searchValue = (filterValue as string[]).map((element) => `${key}==*${element}*`).join(',');
                    conditions.push(searchValue);
                }
                break;
        }
    }

    private handleIsArrayNumber(filterValue: number[], configFilterRSQL: TypeValueRsql, key: string, conditions: unknown[]) {
        let searchValue;
        switch (configFilterRSQL) {
            case 'SetLong':
                if (Array.isArray(filterValue) && filterValue.length > 0) {
                    searchValue = filterValue.toString();
                    conditions.push(`${key}=in=(${searchValue})`);
                }
                break;
            case 'SetLongNotIn':
                if (Array.isArray(filterValue) && filterValue.length > 0) {
                    searchValue = filterValue.toString();
                    conditions.push(`${key}=out=(${searchValue})`);
                }
                break;
            case 'NumberRange':
                if (Array.isArray(filterValue) && filterValue[0] !== null) {
                    conditions.push(`${key}>${filterValue[0]}`);
                }
                if (Array.isArray(filterValue) && filterValue[1] !== null) {
                    conditions.push(`${key}<${filterValue[1]}`);
                }
                break;
            case 'MultiNumber':
                if (Array.isArray(filterValue) && filterValue.length > 0) {
                    let paramNull = '';
                    if (filterValue.includes(null)) {
                        filterValue = filterValue.filter((item) => item !== null);
                        paramNull = 'null';
                    }
                    if (Array.isArray(filterValue) && filterValue.length > 0) {
                        conditions.push(`${key}=in=(${filterValue.toString()})`);
                    }
                    if (paramNull.length > 0) {
                        if (Array.isArray(filterValue) && filterValue.length > 1) {
                            conditions.push(`${key}==null`);
                        } else {
                            conditions.push(`${key}==null`);
                        }
                    }
                }
                break;
        }
    }

    private handleIsArrayBoolean(filterValue: boolean[], configFilterRSQL: TypeValueRsql, key: string, conditions: unknown[]) {
        let searchValue;
        switch (configFilterRSQL) {
            case 'MultiBoolean':
                if (Array.isArray(filterValue) && filterValue.length > 0) {
                    searchValue = filterValue.map((element) => `${key}==${element}`).join(',');
                    conditions.push(`(${searchValue})`);
                }
                break;
        }
    }

    private handleIsDateRange(filterValue: Date[], configFilterRSQL: TypeValueRsql, key: string, conditions: unknown[]) {
        switch (configFilterRSQL) {
            case 'DateRange':
                if (this.isDateRange(filterValue) && filterValue.length > 0) {
                    if (filterValue[0] && isDate(filterValue[0])) {
                        const startDate = new Date(filterValue[0]);
                        startDate.setHours(0, 0, 0, 0); // Đặt về 0h 0m 0s 0ms
                        conditions.push(`${key}>=${startDate.getTime()}`);
                    }
                    if (filterValue[1] && isDate(filterValue[1])) {
                        const endDate = new Date(filterValue[1]);
                        endDate.setHours(23, 59, 59, 999); // Đặt về 23h 59m 59s 999ms
                        conditions.push(`${key}<=${endDate.getTime()}`);
                    }
                }
                break;

            case 'DateRangeInfinity':
                if (this.isDateRange(filterValue) && filterValue.length > 0) {
                    if (filterValue[0] && isDate(filterValue[0]) && filterValue[1]) {
                        const startDate = new Date(filterValue[0]);
                        startDate.setHours(0, 0, 0, 0); // Đặt về 0h 0m 0s 0ms
                        conditions.push(`${key}>=${startDate.getTime()}`);
                    } else if (filterValue[0] && isDate(filterValue[0]) && !filterValue[1]) {
                        const startDate = new Date(filterValue[0]);
                        startDate.setHours(0, 0, 0, 0); // Đặt về 0h 0m 0s 0ms
                        conditions.push(`${key}>=${startDate.getTime()},${key}==null`);
                    }
                    if (filterValue[1] && isDate(filterValue[1])) {
                        const endDate = new Date(filterValue[1]);
                        endDate.setHours(23, 59, 59, 999); // Đặt về 23h 59m 59s 999ms
                        conditions.push(`${key}<=${endDate.getTime()}`);
                    }
                }
                break;
        }
    }

    private convertPaginationToRecord(pagination: Pagination): Record<string, unknown> {
        return {
            page: pagination.page,
            size: pagination.size,
            sort: pagination.sort,
            // add other fields as necessary
        };
    }

    private genQueryNative(filter: Record<string, unknown>): string {
        let queryString = '';
        for (const key in filter) {
            const value = filter[key];
            if (value !== null && value !== undefined) {
                if (Array.isArray(value)) {
                    if (this.isDateRange(value)) {
                        // Chuyển đổi giá trị của mảng date-range thành milliseconds
                        const startTimestamp = value[0] ? value[0].getTime() : '';
                        const endTimestamp = value[1] ? value[1].getTime() : '';
                        queryString += `&${encodeURIComponent(key)}=${startTimestamp},${endTimestamp}`;
                    } else {
                        const arrayFormat = value.join(',');
                        queryString += `&${encodeURIComponent(key)}=${arrayFormat}`;
                    }
                } else if (isDate(value)) {
                    // Chuyển đổi giá trị Date thành milliseconds
                    const timestamp = value.getTime();
                    queryString += `&${encodeURIComponent(key)}=${timestamp}`;
                } else {
                    queryString += `&${encodeURIComponent(key)}=${value}`;
                }
            }
        }
        return queryString;
    }

    getState(tableId: string): TableState<unknown> {
        if (!tableId) return null;
        return this.states[tableId];
    }

    getPagination(tableId: string): BehaviorSubject<Pagination> {
        if (!tableId) return new BehaviorSubject<Pagination>(null);
        return this.states[tableId].pagination$;
    }

    getFilter(tableId: string): FilterTable {
        if (!tableId) return null;
        return this.states[tableId].filter;
    }

    getParams(tableId: string): ParamsTable {
        if (!tableId) return null;
        return this.states[tableId].params;
    }

    getRowSelect(tableId: string): BehaviorSubject<unknown> {
        if (!tableId) return new BehaviorSubject<unknown>([]);
        return this.states[tableId].rowSelects$;
    }

    updateRowSelect<T>(tableId: string, selectedRows: T[]): void {
        const state = this.states[tableId];
        state.rowSelects$.next(selectedRows);
    }

    isDateRange(value: unknown): boolean {
        if (Array.isArray(value) && value.length === 2) {
            return isDate(value[0]) || isDate(value[1]);
        }
        return false;
    }

    isObjectEmptyOrUndefined(obj) {
        return isEmpty(obj) || every(obj, isUndefined);
    }

    private buildBehaviorSubject<T>(state: QueryStatus, data: T[] = null, error: Error = null): QueryObserverBaseResult<T[], Error> {
        let fetchStatus: FetchStatus;
        let isError: boolean;
        let isFetched: boolean;
        let isFetching: boolean;
        let isInitialLoading: boolean;
        let isPending: boolean;
        let isStale: boolean;
        let isSuccess: boolean;
        let status: QueryStatus;

        switch (state) {
            case 'pending':
                fetchStatus = 'fetching';
                isError = false;
                isFetched = false;
                isFetching = true;
                isInitialLoading = true;
                isPending = false;
                isStale = false;
                isSuccess = false;
                status = 'pending';
                break;

            case 'error':
                fetchStatus = 'paused';
                isError = true;
                isFetched = true;
                isFetching = false;
                isInitialLoading = false;
                isPending = false;
                isStale = false;
                isSuccess = false;
                status = 'error';
                break;
            case 'success':
                fetchStatus = 'idle';
                isError = false;
                isFetched = true;
                isFetching = false;
                isInitialLoading = false;
                isPending = true;
                isStale = false;
                isSuccess = false;
                status = 'success';
                break;
        }

        const initialData = {
            data: data,
            error: error,
            dataUpdatedAt: Date.now(),
            errorUpdateCount: 0,
            errorUpdatedAt: 0,
            failureCount: 0,
            failureReason: error,
            fetchStatus: fetchStatus,
            isError: isError && !!error,
            isFetched: isFetched,
            isFetchedAfterMount: true,
            isFetching: isFetching,
            isInitialLoading: isInitialLoading,
            isLoading: false,
            isLoadingError: false,
            isPaused: false,
            isPending: isPending,
            isPlaceholderData: false,
            isRefetchError: false,
            isRefetching: false,
            isStale: isStale,
            isSuccess: isSuccess && !error,
            status: status,
            refetch: () => Promise.resolve(null),
            promise: null,
        };

        return initialData;
    }
}
