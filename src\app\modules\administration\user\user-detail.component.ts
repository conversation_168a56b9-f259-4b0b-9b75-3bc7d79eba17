import {Component, OnInit, ViewChild, Template<PERSON>ef, inject} from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { TagModule } from 'primeng/tag';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { AvatarModule } from 'primeng/avatar';
import { AlertService } from 'src/app/shared/services/alert.service';
import { InputTextModule } from 'primeng/inputtext';
import {ReactiveFormsModule, FormBuilder, FormGroup, Validators, FormsModule} from '@angular/forms';
import { MultiSelectModule } from 'primeng/multiselect';
import { DropdownModule } from 'primeng/dropdown';
import { ConfirmationService } from 'primeng/api';
import { InputSwitchModule } from 'primeng/inputswitch';
import { catchError, of } from 'rxjs';
import { BaseUserService } from '../../../services/administration/admin/user.service';
import { RoleService } from '../../../services/administration/admin/role.service';
import { AreaService } from '../../../services/administration/area/area.service';
import { AuthService } from 'src/app/core/auth/auth.service';
import { Area, Role, User } from '../../../models/interface';
import { SubHeaderComponent } from '../../../shared/components/sub-header/sub-header.component';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import {isArray} from "lodash";
import {finalize} from "rxjs/operators";

@Component({
    selector: 'app-user-detail',
    standalone: true,
    imports: [
        ButtonModule,
        TagModule,
        CommonModule,
        AvatarModule,
        RouterLink,
        InputTextModule,
        ReactiveFormsModule,
        MultiSelectModule,
        DropdownModule,
        InputSwitchModule,
        SubHeaderComponent,
        FormCustomModule,
        HasAnyAuthorityDirective,
        FormsModule
    ],
    providers: [BaseUserService, RoleService, AreaService, AuthService],
    templateUrl: './user-detail.component.html'
})
export class UserDetailComponent implements OnInit {
    @ViewChild('form') form: TemplateRef<FormComponent>;

    userService = inject(BaseUserService);
    roleService = inject(RoleService);
    loadingService = inject(LoadingService);
    alertService = inject(AlertService);
    route = inject(ActivatedRoute);
    router = inject(Router);
    confirmationService = inject(ConfirmationService);
    authService = inject(AuthService);
    areaService = inject(AreaService);
    fb = inject(FormBuilder);

    itemsHeader = [{ label: 'Quản lý người dùng', url: '/administration/user/' }, { label: 'Chi tiết người dùng' }];
    userId: number | null = null;
    editMode: boolean = false;
    formGroup: FormGroup;
    systemRoles: Role[] = [];
    normalRoles: Role[] = [];
    roleOption: Role[] = [];
    areaOption: Area[] = [];
    departmentOption: any[] = [];

    user: User
    status;
    active: boolean = true
    activeClass;
    title;
    iconClass;


    setStatus = () => {
        const statusTitle = {
            true: 'Hoạt động', // ngừng hoạt động
            false: 'Ngừng hoạt động', //kích hoạt
            null: 'Đăng ký', // đăng ký
        };
        const iconStyle = {
            true: 'success',
            false: 'danger',
            null: 'warning',
        };

        const statusStyle = {
            true: { color: '#00b87b' },
            false: { color: '#f4516c' },
            null: { color: '#fba65d' },
        };
        if (this.user.active === true) {
            this.status = 'global.common.active';
        } else if (this.user.active === null || this.user.active === undefined) {
            this.status = 'global.common.register';
        } else {
            this.status = 'global.common.archive';
        }
        this.activeClass = statusStyle['' + this.user.active];
        this.title = statusTitle['' + this.user.active];
        this.iconClass = iconStyle['' + this.user.active];
    };

    constructor() {
        this.initForm();
    }

    ngOnInit() {
        this.route.data.subscribe((data) => {
            this.editMode = data['mode'] === 'edit';
        });

        this.route.paramMap.subscribe((params) => {
            this.userId = params.get('id') ? Number(params.get('id')) : null;
            if (this.userId) {
                this.loadUserData();
            } else {
                this.formGroup.enable();
            }
        });

        this.roleService.getPage('query=&page=0&size=1000').subscribe((res) => {
            this.roleOption = res.body;
            this.systemRoles = this.roleOption.filter((a) => a.type);
            this.normalRoles = this.roleOption.filter((a) => !a.type);
        });

        this.userService.getAllDeparment('query=&page=0&size=200').subscribe((res) => {
            this.departmentOption = res;
        });

        this.areaService.getAll().subscribe((res) => {
            this.areaOption = res;
        });
    }

    initForm(user?: User) {
        const area = user && isArray(user.areas) ? user?.areas?.[0] : null
        const department = user && isArray(user.departments) ? user?.departments?.[0] : null
        this.formGroup = this.fb.group({
            id: [user?.id],
            email: [user?.email || '', [Validators.required, Validators.email]],
            fullName: [user?.fullName || '', Validators.required],
            phone: [user?.phone || ''],
            note: [user?.note || ''],
            systemRoles: [user?.systemRoles || [], Validators.required],
            normalRoles: [user?.normalRoles || [], Validators.required],
            area: [area, Validators.required],
            department: [department, Validators.required],
            active: [user?.active ?? true]
        });

        if (!this.editMode && this.userId) {
            this.formGroup.disable();
        }
    }

    loadUserData() {
        this.loadingService.show();
        this.userService.getOne(this.userId!).subscribe({
            next: (res) => {
                this.user = res
                this.initForm({
                    ...res,
                    systemRoles: res.roles.filter((a) => a.type),
                    normalRoles: res.roles.filter((a) => !a.type),
                    areas: res.areas,
                    departments: res.departments
                });

                this.formGroup.get('email').disable();
                this.setStatus();
                if(this.editMode) {
                    this.itemsHeader = [{label: 'Quản lý người dùng', url: '/administration/user/'}, {
                        label: res.email,
                        url: '/administration/user/' + res.id
                    }, {label: "Chỉnh sửa"}];
                } else {
                    this.itemsHeader = [{label: 'Quản lý người dùng', url: '/administration/user/'}, {
                        label: res.email,
                        url: '/administration/user/' + res.id
                    }, ];
                }
                this.loadingService.hide();
                this.active = res.active
            },
            error: (e) => {
                this.alertService.handleError(e);
                this.loadingService.hide();
            }
        });
    }

    getSeverity(state: boolean): string {
        switch (state) {
            case true:
                return 'success';
            case false:
                return 'warning';
            default:
                return 'info';
        }
    }

    getStateText(state: boolean): string {
        switch (state) {
            case true:
                return 'Hoạt động';
            case false:
                return 'Chưa hoạt động';
            default:
                return 'Đăng kí';
        }
    }

    private buildUserFromForm(formValue: Partial<User>): User {
        return {
            ...(this.user ?? {}),
            ...formValue,
            areas: formValue.area ? [formValue.area] : [],
            departments: formValue.department ? [formValue.department] : [],
            roles: [...(formValue.systemRoles || []), ...(formValue.normalRoles || [])]
    } as User;
    }

    onSubmit(formValue: Partial<User>) {
        if (this.formGroup.invalid) {
            this.formGroup.markAllAsTouched();
            return;
        }

        const user = this.buildUserFromForm(formValue);

        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: this.userId ? 'Cập nhật thông tin người dùng' : 'Tạo tài khoản người dùng',
            icon: 'pi pi-exclamation-triangle',
            acceptIcon: 'none',
            rejectIcon: 'none',
            rejectButtonStyleClass: 'p-button-text',
            acceptButtonStyleClass: 'p-button-outlined',
            acceptLabel: 'Đồng ý',
            rejectLabel: 'Hủy',
            accept: () => {
                this.loadingService.show();
                this.userService.update(user).pipe(
                    catchError((error) => {
                        this.alertService.handleError(error);
                        return of(null);
                    }),
                    finalize(() => {
                        this.loadingService.hide();
                    })
                ).subscribe({
                    next: (res: User | null) => {
                        if (res) {
                            this.alertService.success(
                                'Thành công',
                                this.userId ? 'Chỉnh sửa thành công' : 'Tạo tài khoản thành công'
                            );
                            this.router.navigate(['/administration/user', res.id]);
                        }
                    }
                });
            }
        });
    }

    setActiveStatus = () => {
        if (this.user.active === null) return;

        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: this.user.active ? 'Khóa tài khoản người dùng' : 'Kích hoạt tài khoản người dùng',
            icon: 'pi pi-exclamation-triangle',
            acceptIcon: 'none',
            rejectIcon: 'none',
            rejectButtonStyleClass: 'p-button-text',
            acceptButtonStyleClass: 'p-button-outlined',
            acceptLabel: 'Đồng ý',
            rejectLabel: 'Hủy',
            accept: () => {
                this.loadingService.show();

                if (this.user.active) {
                    this.userService.deactivate(this.user.id).subscribe(() => {
                        this.userService.getOne(this.user.id).subscribe({
                            next: (res) => {
                                this.user = { active: null, ...res };
                                this.formGroup.patchValue({ active: this.user.active });
                                this.loadingService.hide();
                                this.setStatus();
                            },
                            error: () => {
                                this.loadingService.hide();
                            },
                        });
                    });
                } else {
                    this.userService.activate(this.user.id).subscribe(() => {
                        this.userService.getOne(this.user.id).subscribe({
                            next: (res) => {
                                this.user = { active: null, ...res };
                                this.formGroup.patchValue({ active: this.user.active });
                                this.loadingService.hide();
                                this.setStatus();
                            },
                            error: () => {
                                this.loadingService.hide();
                            },
                        });
                    });
                }
            },
        });
    };
}
