import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '../../base.service';
import { MaintenanceCard } from '../../../models/interface/smart-qc';

@Injectable()
export class MaintenanceCardService extends BaseService<MaintenanceCard> {
    constructor(protected override http: HttpClient) {
        super(http, '/smart-qc/api/maintenance-card');
    }

    getMaintenanceCardDetailImage(templateId: number) {
        return this.http.get<MaintenanceCard[]>(`${this.baseUrl}/get-checklist-detail-image?templateId=` + templateId, {
            observe: 'response',
        });
    }
}
