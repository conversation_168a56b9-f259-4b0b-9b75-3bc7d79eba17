// src/app/utils/hash.ts
import SparkMD5 from 'spark-md5';

/**
 * Tính MD5 của một chuỗi
 */
export function md5String(text: string): string {
    return SparkMD5.hash(text);
}

/**
 * Tính MD5 của một File (ArrayBuffer)
 */
export async function md5File(file: File): Promise<string> {
    const buffer = await file.arrayBuffer();
    // SparkMD5.ArrayBuffer.hash trả về MD5 hex string
    return SparkMD5.ArrayBuffer.hash(buffer);
}
