import { Component, EventEmitter, Input, OnInit, Output, TemplateRef, ViewChild } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { ApiResponse } from 'src/app/models/interface';
import { ButtonGroupFileComponent } from '../button-group-file/button-group-file.component';
import { CommonModule } from '@angular/common';
import { FormCustomModule } from '../../form-module/form.custom.module';

@Component({
    selector: 'app-popup-upload',
    templateUrl: './popup-upload.component.html',
    styleUrls: ['./popup-upload.component.scss'],
    standalone: true,
    imports: [
        ButtonModule,
        DialogModule,
        ButtonGroupFileComponent,
        ReactiveFormsModule,
        CommonModule,
        FormCustomModule,
    ],
})
export class PopupUploadComponent implements OnInit {
    @Input() header: string = ''; // Header cho dialog
    @Input() label: string = 'Button';
    @Input() types: string[] | null = null; // Accept an array of file types or null for all types
    @Input() formItems: { [key: string]: FormControl };
    @Input() urlError: string;
    @Input() classButton: string = '';
    @Input() description: string;
    @Input() urlTemplate: string;
    @Input() service: '/auth/api' | '/smart-qc/api' | '/sc/api' = '/sc/api';
    @Input() severity: 'secondary' | 'success' | 'info' | 'warning' | 'help' | 'danger' | 'contrast' | 'primary' =
        'primary';
    @Input() blodResponse: Blob;
    @Input() formTemplate: TemplateRef<Element>;
    @Input() disabled: boolean = false;
    @Output() onUpload = new EventEmitter<{ value: unknown; close: () => void }>();
    @Output() onFileSelected = new EventEmitter<File | null>(); // Trả lại file đã chọn cho component cha
    @Output() onClose = new EventEmitter<void>(); // Event khi người dùng hủy bỏ popup
    @Output() onOpen = new EventEmitter<void>(); // Event khi người dùng hủy bỏ popup
    @Output() onClickDowload = new EventEmitter<null>();

    formGroup: FormGroup;
    fileUpload: File | null = null;
    responseUploaded: ApiResponse = {
        code: 1, // 1: Thành công, 0: Lỗi
        message: '',
    };

    // Biến để điều khiển popup
    @Input() isVisible: boolean = false;

    // Button group file
    @ViewChild(ButtonGroupFileComponent) buttonGroupFileComponent!: ButtonGroupFileComponent;
    // End button group file

    constructor(private fb: FormBuilder) {
        this.initForm();
    }

    ngOnInit(): void {
        if (this.formItems) {
            for (const key in this.formItems) {
                if (this.formItems.hasOwnProperty(key)) {
                    this.formGroup.addControl(key, this.formItems[key]);
                }
            }
        }
    }

    initForm(): void {
        this.formGroup = this.fb.group({
            file: new FormControl(null, Validators.required),
        });
    }

    handleSelectFile(file: File): void {
        this.fileUpload = file;
        this.formGroup.get('file').setValue(file);
        this.onFileSelected.emit(file); // Emit file đã chọn ra ngoài
    }

    handleClearFile() {
        this.formGroup.get('file').setValue(null);
    }
    handleUpload(): void {
        if (this.formGroup.valid && this.fileUpload && this.onUpload) {
            this.onUpload.emit({ value: this.formGroup.value, close: () => this.closeDialog() }); // Gọi hàm upload từ bên ngoài truyền vào
        }
    }

    closeDialog(): void {
        this.formGroup.get('file').setValue(null);
        this.isVisible = false; // Đóng popup
        this.urlError = null;
        this.blodResponse = null;
        this.onClose.emit();

        // Reset
        this.buttonGroupFileComponent.removeFile(-1);
    }

    openDialog(): void {
        this.isVisible = true; // Mở popup
        this.onOpen.emit();
    }

    handleClickDownload() {
        this.onClickDowload.emit();
    }
}
