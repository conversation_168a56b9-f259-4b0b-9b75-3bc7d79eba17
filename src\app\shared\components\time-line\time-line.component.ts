import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
export interface TimelineItem {
    name: string;
    des?: string;
    icon?: string;
    value: string | number;
    disabled?: boolean;
}
@Component({
    selector: 'app-time-line',
    standalone: true,
    imports: [CommonModule],
    templateUrl: './time-line.component.html',
    styleUrls: ['./time-line.component.scss'],
})
export class TimeLineComponent implements OnChanges {
    @Input() items: TimelineItem[] = [];
    @Input() activeValue: number | string;
    @Input() value: number | string;
    @Input() direction: 'horizontal' | 'vertical' = 'horizontal';
    @Output() onStatusClick = new EventEmitter<TimelineItem>();

    indexActive: number = 0;
    statusClick(item: TimelineItem) {
        if (!item.disabled) {
            this.onStatusClick.emit(item);
        }
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['value']) {
            // Tìm index khớp
            this.indexActive = this.items.findIndex((item) => this.value === item.value);

            // Nếu không tìm thấy, kiểm tra nếu value > tất cả item.value thì chọn item cuối
            if (this.indexActive === -1 && this.items?.length > 0) {
                const isGreaterThanAll = this.items.every((item) => this.value > item.value);
                if (isGreaterThanAll) {
                    this.indexActive = this.items.length - 1;
                    this.activeValue = this.items[this.items.length - 1].value;
                }
            }
        }
    }

    getIconClass(item: TimelineItem): string {
        return item.icon ? item.icon : 'pi pi-map-marker';
    }

    getClasses(item: TimelineItem) {
        return {
            [this.getIconClass(item)]: true,
            'tw-bg-gray-200': true,
            'tw-bg-green-400': this.activeValue === item.value,
        };
    }
}
