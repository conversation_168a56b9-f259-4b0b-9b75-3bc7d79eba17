<p-table [value]="tableRows" [autoLayout]="true" class="version-table">
    <ng-template pTemplate="header">
        <tr>
            <th *ngFor="let col of columns">{{ col }}</th>
        </tr>
    </ng-template>

    <ng-template pTemplate="body" let-row>
        <tr>
            <ng-container *ngFor="let key of versionKeys">
                <td>
                    <ng-container *ngIf="row[key] as version">
                        <p-card class="version-card" [styleClass]="version.isCurrent === 1 ? 'current-version' : ''">
                            <div class="card-content">
                                <div><strong>Version:</strong> {{ version.version }}</div>
                                <div><strong>Last update:</strong> {{ version.lastUpdate | date: 'dd/MM/yyyy HH:mm:ss' }}</div>
                                <div><strong>Trạng thái hồ sơ:</strong> {{ version.statusName }}</div>
                            </div>
                            <div class="action-group">
                                <button
                                    pButton
                                    type="button"
                                    icon="pi pi-ellipsis-v"
                                    class="p-button-text p-button-sm menu-btn"
                                    (click)="onMenuClick($event, version, key)"
                                ></button>
                                <p-checkbox
                                    binary="true"
                                    [(ngModel)]="version.selected"
                                    (ngModelChange)="onCheckboxChange()"
                                    inputId="chk-{{ key }}-{{ version.version }}"
                                ></p-checkbox>
                            </div>
                        </p-card>
                        <p-menu #menu [popup]="true" [model]="menuItems" appendTo="body">
                            <!-- Định nghĩa lại cách render mỗi item -->
                            <ng-template pTemplate="item" let-item>
                                <a *appHasAnyAuthority="item.authority" pMenuItem [item]="item" class="p-menuitem-link">
                                    <i *ngIf="item.icon" class="{{ item.icon }} p-menuitem-icon"></i>
                                    <span class="p-menuitem-text">{{ item.label }}</span>
                                </a>
                            </ng-template>
                        </p-menu>
                    </ng-container>
                </td>
            </ng-container>
        </tr>
    </ng-template>
    <!-- Nếu tableRows null/undefined/[] thì hiển thị thông báo này -->
    <ng-template pTemplate="emptymessage">
        <tr>
            <td [attr.colspan]="columns.length" class="text-center tw-text-gray-500 tw-italic">Sản phẩm chưa có hồ sơ</td>
        </tr>
    </ng-template>
</p-table>
