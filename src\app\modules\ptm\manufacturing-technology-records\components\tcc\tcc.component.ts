import { Component, inject, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormArray, FormBuilder, FormGroup, FormsModule } from '@angular/forms';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { InfoShareComponent } from '../info-share/info-share.component';
import { ButtonModule } from 'primeng/button';
import { TableModule } from 'primeng/table';
import { DropdownModule } from 'primeng/dropdown';
import { FormCustomModule } from '../../../../../shared/form-module/form.custom.module';
import { TccDirectLabor, TccProductionPlan, TccProductionTime, TccSection, TccStation } from '../../../../../models/interface/ptm/tcc';
import { CalendarModule } from 'primeng/calendar';
import { CheckboxModule } from 'primeng/checkbox';
import { InputTextModule } from 'primeng/inputtext';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { LINE_PFMEA } from '../../../../../models/constant/ptm';

@Component({
    selector: 'app-tcc',
    templateUrl: './tcc.component.html',
    standalone: true,
    styleUrls: ['./tcc.component.scss'],
    imports: [
        CommonModule,
        FormsModule,
        InputTextareaModule,
        InfoShareComponent,
        ButtonModule,
        TableModule,
        DropdownModule,
        FormCustomModule,
        CalendarModule,
        CheckboxModule,
        InputTextModule,
        OverlayPanelModule,
    ],
})
export class TccComponent implements OnInit {
    @Input() content: string = '';
    @Input() title: string = '';
    collapsed = false;
    fb = inject(FormBuilder);

    tccForm: FormGroup;

    tccSectionsData = [
        {
            id: 1,
            instructionId: 203,
            line: 1,
            tccProductionPlan: {
                id: 1,
                tccSectionId: 1,
                planOutput: 500,
                totalLabor: 20,
                norm: 25,
                taktTime: 90,
            },
            tccProductionTime: {
                id: 1,
                tccSectionId: 1,
                shiftsPerDay: 2,
                timePerShift: 8,
                timePerShiftUnit: 'hours',
                restTime: 0.5,
                lunchTime: 1,
                snackTime: 0.25,
                productionTime: 6.25,
                productionTimeUnit: 'hours',
            },
            tccDirectLaborOnline: {
                id: 1,
                tccSectionId: 1,
                lineBalanceIndex: 92.5,
                smoothingIndex: 85,
                cycleTime: 87,
                laborCount: 10,
                chartUrl: 'https://example.com/chart/online/1',
                laborType: 'online',
                stations: [
                    {
                        id: 1,
                        tccDirectLaborId: 1,
                        stationName: 'Trạm 1',
                        stationCode: 'ST01',
                        refStationCode: 'REF01',
                        od: 'OD001',
                        spec: 'QC-1',
                        laborTime: 30,
                        machineTime: 20,
                        quantityStation: 2,
                        quantityDirectLabor: 3,
                        cycleTime: 90,
                        output: 100,
                    },
                ],
            },
            tccDirectLaborOffline: {
                id: 2,
                tccSectionId: 1,
                lineBalanceIndex: 88,
                smoothingIndex: 80,
                cycleTime: 90,
                laborCount: 5,
                chartUrl: 'https://example.com/chart/offline/1',
                laborType: 'offline',
                stations: [
                    {
                        id: 1,
                        tccDirectLaborId: 1,
                        stationName: 'Trạm 1',
                        stationCode: 'ST01',
                        refStationCode: 'REF01',
                        od: 'OD001',
                        spec: 'QC-1',
                        laborTime: 30,
                        machineTime: 20,
                        quantityStation: 2,
                        quantityDirectLabor: 3,
                        cycleTime: 90,
                        output: 100,
                    },
                ],
            },
        },
        {
            id: 2,
            instructionId: 203,
            line: 2,
            tccProductionPlan: {
                id: 2,
                tccSectionId: 2,
                planOutput: 600,
                totalLabor: 25,
                norm: 24,
                taktTime: 85,
            },
            tccProductionTime: {
                id: 2,
                tccSectionId: 2,
                shiftsPerDay: 3,
                timePerShift: 7,
                timePerShiftUnit: 'hours',
                restTime: 0.5,
                lunchTime: 1,
                snackTime: 0.25,
                productionTime: 5.25,
                productionTimeUnit: 'hours',
            },
            tccDirectLaborOnline: {
                id: 3,
                tccSectionId: 2,
                lineBalanceIndex: 90,
                smoothingIndex: 86,
                cycleTime: 83,
                laborCount: 12,
                chartUrl: 'https://example.com/chart/online/2',
                laborType: 'online',
                stations: [
                    {
                        id: 1,
                        tccDirectLaborId: 1,
                        stationName: 'Trạm 1',
                        stationCode: 'ST01',
                        refStationCode: 'REF01',
                        od: 'OD001',
                        spec: 'QC-1',
                        laborTime: 30,
                        machineTime: 20,
                        quantityStation: 2,
                        quantityDirectLabor: 3,
                        cycleTime: 90,
                        output: 100,
                    },
                ],
            },
            tccDirectLaborOffline: {
                id: 4,
                tccSectionId: 2,
                lineBalanceIndex: 85,
                smoothingIndex: 79,
                cycleTime: 89,
                laborCount: 6,
                chartUrl: 'https://example.com/chart/offline/2',
                laborType: 'offline',
                stations: [
                    {
                        id: 1,
                        tccDirectLaborId: 1,
                        stationName: 'Trạm 1',
                        stationCode: 'ST01',
                        refStationCode: 'REF01',
                        od: 'OD001',
                        spec: 'QC-1',
                        laborTime: 30,
                        machineTime: 20,
                        quantityStation: 2,
                        quantityDirectLabor: 3,
                        cycleTime: 90,
                        output: 100,
                    },
                ],
            },
        },
    ];

    form = {
        soCa: null,
        thoiGianLamViec: null,
        donViLamViec: 'giờ',
        giaiLao: null,
        anTrua: null,
        anPhu: null,
        thoiGianSanXuat: null,
        donViSanXuat: 'giây',
    };

    timeUnits = [
        { label: 'giây', value: 'giây' },
        { label: 'phút', value: 'phút' },
        { label: 'giờ', value: 'giờ' },
    ];

    constructor() {
        this.tccForm = this.buildTccForm();
    }

    get tccSections(): FormArray {
        return this.tccForm.get('tccSections') as FormArray;
    }

    ngOnInit(): void {
        this.setTccSectionsData(this.tccSectionsData);
    }

    buildTccForm(): FormGroup {
        return this.fb.group({
            instructionInfoId: [null],
            instructionInfo: this.buildInstructionInfoForm(),
            limitStation: [null],
            tccSections: this.fb.array([this.buildSectionForm()]),
        });
    }

    buildInstructionInfoForm(): FormGroup {
        return this.fb.group({
            id: [null],
            instructionId: [null],
            status: [null],
            type: [null],
            reviewers: [null],
            creator: [null],
            createdAt: [null],
            updatedAt: [null],
        });
    }

    buildSectionForm(): FormGroup {
        return this.fb.group({
            id: [null],
            instructionId: [null],
            line: [null],

            tccProductionTime: this.buildProductionTimeForm(),
            tccProductionPlan: this.buildProductionPlanForm(),
            tccDirectLaborOnline: this.buildDirectLaborForm(1), // 1: Online
            tccDirectLaborOffline: this.buildDirectLaborForm(2), // 2: Offline
        });
    }

    buildProductionTimeForm(): FormGroup {
        return this.fb.group({
            id: [null],
            tccSectionId: [null],
            shiftsPerDay: [null],
            timePerShift: [null],
            timePerShiftUnit: ['giờ'],
            restTime: [null],
            lunchTime: [null],
            snackTime: [null],
            productionTime: [null],
            productionTimeUnit: ['giờ'],
        });
    }

    buildProductionPlanForm(): FormGroup {
        return this.fb.group({
            id: [null],
            tccSectionId: [null],
            planOutput: [null],
            totalLabor: [null],
            norm: [null],
            taktTime: [null],
        });
    }

    buildDirectLaborForm(type: number): FormGroup {
        return this.fb.group({
            id: [null],
            tccSectionId: [null],
            lineBalanceIndex: [null],
            smoothingIndex: [null],
            cycleTime: [null],
            laborCount: [null],
            chartUrl: [null],
            laborType: [type], // 1: Online, 2: Offline
            stations: this.fb.array([this.buildStationForm]),
        });
    }

    buildStationForm(): FormGroup {
        return this.fb.group({
            id: [null],
            tccDirectLaborId: [null],
            stationName: [null],
            stationCode: [null],
            refStationCode: [null],
            od: [null],
            spec: [null],
            laborTime: [null],
            machineTime: [null],
            quantityStation: [null],
            quantityDirectLabor: [null],
            cycleTime: [null],
            output: [null],
        });
    }

    setTccSectionsData(data: any[]) {
        const sectionArray = this.tccForm.get('tccSections') as FormArray;
        sectionArray.clear(); // Xoá form cũ nếu có

        data.forEach((section) => {
            sectionArray.push(this.setSectionForm(section));
        });
        console.log(sectionArray.getRawValue());
    }

    setSectionForm(data: TccSection): FormGroup {
        return this.fb.group({
            id: [data.id ?? null],
            instructionId: [data.instructionId ?? null],
            line: [data.line ?? null],

            tccProductionPlan: this.setProductionPlanForm(data.tccProductionPlan),
            tccProductionTime: this.setProductionTimeForm(data.tccProductionTime),
            tccDirectLaborOnline: this.setDirectLaborForm(data.tccDirectLaborOnline, 'online'),
            tccDirectLaborOffline: this.setDirectLaborForm(data.tccDirectLaborOffline, 'offline'),
        });
    }

    setProductionPlanForm(data: TccProductionPlan): FormGroup {
        return this.fb.group({
            id: [data.id ?? null],
            tccSectionId: [data.tccSectionId ?? null],
            planOutput: [data.planOutput ?? null],
            totalLabor: [data.totalLabor ?? null],
            norm: [data.norm ?? null],
            taktTime: [data.taktTime ?? null],
        });
    }

    setProductionTimeForm(data: TccProductionTime): FormGroup {
        return this.fb.group({
            id: [data.id ?? null],
            tccSectionId: [data.tccSectionId ?? null],
            shiftsPerDay: [data.shiftsPerDay ?? null],
            timePerShift: [data.timePerShift ?? null],
            timePerShiftUnit: [data.timePerShiftUnit ?? 'hours'],
            restTime: [data.restTime ?? null],
            lunchTime: [data.lunchTime ?? null],
            snackTime: [data.snackTime ?? null],
            productionTime: [data.productionTime ?? null],
            productionTimeUnit: [data.productionTimeUnit ?? 'hours'],
        });
    }

    setDirectLaborForm(data: TccDirectLabor, type: 'online' | 'offline'): FormGroup {
        const stations = (data.stations ?? []).map((station: any) => this.setStationForm(station));
        return this.fb.group({
            id: [data.id ?? null],
            tccSectionId: [data.tccSectionId ?? null],
            lineBalanceIndex: [data.lineBalanceIndex ?? null],
            smoothingIndex: [data.smoothingIndex ?? null],
            cycleTime: [data.cycleTime ?? null],
            laborCount: [data.laborCount ?? null],
            chartUrl: [data.chartUrl ?? null],
            laborType: [type],
            stations: this.fb.array(stations),
        });
    }

    setStationForm(data: TccStation): FormGroup {
        return this.fb.group({
            id: [data.id ?? null],
            tccDirectLaborId: [data.tccDirectLaborId ?? null],
            stationName: [data.stationName ?? null],
            stationCode: [data.stationCode ?? null],
            refStationCode: [data.refStationCode ?? null],
            od: [data.od ?? null],
            spec: [data.spec ?? null],
            laborTime: [data.laborTime ?? null],
            machineTime: [data.machineTime ?? null],
            quantityStation: [data.quantityStation ?? null],
            quantityDirectLabor: [data.quantityDirectLabor ?? null],
            cycleTime: [data.cycleTime ?? null],
            output: [data.output ?? null],
        });
    }

    toggleCollapse() {
        this.collapsed = !this.collapsed;
    }

    protected readonly linePfmea = LINE_PFMEA;
}
