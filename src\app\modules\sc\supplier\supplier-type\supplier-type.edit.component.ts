import { Ng<PERSON><PERSON>, NgIf } from '@angular/common';
import { AfterViewInit, Component } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { NgSelectModule } from '@ng-select/ng-select';
import { ConfirmationService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { TableModule } from 'primeng/table';
import { AlertService } from 'src/app/shared/services/alert.service';
import { CriteriaSupplierService } from '../../../../services/sc/supplier/criteria-supplier.service';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { FileService } from 'src/app/shared/services/file.service';
import { SupplierTypeService } from 'src/app/services/sc/supplier/suppiler-type.service';
import { CriteriaBuy, CriteriaBuySupplier, SupplierCriteria, SupplierType } from '../../../../models/interface/sc';
import { CriteriaDataType, CriteriaType, Operators, SupplierPriority } from 'src/app/models/constant/sc';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { trimValidator } from 'src/app/shared/directives/time.span.validator';
import { PanelModule } from 'primeng/panel';
import { PopupComponent } from '../../../../shared/components/popup/popup.component';
import { HasAnyAuthorityDirective } from '../../../../shared/directives/has-any-authority.directive';

@Component({
    selector: 'app-provider.edit',
    templateUrl: './supplier-type.edit.component.html',
    standalone: true,
    imports: [
        NgIf,
        NgClass,
        RouterLink,
        ReactiveFormsModule,
        ButtonModule,
        NgSelectModule,
        InputTextModule,
        InputTextareaModule,
        TableModule,
        DropdownModule,
        DialogModule,
        CardModule,
        FormsModule,
        SubHeaderComponent,
        PanelModule,
        PopupComponent,
        HasAnyAuthorityDirective,
    ],
    providers: [SupplierTypeService, CriteriaSupplierService],
})
export class SupplierTypeEditComponent implements AfterViewInit {
    operators = Operators;

    supplierType: SupplierType = { criteria: [], createCriteria: [], removeCriteria: [] };

    criteriaTypeDialog: number;
    criteriaType = CriteriaType;

    criteriaForm: FormGroup;

    visibleDialog = false;

    isEditingNewCriteria = false;

    visibleFileDialog = false;

    priority;

    valueType = null;
    criteriaTypeExport: number;

    constructor(
        private route: ActivatedRoute,
        private router: Router,
        private loadingService: LoadingService,
        private formBuilder: FormBuilder,
        private alertService: AlertService,
        private confirmationService: ConfirmationService,
        private supplierTypeService: SupplierTypeService,
        private criteriaSupplierService: CriteriaSupplierService,
        private fileService: FileService,
    ) {
        this.criteriaForm = this.formBuilder.group({
            id: [null],
            name: [null, Validators.required],
            dataType: [null, Validators.required],
            unit: [null, [Validators.maxLength(50)]],
            note: [null],
            operator: [null, Validators.required],
            value: [null, [Validators.required, trimValidator()]],
        });

        this.priority = SupplierPriority;
        this.criteriaForm.get('operator').valueChanges.subscribe((value) => {
            if (value !== null) {
                this.valueType = value;
            } else {
                this.valueType = null;
                this.criteriaForm.patchValue({ value: null });
            }
        });

        this.criteriaForm.get('dataType')?.valueChanges.subscribe((newValue) => {
            this.criteriaForm.patchValue({ value: null, operator: null });
        });
    }

    ngAfterViewInit(): void {
        this.loadData();
    }

    loadData = () => {
        if (this.route.snapshot.paramMap.get('id') != null) {
            const id = Number(this.route.snapshot.paramMap.get('id'));
            this.loadingService.show();
            this.supplierTypeService.getOne(id).subscribe({
                next: (res) => {
                    this.loadingService.hide();
                    this.supplierType = { ...this.supplierType, createCriteria: [], removeCriteria: [], ...res.body };
                    /*this.criteriaNew = this.supplierType.criteria.filter(item => item.type === 0);
                    this.criteriaBuy = this.supplierType.criteria.filter(item => item.type === 1);*/
                },
                error: () => {
                    this.loadingService.hide();
                    this.alertService.error('Lỗi', 'Không thể truy cập thông tin loại nhà cung cấp');
                    this.router.navigate(['./..']);
                },
            });
        }
    };

    deleteObject = (index: number, type) => {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận xóa',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            accept: () => {
                const filteredCriteria = this.supplierType.criteria.filter((e) => e.type === type);

                // Kiểm tra xem index có hợp lệ trong danh sách đã lọc không
                if (index >= 0 && index < filteredCriteria.length) {
                    const itemToRemove = filteredCriteria[index];

                    // Thêm vào danh sách removeCriteria
                    this.supplierType.removeCriteria.push(itemToRemove);

                    // Cập nhật danh sách criteria, loại bỏ phần tử đã chọn
                    this.supplierType.criteria = this.supplierType.criteria.filter((e) => e !== itemToRemove);
                }
            },
        });
    };

    cancelUpdate = () => {
        if (this.isEditing) {
            this.confirmationService.confirm({
                key: 'app-confirm',
                header: 'Xác nhận hủy thay đổi',
                message: 'Thay đổi sẽ không được lưu',
                icon: 'pi pi-exclamation-triangle',
                accept: () => {
                    this.router.navigate(['../'], { relativeTo: this.route });
                },
            });
        } else {
            this.router.navigate(['../'], { relativeTo: this.route });
        }
    };

    indexCriteria = -1;

    openDialog = (object, index: number, isNewObject: boolean, type: number) => {
        this.criteriaForm.reset();
        this.isEditingNewCriteria = isNewObject;

        if (!isNewObject) {
            this.indexCriteria = index;
            this.criteriaForm.patchValue({ ...object });
        }
        this.visibleDialog = true;
        this.criteriaTypeDialog = type;
    };
    isEditing = false;
    saveObject = () => {
        this.loadingService.show();
        this.supplierType.criteria = this.supplierType.criteria.filter((e) => e.id !== undefined || e.id !== null);
        this.supplierTypeService.update(this.supplierType).subscribe((res) => {
            this.alertService.success('Thành công', 'Cấp nhật loại nhà cung cấp');
            this.loadingService.hide();
            this.isEditing = false;
            this.loadData();
        });
    };

    saveDialogObject = () => {
        if (!this.criteriaForm.valid) return;

        const object = this.criteriaForm.getRawValue();
        object.supplierTypeId = this.supplierType.id;
        object.type = this.criteriaTypeDialog;

        if (this.isExist(object, this.indexCriteria)) {
            this.alertService.error('Thất bại', 'Tiêu chí đánh giá đã tồn tại!');
            this.loadingService.hide();
            return;
        }

        if (this.isEditingNewCriteria) {
            this.supplierType.criteria.push(object);
            this.supplierType.createCriteria.push(object);
        } else {
            this.supplierType.criteria = this.supplierType.criteria.map((e, i) => (i === this.indexCriteria ? { ...e, ...object } : e));
        }

        this.visibleDialog = false;
        this.isEditing = true;
    };

    isExist = (object: SupplierCriteria, index: number) => {
        return this.supplierType.criteria.some((e, i) => e.name === object.name && index != i && e.type === object.type);
    };

    exportData = (event, criteriaType: number) => {
        this.supplierTypeService.exportCriteria(this.supplierType.id, criteriaType).subscribe((res) => {
            this.fileService.downLoadFileByService(res.url, '/sc/api');
        });
    };

    getOperator(value) {
        return this.operators.find((e) => e.value === value).name;
    }

    filterCriteriaByType(type: number): any[] {
        return this.supplierType.criteria.filter((item) => item.type === type);
    }

    getOriginalIndex(criteria: any): number {
        return this.supplierType.criteria.findIndex((item) => item === criteria);
    }

    protected readonly CriteriaType = CriteriaType;
}
