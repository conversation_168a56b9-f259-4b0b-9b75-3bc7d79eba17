:host ::ng-deep .p-breadcrumb {
    border: none !important;
    border-radius: 0 !important;
    padding: 13px;
}

:host ::ng-deep .p-menuitem-link {
    font-size: 14px;
    line-height: 20px;
}

:host ::ng-deep .p-button {
    padding: 0.4rem 0.8rem;
    font-size: 14px;
}
:host ::ng-deep .p-menuitem-link {
    overflow: hidden;
    text-overflow: ellipsis;
    text-wrap: nowrap;
}

:host ::ng-deep .p-overlaypanel .p-overlaypanel-content {
    padding: 0.4rem !important;
}

@media (max-width: 768px) {
    .sub-header-lg {
        display: none !important;
    }

    .p-breadcrumb {
        border: none !important;
        border-radius: 0 !important;
        padding: 13px;
    }
}


@media (max-width: 640px) {
    .p-breadcrumb-sm {
        max-width: calc(100% - 100px);
    }
}
