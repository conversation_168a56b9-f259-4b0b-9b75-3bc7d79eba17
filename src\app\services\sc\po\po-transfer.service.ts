import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { CanExportDTO, PoTransfer } from '../../../models/interface/sc';
import { ApiResponse, GeneralEntity } from 'src/app/models/interface';

@Injectable()
export class PoTransferService extends BaseService<PoTransfer> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/po-transfer');
    }

    cancel(id: number) {
        return this.http.get<PoTransfer>(`/sc/api/po-transfer/cancel/${id}`);
    }

    export(id: number) {
        return this.http.post<GeneralEntity>(`/sc/api/po-transfer/export-po-transfer?poTransferId=${id}`, null);
    }

    getFileTemplateImport(body: { poId: number; accountingCodes?: string[] }) {
        return this.http.post<GeneralEntity>(`/sc/api/po-transfer/template-import`, body);
    }

    getFileTemplateExport(body: { poId: number; accountingCodes?: string[] }) {
        return this.http.post<GeneralEntity>(`/sc/api/po-transfer/template-export`, body);
    }

    importFile(file: File, poId: number, accountingCodes: string[], transferId: number) {
        const formData: FormData = new FormData();
        formData.append('file', file);
        formData.append('poId', poId.toString());
        formData.append('accountingCodes', accountingCodes.toString());
        if (transferId != null) {
            formData.append('transferId', transferId.toString());
        }

        return this.http.post<ApiResponse>('/sc/api/po-transfer/import-transfer-import', formData);
    }

    importFileExport(file: File, poId: number, transferId: number) {
        const formData: FormData = new FormData();
        formData.append('file', file);
        formData.append('poId', poId.toString());
        if (transferId != null) {
            formData.append('transferId', transferId.toString());
        }

        return this.http.post<ApiResponse>('/sc/api/po-transfer/import-transfer-export', formData);
    }

    reSyncInv(transferId: number) {
        return this.http.post<void>(`/sc/api/po-transfer/re-sync-inv?transferId=${transferId}`, null);
    }

    updateDate(body: { transferId: number; date: number }) {
        return this.http.post<void>(`/sc/api/po-transfer/update-date`, body);
    }
}
