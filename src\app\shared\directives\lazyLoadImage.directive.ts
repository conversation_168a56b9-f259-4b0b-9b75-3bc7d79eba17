import { Directive, ElementRef, Input, OnInit, Renderer2 } from '@angular/core';

@Directive({
    selector: '[appLazyLoadImage]',
    standalone: true,
})
export class LazyLoadImageDirective implements OnInit {
    @Input() appLazyLoadImage: string = ''; // Nhận đường dẫn ảnh từ input

    constructor(
        private el: ElementRef,
        private renderer: Renderer2,
    ) {}

    ngOnInit() {
        this.lazyLoadImage();
    }

    lazyLoadImage() {
        const imageElement = this.el.nativeElement;

        const intersectionObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    this.renderer.setAttribute(imageElement, 'src', this.appLazyLoadImage);
                    observer.unobserve(imageElement); // Ngừng quan sát sau khi ảnh đã được tải
                }
            });
        });

        intersectionObserver.observe(imageElement); // B<PERSON><PERSON> đầu theo dõi phần tử
    }
}
