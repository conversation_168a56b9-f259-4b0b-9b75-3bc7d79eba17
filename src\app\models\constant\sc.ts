// Function to convert array to map
import Common from './../../utils/common';

export const STATE_SUPPLIER = [
    {
        label: 'Đang đánh giá',
        value: 0,
    },
    {
        label: '<PERSON><PERSON><PERSON> chế hợp tác',
        value: 1,
    },
    {
        label: '<PERSON><PERSON> duyệt tạm thời',
        value: 2,
    },
    {
        label: 'Dừng hợp tác',
        value: 3,
    },
    {
        label: 'Đ<PERSON> phê duy<PERSON> (Chưa giao dịch)',
        value: 4,
    },
    {
        label: 'Đã phê duyệt (Đang giao dịch)',
        value: 5,
    },
    {
        label: 'Đ<PERSON> phê duyệt (Đã giao dịch)',
        value: 6,
    },
];

export const STATE_PROFILE_SUPPLIER = [
    {
        label: 'Fail',
        value: 0,
    },
    {
        label: 'Pass',
        value: 1,
    },
    {
        label: 'Chưa đánh giá',
        value: 2,
    },
];

export const TIME_TYPE = {
    YEAR: 0,
    MONTH: 1,
};

// Converting arrays to maps
export const MAP_STATE_SUPPLIER = Common.arrayToMap(STATE_SUPPLIER);
export const MAP_STATE_PROFILE_SUPPLIER = Common.arrayToMap(STATE_PROFILE_SUPPLIER);

export const SupplierPriority = [
    {
        value: 'Minor',
        id: 0,
    },
    {
        value: 'Major',
        id: 1,
    },
];

export const PO_STATE = [
    { value: 0, label: 'Mới' },
    { value: 1, label: 'Đang xử lí' },
    { value: 2, label: 'Đang giao hàng' },
    { value: 3, label: 'Hoàn thành' },
];

export const PO_STATE_CONSTANT = {
    DRAFT: -1,
    NEW: 0, // hình ảnh
    PROCESSING: 1,
    DELIVERING: 2,
    COMPLETED: 3,
};

export const MAP_PO_STATE = {
    0: 'Mới',
    1: 'Đang xử lí',
    2: 'Đang giao hàng',
    3: 'Hoàn thành',
};

export const TRANSFER_STATE = [
    { label: 'Mới', value: 0 },
    { label: 'Đang xử lý', value: 1 },
    { label: 'Hoàn thành', value: 2 },
    { label: 'Đã hủy', value: 3 },
];

export const TRANSFER_STATE_OBJECT = {
    NEW: 0,
    PROCESSING: 1,
    COMPLETE: 2,
    CANCEL: 3,
};

export const MAP_TRANSFER_STATE = Common.arrayToMap(TRANSFER_STATE);

export const UnitPrice = {
    0: 'USD',
    1: 'VNĐ',
};

export const UnitPriceArr = [
    {
        value: 0,
        label: 'USD',
    },
    {
        value: 1,
        label: 'VNĐ',
    },
];
export const MAP_TYPE_MONEY = Common.arrayToMap(UnitPriceArr);

export const UnitPriceMappingValue = {
    USD: 0,
    VNĐ: 1,
};

export const PoStates = [
    {
        id: 0,
        name: 'Mới',
    },
    {
        id: 1,
        name: 'Đang xử lý',
    },
    {
        id: 2,
        name: 'Đang giao hàng',
    },
    {
        id: 3,
        name: 'Hoàn thành',
    },
];

export const CriteriaType = {
    NEW: 0,
    BUY: 1,
};

export const CriteriaDataType = {
    NUMBER: 0,
    TEXT: 1,
};

export const Operators = [
    { value: 0, name: '=' },
    { value: 1, name: '<' },
    { value: 2, name: '>' },
    { value: 3, name: '>=' },
    { value: 4, name: '<=' },
    { value: 5, name: 'Bao gồm' },
];

export const LOGISTICS_TYPE = [
    { value: 0, label: 'Fowarder' },
    { value: 1, label: 'Bảo hiểm' },
    { value: 2, label: 'Chuyển phát nhanh' },
    { value: 3, label: 'Khác' },
];

export const MAP_LOGISTICS_TYPE = Common.arrayToMap(LOGISTICS_TYPE);

export const LOGISTICS_STATUS = [
    { value: 0, label: 'Đang hợp tác' },
    { value: 1, label: 'Dừng hợp tác' },
];

export const MAP_LOGISTICS_STATUS = Common.arrayToMap(LOGISTICS_STATUS);

export const LOGISTICS_FEE_UNIT = [
    { value: 'PERCENT', label: '%' },
    { value: 'USD', label: 'USD' },
    { value: 'VNĐ', label: 'VNĐ' },
];
export const MAP_LOGISTICS_FEE_UNIT = Common.arrayToMap(LOGISTICS_FEE_UNIT);

export const DEFAULT_LOGISTIC_DOCUMENT = [
    {
        name: 'Tờ trình',
        logisticId: null,
        active: false, // mặc định
        attachmentIds: [],
        attachments: [],
    },
    {
        name: 'Biên bản thương thảo',
        logisticId: null,
        active: false,
        attachmentIds: [],
        attachments: [],
    },
    {
        name: 'Giới thiệu công ty',
        logisticId: null,
        active: false,
        attachmentIds: [],
        attachments: [],
    },
    {
        name: 'Báo cáo tài chính',
        logisticId: null,
        active: false,
        attachmentIds: [],
        attachments: [],
    },
    {
        name: 'Đăng ký kinh doanh',
        logisticId: null,
        active: false,
        attachmentIds: [],
        attachments: [],
    },
];

export const STATE_LOGISTICS = [
    {
        label: 'Đang hợp tác',
        value: 0,
    },
    {
        label: 'Dừng hợp tác',
        value: 1,
    },
];

export const MAP_STATE_LOGISTICS = Common.arrayToMap(STATE_LOGISTICS);

export const LOGISTICS_TYPE_MAP = {
    FORWARDER: 0,
    EXPRESS_DELIVERY: 2,
    INSURANCE: 1,
    OTHER: 3,
};

export const TYPE_SHIPPING_IMPORT = 0;
export const TYPE_SHIPPING_EXPORT = 1;

export const TYPE_SHIPPING = [
    {
        value: TYPE_SHIPPING_IMPORT,
        label: 'Hàng nhập',
    },
    {
        value: TYPE_SHIPPING_EXPORT,
        label: 'Hàng xuất',
    },
];

export const MAP_TYPE_SHIPPING = Common.arrayToMap(TYPE_SHIPPING);

export const TYPE_SHIPPING_METHOD_AIR = 0;
export const TYPE_SHIPPING_METHOD_SEA_LCL = 1;
export const TYPE_SHIPPING_METHOD_SEA_FCL = 2;
export const TYPE_SHIPPING_METHOD_TRUCK = 3;

export const TYPE_BO_SHIPPING_FORWARDER = 0;
export const TYPE_BO_SHIPPING_INSURANCE = 1;

export const TYPE_BO_SHIPPING = [
    {
        value: TYPE_BO_SHIPPING_FORWARDER,
        label: 'Forwarder',
    },
    {
        value: TYPE_BO_SHIPPING_INSURANCE,
        label: 'Bảo hiểm',
    },
];

export const MAP_TYPE_BO_SHIPPING = Common.arrayToMap(TYPE_BO_SHIPPING);

export const LOT_HISTORY_TWO = 0;
export const LOT_HISTORY_THREE = 1;

export type ShippingMethodName = typeof SHIPPING_METHOD_TRUCK | typeof SHIPPING_METHOD_AIR | typeof SHIPPING_METHOD_SEA_LCL | typeof SHIPPING_METHOD_SEA_FCL;

export const SHIPPING_METHOD_TRUCK = 'TRUCK' as const;
export const SHIPPING_METHOD_AIR = 'AIR' as const;
export const SHIPPING_METHOD_SEA_LCL = 'SEA LCL' as const;
export const SHIPPING_METHOD_SEA_FCL = 'SEA FCL' as const;

export const RFQ_STATE = {
    STATE_ONE: 0,
    STATE_TWO: 1,
};

export const RFQ_ITEM_TYPE = {
    SC_BOM: 0,
    RETAIL: 1,
};

export const BomStatus = [
    {
        label: 'Mới',
        value: 0,
    },
    {
        label: 'Đã tạo đơn nháp',
        value: 1,
    },
];

export const PoDraftStatus = [
    {
        label: 'Mới',
        value: 0,
    },
    {
        label: 'Đang xử lý',
        value: 1,
    },
    {
        label: 'Hoàn thành',
        value: 2,
    },
];

export const BoStatus = [
    {
        label: 'Chờ gửi',
        value: -1,
    },
    {
        label: 'Mới',
        value: 0,
    },
    {
        label: 'Thương lượng',
        value: 1,
    },
    {
        label: 'Đã phê duyệt',
        value: 2,
    },
    {
        label: 'Đang vận chuyển',
        value: 3,
    },
    {
        label: 'Hoàn thành',
        value: 4,
    },
];

export const BO_STATUS_CONSTANT = {
    WAITING: -1,
    NEW: 0,
    NEGOTIATE: 1,
    APPROVED: 2,
    DELIVERING: 3,
    COMPLETE: 4,
};

export const PO_DRAFT_STATUS_CONSTANT = {
    NEW: 0,
    CREATED_DRAFT: 1,
};
