import { ChangeDetector<PERSON>ef, Component, TemplateRef, ViewChild, OnInit, AfterViewInit } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TableModule } from 'primeng/table';
import { PaginatorModule } from 'primeng/paginator';
import { ButtonModule } from 'primeng/button';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { TooltipModule } from 'primeng/tooltip';
import { TABLE_KEY } from 'src/app/models/constant';
import { Column } from '../../../../models/interface';
import { AcceptanceDocumentService } from '../../../../services/smart-qc/acceptance-document/acceptance-document.service';
import { AuthService } from '../../../../core/auth/auth.service';
import { HasAnyAuthorityDirective } from '../../../../shared/directives/has-any-authority.directive';
import { SharedModule } from 'primeng/api';
import { TableCommonService } from '../../../../shared/table-module/table.common.service';
import { AcceptanceDocument } from '../../../../models/interface/smart-qc';
import { TableCommonModule } from '../../../../shared/table-module/table.common.module';
import { SubHeaderComponent } from '../../../../shared/components/sub-header/sub-header.component';

@Component({
    selector: 'app-acceptance',
    templateUrl: './acceptance.document.html',
    styleUrls: ['./acceptance.document.style.scss'],
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        TableModule,
        PaginatorModule,
        ButtonModule,
        SharedModule,
        RouterLink,
        HasAnyAuthorityDirective,
        TooltipModule,
        SharedModule,
        SharedModule,
        TableCommonModule,
        SubHeaderComponent,
    ],
})
export class AcceptanceDocumentComponent implements OnInit, AfterViewInit {
    @ViewChild('actionAcceptanceDocument') actionAcceptanceDocument: TemplateRef<unknown>;

    state: QueryObserverBaseResult<AcceptanceDocument[]>;
    columns: Column[] = [];
    tableId: string = TABLE_KEY.ACCEPTANCE_DOCUMENT;
    itemsHeader = [{ label: 'Quản lý hồ sơ nghiệm thu' }, { label: 'Danh sách hồ sơ nghiệm thu' }];

    constructor(
        private acceptanceDocumentService: AcceptanceDocumentService,
        private router: Router,
        private tableCommonService: TableCommonService,
        protected authService: AuthService,
        private cdr: ChangeDetectorRef,
    ) {}

    ngOnInit() {
        this.tableCommonService
            .init<AcceptanceDocument>({
                tableId: this.tableId,
                queryFn: (filter) => this.acceptanceDocumentService.getPageTableCustom(filter),
                configFilterRSQL: {
                    contractId: 'SetLong',
                    actionId: 'SetLong',
                },
                /*configFilter: ['contractId', 'contractName' ,'actionId', 'created' ],*/
            })
            .subscribe((state) => {
                this.state = state;
            });
    }

    ngAfterViewInit(): void {
        this.columns = [
            { field: 'contractName', header: 'Dự án', default: true },
            { field: 'actionName', header: 'Công việc' },
            { field: 'createdBy', header: 'Người tạo' },
            {
                field: 'created',
                header: 'Thời gian tạo',
                type: 'date',
                format: 'dd/MM/yyyy',
            },
            { field: 'action', header: 'Thao tác', body: this.actionAcceptanceDocument },
        ];
        this.cdr.detectChanges();
    }

    goToCreatePage() {
        this.router.navigate(['/sqc/report/acceptance-document/create']);
    }

    gotoDetailPage(id: number) {
        this.router.navigate(['/sqc/report/acceptance-document/' + id + '/edit']);
    }

    deleteSelectedTemplate = (ids: number[]) => {
        return this.acceptanceDocumentService.batchDelete(ids);
    };
}
