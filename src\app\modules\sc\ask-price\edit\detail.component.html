<app-sub-header
    [items]="[{ label: 'Quản lý thông tin mua hàng', url: '/sc/rfq' },
    { label: 'Hỏi giá', url: '/sc/rfq' },
    { label: rfqOld?.code }]"
    [action]="action"
></app-sub-header>

<ng-template #action>
    <!--<p-button label="Lưu" (click)="null" severity="success" size="small" />-->
    <p-button label="Đóng" routerLink="/sc/rfq" severity="secondary" size="small" />
</ng-template>
<ng-container>
    <div class="tw-p-4" *ngIf="isLoading">
        <app-wizard-double-row
            [isActivePreviousState]="true"
            [valueField]="'id'"
            [versionStates]="versionStates"
            [stateIdSelected]="rfqOld.state"
            (onSelectState)="handleSelectState($event)"
        >
        </app-wizard-double-row>
        <div class="tw-mb-5"></div>
        <app-ask-price-state-one *ngIf="stateIdView === 0" [rfqOld]="rfqOld"></app-ask-price-state-one>
        <app-ask-price-state-two *ngIf="stateIdView === 1" [rfqOld]="rfqOld"></app-ask-price-state-two>
    </div>
</ng-container>
