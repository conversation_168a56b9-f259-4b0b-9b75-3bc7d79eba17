import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { GetOtherDocumentsResponse, OtherDocDto, UpdateOtherDocsPayload } from 'src/app/models/interface/ptm/other-docs';

@Injectable({ providedIn: 'root' })
export class OtherDocsService {
    path = '/pr/api/production-instruction';
    #http = inject(HttpClient);

    saveOtherDocs(productInstructionId: string | number, payload: UpdateOtherDocsPayload): Observable<OtherDocDto[]> {
        const url = `${this.path}/${productInstructionId}/other-document`;
        return this.#http.post<OtherDocDto[]>(url, payload);
    }
    getOtherDocuments(productInstructionId: number): Observable<GetOtherDocumentsResponse> {
        const url = `${this.path}/${productInstructionId}/other-document`;
        return this.#http.get<GetOtherDocumentsResponse>(url);
    }
}
