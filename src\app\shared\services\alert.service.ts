import { Injectable, inject } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { MessageService } from 'primeng/api';
import { HttpErrorResponse } from '@angular/common/http';

interface ErrorApp {
    errorKey: string;
    entityName: string;
}

@Injectable({
    providedIn: 'root',
})
export class AlertService {
    private messageService = inject(MessageService);
    private translateService = inject(TranslateService);

    private addMessage(severity: string, summary: string, detail?: string) {
        const translatedSummary = this.translateService.instant(summary);
        const translatedDetail = detail ? this.translateService.instant(detail) : undefined;

        // Kiểm tra và sử dụng giá trị mặc định nếu không có bản dịch
        const finalSummary = translatedSummary === summary ? summary : translatedSummary;
        const finalDetail = translatedDetail === detail ? detail : translatedDetail;

        this.messageService.add({
            key: 'app-alert',
            severity,
            summary: finalSummary,
            detail: finalDetail,
        });
    }

    success(summary: string = 'Thành công', detail?: string) {
        this.addMessage('success', summary, detail);
    }

    info(summary: string = 'Thông tin', detail?: string) {
        this.addMessage('info', summary, detail);
    }

    warning(summary: string = 'Cảnh báo', detail?: string) {
        this.addMessage('warn', summary, detail);
    }

    error(summary: string = 'Lỗi!', detail?: string) {
        this.addMessage('error', summary, detail);
    }

    contrast(summary: string = 'Thông báo', detail?: string) {
        this.addMessage('contrast', summary, detail);
    }

    secondary(summary: string = 'Thông báo', detail?: string) {
        this.addMessage('secondary', summary, detail);
    }

    handleError(res: ErrorApp | HttpErrorResponse) {
        const defaultErrorMessage = 'Có lỗi xảy ra';

        if (res && res['error'] && res['error'] instanceof Blob) return;
        if ('errorKey' in res && 'entityName' in res) {
            if (res.entityName && res.errorKey) {
                this.error(defaultErrorMessage, `error.${res.entityName}.${res.errorKey}`);
            } else {
                this.error(defaultErrorMessage);
            }
        } else if (res instanceof HttpErrorResponse) {
            if (res.error?.errorKey && res.error?.entityName) {
                this.error(defaultErrorMessage, `error.${res.error.entityName}.${res.error.errorKey}`);
            } else {
                this.error(defaultErrorMessage);
            }
        } else {
            this.error(defaultErrorMessage);
        }
    }
}
