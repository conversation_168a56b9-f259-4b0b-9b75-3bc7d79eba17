import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { ApiResponse } from 'src/app/models/interface';
import { SupplierMaterial } from 'src/app/models/interface/sc';

@Injectable({
    providedIn: 'root',
})
export class SupplierMaterialService extends BaseService<SupplierMaterial> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/supplier-material');
    }

    importCreate(file: File) {
        const formData: FormData = new FormData();
        formData.append('file', file);

        return this.http.post<ApiResponse>('/sc/api/supplier-material/import', formData);
    }
}
