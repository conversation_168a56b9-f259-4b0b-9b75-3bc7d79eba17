p {
    margin: 0;
}



:host ::ng-deep .p-multiselect {
    width: 100%;
}

:host ::ng-deep .p-inputtext {
    width: -webkit-fill-available;
}

:host ::ng-deep .p-dropdown {
    width: 100%;
}

textarea {
    resize: none;
}

::ng-deep .tooltip .p-tooltip-text {
        width: fit-content !important;
}

.tooltip {
    max-width: max-content !important;
}

.item-text {
    // max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
