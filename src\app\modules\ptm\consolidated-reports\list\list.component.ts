import { Component } from '@angular/core';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { CalendarModule } from 'primeng/calendar';
import { DropdownModule } from 'primeng/dropdown';
import { ButtonModule } from 'primeng/button';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { ReportFilter } from 'src/app/models/interface/ptm/report-bor';
import { ComboboxNonRSQLComponent } from 'src/app/shared/components/combobox-nonRSQL/combobox-nonRSQL.component';

import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { ReportsBorService } from 'src/app/services/ptm/reports/reports-bor.service';
@Component({
    selector: 'app-list',
    standalone: true,
    templateUrl: './list.component.html',
    styleUrls: ['./list.component.scss'],
    imports: [SubHeaderComponent, CalendarModule, DropdownModule, ButtonModule, ReactiveFormsModule, FormCustomModule, ComboboxNonRSQLComponent],
})
export class ConsolidatedReportsListComponent {
    itemsHeader = [{ label: 'Quản lý CNSX' }, { label: 'Báo cáo tổng hợp' }];
    filterForm: FormGroup;

    productLines = []; // Dữ liệu dropdown dòng sản phẩm
    vnptManPNs = []; // Dữ liệu dropdown VNPT Man P/N

    manData: any[] = [];
    lineData: any[] = [];
    reportDates: Date[] = [];

    activeTab: 'man' | 'line' = 'man';

    constructor(
        private fb: FormBuilder,
        private pps: ReportsBorService,
    ) {}

    ngOnInit() {
        this.filterForm = this.fb.group({
            dateRange: [[]],
            productLineIds: [[]],
            vnptManPn: [[]],
        });
    }

    generateReport() {
        const raw = this.filterForm.value;

        const payload: ReportFilter = {
            from: raw.dateRange?.[0] ? new Date(raw.dateRange[0]).getTime() : null,
            to: raw.dateRange?.[1] ? new Date(raw.dateRange[1]).getTime() : null,
            productLineIds: raw.productLineIds ? [raw.productLineIds] : [],
            vnptManPNs: raw.vnptManPn ? [raw.vnptManPn] : [],
            model: '',
            productName: '',
            vnptManPN: '',
            section: '',
            equipName: '',
            // productName: 'DESC',
        };

        this.pps.compareProductionInstruction(payload).subscribe({
            next: (res) => {
                // Giả sử API trả về dữ liệu dạng { manData: [], lineData: [], reportDates: [] }
                this.manData = res.manData || [];
                this.lineData = res.lineData || [];
                this.reportDates = res.reportDates || [];
            },
            error: (err) => {
                console.error('Lỗi khi gọi API:', err);
            },
        });
    }
}
