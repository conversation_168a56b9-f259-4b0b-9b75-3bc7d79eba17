import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Role } from 'src/app/models/interface';
import { BaseService } from '../../base.service';

@Injectable()
export class RoleService extends BaseService<Role> {
    constructor(protected override http: HttpClient) {
        super(http, '/auth/api/roles');
    }

    getRoleByUser() {
        return this.http.get<Role[]>('/auth/api/roles/user-role');
    }
}
