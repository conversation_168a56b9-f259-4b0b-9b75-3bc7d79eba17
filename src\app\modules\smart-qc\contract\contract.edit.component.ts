import { CommonModule } from '@angular/common';
import {AfterViewInit, Component, OnInit, ViewChild, inject, ElementRef} from '@angular/core';
import {
    FormArray,
    FormBuilder,
    FormGroup,
    FormsModule,
    ReactiveFormsModule,
    Validators
} from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { DialogModule } from 'primeng/dialog';
import { TableModule } from 'primeng/table';
import { PaginatorModule } from 'primeng/paginator';
import { Calendar, CalendarModule } from 'primeng/calendar';
import { CardModule } from 'primeng/card';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { DragDropService } from 'src/app/shared/services/drag.drop.service';
import {Action, Card, Error} from 'src/app/models/interface/smart-qc';
import { NgSelectModule } from '@ng-select/ng-select';
import { FormInputComponent } from 'src/app/shared/components/form-input/form-input.component';
import { ContractService } from 'src/app/services/smart-qc/masterdata/contract.service';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { AreaService } from 'src/app/services/administration/area/area.service';
import { DistrictService } from 'src/app/services/administration/district/district.service';
import { BaseUserService } from 'src/app/services/administration/admin/user.service';
import { ActionService } from 'src/app/services/smart-qc/masterdata/action.service';
import * as _ from 'lodash';
import { AuthService } from 'src/app/core/auth/auth.service';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { ConfirmationService, MessageService } from 'primeng/api';
import { TaskEditTableComponent } from './task-edit.table.component';
import { trimValidator } from 'src/app/shared/directives/time.span.validator';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import {ButtonGroupFileComponent} from "../../../shared/components/button-group-file/button-group-file.component";
import {CardService} from "../../../services/smart-qc/masterdata/card.service";
import {InputValidationComponent} from "../../../shared/components/input-validation/input.validation.component";
import {finalize} from "rxjs";
import {ErrorSmartQCService} from "../../../services/smart-qc/masterdata/error.service";
import {FileService} from "../../../shared/services/file.service";
import {ApiResponse} from "../../../models/interface";
import {AlertService} from "../../../shared/services/alert.service";

@Component({
    selector: 'app-contract.edit',
    standalone: true,
    templateUrl: './contract.create.component.html',
    imports: [
        CommonModule,
        ReactiveFormsModule,
        ButtonModule,
        InputTextModule,
        RouterLink,
        DragDropModule,
        DialogModule,
        TableModule,
        PaginatorModule,
        CalendarModule,
        CardModule,
        InputTextareaModule,
        FormsModule,
        NgSelectModule,
        FormInputComponent,
        TaskEditTableComponent,
        SubHeaderComponent,
        ButtonGroupFileComponent,
        InputValidationComponent
    ],
    providers: [DragDropService, DistrictService, ActionService, AreaService],
})
export class ContractEditComponent implements OnInit, AfterViewInit {
    areaList = [];
    districtList = [];
    districtListFilter = [];
    userList = [];

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    contract: any = { actions: [] };

    actionForm: FormGroup;
    taskForm: FormGroup;

    visibleActionForm = false;
    visibleTaskForm = false;
    visibleImportForm = false;
    visiblemFileForm = false;
    selectedAction: Action = { templateId: null, tasks: [] };
    selectedTask = { area: null, district: null };
    isNewAction = true;
    isNewTask = true;
    authService = inject(AuthService);
    messageService = inject(MessageService);
    confirmationService = inject(ConfirmationService);
    isAdminOrPM = true;

    // Error, Card
    @ViewChild('scrollErrorContainer') scrollErrorContainer!: ElementRef;
    @ViewChild('scrollCardContainer') scrollCardContainer!: ElementRef;
    errors: Error[] = [];
    errorDeleteIndex: number;
    errorsFormArray: FormArray = this.formBuilder.array([]);
    addingError = false;
    editingError = false;
    backupError: Error;

    errorLevelOptions: { label: string; value: number }[] = [
        { label: '1', value: 1 },
        { label: '2', value: 2 },
        { label: '3', value: 3 },
        { label: '4', value: 4 },
    ];

    cards: Card[] = [];
    cardDeleteIndex: number;
    cardsFormArray: FormArray = this.formBuilder.array([]);
    addingCard = false;
    editingCard = false;
    backupCard: Card;
    typesAccept = ["excel"]

    urlError: string;

    constructor(
        public dradropService: DragDropService,
        private formBuilder: FormBuilder,
        private contractService: ContractService,
        private route: ActivatedRoute,
        private router: Router,
        private area: AreaService,
        private district: DistrictService,
        private user: BaseUserService,
        private actionService: ActionService,
        private loadingService: LoadingService,
        private cardService: CardService,
        private errorService: ErrorSmartQCService,
        private fileService: FileService,
        private alertService: AlertService
    ) {
        this.isAdminOrPM = this.authService.isAdminOrPM();
        this.area.getAll().subscribe((data) => {
            this.areaList = data.filter((d) => d.id !== 100 && d.id !== 99);
        });

        this.district.getAll().subscribe((data) => {
            this.districtList = data;
            this.districtListFilter = data;
        });

        if (this.authService.isAdminOrPM() && this.authService.isSubPM()) {
            this.user.getAllQcUser('ROLE_QC_EMPLOYEE,ROLE_QC_SUBPM').subscribe((data) => {
                this.userList = data.map((d) => ({ ...d, displayName: d.fullName + ' - ' + d.email }));
            });
        } else if (this.authService.isAdminOrPM()) {
            this.user.getAllQcUser('ROLE_QC_SUBPM').subscribe((data) => {
                this.userList = data.map((d) => ({ ...d, displayName: d.fullName + ' - ' + d.email }));
            });
        } else {
            this.user.getAllQcUser('ROLE_QC_EMPLOYEE').subscribe((data) => {
                this.userList = data
                    .filter((d) => d.createdBy === this.authService.getPrinciple().email)
                    .map((d) => ({ ...d, displayName: d.fullName + ' - ' + d.email }));
            });
        }
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.loadingService.show();
        });
        this.getContract();
    }

    getContract() {
        const id = Number(this.route.snapshot.paramMap.get('id'));
        this.contractService.getOne(id).subscribe({
            next: (data) => {
                this.contract = data;
                this.selectedAction = this.contract.actions.length > 0 ? this.contract.actions[0] : this.selectedAction;
                this.contract.startTime = this.contract.startTime ? new Date(this.contract.startTime) : '';
                this.contract.endTime = this.contract.endTime ? new Date(this.contract.endTime) : '';
                this.errors = data.errors;
                this.cards = data.cards;
                this.initFormError();
                this.initFormCard();
                this.loadingService.hide();
            },
            error: () => {
                this.loadingService.hide();
                this.messageService.clear();
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'warn',
                    detail: 'Không thể truy cập thông tin dư án',
                });
                this.router.navigate(['/sqc/contract']);
            },
        });
    }

    initFormError() {
        this.errorsFormArray = this.formBuilder.array(
            this.errors.map(error =>
                this.formBuilder.group({
                    id: [error.id],
                    name: [error.name, [Validators.required]],
                    level: [error.level, [Validators.required]],
                    contractId: [this.contract.id],
                    isEdit: [false],
                    isUsed: [error.isUsed ?? false]
                })
            )
        );
    }

    initFormCard() {
        this.cardsFormArray = this.formBuilder.array(
            this.cards.map(card =>
                this.formBuilder.group({
                    id: [card.id],
                    name: [card.name, [Validators.required]],
                    contractId: [this.contract.id],
                    isEdit: [false],
                    isUsed: [card.isUsed ?? false]
                })
            )
        );
    }

    trimName(control) {
        if (typeof control.value === 'string') {
            control.control.setValue(control.value.trim());
        }
    }

    ngOnInit(): void {
        this.actionForm = this.formBuilder.group({
            id: [null],
            name: [null, [trimValidator(), Validators.required, Validators.maxLength(255)]],
            templateId: [null, Validators.required],
            description: [null],
        });
    }

    dropAction(event) {
        this.dradropService.dropElement(event, this.contract.actions, () => {
            this.setActionPosition();
        });
        this.setActionPosition();
        this.actionService.updatePosition(this.contract.actions).subscribe({
            next: () => {},
            error: () => {},
            complete: () => {},
        });
    }

    setActionPosition = () => {
        this.contract.actions.forEach((item, index) => {
            item.position = index + 1;
        });
    };

    selectAction(action) {
        this.selectedAction = action;
    }

    openEditAction(action, isNewAction) {
        this.actionForm.reset();
        this.actionForm.get('templateId').enable();
        if (!isNewAction) {
            if (!action.canDelete) this.actionForm.get('templateId').disable();
            this.actionForm.patchValue(action);
        }
        this.visibleActionForm = true;
        this.isNewAction = isNewAction;
    }

    onSelectTemplate(value) {
        this.actionForm.patchValue({
            templateId: value,
        });
    }

    deleteAction(action, index) {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận xóa công việc',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.actionService.deleteAction(action.id).subscribe(() => {
                    this.contract.actions = this.contract.actions.filter((a, i) => i !== index);
                    if (this.contract.actions.length > 0) {
                        this.selectedAction = this.contract.actions[this.contract.actions.length - 1];
                    } else {
                        this.selectedAction = { templateId: null, tasks: [] };
                    }
                    this.setActionPosition();
                });
            },
        });
    }

    setActionTemplate = (e) => {
        this.actionForm.patchValue({ templateId: e?.id });
    };

    saveAction() {
        if (!this.actionForm.valid) return;

        if (this.isNewAction) {
            if (this.contract.actions.some((a) => a.name === this.actionForm.getRawValue().name)) {
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'error',
                    summary: 'Lỗi thêm công việc',
                    detail: 'Công việc cùng tên đã tồn tại trong dự án',
                });
                return;
            }

            this.actionService
                .createAction({
                    ...this.selectedAction,
                    ...this.actionForm.getRawValue(),
                    contractId: this.contract.id,
                    tasks: [],
                    position: this.contract.actions.length + 1,
                })
                .subscribe({
                    next: (newAction) => {
                        this.contract.actions.push({
                            ...newAction,
                            contractId: this.contract.id,
                            tasks: [],
                        });
                        this.selectedAction = this.contract.actions[this.contract.actions.length - 1];
                        this.setActionPosition();
                        this.messageService.add({
                            key: 'app-alert',
                            severity: 'success',
                            summary: 'Thành công',
                            detail: 'Tạo công việc thành công',
                        });
                    },
                    error: () => {},
                });
        } else {
            this.actionService
                .updateAction({
                    ...this.selectedAction,
                    ...this.actionForm.getRawValue(),
                })
                .subscribe({
                    next: (updatedAction) => {
                        this.contract.actions = this.contract.actions.map((a) => {
                            if (this.selectedAction === a) {
                                this.selectedAction = { ...updatedAction, tasks: a.tasks };
                                return this.selectedAction;
                            }
                            return a;
                        });
                        this.messageService.add({
                            key: 'app-alert',
                            severity: 'success',
                            summary: 'Thành công',
                            detail: 'Cập nhật công việc thành công',
                        });
                    },
                    error: () => {},
                });
            this.setActionPosition();
        }
        this.visibleActionForm = false;
    }

    setTask = (tasks) => {
        this.selectedAction.tasks = tasks;
    };

    @ViewChild('startTime') startTimeModel: Calendar;
    @ViewChild('endTime') endTimeModel: Calendar;

    saveContract = (contractForm) => {
        if (
            (contractForm.invalid && !contractForm.valid) ||
            this.contract.name?.length > 255 ||
            this.startTimeModel?.value?.length ||
            this.endTimeModel?.value?.length ||
            this.startTimeModel?.value?.getTime() > this.endTimeModel?.value?.getTime()
        ) {
            return;
        }

        const body = _.cloneDeep(this.contract);

        body.startTime = body.startTime instanceof Date ? body.startTime.getTime() : body.startTime;
        body.endTime = body.endTime instanceof Date ? body.endTime.getTime() : body.endTime;

        body.actions.forEach((a) => {
            a.contractId = body.id;
            a.tasks.forEach((task) => {
                task.startTime = task.startTime instanceof Date ? task.startTime.getTime() : task.startTime;
                task.endTime = task.endTime instanceof Date ? task.endTime.getTime() : task.endTime;
                task.templateId = a.templateId;
            });
        });

        this.contractService.simpleUpdateOne(body).subscribe(() => {
            this.messageService.add({
                key: 'app-alert',
                severity: 'success',
                summary: 'Thành công',
                detail: 'Sửa dự án thành công',
            });
        });
    };

    checkIfExcelFile(file: File): boolean {
        const allowedExtensions = ['xlsx', 'xls'];
        const fileExtension = file.name.split('.').pop()?.toLowerCase();
        return fileExtension ? allowedExtensions.includes(fileExtension) : false;
    }

    handleSelectFile(file: File) {
        this.loadingService.show();
        this.contractService
            .importError(file, this.contract.id)
            .subscribe({
                next: (res: ApiResponse) => {
                    if (res.code === 1) {
                        this.errors = res.data['errors'] as Error[];
                        this.cards = res.data['cards'] as Card[];
                        this.alertService.success('Thành công');
                        this.urlError = null;

                        this.getContract();
                    } else {
                        this.urlError = res.message;
                    }
                    this.loadingService.hide();
                },
                error: (e) => {
                    this.loadingService.hide();
                    this.alertService.handleError(e);
                },
            });
    }

    handleClearFile() {

    }

    handleDownloadFile() {
        this.fileService.downLoadSampleFileByService('template_error_card.xlsx', '/smart-qc/api');
    }

    removeError(index: number) {
        this.errorDeleteIndex = index;
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận xoá phân loại lỗi',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.confirmDeleteError();
            },
        });
    }

    addError(): void {
        const newItem = this.formBuilder.group({
            id: [null],
            name: [null, [Validators.required]],
            level: [null, [Validators.required]],
            contractId: [this.contract.id],
            isEdit: [true],
            isUsed: [false]
        });
        this.errorsFormArray.push(newItem);
        this.addingError = true;
        this.scrollToBottomError();
    }

    editError(index: number) {
        const control = this.errorsFormArray.at(index) as FormGroup;
        this.backupError = control.getRawValue();
        control.get("isEdit")?.setValue(true);
        this.editingError = true;
    }

    saveError(index: number) {
        const control = this.errorsFormArray.at(index) as FormGroup;
        this.markAllAsTouched(control);
        if (control.valid) {
            if (this.editingError) {
                // Edit error
                const newName = control.get('name')?.value;
                const newLevel = control.get('level')?.value;

                const isDuplicate = this.errorsFormArray.controls.some(((control, errorIndex) =>
                        index !== errorIndex && control.get('name')?.value === newName && control.get('level')?.value === newLevel
                ));

                if (isDuplicate) {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'error',
                        summary: 'Lỗi',
                        detail: 'Phân loại lỗi – Mức độ đã tồn tại',
                    });
                    return; // Không thêm nếu đã tồn tại
                }
                this.callApiUpdateError(control);
            } else {
                // Add new error
                const newName = control.get('name')?.value;
                const newLevel = control.get('level')?.value;

                const isDuplicate = this.errorsFormArray.controls.some(((control, errorIndex) =>
                        index !== errorIndex && control.get('name')?.value === newName && control.get('level')?.value === newLevel
                ));

                if (isDuplicate) {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'error',
                        summary: 'Lỗi',
                        detail: 'Phân loại lỗi – Mức độ đã tồn tại',
                    });
                    return; // Không thêm nếu đã tồn tại
                }
                this.callApiCreateError(control);
            }
        }
    }

    cancelEditError(index: number) {
        if (this.editingError) {
            const control = this.errorsFormArray.at(index) as FormGroup;
            control.patchValue(this.backupError);
            this.editingError = false;
        } else {
            this.errorsFormArray.removeAt(index);
            this.addingError = false;
        }
    }

    callApiUpdateError(control: FormGroup) {
        const error = control.getRawValue();
        this.loadingService.show();
        this.errorService.update(error)
            .pipe(
                finalize(() => {
                    this.loadingService.hide();
                }),
            )
            .subscribe({
                next: () => {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'success',
                        summary: 'Thành công',
                        detail: 'Cập nhật phân loại lỗi thành công',
                    });
                    control.get("isEdit")?.setValue(false);
                    this.editingError = false;
                },
                error: () => {},
                complete: () => {},
            });
    }

    callApiCreateError(control: FormGroup) {
        const error = control.getRawValue();
        this.loadingService.show();
        this.errorService.create(error)
            .pipe(
                finalize(() => {
                    this.loadingService.hide();
                }),
            )
            .subscribe({
                next: (res) => {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'success',
                        summary: 'Thành công',
                        detail: 'Tạo phân loại lỗi thành công',
                    });
                    control.get("isEdit")?.setValue(false);
                    control.get("id")?.setValue(res.body.id);
                    this.addingError = false;
                    setTimeout(() => {
                        this.scrollToBottomError();
                    }, 200);
                },
                error: () => {},
                complete: () => {},
            });
    }

    confirmDeleteError() {
        this.loadingService.show();
        const control = this.errorsFormArray.at(this.errorDeleteIndex) as FormGroup;
        this.errorService.delete(control.get("id").getRawValue())
            .pipe(
                finalize(() => {
                    this.loadingService.hide();
                }),
            )
            .subscribe({
                next: () => {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'success',
                        summary: 'Thành công',
                        detail: 'Xoá phân loại lỗi thành công',
                    });
                    this.errorsFormArray.removeAt(this.errorDeleteIndex);
                },
                error: () => {},
                complete: () => {},
            });
    }


    scrollToBottomError(): void {
        if (this.scrollErrorContainer) {
            const container = this.scrollErrorContainer.nativeElement;
            container.scrollTop = container.scrollHeight;
        }
    }

    // Card

    removeCard(index: number) {
        this.cardDeleteIndex = index;
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận xoá card',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.confirmDeleteCard();
            },
        });
    }

    addCard(): void {
        const newItem = this.formBuilder.group({
            id: [null],
            name: [null, [Validators.required]],
            contractId: [this.contract.id],
            isEdit: [true],
            isUsed: [false]
        });
        this.cardsFormArray.push(newItem);
        this.addingCard = true;
        this.scrollToBottomCard();
    }

    editCard(index: number) {
        const control = this.cardsFormArray.at(index) as FormGroup;
        this.backupCard = control.getRawValue();
        control.get("isEdit")?.setValue(true);
        this.editingCard = true;
    }

    saveCard(index: number) {
        const control = this.cardsFormArray.at(index) as FormGroup;
        this.markAllAsTouched(control);
        if (control.valid) {
            if (this.editingCard) {
                // Edit error
                const newName = control.get('name')?.value;
                const isDuplicate = this.cardsFormArray.controls.some((cardControl, cardIndex) =>
                    index !== cardIndex && cardControl.get('name')?.value === newName
                );
                if (isDuplicate) {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'error',
                        summary: 'Lỗi',
                        detail: 'Tên card đã tồn tại',
                    });
                    return;
                }
                this.callApiUpdateCard(control);
            } else {
                // Add new error
                const newName = control.get('name')?.value;
                const isDuplicate = this.cardsFormArray.controls.some((cardControl, cardIndex) =>
                    index !== cardIndex && cardControl.get('name')?.value === newName
                );
                if (isDuplicate) {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'error',
                        summary: 'Lỗi',
                        detail: 'Tên card đã tồn tại',
                    });
                    return;
                }
                this.callApiCreateCard(control);
            }
        }
    }

    cancelEditCard(index: number) {
        if (this.editingCard) {
            const control = this.cardsFormArray.at(index) as FormGroup;
            control.patchValue(this.backupCard);
            this.editingCard = false;
        } else {
            this.cardsFormArray.removeAt(index);
            this.addingCard = false;
        }
    }


    callApiUpdateCard(control: FormGroup) {
        const card = control.getRawValue();
        this.loadingService.show();
        this.cardService.update(card)
            .pipe(
                finalize(() => {
                    this.loadingService.hide();
                }),
            )
            .subscribe({
                next: () => {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'success',
                        summary: 'Thành công',
                        detail: 'Cập nhật card thành công',
                    });
                    control.get("isEdit")?.setValue(false);
                    this.editingCard = false;
                },
                error: () => {},
                complete: () => {},
            });
    }

    callApiCreateCard(control: FormGroup) {
        const card = control.getRawValue();
        this.loadingService.show();
        this.cardService.create(card)
            .pipe(
                finalize(() => {
                    this.loadingService.hide();
                }),
            )
            .subscribe({
                next: (res) => {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'success',
                        summary: 'Thành công',
                        detail: 'Tạo card thành công',
                    });
                    control.get("isEdit")?.setValue(false);
                    control.get("id")?.setValue(res.body.id);
                    this.addingCard = false;
                    setTimeout(() => {
                        this.scrollToBottomCard();
                    }, 200);
                },
                error: () => {},
                complete: () => {},
            });
    }

    confirmDeleteCard() {
        this.loadingService.show();
        const control = this.cardsFormArray.at(this.cardDeleteIndex) as FormGroup;
        this.cardService.delete(control.get("id").getRawValue())
            .pipe(
                finalize(() => {
                    this.loadingService.hide();
                }),
            )
            .subscribe({
                next: () => {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'success',
                        summary: 'Thành công',
                        detail: 'Xoá card thành công',
                    });
                    this.cardsFormArray.removeAt(this.cardDeleteIndex);
                },
                error: () => {},
                complete: () => {},
            });
    }


    scrollToBottomCard(): void {
        if (this.scrollCardContainer) {
            const container = this.scrollCardContainer.nativeElement;
            container.scrollTop = container.scrollHeight;
        }
    }

    markAllAsTouched(form: FormGroup) {
        Object.keys(form.controls).forEach((field) => {
            const control = form.get(field);
            control?.markAsTouched({ onlySelf: true });
        });
    }
}
