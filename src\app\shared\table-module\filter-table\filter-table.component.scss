:host ::ng-deep .p-dropdown {
    width: 100%;
}

:host ::ng-deep .p-multiselect {
    width: 100%;
}

:host ::ng-deep .p-calendar {
    width: 100%;
}

:host ::ng-deep .p-inputtext {
    width: 100%;
}

:host ::ng-deep .p-datepicker-touch-ui {
    z-index: 1102;
}

:host ::ng-deep .p-multiselect-label {
    max-width: 500px;
}

:host ::ng-deep .p-multiselect-label {
    /* Thay đổi kích thước và màu sắc của scrollbar */
    overflow-y: auto; /* <PERSON><PERSON><PERSON> bảo có thanh cuộn dọc nếu cần */
}

:host ::ng-deep .p-multiselect-label::-webkit-scrollbar {
    width: 8px; /* <PERSON><PERSON>ch thước chiều rộng của scrollbar */
    height: 8px; /* <PERSON><PERSON>ch thước chiều cao của scrollbar (cho scrollbar ngang) */
}

/* <PERSON><PERSON><PERSON> nền của scrollbar */
:host ::ng-deep .p-multiselect-label::-webkit-scrollbar-track {
    background: #f1f1f1; /* <PERSON><PERSON><PERSON> nền cho track (phần nền của scrollbar) */
}

/* Màu của thanh cuộn */
:host ::ng-deep .p-multiselect-label::-webkit-scrollbar-thumb {
    background: #888; /* Màu của thanh cuộn */
    border-radius: 4px; /* Bo tròn góc của thanh cuộn */
}

/* Màu của thanh cuộn khi hover */
:host ::ng-deep .p-multiselect-label::-webkit-scrollbar-thumb:hover {
    background: #555; /* Màu khi hover lên thanh cuộn */
}

/* Tùy chỉnh cho Firefox */
:host ::ng-deep .p-multiselect-label {
    scrollbar-width: thin; /* Đặt kích thước thanh cuộn là nhỏ */
    scrollbar-color: #888 #f1f1f1; /* Màu thanh cuộn và màu nền */
}
