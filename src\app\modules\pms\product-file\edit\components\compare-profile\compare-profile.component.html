<p-table [value]="[]" [tableStyle]="{ 'table-layout': 'fixed', width: '100%' }" class="header-table" styleClass="p-datatable-gridlines">
    <!-- Khóa width để align với 5-cột tables bên dưới:
           20% cho <PERSON><PERSON><PERSON> đo<PERSON>, 40% cho Version 1.0, 40% cho Version 1.1 -->
    <ng-template pTemplate="colgroup">
        <colgroup>
            <col style="width: 20%" />
            <col style="width: 40%" />
            <col style="width: 40%" />
        </colgroup>
    </ng-template>

    <!-- Dòng 1: Version headers -->
    <ng-template pTemplate="header">
        <tr>
            <th></th>
            <th colspan="1">{{ version1 }}</th>
            <th colspan="1">{{ version2 }}</th>
        </tr>
        <!-- Dòng 2: MP dưới mỗi version -->
        <tr>
            <th><PERSON>ia<PERSON> đoạn</th>
            <th>{{ getLifecycleStage(versions[0]?.lifecycleStage) }}</th>
            <th>{{ getLifecycleStage(versions[1]?.lifecycleStage) }}</th>
        </tr>
    </ng-template>
</p-table>
<div class="tw-flex tw-justify-between tw-items-center tw-my-3">
    <div class="tw-flex tw-items-center tw-gap-2">
        <span>Chỉ hiển thị những mục khác biệt</span>
        <p-inputSwitch [(ngModel)]="onlyShowDifferences" (click)="onToggleDifferences()"></p-inputSwitch>
    </div>

    <div class="tw-flex tw-items-center tw-gap-4">
        <span>Ẩn Ghi chú:</span>
        <div class="tw-flex tw-items-center tw-gap-2">
            <p-checkbox [(ngModel)]="hideNote1" inputId="hideNote1" (onChange)="onToggleNoteHidden()" binary="true"></p-checkbox>
            <label for="hideNote1" class="tw-cursor-pointer">{{ version1 }}</label>
        </div>
        <div class="tw-flex tw-items-center tw-gap-2">
            <p-checkbox [(ngModel)]="hideNote2" inputId="hideNote2" (onChange)="onToggleNoteHidden()" binary="true"></p-checkbox>
            <label for="hideNote2" class="tw-cursor-pointer">{{ version2 }}</label>
        </div>
        <!-- <p-checkbox [(ngModel)]="hideNote2" inputId="hideNote2" (onChange)="onToggleNoteHidden()" binary="true" [label]="version2"></p-checkbox> -->
    </div>
</div>
<app-tab-view [tabs]="itemsTab" (tabChange)="onTabChange($event)"> </app-tab-view>
