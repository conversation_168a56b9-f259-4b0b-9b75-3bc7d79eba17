import { Component, Input, Output, EventEmitter, OnInit, <PERSON><PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AbstractControl, FormsModule, ValidationErrors } from '@angular/forms';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { InfoShareComponent } from '../info-share/info-share.component';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { PanelModule } from 'primeng/panel';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { AbstractControlCustom, FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { FormArrayCustom } from 'src/app/shared/form-module/from-array.custom';
import { ManBom, ManBomDetail } from 'src/app/models/interface/ptm/manbom';
import { ManBomService } from 'src/app/services/ptm/manbom/manbom.service';
import { Observable, Subject, takeUntil } from 'rxjs';
import { InputTextModule } from 'primeng/inputtext';
import { MANBOM_PROCESS_TYPE, MANBOM_MATERIAL_TYPE, TAB_TYPE } from 'src/app/models/constant/ptm';
import { DropdownModule } from 'primeng/dropdown';
import { TabSharedStateService } from 'src/app/services/ptm/manufacturing-technology-records/tab-shared-state/tab-shared-state.service';
import { MessageService } from 'primeng/api';
import { TrackChangesComponent } from '../track-changes/track-changes.component';
import { environment } from 'src/environments/environment';
import { HttpClient } from '@angular/common/http';

@Component({
    selector: 'app-manbom',
    standalone: true,
    templateUrl: './manbom.component.html',
    styleUrls: ['./manbom.component.scss'],
    imports: [
        CommonModule,
        FormsModule,
        InputTextareaModule,
        InfoShareComponent,
        FormCustomModule,
        PanelModule,
        TableModule,
        ButtonModule,
        InputTextModule,
        DropdownModule,
        TrackChangesComponent,
    ],
})
export class ManbomComponent implements OnInit, OnDestroy {
    // 🧠 INPUTS từ cha truyền xuống
    @Input() title: string = '';
    @Input() content: string = '';
    @Input() config: { type: string } = { type: 'default' };
    @Input() items: string[] = [];
    @Input() editable: boolean = true;
    @Input() isSaving!: Observable<boolean>;
    @Input() currentProduct: any;
    @Input() productInstructionId: number;
    @ViewChild('form', { static: false }) formComponent!: FormComponent;
    processType = MANBOM_PROCESS_TYPE;
    materialType = MANBOM_MATERIAL_TYPE;
    oldManBom: ManBom = {
        reviewerIds: [],
        versionId: 0,
        phase: 0,
        listManBom: [],
    };
    instructionId: number;
    name: string;
    detailManBom: any;
    mode: 'view' | 'create' | 'edit' = 'create';
    isCreatingInstruction = false;
    tabType = TAB_TYPE.manbom;
    listDes: any[] = [];
    showTrackDialog: boolean;
    selectedTab: number = 1;
    downloadBaseUrl: string = environment.STORAGE_BASE_URL;
    private destroy$ = new Subject<void>();
    // 📤 OUTPUTS emit về cha
    @Output() submitted = new EventEmitter<any>();

    ngOnDestroy(): void {
        console.log('🧹 [ManbomComponent] Unmounted');
    }
    ngOnInit(): void {
        console.log('🧹 [ManbomComponent] Init');
        this.instructionId = this.productInstructionId;
        this.initForm(this.oldManBom);
        this.name = 'PFD-' + this.currentProduct?.tradeName + '-' + this.currentProduct?.vnptManPn;
        this.tabSharedState
            .getProductInstructionId$()
            .pipe(takeUntil(this.destroy$))
            .subscribe((id) => {
                if (id) {
                    this.instructionId = id;
                    this.getManBom();
                } else {
                    this.getManBom();
                }
            });

        this.tabSharedState
            .getProductVersionId$()
            .pipe(takeUntil(this.destroy$))
            .subscribe((verId) => {
                this.formGroup.patchValue({
                    versionId: verId,
                });
            });

        this.tabSharedState
            .getPhase$()
            .pipe(takeUntil(this.destroy$))
            .subscribe((phase) => {
                this.formGroup.patchValue({
                    phase: phase,
                });
            });

        this.tabSharedState
            .getMode$()
            .pipe(takeUntil(this.destroy$))
            .subscribe((mode) => {
                this.mode = mode;
            });

        this.getListDes();
    }
    formGroup: FormGroupCustom<ManBom>;
    constructor(
        private fb: FormBuilder,
        private manBomService: ManBomService,
        private tabSharedState: TabSharedStateService,
        private messageService: MessageService,
        private http: HttpClient,
    ) {
        this.initForm(this.oldManBom);
    }

    initForm(data: ManBom | null) {
        this.formGroup = new FormGroupCustom<ManBom>(this.fb, {
            listManBom: new FormArrayCustom(this.initFormContact(data?.listManBom || [])),
            versionId: [data?.versionId],
            phase: [data?.phase],
            reviewerIds: [data?.reviewerIds],
        });
    }

    get manBom(): FormArrayCustom<FormGroup> {
        return this.formGroup.get('listManBom') as FormArrayCustom<FormGroup>;
    }

    numberCommaDotValidator(control: AbstractControl): ValidationErrors | null {
        const value = control.value;
        if (value === null || value === '') return null;

        const regex = /^[\d,\.]+$/;
        return regex.test(value) ? null : { invalidCharacters: true };
    }

    allowOnlyNumbersCommaDot(event: KeyboardEvent): void {
        const allowedChars = /[0-9.,]/;
        const inputChar = event.key;
        if (!allowedChars.test(inputChar)) {
            event.preventDefault();
        }
    }

    initFormContact(items: ManBomDetail[]): FormGroup[] {
        const isViewMode = this.mode === 'view';
        return items.map(
            (item) =>
                new FormGroupCustom(this.fb, {
                    id: [item?.id],
                    instructionId: [item?.instructionId],
                    pfmeaId: [item?.pfmeaId],
                    workStandardId: [item?.workStandardId],
                    description: [{ value: item?.description, disabled: isViewMode }, Validators.required],
                    unit: [{ value: item?.unit, disabled: isViewMode }, [Validators.required, Validators.maxLength(10)]],
                    section: [{ value: item?.section, disabled: isViewMode }, Validators.required],
                    quantity: [{ value: item?.quantity, disabled: isViewMode }, [Validators.required, Validators.maxLength(10), this.numberCommaDotValidator]],
                    reference: [{ value: item?.reference, disabled: isViewMode }, Validators.maxLength(50)],
                    materialType: [{ value: item?.materialType, disabled: isViewMode }, Validators.required],
                    consumptionRate: [{ value: item?.consumptionRate, disabled: isViewMode }, [Validators.maxLength(10), this.numberCommaDotValidator]],
                    note: [{ value: item?.note, disabled: isViewMode }, Validators.maxLength(50)],
                    actionPayload: [item?.actionPayload],
                }),
        );
    }
    handleApproverChange(event: any) {
        this.formGroup.patchValue({
            reviewerIds: event.map((item: any) => item.id),
        });
    }

    getListDes() {
        this.manBomService.getListDes().subscribe({
            next: (res) => {
                this.listDes = res;
            },
            error: () => {},
        });
    }

    getManBom() {
        this.manBomService.getManBom(this.instructionId).subscribe({
            next: (res) => {
                this.detailManBom = res;
                const isViewMode = this.mode === 'view';

                const newItems = this.detailManBom.manbomDtos.map(
                    (item, index) =>
                        new FormGroupCustom(this.fb, {
                            id: [item?.id],
                            instructionId: [item?.instructionId],
                            pfmeaId: [item?.pfmeaId],
                            workStandardId: [item?.workStandardId],
                            description: [{ value: item?.description, disabled: isViewMode }, Validators.required],
                            unit: [{ value: item?.unit, disabled: isViewMode }, [Validators.required, Validators.maxLength(10)]],
                            section: [{ value: item?.section, disabled: isViewMode }, Validators.required],
                            quantity: [
                                { value: item?.quantity, disabled: isViewMode },
                                [Validators.required, Validators.maxLength(10), this.numberCommaDotValidator],
                            ],
                            reference: [{ value: item?.reference, disabled: isViewMode }, Validators.maxLength(50)],
                            materialType: [{ value: item?.materialType, disabled: isViewMode }, Validators.required],
                            consumptionRate: [{ value: item?.consumptionRate, disabled: isViewMode }, [Validators.maxLength(10), this.numberCommaDotValidator]],
                            note: [{ value: item?.note, disabled: isViewMode }, Validators.maxLength(50)],
                            actionPayload: [item.instructionId ? 0 : 1],
                        }),
                );

                this.manBom.clear();

                newItems.forEach((item) => this.manBom.push(item));

                this.formGroup.patchValue({
                    reviewerIds: this.detailManBom?.instructionInfo?.reviewers.map((item: any) => item.id),
                });
            },
            error: () => {},
        });
    }

    handleChangeValue(index: number) {
        if (this.manBom.at(index).get('actionPayload')?.value === 1) return;
        this.manBom.at(index).get('actionPayload')?.setValue(2);
    }

    addItem() {
        let newItem = null;
        newItem = new FormGroupCustom(this.fb, {
            id: [null],
            pfmeaId: [null],
            workStandardId: [null],
            description: [null, Validators.required],
            unit: [null, [Validators.required, Validators.maxLength(10)]],
            section: [null, Validators.required],
            quantity: [null, [Validators.required, Validators.maxLength(10), this.numberCommaDotValidator]],
            reference: [null, Validators.maxLength(50)],
            materialType: [null, Validators.required],
            consumptionRate: [null, [Validators.maxLength(10), this.numberCommaDotValidator]],
            note: [null, Validators.maxLength(50)],
            actionPayload: [1],
        });
        this.manBom.push(newItem);
    }

    removeItem(index: number) {
        if (this.manBom.at(index).get('id')?.value === null) {
            this.manBom.removeAt(index);
        } else {
            this.manBom.at(index).get('actionPayload')?.setValue(3);
        }
    }

    exportManBom() {
        this.manBomService.exportManBom(this.instructionId).subscribe({
            next: (res) => {
                const url = `${this.downloadBaseUrl}/${res}`;

                this.http.get(url, { responseType: 'blob' }).subscribe((blob) => {
                    const objectUrl = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = objectUrl;
                    a.download = 'ManBom.xlsx';
                    a.click();
                    URL.revokeObjectURL(objectUrl);
                });
            },
            error: () => {},
        });
    }

    exportManBomErp() {
        this.manBomService.exportManBomErp(this.instructionId).subscribe({
            next: (res) => {
                const url = `${this.downloadBaseUrl}/${res}`;

                this.http.get(url, { responseType: 'blob' }).subscribe((blob) => {
                    const objectUrl = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = objectUrl;
                    a.download = 'ManBom Export.xlsx';
                    a.click();
                    URL.revokeObjectURL(objectUrl);
                });
            },
            error: () => {},
        });
    }

    openTrackDialog(tab: number): void {
        this.showTrackDialog = true;
        this.selectedTab = tab;
    }

    handlePreview() {
        const payload = {
            tabType: this.tabType,
        };
        this.manBomService.previewManBom(payload, this.instructionId).subscribe({
            next: (res) => {
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'success',
                    summary: 'Thành công',
                    detail: 'Gửi review thành công',
                });
                this.getManBom();
            },
            error: () => {},
        });
    }

    handleComplete() {
        const payload = {
            tabType: this.tabType,
            approvalStatus: 8,
        };
        this.manBomService.comfirmManBom(payload, this.instructionId).subscribe({
            next: (res) => {
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'success',
                    summary: 'Thành công',
                    detail: 'Phê duyệt thành công',
                });
                this.getManBom();
            },
            error: () => {},
        });
    }

    handleReject() {
        const payload = {
            tabType: this.tabType,
            approvalStatus: 4,
        };
        this.manBomService.comfirmManBom(payload, this.instructionId).subscribe({
            next: (res) => {
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'success',
                    summary: 'Thành công',
                    detail: 'Từ chối thành công',
                });
                this.getManBom();
            },
            error: () => {},
        });
    }

    validateManbom(): boolean {
        const formArray = this.manBom;

        formArray.controls.forEach((groupControl) => {
            const group = groupControl as FormGroup;
            group.markAllAsTouched();

            Object.keys(group.controls).forEach((key) => {
                const control = group.get(key);
                control?.updateValueAndValidity({ onlySelf: true, emitEvent: false });
            });
        });

        formArray.updateValueAndValidity();

        return this.formGroup.valid;
    }

    parseNumber(value: any): number | null {
        if (value === null || value === '') return null;

        // Nếu là string, thay dấu phẩy bằng dấu chấm rồi parseFloat
        if (typeof value === 'string') {
            const cleaned = value.replace(',', '.');
            const parsed = parseFloat(cleaned);
            return isNaN(parsed) ? null : parsed;
        }

        // Nếu đã là số
        if (typeof value === 'number') return value;

        return null;
    }

    handleSubmit() {
        if (!this.validateManbom()) return;
        const payloadData = {
            details: this.manBom.controls.map((group) => {
                return {
                    id: group.get('id')?.value,
                    instructionId: group.get('instructionId')?.value ? group.get('instructionId')?.value : this.instructionId,
                    pfmeaId: group.get('pfmeaId')?.value,
                    workStandardId: group.get('workStandardId')?.value,
                    description: group.get('description')?.value,
                    unit: group.get('unit')?.value,
                    section: group.get('section')?.value,
                    quantity: this.parseNumber(group.get('quantity')?.value),
                    reference: group.get('reference')?.value,
                    materialType: group.get('materialType')?.value,
                    consumptionRate: this.parseNumber(group.get('consumptionRate')?.value),
                    note: group.get('note')?.value,
                    actionPayload: group.get('actionPayload')?.value,
                };
            }),
            versionId: this.formGroup.get('versionId')?.value,
            phase: this.formGroup.get('phase')?.value,
            reviewerIds: this.formGroup.get('reviewerIds')?.value,
        };

        this.manBomService.create(payloadData, this.instructionId).subscribe({
            next: (res) => {
                if (this.instructionId !== 0) {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'success',
                        summary: 'Thành công',
                        detail: 'Cập nhật manbom thành công',
                    });
                } else {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'success',
                        summary: 'Thành công',
                        detail: 'Tạo manbom thành công',
                    });
                }
                if (this.instructionId === 0) {
                    this.instructionId = res.id;
                }
                this.getManBom();
            },
            error: () => {},
            complete: () => {},
        });
    }
    onSubmit() {
        if (this.formGroup.invalid) return;
        this.submitted.emit(this.formGroup.value);
    }
}
