import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpRequest, HttpEvent, HttpEventType, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

import { filter, map } from 'rxjs/operators';
import { ProductLine } from 'src/app/models/interface/pms';

@Injectable()
export class ProductLineService {
    private apiUrl = '/pr/api/product-line';

    constructor(private http: HttpClient) {}

    createProductLine(name: string): Observable<any> {
        return this.http.post(this.apiUrl, { name });
    }

    getProductLine(payload: any): Observable<any> {
        const params: any = {
            page: payload.page,
            size: payload.size,
        };

        // Chỉ thêm name nếu có giá trị và không rỗng
        if (payload.name && payload.name.trim() !== '') {
            params.name = payload.name.trim();
        }
        return this.http.get<ProductLine[]>('/pr/api/product-line/filter', {
            params: params,
        });
    }

    updateProductLine(id: number, name: string): Observable<any> {
        return this.http.put(`/pr/api/product-line/${id}`, { name });
    }

    updateProductModel(
        id: number,
        payload: {
            id: number;
            lineId: number;
            name: string;
            code: string;
            description: string;
        },
    ): Observable<any> {
        return this.http.put(`/pr/api/product-model/${id}`, payload);
    }

    getProductDoc(filters: { versionId?: number; types?: number }): Observable<any> {
        let params = new HttpParams();

        if (filters.versionId != null) {
            params = params.set('versionId', filters.versionId.toString());
        }
        if (filters.types != null) {
            params = params.set('types', filters.types.toString());
        }

        return this.http.get<any>(`/pr/api/product-document`, { params });
    }
    filterProducts(params: { page?: number; size?: number; lineId?: number; modelId?: number; customerId?: number; vnptManPn?: string }): Observable<any> {
        // Xây dựng query parameters
        const queryParams = new URLSearchParams();

        if (params.page) queryParams.append('page', params.page.toString());
        if (params.size) queryParams.append('size', params.size.toString());
        if (params.lineId) queryParams.append('lineId', params.lineId.toString());
        if (params.modelId) queryParams.append('modelId', params.modelId.toString());
        if (params.customerId) queryParams.append('customerId', params.customerId.toString());
        if (params.vnptManPn) queryParams.append('vnptManPn', params.vnptManPn);

        return this.http.get(`/pr/api/product/filter?${queryParams.toString()}`);
    }

    getProductLineById(id: number) {
        return this.http.get(`/pr/api/product-line/${id}`);
    }

    deleteProductLine(id: number) {
        return this.http.delete(`/pr/api/product-line/${id}`);
    }

    duplicateProductLine(id: number) {
        return this.http.post<any>(`/pr/api/product-line/${id}/duplicate`, {});
    }

    createProductModel(model: any): Observable<any> {
        return this.http.post('/pr/api/product-model', model);
    }

    deleteProductModel(id: number): Observable<any> {
        const url = `/pr/api/product-model/${id}`;
        return this.http.delete<any>(url, {
            headers: {
                'Content-Type': 'application/json', // 👈 Không bắt buộc cho DELETE nhưng có thể thêm rõ ràng
            },
        });
    }

    duplicateProductModel(id: number): Observable<any> {
        return this.http.post(`/pr/api/product-model/${id}/duplicate`, {});
    }
    // Tạo mới sản phẩm
    createProduct(formData: FormData): Observable<any> {
        return this.http.post('/pr/api/product', formData);
    }

    getFilteredProducts(params: { page?: number; size?: number; lineId?: number; modelId?: any[]; customerId?: any[]; vnptManPn?: string }): Observable<any> {
        // Chuyển đổi params thành query string
        const queryParams = new URLSearchParams();

        if (params.page) queryParams.append('page', params.page.toString());
        if (params.size) queryParams.append('size', params.size.toString());
        if (params.lineId) queryParams.append('lineId', params.lineId.toString());
        if (params.modelId) queryParams.append('modelId', params.modelId.toString());
        if (params.customerId) queryParams.append('customerId', params.customerId.toString());
        if (params.vnptManPn) queryParams.append('vnptManPn', params.vnptManPn);

        return this.http.get(`/pr/api/product?${queryParams.toString()}`, {
            observe: 'response',
        });
    }

    getProductModels(body: any): Observable<any> {
        return this.http.get<any>('/pr/api/product-model', {
            params: {
                page: body.page,
                size: body.size,
                unpaged: body.unpaged,
                productLineId: body.productLineId,
            },
        });
    }
    getProductModelsHistory(body: any): Observable<any> {
        return this.http.get<any>('/pr/api/product-model', {
            params: {
                name: body.name,
                page: body.page,
                size: body.size,
                unpaged: body.unpaged,
                productLineId: body.productLineId,
            },
        });
    }
    getUnusedManufacturers(query: { page: number; size: number; unpaged: boolean; name?: string }): Observable<any> {
        let params = new HttpParams().set('page', query.page.toString()).set('size', query.size.toString()).set('unpaged', query.unpaged.toString());
        if (query.name) params = params.set('name', query.name);
        return this.http.get<any>('/pr/api/sys_part_manufacturer/unused', { params });
    }

    getCustomers(payload: any): Observable<any> {
        const params = {
        ...(payload?.size != null && { size: payload.size })
    };
    
    return this.http.get<any>('/pr/api/company/list', { params });

    }

    deleteProduct(id: number) {
        return this.http.delete(`/pr/api/product/${id}`);
    }

    getProductById(id: number) {
        return this.http.get(`/pr/api/product/${id}`);
    }

    getProductHistory(id: number) {
        return this.http.get(`/pr/api/history/product/${id}`);
    }

    updateProduct(id: number, formData: FormData) {
        return this.http.put(`/pr/api/product/${id}`, formData);
    }

    getDocuments(params: { versionId: number; types: number }): Observable<any> {
        return this.http.get('/pr/api/product-document', { params });
    }

    analyzeFile(fileName: string, type: string): Observable<any> {
        const rawName = fileName.replace(/\s+/g, '');
        return this.http.post('/pr/api/files/analyze-file', { fileName: rawName, type });
    }
    uploadToPresignedUrl(file: File, presignedUrl: string): Observable<number> {
        const rawName = file.name.replace(/\s+/g, '');

        const encoded = encodeURIComponent(rawName)
            .replace(/['()]/g, (c) => '%' + c.charCodeAt(0).toString(16).toUpperCase())
            .replace(/\*/g, '%2A'); // mã hóa cả kí tự ! ~ * ' ( )
        // 1) Khai báo HttpHeaders đúng kiểu

        const headers = new HttpHeaders({
            'Content-Disposition': `attachment; filename*=UTF-8''${encoded}`,
        });

        // 2) Khởi tạo HttpRequest, body là raw File, headers là HttpHeaders
        const req = new HttpRequest<File>('PUT', presignedUrl, file, {
            headers,
            reportProgress: true,
            responseType: 'text', // S3/MinIO trả empty body
        });

        // 3) Gửi qua rawHttp.request để không có header mặc định nào khác
        return this.http.request(req).pipe(
            // Lọc chỉ những event cần: UploadProgress và Response
            filter((evt: HttpEvent<any>) => evt.type === HttpEventType.UploadProgress || evt.type === HttpEventType.Response),
            map((evt) => {
                if (evt.type === HttpEventType.UploadProgress && evt.total) {
                    // trả về phần trăm upload
                    return Math.round((100 * evt.loaded) / evt.total);
                }
                // khi đã xong → 100%
                return 100;
            }),
        );
    }
}
