<app-sub-header [items]="itemsHeader" [action]="actionHeader"></app-sub-header>

<ng-template #actionHeader>
    <p-button
        *ngIf="authService.isAdminOrPM()"
        (click)="addDistrictForm.reset(); isOpenAddDistrictModal = true; isEdit = false"
        label="Thêm Quận/ huyện"
        severity="success"
        size="small"
    />
</ng-template>

<div style="padding: 1rem 1rem 0 1rem">
    <app-table-common
        [tableId]="tableId"
        [columns]="columns"
        [data]="state.data"
        [loading]="state.isFetching"
        [filterTemplate]="filterTemplate"
        [funcDelete]="deleteSelectedDistrict.bind(this)"
        [rowSelectable]="isRowSelectable.bind(this)"
        [selectionMode]="authService.isAdminOrPM() ? 'multiple' : null"
        name="Danh sách Quận/ huyện"
    >
        <ng-template #filterTemplate>
            <tr>
                <th *ngIf="authService.isAdminOrPM()"></th>
                <th>
                    <app-filter-table
                        [tableId]="tableId"
                        field="name"
                        rsql="true"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'name',
                            dataKey: 'id',
                            fieldLabel: 'name',
                            param: 'name',
                            url: '/auth/api/district/search',
                        }"
                        placeholder="Tên Quận/ Huyện"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'code']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="code"
                        rsql="true"
                        type="text"
                        placeholder="Mã Quận/ Huyện"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'areaName']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="areaName"
                        rsql="true"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'name',
                            fieldLabel: 'name',
                            param: 'name',
                            url: '/auth/api/area/search',
                        }"
                        placeholder="Tỉnh/ Thành phố"
                    ></app-filter-table>
                </th>
                <th *ngIf="authService.isAdminOrPM()">
                    <ng-template #actionDistrict let-rowData>
                        <p-button (click)="openModalEditDistrict(rowData)" icon="pi pi-pencil" label=""></p-button>
                    </ng-template>
                </th>
            </tr>
        </ng-template>
    </app-table-common>
</div>

<p-dialog
    class="custom-dialog"
    [header]="isEdit ? 'Sửa quận/ huyện' : 'Thêm quận/ huyện'"
    [modal]="true"
    [(visible)]="isOpenAddDistrictModal"
    [style]="{ width: '45rem', top: '' }"
>
    <form #formElement="ngForm" [formGroup]="addDistrictForm" (ngSubmit)="isEdit ? saveDistrict() : addDistrict()">
        <div class="tw-mb-5">
            <div class="tw-mb-1">Tên <span class="tw-text-red-600">(*)</span>:</div>
            <input
                placeholder="Nhập tên quận/ huyện"
                formControlName="name"
                style="width: 100%"
                pInputText
                id="name"
                class="flex-auto"
                autocomplete="off"
            />
            <app-input-validate [control]="addDistrictForm.get('name')" fieldName="tên quận/ huyện" />
        </div>
        <div class="tw-mb-5">
            <div class="tw-mb-1">Mã <span class="tw-text-red-600">(*)</span>:</div>
            <input
                placeholder="Nhập mã quận/ huyện"
                formControlName="code"
                style="width: 100%"
                pInputText
                id="code"
                class="flex-auto"
                autocomplete="off"
            />
            <app-input-validate [control]="addDistrictForm.get('code')" fieldName="mã quận/ huyện" />
        </div>
        <div class="tw-mb-5" *ngIf="isOpenAddDistrictModal">
            <div class="tw-mb-1">Tỉnh/ thành phố <span class="tw-text-red-600">(*)</span>:</div>
            <app-filter-table
                field="name"
                rsql="true"
                type="select-one"
                [configSelect]="{
                    fieldValue: 'id',
                    fieldLabel: 'name',
                    param: 'id',
                    url: '/auth/api/area/search',
                    size: 100,
                    rsql: true,
                }"
                [initValue]="addDistrictForm.get('areaId').value"
                (onChange)="onChangeAreaForm($event)"
                placeholder="Tỉnh/ thành phố"
                #filerArea
            ></app-filter-table>
            <app-input-validate [control]="addDistrictForm.get('areaId')" fieldName="tỉnh/ thành phố" />
        </div>
        <div class="flex justify-content-end gap-2">
            <p-button label="Lưu" severity="success" type="submit" />
            <p-button label="Đóng" severity="secondary" (click)="isOpenAddDistrictModal = false" />
        </div>
    </form>
</p-dialog>
