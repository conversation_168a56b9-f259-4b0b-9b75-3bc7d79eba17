import { Injectable } from '@angular/core';
import { BehaviorSubject, EMPTY, finalize, Observable } from 'rxjs';

export interface TabFormActions<T> {
    saveApi: (data: T) => Observable<any>;
    submitApi?: (data: T) => Observable<any>;
    approveApi?: (data: T) => Observable<any>;
}

@Injectable()
export class FormActionService<T = any> {
    private _isSaving = new BehaviorSubject<boolean>(false);
    private _isSubmitting = new BehaviorSubject<boolean>(false);
    private _isApproving = new BehaviorSubject<boolean>(false);

    readonly isSaving$ = this._isSaving.asObservable();
    readonly isSubmitting$ = this._isSubmitting.asObservable();
    readonly isApproving$ = this._isApproving.asObservable();

    constructor() {}

    initialize(actions: TabFormActions<T>) {
        this.actions = actions;
    }

    private actions!: TabFormActions<T>;

    save(data: T): Observable<any> {
        if (!this.actions.saveApi) return EMPTY;
        this._isSaving.next(true);
        return this.actions.saveApi(data).pipe(finalize(() => this._isSaving.next(false)));
    }

    submit(data: T): Observable<any> {
        if (!this.actions.submitApi) return EMPTY;
        this._isSubmitting.next(true);
        return this.actions.submitApi!(data).pipe(finalize(() => this._isSubmitting.next(false)));
    }

    approve(data: T): Observable<any> {
        if (!this.actions.approveApi) return EMPTY;
        this._isApproving.next(true);
        return this.actions.approveApi!(data).pipe(finalize(() => this._isApproving.next(false)));
    }
}
