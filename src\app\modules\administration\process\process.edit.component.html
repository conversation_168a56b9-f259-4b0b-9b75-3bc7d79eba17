<style>
    :host ::ng-deep .p-dropdown {
        width: 100% !important;
    }
    #canvasContainer {
        position: relative;
    }
    #treeCanvas {
        border: 1px solid #ccc;
    }
    :host ::ng-deep .node {
        position: absolute;
        padding: 10px 20px;
        background-color: #87cefa;
        border-radius: 5px;
        cursor: pointer;
        text-align: center;
        user-select: none;
        transition:
            top 0.2s cubic-bezier(0.25, 0.8, 0.25, 1),
            left 0.2s cubic-bezier(0.25, 0.8, 0.25, 1);
    }
    :host ::ng-deep .select {
        border: 2px solid greenyellow;
        box-sizing: border-box;
    }
</style>

<app-sub-header [items]="itemsHeader" [action]="actionHeader"></app-sub-header>
<ng-template #actionHeader>
    <p-button label="Lưu" severity="success" (click)="onSave()" [disabled]="oldProcess?.active" />
    <p-button label="Hủy" routerLink="/administration/process" severity="secondary" />
</ng-template>
<div style="padding: 1rem 1rem 0 1rem">
    <app-form #formCreate formId="formSupplier" [formGroup]="formGroup" layout="vertical" (onSubmit)="onSubmit($event)">
        <p-panel header="Thông tin chung" [toggleable]="true">
            <div class="tw-grid lg:tw-grid-cols-2 tw-grid-cols-1 tw-gap-4">
                <app-form-item label="Tên">
                    <input type="text" class="tw-w-full" pInputText formControlName="name" />
                </app-form-item>

                <app-form-item label="Mô tả">
                    <input type="text" class="tw-w-full" pInputText formControlName="description" />
                </app-form-item>

                <app-form-item label="Key">
                    <p-dropdown class="tw-w-full" formControlName="keyEntity" [options]="allKeyEntity" />
                </app-form-item>
            </div>
        </p-panel>
        <br />
        <p-panel header="Thông tin chi tiết" [toggleable]="true">
            <div>
                <div class="tw-m-4 tw-bg-white tw-rounded">
                    <app-form-item layout="horizontal" label="Trạng thái đầu" labelCol="tw-col-span-2" wrapperCol="tw-col-span-2">
                        <p-dropdown
                            [options]="optionState"
                            optionLabel="name"
                            class="tw-w-52"
                            [disabled]="!formGroup.get('keyEntity').value"
                            (onChange)="changeStartNode($event)"
                            formControlName="rootState"
                            dataKey="id"
                        />
                    </app-form-item>
                </div>
                <div class="mt-4 tw-flex tw-flex-col tw-gap-4 md:tw-flex-row">
                    <div id="canvasContainer">
                        <canvas id="treeCanvas" #treeCanvas height="600" width="800" class="tw-bg-slate-100 tw-rounded-md"></canvas>
                    </div>
                    <div class="tw-bg-slate-100 tw-rounded-md tw-flex-grow tw-flex-shrink tw-p-8" style="width: 300px; border: 1px solid #ccc">
                        <div *ngIf="selectedNode" class="tw-flex tw-flex-col">
                            <div class="tw-p-4 tw-bg-white tw-rounded-md">
                                <p><b>Trạng thái: </b>{{ selectedNode.state.name }}</p>
                                <app-form-item label="Mô tả">
                                    <input type="text" class="tw-w-full" pInputText formControlName="descriptionProcessDetail" />
                                </app-form-item>
                                <br />
                                <div class="tw-flex tw-gap-2">
                                    <p-button size="small" severity="success" (click)="handleSaveDes()">Lưu</p-button>
                                    <p-button size="small" severity="danger" (click)="handleDeleteNode()">Xóa</p-button>
                                </div>
                            </div>
                            <div class="tw-bg-white tw-rounded-md tw-p-4 tw-mt-6">
                                <app-form-item label="Trạng thái tiếp theo">
                                    <p-dropdown
                                        [options]="optionNextState"
                                        optionLabel="name"
                                        class="tw-w-full"
                                        (onChange)="changeNextState($event)"
                                        formControlName="nextState"
                                    />
                                </app-form-item>
                                <br />
                                <app-form-item label="Mô tả chuyển đổi trạng thái tiếp">
                                    <input
                                        type="text"
                                        class="tw-w-full"
                                        pInputText
                                        formControlName="nextDescriptionProcessDetail"
                                        [disabled]="!formGroup.get('nextState').value"
                                    />
                                </app-form-item>
                                <br />
                                <div class="tw-flex tw-gap-2">
                                    <p-button size="small" severity="success" (click)="handleAddNode()" [disabled]="!formGroup.get('nextState').value"
                                        >Thêm</p-button
                                    >
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </p-panel>
    </app-form>
</div>
