import { SideBarItem } from '../sidebar.menu.component';

export const SIDE_BAR_PMS: SideBarItem[] = [
    {
        label: '<PERSON><PERSON> sơ sản phẩm',
        icon: 'pi pi-fw pi-list',
        items: [
            {
                label: 'Dòng sản phẩm',
                routerLink: '/pms/product-line',
                authorize: [
                    'ROLE_SYSTEM_ADMIN',
                    'product_line_view',
                    // 'product_line_create',
                    // 'product_line_update',
                    // 'product_line_delete',
                    // 'product_detail_view',
                    // 'product_compare',
                    // 'product_history',
                ],
            },
            {
                label: '<PERSON><PERSON> sơ sản phẩm',
                routerLink: '/pms/product-file',
                authorize: [
                    'ROLE_SYSTEM_ADMIN',
                    'version_view',
                    'version_detail_view',
                    'version_create',
                    'version_update',
                    'version_history',
                    'version_update_history',
                    'version_compare',
                    'version_submit',
                    'version_approve',
                    'version_transfer',
                    'version_pilot',
                    'version_mp',
                    'version_delete',
                ],
            },
            {
                label: '<PERSON><PERSON><PERSON> mềm hỗ trợ sản xuất',
                routerLink: '/pms/production-software',
                authorize: [
                    'ROLE_SYSTEM_ADMIN',
                    'software_view',
                    'software_create',
                    'software_update',
                    'software_delete',
                    'software_history',
                    'software_delete',
                    'software_applied_profiles',
                ],
            },
        ],
    },
];
