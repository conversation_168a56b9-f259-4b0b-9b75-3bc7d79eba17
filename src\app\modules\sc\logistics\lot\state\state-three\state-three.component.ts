import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PanelModule } from 'primeng/panel';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { TableModule } from 'primeng/table';
import { FormCustomModule } from '../../../../../../shared/form-module/form.custom.module';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { CalendarModule } from 'primeng/calendar';
import { DropdownModule } from 'primeng/dropdown';
import { ButtonModule } from 'primeng/button';
import { Lot, LotHistory } from '../../../../../../models/interface/sc';
import { DialogModule } from 'primeng/dialog';
import { ButtonGroupFileComponent } from '../../../../../../shared/components/button-group-file/button-group-file.component';
import { LotService } from 'src/app/services/sc/lot/lot.service';
import { LotHistoryService } from 'src/app/services/sc/lot/lot-history.service';
import { AttachmentComponent } from 'src/app/shared/components/attachment/attachment.component';
import { FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { ApiResponse, User } from 'src/app/models/interface';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { LOT_HISTORY_THREE } from 'src/app/models/constant/sc';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';

@Component({
    selector: 'app-lot-state-three',
    standalone: true,
    templateUrl: './state-three.component.html',
    styleUrls: ['./state-three.component.scss'],
    imports: [
        CommonModule,
        PanelModule,
        InputTextModule,
        InputTextareaModule,
        TableModule,
        TableCommonModule,
        FormCustomModule,
        ReactiveFormsModule,
        OverlayPanelModule,
        CalendarModule,
        FormCustomModule,
        DropdownModule,
        ButtonModule,
        DialogModule,
        ButtonGroupFileComponent,
        AttachmentComponent,
        HasAnyAuthorityDirective,
    ],
    providers: [LotService, LotHistoryService],
})
export class StateThreeComponent implements OnInit, OnChanges {
    @Input() lot: Lot;
    @Input() receivers: User[];

    lastLotHistory: LotHistory;
    // Form group
    formGroup: FormGroup;
    visibleNew: boolean = false;
    visibleHistory: boolean = false;

    @ViewChild('form', { static: false }) form: FormComponent;
    @ViewChild('formSubmit', { static: false }) formSubmit: FormComponent;

    // table history
    lotHistoryList: LotHistory[] = [];
    isLoadingList: boolean = false;

    //
    visibleSubmit: boolean = false;
    formGroupSubmit: FormGroup;
    @Output('onComplete') onComplete: EventEmitter<Lot> = new EventEmitter();

    constructor(
        private fb: FormBuilder,
        private lotHistoryService: LotHistoryService,
        private loadingService: LoadingService,
        private alertService: AlertService,
        private lotService: LotService,
    ) {}
    ngOnChanges(changes: SimpleChanges): void {
        if (changes['lot'] && !changes['lot'].previousValue && changes['lot'].currentValue) {
            this.loadingService.show();
            this.lotHistoryService.getLast(this.lot.id, LOT_HISTORY_THREE).subscribe({
                next: (res) => {
                    this.lastLotHistory = res;
                    this.loadingService.hide();
                },
                error: () => {
                    this.loadingService.hide();
                },
            });
        }
    }

    ngOnInit() {
        this.formGroupSubmit = new FormGroupCustom(this.fb, {
            receiverIds: [null, Validators.required],
            content: [null],
        });
    }

    showPopupCreateHis() {
        this.visibleNew = true;
        this.formGroup = new FormGroupCustom(this.fb, {
            lotId: [this.lot.id],
            boId: [this.lot.boId],
            logisticId: [this.lot.salesAgentId],
            expectedDeliveryDateCustom: [],
            expectedLeaveDateCustom: [],
            expectedPortDateCustom: [],
            expectedWarehouseDateCustom: [],
            note: [],
            type: [LOT_HISTORY_THREE],
            attachmentIds: [],
            attachments: [],
        });
    }

    handleUploadFile(files: File[]) {
        this.loadingService.show();
        this.lotHistoryService.importFile(files).subscribe({
            next: (res: ApiResponse) => {
                const itemValue = this.formGroup.getRawValue() as LotHistory;
                const attachmentIds = itemValue?.attachmentIds ?? [];
                const attachments = itemValue?.attachments ?? [];
                if (res.code === 1) {
                    // Nối mảng cũ và mảng mới
                    const newAttachmentIds = [...attachmentIds, ...(res.data['attachmentIds'] || [])];
                    const newAttachments = [...attachments, ...(res.data['attachments'] || [])];
                    this.formGroup.patchValue({
                        attachmentIds: newAttachmentIds,
                        attachments: newAttachments,
                    });
                } else {
                    this.alertService.error('Có lỗi xảy ra khi lưu file');
                    this.formGroup.patchValue({ attachmentIds: attachmentIds, attachments: attachments });
                }
                this.loadingService.hide();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    onSubmitHis(value: LotHistory) {
        value.expectedDeliveryDate = value.expectedDeliveryDateCustom ? value.expectedDeliveryDateCustom.getTime() : this.lastLotHistory?.expectedDeliveryDate;
        value.expectedLeaveDate = value.expectedLeaveDateCustom ? value.expectedLeaveDateCustom.getTime() : this.lastLotHistory?.expectedLeaveDate;
        value.expectedPortDate = value.expectedPortDateCustom ? value.expectedPortDateCustom.getTime() : this.lastLotHistory?.expectedPortDate;
        value.expectedWarehouseDate = value.expectedWarehouseDateCustom
            ? value.expectedWarehouseDateCustom.getTime()
            : this.lastLotHistory?.expectedWarehouseDate;
        this.loadingService.show();
        this.lotHistoryService.create(value).subscribe({
            next: (res) => {
                this.loadingService.hide();
                this.alertService.success('Thành công');
                this.visibleNew = false;
                this.lastLotHistory = res.body;
                this.onSubmitState();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    getTotalHistory() {
        this.visibleHistory = true;
        this.isLoadingList = true;
        this.lotHistoryService.getPage(`query=lotId==${this.lot.id};type==${LOT_HISTORY_THREE}&page=0&size=1000&sort=created,desc`).subscribe({
            next: (res) => {
                this.lotHistoryList = res.body;
                this.isLoadingList = false;
            },
            error: () => {
                this.isLoadingList = false;
            },
        });
    }

    onSubmitState() {
        if (!this.formGroup.valid) return;
        this.lotService.stateDeliveryUpdate(this.lot).subscribe({
            next: (res) => {
                this.onComplete.emit(res);
            },
            error: () => {},
        });
    }

    sendNotification(value: { receiverIds: number[]; content: string }) {
        this.loadingService.show();
        this.lotService.sendNotification(this.lot.id, value).subscribe({
            next: () => {
                this.visibleSubmit = false;
                this.loadingService.hide();
                this.alertService.success('Thành công');
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    handleChangeReceivers(event) {
        if (event.value !== null && event.objects !== null && event.objects.length > 0) {
            const ids = event.objects.map((obj) => obj.id);
            this.formGroupSubmit.patchValue({
                receiverIds: ids,
            });
        } else {
            this.formGroupSubmit.patchValue({
                receiverIds: null,
            });
        }
    }
}
