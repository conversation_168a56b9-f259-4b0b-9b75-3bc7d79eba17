import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { ContractRouting } from './contract/contract.routing';
import { ApproveRouting } from './approve/approve.routing';
import { TemplateRouting } from './template/template.routing';
import { DashBoardRouting } from './dashboard/dashboard.routing';
import { AcceptanceRouting } from './report/acceptance/acceptance.routing';
import { GeneralRouting } from './report/general/general.routing';
import { NotificationRouting } from './notification/notification.routing';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { canAuthorize } from 'src/app/core/auth/auth.guard';
import { MaintenanceRouting } from './maintenance/maintenance.routing';

@NgModule({
    imports: [
        RouterModule.forChild([
            {
                path: '',
                redirectTo: 'dashboard', // Redirect from /sqc to /sqc/dashboard
                pathMatch: 'full', // Ensures the redirection happens only if the full URL is /sqc
            },
            {
                path: 'user',
                canActivate: [canAuthorize],
                data: { authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'ROLE_QC_SUBPM'] },
                children: [
                    {
                        path: '',
                        canActivate: [canAuthorize],
                        title: 'Danh sách người dùng',
                        loadComponent: () => import('./user/user.component').then((c) => c.UserComponent),
                        data: { mode: 'view' },
                    },
                    {
                        path: ':id',
                        canActivate: [canAuthorize],
                        title: 'Xem chi tiết người dùng',
                        loadComponent: () => import('./user/user-detail.component').then((c) => c.UserDetailComponent),
                        data: { mode: 'view' },
                    },
                    {
                        path: ':id/edit',
                        canActivate: [canAuthorize],
                        title: 'Sửa thông tin người dùng',
                        loadComponent: () => import('./user/user-detail.component').then((c) => c.UserDetailComponent),
                        data: { mode: 'edit' },
                    },
                    {
                        path: 'create',
                        canActivate: [canAuthorize],
                        title: 'Tạo mới người dùng',
                        loadComponent: () => import('./user/user-detail.component').then((c) => c.UserDetailComponent),
                        data: { mode: 'create' },
                    },
                ],
            },
            {
                path: 'area',
                title: 'Tỉnh/Tp',
                data: { authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'ROLE_QC_SUBPM'] },
                canActivate: [canAuthorize],
                loadComponent: () => import('./area/area.component').then((c) => c.AreaComponent),
            },
            {
                path: 'district',
                title: 'Quận/ huyện',
                data: { authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'ROLE_QC_SUBPM'] },
                canActivate: [canAuthorize],
                loadComponent: () => import('./district/district.component').then((c) => c.DistrictComponent),
            },
            DashBoardRouting,
            AcceptanceRouting,
            GeneralRouting,
            ContractRouting,
            ApproveRouting,
            TemplateRouting,
            NotificationRouting,
            MaintenanceRouting,
        ]),
        ConfirmDialogModule,
    ],
    exports: [RouterModule],
})
export class SmartQcRoutingModule {}
