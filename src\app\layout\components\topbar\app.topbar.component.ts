import { Component, ElementRef, ViewChild, AfterViewInit, OnDestroy, OnInit } from '@angular/core';
import { AuthService } from 'src/app/core/auth/auth.service';
import { Router, RouterModule } from '@angular/router';
import { LayoutService } from '../../service/app.layout.service';
import Common from '../../../utils/common';
import { NotificationService } from '../../../services/smart-qc/notification/notification.service';
import { take } from 'rxjs';
import { OverlayPanel, OverlayPanelModule } from 'primeng/overlaypanel';
import { NotificationDTO } from '../../../models/notification/NotificationDTO';
import { AngularFireMessaging } from '@angular/fire/compat/messaging';
import { environment } from '../../../../environments/environment';
import { CardModule } from 'primeng/card';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { CommonModule } from '@angular/common';
import { Module } from 'src/app/models/constant';
import { User } from 'src/app/models/interface';

@Component({
    selector: 'app-topbar',
    templateUrl: './app.topbar.component.html',
    styleUrls: ['./app.topbar.style.scss'],
    standalone: true,
    imports: [OverlayPanelModule, CardModule, HasAnyAuthorityDirective, CommonModule, RouterModule],
})
export class AppTopBarComponent implements OnInit, AfterViewInit, OnDestroy {
    // Active notification

    moduleActiveNotification: string[] = ['SMART_QC'];
    isActiveNotification: boolean = false;

    fetchNewNotificationInterval: NodeJS.Timeout;
    fetchNewNumberNotificationInterval: NodeJS.Timeout;
    notifications: NotificationDTO[] = [];
    notificationNewNumber: number = 0;
    mode: 'realtime' | 'fetch';
    firstFetchNotification: boolean = true;
    firstFetchNumberNewNotification: boolean = true;
    autoNotification: boolean = true;

    user: User = { email: '' };
    module: Module;
    environmentState = environment;
    @ViewChild('menubutton') menuButton!: ElementRef;

    @ViewChild('topbarmenubutton') topbarMenuButton!: ElementRef;

    @ViewChild('topbarmenu') menu!: ElementRef;

    @ViewChild('notificationTemplate') notificationPanel: OverlayPanel;

    domain = window.location.origin;
    isDomainSmartQc = false;

    constructor(
        public layoutService: LayoutService,
        public auth: AuthService,
        private router: Router,
        private notificationService: NotificationService,
        private authService: AuthService,
        private angularFireMessaging: AngularFireMessaging,
    ) {
        this.auth.userObserver.subscribe((data) => {
            this.user = data;
        });
        this.checkNotification();
        this.autoNotification = environment.autoNotification;
        this.layoutService.module.subscribe((data) => {
            this.module = data;
            if (this.module && this.moduleActiveNotification.includes(this.module.name)) {
                this.isActiveNotification = true;
            } else {
                this.isActiveNotification = false;
            }
        });
    }
    ngOnInit(): void {
        if (this.domain && this.domain.includes('smart-qc')) {
            this.isDomainSmartQc = true;
        } else {
            this.isDomainSmartQc = false;
        }
    }

    logout() {
        this.authService.logOut().subscribe({
            next: () => {
                let redirectURL = window.location.pathname;
                if (
                    redirectURL.includes('error') ||
                    redirectURL.includes('access') ||
                    redirectURL.includes('notfound')
                ) {
                    redirectURL = '';
                }
                this.router.navigate(['/login'], { queryParams: { redirectURL } });
                this.auth.invalidateLogin();
            },
            error: () => {},
        });
    }

    protected readonly Common = Common;

    fetchNewNumberNotification() {
        if (
            this.autoNotification &&
            document.visibilityState === 'visible' &&
            (this.mode === 'fetch' || this.firstFetchNotification)
        ) {
            this.getNewNumberNotification();
        }
    }

    getNewNumberNotification() {
        this.notificationService.fetchNewNumberNotification().subscribe({
            next: (res) => {
                this.notificationNewNumber = res.body as number;
                this.firstFetchNotification = false;
            },
            error: () => {},
            complete: () => {},
        });
    }

    fetchNewNotification() {
        if (
            this.autoNotification &&
            document.visibilityState === 'visible' &&
            this.notificationPanel.overlayVisible &&
            (this.mode === 'fetch' || this.firstFetchNumberNewNotification)
        ) {
            this.getNewNotification();
        }
    }

    getNewNotification() {
        const params = 'query=&page=0&size=5&sort=created,desc';
        this.notificationService.getPage(params).subscribe({
            next: (res) => {
                this.notifications = res.body;
                this.firstFetchNumberNewNotification = false;
            },
            error: () => {},
            complete: () => {},
        });
    }

    fetchNotificationDataOnClick() {
        if (this.notificationPanel.overlayVisible) {
            this.getNewNotification();
            this.getNewNumberNotification();
        }
    }

    ngAfterViewInit() {
        if (this.isActiveNotification) {
            this.fetchNewNumberNotification();
            this.fetchNewNumberNotificationInterval = setInterval(() => {
                this.fetchNewNumberNotification();
            }, 20000);

            this.fetchNewNotificationInterval = setInterval(() => {
                this.fetchNewNotification();
            }, 20000);
        }
    }

    ngOnDestroy() {
        // Dừng chạy fetchNotification khi component bị hủy
        if (this.fetchNewNumberNotificationInterval) {
            clearInterval(this.fetchNewNumberNotificationInterval);
        }

        if (this.fetchNewNotificationInterval) {
            clearInterval(this.fetchNewNotificationInterval);
        }
    }

    protected readonly String = String;

    navigateToApprove(notification: NotificationDTO) {
        if (notification.objectModel && notification.objectId) {
            if (
                notification.objectModel.includes('EmployeeSubmit') ||
                notification.objectModel.includes('SubPMApprove') ||
                notification.objectModel.includes('PMApprove')
            ) {
                // Chuyển tới màn hình chi tiết phê duyệt
                const params = {
                    from: 'notification',
                };
                if (this.authService.isAdminOrPM()) {
                    this.router.navigate(['/sqc/approve/' + notification.objectId], { queryParams: params });
                } else {
                    this.router.navigate(['/sqc/approve/sub/' + notification.objectId], { queryParams: params });
                }
                // Call API mask as Read
                if (notification.isRead === 0) {
                    const markAsReadDTO = {
                        ids: [notification.id],
                        read: true,
                    };
                    this.notificationService.markAsRead(markAsReadDTO).subscribe(() => {
                        // Set read status
                        notification.isRead = 1;
                        if (this.notificationNewNumber > 0) {
                            this.notificationNewNumber--;
                        }
                    });
                }
            }
        }
    }

    markAsReadAll() {}

    checkNotification() {
        if ('Notification' in window) {
            if (Notification.permission === 'granted') {
                this.mode = 'realtime';
                this.requestToken();
                //this.requestFirebaseToken();
            } else if (Notification.permission === 'denied') {
                //this.showNotificationPopup = true;
                this.mode = 'fetch';
            } else {
                this.mode = 'fetch';
                Notification.requestPermission()
                    .then((permission) => {
                        if (permission === 'granted') {
                            //this.requestFirebaseToken();
                            //console.error('Request token now!');
                            this.mode = 'realtime';
                            this.requestToken();
                        }
                    })
                    .catch(() => {
                        this.mode = 'fetch';
                    });
            }
        } else {
            this.mode = 'fetch';
        }
    }

    requestToken() {
        this.angularFireMessaging.requestToken.pipe(take(1)).subscribe({
            next: (token: string) => {
                const tokenDTO = {
                    token: token,
                };
                this.authService.saveFirebaseToken(tokenDTO).subscribe({
                    next: () => {
                        this.fetchNewNotification();
                        this.fetchNewNumberNotification();
                        this.subscribeFirebaseMessage();
                    },
                    error: () => {
                        this.mode = 'fetch';
                    },
                });
            },
            error: (err) => {
                console.error('Unable to get permission to notify!', err);
                this.mode = 'fetch';
            },
            complete: () => {},
        });
    }

    subscribeFirebaseMessage() {
        navigator.serviceWorker.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'NOTIFICATION_FIREBASE_RECEIVER') {
                this.notificationNewNumber++;
                this.notifications.unshift(event.data.data);
            }
        });
        this.angularFireMessaging.messages.subscribe((message) => {
            this.notificationNewNumber++;
            this.notifications.unshift(message.data);
        });
    }

    navigateToNotification() {
        this.router.navigate(['/sqc/notification']);
    }
}
