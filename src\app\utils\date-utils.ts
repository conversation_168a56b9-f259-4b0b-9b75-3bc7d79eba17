import {isDate} from "lodash";

export class DateUtils {
    static generateYearRange(minYear: number): { label: string; value: number }[] {
        const currentYear = new Date().getFullYear();
        const years = [];

        for (let year = currentYear; year >= minYear; year--) {
            years.push({ label: year.toString(), value: year });
        }

        return years;
    }

    static generateMonthRange(): { label: string; value: number }[] {
        const months = [];
        for (let i = 1; i <= 12; i++) {
            months.push({ label: `Tháng ${i}`, value: i });
        }

        return months;
    }

    static formatMilliseconds(milliseconds: number, format: string = 'DD/MM/YYYY HH:mm:ss'): string {
        const date = new Date(milliseconds);

        const options: { [key: string]: string } = {
            YYYY: date.getFullYear().toString(),
            MM: (date.getMonth() + 1).toString().padStart(2, '0'),
            DD: date.getDate().toString().padStart(2, '0'),
            HH: date.getHours().toString().padStart(2, '0'),
            mm: date.getMinutes().toString().padStart(2, '0'),
            ss: date.getSeconds().toString().padStart(2, '0'),
        };

        let formatted = format;
        for (const [key, value] of Object.entries(options)) {
            formatted = formatted.replace(key, value);
        }

        return formatted;
    }

    static formatDateWithPattern(date: number, pattern: string): string {
        if (date == null) {
            return '';
        }
        const leadingZero = (num: number) => (num < 10 ? '0' : '') + num;

        const d = new Date(date);
        const day = leadingZero(d.getDate());
        const month = leadingZero(d.getMonth() + 1);
        const year = d.getFullYear();
        const hours = leadingZero(d.getHours());
        const minutes = leadingZero(d.getMinutes());
        const seconds = leadingZero(d.getSeconds());

        if (pattern === 'dd/mm/yyyy') {
            return `${day}/${month}/${year}`;
        }
        return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
    }

    static formatDateWithPatternHCM(date: number, pattern: string): string {
        if (date == null) {
            return '';
        }

        const leadingZero = (num: number) => (num < 10 ? '0' : '') + num;

        const hcmDate = new Date(new Date(date).toLocaleString('en-US', { timeZone: 'Asia/Ho_Chi_Minh' }));

        const day = leadingZero(hcmDate.getDate());
        const month = leadingZero(hcmDate.getMonth() + 1); // getMonth() từ 0
        const year = hcmDate.getFullYear();
        const hours = leadingZero(hcmDate.getHours());
        const minutes = leadingZero(hcmDate.getMinutes());
        const seconds = leadingZero(hcmDate.getSeconds());

        if (pattern === 'dd/MM/yyyy') {
            return `${day}/${month}/${year}`;
        }

        return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
    }

    static convertToTimestampHCM(date: Date): number {
        const hcmDate = new Date(date.toLocaleString("en-US", { timeZone: "Asia/Ho_Chi_Minh" }));
        hcmDate.setHours(0, 0, 0, 0); // Đặt giờ về 00:00:00.000
        return hcmDate.getTime(); // Trả về timestamp (milliseconds)
    }

    static isEqualHourMinSecondDate(dateOne: Date, dateTwo: Date) {
        if (dateOne == null || dateTwo == null || !isDate(dateOne) || !isDate(dateTwo)) {
            return false;
        }
        return dateOne.getFullYear() === dateTwo.getFullYear() &&
            dateOne.getMonth() === dateTwo.getMonth() &&
            dateOne.getDate() === dateTwo.getDate()
    }
}
