import { Injectable } from '@angular/core';
import { FormGroup } from '@angular/forms';

@Injectable()
export class FormContextService {
    formGroup: FormGroup;
    labelCol: string = 'tw-col-span-4'; // C<PERSON>u hình mặc định cho labelCol
    wrapperCol: string = 'tw-col-span-8'; // Cấu hình mặc định cho wrapperCol
    validateTrigger: 'change' | 'touched' | 'submit' = 'submit'; // Mặc định validate trên touched
    layout: 'horizontal' | 'vertical' = 'horizontal';

    isSubmited: boolean = false;
    constructor() {}

    // Thiết lập cấu hình cho form
    setConfig(config: {
        formGroup: FormGroup;
        labelCol?: string;
        wrapperCol?: string;
        validateTrigger?: 'change' | 'touched' | 'submit';
        layout?: 'horizontal' | 'vertical';
    }) {
        this.formGroup = config.formGroup;

        if (config.labelCol) this.labelCol = config.labelCol;
        if (config.wrapperCol) this.wrapperCol = config.wrapperCol;
        if (config.validateTrigger) this.validateTrigger = config.validateTrigger;
        if (config.layout) this.layout = config.layout;
    }

    setSubmited(value: boolean) {
        this.isSubmited = value;
    }
}
