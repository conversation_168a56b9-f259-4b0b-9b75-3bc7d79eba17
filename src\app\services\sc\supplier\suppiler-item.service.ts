import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { ApiResponse } from 'src/app/models/interface';
import { SupplierItem } from 'src/app/models/interface/sc';

@Injectable({
    providedIn: 'root',
})
export class SupplierItemService extends BaseService<SupplierItem> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/supplier-items');
    }

    importCreate(file: File) {
        const formData: FormData = new FormData();
        formData.append('file', file);

        return this.http.post<ApiResponse>('/sc/api/supplier-items/import', formData);
    }
}
