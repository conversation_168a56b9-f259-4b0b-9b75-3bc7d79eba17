<div class="tables-container">
    <ng-container *ngFor="let group of groupedRows">
        <p-table
            [value]="group.items"
            class="section-table mb-4"
            [tableStyle]="{ 'table-layout': 'fixed', width: '100%' }"
        >
            <!-- SINGLE HEADER ROW WITH 5 COLUMNS -->
            <ng-template pTemplate="header">
                <tr>
                    <th>{{ group.name }}</th>
                    <th></th>
                    <th></th>
                </tr>
            </ng-template>

            <!-- BODY rows -->
            <ng-template pTemplate="body" let-row>
                <tr>
                    <td style="width: 33%;">{{ row.name }}</td>
                    <td class="tw-truncate tw-max-w-[150px]" style="width: 37%;" title="{{ row.note1 }}">
                        <a class="tw-cursor-pointer" (click)="downloadFile(row.path1, row.note1)">{{ row.note1 }}</a>
                    </td>
                    <td class="tw-truncate tw-max-w-[150px]" style="width: 33%;" title="{{ row.note2 }}">
                        <a class="tw-cursor-pointer" (click)="downloadFile(row.path2, row.note2)">{{ row.note2 }}</a>
                    </td>
                </tr>
            </ng-template>
        </p-table>
    </ng-container>
</div>
