import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { DashBoardPMSRouting } from './dashboard/dashboard.routing';
import { PSRouting } from './production-software/production-software-routing';
import { PLRouting } from './product-line/product-line-routing';
import { PFRouting } from './product-file/product-file-routing';

@NgModule({
    imports: [
        RouterModule.forChild([
            {
                path: '',
                redirectTo: 'dashboard',
                pathMatch: 'full',
            },

            DashBoardPMSRouting,
            PLRouting,
            PFRouting,
            PSRouting,
        ]),
    ],
    exports: [RouterModule],
})
export class PMSRoutingModule {}
