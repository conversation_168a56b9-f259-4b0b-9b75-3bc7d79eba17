<p-dialog
    [(visible)]="visible"
    [modal]="true"
    [closable]="false"
    [style]="{ width: '60vw' }"
    header="TRACK CHANGES"
>
    <p-table

        [value]="trackChanges"
        styleClass="p-datatable-gridlines"
    >
        <ng-template pTemplate="header">
            <tr>
                <th style="text-align: center">STT</th>
                <th style="text-align: center">Thao tác thay đổi</th>
                <th style="text-align: center"> Chi tiết cập nhật</th>
                <th style="text-align: center">Người thực hiện</th>
                <th style="text-align: center">Thời điểm thực hiện</th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-item let-i="rowIndex">
            <tr>
                <td style="text-align: center">{{ i + 1 }}</td>
                <td style="text-align: center">{{ getActionLabel(item.action) }}</td>
                <td style="text-align: center">{{ item.note }}</td>
                <td style="text-align: center">{{ item.createdBy }}</td>
                <td style="text-align: center">{{ item.created | date: 'hh:mm a dd/MM/yyyy' }}</td>
            </tr>
        </ng-template>
        <ng-template pTemplate="emptymessage">
            <tr>
                <!-- colspan bằng số cột để nội dung căn giữa -->
                <td [attr.colspan]="5" class="text-center tw-text-gray-500 tw-italic">Chưa có thay đổi</td>
            </tr>
        </ng-template>
    </p-table>

    <div class="tw-flex tw-justify-center tw-mt-4">
        <p-button label="Đóng" size="=large" (click)="handleClose()"></p-button>
    </div>
</p-dialog>
