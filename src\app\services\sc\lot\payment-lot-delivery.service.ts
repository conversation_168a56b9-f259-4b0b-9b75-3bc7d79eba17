import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { PaymentLotDelivery } from '../../../models/interface/sc';

@Injectable()
export class PaymentLotDeliveryService extends BaseService<PaymentLotDelivery> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/payment-lot-delivery');
    }
}
