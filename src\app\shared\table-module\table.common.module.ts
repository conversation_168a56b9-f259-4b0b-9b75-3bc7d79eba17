import { NgModule } from '@angular/core';
import { TableCommonComponent } from './table-common/table-common.component';
import { FilterTableComponent } from './filter-table/filter-table.component';
import { CustomFilterTableComponent } from 'src/app/shared/table-module/custom-filter-table/custom-filter-table.component';
import { PaginatorComponent } from './paginator/paginator.component';
import { CommonModule } from '@angular/common';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { CalendarModule } from 'primeng/calendar';
import { MultiSelectModule } from 'primeng/multiselect';
import { ButtonModule } from 'primeng/button';
import { OverlayModule } from 'primeng/overlay';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DialogModule } from 'primeng/dialog';
import { ColumnFilterDirective } from './filter.directive';
import { RouterModule } from '@angular/router';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { InputNumberModule } from 'primeng/inputnumber';
import { TableCommonService } from './table.common.service';
import { TableModule } from 'primeng/table';
import { AttributeAuthorityDirective } from '../directives/attribute-authority.directive';
import { RangeDateComponent } from './range-date/range-date.component';
import { DialogService } from 'primeng/dynamicdialog';
import { TableMultiComponent } from './table-multi/table-multi.component';
import { TabViewModule } from 'primeng/tabview';
import { TooltipModule } from 'primeng/tooltip';
@NgModule({
    declarations: [
        TableCommonComponent,
        FilterTableComponent,
        CustomFilterTableComponent,
        RangeDateComponent,
        PaginatorComponent,
        ColumnFilterDirective,
        TableMultiComponent,
    ],
    imports: [
        CommonModule,
        InputTextModule,
        InputNumberModule,
        DropdownModule,
        CalendarModule,
        MultiSelectModule,
        ButtonModule,
        OverlayModule,
        DialogModule,
        ReactiveFormsModule,
        RouterModule,
        OverlayPanelModule,
        AttributeAuthorityDirective,
        FormsModule,
        TableModule,
        TabViewModule,
        TooltipModule,
    ],
    exports: [
        TableCommonComponent,
        FilterTableComponent,
        CustomFilterTableComponent,
        RangeDateComponent,
        PaginatorComponent,
        ColumnFilterDirective,
        TableMultiComponent,
    ],
    providers: [TableCommonService, DialogService],
})
export class TableCommonModule {}
