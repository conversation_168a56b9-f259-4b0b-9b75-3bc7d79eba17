<app-sub-header
    [items]="[{ label: '<PERSON>uản lý thông tin mua hàng' }, { label: '<PERSON><PERSON><PERSON> cáo thống kê' }, { label: 'Tổng hợp bảng kê' }]"
    [action]="actionHeader"
></app-sub-header>

<ng-template #actionHeader>
    <app-popup
        header="Xuất file tổng hợp bảng kê"
        severity="success"
        label="Xuất báo cáo"
        typePopup="download"
        (onSubmit)="handleExportReport($event)"
    ></app-popup>
</ng-template>

<div style="padding: 1rem 1rem 0 1rem">
    <app-form [formGroup]="formGroup" layout="vertical">
        <div class="tw-grid lg:tw-grid-cols-4 md:tw-grid-cols-2 sm:tw-grid-cols-1 tw-bg-white tw-p-4 tw-gap-6 tw-rounded-md">
            <div class="tw-flex tw-flex-col tw-space-y-3">
                <div class="tw-font-bold">Thời gian báo cáo</div>
                <div class="p-inputgroup tw-space-x-4">
                    <p-calendar
                        [showButtonBar]="true"
                        formControlName="startTimeCustom"
                        [showIcon]="true"
                        [showOnFocus]="true"
                        dateFormat="dd/mm/yy"
                        inputId="buttondisplay"
                        placeholder="Từ ngày"
                        appendTo="body"
                    />
                    <p-calendar
                        [showButtonBar]="true"
                        formControlName="endTimeCustom"
                        [showIcon]="true"
                        [showOnFocus]="true"
                        dateFormat="dd/mm/yy"
                        inputId="buttondisplay"
                        placeholder="Đến ngày"
                        appendTo="body"
                    />
                </div>
            </div>
            <app-form-item label="Nhà sản xuất">
                <app-filter-table
                    type="select-one"
                    [configSelect]="{
                        fieldValue: 'id',
                        fieldLabel: 'alias',
                        rsql: true,
                        url: '/sc/api/erp-manufacturer/search',
                        sort: 'id,asc',
                        paramForm: 'alias',
                    }"
                    formControlName="manufacturerId"
                ></app-filter-table>
            </app-form-item>
            <app-form-item label="Nhà cung cấp">
                <app-filter-table
                    type="select"
                    [configSelect]="{
                        fieldValue: 'id',
                        fieldLabel: 'shortName',
                        rsql: true,
                        url: '/sc/api/supplier/search',
                        paramForm: 'shortName',
                    }"
                    formControlName="supplierIds"
                ></app-filter-table>
            </app-form-item>
            <app-form-item label="Mã VNPT MAN PN">
                <app-filter-table
                    type="select"
                    [configSelect]="{
                        fieldValue: 'internalReference',
                        fieldLabel: 'internalReference',
                        rsql: true,
                        url: '/sc/api/inventory-product/search',
                        paramForm: 'internalReference',
                    }"
                    formControlName="vnptMan"
                ></app-filter-table>
            </app-form-item>
            <app-form-item label="Mã nhà sản xuất">
                <app-filter-table
                    type="select"
                    [configSelect]="{
                        fieldValue: 'manufacturerCode',
                        fieldLabel: 'manufacturerCode',
                        rsql: true,
                        url: '/sc/api/po-boq/search',
                        paramForm: 'alias',
                        sort: 'id,asc',
                        size: 2000,
                        additionalCondition: 'manufacturerCode!=null',
                    }"
                    formControlName="manufacturerCodes"
                ></app-filter-table>
            </app-form-item>
            <app-form-item label="Số PO/hợp đồng">
                <app-filter-table
                    type="select-one"
                    [configSelect]="{
                        fieldValue: 'id',
                        fieldLabel: 'orderNo',
                        rsql: true,
                        url: '/sc/api/po/search',
                        paramForm: 'id',
                        body: {
                            state: [0, 1, 2, 3],
                        },
                    }"
                    formControlName="poId"
                ></app-filter-table>
            </app-form-item>
            <app-form-item label="Mã kế toán">
                <input type="text" class="tw-w-full" formControlName="accountingCode" pInputText />
            </app-form-item>

            <div class="flex tw-items-end">
                <p-button icon="pi pi-search" (click)="search()"></p-button>
            </div>
        </div>
    </app-form>
    <br />
    <div class="tw-flex tw-justify-end tw-mb-4 tw-mx-4 tw-gap-4">
        <p-button icon="pi pi-filter" [outlined]="true" [size]="'small'" (click)="op.toggle($event)"></p-button>
    </div>
    <p-table
        [value]="tableData"
        [tableStyle]="{ 'min-width': '50rem' }"
        styleClass="p-datatable-gridlines"
        dataKey="poId"
        [loading]="state.isFetching"
        [scrollable]="false"
        scrollHeight="700px"
    >
        <ng-template pTemplate="header">
            <tr>
                <th *ngIf="!columns[0].hide">STT</th>
                <th *ngIf="!columns[1].hide">Số PO/Hợp đồng</th>
                <th *ngIf="!columns[2].hide">Ngày đặt hàng</th>
                <th *ngIf="!columns[3].hide">Nhà cung cấp</th>
                <th *ngIf="!columns[4].hide">Mã kế toán</th>
                <th *ngIf="!columns[5].hide">Mã VNPT MAN PN</th>
                <th *ngIf="!columns[6].hide">Mã MAN PN</th>
                <th *ngIf="!columns[7].hide">Nhà sản xuất</th>
                <th *ngIf="!columns[8].hide">Mô tả</th>
                <th *ngIf="!columns[9].hide">SPQ</th>
                <th *ngIf="!columns[10].hide">MOQ</th>
                <th *ngIf="!columns[11].hide">Tổng số lượng yêu cầu</th>
                <th *ngIf="!columns[12].hide">Tồn khả dụng</th>
                <th *ngIf="!columns[13].hide">Đơn giá</th>
                <th *ngIf="!columns[14].hide" colspan="2">Thông tin đặt hàng</th>
                <th *ngIf="!columns[16].hide" colspan="2">Thông tin hàng đã về</th>
                <th *ngIf="!columns[18].hide" colspan="2">Thông tin hàng chưa về</th>
                <th *ngIf="!columns[20].hide" [attr.colspan]="dateColumns.length > 0 ? dateColumns.length : 1">Ngày hàng về</th>
            </tr>
            <tr>
                <th *ngIf="!columns[0].hide"></th>
                <th *ngIf="!columns[1].hide"></th>
                <th *ngIf="!columns[2].hide"></th>
                <th *ngIf="!columns[3].hide"></th>
                <th *ngIf="!columns[4].hide"></th>
                <th *ngIf="!columns[5].hide"></th>
                <th *ngIf="!columns[6].hide"></th>
                <th *ngIf="!columns[7].hide"></th>
                <th *ngIf="!columns[8].hide"></th>
                <th *ngIf="!columns[9].hide"></th>
                <th *ngIf="!columns[10].hide"></th>
                <th *ngIf="!columns[11].hide"></th>
                <th *ngIf="!columns[12].hide"></th>
                <th *ngIf="!columns[13].hide"></th>
                <th *ngIf="!columns[14].hide">Số lượng</th>
                <th *ngIf="!columns[15].hide">Thành tiền</th>
                <th *ngIf="!columns[16].hide">Số lượng</th>
                <th *ngIf="!columns[17].hide">Thành tiền</th>
                <th *ngIf="!columns[18].hide">Số lượng</th>
                <th *ngIf="!columns[19].hide">Thành tiền</th>
                <ng-container *ngIf="dateColumns.length > 0 && !columns[20].hide; else noDate">
                    <th *ngFor="let date of dateColumns">{{ date | date: 'dd/MM/yyyy' }}</th>
                </ng-container>
                <ng-template #noDate>
                    <th *ngIf="!columns[20].hide"></th>
                </ng-template>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rowData let-rowIndex="rowIndex">
            <tr>
                <td *ngIf="!columns[0].hide && rowData.showPoInfo" [attr.rowspan]="rowData.rowspanPoInfo">{{ rowData.poIndex }}</td>
                <td *ngIf="!columns[1].hide && rowData.showPoInfo" [attr.rowspan]="rowData.rowspanPoInfo">
                    <a [href]="'/sc/po/' + rowData.poId" target="_blank" rel="noopener noreferrer">{{ rowData.orderNo }}</a>
                </td>
                <td *ngIf="!columns[2].hide && rowData.showPoInfo" [attr.rowspan]="rowData.rowspanPoInfo">{{ rowData.orderDate | date: 'dd/MM/yyyy' }}</td>
                <td *ngIf="!columns[3].hide && rowData.showPoInfo" [attr.rowspan]="rowData.rowspanPoInfo">
                    <a [href]="'/sc/supplier-infor/' + rowData.supplierId" target="_blank" rel="noopener noreferrer">{{ rowData.supplierShortName }}</a>
                </td>
                <td *ngIf="!columns[4].hide && rowData.showLevel3" [attr.rowspan]="rowData.rowspanLevel3">{{ rowData.accountingCode }}</td>
                <td *ngIf="!columns[5].hide">{{ rowData.vnptMan }}</td>
                <td *ngIf="!columns[6].hide">{{ rowData.manPn }}</td>
                <td *ngIf="!columns[7].hide">{{ rowData.manufacturer }}</td>
                <td *ngIf="!columns[8].hide">{{ rowData.description }}</td>
                <td *ngIf="!columns[9].hide">{{ rowData.spq | number }}</td>
                <td *ngIf="!columns[10].hide">{{ rowData.moq | number }}</td>
                <td *ngIf="!columns[11].hide && rowData.showLevel3" [attr.rowspan]="rowData.rowspanLevel3">
                    {{ rowData.orderQuantity | number: '1.0-0' }}
                </td>
                <td *ngIf="!columns[12].hide && rowData.showLevel3" [attr.rowspan]="rowData.rowspanLevel3">
                    {{ rowData.availableQuantity | number: '1.0-0' }}
                </td>
                <td *ngIf="!columns[13].hide && rowData.showLevel3" [attr.rowspan]="rowData.rowspanLevel3">
                    {{
                        rowData.orderPrice
                            | currency: (rowData.unitPrice === 'USD' ? 'USD' : 'VND') : 'symbol' : (rowData.unitPrice === 'USD' ? '1.2-2' : '1.0-0')
                    }}
                </td>
                <td *ngIf="!columns[14].hide && rowData.showLevel3" [attr.rowspan]="rowData.rowspanLevel3">
                    {{ rowData.orderQuantity | number: '1.0-0' }}
                </td>
                <td *ngIf="!columns[15].hide && rowData.showLevel3" [attr.rowspan]="rowData.rowspanLevel3">
                    {{
                        rowData.orderAmount
                            | currency: (rowData.unitPrice === 'USD' ? 'USD' : 'VND') : 'symbol' : (rowData.unitPrice === 'USD' ? '1.2-2' : '1.0-0')
                    }}
                </td>
                <td *ngIf="!columns[16].hide && rowData.showLevel3" [attr.rowspan]="rowData.rowspanLevel3">
                    {{ rowData.deliveredQuantity | number: '1.0-0' }}
                </td>
                <td *ngIf="!columns[17].hide && rowData.showLevel3" [attr.rowspan]="rowData.rowspanLevel3">
                    {{
                        rowData.deliveredAmount
                            | currency: (rowData.unitPrice === 'USD' ? 'USD' : 'VND') : 'symbol' : (rowData.unitPrice === 'USD' ? '1.2-2' : '1.0-0')
                    }}
                </td>
                <td *ngIf="!columns[18].hide && rowData.showLevel3" [attr.rowspan]="rowData.rowspanLevel3">
                    {{ rowData.remainingQuantity | number: '1.0-0' }}
                </td>
                <td *ngIf="!columns[19].hide && rowData.showLevel3" [attr.rowspan]="rowData.rowspanLevel3">
                    {{
                        rowData.remainingAmount
                            | currency: (rowData.unitPrice === 'USD' ? 'USD' : 'VND') : 'symbol' : (rowData.unitPrice === 'USD' ? '1.2-2' : '1.0-0')
                    }}
                </td>
                <ng-container *ngIf="dateColumns.length > 0 && !columns[20].hide; else noDatetd">
                    <td *ngFor="let date of dateColumns">{{ getDailyQuantity(rowData, date) }}</td>
                </ng-container>
                <ng-template #noDatetd>
                    <td *ngIf="!columns[20].hide && rowData.showLevel3" [attr.rowspan]="rowData.rowspanLevel3"></td>
                </ng-template>
            </tr>
        </ng-template>
    </p-table>
    <app-paginator
        [tableId]="tableId"
        [totalCount]="pagination.totalCount"
        [currentPage]="pagination.page + 1"
        [totalPages]="pagination.totalPage"
        [refetch]="pagination.refetch"
        [size]="pagination.size"
    ></app-paginator>
    <br />
</div>

<p-overlayPanel #op>
    <p-table [value]="columns" [selection]="columnChoose" (selectionChange)="setColumnSelection($event)" [scrollable]="true" scrollHeight="500px">
        <ng-template pTemplate="header">
            <tr>
                <th>
                    <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                </th>
                <th>Tất cả</th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rowData>
            <tr>
                <td style="font-size: 14px; padding: 8px 12px">
                    <p-tableCheckbox [value]="rowData" [hidden]="rowData.default" />
                </td>
                <td style="font-size: 14px; padding: 8px 12px">
                    {{ rowData['header'] }}
                </td>
            </tr>
        </ng-template>
    </p-table>
</p-overlayPanel>
