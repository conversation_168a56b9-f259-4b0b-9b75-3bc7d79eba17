import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, OnInit, TemplateRef, ViewChild, inject } from '@angular/core';
import { RouterLink } from '@angular/router';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { ButtonModule } from 'primeng/button';
import { FileUploadModule } from 'primeng/fileupload';
import { TABLE_KEY } from 'src/app/models/constant';
import { Column } from 'src/app/models/interface';
import { TableCommonService } from 'src/app/shared/table-module/table.common.service';
import { TagModule } from 'primeng/tag';
import { SupplierTypeService } from 'src/app/services/sc/supplier/suppiler-type.service';
import { SupplierPriority } from 'src/app/models/constant/sc';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { SupplierType } from 'src/app/models/interface/sc';
import { CriteriaBuyService } from 'src/app/services/sc/evaluate/criteria-buy.service';
import {HasAnyAuthorityDirective} from "../../../../../shared/directives/has-any-authority.directive";

@Component({
    selector: 'app-supplier-evaluate',
    templateUrl: './criteria-buy.component.html',
    standalone: true,
    imports: [
        RouterLink,
        TableCommonModule,
        ButtonModule,
        FileUploadModule,
        CommonModule,
        TagModule,
        SubHeaderComponent,
        HasAnyAuthorityDirective,
    ],
    providers: [SupplierTypeService],
})
export class CriteriaBuyComponent implements OnInit, AfterViewInit {
    @ViewChild('templateStatus') templateStatus: TemplateRef<Element>;
    @ViewChild('templateAction') templateAction: TemplateRef<Element>;

    priority = SupplierPriority;

    tableCommonService = inject(TableCommonService);
    state: QueryObserverBaseResult<SupplierType[]>;
    columns: Column[] = [];
    tableId: string = TABLE_KEY.SUPPLIER_EVALUATE;
    criteriaBuyService = inject(CriteriaBuyService);

    ngOnInit() {
        this.tableCommonService
            .init<SupplierType>({
                tableId: this.tableId,
                queryFn: (filter) => this.criteriaBuyService.getPageTableCustom(filter),
                configFilterRSQL: {
                    code: 'Text',
                    createBy: 'Text',
                    createDate: 'DateRange',
                    note: 'Text',
                },
                filterUrl: true,
            })
            .subscribe((state) => {
                this.state = state;
            });
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.columns = [
                {
                    field: 'code',
                    header: 'Mã',
                    type: 'link',
                    url: '/sc/criteria-buy/{id}',
                    default: true,
                },
                { field: 'createBy', header: 'Người tạo' },
                {
                    field: 'createDate',
                    header: 'Ngày đánh giá',
                    type: 'date',
                    format: 'dd/MM/yyyy',
                },
                {
                    field: 'note',
                    header: 'Ghi chú',
                },
            ];
        }, 0);
    }

    deleteSelected = (ids: number[]) => {
        return this.criteriaBuyService.batchDelete(ids);
    };
}
