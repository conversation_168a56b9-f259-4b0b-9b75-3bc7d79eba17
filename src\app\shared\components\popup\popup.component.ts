import { NgIf, NgTemplateOutlet } from '@angular/common';
import { Component, EventEmitter, Input, Output, TemplateRef } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { EventPopupSubmit } from 'src/app/models/interface';

@Component({
    selector: 'app-popup',
    templateUrl: './popup.component.html',
    styleUrls: ['./popup.component.scss'],
    standalone: true,
    imports: [ButtonModule, DialogModule, NgIf, NgTemplateOutlet],
})
export class PopupComponent {
    @Input() header: string = ''; // Header cho dialog
    @Input() typePopup: 'default' | 'download' = 'default';
    @Input() label: string = 'Button';
    @Input() isButtonVisible: boolean = true;
    @Input() dialogWidth?: string;
    @Input() showConfirmButton: boolean = true;
    @Input() severity: 'secondary' | 'success' | 'info' | 'warning' | 'help' | 'danger' | 'contrast' | 'primary' = 'primary';
    @Input() formGroup: FormGroup;
    @Input() openData?: unknown;
    @Input() contentTemplate: TemplateRef<Element>;
    @Output() onClose = new EventEmitter<void>();
    @Output() onOpen = new EventEmitter<unknown>();
    @Output() onSubmit = new EventEmitter<EventPopupSubmit<unknown>>();

    // Biến để điều khiển popup
    isVisible: boolean = false;

    closeDialog(): void {
        this.isVisible = false; // Đóng popup
        this.formGroup?.reset();
        this.formGroup?.patchValue({});
        this.onClose.emit();
    }

    openDialog<T>(data?: T): void {
        this.isVisible = true; // Mở popup
        const payload = (data !== undefined ? data : this.openData) as unknown;
        this.onOpen.emit(payload);
    }

    handleOnSubmit() {
        this.onSubmit.emit({ value: this.formGroup?.value, close: () => this.closeDialog() });
    }
}
