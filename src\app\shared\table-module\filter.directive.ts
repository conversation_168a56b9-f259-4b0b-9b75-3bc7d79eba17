import { Directive, Input, OnInit, ElementRef, OnDestroy } from '@angular/core';
import { TableCommonService } from './table.common.service';
import { Subscription } from 'rxjs';
import { isArray } from 'lodash';
@Directive({
    selector: '[appFilter]',
})
export class ColumnFilterDirective implements OnInit, OnDestroy {
    @Input('appFilter') appFilter: string[];
    tableId: string;
    field: string;
    columnVisible: string[] = [];
    private columnVisibleSubscription: Subscription;

    constructor(
        private el: ElementRef,
        private tableCommonService: TableCommonService,
    ) {}

    ngOnInit() {
        if (isArray(this.appFilter) && this.appFilter.length === 2) {
            this.tableId = this.appFilter[0];
            this.field = this.appFilter[1];
        }

        // Lấy columnVisible từ service và đăng ký lắng nghe các thay đổi
        this.columnVisibleSubscription = this.tableCommonService
            .getColumnVisible(this.tableId)
            .subscribe((columnVisible) => {
                this.columnVisible = columnVisible;

                this.updateColumnVisibility();
            });

        // Cập nhật hiển thị ban đầu của cột
        this.updateColumnVisibility();
    }

    ngOnDestroy() {
        if (this.columnVisibleSubscription) {
            this.columnVisibleSubscription.unsubscribe();
        }
    }

    private updateColumnVisibility() {
        if (this.columnVisible.includes(this.field)) {
            this.el.nativeElement.style.display = '';
        } else {
            this.el.nativeElement.style.display = 'none';
        }
    }
}
