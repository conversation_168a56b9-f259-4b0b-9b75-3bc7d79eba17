<app-sub-header [items]="itemsHeader"></app-sub-header>

<div style="padding: 1rem 1rem 0 1rem">
    <app-table-common
        [tableId]="tableId"
        [columns]="columns"
        [data]="state.data"
        [loading]="state.isFetching"
        [filterTemplate]="filterTemplate"
        [funcDelete]="null"
        name="Danh sách quyền"
        selectionMode=""
    >
        <ng-template #filterTemplate>
            <tr>
                <th [appFilter]="[tableId, 'displayName']">
                    <app-filter-table [tableId]="tableId" field="displayName"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'description']" style="max-width: 8rem">
                    <app-filter-table [tableId]="tableId" field="description"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'created']"></th>
                <th [appFilter]="[tableId, 'createdBy']"></th>
            </tr>
        </ng-template>
        <ng-template #templateType let-rowData>
            <p-tag [value]="rowData.type ? 'Mặc định' : 'Tùy chọn'"></p-tag>
        </ng-template>
    </app-table-common>
</div>
