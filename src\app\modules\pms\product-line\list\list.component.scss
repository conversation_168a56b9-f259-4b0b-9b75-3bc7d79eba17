:host ::ng-deep .p-tree {
    border: none !important;
    box-shadow: none !important;
}

:host ::ng-deep .p-dropdown {
    width: 100% !important;
}

:host ::ng-deep .p-multiselect {
    width: 100%;
}

:host ::ng-deep .p-autocomplete {
    width: 100%;
}

:host ::ng-deep .version-card .p-card-content {
    display: flex; /* biến thẻ thành flex container */
    justify-content: space-between; /* cách đều trái – phải */
    align-items: center; /* căn block action-group lên top */
}

.product-filter-bar {
    display: flex;
    align-items: center;
    justify-content: space-between; /* <PERSON><PERSON> bổ không gian giữa trái và phải */
    padding: 10px;
    border-bottom: 1px solid #eee; /* Đường kẻ dưới */
}

.filter-container {
    display: grid;
    grid-template-columns: 70% 11% auto;
    align-items: center;

    &.sub-model-mode {
        grid-template-columns: 35% 11% auto;
    }
}

.filter-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.right-button {
    position: absolute;
    top: 8px;
    right: 8px;
}

.left-section > * {
    margin-right: 10px; /* Khoảng cách giữa các phần tử bên trái */
}

.left-section > label {
    margin-right: 15px;
}

.right-section {
    justify-self: end;
    display: grid;
    align-items: center;
}

.right-section > * {
    margin-left: 10px; /* Khoảng cách giữa các phần tử bên phải */
}

.filter-dropdown {
    min-width: 150px; /* Đặt chiều rộng tối thiểu cho dropdown */
}

.filter-button {
    padding: 0.5rem 1rem; /* Điều chỉnh padding cho nút */
    font-size: 0.9em;
}

.fixed-width-card {
    width: 170px;
    padding: 0;
    box-sizing: border-box;
    // position: relative;
}

.p-card .p-card-body {
    height: 290px !important;
    padding: 0 !important;
}

.p-card .p-card-content {
    padding: 0 !important;
}

.radio-pn-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 5px;
}

.card-image {
    // width: 100%;
    height: auto;
    height: 100px; /* Giới hạn chiều cao */
    object-fit: contain;
    background-color: #f5f5f5;
}

.card-image:before {
    content: 'No Image Available';
    display: none; /* ẩn mặc định */
}

.card-image[src*='placeholder.png']:before {
    display: block; /* hiển thị khi dùng placeholder */
}

.radio-row {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    background-color: #fff;
}

.small-radio .p-radiobutton-box {
    width: 14px;
    height: 14px;
}

:host ::ng-deep .p-inputtext[disabled] {
    opacity: 1;
    color: #333;
}

:host ::ng-deep .p-dropdown.p-disabled {
    opacity: 1;
    color: #333;
}

:host ::ng-deep .p-multiselect.p-disabled {
    opacity: 1;
    color: #333;
    overflow-x: auto;
}

:host ::ng-deep .p-card-body {
    height: 290px;
}

.image-info {
    display: flex;
    align-items: center;
}

.item-options {
    white-space: nowrap;
    overflow: scroll;
    text-overflow: ellipsis;
}

:host ::ng-deep .p-multiselect.p-disabled {
    opacity: 1;
    color: #333;
    overflow-x: auto;
}
