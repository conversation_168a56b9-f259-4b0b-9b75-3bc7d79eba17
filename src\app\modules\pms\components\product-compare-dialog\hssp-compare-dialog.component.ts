import { Component, Input, Output, EventEmitter, Type, OnInit, OnDestroy, ViewChild } from '@angular/core';
import { forkJoin, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { TableModule } from 'primeng/table';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { ProductLineService } from 'src/app/services/pms/product-line/product-line.service';
import { ProductFileService } from 'src/app/services/pms/product-file/product-file.service';
import { TabViewComponent } from 'src/app/shared/components/tab-view/tab-view.component';

import { TabItem } from 'src/app/models/interface/pms';
import { DesignProfileComponent } from './design-profile/design-profile.component';
import { ProductionProfileComponent } from './production-profile/production-profile.component';
import { QualityProfileComponent } from './quality-profile/quality-profile.component';

interface HsspProduct {
    id: number;
    name: string;
    mdDesign?: string;
    printingDesign?: string;
    testIdMd?: string;
    assemblyTest?: string;
    labelDesign?: string;
    giftBoxDesign?: string;
    accessoryPrinting?: string;
    testAccessories?: string;
    accessoryReport?: string;
    bootloader?: string;
    fwBasic?: string;
    fw1?: string;
    fw2?: string;
    fw3?: string;
    releaseNote?: string;
}

@Component({
    selector: 'app-hssp-compare-dialog',
    templateUrl: './hssp-compare-dialog.component.html',
    styleUrls: ['./hssp-compare-dialog.component.scss'],
    standalone: true,
    imports: [TableModule, CommonModule, ButtonModule, TabViewComponent],
    providers: [ProductLineService, ProductFileService],
})
export class HsspCompareDialogComponent implements OnInit, OnDestroy {
    @Input() visible = false;
    @Input() comparedProducts: HsspProduct[] = [];
    @Input() products: any[] = [];
    @Output() visibleChange = new EventEmitter<boolean>();
    @ViewChild(TabViewComponent) tabViewComp?: TabViewComponent;

    private destroy$ = new Subject<void>();
    isLoading = true;
    errorMessage: string | null = null;

    // Lưu trữ toàn bộ dữ liệu
    allTabData: {
        [versionId: number]: {
            [tabType: number]: any;
        };
    } = {};

    currentActiveTabType = 1;
    itemsTab: TabItem[] = [
        { header: 'Hồ sơ thiết kế', component: DesignProfileComponent, type: 1 },
        { header: 'Hồ sơ sản xuất', component: ProductionProfileComponent, type: 2 },
        { header: 'Hồ sơ chất lượng', component: QualityProfileComponent, type: 4 },
    ];

    hsspComparison = [
        { label: 'Version hồ sơ', key: 'versionProfile' },
        { label: 'Giai đoạn', key: 'stage' },
    ];
    isInitialized = false;

    constructor(private productLineService: ProductLineService) {}

    ngOnInit() {
        this.loadAllTabsData();
    }

    initializeData() {
        if (!this.isInitialized || this.products?.length) {
            this.currentActiveTabType = 1;
            setTimeout(() => {
                this.tabViewComp!.activeIndex = 0;
                this.tabViewComp!.onTabChange({ index: 0 }); // cập nhật currentActiveTabType và data tab
            });
            this.loadAllTabsData();
            this.isInitialized = true;
        }
    }
    ngOnDestroy() {
        this.destroy$.next();
        this.destroy$.complete();
    }

    onClose() {
        this.visible = false;
        this.visibleChange.emit(false);
    }

    onTabChange(type: number) {
        this.currentActiveTabType = type;
        this.updateActiveTabData();
    }

    private loadAllTabsData() {
        const version1Id = this.products[0]?.id;
        const version2Id = this.products[1]?.id;

        if (!version1Id || !version2Id) {
            this.errorMessage = 'Thiếu thông tin sản phẩm để so sánh';
            this.isLoading = false;
            return;
        }
        // Gọi API một lần cho mỗi version với types=7
        forkJoin([
            this.productLineService.getProductDoc({
                versionId: version1Id,
                types: 7, // Lấy tất cả dữ liệu 3 tab
            }),
            this.productLineService.getProductDoc({
                versionId: version2Id,
                types: 7,
            }),
        ])
            .pipe(takeUntil(this.destroy$))
            .subscribe({
                next: ([res1, res2]) => {
                    // Lưu dữ liệu theo cấu trúc: allTabData[versionId][tabType]
                    this.allTabData[version1Id] = {
                        1: res1['1'],
                        2: res1['2'],
                        4: res1['4'],
                    };
                    this.allTabData[version2Id] = {
                        1: res2['1'],
                        2: res2['2'],
                        4: res2['4'],
                    };

                    // Cập nhật tab đầu tiên
                    this.updateActiveTabData();
                    this.isLoading = false;
                },
                error: (err) => {
                    console.error('Error loading all tabs data:', err);
                    this.errorMessage = 'Lỗi khi tải dữ liệu so sánh';
                    this.isLoading = false;
                },
            });
    }

    private updateActiveTabData() {
        const version1Id = this.products[0]?.id;
        const version2Id = this.products[1]?.id;
        const tabType = this.currentActiveTabType;

        if (!version1Id || !version2Id || !this.allTabData[version1Id] || !this.allTabData[version2Id]) {
            return;
        }

        const tabIndex = this.itemsTab.findIndex((tab) => tab.type === tabType);
        if (tabIndex !== -1) {
            this.itemsTab[tabIndex].inputs = {
                docRes1: this.allTabData[version1Id][tabType],
                docRes2: this.allTabData[version2Id][tabType],
            };
        }
    }
}
