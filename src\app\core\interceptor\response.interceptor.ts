import { Injectable } from '@angular/core';
import { HttpRequest, HttpHandler, HttpEvent, HttpInterceptor } from '@angular/common/http';

import { Observable, finalize, tap } from 'rxjs';
import { MessageService } from 'primeng/api';
import { AlertService } from '../../shared/services/alert.service';

@Injectable()
export class ResponseInterceptor implements HttpInterceptor {
    constructor(
        private messageService: MessageService,
        private alertService: AlertService,
    ) {}

    intercept(req: HttpRequest<unknown>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<unknown>> {
        // const started = Date.now();

        return next.handle(req).pipe(
            tap({
                error: (error) => {
                    this.messageService.clear();
                    if (error.status === 500) {
                        if (req.url.includes('/fetch-new') || req.url.includes('/combobox') || req.url.includes('/token') || req.url.match(/\/\d+/g)) {
                            return;
                        }
                        this.messageService.add({
                            key: 'app-alert',
                            severity: 'warn',
                            summary: 'Cảnh báo',
                            detail: 'Lỗi máy chủ',
                        });
                    } else if (error.status === 405) {
                        this.messageService.add({
                            key: 'app-alert',
                            severity: 'warn',
                            summary: 'Cảnh báo',
                            detail: 'Phương thức lấy dữ liệu không phù hợp',
                        });
                    } else if (error.status === 400) {
                        this.alertService.handleError(error);
                    } else if (error.status === 403) {
                        this.messageService.add({
                            key: 'app-alert',
                            severity: 'warn',
                            summary: 'Cảnh báo',
                            detail: 'Không có quyền thực hiện chức năng',
                        });
                    }
                },
            }),
            // todo: handle error
            finalize(() => {
                // const elapsed = Date.now() - started;
                // console.log('elapsed', elapsed)
            }),
        );
    }
}
