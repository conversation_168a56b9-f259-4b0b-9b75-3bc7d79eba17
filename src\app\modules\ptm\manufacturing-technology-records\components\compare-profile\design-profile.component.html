<div class="tables-container">
    <ng-container *ngFor="let group of groupedRows">
        <p-table
            [value]="group.items"
            class="section-table mb-4"
            styleClass="p-datatable-gridlines"
            [tableStyle]="{ 'table-layout': 'fixed', width: '100%' }"
        >
            <!-- SINGLE HEADER ROW WITH 5 COLUMNS -->
            <ng-template pTemplate="header">
                <tr>
                    <th>{{ group.name }}</th>
                    <th></th>
                    <th>Ghi chú</th>
                    <th></th>
                    <th>Ghi chú</th>
                </tr>
            </ng-template>

            <!-- BODY rows -->
            <ng-template pTemplate="body" let-row>
                <tr>
                    <td class="tw-truncate tw-max-w-[150px]" title="{{ row.name }}">{{ row.name }}</td>
                    <td class="tw-truncate tw-max-w-[150px]" title="{{ row.note1 }}">
                        <a class="tw-cursor-pointer" (click)="downloadFile(row.path1, row.note1)">{{ row.note1 }}</a>
                    </td>
                    <td class="tw-truncate tw-max-w-[150px]" title="{{ row.note2 }}">{{ row.note2 }}</td>
                    <td class="tw-truncate tw-max-w-[150px]" title="{{ row.note3 || '' }}">
                        <a class="tw-cursor-pointer" (click)="downloadFile(row.path3, row.note3)">{{ row.note3 || '' }}</a>
                    </td>
                    <td class="tw-truncate tw-max-w-[150px]" title="{{ row.note4 || '' }}">{{ row.note4 || '' }}</td>
                </tr>
            </ng-template>
        </p-table>
    </ng-container>
</div>
