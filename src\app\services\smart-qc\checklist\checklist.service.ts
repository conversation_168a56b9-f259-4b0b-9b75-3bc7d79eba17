import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '../../base.service';
import {Checklist} from "../../../models/interface/smart-qc";

@Injectable({
    providedIn: 'root',
})
export class ChecklistService extends BaseService<Checklist> {
    constructor(protected override http: HttpClient) {
        super(http, '/smart-qc/api/checklist');
    }

    updatePosition(checklist: Checklist[]) {
        return this.http.post<Checklist[]>(`${this.baseUrl}/update-position`, checklist);
    }

    getChecklistDetailImage(templateId: number) {
        return this.http.get<Checklist[]>(`${this.baseUrl}/get-checklist-detail-image?templateId=` + templateId, {
            observe: 'response',
        });
    }
}
