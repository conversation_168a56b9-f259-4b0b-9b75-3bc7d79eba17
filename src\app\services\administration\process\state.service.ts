import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '../../base.service';
import { State } from 'src/app/models/interface';

@Injectable()
export class StateService extends BaseService<State> {
    constructor(protected override http: HttpClient) {
        super(http, '/api/states');
    }

    getAllKeyEntity() {
        return this.http.get<string[]>('/api/states/all-key-entity');
    }
}
