<div
    class="surface-ground flex align-items-center justify-content-center min-h-screen min-w-screen overflow-hidden"
    style="
        background-image: url('/assets/images/background/Background.png');
        background-position: center;
        background-repeat: no-repeat;
        background-size: cover;
    "
>
    <img
        src="/assets/images/background/logo 2.png"
        alt="vnpt logo"
        style="position: absolute; width: 166px; height: 32px; top: 32px; left: 32px"
    />
    <div class="flex flex-column align-items-center justify-content-center">
        <div class="w-full surface-card py-8 px-5 sm:px-8" style="border-radius: 53px">
            <div class="text-center mb-5">
                <div class="text-900 text-3xl font-medium mb-3">Chào mừng!</div>
                <span class="text-600 font-medium"> {{ account.email }}</span>
            </div>
            <form #loginForm="ngForm">
                <label for="password1" class="block text-900 font-medium text-xl tw-mt-1">Mật khẩu mới</label>
                <p-password
                    autocomplete="password"
                    name="password"
                    id="password1"
                    [(ngModel)]="account.newPassword"
                    [toggleMask]="true"
                    [feedback]="false"
                    styleClass="mb-2"
                    inputStyleClass="w-full p-3 md:w-30rem"
                ></p-password>
                <div *ngIf="isWeekPassword()" class="tw-text-red-600 tw-mb-2" style="font-size: 12px">
                    Mật khẩu ít nhất 8 ký tự gồm chữ thường, chữ in hoa, số và ký tự đặc biệt
                </div>
                <label for="password1" class="block text-900 font-medium text-xl tw-mt-1">Xác nhận mật khẩu</label>
                <p-password
                    autocomplete="password"
                    name="password"
                    id="password1"
                    [(ngModel)]="account.confirmPassword"
                    [toggleMask]="true"
                    [feedback]="false"
                    styleClass="mb-5"
                    inputStyleClass="w-full p-3 md:w-30rem"
                ></p-password>
                <div
                    *ngIf="account.confirmPassword && account?.newPassword !== account?.confirmPassword"
                    class="text-red-600"
                >
                    Mật khẩu không khớp
                </div>

                <div class="flex align-items-center justify-content-between mb-5 gap-5">
                    <div class="flex align-items-center"></div>
                </div>
                <button pButton pRipple label="Xác nhận" class="w-full p-3 text-xl" (click)="setPassword()"></button>
            </form>
        </div>
    </div>
</div>
