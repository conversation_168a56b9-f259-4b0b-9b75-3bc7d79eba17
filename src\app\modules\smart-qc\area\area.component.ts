import { Component, OnInit, AfterViewInit } from '@angular/core';
import { TableModule } from 'primeng/table';
import { PaginatorModule } from 'primeng/paginator';
import { CommonModule } from '@angular/common';
import { QueryObserverBaseResult } from '@tanstack/query-core';
import { Area, Column } from '../../../models/interface';
import { TableCommonService } from '../../../shared/table-module/table.common.service';
import { TABLE_KEY } from 'src/app/models/constant';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { AreaService } from 'src/app/services/administration/area/area.service';
import { DistrictService } from 'src/app/services/administration/district/district.service';

@Component({
    selector: 'app-area',
    templateUrl: './area.component.html',
    standalone: true,
    imports: [CommonModule, TableModule, PaginatorModule, TableCommonModule, SubHeaderComponent],
    providers: [AreaService, DistrictService],
})
export class AreaComponent implements OnInit, AfterViewInit {
    state: QueryObserverBaseResult<Area[]>;
    columns: Column[] = [];
    tableId: string = TABLE_KEY.AREA;
    itemsHeader = [{ label: 'Quản lý danh mục' }, { label: 'Danh sách Tỉnh/ thành phố', url: '' }];

    constructor(
        private areaService: AreaService,
        private tableCommonService: TableCommonService,
    ) {}

    ngOnInit() {
        this.tableCommonService
            .init<Area>({
                tableId: this.tableId,
                queryFn: (filter) => this.areaService.getPageTableCustom(filter),
                configFilterRSQL: {
                    name: 'MultiText',
                    code: 'Text',
                },
                initialData: [],
            })
            .subscribe((state) => {
                this.state = state;
            });
    }
    ngAfterViewInit(): void {
        setTimeout(() => {
            this.columns = [
                { field: 'name', header: 'Tên', default: true },
                { field: 'code', header: 'Mã' },
            ];
        });
    }
}
