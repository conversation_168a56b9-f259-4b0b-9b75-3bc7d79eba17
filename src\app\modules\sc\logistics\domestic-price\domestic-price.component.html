<app-sub-header [items]="[{ label: 'Logistics' }, { label: 'Báo giá nội địa tháng' }]" [action]="actionHeader"></app-sub-header>

<ng-template #actionHeader> </ng-template>
<div class="tw-p-4 tw-relative">
    <p-tabView [(activeIndex)]="activeIndex" (activeIndexChange)="updateParam()">
        <p-tabPanel header="Chi tiết">
            <ng-template pTemplate="content">
                <div class="tw-absolute tw-top-4 tw-right-4 tw-mt-2 tw-mr-2">
                    <p-calendar
                        view="month"
                        dateFormat="mm/yy"
                        placeholder="Tháng"
                        (onSelect)="handleChangeDate($event)"
                        (onClear)="handleChangeDate(null)"
                        [(ngModel)]="initDateSerach"
                        [showClear]="true"
                    ></p-calendar>
                </div>
                <p-table
                    [value]="stateRoute"
                    [scrollable]="true"
                    scrollHeight="700px"
                    dataKey="id"
                    styleClass="tw-table-fixed"
                    [loading]="loading"
                    [paginator]="true"
                    [rows]="10"
                    [rowsPerPageOptions]="[10, 25, 50]"
                    [totalRecords]="stateRoute ? stateRoute.length : 0"
                    (onRowExpand)="onRowExpand($event)"
                    (onRowCollapse)="onRowCollapse($event)"
                    [expandedRowKeys]="expandedRows"
                >
                    <ng-template pTemplate="header">
                        <tr>
                            <th class="tw-whitespace-nowrap">Tuyến, NCC</th>
                            <th colspan="12">Cước vận chuyển theo mức cân hoặc loại xe (VNĐ)</th>
                        </tr>
                        <tr>
                            <th></th>
                            <th class="tw-whitespace-nowrap">< 45KG</th>
                            <th class="tw-whitespace-nowrap">45 - 300KG</th>
                            <th class="tw-whitespace-nowrap">300 - 600KG</th>
                            <th class="tw-whitespace-nowrap">Xe 1.25 tấn</th>
                            <th class="tw-whitespace-nowrap">Xe 2.5 tấn</th>
                            <th class="tw-whitespace-nowrap">Xe 3.5 tấn</th>
                            <th class="tw-whitespace-nowrap">Xe 5 tấn</th>
                            <th class="tw-whitespace-nowrap">Xe 7 tấn</th>
                            <th class="tw-whitespace-nowrap">Xe 8 tấn</th>
                            <th class="tw-whitespace-nowrap">Xe 10 tấn</th>
                            <th class="tw-whitespace-nowrap">Container 20'</th>
                            <th class="tw-whitespace-nowrap">Container 40'</th>
                        </tr>
                    </ng-template>

                    <ng-template pTemplate="body" let-route let-rowIndex="rowIndex" let-expanded="expanded">
                        <tr>
                            <td colspan="5" class="">
                                <button
                                    type="button"
                                    pButton
                                    pRipple
                                    [pRowToggler]="route"
                                    class="p-button-text p-button-rounded p-button-plain"
                                    [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"
                                ></button>
                                <b class="tw-leading-10">
                                    {{ route.name }}
                                </b>
                            </td>
                            <td colspan="8"></td>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="rowexpansion" let-route>
                        <tr *ngFor="let logistic of getAllKey(route)">
                            <td>- {{ logistic }}</td>
                            <td
                                [ngClass]="{
                                    'tw-text-green-600': objectMin[detail.id] === true,
                                }"
                                *ngFor="let detail of route.logisticsDetails[logistic]"
                            >
                                {{ detail.price | number }}
                            </td>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="emptymessage">
                        <tr>
                            <td colspan="30"><div class="tw-text-center">Không có dữ liệu.</div></td>
                        </tr>
                    </ng-template>
                </p-table>
            </ng-template>
        </p-tabPanel>
        <p-tabPanel header="Dữ liệu">
            <ng-template pTemplate="content">
                <div class="tw-absolute tw-top-4 tw-right-4 tw-mt-2 tw-mr-2">
                    <app-popup
                        header="Thêm đánh giá báo giá nội địa tháng"
                        label="Nhập dữ liệu"
                        [formGroup]="formGroup"
                        *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_monthly_quote_edit']"
                        (onSubmit)="handleSubmit($event)"
                        (onClose)="handleClose()"
                        [contentTemplate]="contentTemplate"
                    >
                        <ng-template #contentTemplate let-isVisible>
                            <app-form [formGroup]="formGroup" styleClass="tw-grid tw-grid-cols-1 tw-gap-4" *ngIf="formGroup && isVisible">
                                <app-form-item label="Thời gian báo giá">
                                    <p-calendar view="month" dateFormat="mm/yy" placeholder="Tháng" appendTo="body" formControlName="dateCustom"></p-calendar>
                                </app-form-item>

                                <app-form-item label="Tài liệu excel">
                                    <app-button-group-file
                                        (onClearFile)="handleClearFile('DOCUMENT_DATA')"
                                        [urlError]="urlErrorData"
                                        (onFileSelected)="handleUploadFileDocument($event, 'DOCUMENT_DATA')"
                                        urlTemplate="template_monthly_price.xlsx"
                                        [types]="['excel']"
                                        formControlName="attachmentUrl"
                                        [attachment]="formGroup.get('attachmentUrlFile').value"
                                    ></app-button-group-file>
                                </app-form-item>

                                <app-form-item label="Tài liệu khác">
                                    <app-button-group-file
                                        [urlError]="urlErrorAnother"
                                        (onClearFile)="handleClearFile('DOCUMENT_ANOTHER')"
                                        (onFileSelected)="handleUploadFileDocument($event, 'DOCUMENT_ANOTHER')"
                                        simpleUpload=""
                                        formControlName="additionAttachmentUrl"
                                        [attachment]="formGroup.get('additionAttachmentUrlFile').value"
                                    ></app-button-group-file>
                                </app-form-item>
                            </app-form>
                        </ng-template>
                    </app-popup>
                </div>
                <app-table-common
                    [tableId]="tableId"
                    [columns]="columns"
                    [data]="state.data"
                    [loading]="state.isFetching"
                    [filterTemplate]="filterTemplate"
                    name="Danh sách file báo giá"
                    [funcDelete]="deleteSelected"
                >
                    <ng-template #filterTemplate>
                        <tr>
                            <th></th>
                            <th [appFilter]="[tableId, 'date']">
                                <app-filter-table [rsql]="true" [tableId]="tableId" field="date" type="month"></app-filter-table>
                            </th>
                            <th [appFilter]="[tableId, 'attachmentUrl']">
                                <app-filter-table [rsql]="true" [tableId]="tableId" field="attachmentUrl"></app-filter-table>
                            </th>
                            <th [appFilter]="[tableId, 'additionAttachmentUrl']">
                                <app-filter-table [rsql]="true" [tableId]="tableId" field="additionAttachmentUrl"></app-filter-table>
                            </th>
                            <th [appFilter]="[tableId, 'updated']">
                                <app-filter-table [rsql]="true" [tableId]="tableId" field="updated" type="date-range"></app-filter-table>
                            </th>
                            <th [appFilter]="[tableId, 'updatedBy']">
                                <app-filter-table [rsql]="true" [tableId]="tableId" field="updatedBy"></app-filter-table>
                            </th>
                        </tr>
                    </ng-template>
                    <ng-template #templateUrl let-rowData>
                        <a target="_blank" class="tw-cursor-pointer" rel="noopener noreferrer" (click)="handleDownload(rowData.attachmentUrl)">{{
                            getLastPart(rowData, 'attachmentUrl')
                        }}</a>
                    </ng-template>

                    <ng-template #templateAdditionUrl let-rowData>
                        <a target="_blank" class="tw-cursor-pointer" rel="noopener noreferrer" (click)="handleDownload(rowData.additionAttachmentUrl)">{{
                            getLastPart(rowData, 'additionAttachmentUrl')
                        }}</a>
                    </ng-template>
                </app-table-common>
            </ng-template>
        </p-tabPanel>
    </p-tabView>
</div>
