/* eslint-disable @typescript-eslint/no-explicit-any */
import { Component, Input, OnInit, OnChanges, SimpleChanges, forwardRef, Output, EventEmitter, inject } from '@angular/core';
import { FilterTable, TableCommonService } from '../table.common.service';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { injectQuery, QueryObserverBaseResult } from '@ngneat/query';
import { debounce, isArray, isDate, unionBy, isEqual, get } from 'lodash';
import { MultiSelectChangeEvent } from 'primeng/multiselect';
import { DropdownChangeEvent, DropdownFilterEvent } from 'primeng/dropdown';
import { NG_VALUE_ACCESSOR } from '@angular/forms';
import Common from 'src/app/utils/common';

// Interface definitions remain unchanged
export interface ConfigSelectProps {
    options?: Array<unknown>;
    fieldValue: string;
    fieldLabel: string;
    param?: string;
    paramForm?: string;
    url?: string;
    rsql?: boolean;
    dataKey?: string;
    page?: number;
    size?: number;
    sort?: string;
    body?: Record<string, unknown>;
    additionalCondition?: string;
    filterLocal?: boolean;
}

export interface ConfigDate {
    minDate?: Date;
    maxDate?: Date;
    dateFormat?: string;
    filter?: Date[] | Date;
}

export interface FilterChangeEvent {
    value: unknown;
    objects: Array<unknown>;
}

@Component({
    selector: 'app-filter-table',
    templateUrl: './filter-table.component.html',
    styleUrls: ['./filter-table.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => FilterTableComponent),
            multi: true,
        },
    ],
})
export class FilterTableComponent implements OnInit, OnChanges {
    private valueSubject = new Subject<unknown>();
    private objectFilter: FilterTable; // Filter object for the table
    value: any; // Current value for the filter
    objectValue: unknown[] = []; // Selected objects
    #http = inject(HttpClient);
    #query = injectQuery();
    debouncedGetOptions: (...args: unknown[]) => void;

    fetchFirstDone: boolean = false;
    isHasValueUrl: boolean = false;
    countCallApi: number = 0;
    needMoreOption: boolean = false;
    /**
     * Input properties
     */
    @Input() disabled: boolean = false;
    @Input() selectFirstValue: boolean = false;
    @Input() tableId: string;
    @Input() field: string;
    @Input() placeholder: string = '';
    @Input() type: 'text' | 'number' | 'select' | 'select-one' | 'date' | 'date-range' | 'month' = 'text'; // Type of filter
    @Input() rsql: boolean = true;
    @Input() filterCombobox: boolean = true;
    @Input() requiredFilter: string[] = [];
    @Input() configSelect: ConfigSelectProps = {
        options: [],
        fieldValue: 'id',
        fieldLabel: 'id',
        rsql: true,
        dataKey: 'id',
    };
    @Input() configDate: ConfigDate = {
        dateFormat: 'dd/mm/yy',
    };
    @Input() initValue: unknown;
    @Input() removeField: string;
    @Input() removeFieldValues: unknown[];
    @Input() removeNullLabel: boolean = false;
    @Output() onChange: EventEmitter<FilterChangeEvent> = new EventEmitter();
    @Output() onApiCallback: EventEmitter<unknown> = new EventEmitter();
    resultSelect: QueryObserverBaseResult<unknown, Error>;
    prioritizeForm: boolean = false; // key ưu tiên sử dụng paramForm nếu thực hiện thay đổi value qua form
    searchValue: string = '';

    // ControlValueAccessor properties
    private onChangeCallback: (value: any) => void = () => {};
    private onTouchedCallback: () => void = () => {};

    constructor(private tableCommonService: TableCommonService) {
        this.debouncedGetOptions = debounce((searchValue: unknown) => this.fetchOptions(searchValue, this.prioritizeForm), this.getDebounceTime());

        this.initializeDebounce();
    }

    // Initialize debounce and distinctUntilChanged for filter changes
    private initializeDebounce() {
        this.valueSubject
            .pipe(
                debounceTime(this.getDebounceTime()),
                distinctUntilChanged((prev, curr) => isEqual(prev, curr)),
            )
            .subscribe((value) => {
                this.value = value;
                this.applyFilter(value);
                this.onChangeCallback(value);
            });
    }

    private getDebounceTime(): number {
        return ['select', 'select-one'].includes(this.type) ? 300 : 500;
    }

    private updateValueLogic(value: unknown): void {
        this.value = this.type === 'select' ? this.handleSelectValue(value) : value;
    }

    private handleSelectValue(value: unknown): Array<unknown> {
        if (!value) return [];
        return isArray(value) ? value : [value];
    }

    ngOnInit(): void {
        this.initializeComponent();

        if ((this.selectFirstValue || this.value) && this.configSelect.url && !this.fetchFirstDone) {
            this.debouncedGetOptions(this.value);
        }
    }

    ngOnChanges(changes: SimpleChanges): void {
        this.handleChanges(changes);
    }

    private initializeComponent() {
        this.initializeFilter();
        this.initializeResultSelect();
        if (this.initValue) {
            this.value = this.initValue;
        }
    }

    private handleChanges(changes: SimpleChanges) {
        if (this.hasChanged(changes, 'configSelect', 'options')) {
            this.updateResultSelectOptions(changes['configSelect'].currentValue?.options);
        }
        if (this.hasChanged(changes, 'configSelect', 'body') || this.hasChanged(changes, 'removeFieldValues')) {
            this.debouncedGetOptions(this.value);
        }
        if (this.hasChanged(changes, 'initValue')) {
            this.updateValue(changes['initValue']?.currentValue, 'initValue');
        }
    }
    // Utility to check if an input property has changed
    private hasChanged(changes: SimpleChanges, key: string, subKey?: string): boolean {
        // Check if the key exists in changes
        if (!changes[key]) {
            return false;
        }

        const currentValue = subKey ? changes[key].currentValue?.[subKey] : changes[key].currentValue;
        const previousValue = subKey ? changes[key].previousValue?.[subKey] : changes[key].previousValue;

        // If both values are null or undefined, no real change
        if ((currentValue === null || currentValue === undefined) && (previousValue === null || previousValue === undefined)) {
            return false;
        }

        // If one value is null/undefined and the other isn't, it's a change
        if ((currentValue === null || currentValue === undefined) !== (previousValue === null || previousValue === undefined)) {
            return true;
        }

        // Handle arrays specifically
        if (Array.isArray(currentValue) && Array.isArray(previousValue)) {
            if (currentValue.length !== previousValue.length) {
                return true;
            }
            return currentValue.some((item, index) => !Common.isDeepEqual(item, previousValue[index]));
        }

        return !Common.isDeepEqual(currentValue, previousValue);
    }

    private updateResultSelectOptions(newOptions: unknown[]) {
        if (newOptions) {
            const newData = unionBy(newOptions, this.objectValue, this.configSelect.fieldValue);
            this.resultSelect = { ...this.resultSelect, data: newData };
        }
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    private updateValue(newValue: unknown, from: string): void {
        // console.log('update value filter from: ', from);
        this.valueSubject.next(newValue);
    }

    // Initialize object filter and set initial value
    private initializeFilter() {
        this.configSelect = {
            ...this.configSelect,
            rsql: this.configSelect.rsql ?? true,
            dataKey: this.configSelect.dataKey ?? this.configSelect.fieldValue ?? 'id',
            options: this.configSelect.options ?? [],
        };
        this.objectFilter = this.tableCommonService.getFilter(this.tableId);
        this.setValueFromTableService();
    }

    // Initialize resultSelect object
    private initializeResultSelect() {
        this.resultSelect = {
            data: this.configSelect.options || [],
            error: null,
            dataUpdatedAt: Date.now(),
            errorUpdateCount: 0,
            errorUpdatedAt: 0,
            failureCount: 0,
            failureReason: null,
            fetchStatus: 'idle',
            isError: false,
            isPending: true,
            isLoadingError: false,
            isRefetchError: false,
            isSuccess: false,
            status: 'pending',
            isFetched: false,
            isFetchedAfterMount: false,
            isFetching: false,
            isInitialLoading: false,
            isLoading: false,
            isPaused: false,
            isPlaceholderData: true,
            isRefetching: false,
            isStale: false,
            refetch: () => Promise.resolve(null),
        };
    }

    // ControlValueAccessor implementation
    writeValue(value: any): void {
        if (Common.isDeepEqual(value, this.value)) return;
        this.updateValueLogic(value);

        if (this.configSelect?.url) {
            this.prioritizeForm = true;
            this.debouncedGetOptions(value);
        }
    }

    registerOnChange(fn: (value: any) => void): void {
        this.onChangeCallback = fn;
    }

    registerOnTouched(fn: () => void): void {
        this.onTouchedCallback = fn;
    }

    setDisabledState(isDisabled: boolean): void {
        this.disabled = isDisabled;
    }

    onValueChange(newValue: Date[] | Date): void {
        if (this.type === 'date-range') {
            this.configDate.filter = newValue;
        }
        this.updateValue(newValue, 'change input');
        if (this.configSelect.url && !['select', 'select-one'].includes(this.type)) {
            this.fetchOptions(null);
        }
        this.onTouchedCallback();
    }

    onSelectMonth(event: Date) {
        this.updateValue(event ? new Date(event.getFullYear(), event.getMonth(), 1) : null, 'change select month');
    }

    onChangeDropdown(event: DropdownChangeEvent) {
        if (this.type === 'select-one' && isArray(this.resultSelect.data)) {
            this.objectValue = this.resultSelect.data?.filter((item) => item[this.configSelect.fieldValue] === event.value);
        }
        this.updateValue(event.value, 'change select');
        this.onTouchedCallback();
    }

    onSelectChange(event: MultiSelectChangeEvent) {
        this.value = event.value;
        this.updateObjectValue();
        this.updateValue(event.value, 'change select');
        this.onTouchedCallback();
    }

    onDropdowShow(): void {
        if (!this.configSelect.url || this.fetchFirstDone) {
            return;
        }
        this.fetchOptions(null);
    }

    onDropdownSearch(event: DropdownFilterEvent): void {
        this.searchValue = event.filter;
        if (this.configSelect.filterLocal && this.fetchFirstDone) return;
        if (this.configSelect?.url) {
            this.debouncedGetOptions(event.filter);
        }
    }

    remove(event: MouseEvent, object: unknown): void {
        event.stopPropagation();

        // Lọc ra những đối tượng không phải là đối tượng cần xóa
        event.stopPropagation();
        this.objectValue = this.objectValue.filter((item) => item[this.configSelect.fieldValue] !== object[this.configSelect.fieldValue]);
        if (isArray(this.value)) {
            this.updateValue(
                this.value.filter((item: unknown) => item !== object[this.configSelect.fieldValue]),
                'change select',
            );
        }
        this.onTouchedCallback();
    }

    onSelectClear(value: unknown) {
        this.updateValue(value, 'change select');
        this.onTouchedCallback();
    }

    onDateHide() {
        this.updateValue(this.configDate.filter, 'change date hide');
        this.onTouchedCallback();
    }

    private applyFilter(value: unknown) {
        const filterValue = value === null ? undefined : value;
        this.onChange.emit({ value: filterValue, objects: this.objectValue });

        if (!this.tableId) return;
        const newFilter = { [this.field]: filterValue };
        if (this.rsql) {
            this.tableCommonService.updateFilterRSQL(this.tableId, newFilter);
        } else {
            this.tableCommonService.updateFilter(this.tableId, newFilter);
        }
    }

    private setValueFromTableService(): void {
        if (!this.tableId || !this.objectFilter) return;
        this.value =
            this.rsql && this.objectFilter.rsql?.[this.field] !== undefined ? this.objectFilter.rsql[this.field] : this.objectFilter.native?.[this.field];
        if (this.type === 'date-range' && this.value) {
            this.configDate.filter = this.value as Date[];
        }

        this.isHasValueUrl = this.checkHasValue(this.value);
    }
    private checkHasValue(value: unknown): boolean {
        if (!value) return false;
        if (isArray(value)) return value.length > 0;
        if (typeof value === 'string') return value.trim().length > 0;
        return typeof value === 'number' || typeof value === 'boolean' || typeof value === 'bigint' || typeof value === 'symbol';
    }
    getOption(configSelect: ConfigSelectProps, value: unknown) {
        const pageable = `&page=${configSelect.page || '0'}&size=${configSelect.size || '100'}&sort=${configSelect.sort || 'id,desc'}`;
        return configSelect.rsql
            ? this.#http.get<unknown[]>(this.createRsqlUrlParams(configSelect, value) + pageable)
            : this.#http.post<unknown[]>(`${configSelect.url}?${pageable}`, this.createPostBody(configSelect, value));
    }

    private createRsqlUrlParams(configSelect: ConfigSelectProps, value: unknown): string {
        const keyFilter = this.prioritizeForm
            ? configSelect.paramForm ?? configSelect.param ?? configSelect.fieldValue ?? configSelect.fieldLabel
            : configSelect.param ?? configSelect.fieldLabel;

        const filters = this.buildRsqlFilters(keyFilter, value, configSelect.body);
        if (isArray(this.removeFieldValues) && this.removeFieldValues.length) {
            filters.push(`${this.removeField}=out=(${this.removeFieldValues.join(',')})`);
        }
        const additional = configSelect.additionalCondition && !this.prioritizeForm ? (filters.length ? ';' : '') + configSelect.additionalCondition : '';
        return `${this.configSelect.url}?query=${filters.join(';')}${additional}`;
    }

    private buildRsqlFilters(keyFilter: string, value: unknown, body?: Record<string, unknown>): string[] {
        const filters: string[] = [];
        if (isArray(value) && value.length) {
            const searchValue = value.every((item) => typeof item === 'number') ? value.join(',') : value.map(encodeURIComponent).join('","');
            filters.push(`${keyFilter}=in=(${searchValue})`);
        } else if (typeof value === 'string' && value.trim()) {
            filters.push(`${keyFilter}=='*${value.trim()}*'`);
        } else if (typeof value === 'number') {
            filters.push(`${keyFilter}==${value}`);
        }
        if (body) {
            for (const [key, val] of Object.entries(body)) {
                if (isArray(val) && val.length) {
                    const searchValue = val.every((item) => typeof item === 'number') ? val.join(',') : val.map(encodeURIComponent).join('","');
                    filters.push(`${key}=in=(${searchValue})`);
                } else if (val === 'null') {
                    filters.push(`${key}==${val}`);
                } else if (val === '!=null') {
                    filters.push(`${key}!=null`);
                } else if (typeof val === 'string' && val.trim()) {
                    filters.push(`${key}==*${val.trim()}*`);
                } else if (typeof val === 'number') {
                    filters.push(`${key}==${val}`);
                }
            }
        }
        return filters;
    }

    private createPostBody(configSelect: ConfigSelectProps, value: unknown): unknown {
        const body = { ...configSelect.body };
        const param =
            this.isHasValueUrl && !this.countCallApi
                ? configSelect.paramForm ?? configSelect.param ?? configSelect.fieldLabel
                : configSelect.param ?? configSelect.fieldLabel;
        if (param) {
            body[param] = (isArray(value) || typeof value === 'string') && value.length ? value : undefined;
            if (body[param] instanceof Date) {
                body[param] = (body[param] as Date).getTime() + 86399999; // End of day
            } else if (this.isDateRange(body[param])) {
                const [start, end] = body[param] as Date[];
                const splitKey = param.includes('&') ? param.split('&') : [];
                if (splitKey[0]) body[splitKey[0]] = start.getTime();
                if (splitKey[1]) body[splitKey[1]] = end.getTime() + 86399999;
            } else if (typeof value === 'number' && value) {
                body[param] = value;
            }
        }
        return Object.fromEntries(Object.entries(body).map(([k, v]) => [k, isArray(v) && !v.length ? null : v]));
    }

    fetchOptions(searchValue: unknown, prioritizeForm: boolean = false): void {
        if (!this.configSelect?.url) return;
        if (this.requiredFilter.some((filter) => !this.configSelect.body?.[filter])) return;

        this.#query({
            queryKey: [
                this.configSelect.url,
                this.configSelect.fieldValue,
                this.configSelect.fieldLabel,
                this.configSelect.param,
                this.configSelect.body,
                searchValue,
                this.removeFieldValues,
            ],
            queryFn: () => this.getOption(this.configSelect, searchValue),
            refetchOnWindowFocus: false,
            staleTime: 0,
        }).result$.subscribe((res) => this.handleFetchResult(res, prioritizeForm));
    }

    private handleFetchResult(res: QueryObserverBaseResult<unknown, Error>, prioritizeForm: boolean): void {
        if (res.isFetching) return;

        let data = unionBy(this.objectValue, res.data as unknown[], this.configSelect.fieldValue || 'id');
        if (this.removeNullLabel) {
            data = data.filter((item) => item[this.configSelect.fieldValue || 'id'] !== null);
        }

        this.resultSelect.data = data;
        if (this.selectFirstValue && !this.searchValue) {
            this.onApiCallback.emit(data);
        }

        if (prioritizeForm || (this.isHasValueUrl && !this.countCallApi)) {
            this.updateObjectValue();
        }

        if (prioritizeForm) {
            this.onChange.emit({ value: this.value, objects: this.objectValue });
            this.prioritizeForm = false;
        }
        if (this.value && this.countCallApi === 0) {
            this.needMoreOption = true;
        }

        if (this.needMoreOption) {
            this.fetchOptions(null);
            this.needMoreOption = false;
        }
        this.countCallApi++;
        this.fetchFirstDone = true;
    }

    isDateRange(value: unknown): boolean {
        return isArray(value) && value.length === 2 && (isDate(value[0]) || isDate(value[1]));
    }

    private updateObjectValue(): void {
        const data = this.resultSelect.data as any;
        if (this.value) {
            const values = isArray(this.value) ? this.value : [this.value];
            this.objectValue = values.map((val) => data.find((option) => get(option, this.configSelect.fieldValue) === val)).filter((option) => option);
        } else {
            this.objectValue = [];
        }
    }
}
