<p-panel header="Lịch sử giao dịch" [toggleable]="true">
    <ng-template pTemplate="icons">
        <span class="tw-mr-4">
            <span>Tổng giá trị mua hàng: </span>
            <ng-container *ngIf="hasViewPriceRole; else noRoleViewPriceTotalBuy">
                <span>{{ unitPrice === 'VNĐ' ? 'đ' : '$' }} {{ totalValue | number }}</span>
            </ng-container>
            <ng-template #noRoleViewPriceTotalBuy>
                <span>****</span>
            </ng-template>
        </span>
        <i class="pi pi-refresh" style="color: var(--primary-color)" (click)="stateListPo.refetch()"></i>
    </ng-template>
    <ng-template pTemplate="content">
        <div class="tw-flex tw-justify-end"></div>
        <div class="tw-grid lg:tw-grid-cols-5 md:tw-grid-cols-1 sm:tw-grid-cols-1 tw-gap-4 tw-mb-3">
            <div class="tw-flex tw-flex-col tw-space-y-3">
                <div class="tw-font-bold">Ngày</div>
                <div>
                    <p-calendar
                        [(ngModel)]="selectedDateFilter"
                        [showIcon]="true"
                        [showOnFocus]="false"
                        dateFormat="dd/mm/yy"
                        inputId="buttondisplay"
                        (ngModelChange)="selectOrderDate($event)"
                    ></p-calendar>
                </div>
            </div>
            <div class="tw-flex tw-flex-col tw-space-y-3">
                <div class="tw-font-bold">Mã đơn hàng</div>
                <div>
                    <app-filter-table
                        field="orderNo"
                        rsql="true"
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'orderNo',
                            rsql: true,
                            param: 'orderNo',
                            body: {
                                supplierId: supplierId,
                                state: [0, 1, 2, 3],
                            },
                            url: '/sc/api/po/search',
                        }"
                        (onChange)="selectOrderCode($event)"
                        placeholder="Mã đơn hàng"
                        #filerArea
                    ></app-filter-table>
                </div>
            </div>
        </div>
        <p-table
            styleClass="p-datatable-gridlines "
            [scrollable]="true"
            [value]="stateListPo.data?.body"
            [loading]="stateListPo.isFetching"
            scrollHeight="500px"
        >
            <ng-template pTemplate="header">
                <tr>
                    <th>Ngày</th>
                    <th>Mã đơn hàng</th>
                    <th>Tổng giá trị</th>
                    <th>Tình trạng giao hàng</th>
                </tr>
            </ng-template>

            <ng-template pTemplate="body" let-po>
                <tr>
                    <td>{{ po.orderDate | date: 'dd/MM/yyyy' }}</td>
                    <td>
                        <a [routerLink]="'/sc/po/' + po.id">{{ po.orderNo }}</a>
                    </td>
                    <ng-container *ngIf="hasViewPriceRole; else noRoleViewPricePoTotal">
                        <td>{{ po.totalValue | number }} {{ unitPrice === 'VNĐ' ? 'đ' : '$' }}</td>
                    </ng-container>
                    <ng-template #noRoleViewPricePoTotal>
                        <td class="">
                            ****
                        </td>
                    </ng-template>
                    <td><p-tag [severity]="getSeverity(po)" [value]="getValueTagState(po)" /></td>
                </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="30" class="text-center">Không có dữ liệu</td>
                </tr>
            </ng-template>
        </p-table>
    </ng-template>
</p-panel>
<br />
<p-panel header="Thông tin vật tư" [toggleable]="true">
    <ng-template pTemplate="icons">
        <i class="pi pi-refresh" style="color: var(--primary-color)" (click)="stateListMaterial.refetch()"></i>
    </ng-template>
    <ng-template pTemplate="content">
        <p>Đã phát sinh giao dịch</p>
        <p-table
            styleClass="p-datatable-gridlines "
            [scrollable]="true"
            [value]="stateListMaterial.data"
            [loading]="stateListMaterial.isFetching"
            scrollHeight="500px"
        >
            <ng-template pTemplate="header">
                <tr>
                    <th>VNPT/PN</th>
                    <th>Mô tả</th>
                    <th>Tổng giá trị đơn hàng</th>
                    <th>Đơn giá gần nhất</th>
                    <th>Thao tác</th>
                </tr>
            </ng-template>

            <ng-template pTemplate="body" let-po>
                <tr>
                    <td>{{ po.internalReference }}</td>
                    <td>{{ po.description }}</td>
                    <ng-container *ngIf="hasViewPriceRole; else noRoleViewPriceTotal">
                        <td>{{ po.total | currency: (po?.unit === 'USD' ? 'USD' : 'VND') : 'symbol' : '1.0-4' }}</td>
                    </ng-container>
                    <ng-template #noRoleViewPriceTotal>
                        <td class="">
                            ****
                        </td>
                    </ng-template>
                    <ng-container *ngIf="hasViewPriceRole; else noRoleViewPrice">
                        <td>{{ po.price | currency: (po?.unit === 'USD' ? 'USD' : 'VND') : 'symbol' : '1.0-4' }}</td>
                    </ng-container>
                    <ng-template #noRoleViewPrice>
                        <td class="">
                            ****
                        </td>
                    </ng-template>
                    <td>
                        <span *ngIf="hasViewPriceRole" class="tw-p-2 hover:tw-bg-blue-300 tw-rounded-full" (click)="viewChart(po)">
                            <i class="pi pi-chart-line" style="color: var(--primary-color)"></i
                        ></span>
                    </td>
                </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="30" class="text-center">Không có dữ liệu</td>
                </tr>
            </ng-template>
        </p-table>
        <br />
        <p>Danh sách vật tư NCC có thể cung cấp</p>
        <p-table styleClass="p-datatable-gridlines " [scrollable]="true" [value]="provideItems" scrollHeight="500px">
            <ng-template pTemplate="header">
                <tr>
                    <th>VNPT/PN</th>
                    <th>Mã NSX</th>
                    <th>Mô tả</th>
                    <th>Nhà sản xuất</th>
                </tr>
            </ng-template>

            <ng-template pTemplate="body" let-item>
                <tr>
                    <td>{{ item.internalReference }}</td>
                    <td>{{ item.manPn }}</td>
                    <td>{{ item.description }}</td>
                    <td>{{ item.erpManufacturer?.name }}</td>
                </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="30" class="text-center">Không có dữ liệu</td>
                </tr>
            </ng-template>
        </p-table>
    </ng-template>
</p-panel>
<p-dialog [header]="'Biến động giá ' + internalReference" [(visible)]="visible" [modal]="true" [style]="{ width: '80%' }">
    <app-skeleton-loading [isLoading]="stateListMaterialChange?.isFetching">
        <div class="tw-mt-8">
            <canvas style="width: 100%" #templateChartLine></canvas>
        </div>
    </app-skeleton-loading>
</p-dialog>
