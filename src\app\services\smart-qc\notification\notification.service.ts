import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ParamsTable } from '../../../shared/table-module/table.common.service';
import { Contract } from '../../../models/interface/smart-qc';
import { MarkAsReadDTO } from '../../../models/notification/markAsReadDTO';

@Injectable({
    providedIn: 'root',
})
export class NotificationService {
    constructor(private http: HttpClient) {}

    getPage(params: string) {
        return this.http.get<unknown[]>('/smart-qc/api/notification/search?' + params, {
            observe: 'response',
        });
    }

    fetchNewNumberNotification() {
        return this.http.get<number>('/smart-qc/api/notification/fetch-new', {
            observe: 'response',
        });
    }

    getPageTableCustom({ native = '', pageable = '&page=0&size=10' }: ParamsTable) {
        return this.http.get<Contract[]>(`/smart-qc/api/notification/search-custom?query=${native}${pageable}`, {
            observe: 'response',
        });
    }

    getPageTableCustomPost({ pageable }: ParamsTable, body) {
        return this.http.post<Contract[]>(`/smart-qc/api/notification/search-custom?${pageable}`, body, {
            observe: 'response',
        });
    }

    batchDelete(ids: number[]) {
        return this.http.post<unknown[]>(`/smart-qc/api/notification-receiver/batch-delete`, ids);
    }

    markAsRead(markAsReadDTO: MarkAsReadDTO) {
        return this.http.post<unknown[]>(`/smart-qc/api/notification/mark-as-read`, markAsReadDTO);
    }

    deleteAll() {
        return this.http.post<unknown[]>(`/smart-qc/api/notification/delete-all`, null);
    }
}
