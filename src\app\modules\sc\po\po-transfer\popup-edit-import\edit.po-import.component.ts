import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { isEmpty, isEqual } from 'lodash';
import { ConfirmationService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { DialogModule } from 'primeng/dialog';
import { DropdownChangeEvent, DropdownModule } from 'primeng/dropdown';
import { InputNumberModule } from 'primeng/inputnumber';
import { InputTextModule } from 'primeng/inputtext';
import { MultiSelectModule } from 'primeng/multiselect';
import { PanelModule } from 'primeng/panel';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';
import { MAP_TRANSFER_STATE, TRANSFER_STATE_OBJECT, UnitPrice, UnitPriceMappingValue } from 'src/app/models/constant/sc';
import { CanImportDTO, Po, PoDetail, PoDetailDTO, PoTransfer, PoTransferItem } from 'src/app/models/interface/sc';
import { PoDetailService } from 'src/app/services/sc/po/po-detail.service';
import { PoTransferItemService } from 'src/app/services/sc/po/po-transfer-item.service';
import { PoTransferService } from 'src/app/services/sc/po/po-transfer.service';
import { AttributeAuthorityDirective } from 'src/app/shared/directives/attribute-authority.directive';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { AlertService } from 'src/app/shared/services/alert.service';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { TrimmedFormControl } from 'src/app/utils/form';
import { focQuantityValidator, maxValidator, nonEmptyArrayValidator } from 'src/app/utils/validator';
import Common from '../../../../../utils/common';
import { ButtonGroupFileComponent } from 'src/app/shared/components/button-group-file/button-group-file.component';
import { ApiResponse } from 'src/app/models/interface';
import { FileService } from 'src/app/shared/services/file.service';
import { DateUtils } from '../../../../../utils/date-utils';
import { AuthService } from '../../../../../core/auth/auth.service';

@Component({
    selector: 'app-edit-po-transfer',
    templateUrl: './edit.po-import.component.html',
    standalone: true,
    imports: [
        TableModule,
        InputTextModule,
        DropdownModule,
        CalendarModule,
        DialogModule,
        ReactiveFormsModule,
        InputNumberModule,
        FormCustomModule,
        PanelModule,
        ButtonModule,
        CommonModule,
        TooltipModule,
        MultiSelectModule,
        TableCommonModule,
        TagModule,
        AttributeAuthorityDirective,
        ButtonGroupFileComponent,
    ],
    providers: [PoTransferService, PoTransferItemService, PoDetailService],
})
export class EditPoTransferComponent implements OnInit {
    @Input() po: Po;
    @Output() updatePoDetails = new EventEmitter<PoDetailDTO>();
    @Input() poTransfer: PoTransfer;
    @Input() visible: boolean = false;
    @Output() onClose = new EventEmitter<boolean>();
    @Output() onSuccess = new EventEmitter(); // Emits selected file(s)

    formGroup: FormGroup;
    isEditing: boolean = false;
    isAdding: boolean = false;
    isChangeDate: boolean = false;
    originDate: number;
    loading: boolean = false;
    optionAccounting: { label: string; value: string }[] = [];
    optionPoDetail: CanImportDTO[] = [];
    optionPoDetailOrigin: CanImportDTO[] = [];
    valueAccountingInit: string[] = [];
    mapStateTransfer = MAP_TRANSFER_STATE;
    TRANSFER_STATE_OBJECT = TRANSFER_STATE_OBJECT;
    backUpPoTransferItem: PoTransferItem;
    isDisabled: boolean = false;
    isCanCancel: boolean = false;
    quantityByLevel: Map<number, { quantity: number; focQuantity: number }>;
    quantityByLevelOrigin: Map<number, { quantity: number; focQuantity: number }>;
    internalReferenceByLevelMap?;
    isAdmin: boolean = false;

    // object disabled các mã vnpt đã chọn
    poDetailDisabled: Record<string, CanImportDTO> = {};
    poTransferItemsInit: PoTransferItem[] = [];

    unitPrice = UnitPrice;
    unitPriceMapping = UnitPriceMappingValue;

    internalReferenceUnitPriceMap: { [key: string]: string } = {};

    unitPriceValue: number = 0;
    unitPriceString: string = 'USD';

    // uploadFIle
    urlError: string;
    poTransferItemsOldData: PoTransferItem[];
    poTransferItemsDelete: number[];
    isImportFile: boolean = false; // Check if user import file

    manufacturerCodeMap = new Map<string, string>();
    descriptionMap = new Map<string, string>();

    // Custom message
    customMessage = {
        focQuantityInvalid: '',
    };

    constructor(
        private fb: FormBuilder,
        private poTransferService: PoTransferService,
        private poDetailService: PoDetailService,
        private poTransferItemService: PoTransferItemService,
        private alertService: AlertService,
        private loadingService: LoadingService,
        private confirmationService: ConfirmationService,
        private fileService: FileService,
        private authService: AuthService,
    ) {}

    ngOnInit(): void {
        this.initForm(this.poTransfer, this.po);

        this.internalReferenceByLevelMap = this.po.internalReferenceByLevelMap;

        if (this.poTransfer && this.poTransfer?.id) {
            this.isDisabled =
                this.poTransfer && this.poTransfer.state !== TRANSFER_STATE_OBJECT.NEW && this.poTransfer.state !== TRANSFER_STATE_OBJECT.PROCESSING;
            this.isCanCancel = this.poTransfer && this.poTransfer.state === TRANSFER_STATE_OBJECT.PROCESSING;

            this.poTransferItemService.getAll(`query=transferId==${this.poTransfer.id}`).subscribe({
                next: (value) => {
                    this.poTransfer.poTransferItems = value.body;
                    this.poTransferItemsInit = value.body;
                    this.poTransferItemsOldData = value.body;
                    this.poTransfer.isUpdatePoDetail = false;
                    this.loading = false;
                    this.initForm(this.poTransfer, this.po);
                    this.getOptionsPoDetail();
                },
            });
        } else {
            this.getOptionsPoDetail();
        }
        this.po.poDetails.map((d) => {
            if (!this.valueAccountingInit.includes(d.accountingCode)) {
                this.valueAccountingInit.push(d.accountingCode);
                this.optionAccounting.push({ label: d.accountingCode, value: d.accountingCode });
            }
        });
        if (!this.poTransfer) {
            this.poTransfer = this.formGroup.getRawValue();
            this.poTransfer.poTransferItemsDelete = [];
            this.poTransfer.poId = this.po.id;
            this.poTransfer.accountingCode = this.valueAccountingInit.join('*');
        }

        this.po.poDetails.forEach((detail) => {
            this.internalReferenceUnitPriceMap[detail.internalReference] = detail.unitPrice;
        });
        if (this.po.poDetails.length > 0) {
            // Set Unit Price as Po Detail Unit Price first
            this.unitPriceValue = this.unitPriceMapping[this.po.poDetails[0].unitPrice];
            this.unitPriceString = this.po.poDetails[0].unitPrice;
            //console.log(this.unitPriceValue);
        }

        //console.log(this.internalReferenceUnitPriceMap)

        this.po.poBoqQuantityLst?.forEach((quantity) => {
            quantity.poBoqList?.forEach((boq) => {
                this.manufacturerCodeMap.set(boq.productName, boq.manufacturerCode);
                this.descriptionMap.set(boq.description, boq.description);
            });
        });

        // Check admin
        this.isAdmin = this.authService.isAdmin();
    }

    initForm(poTransfer: PoTransfer | null, po: Po) {
        //console.log(poTransfer)
        this.formGroup = this.fb.group({
            poId: new FormControl({
                value: poTransfer ? poTransfer.poId : po.id,
                disabled: this.isDisabled,
            }),
            type: new FormControl({
                value: poTransfer ? poTransfer.type : 0,
                disabled: this.isDisabled,
            }),
            contract: new FormControl({
                value: poTransfer ? poTransfer.contract : null,
                disabled: this.isDisabled,
            }),
            invTransferNumber: new FormControl({
                value: poTransfer ? poTransfer?.invTransferNumber : null,
                disabled: true,
            }),
            accountingCodeCustom: new FormControl({
                value: poTransfer ? (poTransfer.accountingCode ? poTransfer.accountingCode.split('*') : null) : this.valueAccountingInit,
                disabled: this.isDisabled,
            }),
            dateCustom: new FormControl({
                value: poTransfer?.date ? new Date(poTransfer.date) : new Date(),
                disabled: this.isDisabled && !this.isAdmin,
            }),
            orderNo: new FormControl({ value: po.orderNo, disabled: true }),
            warehouse: new TrimmedFormControl({ value: poTransfer?.warehouse ?? 'Hòa lạc', disabled: this.isDisabled }),
            payment: new FormControl({ value: poTransfer?.payment, disabled: this.isDisabled }),
            supplierName: new TrimmedFormControl({
                value: po.supplierName,
                disabled: true,
            }),
            poTransferItems: this.fb.array(this.initPoTransferItems(poTransfer?.poTransferItems || []), [nonEmptyArrayValidator()]),
            createdUser: new FormControl({ value: poTransfer?.createdUser, disabled: this.isDisabled }),
        });
        this.originDate = poTransfer?.date;

        this.formGroup.get('dateCustom')?.valueChanges.subscribe((value) => {
            const poTransfer: PoTransfer = this.formGroup.getRawValue();
            const date = value ? DateUtils.convertToTimestampHCM(value) : null;
            /*console.log(date);
            console.log("Origin date ", this.originDate);
            console.log("Changed date ", value);*/
            this.isChangeDate = date !== this.originDate;
            /*console.log("Is changed ", this.isChangeDate);*/
        });
    }

    getOptionsPoDetail() {
        this.poDetailService
            .getCanImport({
                poId: this.po.id,
                accountingCodes: this.poTransfer?.accountingCodeCustom,
            })
            .subscribe({
                next: (res) => {
                    this.optionPoDetail = res;
                    this.optionPoDetailOrigin = res;
                    /*console.log(this.optionPoDetail)
                    console.log(this.optionPoDetailOrigin)*/
                    this.quantityByLevel = res.reduce((map, item) => {
                        if (!map.has(item.indexLevel)) {
                            map.set(item.indexLevel, { quantity: 0, focQuantity: 0 });
                        }
                        const current = map.get(item.indexLevel)!;
                        current.quantity = item.quantity;
                        current.focQuantity = item.focQuantity;
                        return map;
                    }, new Map<number, { quantity: number; focQuantity: number }>());
                    this.quantityByLevelOrigin = new Map(Array.from(this.quantityByLevel, ([key, value]) => [key, { ...value }]));

                    this.updateQuantityByLevelOriginOnInit();
                    this.removeItemsFromOptionPoDetail();
                    this.updateDisabledOnInit();
                },
            });
    }

    private updateQuantityByLevelOriginOnInit() {
        this.poTransferItems.controls.forEach((control) => {
            const itemValue = control.getRawValue();
            const indexLevel = itemValue.indexLevel;
            const quantity = itemValue.quantity ?? 0;
            const focQuantity = itemValue.focQuantity ?? 0;

            if (this.quantityByLevelOrigin.has(indexLevel)) {
                // Đã có rồi -> cộng dồn
                const existing = this.quantityByLevelOrigin.get(indexLevel)!;
                existing.quantity += quantity;
                existing.focQuantity += focQuantity;

                // Cập nhật lại vào Map (không bắt buộc vì object reference, nhưng clear cho chắc)
                this.quantityByLevelOrigin.set(indexLevel, existing);
            } else {
                // Chưa có -> tạo mới
                this.quantityByLevelOrigin.set(indexLevel, {
                    quantity: quantity,
                    focQuantity: focQuantity,
                });
            }
        });
    }

    private updateDisabledOnInit() {
        this.poDetailDisabled = {};
        /*this.poTransferItems.controls.forEach((control, index) => {
            const itemValue = control.getRawValue();
            const internalRef = itemValue.internalReference;

            this.poDetailDisabled[internalRef] = {
                indexLevel: itemValue.indexLevel,
                internalReference: internalRef,
                quantity: !isImportFile ? (itemValue.quantity ?? 0) : 0,
                focQuantity: itemValue.focQuantity ?? 0,
                price: itemValue.price,
                amount: itemValue.amount,
                description: itemValue.description,
                display: `${internalRef} - ${itemValue.quantity}`,
            };
        });*/
        this.poTransferItemsInit.forEach((itemValue) => {
            const internalRef = itemValue.internalReference;

            this.poDetailDisabled[internalRef] = {
                indexLevel: itemValue.indexLevel,
                internalReference: internalRef,
                quantity: itemValue.quantity ?? 0,
                focQuantity: itemValue.focQuantity ?? 0,
                price: itemValue.price,
                amount: itemValue.amount,
                description: itemValue.description,
                display: `${internalRef} - ${itemValue.quantity}`,
            };
        });

        if (this.optionPoDetailOrigin) {
            this.optionPoDetailOrigin.forEach((po) => {
                const internalRef = po.internalReference;

                if (this.poDetailDisabled[internalRef]) {
                    this.poDetailDisabled[internalRef].quantity += po.quantity ?? 0;
                    this.poDetailDisabled[internalRef].focQuantity += po.focQuantity ?? 0;

                    // Cập nhật lại display để reflect quantity mới
                    this.poDetailDisabled[internalRef].display = `${internalRef} - ${this.poDetailDisabled[internalRef].quantity}`;
                }
            });
        }
    }

    removeItemsFromOptionPoDetail(): void {
        const internalRefsToRemove = this.poTransferItems.controls.map((control) => control.get('internalReference')?.value);

        this.optionPoDetail = this.optionPoDetail.filter((item) => !internalRefsToRemove.includes(item.internalReference));
    }

    initPoTransferItems(items: PoTransferItem[]): FormGroup[] {
        return items.map((item) => {
            const group = this.fb.group({
                id: [item?.id],
                transferId: [item?.transferId],
                created: [item?.created],
                updated: [item?.updated],
                createdBy: [item?.createdBy],
                updatedBy: [item?.updatedBy],
                tenantId: [item?.tenantId],
                active: [item?.active],
                transferType: [0],
                indexLevel: [item?.indexLevel, []],
                internalReference: [item?.internalReference, Validators.required],
                manufacturerCode: [item?.manufacturerCode ?? null],
                description: [{ value: item?.description, disabled: true }],
                quantity: [item?.quantity, [Validators.required, Validators.min(1)]],
                focQuantity: [item?.focQuantity, [Validators.min(0), focQuantityValidator]],
                doneQuantity: [item?.doneQuantity],
                price: [{ value: item?.price, disabled: true }, [Validators.required, Validators.min(0)]],
                amount: [{ value: item?.amount, disabled: true }, Validators.required],
                note: [item?.note],
                dateCode: [item?.dateCode],
                dateCodeCustom: new FormControl({
                    value: item?.dateCode ? new Date(item.dateCode) : null,
                    disabled: this.isDisabled,
                }),
                isEdit: [false],
                unitPrice: this.unitPriceValue,
            });

            group.get('dateCodeCustom')?.valueChanges.subscribe((value: Date) => {
                if (value != null) {
                    const timestamp = DateUtils.convertToTimestampHCM(value);
                    group.get('dateCode')?.setValue(timestamp);
                }
            });

            return group;
        });
    }

    get poTransferItems(): FormArray {
        return this.formGroup.get('poTransferItems') as FormArray;
    }

    addPoTransferItem(): void {
        const newItem = this.fb.group({
            transferId: [this.poTransfer?.id],
            internalReference: [null, [Validators.required]],
            manufacturerCode: [{ value: null, disabled: true }, []],
            indexLevel: [{ value: null }, []],
            description: [{ value: null, disabled: true }, []],
            quantity: [null, [Validators.required, Validators.min(1)]],
            focQuantity: [null, [Validators.min(0), focQuantityValidator('Số lượng FOC không vượt quá tổng số lượng')]],
            price: [{ value: null, disabled: true }, [Validators.required]],
            amount: [{ value: null, disabled: true }, [Validators.required]],
            note: [null],
            dateCode: [null],
            dateCodeCustom: new FormControl({
                value: null,
                disabled: this.isDisabled,
            }),
            isEdit: [true],
            transferType: [0],
        });
        newItem.get('dateCodeCustom')?.valueChanges.subscribe((value: Date) => {
            if (value != null) {
                const timestamp = DateUtils.convertToTimestampHCM(value);
                newItem.get('dateCode')?.setValue(timestamp);
            }
        });
        this.poTransferItems.push(newItem);
        this.isAdding = true;

        newItem.get('quantity').valueChanges.subscribe({
            next: (value) => {
                this.handleChangeQuantity(
                    newItem.get('quantity') as FormControl,
                    newItem.get('focQuantity') as FormControl,
                    newItem.get('amount') as FormControl,
                    value,
                    newItem.get('price').getRawValue(),
                );
            },
        });
        newItem.get('focQuantity').valueChanges.subscribe({
            next: (value) => {
                this.handleChangeFocQuantity(
                    newItem.get('quantity') as FormControl,
                    newItem.get('amount') as FormControl,
                    value,
                    newItem.get('price').getRawValue(),
                );
            },
        });
    }

    editTransferItem(index: number): void {
        const item = this.poTransferItems.at(index);
        this.backUpPoTransferItem = item.getRawValue();
        item.patchValue({ isEdit: true });
        this.isEditing = true;
        item.get('price').disable();
        item.get('amount').disable();
        item.get('description').disable();
        this.updateOptionsOnEdit(item);

        item.get('quantity').valueChanges.subscribe({
            next: (value) => {
                this.handleChangeQuantity(
                    item.get('quantity') as FormControl,
                    item.get('focQuantity') as FormControl,
                    item.get('amount') as FormControl,
                    value,
                    item.get('price').getRawValue(),
                );
            },
        });
        item.get('focQuantity').valueChanges.subscribe({
            next: (value) => {
                this.handleChangeFocQuantity(item.get('quantity') as FormControl, item.get('amount') as FormControl, value, item.get('price').getRawValue());
            },
        });

        // this.updateQuantityOptions(true);
    }

    private updateOptionsOnEdit(item: AbstractControl) {
        const internalReference = item.get('internalReference')?.getRawValue();

        const exists = this.optionPoDetail?.some((po) => po.internalReference === internalReference);

        if (!exists && this.optionPoDetail) {
            const canImportDTO = this.poDetailDisabled[internalReference]; // lấy từ Record thay vì find trong array

            if (canImportDTO) {
                this.optionPoDetail.push(canImportDTO);

                // Update Validator
                const poDetail = canImportDTO;

                // Đặt validator cho trường quantity
                const quantityControl = item.get('quantity');
                const max = poDetail.quantity !== null ? poDetail.quantity : 1;

                quantityControl.setValidators([Validators.required, Validators.min(1), Validators.max(max)]);
                quantityControl.updateValueAndValidity();

                // Đặt validator cho trường focQuantity
                const focQuantityControl = item.get('focQuantity');
                const maxFoc = this.getFocQuantityByLevel(item.get('indexLevel')?.getRawValue());
                focQuantityControl.setValidators([
                    Validators.min(0),
                    maxValidator(maxFoc, `Số lượng FOC phải <= ${maxFoc}`),
                    focQuantityValidator(`Số lượng FOC phải <= ${max}`),
                ]);
                focQuantityControl.updateValueAndValidity();
                this.sortOptionsByLevel();
            }
        }
    }

    saveTransferItem(index: number): void {
        const item = this.poTransferItems.at(index);

        const itemValue = item.getRawValue();
        // Kiểm tra tính hợp lệ của item
        if (item.valid) {
            item.enable();

            // Cập nhật isEdit thành false
            if (itemValue.focQuantity == null) {
                itemValue.focQuantity = 0;
            }
            item.setValue({ ...itemValue, isEdit: false });

            // Đặt trạng thái isEditing thành false
            this.isEditing = false;
            this.isAdding = false;

            this.removeInternalReferenceFromOptions(itemValue);

            this.poTransfer.isUpdatePoDetail = true;
            this.updateQuantityOptions();
        }
    }

    private removeInternalReferenceFromOptions(itemValue) {
        let targetIndex = -1;
        let targetElement: CanImportDTO;

        for (let i = 0; i < this.optionPoDetail.length; i++) {
            if (this.optionPoDetail[i].internalReference === itemValue.internalReference) {
                targetIndex = i;
                targetElement = this.optionPoDetail[i];
                break;
            }
        }

        if (targetIndex !== -1) {
            this.optionPoDetail.splice(targetIndex, 1);
        }

        this.poDetailDisabled[targetElement?.internalReference] = targetElement;
    }

    cancelCreate(index: number): void {
        if (this.isAdding) {
            this.poTransferItems.removeAt(index);
        } else if (this.isEditing) {
            this.poTransferItems.at(index).enable();
            this.poTransferItems.at(index).patchValue(this.backUpPoTransferItem);
            this.removeInternalReferenceFromOptions(this.poTransferItems.at(index)?.getRawValue());
            this.updateQuantityOptions();
        }
        this.isAdding = false;
        this.isEditing = false;
    }

    removeTransferItem(index: number): void {
        this.isEditing = false;

        const itemValue = this.poTransferItems.at(index).value as PoTransferItem;

        // console.log(this.poDetailDisabled[itemValue.internalReference])
        this.optionPoDetail.unshift(this.poDetailDisabled[itemValue.internalReference]);
        this.poDetailDisabled[itemValue.internalReference] = null;

        // Xóa mục tại index trong poTransferItems
        /*console.log(this.optionPoDetail)
        console.log(this.quantityByLevel)*/
        this.poTransferItems.removeAt(index);
        this.updateQuantityOptions();
        if (itemValue.id) {
            this.poTransfer.poTransferItemsDelete.push(itemValue.id);
            this.poTransfer.isUpdatePoDetail = true;
        }
    }

    handleSubmit(): void {
        if (this.formGroup.valid) {
            const poTransfer: PoTransfer = this.formGroup.getRawValue();
            //console.log(poTransfer);

            const lstAccountingCode = poTransfer?.accountingCodeCustom ? poTransfer?.accountingCodeCustom : null;
            const date = poTransfer?.dateCustom ? DateUtils.convertToTimestampHCM(poTransfer?.dateCustom) : null;
            this.loadingService.show();
            if (this.isDisabled) {
                // Update date only
                this.updateDatePoTransfer();
            } else {
                if (this.poTransfer.id) {
                    if (!isEqual(date, this.poTransfer.date)) {
                        this.poTransfer.isUpdatePoDetail = true;
                    }

                    if (this.isImportFile) {
                        this.poTransfer.poTransferItemsDelete = this.poTransferItemsDelete ? this.poTransferItemsDelete : [];
                    }

                    this.poTransferService.update({ ...this.poTransfer, ...poTransfer, lstAccountingCode, date, updateItemsFromSc: true }).subscribe({
                        next: () => {
                            this.callApiGetPoDetail();
                        },
                        error: (res) => {
                            this.alertService.handleError(res);
                            this.loadingService.hide();
                        },
                    });
                } else {
                    this.poTransferService.create({ ...this.poTransfer, ...poTransfer, lstAccountingCode, date }).subscribe({
                        next: () => {
                            this.callApiGetPoDetail();
                        },
                        error: (res) => {
                            this.alertService.handleError(res);
                            this.loadingService.hide();
                        },
                    });
                }
            }
        }
    }

    private callApiGetPoDetail() {
        // Call API lay bang ke
        this.poDetailService.getPoDetailByPo(this.poTransfer.poId).subscribe({
            next: (res) => {
                this.updatePoDetails.emit(res as PoDetailDTO);
                this.alertService.success('Thành công');
                this.onSuccess.emit();
                this.closeDialog();
                this.loadingService.hide();
                // console.log(res as PoDetailDTO)
            },
            error: (res) => {
                this.alertService.handleError(res);
                this.loadingService.hide();
            },
        });
    }

    private updateDatePoTransfer() {
        const poTransfer: PoTransfer = this.formGroup.getRawValue();
        const date = poTransfer?.dateCustom ? DateUtils.convertToTimestampHCM(poTransfer?.dateCustom) : null;
        const data = {
            transferId: this.poTransfer.id,
            date: date,
        };
        this.poTransferService.updateDate(data).subscribe({
            next: () => {
                // Call API lay bang ke
                // this.alertService.success('Thành công');
                this.callApiGetPoDetail();
                this.loadingService.hide();
            },
            error: (res) => {
                this.alertService.handleError(res);
                this.loadingService.hide();
            },
        });
    }

    closeDialog() {
        this.visible = false;
        this.onClose.emit(false);
    }

    handleChangeInternal(event: DropdownChangeEvent, index: number) {
        const formControl = this.poTransferItems.at(index);

        if (event.value) {
            const poDetail = this.optionPoDetail.find((p) => p.internalReference === event.value);

            if (poDetail) {
                // Kiểm tra xem poDetail có tồn tại không
                // Cập nhật giá trị của form control
                formControl.patchValue({
                    internalReference: event.value,
                    description: poDetail.description,
                    indexLevel: poDetail.indexLevel,
                    manufacturerCode: this.manufacturerCodeMap.get(event.value) ?? null,
                    quantity: null, // Đặt giá trị mặc định cho quantity để đảm bảo validator hoạt động
                    focQuantity: null,
                    price: poDetail.price ?? 0,
                    amount: null,
                    note: null,
                });

                // Đặt validator cho trường quantity
                const quantityControl = formControl.get('quantity');
                const max = poDetail.quantity !== null ? poDetail.quantity : 1;

                quantityControl.setValidators([Validators.required, Validators.min(1), Validators.max(max)]);
                quantityControl.updateValueAndValidity(); // Cập nhật lại trạng thái validator của quantity

                const focQuantityControl = formControl.get('focQuantity');
                const maxFoc = this.getFocQuantityByLevel(formControl.get('indexLevel')?.getRawValue());
                focQuantityControl.setValidators([
                    Validators.min(0),
                    maxValidator(maxFoc, `Số lượng FOC phải <= ${maxFoc}`),
                    focQuantityValidator(`Số lượng FOC phải <= ${max}`),
                ]);
                focQuantityControl.updateValueAndValidity(); // Cập nhật lại trạng thái validator của quantity
            }
        } else {
            // Nếu event.value không tồn tại, reset giá trị các trường
            formControl.patchValue({
                indexLevel: null,
                internalReference: null,
                description: null,
                quantity: null,
                focQuantity: null,
                price: null,
                amount: null,
                note: null,
            });

            // Loại bỏ tất cả các validator khỏi trường quantity
            const quantityControl = formControl.get('quantity');
            quantityControl.clearValidators();
            quantityControl.updateValueAndValidity(); // Cập nhật lại trạng thái validator của form control
        }
    }

    handleChangeQuantity(controlQuantity: FormControl, controlFocQuantity: FormControl, controlAmount: FormControl, value: number, price: number) {
        if (!isEmpty(controlQuantity.errors)) return;

        const focQuantity = controlFocQuantity.getRawValue() ?? 0;
        controlAmount.patchValue((value - focQuantity) * price);
    }

    handleChangeFocQuantity(controlQuantity: FormControl, controlAmount: FormControl, value: number, price: number) {
        if (!isEmpty(controlQuantity.errors)) return;

        const quantity = controlQuantity.getRawValue() ?? 0;
        const focQuantity = value ?? 0;
        if (quantity <= focQuantity) {
            controlAmount.patchValue(0);
        } else {
            controlAmount.patchValue((quantity - focQuantity) * price);
        }
    }

    getSeverity() {
        switch (this.poTransfer.state) {
            case 3:
                return 'danger';
            case 0:
                return 'primary';
            case 1:
                return 'primary';
            case 2:
                return 'success';
            default:
                return 'primary';
        }
    }

    getValueTagState() {
        return this.mapStateTransfer[this.poTransfer.state];
    }

    confirmCancel() {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn hủy phiếu',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.loadingService.show();
                this.poTransferService.cancel(this.poTransfer.id).subscribe({
                    next: () => {
                        // Call API lay bang ke
                        this.poDetailService.getPoDetailByPo(this.poTransfer.poId).subscribe({
                            next: (res) => {
                                this.updatePoDetails.emit(res as PoDetailDTO);
                                this.alertService.success('Thành công');
                                this.poTransfer.state = TRANSFER_STATE_OBJECT.CANCEL;
                                this.loadingService.hide();
                                this.closeDialog();
                            },
                            error: (res) => {
                                this.alertService.handleError(res);
                                this.loadingService.hide();
                            },
                        });
                    },
                    error: (e) => {
                        this.alertService.handleError(e);
                        this.loadingService.hide();
                    },
                });
            },
        });
    }

    getDecimalPlaces(rowIndex: number, type: string): number {
        const internalReference = this.poTransferItems.controls[rowIndex].get('internalReference')?.value;
        const unitPrice = this.internalReferenceUnitPriceMap[internalReference];

        /*console.log(internalReference)
        console.log(unitPrice)*/
        if (unitPrice === 'USD') {
            return type === 'amount' ? 2 : 6; // Nếu type là 'amount', USD sẽ lấy 6 chữ số, còn lại 2 chữ số
        } else if (unitPrice === 'VNĐ') {
            return 0; // Nếu là VNĐ, không có số thập phân
        }
        return 2; // Mặc định là 2 chữ số thập phân
    }

    protected readonly Common = Common;

    handleSelectFile(file: File) {
        this.loadingService.show();
        this.poTransferService
            .importFile(
                file,
                this.po.id,
                this.poTransfer ? (this.poTransfer.accountingCode ? this.poTransfer.accountingCode.split('*') : null) : this.valueAccountingInit,
                this.poTransfer?.id,
            )
            .subscribe({
                next: (res: ApiResponse) => {
                    if (res.code === 1) {
                        const data = (res?.data as unknown as PoTransferItem[]) ?? [];
                        this.poTransferItemsDelete = this.poTransferItemsOldData?.map((item) => item.id) || [];
                        this.isImportFile = true;
                        // Empty old data
                        this.poTransferItems.controls.forEach((control, index) => {
                            this.removeTransferItem(index);
                        });
                        /*this.poTransferItems.clear();*/
                        data.forEach((item, index) => {
                            const manufacturerCode = this.manufacturerCodeMap.get(item.internalReference) ?? null;
                            const newItem = this.fb.group({
                                transferId: [this.poTransfer?.id],
                                internalReference: [item.internalReference, [Validators.required]],
                                indexLevel: [item.indexLevel, []],
                                manufacturerCode: [{ value: manufacturerCode, disabled: true }, []],
                                description: [{ value: item.description, disabled: true }, [Validators.required]],
                                quantity: [item.quantity, [Validators.required, Validators.min(1)]],
                                focQuantity: [item.focQuantity ?? 0, [Validators.min(0)]],
                                price: [{ value: item.price, disabled: true }, [Validators.required]],
                                amount: [{ value: item.amount, disabled: true }, [Validators.required]],
                                note: [item.note],
                                dateCode: [item?.dateCode],
                                dateCodeCustom: new FormControl({
                                    value: item?.dateCode ? new Date(item.dateCode) : null,
                                    disabled: this.isDisabled,
                                }),
                                isEdit: [false],
                                transferType: [0],
                            });
                            newItem.get('dateCodeCustom')?.valueChanges.subscribe((value: Date) => {
                                if (value != null) {
                                    const timestamp = DateUtils.convertToTimestampHCM(value);
                                    newItem.get('dateCode')?.setValue(timestamp);
                                }
                            });
                            this.poTransferItems.push(newItem);
                            this.saveTransferItem(index);
                            /*this.poTransferItems.push(newItem);
                            this.optionPoDetail = this.optionPoDetailOrigin;
                            this.removeItemsFromOptionPoDetail();
                            this.updateDisabledOnInit();
                            this.updateQuantityOptions();*/
                        });
                        this.alertService.success('Thành công');
                        this.urlError = null;
                    } else {
                        this.poTransferItems.clear();
                        this.urlError = res.message;
                    }
                    this.loadingService.hide();
                },
                error: (e) => {
                    this.loadingService.hide();
                    this.alertService.handleError(e);
                },
            });
    }

    handleClearFile() {}

    handleDownload() {
        this.poTransferService
            .getFileTemplateImport({
                poId: this.po.id,
                accountingCodes: this.poTransfer
                    ? this.poTransfer.accountingCode
                        ? this.poTransfer.accountingCode.split('*')
                        : null
                    : this.valueAccountingInit,
            })
            .subscribe({
                next: (res) => {
                    this.fileService.downLoadFileByService(res.url, '/sc/api');
                },
                error: (res) => {
                    this.alertService.handleError(res);
                },
            });
    }

    getTotalAmount() {
        return this.poTransferItems.controls.reduce((sum, control) => {
            const amount = control.get('amount')?.value || 0;
            const roundedDelivered = this.roundAmount(amount || 0);
            return sum + roundedDelivered;
        }, 0);
    }

    private roundAmount(amount: number): number {
        const isUSD = this.unitPriceString === 'USD';
        if (isUSD) {
            const formatter = new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
                useGrouping: false,
            });
            return Number(formatter.format(amount));
        }
        return Math.round(amount);
    }

    getFocQuantityByLevel(level: number) {
        if (!level) {
            return 0;
        } else {
            return this.quantityByLevel.get(level)?.focQuantity;
        }
    }

    /*updateQuantity() {
        // Reset lại quantityByLevel để cập nhật chính xác
        const newQuantityByLevel = new Map<number, { quantity: number; focQuantity: number }>();

        // Tạo một bản sao từ dữ liệu gốc
        this.quantityByLevel.forEach((value, key) => {
            newQuantityByLevel.set(key, { ...value });
        });

        // Duyệt qua từng item trong FormArray
        this.poTransferItems.controls.forEach((item: AbstractControl) => {
            const internalReference = item.get('internalReference')?.value;
            if (!internalReference || !(internalReference in this.internalReferenceByLevelMap)) {
                return;
            }

            const level = this.internalReferenceByLevelMap[internalReference];
            const levelData = newQuantityByLevel.get(level);
            if (!levelData) return;

            // Lấy số lượng từ item
            const quantity = item.get('quantity')?.value || 0;
            const focQuantity = item.get('focQuantity')?.value || 0;

            // Giảm số lượng còn lại của level
            levelData.quantity = Math.max(0, levelData.quantity - quantity);
            levelData.focQuantity = Math.max(0, levelData.focQuantity - focQuantity);
        });
        // Cập nhật lại quantityByLevel
        this.quantityByLevel = newQuantityByLevel;

        // Cập nhật lại optionPoDetail để phản ánh số lượng mới
        this.optionPoDetail = this.optionPoDetail.map((option) => {
            const levelData = newQuantityByLevel.get(option.indexLevel);
            return levelData
                ? { ...option, quantity: levelData.quantity, focQuantity: levelData.focQuantity, display: `${option.internalReference} - ${levelData.quantity}` }
                : option;
        });
    }

    updateQuantityCreate() {
        this.quantityByLevel = structuredClone(this.quantityByLevelOrigin);

        // Lặp qua tất cả các item trong FormArray để tính tổng số lượng đã chọn
        this.poTransferItems.controls.forEach((item: AbstractControl) => {
            const internalReference = item.get('internalReference')?.value;
            if (!internalReference || !(internalReference in this.internalReferenceByLevelMap)) {
                return;
            }

            const level = this.internalReferenceByLevelMap[internalReference];
            const levelData = this.quantityByLevel.get(level);
            if (!levelData) return;

            // Lấy số lượng từ item
            const quantity = Number(item.get('quantity')?.value) || 0;
            const focQuantity = Number(item.get('focQuantity')?.value) || 0;

            // Trừ số lượng đã chọn khỏi dữ liệu gốc (không để số âm)
            levelData.quantity = Math.max(0, levelData.quantity - quantity);
            levelData.focQuantity = Math.max(0, levelData.focQuantity - focQuantity);
        });


        // Cập nhật optionPoDetail để phản ánh số lượng mới
        this.optionPoDetail = this.optionPoDetail.map((option) => {
            const levelData = this.quantityByLevel.get(option.indexLevel);
            return levelData
                ? { ...option, quantity: levelData.quantity, focQuantity: levelData.focQuantity, display: `${option.internalReference} - ${levelData.quantity}` }
                : option;
        });
    }

    updateQuantityRemove() {
        this.quantityByLevel = structuredClone(this.quantityByLevelOrigin);

        // Duyệt qua toàn bộ FormArray để trừ số lượng đã chọn
        this.poTransferItems.controls.forEach((item: AbstractControl) => {
            const internalReference = item.get('internalReference')?.value;
            if (!internalReference || !(internalReference in this.internalReferenceByLevelMap)) {
                return;
            }

            const level = this.internalReferenceByLevelMap[internalReference];
            const levelData = this.quantityByLevel.get(level);
            if (!levelData) return;

            // Trừ đi số lượng của các item còn lại
            const quantity = Number(item.get('quantity')?.value) || 0;
            const focQuantity = Number(item.get('focQuantity')?.value) || 0;

            levelData.quantity = Math.max(0, levelData.quantity - quantity);
            levelData.focQuantity = Math.max(0, levelData.focQuantity - focQuantity);
        });

        // Cập nhật lại optionPoDetail
        this.optionPoDetail = this.optionPoDetail.map((option) => {
            const updatedLevelData = this.quantityByLevel.get(option.indexLevel);
            return updatedLevelData
                ? { ...option, quantity: updatedLevelData.quantity, focQuantity: updatedLevelData.focQuantity, display: `${option.internalReference} - ${updatedLevelData.quantity}` }
                : option;
        });
    }

    updateQuantityEdit() {
        this.quantityByLevel = structuredClone(this.quantityByLevelOrigin);

        // Duyệt qua toàn bộ FormArray
        this.poTransferItems.controls.forEach((item: AbstractControl) => {
            const internalReference = item.get('internalReference')?.value;
            if (!internalReference || !(internalReference in this.internalReferenceByLevelMap)) {
                return;
            }

            const level = this.internalReferenceByLevelMap[internalReference];
            const levelData = this.quantityByLevel.get(level);
            if (!levelData) return;

            // Nếu item đang được chỉnh sửa (isEdit = true), thì bỏ qua
            if (item.get('isEdit')?.value) {
                return;
            }

            // Trừ đi số lượng của các item chưa chỉnh sửa
            const quantity = Number(item.get('quantity')?.value) || 0;
            const focQuantity = Number(item.get('focQuantity')?.value) || 0;

            levelData.quantity = Math.max(0, levelData.quantity - quantity);
            levelData.focQuantity = Math.max(0, levelData.focQuantity - focQuantity);
        });

        // Cập nhật lại optionPoDetail
        this.optionPoDetail = this.optionPoDetail.map((option) => {
            const updatedLevelData = this.quantityByLevel.get(option.indexLevel);
            return updatedLevelData
                ? { ...option, quantity: updatedLevelData.quantity, focQuantity: updatedLevelData.focQuantity, display: `${option.internalReference} - ${updatedLevelData.quantity}` }
                : option;
        });
    }*/

    private updateQuantityOptions(isEditTransferItem: boolean = false) {
        this.quantityByLevel = structuredClone(this.quantityByLevelOrigin);

        // Duyệt qua FormArray
        this.poTransferItems.controls.forEach((item: AbstractControl) => {
            const internalReference = item.get('internalReference')?.value;
            if (!internalReference || !(internalReference in this.internalReferenceByLevelMap)) {
                return;
            }

            const level = this.internalReferenceByLevelMap[internalReference];
            const levelData = this.quantityByLevel?.get(level);
            if (!levelData) return;

            // Nếu đang Edit, bỏ qua item có isEdit = true
            if (isEditTransferItem && item.get('isEdit')?.value) {
                return;
            }

            // Trừ số lượng đã chọn
            const quantity = Number(item.get('quantity')?.value) || 0;
            const focQuantity = Number(item.get('focQuantity')?.value) || 0;

            levelData.quantity = Math.max(0, levelData.quantity - quantity);
            levelData.focQuantity = Math.max(0, levelData.focQuantity - focQuantity);
        });

        // Cập nhật optionPoDetail
        this.optionPoDetail = this.optionPoDetail.map((option) => {
            const updatedLevelData = this.quantityByLevel.get(option?.indexLevel);
            return updatedLevelData
                ? {
                      ...option,
                      quantity: updatedLevelData.quantity,
                      focQuantity: updatedLevelData.focQuantity,
                      display: `${option.internalReference} - ${updatedLevelData.quantity}`,
                  }
                : option;
        });

        this.sortOptionsByLevel();
    }

    private sortOptionsByLevel() {
        this.optionPoDetail.sort((a, b) => (a.indexLevel ?? 0) - (b.indexLevel ?? 0));
    }

    protected readonly DateUtils = DateUtils;
}
