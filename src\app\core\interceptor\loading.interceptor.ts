import { Injectable } from '@angular/core';
import { <PERSON>ttp<PERSON><PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Observable, timer } from 'rxjs';
import { delayWhen, finalize } from 'rxjs/operators';
import { LoadingService } from 'src/app/shared/services/loading.service';

@Injectable()
export class LoadingInterceptor implements HttpInterceptor {
    constructor(private loadingService: LoadingService) {}

    intercept(req: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
        // Determine if this request should show the loading spinner
        const showLoading = req.headers.get('show-loading') === 'true';

        if (showLoading) {
            this.loadingService.show();
        }
        const startTime = Date.now();
        return next.handle(req).pipe(
            delayWhen(() => {
                const elapsedTime = Date.now() - startTime;
                // Delay hiding loading spinner if response time is less than 40ms
                if (elapsedTime < 40) {
                    return timer(200); // Delay hiding loading by 200ms
                } else {
                    return timer(0); // No delay if response time is 40ms or more
                }
            }),
            finalize(() => {
                if (showLoading) {
                    this.loadingService.hide();
                }
            }),
        );
    }
}
