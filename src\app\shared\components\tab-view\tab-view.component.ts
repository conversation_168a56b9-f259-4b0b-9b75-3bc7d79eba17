import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TabViewModule } from 'primeng/tabview';
import { PanelModule } from 'primeng/panel';
export interface TabItem {
    /** Title of the tab */
    header: string;
    /** Component class to render inside the tab */
    component: any;
    type: number;
}
@Component({
    selector: 'app-tab-view',
    standalone: true,
    imports: [CommonModule, TabViewModule, PanelModule],
    templateUrl: './tab-view.component.html',
    styleUrls: ['./tab-view.component.scss'],
})
export class TabViewComponent {
    @Input() tabs: TabItem[] = [];
    @Output() tabChange = new EventEmitter<number>();
    activeIndex: number = 0;
    onTabChange(event: { index: number }): void {
        this.activeIndex = event.index;

        const type = this.tabs[event.index]?.type ?? event.index + 1;
        this.tabChange.emit(type);
    }
}
