import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { PaymentLot } from '../../../models/interface/sc';
import { ApiResponse } from 'src/app/models/interface';

@Injectable()
export class PaymentLotService extends BaseService<PaymentLot> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/payment-lot');
    }

    getByLot(lotId: number) {
        return this.http.get<PaymentLot>('/sc/api/payment-lot/get-info', {
            params: { lotId },
        });
    }

    importFile(files: File[]) {
        const formData: FormData = new FormData();
        files.forEach((file) => formData.append('files', file));

        return this.http.post<ApiResponse>('/sc/api/payment-lot/import-file', formData);
    }
}
