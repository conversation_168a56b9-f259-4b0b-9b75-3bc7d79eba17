<style></style>
<app-sub-header [items]="itemsHeader" [action]="actionHeader">
    <ng-template #actionHeader></ng-template>
</app-sub-header>
<div class="tw-p-5">
    <form class="tw-bg-white tw-p-5 tw-rounded-xl tw-mb-5">
        <div class="tw-grid tw-grid-cols-12 tw-gap-4">
            <div class="tw-col-span-3 tw-h-screen tw-flex tw-flex-col">
                <p-button
                    *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'product_line_create']"
                    [style]="{ width: '100%' }"
                    label="Thêm dòng sản phẩm"
                    (click)="addNewNode()"
                    class="tw-mb-4"
                ></p-button>

                <span class="p-input-icon-left tw-mb-4">
                    <i class="pi pi-search"></i>
                    <input
                        type="text"
                        name="searchQuery"
                        pInputText
                        [(ngModel)]="searchQuery"
                        (input)="onSearchInput()"
                        placeholder="Tìm kiếm..."
                        class="tw-w-full"
                    />
                </span>
                <div
                    *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'product_line_view']"
                    class="tw-flex-grow tw-overflow-y-auto"
                    #scrollContainer
                    (scroll)="onScroll($event)"
                    style="height: 600px; max-height: 600px"
                >
                    <p-tree
                        [value]="treeData"
                        selectionMode="single"
                        [(selection)]="selectedNode"
                        (onNodeSelect)="onNodeSelect($event.node)"
                        (onContextMenuSelect)="onContextMenuSelect($event)"
                        class="tw-bg-blue"
                        [contextMenu]="menu"
                        [style.height]="treeData?.length > 15 ? 'auto' : '100%'"
                    >
                        <ng-template let-node pTemplate="default">
                            <div
                                class="tw-flex tw-justify-between tw-items-center tw-w-full"
                                [attr.data-id]="node.data?.id"
                                [attr.data-line-id]="node.data?.lineId"
                            >
                                <div *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'product_line_update']">
                                    <input
                                        *ngIf="node === editingNode || !node.label"
                                        [(ngModel)]="editingLabel"
                                        name="nodeLabelEdit"
                                        (keyup.enter)="saveNodeEdit(node)"
                                        (blur)="handleBlur(node)"
                                        (keydown.space)="$event.stopPropagation()"
                                        (keyup.escape)="cancelEdit()"
                                        pInputText
                                        class="p-inputtext-sm tw-w-full"
                                        [ngClass]="{ 'p-invalid': node.data?.errors?.labelRequired }"
                                        autofocus
                                        #editInput
                                    />
                                    <small *ngIf="node.data?.errors?.labelRequired" class="p-error" style="margin-top: 4rem"> Trường này là bắt buộc </small>
                                </div>
                                <!-- Nếu có quyền -->
                                <ng-container *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'product_line_update']">
                                    <span *ngIf="node !== editingNode" (dblclick)="startEdit(node)">
                                        {{ node.label }}
                                    </span>
                                </ng-container>

                                <!-- Nếu không có quyền -->
                                <ng-container *ngIf="!hasUpdatePermission()">
                                    <span *ngIf="node !== editingNode">
                                        {{ node.label }}
                                    </span>
                                </ng-container>

                                <!-- Nút 3 chấm luôn hiển thị -->
                                <div>
                                    <button
                                        type="button"
                                        pButton
                                        icon="pi pi-ellipsis-v"
                                        class="p-button-text p-button-sm"
                                        (click)="$event.stopPropagation(); onMenuClick($event, node)"
                                    ></button>
                                </div>
                            </div>
                        </ng-template>
                    </p-tree>
                    <p-contextMenu #menu [model]="contextMenuItems"></p-contextMenu>
                </div>
            </div>
            <div class="tw-col-span-9">
                <ng-container *ngIf="selectedNode; else noSelection">
                    <div class="">
                        <div class="filter-label">
                            <label class="tw-font-bold">Danh sách sản phẩm</label>
                            <div
                                *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'product_line_create']"
                                class="right-section"
                                [ngStyle]="isSubFolder ? { gridTemplateColumns: 'none' } : {}"
                            >
                                <p-button
                                    size="small"
                                    label="Thêm sản phẩm"
                                    styleClass="p-button-info filter-button"
                                    (click)="openProduct($event, 'create')"
                                ></p-button>
                            </div>
                        </div>
                        <div [ngClass]="{ 'sub-model-mode': isSubFolder }">
                            <app-form [formGroup]="filterForm" layout="vertical">
                                <div class="tw-grid tw-grid-cols-[8fr] tw-gap-4 tw-my-4">
                                    <div class="tw-grid tw-grid-cols-3 tw-gap-4">
                                        <app-custom-form-item [noGrid]="true" *ngIf="!isSubFolder" label="" [control]="filterForm.get('selectedModels')">
                                            <app-combobox-nonRSQL
                                                #modelSelectFilter
                                                [fetchOnInit]="false"
                                                type="select"
                                                (panelShow)="handlePanelShow(modelSelectFilter)"
                                                formControlName="selectedModels"
                                                fieldValue="id"
                                                fieldLabel="name"
                                                url="/pr/api/product-model"
                                                param="name"
                                                placeholder="Chọn Model"
                                                [additionalParams]="{ size: 100, page: 0, unpaged: false, productLineId: selectedProductLineId }"
                                            >
                                            </app-combobox-nonRSQL>
                                        </app-custom-form-item>
                                        <app-custom-form-item [noGrid]="true" label="" [control]="filterForm.get('selectedCustomer')">
                                            <app-combobox-nonRSQL
                                                #customerFilter
                                                [fetchOnInit]="false"
                                                type="select"
                                                formControlName="selectedCustomer"
                                                fieldValue="id"
                                                (panelShow)="handlePanelShow(customerFilter)"
                                                fieldLabel="name"
                                                url="/pr/api/company/list"
                                                param="query"
                                                placeholder="Chọn khách hàng"
                                                [additionalParams]="{ size: 100 }"
                                            >
                                            </app-combobox-nonRSQL>
                                        </app-custom-form-item>
                                        <div class="tw-flex tw-gap-3 tw-items-center">
                                            <p-button
                                                *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'product_line_view']"
                                                size="small"
                                                label="Tìm kiếm"
                                                styleClass="filter-button"
                                                (click)="searchProducts()"
                                            ></p-button>
                                            <p-button
                                                *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'product_compare']"
                                                size="small"
                                                label="So sánh"
                                                styleClass="p-button-info filter-button"
                                                (click)="openCompareDialog()"
                                            ></p-button>
                                        </div>
                                    </div>
                                </div>
                            </app-form>
                        </div>
                    </div>
                    <div *ngIf="products.length > 0; else noData" class="tw-grid tw-grid-cols-3 tw-gap-4 tw-mb-4" style="margin-top: 10px">
                        <ng-container>
                            <div *ngFor="let product of products">
                                <p-card header="" class="fixed-width-card">
                                    <div class="image-wrapper" style="display: flex; flex-direction: column">
                                        <img *ngIf="product.image; else noImage" [src]="product.image" class="card-image" />
                                        <ng-template #noImage>
                                            <img
                                                #noImage
                                                src="https://thumbs.dreamstime.com/b/no-image-vector-sy…ble-icon-gallery-moment-placeholder-246411909.jpg"
                                                class="card-image"
                                            />
                                        </ng-template>
                                    </div>
                                    <div class="card-content">
                                        <div class="flex align-items-center justify-content-between mb-2 w-full">
                                            <p-checkbox
                                                name="compare"
                                                [value]="product"
                                                [(ngModel)]="selectedProducts"
                                                (onChange)="handleCompare($event, product)"
                                            ></p-checkbox>
                                            <span class="tw-ml-2">P/N {{ product.vnptManPn }}</span>
                                            <button
                                                type="button"
                                                pButton
                                                icon="pi pi-ellipsis-v"
                                                (click)="openMenu($event, product, menu)"
                                                class="p-button-text p-button-sm"
                                            ></button>
                                        </div>
                                        <div class="tw-font-semibold">Model: {{ product.modelName }}</div>
                                        <div class="tw-font-semibold tw-truncate tw-max-w-[300px]" pTooltip="{{ product.name }}" tooltipPosition="top">
                                            Tên sản phẩm: {{ product.name }}
                                        </div>
                                        <div class="tw-font-semibold tw-truncate tw-max-w-[300px]" pTooltip="{{ product.tradeName }}" tooltipPosition="top">
                                            Tên thương mại: {{ product.tradeName }}
                                        </div>
                                        <div class="tw-font-semibold">Giai đoạn SX: {{ product.lifecycleStageLabel }}</div>
                                    </div>
                                </p-card>
                                <p-menu #menu [popup]="true" [model]="productMenu"></p-menu>
                            </div>
                        </ng-container>
                    </div>
                    <ng-template #noData>
                        <div class="tw-text-gray-500 tw-text-center tw-w-full tw-mt-4">Không có dữ liệu</div>
                    </ng-template>
                    <div *ngIf="products.length > 0" class="tw-mt-4 tw-text-center">
                        <p-paginator (onPageChange)="onPageChange($event)" [first]="first" [rows]="rows" [totalRecords]="totalRecords" />
                    </div>
                </ng-container>
                <ng-template #noSelection>
                    <div class="tw-text-gray-500 tw-text-center tw-w-full tw-mt-4">Không có dữ liệu</div>
                </ng-template>
            </div>
        </div>
    </form>
</div>

<app-popup
    #EditPopup
    [header]="title"
    [isButtonVisible]="isButtonVisible"
    [showConfirmButton]="!isDetail"
    dialogWidth="60vw"
    [formGroup]="addProductForm"
    (onSubmit)="submitFormEdit()"
    (onClose)="handlePopupClose()"
>
    <app-form [formGroup]="addProductForm" styleClass="tw-grid tw-grid-cols-1 tw-gap-4" *ngIf="addProductForm">
        <div class="tw-grid tw-grid-cols-2 tw-gap-4">
            <app-custom-form-item label="Tên sản phẩm" [control]="addProductForm.get('productName')">
                <input
                    type="text"
                    class="tw-w-full"
                    pInputText
                    formControlName="productName"
                    [readonly]="isViewOnly"
                    maxlength="1000"
                    (blur)="onBlurTrim('productName')"
                />
                <app-input-validate [control]="addProductForm.get('productName')" fieldName="tên sản phẩm" />
            </app-custom-form-item>
            <app-custom-form-item label="Generation" [control]="addProductForm.get('generation')">
                <input
                    type="text"
                    class="tw-w-full"
                    pInputText
                    formControlName="generation"
                    [readonly]="isViewOnly"
                    maxlength="200"
                    (blur)="onBlurTrim('generation')"
                />
            </app-custom-form-item>
            <app-custom-form-item label="Tên thương mại" [control]="addProductForm.get('brandName')">
                <input
                    type="text"
                    class="tw-w-full"
                    pInputText
                    formControlName="brandName"
                    [readonly]="isViewOnly"
                    maxlength="200"
                    (blur)="onBlurTrim('brandName')"
                />
                <app-input-validate [control]="addProductForm.get('brandName')" fieldName="tên thương mại" />
            </app-custom-form-item>
            <app-custom-form-item label="Mô tả" [control]="addProductForm.get('description')">
                <input
                    type="text"
                    class="tw-w-full"
                    pInputText
                    [readonly]="isViewOnly"
                    formControlName="description"
                    maxlength="1000"
                    (blur)="onBlurTrim('description')"
                />
            </app-custom-form-item>
            <app-custom-form-item label="Mã thương mại" [control]="addProductForm.get('codeName')">
                <input
                    type="text"
                    class="tw-w-full"
                    [readonly]="isViewOnly"
                    pInputText
                    formControlName="codeName"
                    maxlength="200"
                    (blur)="onBlurTrim('codeName')"
                />
                <app-input-validate [control]="addProductForm.get('codeName')" fieldName="mã thương mại" />
            </app-custom-form-item>
            <app-custom-form-item label="VNPT Man P/N" [control]="addProductForm.get('manPn')">
                <p-autoComplete
                    *ngIf="!isDetail && !isEdit"
                    formControlName="manPn"
                    [suggestions]="optionManPn"
                    (completeMethod)="loadUnusedManufacturers($event)"
                    appendTo="body"
                    [dropdown]="true"
                    (onSelect)="onManPnSelect($event)"
                ></p-autoComplete>

                <input
                    *ngIf="isDetail || isEdit"
                    type="text"
                    class="tw-w-full"
                    [readonly]="isViewOnly"
                    pInputText
                    [disabled]="true"
                    [value]="addProductForm.get('manPn')?.value"
                />
                <app-input-validate [control]="addProductForm.get('manPn')" fieldName="vnpt man p/n" />
            </app-custom-form-item>
            <app-custom-form-item label="Model" [control]="addProductForm.get('modelId')">
                <app-combobox-nonRSQL
                    *ngIf="!isSubFolder"
                    #modelSelectPopup
                    [fetchOnInit]="false"
                    type="select-one"
                    formControlName="modelId"
                    (panelShow)="handlePanelShow(modelSelectPopup)"
                    fieldValue="id"
                    fieldLabel="name"
                    [disabled]="isViewOnly"
                    param="name"
                    url="/pr/api/product-model"
                    [additionalParams]="{ size: 100, page: 0, unpaged: false, productLineId: selectedProductLineId }"
                >
                </app-combobox-nonRSQL>
                <input *ngIf="isSubFolder" type="text" class="tw-w-full" pInputText [value]="selectedNode?.label" disabled />
            </app-custom-form-item>
            <app-custom-form-item label="Khách hàng" [control]="addProductForm.get('customer')">
                <app-combobox-nonRSQL
                    #customerPopup
                    [fetchOnInit]="false"
                    type="select"
                    formControlName="customer"
                    (panelShow)="handlePanelShow(customerPopup)"
                    fieldValue="id"
                    fieldLabel="name"
                    [disabled]="isViewOnly"
                    url="/pr/api/company/list"
                    param="query"
                    [additionalParams]="{ size: 100, page: 0, unpaged: false }"
                >
                </app-combobox-nonRSQL>
            </app-custom-form-item>
            <app-custom-form-item label="Hình ảnh sản phẩm" [control]="addProductForm.get('image')">
                <div *ngIf="addProductForm.get('image')?.value" class="image-detail-container">
                    <div class="image-info">
                        <ng-container *ngIf="isNewUpload(); else existingImage">
                            <a [href]="getTempImageUrl()" target="_blank" class="image-link">
                                {{ addProductForm.get('image')?.value?.name }}
                            </a>
                        </ng-container>

                        <ng-template #existingImage>
                            <a
                                [href]="getFullImageUrl(addProductForm.get('image')?.value.url)"
                                target="_blank"
                                class="image-link tw-truncate tw-max-w-[250px]"
                                >{{ addProductForm.get('image')?.value?.name }}</a
                            >
                        </ng-template>
                        <button type="button" pButton icon="pi pi-times" class="p-button-text p-button-danger p-button-sm" (click)="handleClearFile()"></button>
                    </div>
                </div>
                <app-upload-custom
                    *ngIf="!isDetail && !addProductForm.get('image')?.value"
                    #uploadRef
                    [disabled]="isDetail"
                    (onClear)="handleClearFile()"
                    [fileName]="addProductForm.get('image')"
                    [limit]="1"
                    (onChange)="handleUploadFile($event)"
                >
                </app-upload-custom>
            </app-custom-form-item>

            <ng-container *ngIf="isDetail || isEdit">
                <app-custom-form-item label="Firmware version">
                    <span class="tw-leading-8">{{ addProductForm.get('firmwareVersion')?.value }}</span>
                </app-custom-form-item>

                <app-custom-form-item label="RD BOM">
                    <span class="tw-leading-8">{{ addProductForm.get('rdBom')?.value }}</span>
                </app-custom-form-item>

                <app-custom-form-item label="Giai đoạn">
                    <span class="tw-leading-8">{{ getLifecycleStageLabel(addProductForm.get('lifecycleStage')?.value) }}</span>
                </app-custom-form-item>

                <app-custom-form-item label="Hardware version" control="addProductForm('hardwareVersion')">
                    <input
                        type="text"
                        class="tw-w-full"
                        pInputText
                        [readonly]="isViewOnly"
                        formControlName="hardwareVersion"
                        maxlength="200"
                        (blur)="onBlurTrim('hardwareVersion')"
                    />
                </app-custom-form-item>

                <app-custom-form-item label="Version hồ sơ">
                    <span class="tw-leading-8">{{ addProductForm.get('version')?.value }}</span>
                    <a
                        *ngIf="addProductForm.get('version')?.value"
                        href="javascript:void(0)"
                        class="tw-text-blue-500 tw-leading-8 hover:underline tw-ml-2"
                        (click)="openHsspDialog()"
                    >
                        Xem HSSP
                    </a>
                </app-custom-form-item>
            </ng-container>
        </div>
    </app-form>
    <!-- <div *ngIf="isDetail && !isSubFolder" class="tw-absolute tw-right-[90px]" style="bottom: 20px">
        <p-button label="Sửa" size="small" severity="primary" (click)="enableEditForm()"></p-button>
    </div> -->
</app-popup>

<app-popup #HSSPPopup header="So sánh sản phẩm" [isButtonVisible]="false" dialogWidth="50vw" [showConfirmButton]="false">
    <p-table [value]="compareFields" showGridlines [tableStyle]="{ 'min-width': '50rem' }">
        <ng-template pTemplate="colgroup">
            <colgroup>
                <col style="width: 20%" />
                <col style="width: 40%" />
                <col style="width: 40%" />
            </colgroup>
        </ng-template>
        <ng-template pTemplate="header">
            <tr>
                <th></th>
                <th>{{ comparedProducts[0]?.name }}</th>
                <th>{{ comparedProducts[1]?.name }}</th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-field>
            <!-- <tr *ngFor="let field of compareFields"> -->
            <tr>
                <td>{{ field.label }}</td>
                <td class="tw-text-center">{{ getDisplayValue(field.key, comparedProducts[0]?.[field.key], 0) }}</td>
                <td class="tw-text-center">{{ getDisplayValue(field.key, comparedProducts[1]?.[field.key], 1) }}</td>
            </tr>
            <!-- </tr> -->
        </ng-template>
    </p-table>
    <div class="tw-absolute tw-right-[90px]" style="bottom: 20px">
        <button
            *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'version_compare']"
            pButton
            label="So sánh HSSP"
            [disabled]="isHSSPDisabled"
            class="p-button-sm p-button-secondary"
            (click)="compareHSSP()"
        ></button>
    </div>
</app-popup>

<app-popup #ComparePopup header="SO SÁNH HSSP" [isButtonVisible]="isButtonVisible" dialogWidth="60vw" [showConfirmButton]="false">
    <app-hssp-compare-dialog #HsspCompareDialogComponent [products]="productsToCompare"></app-hssp-compare-dialog>
</app-popup>

<app-popup #HistoryPopup header="THEO DÕI LỊCH SỬ THAY ĐỔI" [isButtonVisible]="false" dialogWidth="60vw" [showConfirmButton]="false">
    <app-table-custom [columns]="trackCols" [data]="historyData">
        <ng-template TableCell="details" let-product>
            <div style="white-space: pre-line; line-height: 1.5">{{ product.details }}</div>
        </ng-template>
    </app-table-custom>
</app-popup>
