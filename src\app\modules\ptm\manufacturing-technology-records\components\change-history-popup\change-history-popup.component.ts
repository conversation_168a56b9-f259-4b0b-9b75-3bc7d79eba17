// change-history-popup.component.ts
import { Component, Input, Output, EventEmitter } from '@angular/core';
import { DialogModule } from 'primeng/dialog';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';

export interface ChangeHistoryRecord {
    stt: number;
    action: string;
    detail: string;
    beforeFile?: string;
    beforeVersion?: string;
    beforeBuildtime?: string;
    afterFile?: string;
    afterVersion?: string;
    afterBuildtime?: string;
    note?: string;
    user?: string;
    timestamp: string;
}

@Component({
    selector: 'app-change-history-popup',
    standalone: true,
    imports: [DialogModule, TableModule, ButtonModule],
    templateUrl: './change-history-popup.component.html',
})
export class ChangeHistoryPopupComponent {
    /** Hiển thị dialog */
    @Input() visible: boolean = false;
    @Output() visibleChange = new EventEmitter<boolean>();

    /** <PERSON><PERSON> thể override width từ parent, ví dụ '600px' hoặc '80%' */
    @Input() dialogWidth?: string;
    @Input() records: ChangeHistoryRecord[] = [];
    /** <PERSON><PERSON> liệu mẫu */
    // records: ChangeHistoryRecord[] = [
    //     {
    //         stt: 1,
    //         action: 'Thêm mới',
    //         detail: '',
    //         timestamp: '10:00am 03/04/2025',
    //     },
    //     {
    //         stt: 2,
    //         action: 'Chỉnh sửa',
    //         detail: 'Thiết kế Schematic',
    //         beforeFile: 'Tên file',
    //         afterFile: 'Tên file',
    //         timestamp: '10:00am 03/04/2025',
    //     },
    // ];

    close() {
        this.visible = false;
        this.visibleChange.emit(this.visible);
    }
}
