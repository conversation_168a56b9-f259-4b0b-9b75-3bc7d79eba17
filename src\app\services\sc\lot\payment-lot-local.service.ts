import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { PaymentLotLocal } from '../../../models/interface/sc';

@Injectable()
export class PaymentLotLocalService extends BaseService<PaymentLotLocal> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/payment-lot-local');
    }
}
