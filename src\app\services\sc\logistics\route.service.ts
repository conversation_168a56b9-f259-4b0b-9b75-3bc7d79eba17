import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { Route } from '../../../models/interface/sc';

@Injectable()
export class RouteService extends BaseService<Route> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/route');
    }

    groupByDate(date?: number) {
        const params = {};
        if (date) {
            params['date'] = date;
        }
        return this.http.get<Route[]>('/sc/api/route/group-by-date', {
            params,
        });
    }
}
