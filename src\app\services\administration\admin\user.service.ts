import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { isNull, isUndefined } from 'lodash';
import { catchError, throwError } from 'rxjs';
import { ParamsTable } from 'src/app/shared/table-module/table.common.service';
import { User } from '../../../models/interface';

@Injectable()
export class BaseUserService {
    #http = inject(HttpClient);

    getPage({ pageable, rsql }: ParamsTable, body) {
        const subQuery = [];
        if (!isNull(body.roleId) && !isUndefined(body.roleId)) {
            subQuery.push(`roleId=${body.roleId}`);
        }

        if (!isNull(body.areaId) && !isUndefined(body.areaId)) {
            subQuery.push(`areaId=${body.areaId}`);
        }
        if (!isNull(body.departmentId) && !isUndefined(body.departmentId)) {
            subQuery.push(`departmentId=${body.departmentId}`);
        }
        return this.#http.get<User[]>(`/auth/api/users/search-advance?query=${rsql}&${subQuery.join('&')}&service=SmartQcService${pageable}`, {
            observe: 'response',
        });
    }

    getAllQcUser(role) {
        return this.#http.get<User[]>(`/auth/api/users/smartqc-user?roles=` + role);
    }

    deleteMany(ids: number[]) {
        return this.#http.post<number[]>(`/auth/api/users/batch-delete`, ids);
    }

    getOne(id: number) {
        return this.#http.get<User>(`/auth/api/users/${id}`);
    }

    getAllDeparment(query) {
        return this.#http.get<unknown[]>(`/auth/api/department/search?${query}`);
    }

    update(user: User) {
        return this.#http.put('/auth/api/users/' + user.id, user);
    }

    create(user: User) {
        return this.#http.post('/auth/api/users/smartqc-create', user);
    }

    downloadTemplate() {
        return this.#http.get<unknown>(`/auth/api/users/template`);
    }

    deactiveUser(ids: number[]) {
        return this.#http.post('/auth/api/users/deactivate', ids);
    }

    deactivate(id: number) {
        return this.#http.get('/auth/api/users/deactivate?id=' + id);
    }

    activate(id: number) {
        return this.#http.get('/auth/api/users/activate?id=' + id);
    }

    importUser(file, roleLvl) {
        const fd = new FormData();
        fd.append('file', file);
        fd.append('roleLvl', roleLvl);
        const url = '/auth/api/users/import-user-smartqc';
        const headers = new HttpHeaders().set('show-loading', 'true');
        headers.set('Content-Type', undefined);
        return this.#http.post<unknown>(url, fd, { headers }).pipe(
            catchError((error: HttpErrorResponse) => {
                return throwError(() => error);
            }),
        );
    }

    getUserHavePrivilege(privileges: string[]) {
        return this.#http.post<User[]>('/auth/api/users/have-privileges', {
            privileges,
        });
    }
}
