import { AfterViewInit, Component, ElementRef, Input, ViewChild } from '@angular/core';
import { LineSupplierPrice } from 'src/app/models/interface/sc';
import { SupplierPriceService } from 'src/app/services/sc/supplier-report/suppiler-price.service';
import { ChartConfiguration } from 'chart.js';
import Chart from 'chart.js/auto';
import { FormGroup } from '@angular/forms';
import { TableModule } from 'primeng/table';
import { SkeletonLoadingComponent } from 'src/app/shared/components/skeleton-loading/skeleton-loading.component';
import { CommonModule } from '@angular/common';
import { catchError, Observable, tap, throwError } from 'rxjs';
@Component({
    selector: 'app-supplier-chart-price',
    templateUrl: './chart-price.component.html',
    styleUrls: ['./chart-price.component.scss'],
    standalone: true,
    providers: [SupplierPriceService],
    imports: [CommonModule, TableModule, SkeletonLoadingComponent],
})
export class ChartPriceComponent implements AfterViewInit {
    @Input() formReport: FormGroup;
    @Input() supplierId: number;

    lineSupplierPrices: LineSupplierPrice;
    @ViewChild('templateChartLine') templateChartLine: ElementRef<HTMLCanvasElement>;
    isLoading = true;
    chartLineSupplierPrice: Chart;

    constructor(private supplierPriceService: SupplierPriceService) {}

    ngAfterViewInit() {
        if (!this.isLoading) {
            this.initializeChart();
        }
    }

    initializeChart() {
        if (this.chartLineSupplierPrice) {
            this.chartLineSupplierPrice.destroy();
        }

        const ctx: HTMLCanvasElement = this.templateChartLine.nativeElement;

        const config: ChartConfiguration<'line', number[], string> = {
            type: 'line',
            data: {
                labels: this.lineSupplierPrices.labels,
                datasets: [
                    {
                        label: '% theo tháng',
                        data: this.lineSupplierPrices.percents,
                    },
                ],
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    filler: {
                        propagate: false,
                    },
                    title: {
                        display: true,
                        text: 'Biến động giá',
                    },
                },
                interaction: {
                    intersect: false,
                },
            },
        };

        this.chartLineSupplierPrice = new Chart(ctx, config);
    }
    callApiReportPrice(): Observable<LineSupplierPrice> {
        let startTime = new Date();
        let endTime = new Date();
        this.isLoading = true;

        if (this.formReport.value['type'] === 'year') {
            startTime.setFullYear(this.formReport.value['year'], 0, 1);
            endTime.setFullYear(this.formReport.value['year'], 11, 1);
        } else {
            startTime = this.formReport.value['startTime'];
            endTime = this.formReport.value['endTime'];
        }

        return this.supplierPriceService
            .lineSupplierPrice(this.supplierId, startTime.getTime(), endTime.getTime())
            .pipe(
                tap((res: LineSupplierPrice) => {
                    this.lineSupplierPrices = res;
                    this.isLoading = false;

                    // If the chart template exists, initialize the chart
                    if (this.templateChartLine) {
                        this.initializeChart();
                    }
                }),
                catchError((error) => {
                    this.isLoading = false;
                    return throwError(error);
                }),
            );
    }
}
