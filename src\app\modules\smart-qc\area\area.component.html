<app-sub-header [items]="itemsHeader" [action]="actionHeader"></app-sub-header>

<ng-template #actionHeader>
    <!--<p-button size="small" (click)="goToCreateTemplatePage()" label="Thêm mới" severity="success" />-->
</ng-template>

<div style="padding: 1rem 1rem 0 1rem">
    <app-table-common
        [tableId]="tableId"
        [columns]="columns"
        [data]="state.data"
        [loading]="state.isFetching"
        [filterTemplate]="filterTemplate"
        selectionMode=""
        name="Danh sách Tỉnh/ thành phố"
    >
        <ng-template #filterTemplate>
            <tr>
                <th>
                    <app-filter-table
                        [tableId]="tableId"
                        field="name"
                        rsql="true"
                        type="select"
                        [configSelect]="{
                            fieldValue: 'name',
                            fieldLabel: 'name',
                            param: 'name',
                            url: '/auth/api/area/search',
                        }"
                        placeholder="Tên Tỉnh/ Thành phố"
                    ></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'code']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="code"
                        [rsql]="true"
                        type="text"
                        placeholder="Mã Tỉnh/ Thành phố"
                    ></app-filter-table>
                </th>
            </tr>
        </ng-template>
    </app-table-common>
</div>
