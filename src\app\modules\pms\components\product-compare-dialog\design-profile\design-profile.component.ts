import { Component, Input, SimpleChanges, OnChanges } from '@angular/core';
import { TableModule } from 'primeng/table';
import { CommonModule } from '@angular/common';
import { environment } from 'src/environments/environment';

interface RowData {
    section: string;
    name: string;
    note1?: string;
    note2?: string;
    path1?: string;
    path2?: string;
}

@Component({
    selector: 'app-design-profile',
    templateUrl: './design-profile.component.html',
    styleUrls: ['./design-profile.component.scss'],
    standalone: true,
    imports: [TableModule, CommonModule],
})
export class DesignProfileComponent implements OnChanges {
    @Input() docRes1: any; // Dữ liệu từ sản phẩm 1 (tab 1)
    @Input() docRes2: any; // Dữ liệu từ sản phẩm 2 (tab 1)
    groupedRows: { name: string; items: RowData[] }[] = [];
    public STORAGE_BASE_URL = environment.STORAGE_BASE_URL;

    ngOnChanges(changes: SimpleChanges) {
        if (changes['docRes1'] || changes['docRes2']) {
            this.updateGroupedRows();
        }
    }

    private updateGroupedRows() {
        // Kiểm tra dữ liệu đầu vào
        if (!this.docRes1 || !this.docRes2) {
            console.warn('Missing input data');
            return;
        }

        const sectionMap: { [key: number]: string } = {
            0: 'Thông tin sản phẩm',
            1: 'RD BOM',
            2: 'Thiết kế HW',
            3: 'Thiết kế ID/ MD',
            4: 'Thiết kế Accessories và Packaging',
            5: 'Bootloader/Firmware sản phẩm',
        };

        const data1 = this.docRes1 ?? {};
        const data2 = this.docRes2 ?? {};

        const allSectionKeys = new Set([...Object.keys(data1), ...Object.keys(data2)]);
        const map = new Map<string, RowData[]>();

        for (const sectionKey of allSectionKeys) {
            const sectionNum = Number(sectionKey);
            const sectionName = sectionMap[sectionNum] || 'Khác';

            const list1 = Array.isArray(data1[sectionKey]) ? data1[sectionKey] : [];
            const list2 = Array.isArray(data2[sectionKey]) ? data2[sectionKey] : [];

            const maxLength = Math.max(list1.length, list2.length);
            const rows: RowData[] = [];

            for (let i = 0; i < maxLength; i++) {
                const item1 = list1[i] || {};
                const item2 = list2[i] || {};

                rows.push({
                    section: sectionName,
                    name: item1.description || item2.description || '',
                    note1: item1.fileName || '',
                    note2: item2.fileName || '',
                    path1: item1.filePath || '',
                    path2: item2.filePath || '',
                });
            }

            if (rows.length > 0) {
                map.set(sectionName, rows);
            }
        }

        this.groupedRows = Array.from(map, ([name, items]) => ({ name, items }));
    }
    isRdBomSection(sectionName: string): boolean {
        return sectionName === 'RD BOM';
    }
    downloadFile(url: string, namePath: string) {
        const a = document.createElement('a');
        a.href = this.STORAGE_BASE_URL + '/' + url;
        a.download = `${namePath}`;
        a.click();
        a.remove();
    }
}
