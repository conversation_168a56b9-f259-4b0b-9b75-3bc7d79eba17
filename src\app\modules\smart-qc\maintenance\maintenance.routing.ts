import { canAuthorize } from '../../../core/auth/auth.guard';

export const MaintenanceRouting = {
    path: 'maintenance',
    title: '<PERSON><PERSON><PERSON>n lý sau triển khai',
    children: [
        {
            path: '',
            canActivate: [canAuthorize],
            data: { authorize: ['ROLE_SYSTEM_ADMIN', 'ROLE_QC_PM', 'sqc_maintenance_view'] },
            loadComponent: () => import('./maintenance.component').then((c) => c.MaintenanceComponent),
        },
    ],
};
