<p-panel header="Giai đoạn 3: <PERSON><PERSON><PERSON> nhận hàng vận chuyển từ nước ngoài" [toggleable]="true" *ngIf="lot">
    <ng-template pTemplate="icons">
        <p-button label="Lịch sử thay đổi" severity="secondary" size="small" class="tw-mr-4" (click)="getTotalHistory()"></p-button>
        <p-button label="Cập nhật lịch trình" severity="primary" size="small" (click)="showPopupCreateHis()"></p-button>
    </ng-template>
    <div class="tw-grid tw-grid-cols-2 tw-gap-4">
        <div class="tw-space-y-2">
            <div class="font-bold">Ngày giao hàng thực tế</div>
            <div>{{ lastLotHistory?.expectedDeliveryDate | date: 'dd/MM/yyyy' }}</div>
            <div class="text-red-600" *ngIf="lastLotHistory?.delayDeliveryDate">Thời gian delay {{ lastLotHistory.delayDeliveryDate }} ngày</div>
        </div>

        <div class="tw-space-y-2">
            <div class="font-bold">Thời gian khởi hành thực tế</div>
            <div>{{ lastLotHistory?.expectedLeaveDate | date: 'dd/MM/yyyy' }}</div>
            <div class="text-red-600" *ngIf="lastLotHistory?.delayLeaveDate">Thời gian delay {{ lastLotHistory.delayLeaveDate }} ngày</div>
        </div>

        <div class="tw-space-y-2">
            <div class="font-bold">Thời gian đến nơi</div>
            <div>{{ lastLotHistory?.expectedPortDate | date: 'dd/MM/yyyy' }}</div>
        </div>

        <div class="tw-space-y-2">
            <div class="font-bold">Thời gian về kho</div>
            <div>{{ lastLotHistory?.expectedWarehouseDate | date: 'dd/MM/yyyy' }}</div>
        </div>
        <div class="tw-space-y-2 tw-col-span-2">
            <div class="font-bold">Ghi chú</div>
            <div>{{ lastLotHistory?.note }}</div>
        </div>
        <div class="tw-space-y-2 tw-col-span-2">
            <div class="font-bold">Đính kèm</div>
            <div class="tw-flex tw-flex-col tw-gap-4" *ngIf="lastLotHistory">
                <ng-container *ngFor="let att of lastLotHistory.attachments">
                    <app-attachment [attachment]="att"></app-attachment>
                </ng-container>
            </div>
        </div>
    </div>
    <br />
    <div class="tw-flex tw-justify-end tw-space-x-3">
        <p-button label="Thông báo cho người tiếp nhận" severity="primary" size="small" (click)="visibleSubmit = true"></p-button>
    </div>
</p-panel>

<p-dialog header="Cập nhật lịch trình" [(visible)]="visibleNew" [style]="{ width: '80vw' }" [breakpoints]="{ '1199px': '80vw', '575px': '100vw' }">
    <hr style="margin: 0" />
    <br />
    <app-form #form *ngIf="formGroup" [formGroup]="formGroup" layout="vertical" (onSubmit)="onSubmitHis($event)">
        <div class="tw-grid tw-grid-cols-2 tw-gap-4">
            <app-form-item label="Ngày giao hàng thực tế">
                <p-calendar
                    [showButtonBar]="true"
                    placeholder="dd/MM/yyyy"
                    dateFormat="dd/mm/yy"
                    appendTo="body"
                    formControlName="expectedDeliveryDateCustom"
                ></p-calendar>
            </app-form-item>
            <app-form-item label="Thời gian khởi hành thực tế">
                <p-calendar
                    [showButtonBar]="true"
                    placeholder="dd/MM/yyyy"
                    dateFormat="dd/mm/yy"
                    appendTo="body"
                    formControlName="expectedLeaveDateCustom"
                ></p-calendar>
            </app-form-item>
            <app-form-item label="Thời gian đến nơi">
                <p-calendar
                    [showButtonBar]="true"
                    placeholder="dd/MM/yyyy"
                    dateFormat="dd/mm/yy"
                    appendTo="body"
                    formControlName="expectedPortDateCustom"
                ></p-calendar>
            </app-form-item>
            <app-form-item label="Thời gian về kho">
                <p-calendar
                    [showButtonBar]="true"
                    placeholder="dd/MM/yyyy"
                    dateFormat="dd/mm/yy"
                    appendTo="body"
                    formControlName="expectedWarehouseDateCustom"
                ></p-calendar>
            </app-form-item>

            <app-form-item label="Ghi chú" class="tw-col-span-2">
                <textarea rows="5" formControlName="note" pInputTextarea class="tw-w-full"></textarea>
            </app-form-item>

            <app-form-item label="Đính kèm">
                <app-button-group-file
                    simpleUpload=""
                    (onFileSelected)="handleUploadFile($event)"
                    [attachments]="formGroup.getRawValue().attachments"
                    formControlName="attachmentIds"
                    [multiple]="true"
                ></app-button-group-file>

                <app-button-group-file
                    *ngIf="formGroup.getRawValue().attachments && formGroup.getRawValue().attachments.length > 0"
                    class="tw-col-span-2"
                    (onFileSelected)="handleUploadFile($event)"
                    [multiple]="true"
                    simpleUpload=""
                    formControlName="attachmentIds"
                ></app-button-group-file>
            </app-form-item>
        </div>
    </app-form>
    <ng-template pTemplate="footer">
        <div>
            <p-button type="button" severity="primary" size="small" (click)="form.handleSubmit()" label="Lưu"></p-button>
            <p-button type="button" label="Hủy" [text]="true" [raised]="true" size="small" severity="secondary" (click)="visibleNew = false"></p-button>
        </div>
    </ng-template>
</p-dialog>

<p-dialog header="Cập nhật lịch trình" [(visible)]="visibleHistory" [style]="{ width: '80vw' }" [breakpoints]="{ '1199px': '80vw', '575px': '100vw' }">
    <hr style="margin: 0" />
    <br />
    <p-table styleClass="p-datatable-gridlines" [value]="lotHistoryList" [scrollable]="true" [loading]="isLoadingList">
        <ng-template pTemplate="header">
            <tr>
                <th>Ngày cập nhật</th>
                <th>Người cập nhật</th>
                <th style="min-width: 10rem">Ngày giao hàng thực tế</th>
                <th style="min-width: 10rem">Thời gian đến nơi</th>
                <th style="min-width: 10rem">Thời gian khởi hành thực tế</th>
                <th style="min-width: 10rem">Thời gian về kho</th>
                <th style="min-width: 10rem">Ghi chú</th>
                <th style="min-width: 10rem">Đính kèm</th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
            <tr>
                <td>
                    {{ item.created | date: 'dd/MM/yyyy HH:mm:ss' }}
                </td>
                <td>
                    {{ item.createdBy }}
                </td>
                <td>
                    {{ item.expectedDeliveryDate | date: 'dd/MM/yyyy' }}
                </td>
                <td>
                    {{ item.expectedPortDate | date: 'dd/MM/yyyy' }}
                </td>
                <td>
                    {{ item.expectedLeaveDate | date: 'dd/MM/yyyy' }}
                </td>
                <td>
                    {{ item.expectedWarehouseDate | date: 'dd/MM/yyyy' }}
                </td>
                <td>{{ item.note }}</td>

                <td>
                    <div class="tw-flex tw-flex-row tw-gap-4" *ngIf="item.attachments">
                        <ng-container *ngFor="let att of item.attachments">
                            <app-attachment [attachment]="att"></app-attachment>
                        </ng-container>
                    </div>
                </td>
            </tr>
        </ng-template>
    </p-table>
</p-dialog>

<p-dialog
    header="Thông báo cho người tiếp nhận"
    [(visible)]="visibleSubmit"
    [style]="{ width: '80vw' }"
    [breakpoints]="{ '1199px': '80vw', '575px': '100vw' }"
    (onHide)="formGroupSubmit.reset()"
>
    <hr style="margin: 0" />
    <br />
    <app-form #formSubmit *ngIf="visibleSubmit" [formGroup]="formGroupSubmit" layout="vertical" (onSubmit)="sendNotification($event)">
        <app-form-item label="Nội dung">
            <textarea rows="5" formControlName="content" pInputTextarea class="tw-w-full"></textarea>
        </app-form-item>
        <app-form-item label="Người tiếp nhận" [isRequired]="true">
            <app-filter-table
                type="select"
                [configSelect]="{
                    fieldValue: 'id',
                    fieldLabel: 'email',
                    options: receivers,
                    filterLocal: true,
                }"
                (onChange)="handleChangeReceivers($event)"
            ></app-filter-table>
        </app-form-item>
    </app-form>
    <ng-template pTemplate="footer">
        <div>
            <p-button
                type="button"
                severity="primary"
                size="small"
                (click)="formSubmit.handleSubmit()"
                label="Xác nhận gửi"
                [disabled]="formGroupSubmit.invalid"
                *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_lot_edit']"
            ></p-button>
            <p-button
                type="button"
                label="Hủy"
                [text]="true"
                [raised]="true"
                size="small"
                severity="secondary"
                (click)="visibleSubmit = false; formGroupSubmit.reset()"
            ></p-button>
        </div>
    </ng-template>
</p-dialog>
