import { inject } from '@angular/core';
import { CanActivateFn, ActivatedRouteSnapshot, RouterStateSnapshot, Router, CanActivateChildFn } from '@angular/router';
import { map, catchError, of } from 'rxjs';
import { AuthService } from './auth.service';
import { LayoutService } from 'src/app/layout/service/app.layout.service';

export const canAuthenticate: CanActivateFn = (route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => {
    const authService = inject(AuthService);
    const router = inject(Router);
    return authService.userObserver.pipe(
        catchError(() => {
            authService.invalidateLogin();
            router.navigate(['/login'], { queryParams: { redirectURL: state.url } });
            return of(false);
        }),
        map((user) => {
            if (!user) {
                router.navigate(['/login'], { queryParams: { redirectURL: state.url } });
                return false;
            }
            return true;
        }),
    );
};

export const canAuthenticateChild: CanActivateChildFn = (route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => canAuthenticate(route, state);

export const canAuthorize: CanActivateFn = (route: ActivatedRouteSnapshot) => {
    const authService = inject(AuthService);
    const layoutService = inject(LayoutService);
    const router = inject(Router);

    const principle = authService.getPrinciple();

    const isAuthorized =
        route.data['authorize'] && route.data['authorize'].length > 0
            ? principle?.authorities.some((s) => route.data['authorize']?.some((p) => p === s))
            : true;
    if (route.data['sidebar'] !== undefined) {
        layoutService.setSideBarItems(route.data['sidebar']);
    }
    if (route.data['module'] !== undefined) {
        layoutService.setModule(route.data['module']);
    }
    if (!isAuthorized) {
        router.navigate(['/access']);
    }

    return isAuthorized;
};

export const canRedirectDashBoard: CanActivateChildFn = () => {
    const router = inject(Router);
    router.navigate(['/dashboard']);
    return false;
};

export const canRedirectLogin: CanActivateFn = (route: ActivatedRouteSnapshot) => {
    const authService = inject(AuthService);
    const router = inject(Router);
    const token = route.queryParams['token'];
    if (token) {
        localStorage.setItem('token', JSON.stringify(token));
        router.navigate(['/']);
    }
    if (authService.getToken()) {
        router.navigate(['/']);
    }
    return true;
};

export const canRedirectDashBoardSQC: CanActivateFn = (route: ActivatedRouteSnapshot) => {
    const router = inject(Router);
    const token = route.queryParams['token'];
    if (token) {
        localStorage.setItem('token', JSON.stringify(token));
        router.navigate(['/sqc/dashboard']);
    }
    return true;
};

export const canRedirectDashBoardSCC: CanActivateFn = (route: ActivatedRouteSnapshot) => {
    const router = inject(Router);
    const token = route.queryParams['token'];
    if (token) {
        localStorage.setItem('token', JSON.stringify(token));
        router.navigate(['/sc/dashboard']);
    }

    return true;
};

// Vivas ----------------
export const canRedirectDashBoardPMS: CanActivateFn = (route: ActivatedRouteSnapshot) => {
    const router = inject(Router);
    const token = route.queryParams['token'];
    if (token) {
        localStorage.setItem('token', JSON.stringify(token));
        router.navigate(['/pms/dashboard']);
    }

    return true;
};
// Vivas ----------------
