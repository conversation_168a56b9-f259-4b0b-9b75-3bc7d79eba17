import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, catchError, Observable, of, retry, shareReplay, switchMap } from 'rxjs';
import { Router } from '@angular/router';
import { ApiResponse, TokenDTO, TokenLogin, User } from 'src/app/models/interface';

@Injectable({
    providedIn: 'root',
})
export class AuthService {
    userObserver = new BehaviorSubject<User>(null);

    constructor(
        private http: HttpClient,
        private router: Router,
    ) {}

    public getToken(): string | null {
        const tokenString = localStorage.getItem('token');
        return tokenString ? JSON.parse(tokenString) : null;
    }

    public login(credentials): Observable<TokenLogin> {
        const data = {
            email: credentials.email,
            account: credentials.account,
            password: credentials.password,
            rememberMe: credentials.rememberMe,
        };
        return this.http.post<TokenLogin>('/auth/api/auth/token', data);
    }

    public authenticateSuccess(token) {
        localStorage.setItem('token', JSON.stringify(token));
        this.setCurrentUser().subscribe(); // Ensure user data is set
    }

    public setCurrentUser(): Observable<User> {
        if (!this.getToken()) {
            this.invalidateLogin();
            return of(null);
        } else {
            return this.getCurrentUser().pipe(
                retry(2),
                shareReplay(1),
                switchMap((user) => {
                    this.userObserver.next(user);
                    return of(user);
                }),
                catchError(() => {
                    this.invalidateLogin();
                    return of(null);
                }),
            );
        }
    }

    public invalidateLogin() {
        localStorage.removeItem('token');
        this.userObserver.next(null);
    }

    public getPrinciple() {
        return this.userObserver.getValue();
    }

    hasAuthority(authorities: string[]): boolean {
        const principle = this.getPrinciple();
        if (!principle) return false;
        return authorities.some((authority) => principle.authorities.includes(authority));
    }

    isAdmin() {
        return this.hasAuthority(['ROLE_SYSTEM_ADMIN']);
    }

    isPM() {
        return this.hasAuthority(['ROLE_QC_PM']);
    }

    isAdminOrPM() {
        return this.hasAuthority(['ROLE_QC_PM', 'ROLE_SYSTEM_ADMIN']);
    }

    isSubPM() {
        return this.hasAuthority(['ROLE_QC_SUBPM']);
    }

    isEmployee() {
        return this.hasAuthority(['ROLE_QC_EMPLOYEE']);
    }

    isCustomer() {
        return this.hasAuthority(['ROLE_QC_CUSTOMER']);
    }

    saveFirebaseToken(tokenDTO: TokenDTO) {
        return this.http.post<ApiResponse>(`/auth/api/users/firebase`, tokenDTO, {
            observe: 'response',
        });
    }

    forgotPassword(body: { email: string; domain: string }) {
        return this.http.post<ApiResponse>(`/auth/api/users/smartqc/forgot-password/init`, body, {
            observe: 'response',
        });
    }

    setPassword(account) {
        return this.http.post<ApiResponse>(`/auth/api/users/forgot-password/finish`, account);
    }

    activeNewuser(account) {
        return this.http.post<ApiResponse>(`/auth/api/users/active-new-user`, account);
    }

    logOut() {
        this.userObserver.next(null);
        return this.http.get<void>(`/auth/api/auth/logout`);
    }

    getCurrentUser(): Observable<User> {
        return this.http.get<User>('/auth/api/users/current');
    }
}
