import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { OtherDoc } from 'src/app/models/interface/ptm';

@Injectable({ providedIn: 'root' })
export class ProjectPlanService {
    path = '/pr/api/production-instruction';
    #http = inject(HttpClient);

    // tab 1 process Flow

    // tab 11 Tài liệu khác
    saveOtherDocs(instructionId: string, docs: OtherDoc[]): Observable<OtherDoc[]> {
        const url = `${this.path}/other/${instructionId}`;
        return this.#http.put<OtherDoc[]>(url, docs);
    }
}
