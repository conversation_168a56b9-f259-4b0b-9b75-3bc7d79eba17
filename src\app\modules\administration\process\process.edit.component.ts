import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { CheckboxModule } from 'primeng/checkbox';
import { DropdownChangeEvent, DropdownModule } from 'primeng/dropdown';
import { InputSwitchModule } from 'primeng/inputswitch';
import { InputTextModule } from 'primeng/inputtext';
import { PanelModule } from 'primeng/panel';
import { TagModule } from 'primeng/tag';
import { Process, State } from 'src/app/models/interface';
import { ProcessService } from 'src/app/services/administration/process/process.service';
import { StateService } from 'src/app/services/administration/process/state.service';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { AlertService } from 'src/app/shared/services/alert.service';
import { LoadingService } from 'src/app/shared/services/loading.service';

@Component({
    selector: 'app-process-edit',
    standalone: true,
    templateUrl: './process.edit.component.html',
    imports: [
        CommonModule,
        RouterLink,
        SubHeaderComponent,
        HasAnyAuthorityDirective,
        TagModule,
        ButtonModule,
        CheckboxModule,
        PanelModule,
        FormCustomModule,
        InputSwitchModule,
        InputTextModule,
        DropdownModule,
        ReactiveFormsModule,
    ],
    providers: [ProcessService, StateService],
})
export class ProcessEditComponent implements OnInit, AfterViewInit {
    @ViewChild('treeCanvas', { static: false }) treeCanvas: ElementRef<HTMLCanvasElement>;

    context: CanvasRenderingContext2D;

    readonly NODE_HEIGHT = 50;
    readonly HORIZONTAL_GAP = 150;
    readonly VERTICAL_GAP = 100;

    nodeElements = [];
    selectedNode: {
        dom: HTMLDivElement;
        state: State;
    };
    draggingNode: HTMLDivElement = null;
    draggingNodeId: string = null;
    offsetX = 0;
    offsetY = 0;
    objectPosition = {};

    //
    itemsHeader = [{ label: 'Quản lý tiến trình' }, { label: 'Tạo mới' }];
    processId: number;
    oldProcess: Process;
    // form
    @ViewChild('formCreate') form: FormComponent;
    formGroup: FormGroup;
    allKeyEntity: string[] = [];
    optionState: State[] = [];
    optionNextState: State[] = [];

    constructor(
        private processService: ProcessService,
        private fb: FormBuilder,
        private router: Router,
        private route: ActivatedRoute,
        private loadingService: LoadingService,
        private alertService: AlertService,
        private stateService: StateService,
    ) {
        this.initForm();
    }

    ngOnInit(): void {
        this.route.paramMap.subscribe((params) => {
            this.processId = params.get('id') ? Number(params.get('id')) : null;

            if (this.processId) {
                this.loadingService.show();
                this.processService.getOne(this.processId).subscribe({
                    next: (res) => {
                        this.loadingService.hide();
                        this.oldProcess = res.body;
                        this.itemsHeader = [{ label: 'Quản lý tiến trình' }, { label: this.oldProcess.name }];

                        this.initForm(this.oldProcess);
                        this.optionState = this.oldProcess.states;
                        this.redraw();
                    },
                    error: (error) => {
                        this.alertService.handleError(error);
                        this.loadingService.hide();
                    },
                });
            }
        });

        this.getAllKey();
    }

    ngAfterViewInit(): void {
        this.context = this.treeCanvas.nativeElement.getContext('2d');
        this.redraw();
    }

    getAllKey() {
        this.stateService.getAllKeyEntity().subscribe({
            next: (res) => {
                this.allKeyEntity = res;
            },
        });
    }

    getAllStateByKey(keyEntity: string) {
        if (!keyEntity) {
            this.optionState = [];
            return;
        }
        this.stateService.getPage(`query=keyEntity=='${keyEntity}'`).subscribe({
            next: (res) => {
                this.optionState = res.body;
            },
        });
    }

    initForm(process: Process = null): void {
        let rootState: State = null;
        if (process && process.treeState) {
            rootState = { ...process.treeState, nextStates: [] };
        }

        this.formGroup = this.fb.group({
            name: new FormControl({ value: process?.name, disabled: process?.active }, Validators.required),
            description: new FormControl({ value: process?.description, disabled: process?.active }),
            keyEntity: new FormControl({ value: process?.keyEntity, disabled: process?.active }, Validators.required),
            treeState: new FormControl({ value: process?.treeState, disabled: process?.active }, Validators.required),
            nextState: null,
            descriptionProcessDetail: null,
            nextDescriptionProcessDetail: null,
            rootState: new FormControl({ value: rootState, disabled: process?.active }, Validators.required),
        });

        this.formGroup.get('keyEntity').valueChanges.subscribe((newValue) => {
            this.getAllStateByKey(newValue);
        });
    }

    onSave() {
        this.form.handleSubmit();
    }

    onSubmit(value: Process) {
        this.loadingService.show();
        const request = this.processId
            ? this.processService.update({ ...this.oldProcess, ...value })
            : this.processService.create({ ...value, active: false });

        request.subscribe({
            next: (res) => {
                this.loadingService.hide();
                this.alertService.success('Thành công');
                if (!this.processId) {
                    this.router.navigate([`/administration/process/${res.body.id}`]);
                }
            },
            error: (e) => {
                this.loadingService.hide();
                this.alertService.handleError(e);
            },
        });
    }

    changeStartNode(event: DropdownChangeEvent) {
        this.formGroup.patchValue({ treeState: event.value });
        this.redraw();
        this.objectPosition = {};
        if (this.processId) {
            this.oldProcess.isUpdateDetail = true;
        }
    }

    changeNextState(event: DropdownChangeEvent) {
        this.formGroup.get('nextState').setValue(event.value);
    }

    drawTree(node: State, x: number, y: number, parentKey: string = null): void {
        if (!this.context) return;
        if (parentKey) {
            node.key = parentKey + '-' + node.id.toString();
        } else {
            node.key = node.id.toString();
        }
        this.drawNode(node, x, y);

        const totalWidth = (node.nextStates.length - 1) * this.HORIZONTAL_GAP;
        let childX = x - totalWidth / 2;

        node.nextStates.forEach((child) => {
            const childY = y + this.VERTICAL_GAP;
            const oldPosition = this.objectPosition[child.key];
            this.drawTree(
                child,
                oldPosition ? oldPosition[2] : childX,
                oldPosition ? oldPosition[3] : childY,
                node.key,
            );
            if (!this.objectPosition[child.key]) {
                this.objectPosition[child.key] = [x, y, childX, childY];
            }
            childX += this.HORIZONTAL_GAP;
        });
    }

    drawNode(node: State, x: number, y: number) {
        const nodeElement = document.createElement('div');

        nodeElement.classList.add('node');

        if (node.key === this.selectedNode?.dom.id) {
            nodeElement.classList.add('select');
        }

        nodeElement.innerText = node.name;
        nodeElement.id = node.key;

        nodeElement.onclick = () => this.handleSelectNode(nodeElement, node);
        nodeElement.addEventListener('mousedown', (e) => this.startDrag(e, nodeElement));

        document.getElementById('canvasContainer').appendChild(nodeElement);
        this.nodeElements.push({ nodeElement, x, y, node });
        const top = y - this.NODE_HEIGHT / 2;
        const left = x - nodeElement.offsetWidth / 2;
        nodeElement.style.top = `${top}px`;
        nodeElement.style.left = `${left}px`;
        nodeElement.style.backgroundColor = `${node.color}`;
        nodeElement.setAttribute('data-x', top.toString());
        nodeElement.setAttribute('data-y', left.toString());
    }

    drawConnector() {
        this.context.clearRect(0, 0, this.treeCanvas.nativeElement.width, this.treeCanvas.nativeElement.height);
        if (!this.objectPosition) return;
        for (const key in this.objectPosition) {
            if (Object.prototype.hasOwnProperty.call(this.objectPosition, key)) {
                const element = this.objectPosition[key];
                const parentX = element[0];
                const parentY = element[1];
                const childX = element[2];
                const childY = element[3];
                this.context.strokeStyle = '#888';
                this.context.lineWidth = 1.5;
                const arrowLength = 10;
                const angle = Math.atan2(childY - parentY, childX - parentX);

                this.context.beginPath();
                this.context.moveTo(parentX, parentY + this.NODE_HEIGHT / 2);
                this.context.lineTo(childX, childY - this.NODE_HEIGHT / 2);
                this.context.stroke();

                this.context.beginPath();
                this.context.moveTo(childX, childY - this.NODE_HEIGHT / 2);
                this.context.lineTo(
                    childX - arrowLength * Math.cos(angle - Math.PI / 6),
                    childY - this.NODE_HEIGHT / 2 - arrowLength * Math.sin(angle - Math.PI / 6),
                );
                this.context.lineTo(
                    childX - arrowLength * Math.cos(angle + Math.PI / 6),
                    childY - this.NODE_HEIGHT / 2 - arrowLength * Math.sin(angle + Math.PI / 6),
                );
                this.context.closePath();
                this.context.fillStyle = '#333333';
                this.context.fill();
            }
        }
    }

    handleAddNode() {
        const key = this.selectedNode.state.key + '-' + this.formGroup.get('nextState').value.id;

        const nextState = {
            ...this.formGroup.get('nextState').value,
            descriptionProcessDetail: this.formGroup.get('nextDescriptionProcessDetail').value,
            key: key,
            nextStates: [],
        };
        this.selectedNode.state.nextStates.push(nextState);

        this.optionNextState = this.optionNextState.filter((s) => s.id !== nextState.id);

        this.redraw();
        this.formGroup.patchValue({ nextState: null, nextDescriptionProcessDetail: null });
        if (this.processId) {
            this.oldProcess.isUpdateDetail = true;
        }
    }

    handleDeleteNode() {
        delete this.objectPosition[this.selectedNode.state.key];

        this.removeNodeByKey(this.formGroup.get('treeState').value, this.selectedNode.state);
        this.redraw();
        if (this.processId) {
            this.oldProcess.isUpdateDetail = true;
        }
        this.selectedNode = null;
    }

    removeNodeByKey(node: State, targetNode: State, parent: State = null) {
        if (node.key === targetNode.key) {
            if (parent) {
                parent.nextStates = parent.nextStates.filter((child) => child.key !== node.key);
            } else {
                this.formGroup.patchValue({ treeState: null });
            }
            return true;
        }
        for (const child of node.nextStates) {
            if (this.removeNodeByKey(child, targetNode, node)) {
                return true;
            }
        }
        return false;
    }

    handleSelectNode(nodeElement: HTMLDivElement, state: State) {
        if (this.selectedNode && this.selectedNode?.dom.id === nodeElement.id) {
            this.selectedNode.dom.classList.remove('select');
            this.selectedNode = null;
            this.optionNextState = [];
            return;
        }
        nodeElement.classList.add('select');
        if (this.selectedNode) {
            document.getElementById(this.selectedNode.dom.id).classList.remove('select');
        }
        const idUsed = state.nextStates.map((s) => s.id);

        this.optionNextState = this.optionState.filter((s) => s.id !== state.id && !idUsed.includes(s.id));
        this.formGroup.get('descriptionProcessDetail').setValue(state.descriptionProcessDetail);

        this.selectedNode = {
            dom: nodeElement,
            state: state,
        };
    }

    handleSaveDes() {
        this.selectedNode.state.descriptionProcessDetail = this.formGroup.get('descriptionProcessDetail').value;
    }

    startDrag(event, nodeElement) {
        this.draggingNode = nodeElement;
        this.offsetX = event.clientX - nodeElement.offsetLeft;
        this.offsetY = event.clientY - nodeElement.offsetTop;

        document.addEventListener('mousemove', this.dragMove);
        document.addEventListener('mouseup', this.stopDrag);
    }

    dragMove = (event) => {
        if (!this.draggingNode) return;
        this.draggingNodeId = this.draggingNode.id;
        const newX = event.clientX - this.offsetX;
        const newY = event.clientY - this.offsetY;

        for (const key in this.objectPosition) {
            if (key === this.draggingNode.id) {
                const oldPosition = this.objectPosition[key];

                oldPosition[2] = newX + this.draggingNode.offsetWidth / 2;
                oldPosition[3] = newY + this.NODE_HEIGHT / 2;
            } else if (
                key.startsWith(this.draggingNode.id) &&
                this.draggingNode.id.split('-').length === key.split('-').length - 1
            ) {
                const oldPosition = this.objectPosition[key];

                oldPosition[0] = newX + this.draggingNode.offsetWidth / 2;
                oldPosition[1] = newY + this.NODE_HEIGHT / 2;
            }
        }

        this.draggingNode.style.left = `${newX}px`;
        this.draggingNode.style.top = `${newY}px`;
        this.drawConnector();
    };

    stopDrag = () => {
        this.draggingNode = null;
        document.removeEventListener('mousemove', this.dragMove);
        document.removeEventListener('mouseup', this.stopDrag);
    };

    redraw() {
        this.nodeElements.forEach((el) => el.nodeElement.remove());
        this.nodeElements = [];
        if (!this.formGroup.get('treeState').value) return;

        this.drawTree(this.formGroup.get('treeState').value, this.treeCanvas.nativeElement.offsetWidth / 2, 50);

        this.drawConnector();
    }
}
