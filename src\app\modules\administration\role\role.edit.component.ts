import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { TreeNode } from 'primeng/api';
import { RoleService } from 'src/app/services/administration/admin/role.service';
import { TreeTableModule } from 'primeng/treetable';
import { TreeModule } from 'primeng/tree';
import { CommonModule } from '@angular/common';
import { Privilege, Role } from 'src/app/models/interface';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { ButtonModule } from 'primeng/button';
import { PrivilegeService } from 'src/app/services/administration/admin/privilege.service';
import { PanelModule } from 'primeng/panel';
import { Form<PERSON><PERSON>er, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { InputTextModule } from 'primeng/inputtext';
import { debounceTime, distinctUntilChanged, Subject } from 'rxjs';
import { isEmpty } from 'lodash';
import { DateUtils } from 'src/app/utils/date-utils';

@Component({
    selector: 'app-role-edit',
    templateUrl: './role.edit.component.html',
    standalone: true,
    imports: [
        CommonModule,
        TreeModule,
        TreeTableModule,
        ButtonModule,
        FormCustomModule,
        SubHeaderComponent,
        HasAnyAuthorityDirective,
        PanelModule,
        FormCustomModule,
        ReactiveFormsModule,
        InputTextModule,
        FormsModule,
        RouterLink,
    ],
    providers: [RoleService, PrivilegeService],
})
export class RoleEditComponent implements OnInit {
    role: Role;
    roleId: number;

    @ViewChild('formCreate') form: FormComponent;
    formGroup: FormGroup;

    treeLeft: TreeNode[] = [];
    treeRight: TreeNode[] = [];

    allPriv: Privilege[] = [];
    treeRightPriv: Privilege[] = [];
    treeLeftPriv: Privilege[] = [];

    treeRightDisplayPriv: Privilege[] = [];
    treeLeftDisplayPriv: Privilege[] = [];

    selectedLeftNode: TreeNode[] = [];
    selectedRightNode: TreeNode[] = [];

    searchLeftSubject: Subject<string> = new Subject<string>();
    searchRightSubject: Subject<string> = new Subject<string>();

    constructor(
        private route: ActivatedRoute,
        private roleService: RoleService,
        private privilegeService: PrivilegeService,
        private fb: FormBuilder,
        private loadingService: LoadingService,
        private alertService: AlertService,
        private router: Router,
    ) {
        this.initForm();
        this.searchLeftSubject
            .pipe(
                debounceTime(300), // Thời gian debounce (300ms)
                distinctUntilChanged(), // Chỉ emit khi giá trị thay đổi
            )
            .subscribe((searchText) => {
                this.performLeftSearch(searchText); // Gọi hàm tìm kiếm
            });

        this.searchRightSubject
            .pipe(
                debounceTime(300), // Thời gian debounce (300ms)
                distinctUntilChanged(), // Chỉ emit khi giá trị thay đổi
            )
            .subscribe((searchText) => {
                this.performRightSearch(searchText); // Gọi hàm tìm kiếm
            });
    }

    ngOnInit() {
        this.roleId = Number(this.route.snapshot.paramMap.get('id'));
        this.privilegeService.getPage('query=&page=0&size=2000&sort=created,desc').subscribe((res) => {
            this.allPriv = res.body;
            this.treeLeftDisplayPriv = this.treeLeftPriv = res.body;
            this.treeLeft = this.genTreeObject(this.allPriv);
        });
        if (this.roleId) {
            this.loadingService.show();
            this.roleService.getOne(this.roleId).subscribe((role) => {
                this.role = role.body;
                if (this.role.type) {
                    this.router.navigate(['/administration/role']);
                }
                this.initForm(this.role);
                this.treeRight = this.genTreeObject(this.role.privileges);
                this.treeRightPriv = this.role.privileges ?? [];
                const idUsed = this.treeRightPriv.map((i) => i.id);
                this.treeLeftDisplayPriv = this.treeLeftPriv = this.treeLeftPriv.filter((item) => !idUsed.includes(item.id));
                this.treeLeft = this.genTreeObject(this.treeLeftDisplayPriv);
                this.loadingService.hide();
            });
        }
    }

    initForm(role: Role = null): void {
        this.formGroup = this.fb.group({
            name: new FormControl(role?.name, Validators.required),
            description: new FormControl(role?.description),
            createdText: new FormControl({
                value: role ? DateUtils.formatMilliseconds(role.created) : null,
                disabled: true,
            }),
            updatedText: new FormControl({
                value: role ? DateUtils.formatMilliseconds(role.updated) : null,
                disabled: true,
            }),
            createdBy: new FormControl({ value: role?.createdBy, disabled: true }),
            updatedBy: new FormControl({ value: role?.updatedBy, disabled: true }),
            searchLeft: null,
            searchRight: null,
        });
    }

    genTreeObject(rawData: Privilege[]) {
        const treeMap = new Map();
        const tree = [];
        rawData.forEach((element) => {
            const privLabel = element.categoryName ? element.categoryName.split('_') : [element.categoryName];

            if (!treeMap.has(privLabel[1])) {
                treeMap.set(privLabel[1], new Map());
            }

            if (!treeMap.get(privLabel[1]).has(privLabel[2])) {
                treeMap.get(privLabel[1]).set(privLabel[2], []);
            }

            treeMap.get(privLabel[1]).get(privLabel[2]).push([privLabel[3], element]);
        });
        let index1 = 0;
        for (const [key, nodeMap] of treeMap.entries()) {
            const rootNode: TreeNode = {};
            tree.push(rootNode);

            rootNode.key = index1.toString();
            rootNode.label = key;
            rootNode.expanded = true;
            rootNode.leaf = false;
            rootNode.children = [];
            rootNode['level'] = 1;
            let index2 = 0;
            for (const [keyNode, value] of nodeMap.entries()) {
                const childNode1: TreeNode = {};
                rootNode.children.push(childNode1);

                childNode1.key = `${index1}-${index2}`;
                childNode1.data = rootNode.key;
                childNode1.label = keyNode;
                childNode1.leaf = false;
                childNode1.expanded = false;
                childNode1.children = [];
                childNode1['level'] = 2;

                let index3 = 0;

                for (let i = 0; i < value.length; i++) {
                    const childNode2: TreeNode = {};
                    childNode1.children.push(childNode2);
                    childNode1.key = `${index1}-${index2}-${index3}`;
                    childNode2.expanded = false;
                    childNode2.label = value[i][0];
                    childNode2.data = value[i][1];
                    childNode2.leaf = true;
                    childNode2['level'] = 3;

                    index3++;
                }
                index2++;
            }
            index1++;
        }

        return tree;
    }

    handleSelectNode(value: TreeNode, side: 'left' | 'right') {
        if (isEmpty(value)) {
            if (side === 'left') {
                this.selectedLeftNode = [];
            } else {
                this.selectedRightNode = [];
            }
            return;
        }
    }
    onSubmit(value: Role) {
        this.loadingService.show();
        const request = this.roleId
            ? this.roleService.update({ ...this.role, ...value, privileges: this.treeRightPriv, user: null })
            : this.roleService.create({ ...value, privileges: this.treeRightPriv });

        request.subscribe({
            next: () => {
                this.loadingService.hide();
                this.alertService.success('Thành công');
            },
            error: (e) => {
                this.loadingService.hide();
                this.alertService.handleError(e);
            },
        });
    }
    onSave() {
        this.form.handleSubmit();
    }

    addPriv() {
        const selectedLeftPrivs = [];
        for (const element of this.selectedLeftNode) {
            if (element['level'] === 3 && element['data']) {
                selectedLeftPrivs.push(element['data']);
            }
        }
        this.treeRightPriv = selectedLeftPrivs.concat(this.treeRightPriv);

        this.performRightSearch(this.formGroup.get('searchRight').value);

        const idSelected = selectedLeftPrivs.map((item) => item.id);
        this.treeLeftPriv = this.treeLeftPriv.filter((item) => !idSelected.includes(item.id));
        this.performLeftSearch(this.formGroup.get('searchLeft').value);

        this.selectedLeftNode = [];
    }
    addAllPriv() {
        this.treeRightPriv = this.allPriv;
        this.treeLeftPriv = [];
        this.treeLeftDisplayPriv = [];
        this.performLeftSearch(this.formGroup.get('searchLeft').value);
        this.performRightSearch(this.formGroup.get('searchRight').value);
    }
    removePriv() {
        const selectedRightPrivs = [];
        for (const element of this.selectedRightNode) {
            if (element['level'] === 3 && element['data']) {
                selectedRightPrivs.push(element['data']);
            }
        }
        this.treeLeftPriv = selectedRightPrivs.concat(this.treeLeftPriv);

        this.performLeftSearch(this.formGroup.get('searchLeft').value);

        const idSelected = selectedRightPrivs.map((item) => item.id);
        this.treeRightPriv = this.treeRightPriv.filter((item) => !idSelected.includes(item.id));
        this.performRightSearch(this.formGroup.get('searchRight').value);

        this.selectedRightNode = [];
    }

    removeAllPriv() {
        this.treeLeftPriv = this.allPriv;
        this.treeRightPriv = [];
        this.treeRightDisplayPriv = [];
        this.performLeftSearch(this.formGroup.get('searchLeft').value);
        this.performRightSearch(this.formGroup.get('searchRight').value);
    }
    onSearchLeftChange(value: string): void {
        this.searchLeftSubject.next(value);
    }

    onSearchRightChange(value: string): void {
        this.searchRightSubject.next(value);
    }

    performLeftSearch(searchText: string): void {
        if (!searchText || searchText.trim().length === 0) {
            this.treeLeftDisplayPriv = this.treeLeftPriv;
        } else {
            this.treeLeftDisplayPriv = this.treeLeftPriv.filter((item) => item.displayName.toLowerCase().includes(searchText.toLowerCase()));
        }

        this.treeLeft = this.genTreeObject(this.treeLeftDisplayPriv);
    }
    onClear(side: 'left' | 'right') {
        if (side === 'left') {
            this.formGroup.patchValue({ searchLeft: null });
            this.searchLeftSubject.next(null);
        } else {
            this.searchRightSubject.next(null);
            this.formGroup.patchValue({ searchRight: null });
        }
    }

    performRightSearch(searchText: string): void {
        if (!searchText || searchText.trim().length === 0) {
            this.treeRightDisplayPriv = this.treeRightPriv;
        } else {
            this.treeRightDisplayPriv = this.treeRightPriv.filter((item) => item.displayName.toLowerCase().includes(searchText.toLowerCase()));
        }

        this.treeRight = this.genTreeObject(this.treeRightDisplayPriv);
    }
}
