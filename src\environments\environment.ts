// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
    production: false,
    HOST_GW: 'https://dac.vivas.vn/gateway/dev',
    HOST_INVENTORY: 'http://************:9090/#/',
    HOST_DOC: 'https://dac.vnpt-technology.vn:3001',
    HOST_BOS: 'http://************:443/#/',
    STORAGE_BASE_URL: 'https://dac.vivas.vn/storage/product-record',
    autoNotification: false,
    firebaseConfig: {
        apiKey: 'AIzaSyD0Q7kDPsgzFBMPyZX1jOFGh6U1ma1XYJA',
        authDomain: 'smartqc-8cd89.firebaseapp.com',
        projectId: 'smartqc-8cd89',
        storageBucket: 'smartqc-8cd89.appspot.com',
        messagingSenderId: '982731833006',
        appId: '1:982731833006:web:03819f1327107ad6ddf73f',
        measurementId: 'G-7J0Z0W0L8C',
    },
    hmr: true,
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
