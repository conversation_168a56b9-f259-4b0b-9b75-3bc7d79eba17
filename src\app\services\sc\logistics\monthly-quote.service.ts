import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../../base.service';
import { MonthlyQuote } from '../../../models/interface/sc';
import { ApiResponse } from 'src/app/models/interface';

@Injectable()
export class MonthlyQuoteService extends BaseService<MonthlyQuote> {
    constructor(protected override http: HttpClient) {
        super(http, '/sc/api/monthly-quote');
    }

    importFile(file: File, type: 'DOCUMENT_DATA' | 'DOCUMENT_ANOTHER') {
        const formData: FormData = new FormData();
        formData.append('file', file);
        formData.append('type', type);
        return this.http.post<ApiResponse>('/sc/api/monthly-quote/import-file', formData);
    }
}
