import { Component, EventEmitter, Input, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, Output, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PanelModule } from 'primeng/panel';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { TableModule } from 'primeng/table';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { FormArray, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { InputNumberModule } from 'primeng/inputnumber';
import { CalendarModule } from 'primeng/calendar';
import {
    SHIPPING_METHOD_AIR,
    SHIPPING_METHOD_SEA_FCL,
    SHIPPING_METHOD_SEA_LCL,
    SHIPPING_METHOD_TRUCK,
    TYPE_SHIPPING_EXPORT,
    TYPE_SHIPPING_IMPORT,
    TYPE_SHIPPING_METHOD_AIR,
    TYPE_SHIPPING_METHOD_SEA_FCL,
    TYPE_SHIPPING_METHOD_SEA_LCL,
    TYPE_SHIPPING_METHOD_TRUCK,
} from 'src/app/models/constant/sc';
import { Bo, BoNegotiate, BoShippingMethod, EstimatedInsurance, EstimatedSchedule, EstimatedTransportCost, ShipmentInfo } from 'src/app/models/interface/sc';
import { DropdownModule } from 'primeng/dropdown';
import { CheckboxChangeEvent, CheckboxModule } from 'primeng/checkbox';
import { ButtonModule } from 'primeng/button';
import { ConfirmationService } from 'primeng/api';
import { LoadingService } from 'src/app/shared/services/loading.service';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { BoService } from 'src/app/services/sc/bo/bo.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { isArray, isEqual, isNull, isUndefined } from 'lodash';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { Column, EventPopupSubmit } from 'src/app/models/interface';
import { PopupComponent } from 'src/app/shared/components/popup/popup.component';
import { FormGroupCustom } from '../../../../../shared/form-module/from-group.custom';
import { FileService } from 'src/app/shared/services/file.service';
import { InputNumberComponent } from 'src/app/shared/components/inputnumber/inputnumber.component';
import { AdditionalFeeService } from 'src/app/services/sc/bo/additional-fee.service';
import { DialogModule } from 'primeng/dialog';
import { debounceTime, firstValueFrom, Subscription, switchMap } from 'rxjs';

@Component({
    selector: 'app-bo-negotiate',
    standalone: true,
    templateUrl: './negotiate.component.html',
    styleUrls: ['./negotiate.component.scss'],
    imports: [
        CommonModule,
        PanelModule,
        InputTextModule,
        InputTextareaModule,
        TableModule,
        FormCustomModule,
        ReactiveFormsModule,
        OverlayPanelModule,
        InputNumberModule,
        CalendarModule,
        FormCustomModule,
        DropdownModule,
        CheckboxModule,
        ButtonModule,
        TableCommonModule,
        PopupComponent,
        InputNumberComponent,
        DialogModule,
    ],
    providers: [BoService, AdditionalFeeService],
})
export class BoNegotiateComponent implements OnChanges, OnInit, OnDestroy {
    @Input() bo: Bo;
    @Input() formBo: FormGroupCustom<Bo>;
    @Input() mapTypeShippingMethod: Record<string, string>;
    @ViewChild('form') form: TemplateRef<FormComponent>;
    @Output('onNegotiate') onNegotiate = new EventEmitter<Bo>();

    newBoData: Bo;
    formGroup: FormGroup;
    addCostForm: FormGroup;
    showAddCostDialog = false;
    isShowEstimatedInsurances = true;

    // type
    TYPE_SHIPPING_IMPORT = TYPE_SHIPPING_IMPORT;
    TYPE_SHIPPING_EXPORT = TYPE_SHIPPING_EXPORT;
    TYPE_SHIPPING_METHOD_AIR = TYPE_SHIPPING_METHOD_AIR;
    TYPE_SHIPPING_METHOD_SEA_LCL = TYPE_SHIPPING_METHOD_SEA_LCL;
    TYPE_SHIPPING_METHOD_SEA_FCL = TYPE_SHIPPING_METHOD_SEA_FCL;
    TYPE_SHIPPING_METHOD_TRUCK = TYPE_SHIPPING_METHOD_TRUCK;

    private fixedFields: string[] = [];
    private previousValueTransportCost: EstimatedTransportCost | null = null;
    private initialCustomValuesMap = new Map<FormGroup, Record<string, unknown>>();
    private subscriptions: Subscription[] = [];

    columns: Column[] = [
        {
            header: 'Lựa chọn',
            field: 'isChoice',
            default: true,
            hide: false,
        },
        {
            header: 'NCC dịch vụ',
            field: 'logisticShortName',
            default: true,
            hide: false,
        },
        {
            header: 'Phương thức vận chuyển',
            field: 'shippingMethodId',
            default: true,
            hide: false,
        },
        {
            header: 'Tỷ giá VND/USD',
            field: 'exchangeRate',
            hide: false,
        },
        {
            header: 'Phí chứng từ đầu xuất',
            field: 'documentFee',
            hide: false,
        },
        {
            header: 'Phí hải quan xuất khẩu',
            field: 'exportCustomsFee',
            hide: false,
        },
        {
            header: 'Phí bến bãi hàng truck qua cửa khẩu',
            field: 'borderTruckYardFee',
            hide: false,
        },
        {
            header: 'Phí lưu ca xe đầu xuất',
            field: 'exportTruckStorageFee',
            hide: false,
        },
        {
            header: 'Phí EXW đầu xuất',
            field: 'exwFee',
            hide: false,
        },
        {
            header: 'Phí FCA đầu xuất',
            field: 'fcaFee',
            hide: false,
        },
        {
            header: 'Phí local charge đầu xuất',
            field: 'exportLocalCharge',
            hide: false,
        },
        {
            header: 'Phí giấy phép xuất khẩu',
            field: 'exportLicenseFee',
            hide: false,
        },
        {
            header: 'Phí kiểm tra từ tính',
            field: 'magneticInspectionFee',
            hide: false,
        },
        {
            header: 'Phí đầu xuất phát sinh khác (Nếu có)',
            field: 'otherExportFees',
            hide: false,
        },
        {
            header: 'Phí trucking từ kho nhà máy đến cửa khẩu/cảng biển/cảng hàng không',
            field: 'factoryToPortFee',
            hide: false,
        },
        {
            header: 'Phí nâng hạ bốc xếp tại cảng nhập (Chi hộ)',
            field: 'importPortHandlingFee',
            hide: false,
        },
        {
            header: 'Cước hàng không/biển/bộ chuyển phát nhanh',
            field: 'mainTransportCost',
            hide: false,
        },
        {
            header: 'Phí local charge tại cảng nhập',
            field: 'importLocalCharge',
            hide: false,
        },
        {
            header: 'Phí bến bãi biên phòng kiểm dịch',
            field: 'borderInspectionFee',
            hide: false,
        },
        {
            header: 'Phí cầu cảng',
            field: 'wharfFee',
            hide: false,
        },
        {
            header: 'Phí hải quan giám sát tại cửa khẩu',
            field: 'supervisionFee',
            hide: false,
        },
        {
            header: 'Phí dịch vụ hải quan tại cảng nhập',
            field: 'serviceFee',
            hide: false,
        },
        {
            header: 'Phí vận chuyển nội địa từ cảng nhập đến địa điểm nhận hàng cuối cùng',
            field: 'domesticTransportFee',
            hide: false,
        },
        {
            header: 'Phí phát sinh khác tại cảng nhập (Nếu có)',
            field: 'otherImportFees',
            hide: false,
        },
        {
            header: 'Phí bốc xếp tại kho (Nếu có)',
            field: 'warehouseHandlingFee',
            hide: false,
        },
        {
            header: 'Tổng chi phí vận chuyển dự kiến',
            field: 'totalEstimatedTransportCost',
            hide: false,
            default: true,
        },
        {
            header: 'Tổng chi phí vận chuyển dự kiến sau khi trừ phân phí NCC chịu (Nếu có)',
            field: 'totalCostAfterSupplierDeduction',
            hide: false,
            default: true,
        },
        {
            header: 'Đơn giá tính quốc tế',
            field: 'internationalPrice',
            hide: false,
        },
        {
            header: 'Đơn vị tính cước quốc tế (CBM/CONT/KG)',
            field: 'internationalRateUnitId',
            hide: false,
        },
    ];
    columnChoose: Column[] = [];

    constructor(
        private fb: FormBuilder,
        private confirmationService: ConfirmationService,
        private loadingService: LoadingService,
        private boService: BoService,
        private additionalFeeService: AdditionalFeeService,
        private alertService: AlertService,
        private fileService: FileService,
    ) {
        this.columnChoose = this.columns.filter((col: Column) => !col.hide);
        this.updateFixedFields();
    }

    ngOnInit(): void {
        this.initAddCostForm();
        if (!this.bo?.shipmentInfo?.id) {
            this.confirmApprove(this.formGroup.getRawValue(), false, 'Init');
        }
    }

    ngOnChanges(): void {
        this.newBoData = this.formBo.getRawValue();
        this.initForm(this.bo);
    }

    ngOnDestroy(): void {
        this.subscriptions.forEach((sub) => sub.unsubscribe());
    }

    // Phương thức cập nhật fixedFields dựa trên columns
    private updateFixedFields(): void {
        this.fixedFields = this.columns
            .filter((col) => (!col.group || col.group !== 'custom') && col.field !== 'totalEstimatedTransportCost')
            .map((col) => col.field);
    }

    private initAddCostForm(): void {
        this.addCostForm = this.fb.group({
            name: ['', Validators.required],
            value: [null],
            boId: [this.bo.id],
            position: [null],
            transportCostId: [null],
        });
    }

    openAddCostDialog(): void {
        this.showAddCostDialog = true;
        this.addCostForm.reset();
    }

    async addNewCost(): Promise<void> {
        if (!this.addCostForm.valid) {
            this.alertService.error('Vui lòng nhập tên chi phí hợp lệ');
            return;
        }

        const fieldName = this.addCostForm.get('name')?.value;

        if (this.estimatedTransportCosts.controls.length === 0) {
            this.alertService.error('Không có chi phí vận chuyển để thêm phí mới');
            return;
        }

        this.loadingService.show();

        try {
            const promises = this.estimatedTransportCosts.controls.map((control: FormGroup, i) => {
                const transportCostId = control.get('id')?.value;
                const warehouseHandlingIndex = this.columns.findIndex((col) => col.field === 'warehouseHandlingFee');
                const insertIndex = warehouseHandlingIndex > -1 ? warehouseHandlingIndex + 1 : this.columns.length;

                const additionalFee = {
                    boId: this.bo.id,
                    transportCostId: transportCostId,
                    name: fieldName,
                    value: null,
                    position: insertIndex,
                };
                return firstValueFrom(this.additionalFeeService.create(additionalFee)).then(() => ({ control, index: i }));
            });

            await Promise.all(promises).then((results) => {
                this.loadingService.hide();
                this.showAddCostDialog = false;

                results.forEach(({ control }) => {
                    control.addControl(fieldName, this.fb.control(null));
                    // Cập nhật initialCustomValuesMap cho cột mới
                    const initialCustomValues = this.initialCustomValuesMap.get(control) || {};
                    initialCustomValues[fieldName] = null;
                    this.initialCustomValuesMap.set(control, initialCustomValues);
                });
                if (results.length > 0) {
                    const warehouseHandlingIndex = this.columns.findIndex((col) => col.field === 'warehouseHandlingFee');
                    const maxCustomIndex = this.columns.reduceRight((lastIndex, col, index) => {
                        return col.group === 'custom' && lastIndex === -1 ? index : lastIndex;
                    }, -1);

                    const insertIndex =
                        maxCustomIndex > -1 ? maxCustomIndex + 1 : warehouseHandlingIndex > -1 ? warehouseHandlingIndex + 1 : this.columns.length;
                    const newColumn: Column = {
                        header: fieldName,
                        field: fieldName,
                        hide: false,
                        group: 'custom',
                    };
                    this.columns.splice(insertIndex, 0, newColumn);
                    this.columnChoose.push(newColumn);
                }
            });
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (res: any) {
            if (res.error.errorKey !== 'DuplicateName') {
                this.additionalFeeService.deleteByBo(this.bo.id, fieldName).subscribe();
            }
            this.loadingService.hide();
        }
    }

    private calculateTotalEstimatedTransportCost(control: FormGroup): void {
        let total = 0;
        this.columns.forEach((col) => {
            if (
                col.field !== 'exchangeRate' &&
                col.field !== 'totalEstimatedTransportCost' &&
                col.field !== 'totalCostAfterSupplierDeduction' &&
                col.field !== 'internationalPrice' &&
                col.field !== 'internationalRateUnitId' &&
                col.field !== 'isChoice' &&
                col.field !== 'logisticShortName' &&
                col.field !== 'shippingMethodId'
            ) {
                const value = control.get(col.field)?.value;
                total += value && !isNaN(value) ? Number(value) : 0;
            }
        });

        // Kiểm tra xem total có khác với giá trị hiện tại không
        const currentTotal = control.get('totalEstimatedTransportCost')?.getRawValue();
        if (currentTotal !== total) {
            // Sử dụng emitEvent: false để ngăn kích hoạt valueChanges
            control.get('totalEstimatedTransportCost')?.setValue(total, { emitEvent: false });
            this.findIndexLowestCost();
        }
    }

    private initForm(data: Bo | null): void {
        this.formGroup = this.fb.group({
            shipmentInfo: this.initShipmentInfo(data?.shipmentInfo),
            estimatedTransportCosts: this.initEstimatedTransportCosts(data?.boShippingMethods || [], data?.estimatedTransportCosts || []),
            estimatedSchedules: this.initEstimatedSchedules(data?.boShippingMethods || [], data?.estimatedSchedules || []),
            estimatedInsurances: this.initEstimatedInsurance(data?.boShippingMethods || [], data?.estimatedInsurances || []),
            noteNegotiate: [this.bo.noteNegotiate],
        });

        this.initializeSubscriptions();
        this.updateCheckboxEstimatedInsurances(true);
        this.isShowEstimatedInsurances = this.showEstimatedInsurances(this.bo.boShippingMethods);
    }

    private initializeSubscriptions(): void {
        // Theo dõi valueChanges cho estimatedInsurances
        this.estimatedInsurances.controls.forEach((control) => {
            ['insuranceRate', 'exchangeRate', 'totalInsuranceValue'].forEach((field) => {
                control.get(field)?.valueChanges.subscribe(() => {
                    const fee = this.calculateInsuranceFee(control.getRawValue());
                    control.get('insuranceFee')?.setValue(fee, { emitEvent: false });
                });
            });
            ['insuranceFee'].forEach((field) => {
                control.get(field)?.valueChanges.subscribe(() => {
                    this.updateCheckboxEstimatedInsurances(false);
                });
            });
        });

        // 1. Theo dõi thay đổi ngoài estimatedTransportCosts
        this.subscriptions.push(
            this.formGroup
                .get('shipmentInfo')
                ?.valueChanges.pipe(debounceTime(1000))
                .subscribe(() => {
                    this.confirmApprove(this.formGroup.getRawValue(), false, 'Thay đổi shipmentInfo');
                }),
            this.formGroup
                .get('estimatedSchedules')
                ?.valueChanges.pipe(debounceTime(1000))
                .subscribe(() => {
                    this.confirmApprove(this.formGroup.getRawValue(), false, 'Thay đổi estimatedSchedules');
                }),
            this.formGroup
                .get('estimatedInsurances')
                ?.valueChanges.pipe(debounceTime(1000))
                .subscribe(() => {
                    this.confirmApprove(this.formGroup.getRawValue(), false, 'Thay đổi estimatedInsurances');
                }),
            this.formGroup
                .get('noteNegotiate')
                ?.valueChanges.pipe(debounceTime(1000))
                .subscribe(() => {
                    this.confirmApprove(this.formGroup.getRawValue(), false, 'Thay đổi noteNegotiate');
                }),
        );

        // 2. Theo dõi thay đổi trong estimatedTransportCosts
        const colsAdd = this.columns.filter((col) => col?.group && col.group === 'custom');
        this.initialCustomValuesMap.clear();
        this.estimatedTransportCosts.controls.forEach((control: FormGroup) => {
            const initialCustomValues: Record<string, unknown> = {};
            colsAdd.forEach((col) => {
                initialCustomValues[col.field] = control.get(col.field)?.value;
            });
            this.initialCustomValuesMap.set(control, initialCustomValues);

            this.subscriptions.push(
                control.valueChanges
                    .pipe(
                        debounceTime(1000),
                        switchMap((value) => {
                            const initialCustomValues = this.initialCustomValuesMap.get(control)!;

                            // Kiểm tra xem có thay đổi trong các cột custom không
                            const hasCustomChange = colsAdd.some((col) => {
                                const currentValue = value[col.field];
                                const initialValue = initialCustomValues ? initialCustomValues[col.field] : null;
                                return currentValue !== initialValue && currentValue !== null && !isNaN(currentValue) && currentValue !== undefined;
                            });

                            // Gửi API updateByBo nếu có thay đổi trong cột custom
                            if (hasCustomChange && colsAdd.length > 0) {
                                const dtos = colsAdd
                                    .filter((col) => value[col.field] !== null && !isNaN(value[col.field]))
                                    .map((col) => ({
                                        name: col.field,
                                        value: value[col.field] === 0 ? null : value[col.field],
                                        transportCostId: value.id,
                                    }));
                                if (dtos.length > 0) {
                                    return this.additionalFeeService.updateByBo(dtos).pipe(
                                        switchMap(() => {
                                            colsAdd.forEach((col) => {
                                                initialCustomValues[col.field] = value[col.field];
                                            });
                                            this.calculateTotalEstimatedTransportCost(control);
                                            return this.processFixedFields(control, value);
                                        }),
                                    );
                                }
                            }
                            return this.processFixedFields(control, value);
                        }),
                    )
                    .subscribe(),
            );
        });
    }

    private processFixedFields(control: FormGroup, value: EstimatedTransportCost) {
        // Kiểm tra xem có thay đổi trong trường cố định không
        const hasFixedFieldChange = this.fixedFields.some((field) => {
            const currentValue = value[field];
            const previousValue = this.previousValueTransportCost ? this.previousValueTransportCost[field] : null;
            return !isEqual(currentValue, previousValue);
        });

        // Chỉ gọi confirmApprove nếu có thay đổi trong trường cố định
        if (hasFixedFieldChange) {
            this.previousValueTransportCost = { ...value };
            this.calculateTotalEstimatedTransportCost(control);
            this.confirmApprove(this.formGroup.getRawValue(), false, 'Thay đổi phí COST');
        }
        return [];
    }

    private showEstimatedInsurances(value: BoShippingMethod[]): boolean {
        if (!value || value.length === 0) {
            return false;
        } else {
            const setTypeShippingMethodInSurance = new Set();
            for (const boShipping of value) {
                if (boShipping.logisticInsuranceId) {
                    setTypeShippingMethodInSurance.add(boShipping.logisticInsuranceId);
                }
            }
            if (setTypeShippingMethodInSurance.size < 1) {
                return false;
            } else {
                return true;
            }
        }
    }

    private updateCheckboxEstimatedInsurances(isFirst: boolean): void {
        const controls = this.estimatedInsurances.controls;

        // Kiểm tra nếu có isChoice = true và đang ở lần đầu (isFirst), bỏ qua
        if (isFirst && controls.some((control) => control.get('isChoice')?.value === true)) {
            return;
        }

        let minIndex = -1;
        let minFee = Number.POSITIVE_INFINITY;
        let currentChoiceIndex = -1;

        // Tìm minIndex và currentChoiceIndex trong một lần lặp
        controls.forEach((control, index) => {
            const insuranceFee = control.get('insuranceFee')?.value;
            const exchangeRate = control.get('exchangeRate')?.value;
            const isChoice = control.get('isChoice')?.value;

            // Lưu chỉ số hiện tại có isChoice = true
            if (isChoice === true) {
                currentChoiceIndex = index;
            }

            // Tìm insuranceFee nhỏ nhất
            if (typeof insuranceFee === 'number' && !isNaN(insuranceFee) && insuranceFee < minFee && exchangeRate) {
                minFee = insuranceFee;
                minIndex = index;
            }
        });

        // Nếu không tìm thấy minIndex hợp lệ, không cập nhật
        if (minIndex === -1) {
            return;
        }

        // Chỉ cập nhật nếu minIndex khác với currentChoiceIndex
        if (minIndex !== currentChoiceIndex) {
            controls.forEach((control, index) => {
                const newValue = index === minIndex;
                const currentValue = control.get('isChoice')?.value;
                // Chỉ setValue nếu giá trị thay đổi
                if (newValue !== currentValue) {
                    control.get('isChoice')?.setValue(newValue, { emitEvent: false });
                }
            });

            // Gọi confirmApprove để cập nhật database
            this.confirmApprove(this.formGroup.getRawValue(), false, 'update checkbox');
        }
    }

    private calculateInsuranceFee(item: EstimatedInsurance): number | null {
        const exchangeRateVndUsd = item.exchangeRate; // Tỷ giá VNĐ/USD
        if (!exchangeRateVndUsd) return null;
        let minFee: number; // Phí tối thiểu
        switch (item.currencyFee) {
            case 'VND':
                minFee = item.minFee;
                break;
            case 'USD': // USD -> VND
                minFee = item.minFee * exchangeRateVndUsd;
                break;
        }
        const totalValueWithBuffer = item.totalInsuranceValue * 1.1; // 110%

        if (!exchangeRateVndUsd || !totalValueWithBuffer) return null;
        // Step 1: Calculate X based on the formula
        // X = (Tổng giá trị shipment dự kiến * 110% * Tỷ giá bán VNĐ/USD * Tỷ lệ phí chấp nhận (%)) / 1000
        const x = (totalValueWithBuffer * exchangeRateVndUsd * (item.insuranceRate / 100)) / 1000;
        const roundedX = Math.round(x); // Round to 0 decimal places

        // Step 2: Calculate initial insurance fee (VNĐ)
        let insuranceFee = roundedX * 1000;

        // Step 3: Compare insurance fee with minimum fee
        insuranceFee = Math.max(insuranceFee, minFee);

        return insuranceFee;
    }

    private get estimatedTransportCosts(): FormArray {
        return this.formGroup?.get('estimatedTransportCosts') as FormArray;
    }

    private get estimatedInsurances(): FormArray {
        return this.formGroup?.get('estimatedInsurances') as FormArray;
    }

    private get estimatedSchedules(): FormArray {
        return this.formGroup?.get('estimatedSchedules') as FormArray;
    }

    /**
     * Hàm hỗ trợ chuyển đổi ngày, tránh lỗi khi giá trị không hợp lệ
     */
    private parseDate(date: number | null): Date | null {
        return date ? new Date(date) : null;
    }

    private initShipmentInfo(item: ShipmentInfo): FormGroup {
        return this.fb.group({
            id: [item?.id],
            boId: [this.bo?.id],
            created: [item?.created],
            updated: [item?.updated],
            createdBy: [item?.createdBy],
            updatedBy: [item?.updatedBy],
            tenantId: [item?.tenantId],
            active: [item?.active],
            requestDateOrder: [this.bo.code.replace('VNPT/TECH-BO-', ''), Validators.required],
            sender: [item?.id ? item?.sender : this.bo?.type === TYPE_SHIPPING_EXPORT ? 'VNPT Technology' : this.bo?.supplierName],
            receiver: [item?.id ? item?.receiver : this.bo?.type === TYPE_SHIPPING_IMPORT ? 'VNPT Technology' : null],
            itemName: [item?.itemName ? item.itemName : this.newBoData?.goodsName],
            deliveryCondition: [item?.itemName ? item.deliveryCondition : this.newBoData?.deliveryCondition],
            estimatedWeightCw: [item?.estimatedWeightCw ? item.estimatedWeightCw : this.newBoData?.totalWeight],
            estimatedVolumeCbm: [item?.estimatedVolumeCbm],
            deliveryLocation: [item?.id ? item?.deliveryLocation : null],
            exportPort: [item?.exportPort],
            importPort: [item?.importPort],
            finalDestination: [
                item?.finalDestination ? item?.finalDestination : this.bo?.type === TYPE_SHIPPING_IMPORT ? 'VNPT Technology (Khu công nghệ cao Hòa Lạc)' : null,
            ],
            boCode: [item?.boCode || this.bo.code, Validators.required],
            accountingCode: [item?.accountingCode || this.bo?.accountingCode],
            indexShipment: [this.bo?.indexShipment],
            shippedValue: [item?.shippedValue],
            currentShipmentValue: [this.bo?.shipmentValue],
            pendingShipmentValue: [item?.pendingShipmentValue],
            totalPoValue: [item?.totalPoValue],
            totalEstimatedTransportCost: [item?.totalEstimatedTransportCost],
            transportCostRatio: [item?.transportCostRatio],
        });
    }

    private initEstimatedTransportCosts(shippings: BoShippingMethod[], items: EstimatedTransportCost[]): FormArray {
        let warehouseHandlingIndex = this.columns.findIndex((col) => col.field === 'warehouseHandlingFee');

        return this.fb.array(
            shippings
                .filter((item) => !!item.logisticFwdId)
                .map((shipping, index) => {
                    const item: EstimatedTransportCost = items[index] ?? {
                        id: null,
                        boId: null,
                        created: null,
                        updated: null,
                        createdBy: null,
                        updatedBy: null,
                        tenantId: null,
                        active: true,
                        isChoice: false,
                        logisticId: shipping.logisticFwdId ?? null,
                        shippingMethodId: shipping.shippingMethodId ?? null,
                        exchangeRate: null,
                        documentFee: null,
                        exportCustomsFee: null,
                        borderTruckYardFee: null,
                        exportTruckStorageFee: null,
                        exwFee: null,
                        fcaFee: null,
                        exportLocalCharge: null,
                        exportLicenseFee: null,
                        magneticInspectionFee: null,
                        otherExportFees: null,
                        factoryToPortFee: null,
                        importPortHandlingFee: null,
                        mainTransportCost: null,
                        importLocalCharge: null,
                        borderInspectionFee: null,
                        wharfFee: null,
                        supervisionFee: null,
                        serviceFee: null,
                        domesticTransportFee: null,
                        otherImportFees: null,
                        warehouseHandlingFee: null,
                        totalEstimatedTransportCost: null,
                        totalCostAfterSupplierDeduction: null,
                        internationalPrice: null,
                        internationalRateUnitId: null,
                        additionalFees: [],
                    };

                    const formGroup: FormGroup = this.fb.group({
                        id: [item.id],
                        boId: [this.bo?.id ?? null],
                        created: [item.created],
                        updated: [item.updated],
                        createdBy: [item.createdBy],
                        updatedBy: [item.updatedBy],
                        tenantId: [item.tenantId],
                        active: [item.active],
                        isChoice: [item.isChoice],
                        logisticId: [item.logisticId],
                        logisticShortName: [shipping.logisticsFwd?.shortName], // Nhà cung cấp dịch vụ
                        shippingMethodId: [item.shippingMethodId],
                        roadNote: [shipping.roadNote], // Phương thức vận chuyển
                        exchangeRate: [item.exchangeRate], // Tỷ giá VND/USD
                        documentFee: [item.documentFee], // Phí chứng từ đầu xuất
                        exportCustomsFee: [item.exportCustomsFee], // Phí hải quan xuất khẩu
                        borderTruckYardFee: [item.borderTruckYardFee], // Phí bến bãi hàng truck qua cửa khẩu
                        exportTruckStorageFee: [item.exportTruckStorageFee], // Phí lưu ca xe đầu xuất
                        exwFee: [item.exwFee], // Phí EXW đầu xuất
                        fcaFee: [item.fcaFee], // Phí FCA đầu xuất
                        exportLocalCharge: [item.exportLocalCharge], // Phí local charge đầu xuất
                        exportLicenseFee: [item.exportLicenseFee], // Phí giấy phép xuất khẩu
                        magneticInspectionFee: [item.magneticInspectionFee], // Phí kiểm tra từ tính
                        otherExportFees: [item.otherExportFees], // Phí đầu xuất phát sinh khác(Nếu có)
                        factoryToPortFee: [item.factoryToPortFee], // Phí trucking từ kho nhà máy đến cửa khẩu/cảng biển/cảng hàng không
                        importPortHandlingFee: [item.importPortHandlingFee], // Phí nâng hạ bốc xếp tại cảng nhập(Chi hộ)
                        mainTransportCost: [item.mainTransportCost], // Cước hàng không/biển/bộ chuyển phát nhanh
                        importLocalCharge: [item.importLocalCharge], // Phí local charge tại cảng nhập
                        borderInspectionFee: [item.borderInspectionFee], // Phí bến bãi biên phòng kiểm dịch
                        wharfFee: [item.wharfFee], // Phí cầu cảng
                        supervisionFee: [item.supervisionFee], // Phí hải quan giám sát tại cửa khẩu
                        serviceFee: [item.serviceFee], // Phí dịch vụ hải quan tại cảng nhập
                        domesticTransportFee: [item.domesticTransportFee], // Phí vận chuyển nội địa từ cảng nhập đến địa điểm nhận hàng cuối cùng
                        otherImportFees: [item.otherImportFees], // Phí phát sinh khác tại cảng nhập(Nếu có)
                        warehouseHandlingFee: [item.warehouseHandlingFee], // Phí bốc xếp tại kho(Nếu có)
                        totalEstimatedTransportCost: [{ value: item.totalEstimatedTransportCost, disabled: true }], // Tổng chi phí vận chuyển dự kiến
                        totalCostAfterSupplierDeduction: [item.totalCostAfterSupplierDeduction], // Tổng chi phí sau khi trừ phân phí NCC chịu(Nếu có)
                        internationalPrice: [item.internationalPrice], // Đơn giá tính quốc tế
                        internationalRateUnitId: [item.internationalRateUnitId], // Đơn vị tính cước quốc tế (CBM/CONT/KG)
                    });

                    // Add custom cost fields if they exist
                    if (item?.additionalFees && isArray(item?.additionalFees)) {
                        // Thêm control cho tất cả additionalFees
                        item.additionalFees.forEach((add) => {
                            formGroup.addControl(add.name, this.fb.control(add.value));
                        });

                        // Chỉ thêm cột cho bản ghi đầu tiên (index === 0)
                        if (index === 0) {
                            // Xóa cột custom heads-up để tránh lặp lại
                            this.columns = this.columns.filter((item) => isNull(item.group) || isUndefined(item.group));
                            this.columnChoose = this.columnChoose.filter((item) => isNull(item.group) || isUndefined(item.group));

                            // Sắp xếp additionalFees theo position
                            const sortedFees = [...item.additionalFees].sort((a, b) => a.position - b.position);

                            // Thêm cột mới theo thứ tự position
                            sortedFees.forEach((add) => {
                                const newColumn: Column = {
                                    header: add.name,
                                    field: add.name,
                                    hide: false,
                                    group: 'custom',
                                };
                                this.columns.splice(++warehouseHandlingIndex, 0, newColumn);
                                this.columnChoose.push(newColumn);
                            });
                        }
                    }
                    return formGroup;
                }),
        );
    }

    private initEstimatedSchedules(shippings: BoShippingMethod[], items: EstimatedSchedule[]): FormArray {
        return this.fb.array(
            shippings
                .filter((item) => item.logisticFwdId)
                .map((shipping, index) => {
                    const item: EstimatedSchedule = items[index] ?? {
                        id: null,
                        boId: null,
                        created: null,
                        updated: null,
                        createdBy: null,
                        updatedBy: null,
                        tenantId: null,
                        active: true,
                        logisticId: shipping.logisticFwdId ?? null,
                        shippingMethodId: shipping.shippingMethodId ?? null,
                        roadNote: shipping.roadNote,
                        readyDate: this.bo.readyDate,
                        finalDeliveryRequestDate: this.bo.requiredArrivedDate,
                        exportPortDeliveryDate: null,
                        departureDate: null,
                        arrivalDate: null,
                        clearanceTime: null,
                        finalDeliveryDate: null,
                        note: null,
                    };

                    return this.fb.group({
                        id: [item.id],
                        boId: [this.bo?.id ?? null],
                        created: [item.created],
                        updated: [item.updated],
                        createdBy: [item.createdBy],
                        updatedBy: [item.updatedBy],
                        tenantId: [item.tenantId],
                        active: [item.active],
                        logisticId: [item.logisticId],
                        logisticShortName: [shipping.logisticsFwd?.shortName], // Nhà cung cấp bảo hiểm
                        shippingMethodId: [item.shippingMethodId],
                        readyDateCustom: [this.parseDate(item.readyDate)],
                        finalDeliveryRequestDateCustom: [this.parseDate(item.finalDeliveryRequestDate)],
                        exportPortDeliveryDateCustom: [this.parseDate(item.exportPortDeliveryDate)],
                        departureDateCustom: [this.parseDate(item.departureDate)],
                        arrivalDateCustom: [this.parseDate(item.arrivalDate)],
                        clearanceTime: [item.clearanceTime],
                        finalDeliveryDateCustom: [this.parseDate(item.finalDeliveryDate)],
                        note: [item.note],
                        roadNote: [shipping.roadNote],
                    });
                }),
        );
    }

    private initEstimatedInsurance(shippings: BoShippingMethod[], items: EstimatedInsurance[]): FormArray {
        return this.fb.array(
            shippings
                .filter((item) => item.logisticInsuranceId)
                .map((shipping, index) => {
                    let insuranceRate: number;
                    const logisticInsuarance = shipping.logisticsInsurance;
                    const shippingMethod = shipping.shippingMethod;
                    // Điều kiện thanh toán ở booking có chứa text L/C  Lấy theo Phí LC (AIR – SEA)
                    /**	AIR: Lấy theo Phí bảo hiểm áp dụng cho Đường AIR
	                    SEA LCL/ SEA FCL: Lấy the Phí bảo hiểm áp dụng cho đường SEA
	                    TRUCK:
	                        Điều kiện giao hàng có chứa text CIP/CIF  Đường bộ nội địa
	                        Còn lại: Đường bộ quốc tế
                    **/

                    if (this.bo.paymentCondition && this.bo.paymentCondition.toLocaleUpperCase().includes('L/C')) {
                        insuranceRate = logisticInsuarance?.logisticsFee?.lcAirSea;
                    } else if (shippingMethod.name === SHIPPING_METHOD_AIR) {
                        insuranceRate = logisticInsuarance?.logisticsFee?.airRoad;
                    } else if (shippingMethod.name === SHIPPING_METHOD_SEA_FCL || shippingMethod.name === SHIPPING_METHOD_SEA_LCL) {
                        insuranceRate = logisticInsuarance?.logisticsFee?.seaRoad;
                    } else if (shippingMethod.name === SHIPPING_METHOD_TRUCK) {
                        if (this.bo.deliveryCondition && this.bo.deliveryCondition.toLocaleUpperCase().includes('CIP/CIF')) {
                            // Đường bộ nội địa
                            insuranceRate = logisticInsuarance?.logisticsFee?.internalRoad;
                        } else {
                            // Còn lại là Đường bộ quốc tế
                            insuranceRate = logisticInsuarance?.logisticsFee?.internationalRoad;
                        }
                    } else {
                        insuranceRate = logisticInsuarance?.logisticsFee?.internationalRoad;
                    }

                    const minFee = logisticInsuarance?.logisticsFee?.minFee;
                    const currencyFee = logisticInsuarance?.logisticsFee?.currency;
                    const item: EstimatedInsurance = items[index] ?? {
                        id: null,
                        boId: null,
                        created: null,
                        updated: null,
                        createdBy: null,
                        updatedBy: null,
                        tenantId: null,
                        active: true,
                        isChoice: false,
                        logisticId: shipping.logisticInsuranceId,
                        shippingMethodId: shipping.shippingMethodId,
                        insuranceRate: insuranceRate,
                        exchangeRate: null,
                        totalInsuranceValue: this.bo.shipmentValue,
                        insuranceFee: null,
                        roadNote: shipping.roadNote,
                        minFee: minFee,
                    };

                    return this.fb.group({
                        id: [item.id], // ID của bảo hiểm ước tính
                        boId: [this.bo.id ?? null], // ID đơn hàng gốc
                        created: [item.created], // Ngày tạo
                        updated: [item.updated], // Ngày cập nhật
                        createdBy: [item.createdBy], // Người tạo
                        updatedBy: [item.updatedBy], // Người cập nhật
                        tenantId: [item.tenantId], // ID tenant
                        active: [item.active], // Trạng thái hoạt động
                        isChoice: [item.isChoice], // Lựa chọn bảo hiểm
                        logisticId: [item.logisticId], // Nhà cung cấp bảo hiểm
                        logisticShortName: [shipping.logisticsInsurance?.shortName], // Nhà cung cấp bảo hiểm
                        shippingMethodId: [item.shippingMethodId], // Phương thức vận chuyển
                        roadNote: [item.roadNote], // Phương thức vận chuyển road note
                        insuranceRate: [item.insuranceRate], // Tỷ lệ phí bảo hiểm
                        exchangeRate: [item.exchangeRate], // Tỷ giá VNĐ/USD
                        totalInsuranceValue: [item.totalInsuranceValue], // Tổng số tiền bảo hiểm
                        insuranceFee: [item.insuranceFee], // Phí bảo hiểm
                        minFee: [item.minFee],
                        currencyFee: [currencyFee],
                    });
                }),
        );
    }

    private findIndexLowestCost(): number {
        const costs = this.estimatedTransportCosts.controls;

        if (!costs || costs.length === 0) {
            return -1;
        }

        // Tìm chỉ số hiện tại có isChoice = true
        let currentChoiceIndex = -1;
        for (let i = 0; i < costs.length; i++) {
            if (costs[i].get('isChoice')?.value === true) {
                currentChoiceIndex = i;
                break;
            }
        }

        // Đếm số giá trị không null để chọn field so sánh
        let countAfterSupplier = 0;
        let countEstimatedCost = 0;
        costs.forEach((control) => {
            if (control.get('totalCostAfterSupplierDeduction')?.value !== null) countAfterSupplier++;
            if (control.get('totalEstimatedTransportCost')?.value !== null) countEstimatedCost++;
        });

        // Xác định trường nào có nhiều giá trị hơn để ưu tiên so sánh
        const compareField = countAfterSupplier >= countEstimatedCost ? 'totalCostAfterSupplierDeduction' : 'totalEstimatedTransportCost';

        let minIndex = -1;
        let minCost = Number.MAX_VALUE;

        for (let i = 0; i < costs.length; i++) {
            const currentCost = costs[i].get(compareField)?.value;
            if (currentCost !== null && currentCost !== undefined && currentCost < minCost) {
                minCost = currentCost;
                minIndex = i;
            }
        }

        // Chỉ cập nhật isChoice nếu minIndex khác với currentChoiceIndex
        if (minIndex !== currentChoiceIndex && minIndex !== -1) {
            this.estimatedTransportCosts.controls.forEach((control, index) => {
                // Sử dụng emitEvent: false để ngăn kích hoạt valueChanges
                control.patchValue({ isChoice: index === minIndex }, { emitEvent: false });
            });
        }

        return minIndex;
    }

    changeCheckBox(event: CheckboxChangeEvent, rowIndex: number, type: 'estimatedTransportCosts' | 'estimatedInsurances'): void {
        const controls = type === 'estimatedInsurances' ? this.estimatedInsurances.controls : this.estimatedTransportCosts.controls;
        if (event.checked) {
            controls.forEach((control, index) => {
                control.patchValue({ isChoice: index === rowIndex }, { emitEvent: false });
            });
        } else {
            controls[rowIndex].patchValue({ isChoice: false }, { emitEvent: false });
        }
        this.confirmApprove(this.formGroup.getRawValue(), false, `Change checkbox ${type}`);
    }

    private confirmApprove(value: BoNegotiate, isNegotiate: boolean, from: string): void {
        if (isArray(value.estimatedSchedules)) {
            value.estimatedSchedules.filter((item) => {
                item.readyDate = this.getTime(item.readyDateCustom);
                item.finalDeliveryRequestDate = this.getTime(item.finalDeliveryRequestDateCustom);
                item.exportPortDeliveryDate = this.getTime(item.exportPortDeliveryDateCustom);
                item.departureDate = this.getTime(item.departureDateCustom);
                item.arrivalDate = this.getTime(item.arrivalDateCustom);
                item.finalDeliveryDate = this.getTime(item.finalDeliveryDateCustom);
            });
        }
        console.log('confirm from : ', from);

        if (isNegotiate) {
            this.confirmationService.confirm({
                key: 'app-confirm',
                header: `Xác nhận phê duyệt`,
                message: 'Xác nhận thông tin kế hoạch vận chuyển',
                icon: 'pi pi-exclamation-triangle',
                accept: () => {
                    this.loadingService.show();
                    this.boService.negotiate(this.bo.id, { ...value, isNegotiate }).subscribe({
                        next: (res) => {
                            this.onNegotiate.emit({ ...res, isNegotiate });
                            this.alertService.success('Thành công');
                            this.loadingService.hide();
                            this.formGroup.patchValue(res, { emitEvent: false });
                        },
                        error: (res) => {
                            this.alertService.handleError(res);
                            this.loadingService.hide();
                        },
                    });
                },
            });
        } else {
            this.boService.negotiate(this.bo.id, { ...value, isNegotiate }).subscribe({
                next: (res) => {
                    this.onNegotiate.emit({ ...res, isNegotiate });
                    this.formGroup.patchValue(res, { emitEvent: false });
                },
                error: () => {},
            });
        }
    }

    private getTime(date: Date): number | null {
        return date ? date.getTime() : null;
    }

    setColumnSelection(selectedColumns: Column[]): void {
        if (selectedColumns.length === 0) {
            this.columns.forEach((c) => {
                if (!c.default) {
                    c.hide = true;
                }
            });
            return;
        } else {
            this.columns.forEach((c) => {
                if (!c.default) {
                    if (selectedColumns.some((s) => c.field === s.field)) {
                        c.hide = false; // Show the column
                    } else {
                        c.hide = true; // Hide the column
                    }
                }
            });
        }
    }

    export(event: EventPopupSubmit<unknown>): void {
        this.loadingService.show();
        const value = this.formGroup.getRawValue() as Bo;
        if (isArray(value.estimatedSchedules)) {
            value.estimatedSchedules.filter((item) => {
                item.readyDate = this.getTime(item.readyDateCustom);
                item.finalDeliveryRequestDate = this.getTime(item.finalDeliveryRequestDateCustom);
                item.exportPortDeliveryDate = this.getTime(item.exportPortDeliveryDateCustom);
                item.departureDate = this.getTime(item.departureDateCustom);
                item.arrivalDate = this.getTime(item.arrivalDateCustom);
                item.finalDeliveryDate = this.getTime(item.finalDeliveryDateCustom);
            });
        }
        this.boService.exportNegotiate({ bo: { ...this.bo, ...value }, columns: this.columns }).subscribe({
            next: (res) => {
                this.loadingService.hide();
                this.fileService.downLoadFileByService(res.url, '/sc/api');
                event.close();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    public updateFromBoChange(bo: Bo): void {
        this.formGroup?.get('shipmentInfo').patchValue({
            itemName: bo?.goodsName,
            deliveryCondition: bo?.deliveryCondition,
            estimatedWeightCw: bo?.totalWeight,
        });
    }

    deleteAdditionalFee(fieldName: string): void {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: `Xác nhận xóa`,
            message: 'Xác nhận thông tin phí bổ sung',
            icon: 'pi pi-exclamation-triangle',
            accept: () => {
                this.loadingService.show();
                this.additionalFeeService.deleteByBo(this.bo.id, fieldName).subscribe({
                    next: () => {
                        this.alertService.success('Thành công');
                        this.loadingService.hide();
                        const columnIndex = this.columns.findIndex((col) => col.field === fieldName);
                        if (columnIndex > -1) {
                            this.columns.splice(columnIndex, 1);
                            this.columnChoose = this.columnChoose.filter((col) => col.field !== fieldName);
                        }

                        this.estimatedTransportCosts.controls.forEach((control: FormGroup) => {
                            const previousValue = control.get(fieldName)?.value;
                            control.removeControl(fieldName);
                            // Xóa giá trị khỏi initialCustomValuesMap
                            const initialCustomValues = this.initialCustomValuesMap.get(control);
                            if (initialCustomValues) {
                                delete initialCustomValues[fieldName];
                            }
                            if (previousValue !== null && previousValue !== undefined && previousValue !== 0) {
                                this.calculateTotalEstimatedTransportCost(control);
                            }
                        });
                    },
                    error: () => {
                        this.loadingService.hide();
                    },
                });
            },
        });
    }
}
