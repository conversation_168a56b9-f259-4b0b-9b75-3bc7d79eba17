import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { isEqual } from 'lodash';
import { LayoutService, SideBarState } from '../../service/app.layout.service';
import { AttributeAuthorityDirective } from '../../../shared/directives/attribute-authority.directive';

interface SideBarItem {
    label: string;
    icon?: string;
    routerLink?: string;
    queryParams?: { [k: string]: unknown };
    items?: SideBarItem[];
    disabled?: boolean;
}

@Component({
    selector: 'app-menuitem',
    templateUrl: './menuitem.component.html',
    styleUrls: ['./side-bar.style.scss'],
    standalone: true,
    imports: [CommonModule, RouterModule, AttributeAuthorityDirective],
})
export class AppMenuitemComponent implements OnInit {
    @Input() item: SideBarItem;
    @Input() level: number;
    @Output() isActiveChange: EventEmitter<boolean> = new EventEmitter();
    active = false; // Trạng thái active cho item
    expand = false;
    haveChildActive = false;
    objectChildActive: Record<string, boolean> = {};
    sideBarState: SideBarState;

    constructor(
        private layoutService: LayoutService,
        private router: Router,
    ) {}
    ngOnInit(): void {
        this.layoutService.sideBarState.subscribe((data) => {
            this.sideBarState = data;
        });
    }

    // Hàm xử lý khi click vào item
    itemClick(event: Event) {
        event.stopPropagation();
        if (this.item?.disabled) {
            event.preventDefault(); // Nếu item bị disabled, chặn sự kiện click
            return;
        }

        this.expand = !this.expand;

        if (this.item.routerLink && this.sideBarState.device === 'mobile') {
            this.layoutService.changeSideBarState();
        }
    }
    onRouterLinkActive(isActive: boolean) {
        this.active = isActive;
        this.isActiveChange.next(isActive);
    }

    onChildRouterLinkActive(key, isActive: boolean) {
        this.objectChildActive[key] = isActive;
        let newValue = false;

        for (const property in this.objectChildActive) {
            if (this.objectChildActive[property]) {
                newValue = true;
                this.expand = true;

                break;
            }
        }
        if (!isEqual(newValue, this.haveChildActive)) {
            this.haveChildActive = newValue;
        }
    }
}
